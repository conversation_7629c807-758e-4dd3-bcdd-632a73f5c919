package sale

import (
	"context"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	"hcscm/structure/product"
	"hcscm/structure/sale"
	structure "hcscm/structure/sale"

	"gorm.io/gorm"

	"hcscm/common/errors"

	"hcscm/vars"
)

func GetSaleProductOrderDetailIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "sale_product_order_detail_id")
}

type SaleProductOrderDetailList []SaleProductOrderDetail

func (r SaleProductOrderDetailList) List() []SaleProductOrderDetail {
	return r
}

func (r SaleProductOrderDetailList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r SaleProductOrderDetailList) One() SaleProductOrderDetail {
	return r[0]
}

func (r SaleProductOrderDetailList) Pick(id uint64) (o SaleProductOrderDetail) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r SaleProductOrderDetailList) PickByOrderId(id uint64) (o SaleProductOrderDetailList) {
	var list = make(SaleProductOrderDetailList, 0)
	for _, t := range r {
		if t.SaleProductOrderId == id {
			list = append(list, t)
		}
	}
	o = list
	return
}

// 订单匹数=预约匹数+采购匹数+欠货匹数
func (r SaleProductOrderDetailList) IsNumberEqual() (ok bool) {
	for _, t := range r {
		if t.Roll != t.BookRoll+t.PurchaseRoll+t.ShortageRoll {
			ok = false
			return
		}
	}
	return true
}

func (r SaleProductOrderDetailList) SumRoll() (roll int) {
	for _, t := range r {
		roll += t.Roll
	}
	return
}

func (r SaleProductOrderDetailList) SumWeight() (weight int) {
	for _, t := range r {
		weight += t.Weight
	}
	return
}

// SaleProductOrderDetail 成品销售单成品详情
type SaleProductOrderDetail struct {
	mysql_base.Model
	Id                              uint64 `gorm:"column:id;primaryKey"`
	StockProductId                  uint64 `gorm:"column:stock_product_id" relate:"stock_product_id"`           // 库存汇总id
	SaleProductOrderId              uint64 `gorm:"column:sale_product_order_id" relate:"sale_product_order_id"` // 成品销售单id
	ProductColorId                  uint64 `gorm:"column:product_color_id" relate:"product_color_id"`           // 颜色id
	ProductColorKindId              uint64 `gorm:"column:product_color_kind_id" relate:"product_color_kind_id"` // 颜色类别id(关联type_finished_product_kind_id)
	CustomerId                      uint64 `gorm:"column:customer_id" relate:"biz_unit_id"`                     // 所属客户id
	ProductId                       uint64 `gorm:"column:product_id" relate:"product_id"`                       // 成品id
	ProductLevelId                  uint64 `gorm:"column:product_level_id" relate:"product_level_id"`           // 成品等级id
	ProductKindId                   uint64 `gorm:"column:product_kind_id" relate:"product_kind_id"`             // 布种类型id(关联type_grey_fabric_id)
	DyelotNumber                    string `gorm:"column:dyelot_number"`                                        // 缸号
	ProductRemark                   string `gorm:"column:product_remark"`                                       // 成品备注
	AuxiliaryUnitId                 uint64 `gorm:"column:auxiliary_unit_id" relate:"auxiliary_unit_id"`         // 辅助单位id
	MeasurementUnitId               uint64 `gorm:"column:measurement_unit_id" relate:"measurement_unit_id"`     // 计量单位id
	Weight                          int    `gorm:"column:weight"`                                               // 数量
	Roll                            int    `gorm:"column:roll"`                                                 // 匹数
	CustomerAccountNum              string `gorm:"column:customer_account_num"`                                 // 客户款号
	Remark                          string `gorm:"column:remark"`                                               // 备注
	StandardSalePrice               int    `gorm:"column:standard_sale_price"`                                  // 标准销售报价(大货 散剪)
	SaleLevelId                     uint64 `gorm:"column:sale_level_id" relate:"sale_level_id"`                 // 销售等级ID
	OffsetSalePrice                 int    `gorm:"column:offset_sale_price"`                                    // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                       int    `gorm:"column:sale_price"`                                           // 销售单价(销售报价-优惠单价)(大货 散剪)
	WeightError                     int    `gorm:"column:weight_error"`                                         // 标准空差 /0.1g
	OffsetWeightError               int    `gorm:"column:offset_weight_error"`                                  // 优惠空差 /0.1g
	AdjustWeightError               int    `gorm:"column:adjust_weight_error"`                                  // 调整空差 /0.1g
	SettleWeightError               int    `gorm:"column:settle_weight_error"`                                  // 结算空差 /0.1g
	Length                          int    `gorm:"column:length"`                                               // 长度
	StandardLengthCutSalePrice      int    `gorm:"column:standard_length_cut_sale_price"`                       // 剪板销售价格
	OffsetLengthCutSalePrice        int    `gorm:"column:offset_length_cut_sale_price"`                         // 剪版优惠单价
	LengthCutSalePrice              int    `gorm:"column:length_cut_sale_price"`                                // 剪版销售单价(剪板销售价格-剪版优惠单价)
	OtherPrice                      int    `gorm:"column:other_price"`                                          // 其他金额
	WarehouseId                     uint64 `gorm:"column:warehouse_id" relate:"warehouse_id"`                   // 出货仓库id
	StockRoll                       int    `gorm:"column:stock_roll"`                                           // 库存可用匹数(可用库存)
	AvailableWeight                 int    `gorm:"column:available_weight"`                                     // 库存可用数量
	StockLength                     int    `gorm:"column:stock_length"`                                         // 库存可用长度
	BookRoll                        int    `gorm:"column:book_roll"`                                            // 预约匹数
	PurchaseRoll                    int    `gorm:"column:purchase_roll"`                                        // 采购匹数
	PurchaseWeight                  int    `gorm:"column:purchase_weight"`                                      // 采购数量
	PurchaseLength                  int    `gorm:"column:purchase_length"`                                      // 采购长度
	ShortageRoll                    int    `gorm:"column:shortage_roll"`                                        // 欠货匹数
	ShortageWeight                  int    `gorm:"column:shortage_weight"`                                      // 欠货数量
	ShortageLength                  int    `gorm:"column:shortage_length"`                                      // 欠货长度
	PmcGreyPlanOrderSummaryDetailId uint64 `gorm:"column:pmc_grey_plan_order_summary_detail_id"`                // pmc布料计划单下推单据详情id
	PlanDetailId                    uint64 `gorm:"column:plan_detail_id"`                                       // 销售计划单详细id
}

// 查询后的钩子
func (r *SaleProductOrderDetail) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r SaleProductOrderDetail) GetId() uint64 {
	return r.Id
}

// TableName SaleProductOrderDetail 表名
func (SaleProductOrderDetail) TableName() string {
	return "sale_product_order_detail"
}

func (r SaleProductOrderDetail) IsMain() bool {
	return false
}

func (r SaleProductOrderDetail) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (SaleProductOrderDetail) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (SaleProductOrderDetail) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeSaleProductOrderDetailNotExist
}

func (SaleProductOrderDetail) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeSaleProductOrderDetailAlreadyExist
}

func (r SaleProductOrderDetail) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func NewSaleProductOrderDetail(
	ctx context.Context,
	p *structure.AddSaleProductOrderDetailParam,
	saleProductOrderId uint64,
) (r SaleProductOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.StockProductId = p.StockProductId
	r.SaleProductOrderId = saleProductOrderId
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductLevelId = p.ProductLevelId
	r.ProductKindId = p.ProductKindId
	r.DyelotNumber = p.DyelotNumber
	r.ProductRemark = p.ProductRemark
	r.MeasurementUnitId = p.MeasurementUnitId
	r.AuxiliaryUnitId = p.AuxiliaryUnitId
	if r.AuxiliaryUnitId == 0 {
		r.AuxiliaryUnitId = r.MeasurementUnitId
	}
	r.Weight = p.Weight
	r.Roll = p.Roll
	r.CustomerAccountNum = p.CustomerAccountNum
	r.Remark = p.Remark
	r.StandardSalePrice = p.StandardSalePrice
	r.SaleLevelId = p.SaleLevelId
	r.OffsetSalePrice = p.OffsetSalePrice
	r.SalePrice = p.SalePrice
	r.WeightError = p.WeightError
	r.OffsetWeightError = p.OffsetWeightError
	r.AdjustWeightError = p.AdjustWeightError
	r.SettleWeightError = p.OffsetWeightError + p.AdjustWeightError
	r.Length = p.Length
	r.StandardLengthCutSalePrice = p.StandardLengthCutSalePrice
	r.OffsetLengthCutSalePrice = p.OffsetLengthCutSalePrice
	r.LengthCutSalePrice = p.LengthCutSalePrice
	r.OtherPrice = p.OtherPrice
	r.WarehouseId = p.WarehouseId
	r.StockRoll = p.StockRoll
	r.AvailableWeight = p.StockWeight
	// r.StockLength = p.StockLength
	r.BookRoll = p.BookRoll
	r.PurchaseRoll = p.PurchaseRoll
	r.PurchaseWeight = p.PurchaseWeight
	r.PurchaseLength = p.PurchaseLength
	r.ShortageRoll = r.Roll - r.BookRoll
	r.ShortageWeight = p.ShortageWeight
	r.ShortageLength = p.ShortageLength
	r.PmcGreyPlanOrderSummaryDetailId = p.PmcGreyPlanOrderSummaryDetailId
	r.PlanDetailId = p.PlanDetailId
	return
}

func NewMPSaleProductOrderDetail(
	ctx context.Context,
	p *structure.AddSaleProductOrderDetailParamV2,
	saleProductOrderId uint64,
) (r []SaleProductOrderDetail) {
	for _, detail := range p.ColorDetail {
		var (
			ShortageRoll   int // 欠货匹数
			ShortageWeight int // 欠货数量
			BookRoll       int
		)
		// 计算匹数
		BookRoll, ShortageRoll = CalculateShortageAndBooking(detail.Roll, detail.StockRoll)
		// 计算重量
		_, ShortageWeight = CalculateShortageAndBooking(detail.Weight, detail.StockWeight)

		// 判断是否是修改操作
		if p.WarehouseId != 0 {
			detail.WarehouseId = p.WarehouseId
		}
		r = append(r, SaleProductOrderDetail{
			Id:                         vars.Snowflake.GenerateId().UInt64(),
			StockProductId:             detail.StockProductId,
			SaleProductOrderId:         saleProductOrderId,
			ProductColorId:             detail.ProductColorId,
			ProductId:                  p.ProductId,
			ProductLevelId:             detail.ProductLevelId,
			DyelotNumber:               detail.Remark,
			AuxiliaryUnitId:            detail.AuxiliaryUnitId,
			MeasurementUnitId:          detail.MeasurementUnitId,
			Weight:                     detail.Weight,
			Roll:                       detail.Roll,
			CustomerAccountNum:         detail.CustomerAccountNum,
			StandardSalePrice:          detail.StandardSalePrice,
			SalePrice:                  detail.SalePrice,
			AdjustWeightError:          detail.AdjustWeightError,
			Length:                     detail.Length,
			StandardLengthCutSalePrice: detail.StandardLengthCutSalePrice,
			LengthCutSalePrice:         detail.LengthCutSalePrice,
			OtherPrice:                 detail.OtherPrice,
			WarehouseId:                detail.WarehouseId,
			StockRoll:                  detail.StockRoll,
			AvailableWeight:            detail.StockWeight,
			Remark:                     detail.Remark,
			BookRoll:                   BookRoll,
			PurchaseRoll:               detail.PurchaseRoll,
			PurchaseWeight:             detail.PurchaseWeight,
			PurchaseLength:             detail.PurchaseLength,
			ShortageRoll:               ShortageRoll,
			ShortageWeight:             ShortageWeight,
			ProductRemark:              detail.ProductRemark,
			CustomerId:                 detail.CustomerId,
			ProductColorKindId:         detail.ProductColorKindId, // 颜色类别id(关联type_finished_product_kind_id) // 成品id
			SaleLevelId:                detail.SaleLevelId,        // 销售等级ID
		})
	}
	return
}
func CalculateShortageAndBooking(demand, available int) (booking, shortage int) {
	if demand > available {
		shortage = -(available - demand) // 欠货数量为负数
		booking = available              // 占用全部可用数量
	} else {
		booking = demand // 占用需求数量
		shortage = 0     // 无欠货
	}
	return
}

// 汇总预约匹数入参
func (p *SaleProductOrderDetail) ToUpdateStockProductDetailParam(isAudit bool) *product.UpdateStockProductDetailParam {
	r := &product.UpdateStockProductDetailParam{}
	r.Id = p.Id
	r.StockProductId = p.StockProductId
	r.ProductId = p.ProductId
	r.ProductColorId = p.ProductColorId
	r.OrderId = p.SaleProductOrderId
	if isAudit {
		r.BookRoll += p.Roll
		r.BookWeight += p.Weight
		r.OrderType = common_system.BookOrderTypeProductSalePass
	} else {
		r.BookRoll -= p.Roll
		r.BookWeight -= p.Weight
		r.OrderType = common_system.BookOrderTypeProductSaleWait
	}
	return r
}

func (p *SaleProductOrderDetail) ToAddFpmArrangeOrderParam(saleProductOrder SaleProductOrder) product.AddFpmArrangeOrderItemParam {
	r := product.AddFpmArrangeOrderItemParam{}
	r.QuoteOrderItemId = p.Id
	r.SalePlanOrderItemId = p.PlanDetailId
	r.SumStockId = p.StockProductId
	r.ProductColorId = p.ProductColorId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductLevelId = p.ProductLevelId
	r.DyeFactoryDyelotNumber = p.DyelotNumber
	r.ProductRemark = p.ProductRemark
	r.UnitId = p.MeasurementUnitId
	r.AuxiliaryUnitId = p.AuxiliaryUnitId
	if r.AuxiliaryUnitId == 0 {
		r.AuxiliaryUnitId = r.UnitId
	}
	r.WarehouseId = p.WarehouseId
	r.SettleErrorWeight = p.SettleWeightError
	r.SumStockRoll = p.StockRoll
	r.SumStockWeight = p.AvailableWeight
	// 销售价格
	r.StandardSalePrice = p.StandardSalePrice
	r.SaleLevelId = p.SaleLevelId
	r.OffsetSalePrice = p.OffsetSalePrice
	r.SalePrice = p.SalePrice
	r.WeightError = p.WeightError
	r.OffsetWeightError = p.OffsetWeightError
	r.AdjustWeightError = p.AdjustWeightError
	r.StandardLengthCutSalePrice = p.StandardLengthCutSalePrice
	r.OffsetLengthCutSalePrice = p.OffsetLengthCutSalePrice
	r.LengthCutSalePrice = p.LengthCutSalePrice
	r.OtherPrice = p.OtherPrice
	r.SaleTaxRate = saleProductOrder.SaleTaxRate
	// 下推信息
	r.PushRoll = p.Roll
	r.Remark = p.Remark
	r.PushLength = p.Length
	r.PushWeight = p.Weight - p.PurchaseWeight - p.ShortageWeight - p.SettleWeightError
	return r
}

func (p *SaleProductOrderDetail) ToAddShortageProductOrderDetailParam() sale.AddShortageProductOrderDetailParam {
	r := sale.AddShortageProductOrderDetailParam{}
	r.StockProductId = p.StockProductId
	r.ProductId = p.ProductId
	r.CustomerId = p.CustomerId
	r.ProductKindId = p.ProductKindId
	r.CustomerAccountNum = p.CustomerAccountNum
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.DyelotNumber = p.DyelotNumber
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.WarehouseId = p.WarehouseId
	r.MeasurementUnitId = p.MeasurementUnitId
	r.ShortageRoll = p.ShortageRoll
	r.ShortageWeight = p.ShortageWeight
	r.ShortageLength = p.ShortageLength
	r.Remark = p.Remark
	return r
}
