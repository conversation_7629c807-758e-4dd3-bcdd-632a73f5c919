package product

import (
	"github.com/gin-gonic/gin"
	"hcscm/server/system"
	svc "hcscm/service/product"
	structure "hcscm/structure/product"
)

// @Tags 【成品库存】
// @Summary 查询库存(汇总库存)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     product_color_id     query     int  false  "颜色id"
// @Param     product_color_code     query     string  false  "颜色编号"
// @Param     product_color_name     query     string  false  "颜色名称"
// @Param     product_kind_id     query     int  false  "颜色类别id"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     warehouse_name     query     string  false  "仓库名称"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     product_id     query     int  false  "成品id"
// @Param     product_code     query     string  false  "成品编号"
// @Param     product_name     query     string  false  "成品名称"
// @Param     product_level_id     query     int  false  "成品等级id"
// @Param     product_remark     query     string  false  "成品备注"
// @Param     weight     query     int  false  "数量"
// @Param     length     query     int  false  "长度"
// @Param     book_roll     query     int  false  "预约条数"
// @Param     stock_roll     query     int  false  "库存条数"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     measurement_unit_name     query     string  false  "计量单位名称"
// @Param     remark     query     string  false  "库存备注"
// @Param     stock_show_type     query     int  false  "显示方式"
// @Success 200 {object}  structure.GetStockProductDataList{}
// @Router /hcscm/admin/v1/product/stockProduct/getStockProductList [get]
func GetStockProductList(c *gin.Context) {
	var (
		q     = &structure.GetStockProductListQuery{}
		list  = make(structure.GetStockProductDataList, 0)
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【库存管理】
// @Summary 查询库存
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     product_color_id     query     int  false  "颜色id"
// @Param     product_color_code     query     string  false  "色号"
// @Param     product_color_name     query     string  false  "颜色名称"
// @Param     product_kind_id     query     int  false  "颜色类别id"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     warehouse_name     query     string  false  "仓库名称"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     product_id     query     int  false  "成品id"
// @Param     product_code     query     string  false  "成品编号"
// @Param     product_name     query     string  false  "成品名称"
// @Param     product_level_id     query     int  false  "成品等级id"
// @Param     product_remark     query     string  false  "成品备注"
// @Param     book_roll     query     int  false  "预约条数"
// @Param     stock_roll     query     int  false  "库存条数"
// @Success 200 {object}  structure.GetYBStockProductDataList{}
// @Router /hcscm/mp/v1/product/stockProduct/getYBStockProductList [get]
func GetYBStockProductList(c *gin.Context) {
	var (
		q     = &structure.GetYBStockProductListQuery{}
		list  = make(structure.GetYBStockProductDataList, 0)
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetYBList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【库存管理】
// @Summary 获取色号详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     product_code     query     string  false  "成品编号"
// @Success 200 {object}  structure.GetTotalData{}
// @Router /hcscm/mp/v1/product/stockProduct/getColorDetailList [get]
func GetColorDetailList(c *gin.Context) {
	var (
		q = &structure.GetColorDetailQuery{}
		// list  = make(structure.GetColorDetailDataList, 0)
		// list  = make(structure.GetTotalDataList, 0)
		data = structure.GetTotalData{}
		// total int
		err error
		svc = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, _, err = svc.GetColorDetailList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存】
// @Summary 通过stock_id查询成品详细信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     stock_id     query     int  true  "库存ID"
// @Success 200 {object}  structure.GetStockProductDetailInfoResp{}
// @Router /hcscm/admin/v1/product/stockProduct/getStockProductDetailInfo [get]
func GetStockProductDetailInfo(c *gin.Context) {
	var (
		q    = &structure.GetStockProductDetailInfoQuery{}
		data structure.GetStockProductDetailInfoData
		err  error
		svc  = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.GetStockProductDetailInfo(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【库存管理】
// @Summary 查询缸号流水明细
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     stock_product_id     query     int  false  "汇总库存id"
// @Param     dyelot_number     query     string  false  "缸号"
// @Success 200 {object}  structure.GetStockProductOutAndInDetailDataListResp{}
// @Router /hcscm/mp/v1/product/stockProduct/getDyelotDetail [get]
func GetDyelotDetail(c *gin.Context) {
	var (
		q     = &structure.UnionOutAndInBaseListQuery{}
		list  structure.GetStockProductOutAndInDetailDataListResp
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, list)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDyelotDetail(ctx, q)
	if err != nil {
		return
	}
	list.Total = total

	return
}

// @Tags 【成品库存】
// @Summary 获取成品库存汇总下拉列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     product_color_id     query     int  false  "颜色id"
// @Param     product_color_code     query     string  false  "颜色编号"
// @Param     product_color_name     query     string  false  "颜色名称"
// @Param     product_kind_id     query     int  false  "颜色类别id"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     warehouse_name     query     string  false  "仓库名称"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     product_id     query     int  false  "成品id"
// @Param     product_code     query     string  false  "成品编号"
// @Param     product_name     query     string  false  "成品名称"
// @Param     product_level_id     query     int  false  "成品等级id"
// @Param     product_remark     query     string  false  "成品备注"
// @Param     weight     query     int  false  "数量"
// @Param     length     query     int  false  "长度"
// @Param     book_roll     query     int  false  "预约条数"
// @Param     stock_roll     query     int  false  "库存条数"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     measurement_unit_name     query     string  false  "计量单位名称"
// @Param     remark     query     string  false  "库存备注"
// @Param     available_only     query     bool  false  "仅显示有可用库存"
// @Param     stock_show_type     query     int  false  "显示方式"
// @Param     with_price     query     bool  false  "是否带上价格"
// @Success 200 {object}  structure.GetStockProductDropdownDataList{}
// @Router /hcscm/admin/v1/product/stockProduct/getStockProductDropdownList [get]
func GetStockProductDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetStockProductListQuery{}
		list  = make(structure.GetStockProductDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【企微快速下单-选择产品（成品库存）】
// @Summary 获取成品库存汇总下拉列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     product_color_id     query     int  false  "颜色id"
// @Param     product_color_code     query     string  false  "颜色编号"
// @Param     product_color_name     query     string  false  "颜色名称"
// @Param     product_kind_id     query     int  false  "颜色类别id"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     warehouse_name     query     string  false  "仓库名称"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     product_id     query     int  false  "成品id"
// @Param     product_code     query     string  false  "成品编号"
// @Param     product_name     query     string  false  "成品名称"
// @Param     product_level_id     query     int  false  "成品等级id"
// @Param     product_remark     query     string  false  "成品备注"
// @Param     weight     query     int  false  "数量"
// @Param     length     query     int  false  "长度"
// @Param     book_roll     query     int  false  "预约条数"
// @Param     stock_roll     query     int  false  "库存条数"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     measurement_unit_name     query     string  false  "计量单位名称"
// @Param     remark     query     string  false  "库存备注"
// @Param     available_only     query     bool  false  "仅显示有可用库存"
// @Param     stock_show_type     query     int  false  "显示方式"
// @Param     with_price     query     bool  false  "是否带上价格"
// @Param     order_type_customer     query     bool  false  "订单类型-客订"
// @Param     order_type_stock     query     bool  false  "订单类型-现货"
// @Param     color_reservation     query     int  false  "齐色预约"
// @Success 200 {object}  structure.GetStockProductDropdownDataList{}
// @Router /hcscm/mp/v1/product/stockProduct/getStockProductDropdownList [get]
func MPGetStockProductDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetStockProductListQuery{}
		list  = make(structure.GetStockProductDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.MPGetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存】
// @Summary 出入库记录(汇总库存)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     stock_product_id     query     int  false  "汇总库存id"
// @Param     begin_time     query     string  false  "开始时间"
// @Param     end_time     query     string  false  "结束时间"
// @Success 200 {object}  structure.GetStockProductOutAndInDetailDataListResp{}
// @Router /hcscm/admin/v1/product/stockProduct/getSumStockProductOutAndInDetailList [get]
func GetSumStockProductOutAndInDetailList(c *gin.Context) {
	var (
		q     = &structure.UnionOutAndInBaseListQuery{}
		list  structure.GetStockProductOutAndInDetailDataListResp
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, list)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetSumStockProductOutAndInDetailList(ctx, q)
	if err != nil {
		return
	}
	list.Total = total
	return
}

// @Tags 【成品库存】
// @Summary 出入库记录(缸号库存)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     dyelot_number  query     string  false  "缸号"
// @Param     product_color_id     query     int  false  "成品颜色id"
// @Param     stock_product_id     query     int  false  "汇总成品库存id"
// @Param     begin_time     query     string  false  "开始时间"
// @Param     end_time     query     string  false  "结束时间"
// @Success 200 {object}  structure.GetStockProductOutAndInDetailDataListResp{}
// @Router /hcscm/admin/v1/product/stockProduct/getDyeNumberAndColorStockProductOutAndInDetailList [get]
func GetDyeNumberAndColorStockProductOutAndInDetailList(c *gin.Context) {
	var (
		q     = &structure.UnionOutAndInBaseListQuery{}
		list  structure.GetStockProductOutAndInDetailDataListResp
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, list)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDyeNumberAndColorStockProductOutAndInDetailList(ctx, q)
	if err != nil {
		return
	}
	list.Total = total
	return
}

// @Tags 【成品库存】
// @Summary 出入库记录(详细库存)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     stock_detail_id     query  int  false  "详细库存id"
// @Param     stock_product_id     query  int  false  "汇总库存id"
// @Param     begin_time     query     string  false  "开始时间"
// @Param     end_time     query     string  false  "结束时间"
// @Success 200 {object}  structure.GetStockProductOutAndInDetailDataListResp{}
// @Router /hcscm/admin/v1/product/stockProduct/getDetailStockProductOutAndInDetailList [get]
func GetDetailStockProductOutAndInDetailList(c *gin.Context) {
	var (
		q     = &structure.UnionOutAndInBaseListQuery{}
		list  structure.GetStockProductOutAndInDetailDataListResp
		total int
		err   error
		svc   = svc.NewStockProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, list)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDetailStockProductOutAndInDetailList(ctx, q)
	if err != nil {
		return
	}
	list.Total = total
	return
}
