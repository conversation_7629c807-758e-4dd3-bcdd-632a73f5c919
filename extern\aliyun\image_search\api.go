package image_search

import (
	"context"
	"fmt"
	"hcscm/extern/aliyun"
	"io"
	"net/http"

	rpc "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	imagesearch "github.com/alibabacloud-go/imagesearch-20201214/v4/client"
	util "github.com/alibabacloud-go/tea-utils/v2/service"
	"github.com/alibabacloud-go/tea/tea"
)

func createClient() (_result *imagesearch.Client, _err error) {
	// 初始化 config
	// 阿里云账号AccessKey拥有所有API的访问权限，建议您使用RAM用户进行API访问或日常运维。
	// 强烈建议不要把AccessKey ID和AccessKey Secret保存到工程代码里，否则可能导致AccessKey泄露，威胁您账号下所有资源的安全。
	// 本示例以将AccessKey ID和AccessKey Secret保存在环境变量为例说明。您也可以根据业务需要，保存到配置文件里。
	var config = new(rpc.Config).SetAccessKeyId(aliyun.SearchImageaccesskey).
		SetAccessKeySecret(aliyun.SearchImageaccesssecret).
		SetType("access_key").
		// 请更换成您购买实例的区域，例如购买的是杭州区域，则为"imagesearch.cn-hangzhou.aliyuncs.com"
		SetEndpoint("imagesearch.cn-shanghai.aliyuncs.com").

		// 以下为内网（VPC）访问方式
		// 说明：内网（VPC）访问：仅限同区域ECS或资源的访问，例如您购买的图像搜索实例是华东2（上海），那么您的ECS或资源也必须在华东2（上海）才能通过内网VPC地址访问图搜服务，否则会调用不通，如果遇到调用不通，请先检查您的ECS或资源与图像搜索是否在同一个区域。
		// SetEndpointType("internal").  // 如果是内网访问图像搜索服务，则endpointType为必填项，值统一为"internal"
		// SetEndpoint("imagesearch-vpc.cn-shanghai.aliyuncs.com"). // 为内网访问（VPC）地址，请您更换为您购买实例的区域，例如您购买实例的区域为杭州，则为"imagesearch-vpc.cn-hangzhou.aliyuncs.com"

		// 请您更换成您购买实例的区域，例如您购买的实例区域为杭州，则更换为"cn-hangzhou"
		SetRegionId("cn-shanghai")
	// 创建客户端
	client, err := imagesearch.NewClient(config)
	if err != nil {
		panic(err)
	}
	return client, nil
}

func addImageApiInfo(instanceName, productCode, url string, b io.Reader) (_result *imagesearch.AddImageAdvanceRequest) {
	request := new(imagesearch.AddImageAdvanceRequest).
		// 必填，图像搜索实例名称。注意是实例名称不是实例ID。购买后通过上云层管控台实例信息一栏查看：https://imagesearch.console.aliyun.com/overview
		SetInstanceName(instanceName).
		// 必填，图片名称，最多支持 256个字符。
		// 1. ProductId + PicName唯一确定一张图片。
		// 2. 如果多次添加图片具有相同的ProductId + PicName，以最后一次添加为准，前面添加的图片将被覆盖。
		SetPicName(url).
		// 必填，商品id，最多支持 256个字符。
		// 一个商品可有多张图片。
		SetProductId(productCode).
		// 图片内容，最多支持 4MB大小图片以及5s的传输等待时间。当前仅支持PNG、JPG、JPEG、BMP、GIF、WEBP、TIFF、PPM格式图片；
		// 对于商品、商标、通用图片搜索，图片长和宽的像素必须都大于等于100且小于等于4096；
		// 对于布料搜索，图片长和宽的像素必须都大于等于448且小于等于4096；
		// 图像中不能带有旋转信息图片内容，最多支持 2MB大小图片以及5s的传输等待时间。当前仅支持jpg和png格式图片；
		SetPicContentObject(b).
		// 选填，图片类目。
		// 1. 对于商品搜索：若设置类目，则以设置的为准；若不设置类目，将由系统进行类目预测，预测的类目结果可在Response中获取 。
		// 2. 对于布料、商标、通用搜索：不论是否设置类目，系统会将类目设置为88888888。
		SetCategoryId(2).
		// 选填，是否需要进行主体识别，默认为true。
		// 1.为true时，由系统进行主体识别，以识别的主体进行搜索，主体识别结果可在Response中获取。
		// 2. 为false时，则不进行主体识别，以整张图进行搜索。
		// 3.对于布料图片搜索，此参数会被忽略，系统会以整张图进行搜索。
		SetCrop(true).
		// 选填，图片的主体区域，格式为 x1,x2,y1,y2, 其中 x1,y1 是左上角的点，x2，y2是右下角的点。
		// 设置的region 区域不要超过图片的边界。
		// 若用户设置了Region，则不论Crop参数为何值，都将以用户输入Region进行搜索。
		// 对于布料图片搜索，此参数会被忽略，系统会以整张图进行搜索。
		SetRegion("").
		// 选填，用户自定义的内容，最多支持 4096个字符。
		// 查询时会返回该字段。例如可添加图片的描述等文本。
		SetCustomContent("").
		// 选填，整数类型属性，可用于查询时过滤，查询时会返回该字段。
		//  例如不同的站点的图片/不同用户的图片，可以设置不同的IntAttr，查询时通过过滤来达到隔离的目的
		SetIntAttr(0).
		// 选填，字符串类型属性，最多支持 128个字符。可用于查询时过滤，查询时会返回该字段。
		SetStrAttr("0")
	_result = request
	return _result
}

func deleteImageApiInfo(instanceName, productCode, url string) (_result *imagesearch.DeleteImageRequest) {
	request := new(imagesearch.DeleteImageRequest).
		// 必填，图像搜索实例名称。注意是实例名称不是实例ID。购买后通过上云层管控台实例信息一栏查看：https://imagesearch.console.aliyun.com/overview
		SetInstanceName(instanceName).
		// 必填，图片名称，最多支持 256个字符。
		// 1. ProductId + PicName唯一确定一张图片。
		SetPicName(url).
		//  选填，图片名称。若不指定本参数，则删除ProductId下所有图片；若指定本参数，则删除ProductId+PicName指定的图片。
		SetProductId(productCode).
		// 选填,若为true则根据filter进行删除。
		SetIsDeleteByFilter(false).
		SetFilter("intattr3=xxx")
	_result = request
	return _result
}

func searchImageByPicApiInfo(instanceName string, b io.Reader, offset, size int) (_result *imagesearch.SearchImageByPicAdvanceRequest) {
	request := new(imagesearch.SearchImageByPicAdvanceRequest).
		// 必填，图像搜索实例名称。注意是实例名称不是实例ID。购买后通过上云层管控台实例信息一栏查看：https://imagesearch.console.aliyun.com/overview
		SetInstanceName(instanceName).
		// 图片内容，最多支持 4MB大小图片以及5s的传输等待时间。当前仅支持PNG、JPG、JPEG、BMP、GIF、WEBP、TIFF、PPM格式图片；
		// 对于商品、商标、通用图片搜索，图片长和宽的像素必须都大于等于100且小于等于4096；
		// 对于布料搜索，图片长和宽的像素必须都大于等于448且小于等于4096；
		// 图像中不能带有旋转信息
		SetPicContentObject(b).
		// 选填，商品类目。
		// 1. 对于商品搜索：若设置类目，则以设置的为准；若不设置类目，将由系统进行类目预测，预测的类目结果可在Response中获取 。
		// 2. 对于布料、商标、通用搜索：不论是否设置类目，系统会将类目设置为88888888。
		SetCategoryId(2).
		// 选填，返回结果的数目。取值范围：1-100。默认值：10。
		SetNum(int32(size)).
		// 选填，返回结果的起始位置。取值范围：0-499。默认值：0
		SetStart(int32(offset)).
		// 选填，过滤条件
		// int_attr支持的操作符有>、>=、<、<=、=，str_attr支持的操作符有=和!=，多个条件之支持AND和OR进行连接。
		// 示例：
		//  1. 根据IntAttr过滤结果，int_attr>=100
		//  2. 根据StrAttr过滤结果，str_attr!="value1"
		//  3. 根据IntAttr和StrAttr联合过滤结果，int_attr=1000 AND str_attr="value1"
		SetFilter("").
		// 选填，是否需要进行主体识别，默认为true。
		// 1.为true时，由系统进行主体识别，以识别的主体进行搜索，主体识别结果可在Response中获取。
		// 2. 为false时，则不进行主体识别，以整张图进行搜索。
		// 3.对于布料图片搜索，此参数会被忽略，系统会以整张图进行搜索。
		SetCrop(true).
		// 选填，图片的主体区域，格式为 x1,x2,y1,y2, 其中 x1,y1 是左上角的点，x2，y2是右下角的点。
		// 设置的region 区域不要超过图片的边界。
		// 若用户设置了Region，则不论Crop参数为何值，都将以用户输入Region进行搜索。
		// 3.对于布料图片搜索，此参数会被忽略，系统会以整张图进行搜索。
		SetRegion("").
		// 选填,若为true则响应数据根据ProductId进行返回。
		SetDistinctProductId(true)
	_result = request
	return _result
}

// 根据url搜索相似图片
func SearchImageByUrl(ctx context.Context, searchImageInstanceName, url string, offset, size int) (res SearchImageByUrlDataList, total int, err error) {
	// 使用限流器执行请求
	result, err := GetRateLimiter().ExecuteWithResult(ctx, func() (interface{}, error) {
		if size == 0 {
			size = 10
		}
		return searchImageByUrlInternal(ctx, searchImageInstanceName, url, offset, size)
	})

	if err != nil {
		return nil, 0, err
	}

	// 类型断言
	resData, ok := result.(struct {
		res   SearchImageByUrlDataList
		total int
	})
	if !ok {
		return nil, 0, fmt.Errorf("类型断言失败")
	}

	return resData.res, resData.total, nil
}

// searchImageByUrlInternal 内部实现，不直接对外暴露
func searchImageByUrlInternal(ctx context.Context, searchImageInstanceName, url string, offset, size int) (interface{}, error) {
	var res SearchImageByUrlDataList
	var total int

	client, err := createClient()
	if err != nil {
		return nil, err
	}
	// 初始化 runtimeObject
	var runtimeObject = new(util.RuntimeOptions)
	b, err := http.Get(url)
	if err != nil {
		return nil, err
	}
	defer b.Body.Close()

	// 如果没有指定实例名称，则使用默认实例名称
	if searchImageInstanceName == "" {
		searchImageInstanceName = aliyun.SearchImageInstanceName
	}

	request := searchImageByPicApiInfo(searchImageInstanceName, b.Body, offset, size)
	// 调用 api
	resp, err := client.SearchImageByPicAdvance(request, runtimeObject)
	if err != nil {
		return nil, err
	}
	if resp != nil {
		if resp.Body != nil {
			for _, auction := range resp.Body.Auctions {
				data := SearchImageByUrlData{
					ProductCode: tea.StringValue(auction.ProductId),
					Url:         tea.StringValue(auction.PicName),
					Score:       tea.Float32Value(auction.Score),
				}
				res = append(res, data)
			}
		}
	}
	total = int(tea.Int32Value(resp.Body.Head.DocsReturn))

	return struct {
		res   SearchImageByUrlDataList
		total int
	}{res, total}, nil
}

// 根据url上传图片到图库
func UploadImageByUrl(ctx context.Context, searchImageInstanceName, productCode, url string) (err error) {
	// 使用限流器执行请求
	_, err = GetRateLimiter().ExecuteWithResult(ctx, func() (interface{}, error) {
		return nil, uploadImageByUrlInternal(ctx, searchImageInstanceName, productCode, url)
	})

	if err != nil {
		return err
	}

	return nil
}

// uploadImageByUrlInternal 内部实现，不直接对外暴露
func uploadImageByUrlInternal(ctx context.Context, searchImageInstanceName, productCode, url string) error {
	client, err := createClient()
	if err != nil {
		return err
	}

	b, err := http.Get(url)
	if err != nil {
		return err
	}
	defer b.Body.Close()

	// 如果没有指定实例名称，则使用默认实例名称
	if searchImageInstanceName == "" {
		searchImageInstanceName = aliyun.SearchImageInstanceName
	}

	request := addImageApiInfo(searchImageInstanceName, productCode, url, b.Body)
	var runtimeObject = new(util.RuntimeOptions)
	resp, err := client.AddImageAdvance(request, runtimeObject)
	if err != nil {
		return err
	}
	if !tea.BoolValue(resp.Body.Success) {
		return fmt.Errorf("上传失败：%s", tea.StringValue(resp.Body.Message))
	}
	return nil
}

// DeleteImage 删除图片(不指定url则删除productCode下的所有图片)
func DeleteImage(ctx context.Context, searchImageInstanceName, productCode, url string) (err error) {
	// 使用限流器执行请求
	_, err = GetRateLimiter().ExecuteWithResult(ctx, func() (interface{}, error) {
		return nil, deleteImageByUrlInternal(ctx, searchImageInstanceName, productCode, url)
	})

	if err != nil {
		return err
	}

	return nil
}

// deleteImageByUrlInternal 内部实现，不直接对外暴露
func deleteImageByUrlInternal(ctx context.Context, searchImageInstanceName, productCode, url string) error {
	client, err := createClient()
	if err != nil {
		return err
	}

	// 如果没有指定实例名称，则使用默认实例名称
	if searchImageInstanceName == "" {
		searchImageInstanceName = aliyun.SearchImageInstanceName
	}

	request := deleteImageApiInfo(searchImageInstanceName, productCode, url)
	resp, err := client.DeleteImage(request)
	if err != nil {
		return err
	}
	if !tea.BoolValue(resp.Body.Success) {
		return fmt.Errorf("上传失败：%s", tea.StringValue(resp.Body.Message))
	}
	return nil
}
