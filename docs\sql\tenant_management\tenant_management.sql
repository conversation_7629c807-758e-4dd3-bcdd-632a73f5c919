CREATE TABLE IF NOT EXISTS `tenant_management`
(
    `id`                       bigint(20) unsigned                               NOT NULL COMMENT 'id',
    `create_time`              datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`               bigint(20) unsigned DEFAULT '0'                   NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name`             VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '创建人名称',
    `update_time`              datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`               bigint(20) unsigned DEFAULT '0'                   NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name`             VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '更新人名称',
    `delete_time`              datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`               bigint(20) unsigned DEFAULT '0'                   NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name`             VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '删除人名称',
    `name`                     VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '名称',
    `tenant_management_status` tinyint(3) unsigned                               NOT NULL DEFAULT 0 COMMENT '状态 1未激活 2正常 3已过期',
    `tenant_package_id`       bigint(20)                                    NOT NULL DEFAULT 0 COMMENT '套餐ID',
    `package_number_of_people` int(11)                                           NOT NULL DEFAULT 0 COMMENT '套餐人数',
    `tenant_phone_number`      VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '租户手机号',
    `tenant_phone_contacts`    VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '租户联系人',
    `tenant_company_name`      VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '租户公司名称',
    `database_name`            VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '数据库名称',
    `deadline`                 datetime            default '0000-00-00 00:00:00' NOT NULL COMMENT '删除时间',
    `activation_time`          datetime            default '0000-00-00 00:00:00' NOT NULL COMMENT '激活时间',
    `config_json`          longtext      DEFAULT ''          COMMENT '配置文件json',
    `secret`          longtext      DEFAULT ''          COMMENT '租户秘钥(禁止外传)',
    `assign_port` int(11)                                           NOT NULL DEFAULT 0 COMMENT '分配端口',
    `code_list_orc_dead_line`                 datetime            default '0000-00-00 00:00:00' NOT NULL COMMENT '码单有效期',
    `code_list_orc_status` tinyint(3) unsigned                               NOT NULL DEFAULT 0 COMMENT '状态 1启用 2禁用 3未激活',
    `code_list_orc_is_recognize` tinyint(2) unsigned                               NOT NULL DEFAULT 0 COMMENT '是否识别过 1是 2否',
    `request_domain` VARCHAR(255)        DEFAULT ''                    NOT NULL COMMENT '请求域名',
    `request_domain_prefix` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '请求域名前缀',
    `electronic_color_card_status` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '电子色卡状态 1启用 2禁用 3未激活',
    `electronic_color_card_dead_line` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '电子色卡有效期',
    `search_image_status` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '搜索图片状态',
    `search_image_dead_line` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '搜索图片截止日期',
    PRIMARY KEY (`id`)
) ENGINE = InnoDB
  DEFAULT CHARSET = utf8mb4 COMMENT ='租户管理表';