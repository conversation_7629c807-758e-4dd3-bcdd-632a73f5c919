package product

func GetGenerateReportStatusMap() (r map[GenerateReportStatus]string) {
	l := []GenerateReportStatus{GenerateReportStatusNotGen, GenerateReportStatusIsGen}
	r = make(map[GenerateReportStatus]string)
	for _, k := range l {
		r[k] = k.String()
	}
	return r
}
func GetGenerateReportStatusReverseMap() (r map[string]GenerateReportStatus) {
	l := []GenerateReportStatus{GenerateReportStatusNotGen, GenerateReportStatusIsGen}
	r = make(map[string]GenerateReportStatus)
	for _, k := range l {
		r[k.String()] = k
	}
	return r
}
func GetGenerateReportStatusReverseIntMap() (r map[string]int) {
	l := []GenerateReportStatus{GenerateReportStatusNotGen, GenerateReportStatusIsGen}
	r = make(map[string]int)
	for _, k := range l {
		r[k.String()] = int(k)
	}
	return r
}

func (t GenerateReportStatus) Check() bool {
	l := []GenerateReportStatus{GenerateReportStatusNotGen, GenerateReportStatusIsGen}
	for i := range l {
		if l[i] == t {
			return true
		}
	}
	return false
}
