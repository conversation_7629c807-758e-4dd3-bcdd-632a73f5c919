package product

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	"hcscm/vars"
	"time"
)

func GetFpmArrangeOrderItemFcIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_arrange_order_item_fc_id")
}

type FpmArrangeOrderItemFcList []FpmArrangeOrderItemFc

func (r FpmArrangeOrderItemFcList) List() []FpmArrangeOrderItemFc {
	return r
}

func (r FpmArrangeOrderItemFcList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmArrangeOrderItemFcList) GetStockIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.StockId)
	}
	return o
}

func (r FpmArrangeOrderItemFcList) One() FpmArrangeOrderItemFc {
	return r[0]
}

func (r FpmArrangeOrderItemFcList) Pick(id uint64) (o FpmArrangeOrderItemFc) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r FpmArrangeOrderItemFcList) PickByParentId(parentId uint64) (o FpmArrangeOrderItemFcList) {
	for _, t := range r {
		if t.ParentId == parentId {
			o = append(o, t)
		}
	}
	return
}

func (r FpmArrangeOrderItemFcList) PickByStockId(stockId uint64) (o FpmArrangeOrderItemFc) {
	for _, t := range r {
		if t.StockId == stockId {
			return t
		}
	}
	return
}

// 通过父id获取子列表
func (r FpmArrangeOrderItemFcList) PickList(itemId uint64) (o FpmArrangeOrderItemFcList) {
	var list = make(FpmArrangeOrderItemFcList, 0)
	for _, t := range r {
		if t.ParentId == itemId {
			list = append(list, t)
		}
	}
	o = list
	return
}

// FpmArrangeOrderItemFc 配布单-成品信息-细码
type FpmArrangeOrderItemFc struct {
	mysql_base.ModelHard
	Id                     uint64                         `gorm:"column:id;primaryKey"`
	ParentId               uint64                         `gorm:"column:parent_id"`                                              // 父id（成品信息行id）
	Roll                   int                            `gorm:"column:roll"`                                                   // 条数(条)，乘100存
	WarehouseId            uint64                         `gorm:"column:warehouse_id" relate:"warehouse_id"`                     // 仓库id
	WarehouseBinId         uint64                         `gorm:"column:warehouse_bin_id" relate:"warehouse_bin_id"`             // 仓号(公斤)，乘10000存
	VolumeNumber           int                            `gorm:"column:volume_number"`                                          // 卷号
	WarehouseOutType       cus_const.WarehouseGoodOutType `gorm:"column:warehouse_out_type"`                                     // 出仓类型
	WarehouseOutOrderId    uint64                         `gorm:"column:warehouse_out_order_id" relate:"warehouse_out_order_id"` // 出仓单id
	WarehouseOutOrderNo    string                         `gorm:"column:warehouse_out_order_no"`                                 // 出仓单号
	WarehouseInType        cus_const.WarehouseGoodInType  `gorm:"column:warehouse_in_type"`                                      // 来源类型
	WarehouseInOrderId     uint64                         `gorm:"column:warehouse_in_order_id" relate:"warehouse_in_order_id"`   // 进仓单id
	WarehouseInOrderNo     string                         `gorm:"column:warehouse_in_order_no"`                                  // 进仓单号
	ArrangeOrderNo         string                         `gorm:"column:arrange_order_no"`                                       // 配布单号
	StockId                uint64                         `gorm:"column:stock_id" relate:"stock_id"`                             // 库存成品id
	SumStockId             uint64                         `gorm:"column:sum_stock_id" relate:"sum_stock_id"`                     // 汇总库存成品id
	BaseUnitWeight         int                            `gorm:"column:base_unit_weight"`                                       // 基本单位数量(公斤)，乘10000存
	PaperTubeWeight        int                            `gorm:"column:paper_tube_weight"`                                      // 纸筒数量(公斤)，乘10000存
	WeightError            int                            `gorm:"column:weight_error"`                                           // 空差数量(公斤)，乘10000存
	ActuallyWeight         int                            `gorm:"column:actually_weight"`                                        // 码单数量
	SettleErrorWeight      int                            `gorm:"column:settle_error_weight"`                                    // 结算空差数量
	UnitId                 uint64                         `gorm:"column:unit_id" relate:"measurement_unit_id"`                   // 单位id（kg）
	Length                 int                            `gorm:"column:length"`                                                 // 长度，乘100存
	SettleWeight           int                            `gorm:"column:settle_weight"`                                          // 结算数量(公斤)，乘10000存
	DigitalCode            string                         `gorm:"column:digital_code"`                                           // 数字码
	ShelfNo                string                         `gorm:"column:shelf_no"`                                               // 货架号
	ContractNumber         string                         `gorm:"column:contract_number"`                                        // 合同号
	CustomerPoNum          string                         `gorm:"column:customer_po_num"`                                        // 客户po号
	AccountNum             string                         `gorm:"column:account_num"`                                            // 客户款号
	DyeFactoryColorCode    string                         `gorm:"column:dye_factory_color_code"`                                 // 染厂色号
	DyeFactoryDyelotNumber string                         `gorm:"column:dye_factory_dyelot_number"`                              // 染厂缸号
	ProductWidth           string                         `gorm:"column:product_width"`                                          // 成品幅宽
	ProductGramWeight      string                         `gorm:"column:product_gram_weight"`                                    // 成品克重
	StockRemark            string                         `gorm:"column:stock_remark"`                                           // 库存备注
	Remark                 string                         `gorm:"column:remark"`                                                 // 备注
	InternalRemark         string                         `gorm:"column:internal_remark"`                                        // 内部备注
	ScanUserId             uint64                         `gorm:"column:scan_user_id"`                                           // 扫描人id
	ScanUserName           string                         `gorm:"column:scan_user_name"`                                         // 扫描人名称
	ScanTime               time.Time                      `gorm:"column:scan_time"`                                              // 扫描时间
}

// 查询后的钩子
func (r *FpmArrangeOrderItemFc) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmArrangeOrderItemFc) GetId() uint64 {
	return r.Id
}

// TableName FpmArrangeOrderItemFc 表名
func (FpmArrangeOrderItemFc) TableName() string {
	return "fpm_arrange_order_item_fc"
}

func (r FpmArrangeOrderItemFc) IsMain() bool {
	return false
}

func (r FpmArrangeOrderItemFc) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmArrangeOrderItemFc) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

// ErrCodeFpmArrangeOrderItemFcAlreadyExist     ErrCode = 51XX1 // 配布单-成品信息-细码已存在
// ErrCodeFpmArrangeOrderItemFcNotExist         ErrCode = 51XX2 // 配布单-成品信息-细码不存在
func (FpmArrangeOrderItemFc) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmArrangeOrderItemFcNotExist
}

func (FpmArrangeOrderItemFc) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmArrangeOrderItemFcAlreadyExist
}

func (r FpmArrangeOrderItemFc) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func NewFpmArrangeOrderItemFc(
	ctx context.Context,
	p *structure.AddFpmArrangeOrderItemFcParam,
) (r FpmArrangeOrderItemFc) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.Roll = p.Roll
	r.WarehouseId = p.WarehouseId
	r.WarehouseBinId = p.WarehouseBinId
	r.VolumeNumber = p.VolumeNumber
	r.WarehouseOutType = p.WarehouseOutType
	r.WarehouseOutOrderId = p.WarehouseOutOrderId
	r.WarehouseOutOrderNo = p.WarehouseOutOrderNo
	r.WarehouseInType = p.WarehouseInType
	r.WarehouseInOrderId = p.WarehouseInOrderId
	r.WarehouseInOrderNo = p.WarehouseInOrderNo
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.StockId = p.StockId
	r.SumStockId = p.SumStockId
	r.BaseUnitWeight = p.BaseUnitWeight
	r.PaperTubeWeight = p.PaperTubeWeight
	r.WeightError = p.WeightError
	r.UnitId = p.UnitId
	r.Length = p.Length
	r.SettleWeight = p.SettleWeight
	r.DigitalCode = p.DigitalCode
	r.ShelfNo = p.ShelfNo
	r.ContractNumber = p.ContractNumber
	r.CustomerPoNum = p.CustomerPoNum
	r.AccountNum = p.AccountNum
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.StockRemark = p.StockRemark
	r.Remark = p.Remark
	r.InternalRemark = p.InternalRemark
	r.ScanUserId = p.ScanUserId
	r.ScanUserName = p.ScanUserName
	r.SettleErrorWeight = p.SettleErrorWeight
	r.ScanTime = p.ScanTime.ToTimeYMD()
	return
}

func (r *FpmArrangeOrderItemFc) SwapChange2Model(ctx context.Context, p structure.GetFpmChangeArrangeOrderItemFcData) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.Roll = p.Roll
	r.WarehouseId = p.WarehouseId
	r.WarehouseBinId = p.WarehouseBinId
	r.VolumeNumber = p.VolumeNumber
	r.WarehouseOutType = p.WarehouseOutType
	r.WarehouseOutOrderId = p.WarehouseOutOrderId
	r.WarehouseOutOrderNo = p.WarehouseOutOrderNo
	r.WarehouseInType = p.WarehouseInType
	r.WarehouseInOrderId = p.WarehouseInOrderId
	r.WarehouseInOrderNo = p.WarehouseInOrderNo
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.StockId = p.StockId
	r.SumStockId = p.SumStockId
	r.BaseUnitWeight = p.BaseUnitWeight
	r.PaperTubeWeight = p.PaperTubeWeight
	r.WeightError = p.WeightError
	r.ActuallyWeight = p.ActuallyWeight
	r.SettleErrorWeight = p.SettleErrorWeight
	r.UnitId = p.UnitId
	r.Length = p.Length
	r.SettleWeight = p.SettleWeight
	r.DigitalCode = p.DigitalCode
	r.ShelfNo = p.ShelfNo
	r.ContractNumber = p.ContractNumber
	r.CustomerPoNum = p.CustomerPoNum
	r.AccountNum = p.AccountNum
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.StockRemark = p.StockRemark
	r.Remark = p.Remark
	r.InternalRemark = p.InternalRemark
	r.ScanUserId = p.ScanUserId
	r.ScanUserName = p.ScanUserName
	r.ScanTime = p.ScanTime.ToTime()
}

func (r *FpmArrangeOrderItemFc) SwapChange2ModelfcChange(ctx context.Context, p structure.GetFpmChangeArrangeOrderItemFcChangeData) {
	r.Roll += p.ChangeRoll
	r.Length += p.ChangeLength
	r.BaseUnitWeight += p.ChangeWeight
}

func (r *FpmArrangeOrderItemFc) SwapChange2ModelfcChangeWait(ctx context.Context, p structure.GetFpmChangeArrangeOrderItemFcChangeData) {
	r.Roll -= p.ChangeRoll
	r.Length -= p.ChangeLength
	r.BaseUnitWeight -= p.ChangeWeight
}
