package system

import (
	"go/token"
	"hcscm/tools"
	"strings"
	"text/template"
)

// AutoCodeStructParam 初始版本自动化代码工具
type AutoCodeStructParam struct {
	Param
	StructName         string   `json:"structName"`         // Struct名称
	StructNameList     string   `json:"structNameList"`     // StructList名称
	TableName          string   `json:"tableName"`          // 表名
	PackageName        string   `json:"packageName"`        // 文件名称
	HumpPackageName    string   `json:"humpPackageName"`    // go文件名称
	Abbreviation       string   `json:"abbreviation"`       // Struct简称
	Description        string   `json:"description"`        // Struct中文名称
	AutoCreateResource bool     `json:"autoCreateResource"` // 是否自动创建资源软删除标识
	AutoMoveFile       bool     `json:"autoMoveFile"`       // 是否自动移动文件
	BusinessDB         string   `json:"businessDB"`         // 业务数据库
	Fields             []*Field `json:"fields,omitempty"`
	HasStatus          bool     `json:"-"` // 是否有通用状态
	HasAuditStatus     bool     `json:"-"` // 是否是单据状态
	HasBusinessClose   bool     `json:"-"` // 是否是单据业务关闭
	HasTimer           bool     `json:"-"` // 是否有时间
	IsOrder            bool     `json:"-"` // 是否是单据
	DictTypes          []string `json:"-"`
	Package            string   `json:"package"`
	PackageT           string   `json:"-"`
	NeedValid          bool     `json:"-"`
	NeedSort           bool     `json:"-"`
	EnumList           EnumList `json:"enum_list"` // 新增枚举
}

func (a *AutoCodeStructParam) Pretreatment() {
	a.CopyInField()
	a.KeyWord()
	a.SuffixTest()
	// 处理转义字段
	a.SuffixUint64()
}

// KeyWord 是go关键字的处理加上 _ ，防止编译报错
func (a *AutoCodeStructParam) KeyWord() {
	if token.IsKeyword(a.Abbreviation) {
		a.Abbreviation = a.Abbreviation + "_"
	}
}

// SuffixTest 处理_test 后缀
func (a *AutoCodeStructParam) SuffixTest() {
	if strings.HasSuffix(a.HumpPackageName, "test") {
		a.HumpPackageName = a.HumpPackageName + "_"
	}
}

// SuffixUint64 处理 Id/Id 后缀
func (a *AutoCodeStructParam) SuffixUint64() {
	for _, field := range a.Fields {
		if strings.HasSuffix(field.FieldName, "Id") {
			field.FieldNameChange = strings.TrimSuffix(field.FieldName, "Id") + "Name"
			field.FieldNameChangePb = tools.FirstLower(strings.TrimSuffix(field.FieldName, "Id"))
			field.FieldJsonChange = strings.TrimSuffix(field.FieldJson, "id") + "name"
			field.CommentChange = strings.TrimSuffix(field.Comment, "id") + "名称"
			continue
		}
		if strings.HasSuffix(field.FieldName, "Id") {
			field.FieldNameChange = strings.TrimSuffix(field.FieldName, "Id") + "Name"
			field.FieldNameChangePb = tools.FirstLower(strings.TrimSuffix(field.FieldName, "Id"))
			field.FieldJsonChange = strings.TrimSuffix(field.FieldJson, "id") + "name"
			field.CommentChange = strings.TrimSuffix(field.Comment, "id") + "名称"
			continue
		}
	}
}

func (a *AutoCodeStructParam) CopyInField() {
	var f []*Field
	for _, field := range a.Fields {
		field.Abbreviation = a.Abbreviation
		f = append(f, field)
	}
	a.Fields = f
}

// 处理枚举值
func (a *AutoCodeStructParam) GetEnumList() {
	var (
		enums EnumList
	)
	for _, field := range a.Fields {
		if field.FieldType == "enum" {
			if field.DataTypeLong == "" {
				continue
			}
			var (
				fieldEnum Enum
				num       = 0
			)
			fieldEnum.FieldName = field.FieldName
			fieldEnum.FieldDesc = field.FieldDesc
			fieldEnum.StructName = a.StructName
			dataTypeLongs := strings.Split(field.DataTypeLong, ",")
			for _, dataTypeLong := range dataTypeLongs {
				num++
				if strings.Trim(dataTypeLong, " ") == "" {
					num--
					continue
				}
				names := strings.Split(dataTypeLong, "/")
				if len(names) < 2 {
					continue
				}
				var fieldStatus FieldStatus
				fieldStatus.FieldName = fieldEnum.FieldName
				fieldStatus.Number = num
				fieldStatus.Word = names[0]
				fieldStatus.Name = names[1]
				fieldEnum.FieldStatusList = append(fieldEnum.FieldStatusList, fieldStatus)
			}
			enums = append(enums, fieldEnum)
		}
	}
	a.EnumList = enums
}

type Field struct {
	Abbreviation      string `json:"abbreviation"`      // Struct简称
	FieldName         string `json:"fieldName"`         // Field名
	FieldDesc         string `json:"fieldDesc"`         // 中文名
	FieldType         string `json:"fieldType"`         // Field数据类型
	FieldJson         string `json:"fieldJson"`         // FieldJson
	FieldNameChange   string `json:"fieldNameChange"`   // Field转义名
	FieldNameChangePb string `json:"fieldNameChangePb"` // Field转义名Pb引用
	FieldJsonChange   string `json:"fieldJsonChange"`   // Field转义Json
	DataTypeLong      string `json:"dataTypeLong"`      // 数据库字段长度
	Comment           string `json:"comment"`           // 数据库字段描述
	CommentChange     string `json:"commentChange"`     // 数据库字段转义描述
	ColumnName        string `json:"columnName"`        // 数据库字段
	FieldSearchType   string `json:"fieldSearchType"`   // 搜索条件
	DictType          string `json:"dictType"`          // 字典
	Require           bool   `json:"require"`           // 是否必填
	ErrorText         string `json:"errorText"`         // 校验失败文字
	Clearable         bool   `json:"clearable"`         // 是否可清空
	Sort              bool   `json:"sort"`              // 是否增加排序
}

// 枚举自动创建
type EnumList []Enum

type Enum struct {
	FieldName       string        `json:"field_name"` // Field名
	FieldDesc       string        `json:"fieldDesc"`  // 中文名
	StructName      string        `json:"structName"` // Struct名称
	FieldStatusList []FieldStatus `json:"field_status_list"`
}

// 枚举 数值填写为: 英文名称/名称,English/中文
type FieldStatus struct {
	FieldName string `json:"field_name"` // Field名
	Number    int    `json:"number"`     // 序号
	Word      string `json:"word"`       // 英文名称
	Name      string `json:"name"`       // 名称
}

type AutoCodeStructData struct {
	ResponseData
	AutoCode map[string]string `json:"auto_code"`
}

type AddPackageParam struct {
	Param
	PackageName string `json:"packageName"`
	Label       string `json:"label"`
	Desc        string `json:"desc"`
}

type AddPackageData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type UpdatePackageParam struct {
	Param
	PackageName string `json:"packageName"`
	Label       string `json:"label"`
	Desc        string `json:"desc"`
}

type UpdatePackageData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type GetPackageQuery struct {
	Query
	Id uint64 `form:"id"` // id
}

type GetPackageListQuery struct {
	ListQuery
	PackageName string `json:"packageName"`
	Label       string `json:"label"`
	Desc        string `json:"desc"`
}

type GetPackageData struct {
	RecordData
	PackageName string `json:"packageName"`
	Label       string `json:"label"`
	Desc        string `json:"desc"`
}

type GetPackageDataList []GetPackageData

func (g GetPackageDataList) Adjust() {
}

func (r GetPackageListQuery) Adjust() {
}

type DeletePackageParam struct {
	Param
	Id uint64 `json:"id"`
}

type DeletePackageData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type TplData struct {
	Template         *template.Template
	AutoPackage      string
	LocationPath     string
	AutoCodePath     string
	AutoMoveFilePath string
}

type InjectionMeta struct {
	Path        string
	FuncName    string
	StructNameF string // 带格式化的
}

type AstInjectionMeta struct {
	Path         string
	ImportCodeF  string
	StructNameF  string
	PackageNameF string
	GroupName    string
}

type GetDBData struct {
	ResponseData
	DBs []Db `json:"dbs"`
}

type GetTablesData struct {
	ResponseData
	Tables []Table `json:"tables"`
}

type GetColumnData struct {
	ResponseData
	Columns []Column `json:"columns"`
}

type Db struct {
	Database string `json:"database" gorm:"column:database"`
}

type Table struct {
	TableName string `json:"table_name" gorm:"column:table_name"`
}

type TableCreateSql struct {
	Table       string `gorm:"column:table"`
	CreateTable string `gorm:"column:Create Table"`
}

type Column struct {
	// DataType      string `json:"data_type" gorm:"column:data_type"`
	ColumnType    string `json:"column_type" gorm:"column:column_type"`
	ColumnName    string `json:"column_name" gorm:"column:column_name"`
	ColumnDefault string `json:"column_default" gorm:"column:column_default"`
	// DataTypeLong  string `json:"data_type_long" gorm:"column:data_type_long"`
	ColumnComment string `json:"column_comment" gorm:"column:column_comment"`
}
