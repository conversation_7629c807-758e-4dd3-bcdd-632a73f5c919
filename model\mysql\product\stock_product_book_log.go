package product

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
)

func GetStockProductBookLogIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "stock_product_book_log_id")
}

type StockProductBookLogList []StockProductBookLog

func (r StockProductBookLogList) List() []StockProductBookLog {
	return r
}

func (r StockProductBookLogList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r StockProductBookLogList) One() StockProductBookLog {
	return r[0]
}

func (r StockProductBookLogList) Pick(id uint64) (o StockProductBookLog) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r StockProductBookLogList) PickList(id uint64) (o StockProductBookLogList) {
	var list = make(StockProductBookLogList, 0)
	for _, t := range r {
		if t.Id == id {
			list = append(list, t)
		}
	}
	o = list
	return
}

// StockProductBookLog 库存预约占用日志
type StockProductBookLog struct {
	mysql_base.Model
	Id            uint64                      `gorm:"column:id;primaryKey"`
	StockType     common_system.StockType     `gorm:"column:stock_type"`                 // 库存类型
	OrderId       uint64                      `gorm:"column:order_id" relate:"order_id"` // 占用单id
	OrderNo       string                      `gorm:"column:order_no"`                   // 占用单号
	OrderType     common_system.BookOrderType `gorm:"column:order_type"`                 // 占用单类型
	OrderTypeName string                      `gorm:"column:order_type_name"`            // 占用单类型名
	ProductId     uint64                      `gorm:"column:product_id"`                 // 成品id
	ColorId       uint64                      `gorm:"column:color_id"`                   // 颜色id
	BookUserId    uint64                      `gorm:"column:book_user_id"`               // 预约人id
	BookUserName  string                      `gorm:"column:book_user_name"`             // 预约人名称
	StockId       uint64                      `gorm:"column:stock_id"`                   // 库存id
	BookRoll      int                         `gorm:"column:book_roll"`                  // 预约匹数
	BookWeight    int                         `gorm:"column:book_weight"`                // 预约重量
	BookLength    int                         `gorm:"column:book_length"`                // 预约匹数
	BookOrderId   uint64                      `gorm:"column:book_order_id"`              // 占用单id
}

// 查询后的钩子
func (r *StockProductBookLog) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r StockProductBookLog) GetId() uint64 {
	return r.Id
}

// TableName StockProductBookLog 表名
func (StockProductBookLog) TableName() string {
	return "stock_product_book_log"
}

func (r StockProductBookLog) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (StockProductBookLog) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

// ErrCodeStockProductBookLogAlreadyExist     ErrCode = 52XX1 // 库存预约占用日志已存在
// ErrCodeStockProductBookLogNotExist         ErrCode = 52XX2 // 库存预约占用日志不存在
func (StockProductBookLog) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeStockProductBookLogNotExist
}

func (StockProductBookLog) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeStockProductBookLogAlreadyExist
}

func (r StockProductBookLog) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func NewStockProductBookLogForProduct(
	ctx context.Context,
	p *structure.AddStockProductBookLogParam,
) (r StockProductBookLog) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.StockType = p.StockType
	r.OrderId = p.OrderId
	r.OrderNo = p.OrderNo
	r.OrderType = p.OrderType
	r.OrderTypeName = r.OrderType.String()
	r.ProductId = p.ProductId
	r.ColorId = p.ColorId
	r.BookUserId = p.BookUserId
	r.BookUserName = p.BookUserName
	r.StockId = p.StockId
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	r.BookLength = p.BookLength
	return
}

func (r *StockProductBookLog) BuildRespData(o *structure.GetStockProductBookLogData) {
	o.Id = r.Id
	o.StockType = r.StockType
	o.OrderId = r.OrderId
	o.OrderNo = r.OrderNo
	o.OrderType = r.OrderType
	o.OrderTypeName = r.OrderTypeName
	o.ProductId = r.ProductId
	o.ColorId = r.ColorId
	o.BookUserId = r.BookUserId
	o.BookUserName = r.BookUserName
	o.StockId = r.StockId
	o.BookRoll = r.BookRoll
	o.BookWeight = r.BookWeight
	o.BookLength = r.BookLength
	o.Id = r.Id
	o.CreateTime = tools.MyTime(r.CreateTime)
	o.UpdateTime = tools.MyTime(r.UpdateTime)
	o.CreatorId = r.CreatorId
	o.CreatorName = r.CreatorName
	o.UpdaterId = r.UpdaterId
	o.UpdateUserName = r.UpdaterName
}

func (r *StockProductBookLog) UpdateStockProductBookLog(
	ctx context.Context,
	p *structure.UpdateStockProductBookLogParam,
) {
	r.StockType = p.StockType
	r.OrderId = p.OrderId
	r.OrderNo = p.OrderNo
	r.OrderType = p.OrderType
	r.OrderTypeName = p.OrderTypeName
	r.ProductId = p.ProductId
	r.ColorId = p.ColorId
	r.BookUserId = p.BookUserId
	r.BookUserName = p.BookUserName
	r.StockId = p.StockId
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	r.BookLength = p.BookLength
}

func (r *StockProductBookLog) AddStockProductBookLogByAddStockProductParam(
	ctx context.Context,
	p *structure.AddStockProductParam,
) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.StockType = common_system.StockTypeProduct
	r.OrderId = p.OrderId
	r.OrderNo = p.OrderNo
	r.OrderType = p.OrderType
	r.OrderTypeName = r.OrderType.String()
	r.ProductId = p.ProductId
	r.ColorId = p.ProductColorId
	r.BookUserId = metadata.GetUserId(ctx)
	r.BookUserName = metadata.GetUserName(ctx)
	r.StockId = p.Id
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	r.BookLength = p.BookLength
	r.BookOrderId = p.BookOrderId
}
