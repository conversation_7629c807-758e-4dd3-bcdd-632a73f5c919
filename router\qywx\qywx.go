package qywx

import (
	"hcscm/server/qywx"
	"hcscm/server/system"
)

func InitQYWX(routerGroup *system.RouterGroup) {
	qywxRouter := routerGroup.Group("qywx")
	qywxRouter.GET("企微应用配置列表", "", qywx.GetQYWXBoundList)
	{
		tobeDevelopedApp := qywxRouter.Group("tobe_developed_app")
		tobeDevelopedApp.GET("代开发应用列表", "", qywx.GetTobeDevelopedAppList)
		tobeDevelopedApp.POST("代开发应用绑定账套", "bind_tenant", qywx.TobeDevelopedAppBindTenant)
		tobeDevelopedApp.PUT("代开发应用解绑账套", "cancel_bind_tenant", qywx.TobeDevelopedAppCancelBindTenant)
		tobeDevelopedApp.PUT("绑定阿布机器人", "", qywx.TobeDevelopedAppBindRobot)
		tobeDevelopedApp.GET("群机器人列表", "qywx_robot", qywx.GetQYWXRobotList)
		tobeDevelopedApp.POST("创建群机器人", "qywx_robot", qywx.CreateQYWXRobot)
		tobeDevelopedApp.DELETE("删除群机器人", "qywx_robot", qywx.DeleteQYWXRobot)
		tobeDevelopedApp.GET("企微员工列表", "qywx_users", qywx.GetQYWXUsers)
		tobeDevelopedApp.GET("企微客户列表", "qywx_customers", qywx.GetQYWXCustomers)
		tobeDevelopedApp.GET("企微群聊列表", "qywx_group_chat_list", qywx.GetGroupChatList)
		tobeDevelopedApp.POST("添加外部群", "add_qywx_group_chat", qywx.AddGroupChat)
		tobeDevelopedApp.GET("获取企微客户绑定客户", "get_qywx_bind_rel", qywx.GetQYWXCustomerBindRel)
		tobeDevelopedApp.GET("获取企微签名", "get_qywx_signature", qywx.GetQYWXSignature)
		tobeDevelopedApp.GET("获取企业微信标签列表", "corp_tag_list", qywx.GetCorpTagGroupList)
	}
	{
		visitTag := routerGroup.Group("visit_tag")
		visitTag.POST("添加拜访标签", "add", qywx.AddVisitTag)
		visitTag.PUT("更新拜访标签", "update", qywx.UpdateVisitTag)
		visitTag.GET("获取拜访标签列表", "list", qywx.GetVisitTagDataList)
		visitTag.GET("获取拜访标签枚举列表", "enum_list", qywx.GetMPVisitTagDataList)
		visitTag.GET("获取拜访模式主标签", "visiting_mode", qywx.GetVisitingModeList)
	}
}

func InitCustomerFollowRecord(routerGroup *system.RouterGroup) {
	customerFollowRecord := routerGroup.Group("customer_follow_record")
	customerFollowRecord.POST("新增客户跟进记录", "add", qywx.AddCustomerFollowRecord)
	customerFollowRecord.PUT("更新客户跟进记录", "update", qywx.UpdateCustomerFollowRecord)
	customerFollowRecord.DELETE("删除客户跟进记录", "delete", qywx.DeleteCustomerFollowRecord)
	customerFollowRecord.GET("获取跟进记录详情", "detail", qywx.GetCustomerFollowRecordData)
	customerFollowRecord.GET("获取团队客户跟进记录列表", "teamList", qywx.GetTeamCustomerFollowRecord)
	customerFollowRecord.GET("获取团队月份跟进数", "month_team_record", qywx.GetTeamCustomerFollowRecordNums)
	customerFollowRecord.POST("添加评论", "add_comment", qywx.AddComment)
	customerFollowRecord.GET("获取微信好友列表", "wechatFriends", qywx.GetWechatFriends)
	customerFollowRecord.GET("获取团队拜访记录数据", "team_record", qywx.GetTeamCustomerFollowRecordData)

	customerFollowRecord.GET("获取团队拜访记录产品排行数据", "team_matchable_product_rank", qywx.GetTeamMatchableProductDetailData)
	customerFollowRecord.GET("获取团队拜访记录客户产品排行数据", "team_matchable_product_customer_rank", qywx.GetTeamMatchableProductPurchaserRankDetailList)
	customerFollowRecord.GET("获取团队拜访记录客户建议产品数据", "dev_product_customer_rank", qywx.GetTeamDevProductDetailDataList)
}
