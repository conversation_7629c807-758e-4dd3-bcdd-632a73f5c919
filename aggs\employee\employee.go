package employee

import (
	"context"
	"hcscm/domain/employee/entity"
	mysql "hcscm/model/mysql/employee"
	"hcscm/model/mysql/employee/dao"
	"hcscm/model/mysql/mysql_base"
	mysql_system "hcscm/model/mysql/system"
	structure_base "hcscm/structure/system"
)

func (r *Repo) AddEmployee(ctx context.Context, do *entity.Employee) error {
	po := mysql.ToEmployeePO(do)
	return r.dao.CreateEmployee(ctx, po)
}

func (r *Repo) UpdateEmployee(ctx context.Context, do *entity.Employee) error {
	po := mysql.ToEmployeePO(do)
	return r.dao.UpdateEmployee(ctx, po)
}

func (r *Repo) UpdateMultiEmployeeStatus(ctx context.Context, do []*entity.Employee) error {
	m := map[int][]uint64{}
	for _, item := range do {
		_, ok := m[item.Status]
		if !ok {
			m[item.Status] = []uint64{}
		}
		m[item.Status] = append(m[item.Status], item.Id)
	}
	for status, ids := range m {
		if err := r.dao.UpdateEmployeeStatus(ctx, ids, status); err != nil {
			return err
		}
	}
	return nil
}

func (r *Repo) GetEmployee(ctx context.Context, id uint64) (*entity.Employee, error) {
	po, err := r.dao.QueryEmployee(ctx, id)
	if err != nil {
		return nil, err
	}
	return mysql.ToEmployeeDO(po)
}

func (r *Repo) GetMultiEmployee(ctx context.Context, ids []uint64) ([]*entity.Employee, error) {
	p := &dao.QueryMultiEmployeeParams{IDs: ids}
	po, err := r.dao.QueryMultiEmployee(ctx, p)
	if err != nil {
		return nil, err
	}
	return mysql.ToEmployeeDOs(po)
}

func (r *Repo) GetMaxNumber(ctx context.Context) (int, error) {
	return r.dao.GetMaxNumber(ctx)
}

func (r *Repo) DeleteEmployee(ctx context.Context, do []*entity.Employee) error {
	ids := make([]uint64, 0, len(do))
	for _, item := range do {
		ids = append(ids, item.Id)
	}
	return r.dao.DeleteEmployee(ctx, ids)
}

func (r *Repo) SaveEmployeeSaleSystem(ctx context.Context, do *entity.Employee) error {
	err := r.dao.DeleteEmployeeSaleSystem(ctx, do.Id)
	if err != nil {
		return err
	}

	if len(do.SaleSystem) > 0 {
		err = r.dao.CreateEmployeeSaleSystem(ctx, do.Id, do.SaleSystem)
		if err != nil {
			return err
		}
	}
	return nil
}

func (d *Repo) QueryMultiEmployeeByDuty(duty uint64) (o []*mysql.Employee, err error) {
	var (
		e    mysql.Employee
		cond = mysql_base.NewCondition()
		list []*mysql.Employee
	)
	cond.AddUInt64ListJsonContainMatch("duty", duty)
	err = mysql_base.FindByCond(d.tx, &e, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (r *Repo) QueryEmployeeList(ctx context.Context, tx *mysql_base.Tx, q *structure_base.GetEmployeeListParams) (o []*mysql.Employee, count int, err error) {
	var (
		e    mysql.Employee
		cond = mysql_base.NewCondition()
		list []*mysql.Employee
	)

	if !q.DepartmentID.IsNil() {
		cond.AddContainMatch("department_id", q.DepartmentID.ToUint64())
	}
	if q.Name != "" {
		cond.AddMultiFieldLikeMatch([]string{"name"}, q.Name)
	}
	if q.Code != "" {
		cond.AddMultiFieldLikeMatch([]string{"code"}, q.Code)
	}
	if q.Status != 0 {
		cond.AddEqual("status", q.Status)
	}
	if !q.Duty.IsNil() {
		cond.AddIntListJsonContainMatch("duty", q.Duty.ToInt()...)
	}
	if !q.SaleSystemId.IsNil() {
		cond.AddContainMatch("id", q.Ids)
	}
	count, err = mysql_base.SearchListGroupForPaging(tx, &e, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (r *Repo) QueryEmployeeEnumList(ctx context.Context, tx *mysql_base.Tx, q *structure_base.GetEmployeeListParams) (o []*mysql.Employee, count int, err error) {
	var (
		e           mysql.Employee
		cond        = mysql_base.NewCondition()
		list        []*mysql.Employee
		groupFields []string
	)
	mysql_system.CommonDataSeparate(ctx, &e, cond)

	if !q.DepartmentID.IsNil() {
		cond.AddTableContainMatch(&e, "department_id", q.DepartmentID.ToUint64())
	}
	if q.Name != "" {
		cond.AddTableMultiFieldLikeMatch(&e, []string{"name"}, q.Name)
	}
	if q.Code != "" {
		cond.AddTableMultiFieldLikeMatch(&e, []string{"code"}, q.Code)
	}
	if q.Status != 0 {
		cond.AddTableEqual(&e, "status", q.Status)
	}
	if !q.Duty.IsNil() {
		cond.AddTableIntListJsonContainMatch(&e, "duty", q.Duty.ToInt()...)
	}
	if !q.SaleSystemId.IsNil() {
		cond.AddTableContainMatch(&e, "id", q.Ids)
	}
	groupFields = []string{"employee.id"}
	cond.AddTableEqual(&e, "delete_time", "0000-00-00 00:00:00")
	count, err = mysql_base.SearchListGroupForPaging(tx, &e, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func (r *Repo) QueryEmployeeSaleSystemByIds(ctx context.Context, ids []uint64) ([]*mysql.EmployeeSaleSystemRel, error) {
	p := &dao.QueryEmployeeSaleSystemParams{EmployeeIDs: ids}
	return r.dao.QueryEmployeeSaleSystem(ctx, p)
}

func (r *Repo) QueryEmployee(ctx context.Context, id uint64) (*mysql.Employee, error) {
	return r.dao.QueryEmployee(ctx, id)
}

func (r *Repo) QueryEmployeeByCodeOrName(ctx context.Context, code, name []string) ([]*mysql.Employee, error) {
	return r.dao.QueryMultiEmployeeByCodeOrName(ctx, code, name)
}

func (r *Repo) QueryEmployeeByLikeCodeOrName(ctx context.Context, codeOrName string) ([]*mysql.Employee, error) {
	return r.dao.QueryMultiEmployeeByLikeCodeOrName(ctx, codeOrName)
}

func (r *Repo) QueryEmployeeByIds(ctx context.Context, ids []uint64) ([]*mysql.Employee, error) {
	p := &dao.QueryMultiEmployeeParams{IDs: ids}
	return r.dao.QueryMultiEmployee(ctx, p)
}

func (r *Repo) QueryEmployeeSaleSystem(ctx context.Context, id uint64) ([]uint64, error) {
	p := &dao.QueryEmployeeSaleSystemParams{EmployeeIDs: []uint64{id}}
	po, err := r.dao.QueryEmployeeSaleSystem(ctx, p)
	if err != nil {
		return nil, err
	}
	saleSystemIds := make([]uint64, 0, len(po))
	for _, item := range po {
		saleSystemIds = append(saleSystemIds, item.SaleSystemID)
	}
	return saleSystemIds, nil
}

func (r *Repo) QueryEmployeeByDepartmentIDs(ctx context.Context, departmentIDs []uint64) ([]*mysql.Employee, error) {
	p := &dao.QueryEmployeeDepartmentIDsParams{DepartmentIDs: departmentIDs}
	return r.dao.QueryEmployeeDepartmentIDs(ctx, p)
}

func (r *Repo) JudgeEmpIsChecker(ctx context.Context, tx *mysql_base.Tx, id uint64, duty int) (exit bool, err error) {
	var (
		e    mysql.Employee
		cond = mysql_base.NewCondition()
	)
	mysql_system.CommonDataSeparate(ctx, &e, cond)

	cond.AddEqual("id", id)
	cond.AddIntListJsonContainMatch("duty", duty)
	exit, err = mysql_base.FirstByCond(tx, &e, cond)
	if err != nil {
		return
	}
	return
}

func (r *Repo) QueryEmployeeIdsBySaleSystemIds(ctx context.Context, saleSysId []uint64) ([]uint64, error) {
	p := &dao.QueryEmployeeSaleSystemParams{SaleSystemIds: saleSysId}
	po, err := r.dao.QueryEmployeeSaleSystem(ctx, p)
	if err != nil {
		return nil, err
	}
	ids := make([]uint64, 0, len(po))
	for _, item := range po {
		ids = append(ids, item.EmployeeID)
	}
	return ids, nil
}

func (r *Repo) GetEmployeeByQYWXUserIDs(ctx context.Context, id []string) (list mysql.EmployeeList, err error) {
	list, err = r.dao.QueryEmployeeByQYWXUserIDs(ctx, id)
	return
}
