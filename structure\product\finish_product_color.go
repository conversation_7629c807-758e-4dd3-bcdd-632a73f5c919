package product

import (
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFinishProductColorParamList []AddFinishProductColorParam

func (r AddFinishProductColorParamList) Adjust() {

}

type AddFinishProductColorParam struct {
	structure_base.Param
	FinishProductId               uint64                                  `json:"finish_product_id"  mean:"成品id" validates:"required"` // 成品id
	FinishProductCraft            string                                  `json:"finish_product_craft"`                                // 成品工艺
	TypeGreyFabricId              uint64                                  `json:"type_grey_fabric_id"`                                 // 布种类型id
	GreyFabricId                  uint64                                  `json:"grey_fabric_id"`                                      // 坯布信息ID
	TypeFinishedProductKindId     uint64                                  `json:"type_finished_product_kind_id"`                       // 颜色类别id
	ProductColorCode              string                                  `json:"product_color_code" mean:"颜色编号" validates:"required"` // 颜色编号
	ProductColorName              string                                  `json:"product_color_name" mean:"颜色名称" validates:"required"` // 颜色名称
	ProductColorFullName          string                                  `json:"product_color_full_name"`                             // 颜色全称(用于搜索)
	LengthToWeightRate            int                                     `json:"length_to_weight_rate"`                               // 长度转数量(公斤/米)
	BulkMinSafeAmount             int                                     `json:"bulk_min_safe_amount"`                                // 大货最低安全库存数
	BulkMaxSafeAmount             int                                     `json:"bulk_max_safe_amount"`                                // 大货最高安全库存数
	LengthCutMinSafeAmount        int                                     `json:"length_cut_min_safe_amount"`                          // 剪版最低安全库存数量
	LengthCutMaxSafeAmount        int                                     `json:"length_cut_max_safe_amount"`                          // 剪版最高安全库存数量
	IsComposite                   bool                                    `json:"is_composite"`                                        // 是否是复合布
	Status                        common_system.Status                    `json:"status"`                                              // 状态
	Remark                        string                                  `json:"remark"`                                              // 备注
	CompositeItemData             AddFinishProductCompositeColorParamList `json:"composite_item_data"`                                 // 复合布信息
	DyeingColorItemData           AddFinishProductDyeingColorParamList    `json:"dyeing_color_item_data"`                              // 染厂颜色信息
	TextureURL                    tools.QueryStringList                   `json:"texture_url"`                                         // 纹理图片URL（详情显示）
	CoverTextureURL               string                                  `json:"cover_texture_url"`                                   // 封面纹理图片URL（详情显示）
	YarnCount                     string                                  `json:"yarn_count"`                                          // 纱支
	Density                       string                                  `json:"density"`                                             // 密度
	FinishProductWidth            string                                  `json:"finish_product_width"`                                // 成品幅宽
	FinishProductGramWeight       string                                  `json:"finish_product_gram_weight"`                          // 成品克重
	FinishProductWidthUnitId      uint64                                  `json:"finish_product_width_unit_id"`                        // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                                  `json:"finish_product_gram_weight_unit_id"`                  // 成品克重单位id(字典)
	WeavingOrganizationId         uint64                                  `json:"weaving_organization_id"`                             // 织造组织id
	BleachId                      uint64                                  `json:"bleach_id"`                                           // 漂染性id（字典）
	ShrinkageWarp                 string                                  `json:"shrinkage_warp" excel:"缩率"`                           // 缩率率(经向)
	SupplierId                    []uint64                                `json:"supplier_id"`                                         // 供应商
}

type AddFinishProductColorData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type QuickAddFinishProductColorParam struct {
	structure_base.Param
	ProductId            uint64 `json:"product_id"  mean:"成品id" validates:"required"` // 成品id
	ProductColorCode     string `json:"product_color_code"`                           // 颜色编号
	ProductColorName     string `json:"product_color_name"`                           // 颜色名称
	ProductColorFullName string `json:"product_color_full_name"`                      // 颜色全称(用于搜索)
}

type UpdateFinishProductColorParam struct {
	structure_base.Param
	Id                            uint64                                  `json:"id"`
	FinishProductIds              tools.QueryIntList                      `json:"-"`                                  // 成品id
	FinishProductId               uint64                                  `json:"finish_product_id"`                  // 成品id
	FinishProductCraft            string                                  `json:"finish_product_craft"`               // 成品工艺
	TypeGreyFabricId              uint64                                  `json:"type_grey_fabric_id"`                // 布种类型id
	GreyFabricId                  uint64                                  `json:"grey_fabric_id"`                     // 坯布信息ID
	TypeFinishedProductKindId     uint64                                  `json:"type_finished_product_kind_id"`      // 颜色类别id
	ProductColorCode              string                                  `json:"product_color_code"`                 // 颜色编号
	ProductColorName              string                                  `json:"product_color_name"`                 // 颜色名称
	LengthToWeightRate            int                                     `json:"length_to_weight_rate"`              // 长度转数量(公斤/米)
	BulkMinSafeAmount             int                                     `json:"bulk_min_safe_amount"`               // 大货最低安全库存数
	BulkMaxSafeAmount             int                                     `json:"bulk_max_safe_amount"`               // 大货最高安全库存数
	LengthCutMinSafeAmount        int                                     `json:"length_cut_min_safe_amount"`         // 剪版最低安全库存数量
	LengthCutMaxSafeAmount        int                                     `json:"length_cut_max_safe_amount"`         // 剪版最高安全库存数量
	IsComposite                   bool                                    `json:"is_composite"`                       // 是否是复合布
	Status                        common_system.Status                    `json:"status"`                             // 状态
	Remark                        string                                  `json:"remark"`                             // 备注
	CompositeItemData             AddFinishProductCompositeColorParamList `json:"composite_item_data"`                // 复合布信息
	DyeingColorItemData           AddFinishProductDyeingColorParamList    `json:"dyeing_color_item_data"`             // 染厂颜色信息
	TextureURL                    tools.QueryStringList                   `json:"texture_url"`                        // 纹理图片URL（详情显示）
	CoverTextureURL               string                                  `json:"cover_texture_url"`                  // 封面纹理图片URL（详情显示）
	YarnCount                     string                                  `json:"yarn_count"`                         // 纱支
	Density                       string                                  `json:"density"`                            // 密度
	FinishProductWidth            string                                  `json:"finish_product_width"`               // 成品幅宽
	FinishProductGramWeight       string                                  `json:"finish_product_gram_weight"`         // 成品克重
	FinishProductWidthUnitId      uint64                                  `json:"finish_product_width_unit_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                                  `json:"finish_product_gram_weight_unit_id"` // 成品克重单位id(字典)
	WeavingOrganizationId         uint64                                  `json:"weaving_organization_id"`            // 织造组织id
	BleachId                      uint64                                  `json:"bleach_id"`                          // 漂染性id（字典）
	ShrinkageWarp                 string                                  `json:"shrinkage_warp" excel:"缩率"`          // 缩率率(经向)
	SupplierId                    []uint64                                `json:"supplier_id"`                        // 供应商
}

// 新增染厂色号(快捷新增)
type AddProductDyeColorParam struct {
	structure_base.Param
	ProductID           uint64                         `json:"product_id"`             // 成品id
	DyeFactoryId        uint64                         `json:"dye_factory_id"`         // 染厂id
	DyeingColorItemData AddProductDyeingColorParamList `json:"dyeing_color_item_data"` // 染厂颜色信息
}

type AddProductDyeingColorParam struct {
	structure_base.Param
	ProductColorId      uint64 `json:"product_color_id"`       // 成品颜色id
	DyeFactoryColorCode string `json:"dye_factory_color_code"` // 染厂颜色编号
	DyeFactoryColorName string `json:"dye_factory_color_name"` // 染厂颜色名称
	DyelotNumber        string `json:"dyelot_number"`          // 对应缸号
}

type AddProductDyeingColorParamList []*AddProductDyeingColorParam

type UpdateFinishProductColorData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type DeleteFinishProductColorParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFinishProductColorData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type UpdateFinishProductColorStatusParam struct {
	structure_base.Param
	Id     tools.QueryIntList   `json:"id"`
	Status common_system.Status `json:"status"`
}

type UpdateFinishProductColorStatusData struct {
	structure_base.ResponseData
}

type GetFinishProductColorQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFinishProductColorListQuery struct {
	structure_base.ListQuery
	FinishProductId           uint64               `form:"finish_product_id"`             // 成品id
	ProductId                 tools.QueryIntList   `form:"product_id"`                    // 成品id,逗号拼接
	FinishProductCraft        string               `form:"finish_product_craft"`          // 成品工艺
	TypeGreyFabricId          uint64               `form:"type_grey_fabric_id"`           // 布种类型id
	GreyFabricId              uint64               `form:"grey_fabric_id"`                // 坯布信息ID
	TypeFinishedProductKindId uint64               `form:"type_finished_product_kind_id"` // 颜色类别id
	ProductColorId            uint64               `form:"product_color_id"`              // 颜色id
	ProductColorCode          string               `form:"product_color_code"`            // 颜色编号
	ProductColorName          string               `form:"product_color_name"`            // 颜色名称
	ProductColorCodeOrName    string               `form:"product_color_code_or_name"`    // 颜色编号或名称
	LengthToWeightRate        int                  `form:"length_to_weight_rate"`         // 长度转数量(公斤/米)
	BulkMinSafeAmount         int                  `form:"bulk_min_safe_amount"`          // 大货最低安全库存数
	BulkMaxSafeAmount         int                  `form:"bulk_max_safe_amount"`          // 大货最高安全库存数
	LengthCutMinSafeAmount    int                  `form:"length_cut_min_safe_amount"`    // 剪版最低安全库存数量
	LengthCutMaxSafeAmount    int                  `form:"length_cut_max_safe_amount"`    // 剪版最高安全库存数量
	IsComposite               bool                 `form:"is_composite"`                  // 是否是复合布
	Status                    common_system.Status `form:"status"`                        // 状态
	FieldSearch               string               `form:"field_search"`                  // 字段搜索
	NoAvailableStock          bool                 `form:"no_available_stock"`            // 显示无库存
	Codes                     []string
	Names                     []string
	UintProductId             uint64
	IsFindByCodeOrName        bool
	FieldSearchProductIds     []uint64
	IsPage                    bool `form:"-"`
}

func (r *GetFinishProductColorListQuery) Adjust() {
	r.IsPage = true
}

type GetFinishProductColorData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ProductColorCode            string               `json:"product_color_code" excel:"颜色编号,必填"`                                             // 颜色编号
	ProductColorName            string               `json:"product_color_name" excel:"颜色名称,必填"`                                             // 颜色名称
	TypeFinishedProductKindId   uint64               `json:"type_finished_product_kind_id"`                                                  // 颜色类别id
	TypeFinishedProductKindName string               `json:"type_finished_product_kind_name" excel:"颜色类别,必填"`                                // 颜色类别名
	FinishProductId             uint64               `json:"finish_product_id"`                                                              // 成品id
	FinishProductCode           string               `json:"finish_product_code" excel:"成品编号,必填"`                                            // 成品编号
	FinishProductName           string               `json:"finish_product_name" excel:"成品名称"`                                               // 成品名称
	FinishProductFullName       string               `json:"finish_product_full_name"`                                                       // 成品全称
	FinishProductCraft          string               `json:"finish_product_craft" excel:"成品工艺"`                                              // 成品工艺
	TypeGreyFabricId            uint64               `json:"type_grey_fabric_id"`                                                            // 布种类型id
	TypeGreyFabricCode          string               `json:"type_grey_fabric_code" excel:"布种类型编号"`                                           // 布种类型编号
	TypeGreyFabricName          string               `json:"type_grey_fabric_name" excel:"布种类型名称"`                                           // 布种类型名称
	GreyFabricId                uint64               `json:"grey_fabric_id"`                                                                 // 坯布信息ID
	GreyFabricCode              string               `json:"grey_fabric_code" excel:"坯布编号"`                                                  // 坯布编号
	GreyFabricName              string               `json:"grey_fabric_name" excel:"坯布名称"`                                                  // 坯布名称
	LengthToWeightRate          int                  `json:"length_to_weight_rate" excel:"长度转数量(公斤/米)"`                                      // 长度转数量(公斤/米)
	BulkMinSafeAmount           int                  `json:"bulk_min_safe_amount" excel:"大货最低安全库存数"`                                         // 大货最低安全库存数
	BulkMaxSafeAmount           int                  `json:"bulk_max_safe_amount" excel:"大货最高安全库存数"`                                         // 大货最高安全库存数
	LengthCutMinSafeAmount      int                  `json:"length_cut_min_safe_amount" excel:"剪版最低安全库存数量"`                                  // 剪版最低安全库存数量
	LengthCutMaxSafeAmount      int                  `json:"length_cut_max_safe_amount" excel:"剪版最高安全库存数量"`                                  // 剪版最高安全库存数量
	IsComposite                 bool                 `json:"is_composite" excel:"是否是复合布"`                                                    // 是否是复合布
	Status                      common_system.Status `json:"status"`                                                                         // 状态
	StatusName                  string               `json:"status_name"`                                                                    // 状态
	Remark                      string               `json:"remark" excel:"备注"`                                                              // 备注
	TextureURL                  []string             `json:"texture_url"`                                                                    // 纹理图片URL（详情显示）
	CoverTextureURL             string               `json:"cover_texture_url"`                                                              // 封面纹理图片URL（详情显示）
	FinishProductIngredient     string               `json:"finish_product_ingredient" excel:"成品成分"`                                         // 成品成分
	YarnCount                   string               `json:"yarn_count" excel:"纱支"`                                                          // 纱支
	Density                     string               `json:"density" excel:"密度"`                                                             // 密度
	WeavingOrganizationId       uint64               `json:"weaving_organization_id"`                                                        // 织造组织id
	WeavingOrganizationCode     string               `json:"weaving_organization_code" excel:"织造组织编号"`                                       // 织造组织编号
	WeavingOrganizationName     string               `json:"weaving_organization_name" excel:"织造组织名称"`                                       // 织造组织名称
	BleachId                    uint64               `json:"bleach_id"`                                                                      // 漂染性id（字典）
	BleachName                  string               `json:"bleach_name" excel:"漂染性名称"`                                                      // 漂染性名称
	MainTextureUrl              string               `json:"main_texture_url"`                                                               // 主图纹理图片URL
	ShrinkageWarp               string               `json:"shrinkage_warp" excel:"缩率"`                                                      // 缩率率(经向)
	SupplierId                  []uint64             `json:"supplier_id" desensitization:"ids=decryption_supplier_name"`                     // 供应商id,多选
	SupplierName                string               `json:"supplier_name" excel:"供应商,逗号隔开" desensitization:"name=decryption_supplier_name"` // 供应商名称,逗号拼接
}

type GetFinishProductColorDataList []GetFinishProductColorData

func (g GetFinishProductColorDataList) Adjust() {

}

type GetFinishProductColorDropdownData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	FinishProductId             uint64                `json:"finish_product_id"`               // 成品id
	FinishProductCode           string                `json:"finish_product_code"`             // 成品编号
	FinishProductName           string                `json:"finish_product_name"`             // 成品名称
	FinishProductFullName       string                `json:"finish_product_full_name"`        // 成品全称
	TypeGreyFabricId            uint64                `json:"type_grey_fabric_id"`             // 布种类型id
	TypeGreyFabricCode          string                `json:"type_grey_fabric_code"`           // 布种类型编号
	TypeGreyFabricName          string                `json:"type_grey_fabric_name"`           // 布种类型名称
	GreyFabricId                uint64                `json:"grey_fabric_id"`                  // 坯布信息ID
	GreyFabricCode              string                `json:"grey_fabric_code"`                // 坯布编号
	GreyFabricName              string                `json:"grey_fabric_name"`                // 坯布名称
	TypeFinishedProductKindId   uint64                `json:"type_finished_product_kind_id"`   // 颜色类别id
	TypeFinishedProductKindName string                `json:"type_finished_product_kind_name"` // 颜色类别名称
	ProductColorCode            string                `json:"product_color_code"`              // 颜色编号
	ProductColorName            string                `json:"product_color_name"`              // 颜色名称
	MergeCodeName               string                `json:"merge_code_name"`                 // 拼接颜色编号和名称
	LengthToWeightRate          int                   `json:"length_to_weight_rate"`           // 长度转数量(公斤/米)
	YarnCount                   string                `json:"yarn_count"`                      // 纱支
	Density                     string                `json:"density"`                         // 密度
	Remark                      string                `json:"remark"`                          // 备注
	TextureURL                  mysql_base.StringList `json:"texture_url"`                     // 纹理图片URL(单张)
	CoverTextureURL             string                `json:"cover_texture_url"`               // 封面纹理图片URL（详情显示）

	MeasurementUnitId   uint64 `json:"measurement_unit_id"`   // 默认计量单位id(小程序使用)
	MeasurementUnitName string `json:"measurement_unit_name"` // 默认计量单位名称(小程序使用)
}

type GetFinishProductColorDropdownDataList []GetFinishProductColorDropdownData

func (g GetFinishProductColorDropdownDataList) Adjust() {

}

type GetTypeFinishedProductColorListQuery struct {
	structure_base.ListQuery
	ProductId uint64   `form:"product_id"`
	Ids       []uint64 `form:"-"`
	Code      string   `form:"code"`
	Name      string   `form:"name"`
}

type GetTypeFinishedProductColorData struct {
	Id               uint64                  `json:"id"`                 // id
	Code             string                  `json:"code"`               // 编号
	Name             string                  `json:"name"`               // 名称
	ProductColorList []*GetPriceProductColor `json:"product_color_list"` // 面料颜色列表
}

type GetPriceProductColor struct {
	ProductColorID   uint64 `json:"product_color_id"`   // 面料颜色ID
	ProductColorCode string `json:"product_color_code"` // 面料颜色编号
	ProductColorName string `json:"product_color_name"` // 面料颜色名称
}

type GetTypeFinishedProductColorDataList []GetTypeFinishedProductColorData

func (q GetTypeFinishedProductColorDataList) Adjust() {

}

type GetSomeFinishProductColorFieldData struct {
	Id               uint64 `json:"id"`                 // Id
	ProductColorCode string `json:"product_color_code"` // 编号
}

type GetSomeFinishProductColorFieldDataList []*GetSomeFinishProductColorFieldData

func (g GetSomeFinishProductColorFieldDataList) Adjust() {
}
