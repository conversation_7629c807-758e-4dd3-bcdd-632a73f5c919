package dao

import (
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/mysql/third_party_extra"
	"hcscm/structure/third_party_extra_structure"
)

// GetPersonalCenterByUserId 根据用户ID获取个人中心信息
func GetPersonalCenterByUserId(tx *mysql_base.Tx, userId uint64) (personalCenter third_party_extra.PersonalCenter, exist bool, err error) {
	cond := mysql_base.NewCondition()
	cond.AddEqual("user_id", userId)
	exist, err = mysql_base.FirstByCond(tx, &personalCenter, cond)
	return
}

// CreatePersonalCenter 创建个人中心信息
func CreatePersonalCenter(tx *mysql_base.Tx, personalCenter *third_party_extra.PersonalCenter) (err error) {
	err = mysql_base.MustCreateModel(tx, personalCenter)
	return
}

// UpdatePersonalCenter 更新个人中心信息
func UpdatePersonalCenter(tx *mysql_base.Tx, personalCenter *third_party_extra.PersonalCenter) (err error) {
	err = mysql_base.MustUpdateModel(tx, personalCenter)
	return
}

// DeletePersonalCenter 删除个人中心信息
func DeletePersonalCenter(tx *mysql_base.Tx, id uint64) (err error) {
	cond := mysql_base.NewCondition()
	cond.AddEqual("id", id)
	err = mysql_base.MustDeleteByCond(tx, &third_party_extra.PersonalCenter{}, cond)
	return
}

// GetPersonalCenterList 获取个人中心列表
func GetPersonalCenterList(tx *mysql_base.Tx, req third_party_extra_structure.GetPersonalCenterListRequest) (list third_party_extra.PersonalCenterList, total int, err error) {
	cond := mysql_base.NewCondition()
	if req.UserId != 0 {
		cond.AddEqual("user_id", req.UserId)
	}
	if req.Status != nil {
		cond.AddEqual("status", *req.Status)
	}
	if req.Keyword != "" {
		cond.AddLike("nickname", req.Keyword)
	}
	total, err = mysql_base.SearchListGroupForPaging(tx, &third_party_extra.PersonalCenter{}, req, &list, cond)
	return
}
