/*系统基础功能枚举*/
package common

type IEnumerable interface {
	Check() bool
}

// 数据类型
//
//go:generate stringer -type=DataType --linecomment
//go:generate generate --file=consts.go  --type=DataType
type DataType int

const (
	DataTypeRawMatl    DataType = 1 // 原料
	DataTypeGreyFabric DataType = 2 // 坯布
	DataTypeProduct    DataType = 3 // 成品
)

// 打印模板类型
//
//go:generate stringer -type=PrintTemplateType --linecomment
//go:generate generate --file=consts.go  --type=PrintTemplateType
type PrintTemplateType int

const (
	PrintTemplateTypePurOrder                      PrintTemplateType = 1  // 采购单
	PrintTemplateTypePurReturnOrder                PrintTemplateType = 2  // 采购退货单
	PrintTemplateTypeProduceNotify                 PrintTemplateType = 3  // 生产通知单
	PrintTemplateTypeDNFNotify                     PrintTemplateType = 4  // 染整通知单
	PrintTemplateTypeSaleOrder                     PrintTemplateType = 5  // 销售单
	PrintTemplateTypeShouldCollectOrderProductSale PrintTemplateType = 6  // 销售送货单
	PrintTemplateTypeSaleTransferOrder             PrintTemplateType = 14 // 调货销售单
	PrintTemplateTypeSaleTransferOrderRtn          PrintTemplateType = 15 // 调货退货单
	PrintTemplateTypeQualityCheckOrder             PrintTemplateType = 16 // 质检管理
	PrintTemplateTypePurchaserReconciliation       PrintTemplateType = 17 // 客户对账单
	PrintTemplateTypeSupplierReconciliation        PrintTemplateType = 18 // 供应商对账单
	PrintTemplateTypeSpotFabricOrder               PrintTemplateType = 19 // 发货配布单
	PrintTemplateTypeStock                         PrintTemplateType = 50 // 库存打码
	PrintTemplateTypeWarehouseBin                  PrintTemplateType = 51 // 仓位打印
	PrintTemplateTypeLabel                         PrintTemplateType = 52 // 标签打印
	PrintTemplateTypeColorLabel                    PrintTemplateType = 53 // 颜色标签打印
)

// 消息队列类型
//
//go:generate stringer -type=MqMessageType --linecomment
//go:generate generate --file=consts.go  --type=MqMessageType
type MqMessageType int

const (
	MqMessageTypeSalePlanOrder            MqMessageType = 1  // 销售计划单反写
	MqMessageTypeModifyProductStock       MqMessageType = 2  // 修改成品库存
	MqMessageTypeSalePriceAdjustOrder     MqMessageType = 3  // 销售调价单生效
	MqMessageTypeUserAccess               MqMessageType = 4  // 用户权限
	MqMessageTypeSalePriceAdjustOrderWait MqMessageType = 5  // 销售调价单消审
	MqMessageTypeAliPay                   MqMessageType = 6  // 支付宝支付
	MqMessageTypeTenantManagementExpire   MqMessageType = 7  // 租户管理过期
	MqMessageTypeTenantPackageExpire      MqMessageType = 8  // 账套套餐过期
	MqMessageTypeTenantPackageCancel      MqMessageType = 9  // 账套套餐作废
	MqMessageTypeNewTenantNotify          MqMessageType = 10 // 新建账套通知
	MqMessageTypeCustomerFeedback         MqMessageType = 11 // 客户反馈
	MqMessageTyepeOcrCodeManagementExpire MqMessageType = 12 // OCR码单识别过期
	MqMessageTypeOcrCodePackageExpire     MqMessageType = 13 // 码单识别套餐过期
	MqMessageTypeSearchImageUpload        MqMessageType = 14 // 搜索图片上传

)

// 是否离职
//
//go:generate stringer -type=IsResign --linecomment
//go:generate generate --file=consts.go  --type=IsResign
type IsResign int

const (
	IsResignUnKnown IsResign = 0 // 未知
	IsResignYes     IsResign = 1 // 是
	IsResignNo      IsResign = 2 // 否
)

// RoleType 角色类型
//
//go:generate stringer -type=RoleType --linecomment
//go:generate generate --file=consts.go  --type=RoleType
type RoleType int

const (
	RoleTypeDepartment RoleType = 1 // 部门权限
	RoleTypeWarehouse  RoleType = 2 // 仓库权限
	RoleTypeUser       RoleType = 3 // 个人权限
	RoleTypeCompany    RoleType = 4 // 公司权限
	RoleTypeDuty       RoleType = 5 // 职务权限
)

// 状态
//
//go:generate stringer -type=Status --linecomment
//go:generate generate --file=consts.go  --type=Status
type Status int

const (
	StatusEnable   Status = 1 // 启用
	StatusDisable  Status = 2 // 禁用
	StatusNoActive Status = 3 // 未激活
)

// 排序
//
//go:generate stringer -type=Sort --linecomment
//go:generate generate --file=consts.go  --type=Sort
type Sort int

const (
	SortAsc  Sort = 1 // 升序
	SortDesc Sort = 2 // 倒序
)

// Platform 终端
//
//go:generate stringer -type=Platform --linecomment
//go:generate generate --file=consts.go  --type=Platform
type Platform int

const (
	PlatformDebug      Platform = 0 // DEBUG
	PlatformAdmin      Platform = 1 // 后台端
	PlatformPDA        Platform = 2 // 终端
	PlatformMP         Platform = 3 // 小程序端
	PlatformWxCB       Platform = 4 // 微信回调
	PlatformAPI        Platform = 5 // 公开API
	PlatformH5         Platform = 6 // h5
	PlatformThirdParty Platform = 7 // 第三方
)

// DepartmentType 部门类型
//
//go:generate stringer -type=DepartmentType --linecomment
//go:generate generate --file=consts.go  --type=DepartmentType
type DepartmentType int

const (
	DepartmentTypeUnknown DepartmentType = 0 // 未知部门
	DepartmentTypeIT      DepartmentType = 1 // 技术部
	DepartmentTypeSale    DepartmentType = 2 // 营销部
)

// 角色数据权限范围
//
//go:generate stringer -type=RoleAccessDataScope --linecomment
//go:generate generate --file=consts.go  --type=RoleAccessDataScope
type RoleAccessDataScope int

const (
	RoleAccessDataScopeSaasCompany        RoleAccessDataScope = 1 // 整个saas
	RoleAccessDataScopeCompany            RoleAccessDataScope = 2 // 整个公司
	RoleAccessDataScopeDepartment         RoleAccessDataScope = 3 // 所在部门
	RoleAccessDataScopeDepartmentAndBelow RoleAccessDataScope = 4 // 所在部门及以下
	RoleAccessDataScopePerson             RoleAccessDataScope = 5 // 仅个人
	RoleAccessDataScopeOther              RoleAccessDataScope = 6 // 其他
)

// 是否需要验证权限
//
//go:generate stringer -type=NeedAuth --linecomment
//go:generate generate --file=consts.go  --type=NeedAuth
type NeedAuth int

const (
	NeedAuthNo  NeedAuth = 1 // 否
	NeedAuthYes NeedAuth = 2 // 是
)

// SupplierType 供应商类型
//
//go:generate stringer -type=SupplierType --linecomment
//go:generate generate --file=consts.go  --type=SupplierType
type SupplierType int

const (
	SupplierTypeFabricSupplier   SupplierType = 1 // 面料供应商
	SupplierTypeClothSupplier    SupplierType = 2 // 胚布供应商
	SupplierTypeMaterialSupplier SupplierType = 3 // 原料供应商
)

// 路由状态
//
//go:generate stringer -type=RouterStatus --linecomment
//go:generate generate --file=consts.go  --type=RouterStatus
type RouterStatus int

const (
	RouterStatusEnable  RouterStatus = 1 // 启用
	RouterStatusDisable RouterStatus = 2 // 禁用
)

// 模块类型
//
//go:generate stringer -type=RouterType --linecomment
//go:generate generate --file=consts.go  --type=RouterType
type RouterType int

const (
	RouterTypeBackEnd  RouterType = 1 // 后端模块
	RouterTypeFrontEnd RouterType = 2 // 前端模块
)

// 后台消息类型
//
//go:generate stringer -type=NotifyMessageType --linecomment
//go:generate generate --file=consts.go  --type=NotifyMessageType
type NotifyMessageType int

const (
	NotifyMessageTypeTest NotifyMessageType = 0 // 测试消息
)

// 行政地区级别
//
//go:generate stringer -type=DistrictLevel --linecomment
//go:generate generate --file=consts.go  --type=DistrictLevel
type DistrictLevel int

const (
	DistrictLevelNone     DistrictLevel = 0 // 空
	DistrictLevelCountry  DistrictLevel = 1 // 国家
	DistrictLevelProvince DistrictLevel = 2 // 省份
	DistrictLevelCity     DistrictLevel = 3 // 城市
	DistrictLevelDistrict DistrictLevel = 4 // 镇区
	DistrictLevelStreet   DistrictLevel = 5 // 街道
)

// CDNOperateScene CDN操作场景
//
//go:generate stringer -type=CDNOperateScene --linecomment
//go:generate generate --file=consts.go  --type=CDNOperateScene
type CDNOperateScene int

const (
	CDNOperateSceneUploadSaleOrderWeight CDNOperateScene = 1 // 上传销售单细码图
)

// RadixPointSign 小数位
//
//go:generate stringer -type=RadixPointSign --linecomment
//go:generate generate --file=consts.go  --type=RadixPointSign
type RadixPointSign int

const (
	RadixPointSignNotLimit RadixPointSign = 1 // 不限制
	RadixPointSignZero     RadixPointSign = 2 // 0
	RadixPointSignOne      RadixPointSign = 3 // 1
	RadixPointSignTwo      RadixPointSign = 4 // 2
	RadixPointSignThird    RadixPointSign = 5 // 3
	RadixPointSignFour     RadixPointSign = 6 // 4
	RadixPointSignFive     RadixPointSign = 7 // 5
)

// SettleType 默认结算类型
//
//go:generate stringer -type=SettleType --linecomment
//go:generate generate --file=consts.go  --type=SettleType
type SettleType int

const (
	SettleTypeCash  SettleType = 1 // 现金结算
	SettleTypeDay   SettleType = 2 // 按天结算
	SettleTypeCycle SettleType = 3 // 按周期结算
	SettleTypeMonth SettleType = 4 // 按月结算
)

// SettleCycle 结算周期
//
//go:generate stringer -type=SettleCycle --linecomment
//go:generate generate --file=consts.go  --type=SettleCycle
type SettleCycle int

const (
	SettleCycleWeek      SettleCycle = 7  // 按星期结算
	SettleCycleTen       SettleCycle = 10 // 按10天结算
	SettleCycleFifteen   SettleCycle = 15 // 按15天结算
	SettleCycleFortyFive SettleCycle = 45 // 按45天结算
	SettleCycleSixty     SettleCycle = 60 // 按60天结算
	SettleCycleNinety    SettleCycle = 90 // 按90天结算
)

// CreditLevel 信用等级
//
//go:generate stringer -type=CreditLevel --linecomment
//go:generate generate --file=consts.go  --type=CreditLevel
type CreditLevel int

const (
	CreditLevelNice CreditLevel = 1 // 优秀
	CreditLevelGood CreditLevel = 2 // 良好
	CreditLevelBad  CreditLevel = 3 // 差
)

// UserType 用户类型
//
//go:generate stringer -type=UserType --linecomment
//go:generate generate --file=consts.go  --type=UserType
type UserType int

const (
	UserTypeAdminRegister      UserType = 1 // 后台注册用户
	UserTypeWechatMallRegister UserType = 2 // 微信小程序注册用户
)

// UserLoginType 用户类型
//
//go:generate stringer -type=UserLoginType --linecomment
//go:generate generate --file=consts.go  --type=UserLoginType
type UserLoginType int

const (
	UserLoginTypeUser             UserLoginType = 1 // 用户登录
	UserLoginTypeTenantManagement UserLoginType = 2 // 租户登录
	UserLoginTypeBizUnit          UserLoginType = 3 // 往来单位登录
)

// Type 类型
//
//go:generate stringer -type=Type --linecomment
//go:generate generate --file=consts.go  --type=Type
type Type int

const (
	TypeRole  Type = 1 // 角色
	TypeUser  Type = 2 // 用户
	TypeGroup Type = 3 // 所有
)

// GlobalConfigType 全局配置类型
//
//go:generate stringer -type=GlobalConfigType --linecomment
//go:generate generate --file=consts.go  --type=GlobalConfigType
type GlobalConfigType int

const (
	GlobalConfigTypeText         GlobalConfigType = 1 // 文本
	GlobalConfigTypeChoice       GlobalConfigType = 2 // 单选
	GlobalConfigTypeMultiChoices GlobalConfigType = 3 // 多选
)

// DropList
//
//go:generate stringer -type=DropList --linecomment
//go:generate generate --file=consts.go  --type=DropList
type DropList int

const (
	InDropListYes DropList = 1 // 进入回收站列表
	InDropListNo  DropList = 2 // 不是回收站列表
)

// OrderStatus 订单状态
//
//go:generate stringer -type=OrderStatus --linecomment
//go:generate generate --file=consts.go  --type=OrderStatus
type OrderStatus int

const (
	OrderStatusPendingAudit OrderStatus = 1 // 待审核
	OrderStatusAudited      OrderStatus = 2 // 已审核
	OrderStatusRejected     OrderStatus = 3 // 已驳回
	OrderStatusVoided       OrderStatus = 4 // 已作废
)

// 业务关闭
//
//go:generate stringer -type=BusinessClose --linecomment
//go:generate generate --file=consts.go  --type=BusinessClose
type BusinessClose int

const (
	BusinessCloseNo  BusinessClose = 1 // 开启
	BusinessCloseYes BusinessClose = 2 // 关闭
)

// 库存状态
//
//go:generate stringer -type=StockStatus --linecomment
//go:generate generate --file=consts.go  --type=StockStatus
type StockStatus int

const (
	StockStatusWarehouseIn  StockStatus = 1 // 正常
	StockStatusArrange      StockStatus = 2 // 已占用
	StockStatusWarehouseOut StockStatus = 3 // 已出库
)

// 盘点状态
//
//go:generate stringer -type=CheckStatus --linecomment
//go:generate generate --file=consts.go  --type=CheckStatus
type CheckStatus int

const (
	CheckStatusNormal   CheckStatus = 1 // 正常
	CheckStatusChecking CheckStatus = 2 // 盘点中
)

// 往来单位类型
//
//go:generate stringer -type=BizUnitType --linecomment
//go:generate generate --file=consts.go  --type=BizUnitType
type BizUnitType int

const (
	BizUnitTypeNotOur BizUnitType = 12 // 非本仓染厂
	BizUnitTypeOur    BizUnitType = 16 // 本仓染厂
)

// 列表习惯类型
//
//go:generate stringer -type=ListHabitsType --linecomment
//go:generate generate --file=consts.go  --type=ListHabitsType
type ListHabitsType int

const (
	ListHabitsTypeSave       ListHabitsType = 1 // 保存信息
	ListHabitsTypeTemp       ListHabitsType = 2 // 模板
	ListHabitsTypeMPEnter    ListHabitsType = 3 // 小程序快捷入口
	ListHabitsTypeAdminEnter ListHabitsType = 4 // 后台快捷入口
)

// 经纬纱类型
//
//go:generate stringer -type=MaterialType --linecomment
//go:generate generate --file=consts.go  --type=MaterialType
type MaterialType int

const (
	MaterialTypeWarp MaterialType = 1 // 经纱
	MaterialTypeWeft MaterialType = 2 // 纬纱
)

// OrderType 订单类型
//
//go:generate stringer -type=OrderType --linecomment
//go:generate generate --file=consts.go  --type=OrderType
type OrderType int

const (
	OrderTypeSale   OrderType = 1 // 销售调货单
	OrderTypeReturn OrderType = 2 // 退货调货单
)

// 实际单据类型
//
//go:generate stringer -type=ActuallyOrderType --linecomment
//go:generate generate --file=consts.go  --type=ActuallyOrderType
type ActuallyOrderType int

const (
	ActuallyOrderTypeActually ActuallyOrderType = 1 // 实收（实付）
	ActuallyOrderTypeOther    ActuallyOrderType = 2 // 其他
)

// 往来单位类型
//
//go:generate stringer -type=Category --linecomment
//go:generate generate --file=consts.go  --type=Category
type Category int

const (
	CategorySupplier Category = 1 // 供应商
	CategoryCustomer Category = 2 // 客户
)

// // 结算状态
// //
// //go:generate stringer -type=MaterialType --linecomment
// //go:generate generate --file=consts.go  --type=MaterialType
// type SettleStatus int
// 租户状态
//
//go:generate stringer -type=TenantManagementStatus --linecomment
//go:generate generate --file=consts.go  --type=TenantManagementStatus
type TenantManagementStatus int

const (
	TenantManagementStatusNotActivation TenantManagementStatus = 1 // 未激活
	TenantManagementStatusNormal        TenantManagementStatus = 2 // 正常
	TenantManagementStatusExpire        TenantManagementStatus = 3 // 已过期
	TenantManagementStatusDisable       TenantManagementStatus = 4 // 已禁用
)

// 租户状态
//
//go:generate stringer -type=PayWay --linecomment
//go:generate generate --file=consts.go  --type=PayWay
type PayWay int

const (
	PayWayWechatPay PayWay = 1 // 微信支付
	PayWayAliPay    PayWay = 2 // 支付宝支付
)

// 支付状态
//
//go:generate stringer -type=PayStatus --linecomment
//go:generate generate --file=consts.go  --type=PayStatus
type PayStatus int

const (
	PayStatusUnpaid        PayStatus = 1 // 未支付
	PayStatusPaid          PayStatus = 2 // 已支付
	PayStatusExpireOrClose PayStatus = 3 // 已过期/已关闭
	PayStatusCancel        PayStatus = 4 // 已作废
)

// 支付来源类型
//
//go:generate stringer -type=PaySourceType --linecomment
//go:generate generate --file=consts.go  --type=PaySourceType
type PaySourceType int

const (
	PaySourceTypeTenantManagement PaySourceType = 1 // 租户管理
	PaySourceTypeCodeListOrc      PaySourceType = 2 // 码单OCR识别
)

// 支付记录类型
//
//go:generate stringer -type=PayRecordType --linecomment
//go:generate generate --file=consts.go  --type=PayRecordType
type PayRecordType int

const (
	PayRecordTypePayAuto           PayRecordType = 1 // 支付自动创建
	PayRecordTypeManual            PayRecordType = 2 // 后台手动创建
	PayRecordTypeFirstRegistration PayRecordType = 3 // 首次注册创建
)

// 租户套餐租状态
//
//go:generate stringer -type=TenantManagementPackageStatus --linecomment
//go:generate generate --file=consts.go  --type=TenantManagementPackageStatus
type TenantManagementPackageStatus int

const (
	TenantManagementPackageStatusNotActivation TenantManagementPackageStatus = 1 // 未激活
	TenantManagementPackageStatusNormal        TenantManagementPackageStatus = 2 // 正常
	TenantManagementPackageStatusExpire        TenantManagementPackageStatus = 3 // 已过期
	TenantManagementPackageStatusDisable       TenantManagementPackageStatus = 4 // 已禁用
)

// 收货状态
//
//go:generate stringer -type=RecStatus --linecomment
//go:generate generate --file=consts.go  --type=RecStatus
type RecStatus int

const (
	RecStatusWaitBegin RecStatus = 1 // 未收
	RecStatusInPart    RecStatus = 2 // 收部分
	RecStatusFinish    RecStatus = 3 // 收完
)

// 打印机类型
//
//go:generate stringer -type=PrinterType --linecomment
//go:generate generate --file=consts.go  --type=PrinterType
type PrinterType int

const (
	PrinterTypeFeie PrinterType = 1 // feie
)

// 打印机记录类型
//
//go:generate stringer -type=PrinterLogType --linecomment
//go:generate generate --file=consts.go  --type=PrinterLogType
type PrinterLogType int

const (
	PrinterLogTypeAdd   PrinterLogType = 1 // 打印机记录
	PrinterLogTypePrint PrinterLogType = 2 // 打印记录
)

// 执行状态
//
//go:generate stringer -type=ExecStatus --linecomment
//go:generate generate --file=consts.go  --type=ExecStatus
type ExecStatus int

const (
	ExecStatusSuccess ExecStatus = 1 // 成功
	ExecStatusFail    ExecStatus = 2 // 失败
)

// 操作类型
//
//go:generate stringer -type=OperateType --linecomment
//go:generate generate --file=consts.go  --type=OperateType
type OperateType int

const (
	OperateTypeAdd  OperateType = 1 // 添加
	OperateTypeEdit OperateType = 2 // 修改
	OperateTypeDel  OperateType = 3 // 删除
)

// 完成状态
//
//go:generate stringer -type=FinishStatus --linecomment
//go:generate generate --file=consts.go  --type=FinishStatus
type FinishStatus int

const (
	FinishStatusYes FinishStatus = 1 // 已完成
	FinishStatusNo  FinishStatus = 2 // 未完成
)

// 地址类型
//
//go:generate stringer -type=AddrType --linecomment
//go:generate generate --file=consts.go  --type=AddrType
type AddrType int

const (
	AddrTypeTenant AddrType = 1 // 账套地址
	AddrTypeReturn AddrType = 2 // 回货地址
)

// 下推状态
//
//go:generate stringer -type=PushStatus --linecomment
//go:generate generate --file=consts.go  --type=PushStatus
type PushStatus int

const (
	PushStatusDoing PushStatus = 1 // 未完成
	PushStatusDone  PushStatus = 2 // 已完成
)

// 坯布半成品操作库存类型
//
//go:generate stringer -type=OperateStockType --linecomment
//go:generate generate --file=consts.go  --type=OperateStockType
type OperateStockType int

const (
	OperateStockTypeIngDnf             OperateStockType = 1 // 在染-染整
	OperateStockTypeRedye              OperateStockType = 2 // 在染-复色
	OperateStockTypeProcessingUnhandle OperateStockType = 3 // 返工布-加工-未处理
	OperateStockTypeProcessingHandling OperateStockType = 4 // 返工布-加工-在处理
	OperateStockTypeProcessingRedye    OperateStockType = 5 // 返工布-加工-复色
	OperateStockTypeRepairUnhandle     OperateStockType = 6 // 返工布-回修-未处理
	OperateStockTypeRepairHandling     OperateStockType = 7 // 返工布-回修-在处理
	OperateStockTypeRepairRedye        OperateStockType = 8 // 返工布-回修-复色

	OperateStockTypeDNFNotify OperateStockType = 253 // 染整通知单
	OperateStockTypeDNFHZ     OperateStockType = 254 // 后整通知单
)

// 染整单类型
//
//go:generate stringer -type=DNFOrderType --linecomment
//go:generate generate --file=consts.go  --type=DNFOrderType
type DNFOrderType int

const (
	DNFOrderTypeDNFNotice DNFOrderType = 1 // 染整通知单
	DNFOrderTypeRedye     DNFOrderType = 2 // 复色通知单
	DNFOrderTypeFinishing DNFOrderType = 3 // 后整单
)

// 染整通知单类型
//
//go:generate stringer -type=DNFNoticeOrderType --linecomment
//go:generate generate --file=consts.go  --type=DNFNoticeOrderType
type DNFNoticeOrderType int

const (
	DNFNoticeOrderTypeDNFNotice DNFNoticeOrderType = 1 // 染整
	DNFNoticeOrderTypeRedye     DNFNoticeOrderType = 2 // 加工
	DNFNoticeOrderTypeFinishing DNFNoticeOrderType = 3 // 回修
)

// 系统库存类型
//
//go:generate stringer -type=StockType --linecomment
//go:generate generate --file=consts.go  --type=StockType
type StockType int

const (
	StockTypeRaw     StockType = 1 // 原料库存
	StockTypeGF      StockType = 2 // 坯布库存
	StockTypeProduct StockType = 3 // 成品库存
)

// 单据类型
//
//go:generate stringer -type=BookOrderType --linecomment
//go:generate generate --file=consts.go  --type=BookOrderType
type BookOrderType int

const (
	BookOrderTypeReservationPass         BookOrderType = 1  // 成品预约出仓单审核
	BookOrderTypeReservationWait         BookOrderType = 2  // 成品预约出仓单消审
	BookOrderTypeArrangeChangePass       BookOrderType = 3  // 成品配布变更单审核
	BookOrderTypeArrangeChangeWait       BookOrderType = 4  // 成品配布变更单消审
	BookOrderTypePurchaseReturnPass      BookOrderType = 5  // 成品采购退货单审核
	BookOrderTypePurchaseReturnWait      BookOrderType = 6  // 成品采购退货单消审
	BookOrderTypePMCPush                 BookOrderType = 7  // 物料计划单下推销售单自动审核
	BookOrderTypeProductSalePass         BookOrderType = 8  // 成品销售单审核
	BookOrderTypeProductSaleWait         BookOrderType = 9  // 成品销售单消审
	BookOrderTypeProductSaleOutPass      BookOrderType = 10 // 成品销售出仓单审核
	BookOrderTypeProductSaleOutWait      BookOrderType = 11 // 成品销售出仓单消审
	BookOrderTypeProductIntAlloOutPass   BookOrderType = 12 // 成品内部调拨出仓单审核
	BookOrderTypeProductIntAlloOutWait   BookOrderType = 13 // 成品内部调拨出仓单消审
	BookOrderTypeProductPrtOutPass       BookOrderType = 14 // 成品采购退货出仓单审核
	BookOrderTypeProductPrtOutWait       BookOrderType = 15 // 成品采购退货出仓单消审
	BookOrderTypeProductOtherOutPass     BookOrderType = 16 // 成品其他出仓单审核
	BookOrderTypeProductOtherOutWait     BookOrderType = 17 // 成品其他出仓单消审
	BookOrderTypeProductDeductionOutPass BookOrderType = 18 // 成品扣款出仓单审核
	BookOrderTypeProductDeductionOutWait BookOrderType = 19 // 成品扣款出仓单消审
	BookOrderTypeProductCheckOutPass     BookOrderType = 20 // 成品盘点出仓单审核
	BookOrderTypeProductCheckOutWait     BookOrderType = 21 // 成品盘点出仓单消审
	BookOrderTypeProductProcessOutPass   BookOrderType = 22 // 成品加工出仓单审核
	BookOrderTypeProductProcessOutWait   BookOrderType = 23 // 成品加工出仓单消审
	BookOrderTypeProductSaleAlloOutPass  BookOrderType = 24 // 成品销售调拨出仓单审核
	BookOrderTypeProductSaleAlloOutWait  BookOrderType = 25 // 成品销售调拨出仓单消审
	BookOrderTypeProductAdjustOutPass    BookOrderType = 26 // 成品调整出仓单审核
	BookOrderTypeProductAdjustOutWait    BookOrderType = 27 // 成品调整出仓单消审
	BookOrderTypeProductSaleAlloInPass   BookOrderType = 28 // 成品销售调拨进仓单审核
	BookOrderTypeProductSaleAlloInWait   BookOrderType = 29 // 成品销售调拨进仓单消审
)

// 拆分或合并类型
//
//go:generate stringer -type=SMType --linecomment
//go:generate generate --file=consts.go  --type=SMType
type SMType int

const (
	SMTypeSplit SMType = 1 // 拆分
	SMTypeMerge SMType = 2 // 合并
)

// 拆分或合并单据类型
//
//go:generate stringer -type=SMOrderType --linecomment
//go:generate generate --file=consts.go  --type=SMOrderType
type SMOrderType int

const (
	SMOrderTypePMC SMOrderType = 1 // 物料计划单
)

// SituStatus 进度状态
//
//go:generate stringer -type=SituStatus --linecomment
//go:generate generate --file=consts.go  --type=SituStatus
type SituStatus int

const (
	SituStatusPlaning                         SituStatus = 1  // 计划中
	SituStatusPmcOrder                        SituStatus = 2  // 分配-计划
	SituStatusRmlPurchaseOrder                SituStatus = 3  // 原料-采购中
	SituStatusRmlPurchaseReceiveOrProcessIn   SituStatus = 4  // 原料-已收货
	SituStatusRmlDeying                       SituStatus = 5  // 原料-染整中
	SituStatusGFProductionOrder               SituStatus = 6  // 生产中
	SituStatusGFPurchaseOrder                 SituStatus = 7  // 坯布-采购中
	SituStatusGFProductionOrPurchaseReceive   SituStatus = 8  // 坯布-已收货
	SituStatusDNFOrder                        SituStatus = 9  // 染整中
	SituStatusProductPurchase                 SituStatus = 10 // 成品-采购中
	SituStatusProductProcessOrPurchaseReceive SituStatus = 11 // 成品-已收货
	SituStatusProductSaleOrder                SituStatus = 12 // 已下单-配布中
	SituStatusProductArrangeOrder             SituStatus = 13 // 已配布-待确定
	SituStatusProductSaleDeliveryOrder        SituStatus = 14 // 已确定-待出仓
	SituStatusProductSaleOutOrder             SituStatus = 15 // 已出仓-待收款
	SituStatusActuallyCollectOrderPart        SituStatus = 16 // 部分收款
	SituStatusActuallyCollectOrderAll         SituStatus = 17 // 已收款
	SituStatusCancel                          SituStatus = 18 // 已作废
	SituStatusGFSaleOrder                     SituStatus = 19 // 坯布-已出仓
)

// MachineStatus 机台状态
//
//go:generate stringer -type=MachineStatus --linecomment
//go:generate generate --file=consts.go  --type=MachineStatus
type MachineStatus int

const (
	MachineStatusRunning           MachineStatus = 1 // 正在运行
	MachineStatusConnecting        MachineStatus = 2 // 连接中
	MachineStatusWait              MachineStatus = 3 // 待排产
	MachineStatusStop              MachineStatus = 4 // 故障中
	MachineStatusEfficiencyWarning MachineStatus = 5 // 效率预警
	MachineStatusCardAbnormal      MachineStatus = 6 // 刷卡异常
)

// MachineSort 机台排序
//
//go:generate stringer -type=MachineSort --linecomment
//go:generate generate --file=consts.go  --type=MachineSort
type MachineSort int

const (
	MachineSortUp             MachineSort = 1 // 机号升序
	MachineSortDown           MachineSort = 2 // 机号降序
	MachineSortAmountUp       MachineSort = 3 // 工资升序
	MachineSortAmountDown     MachineSort = 4 // 工资降序
	MachineSortRxvUp          MachineSort = 5 // 效能升序
	MachineSortRxvDown        MachineSort = 6 // 效能降序
	MachineSortTotalCountUp   MachineSort = 7 // 转数升序
	MachineSortTotalCountDown MachineSort = 8 // 转数降序
)

// CodeListStatus 码单状态
//
//go:generate stringer -type=CodeListStatus --linecomment
//go:generate generate --file=consts.go  --type=CodeListStatus
type CodeListStatus int

const (
	CodeListStatusEnable   CodeListStatus = 1 // 启用
	CodeListStatusDisable  CodeListStatus = 2 // 禁用
	CodeListStatusNoActive CodeListStatus = 3 // 未激活
	CodeListStatusExpire   CodeListStatus = 4 // 已过期
)

// ElectronicColorCardStatus 电子色卡状态
//
//go:generate stringer -type=ElectronicColorCardStatus --linecomment
//go:generate generate --file=consts.go  --type=ElectronicColorCardStatus
type ElectronicColorCardStatus int

const (
	ElectronicColorCardStatusEnable   ElectronicColorCardStatus = 1 // 启用
	ElectronicColorCardStatusDisable  ElectronicColorCardStatus = 2 // 禁用
	ElectronicColorCardStatusNoActive ElectronicColorCardStatus = 3 // 未激活
	ElectronicColorCardStatusExpire   ElectronicColorCardStatus = 4 // 已过期
)

// RechargeType 续费/充值类型
//
//go:generate stringer -type=RechargeType --linecomment
//go:generate generate --file=consts.go  --type=RechargeType
type RechargeType int

const (
	RechargeTypeOcr          RechargeType = 1 // OCR
	RechargeTypeEleColorCard RechargeType = 2 // 电子色卡
	RechargeTypeSearchImage  RechargeType = 3 // 图片搜索
)

// 跳转类型
//
//go:generate stringer -type=JumpType --linecomment
//go:generate generate --file=consts.go  --type=JumpType
type JumpType int

const (
	// JumpTypeCategoryPage JumpType = 1 // 分类页
	JumpTypeDetailPage JumpType = 2 // 详情页
	// JumpTypeSpecialPage  JumpType = 3 // 专题页
	JumpTypeNoJump       JumpType = 4 // 无跳转
	JumpTypeImgPreview   JumpType = 5 // 图片预览
	JumpTypeVideoPreview JumpType = 6 // 视频预览
)

// 状态
//
//go:generate stringer -type=CarouseStatus --linecomment
//go:generate generate --file=consts.go  --type=CarouseStatus
type CarouseStatus int

const (
	CarouseStatusEnable  CarouseStatus = 1 // 启用
	CarouseStatusDisable CarouseStatus = 2 // 禁用
)

//go:generate stringer -type=TagMerChantInfo --linecomment
//go:generate generate --file=consts.go  --type=TagMerChantInfo
type TagMerChantInfo int

const (
	TagMerChantInfoChemicalFiber TagMerChantInfo = 1 // 化学纤维企业
	TagMerChantInfoCotton        TagMerChantInfo = 2 // 棉纺织企业
	TagMerChantInfoDyeing        TagMerChantInfo = 3 // 印染企业
	TagMerChantInfoMutton        TagMerChantInfo = 4 // 毛纺企业(精纺)
	TagMerChantInfoHemp          TagMerChantInfo = 5 // 麻纺企业
)

// CostPriceType 成本类型
//
//go:generate stringer -type=CostPriceType --linecomment
//go:generate generate --file=consts.go  --type=CostPriceType
type CostPriceType int

const (
	CostPriceTypeRm  CostPriceType = 1 // 原料
	CostPriceTypeGfm CostPriceType = 2 // 坯布
	CostPriceTypeFpm CostPriceType = 3 // 成品
)
