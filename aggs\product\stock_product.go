package product

import (
	"context"
	"fmt"
	salePriceAggs "hcscm/aggs/sale_price"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product2 "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/basic_data/warehouse"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/employee"
	"hcscm/extern/pb/sale_price"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	"hcscm/model/mysql/sale/dao"
	salePriceModel "hcscm/model/mysql/sale_price"
	salePriceMysql "hcscm/model/mysql/sale_price/dao"
	redisF "hcscm/model/redis"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/set"
	"strings"

	"github.com/go-redis/redis"
)

type StockProductRepo struct {
	tx                 *mysql_base.Tx
	redisClient        *redis.Client
	stockProduct       map[uint64]model.StockProduct
	stockProductDetail map[uint64]model.StockProductDetail
	stockProductReq    *set.ConcurrentMap[string, uint64] // 上锁使用
}

func NewStockProductRepo(ctx context.Context, tx *mysql_base.Tx, stockProduct map[uint64]model.StockProduct, stockProductDetail map[uint64]model.StockProductDetail, stockProductReq *set.ConcurrentMap[string, uint64]) *StockProductRepo {
	return &StockProductRepo{
		tx:                 tx,
		redisClient:        redisF.GetClient(ctx),
		stockProduct:       stockProduct,
		stockProductDetail: stockProductDetail,
		stockProductReq:    stockProductReq,
	}
}

// 新增库存汇总(汇总记录不存在先创建)
func (u *StockProductRepo) AddOrUpdate(ctx context.Context, req *structure.AddStockProductParam) (id uint64, err error) {
	var (
		stockProduct     model.StockProduct
		ok               bool
		stockProductId   uint64
		productItem      product2.ProductRes
		productColorItem product2.ProductColorRes
		logDao           = mysql.NewStockProductBookLogDao(u.tx)
	)
	// 如果req的id为0，需要判断是上锁阶段还是更改库存阶段，上锁阶段则新增，更改库存阶段则取出上锁获得的id接着往下走
	if req.Id == 0 {
		stockProductId, ok = u.stockProductReq.Get(req.StockProductKey)
		// 上锁阶段有可能ok为false,false的时候新建一条然后return
		if !ok || stockProductId == 0 {
			stockProduct = model.NewStockProduct(ctx, req)

			// 补充颜色类别id
			if stockProduct.ProductColorKindId == 0 {
				productColorSvc := product2.NewProductColorClient()
				productColorItem, err = productColorSvc.GetProductColorById(ctx, stockProduct.ProductColorId)
				if err != nil {
					return
				}
				stockProduct.ProductColorKindId = productColorItem.TypeFinishedProductKindId
			}
			// 补充成品种类id
			if stockProduct.ProductKindId == 0 {
				productSvc := product2.NewProductClient()
				productItem, err = productSvc.GetProduct(ctx, product2.ProductReq{Id: stockProduct.ProductId})
				if err != nil {
					return
				}
				stockProduct.ProductKindId = productItem.TypeGreyFabricId
			}

			stockProduct, err = mysql.MustCreateStockProduct(u.tx, stockProduct)
			if err != nil {
				return
			}
			u.stockProduct[stockProduct.Id] = stockProduct
			id = stockProduct.Id
			return
		}
		req.Id = stockProductId
		id = stockProductId
	}
	// } else {
	stockProduct, ok = u.stockProduct[req.Id]
	if !ok {
		stockProduct, err = mysql.MustFirstStockProductByID(u.tx, req.Id)
		if err != nil {
			return
		}
	}

	// 判断汇总库存是否充足
	if !req.IsNoNeedCheckStock {
		if stockProduct.StockRoll-stockProduct.BookRoll-req.BookRoll < 0 || stockProduct.Weight-stockProduct.BookWeight-req.BookWeight < 0 {
			if stockProduct.ProductId > 0 {
				productSvc := product2.NewProductClient()
				productItem, err = productSvc.GetProduct(ctx, product2.ProductReq{Id: stockProduct.ProductId})
				if err != nil {
					return
				}
			}
			if stockProduct.ProductColorId > 0 {
				productColorSvc := product2.NewProductColorClient()
				productColorItem, err = productColorSvc.GetProductColorById(ctx, stockProduct.ProductColorId)
				if err != nil {
					return
				}
			}
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeNotEnoughStock,
				fmt.Sprintf("成品名:%v%v,成品色号:%v%v",
					productItem.FinishProductCode,
					productItem.FinishProductName,
					productColorItem.ProductColorCode,
					productColorItem.ProductColorName,
				)),
			)
			return
		}
		// 如果是库存进出跳过占用直接扣除，则需要单独判断库存是否充足
		if req.BookRoll == 0 && req.BookWeight == 0 {
			if stockProduct.StockRoll-stockProduct.BookRoll+req.StockRoll < 0 || stockProduct.Weight-stockProduct.BookWeight+req.Weight < 0 {
				if stockProduct.ProductId > 0 {
					productSvc := product2.NewProductClient()
					productItem, err = productSvc.GetProduct(ctx, product2.ProductReq{Id: stockProduct.ProductId})
					if err != nil {
						return
					}
				}
				if stockProduct.ProductColorId > 0 {
					productColorSvc := product2.NewProductColorClient()
					productColorItem, err = productColorSvc.GetProductColorById(ctx, stockProduct.ProductColorId)
					if err != nil {
						return
					}
				}
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeNotEnoughStock,
					fmt.Sprintf("成品名:%v%v,成品色号:%v%v",
						productItem.FinishProductCode,
						productItem.FinishProductName,
						productColorItem.ProductColorCode,
						productColorItem.ProductColorName,
					)),
				)
				return
			}
		}
	}

	stockProduct.UpdateStockProduct(ctx, req)

	stockProduct, err = mysql.MustUpdateStockProduct(u.tx, stockProduct)
	if err != nil {
		return
	}
	// }
	u.stockProduct[stockProduct.Id] = stockProduct

	if req.BookRoll != 0 || req.BookWeight != 0 {
		{
			req.Id = stockProduct.Id
			req.ProductId = stockProduct.ProductId
			req.ProductColorId = stockProduct.ProductColorId
		}
		log := model.StockProductBookLog{}
		log.AddStockProductBookLogByAddStockProductParam(ctx, req)
		logDao.MustCreateStockProductBookLog(log) // 忽略错误
	}

	return stockProduct.Id, err
}

// 删除汇总库存
func (u *StockProductRepo) Delete(ctx context.Context, req *structure.DeleteStockProductParam) (ids []uint64, err error) {
	var (
		stockProductList model.StockProductList
	)

	stockProductList, err = mysql.FindStockProductByIDs(u.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	// 先更新删除原因，后执行软删除
	for _, stockProduct := range stockProductList {
		// 删除
		err = mysql.MustDeleteStockProduct(u.tx, stockProduct)
		if err != nil {
			return
		}
		ids = append(ids, stockProduct.Id)
	}
	return
}

// 汇总库存是否存在
func (u *StockProductRepo) Exist(ctx context.Context, warehouseId, customerId, productColorId, productLevelId uint64, remark string) (stockProductId uint64, exist bool, err error) {

	stockProductId, exist, err = mysql.ExistStockProduct(u.tx, warehouseId, customerId, productColorId, productLevelId, remark)
	if err != nil {
		return
	}
	return stockProductId, exist, err
}

// 获取单条汇总库存
func (u *StockProductRepo) Get(ctx context.Context, stockProductId uint64) (data structure.GetStockProductData, err error) {
	var (
		stockProduct model.StockProduct
	)
	stockProduct, err = mysql.MustFirstStockProductByID(u.tx, stockProductId)
	if err != nil {
		return
	}

	o := structure.GetStockProductData{}
	o.Id = stockProduct.Id
	o.ProductColorId = stockProduct.ProductColorId
	o.WarehouseId = stockProduct.WarehouseId
	o.CustomerId = stockProduct.CustomerId
	o.ProductLevelId = stockProduct.ProductLevelId
	o.Remark = stockProduct.Remark
	data = o
	return
}

// 获取汇总库存列表
func (u *StockProductRepo) GetList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDataList, total int, err error) {
	var (
		stockProducts   model.StockProductList
		productSvc      = product2.NewProductClient()
		productColorSvc = product2.NewProductColorClient()
	)

	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}
	stockProducts, total, err = mysql.SearchStockProduct(u.tx, req)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProducts, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProducts, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProducts, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProducts, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProducts, "product_id")
	productItem, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProducts, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProducts, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	for _, stockProduct := range stockProducts.List() {
		o := structure.GetStockProductData{}
		o.Id = stockProduct.Id
		o.CreateTime = tools.MyTime(stockProduct.CreateTime)
		o.UpdateTime = tools.MyTime(stockProduct.UpdateTime)
		o.CreatorId = stockProduct.CreatorId
		o.CreatorName = stockProduct.CreatorName
		o.UpdaterId = stockProduct.UpdaterId
		o.UpdateUserName = stockProduct.UpdaterName
		o.ProductColorId = stockProduct.ProductColorId
		o.ProductColorCode = productColorItem[stockProduct.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProduct.ProductColorId][1]
		o.ProductColorKindId = stockProduct.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProduct.ProductColorKindId]
		o.WarehouseId = stockProduct.WarehouseId
		o.WarehouseName = warehouseName[stockProduct.WarehouseId]
		o.CustomerId = stockProduct.CustomerId
		o.CustomerName = bizUnit[stockProduct.CustomerId]
		o.ProductId = stockProduct.ProductId
		if product, ok := productItem[stockProduct.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			// o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.WeavingOrganizationId = product.WeavingOrganizationId
			o.WeavingOrganizationCode = product.WeavingOrganizationCode
			o.WeavingOrganizationName = product.WeavingOrganizationName
			o.Density = product.Density
			o.YarnCount = product.YarnCount
			// o.BleachId = product.BleachId
			// o.BleachName = product.BleachName
			// 库存计量单位取基础资料的
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
		}
		// o.MeasurementUnitId = stockProduct.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProduct.MeasurementUnitId]
		o.ProductLevelId = stockProduct.ProductLevelId
		o.ProductLevelName = productLevel[stockProduct.ProductLevelId]
		o.ProductRemark = stockProduct.ProductRemark
		o.Weight = stockProduct.Weight
		o.Length = stockProduct.Length
		o.BookRoll = stockProduct.BookRoll
		o.BookWeight = stockProduct.BookWeight
		o.StockRoll = stockProduct.StockRoll
		o.AvailableRoll = stockProduct.StockRoll - stockProduct.BookRoll
		o.AvailableWeight = stockProduct.Weight - stockProduct.BookWeight
		o.Remark = stockProduct.Remark
		list = append(list, o)
	}
	return
}

// 获取库存列表
func (u *StockProductRepo) GetYBList(ctx context.Context, req *structure.GetYBStockProductListQuery) (list structure.GetYBStockProductDataList, total int, err error) {
	var (
		stockProducts model.SumColorStockProductList
		// stockProducts   mysql.StockDyelotNumberDetailList
		productSvc      = product2.NewProductClient()
		productColorSvc = product2.NewProductColorClient()
	)

	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}
	if req.ProductCodeOrName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, "", "", req.ProductCodeOrName)
		if err != nil {
			return
		}
	}
	if req.ProductColorCodeOrName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, "", "", req.ProductColorCodeOrName)
		if err != nil {
			return
		}
	}
	stockProducts, total, err = mysql.SearchYBListStockProduct(u.tx, req)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProducts, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProducts, "product_id")
	productItem, _, err := productSvc.GetProductByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProducts, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	for _, stockProduct := range stockProducts {
		o := structure.GetYBListStockProductData{}
		o.ProductColorId = stockProduct.ProductColorId
		o.ProductColorCode = productColorItem[stockProduct.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProduct.ProductColorId][1]
		o.WarehouseId = stockProduct.WarehouseId
		o.WarehouseName = warehouseName[stockProduct.WarehouseId]
		o.ProductId = stockProduct.ProductId
		o.ProductCode = productItem[stockProduct.ProductId][0]
		o.ProductName = productItem[stockProduct.ProductId][1]
		o.Weight = stockProduct.Weight
		o.Length = stockProduct.Length
		o.BookRoll = stockProduct.BookRoll
		o.BookWeight = stockProduct.BookWeight
		o.StockRoll = stockProduct.StockRoll
		o.AvailableRoll = stockProduct.StockRoll - stockProduct.BookRoll
		o.AvailableWeight = stockProduct.Weight - stockProduct.BookWeight
		list = append(list, o)
	}
	return
}

// todo
func (u *StockProductRepo) GetDyelotDetail(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (data structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {
	var (
		list = make(structure.GetStockProductOutAndInDetailDataList, 0)
		// o               model.StockProductOutAndInDetailList
		o               mysql.UnionOutAndInBaseList
		inModelList     model.FpmInOrderItemFcList
		outModelList    model.FpmOutOrderItemFcList
		totalRWL        = make([]int, 3)
		detailStockList model.StockProductDetailList
		detailStockIds  = make([]uint64, 0)
	)
	// if q.BeginTime == "" && q.EndTime == "" {
	//	q.BeginTime = tools.QueryTime(time.Now().AddDate(0, -1, 0).Format("2006-01-02 15:04:05"))
	//	q.EndTime = tools.QueryTime(time.Now().Format("2006-01-02 15:04:05"))
	// }
	detailStockList, err = mysql.FindStockProductByDyelotNumberAndColorList(u.tx, q)
	if err != nil {
		return
	}
	// 详细库存ids
	detailStockIds = detailStockList.GetIds()

	// 汇总日期前的入库数据 下标：0 roll ; 1 weight ; 2 length
	inModelList, err = mysql.DetailWarehouseInDataRWLByDetailIDs(u.tx, q, detailStockIds)
	if err != nil {
		return
	}
	inTotalRWL := inModelList.GetTotalRWL()
	// 汇总日期前的出库数据
	outModelList, err = mysql.DetailWarehouseOutDataRWLByDetailIDs(u.tx, q, detailStockIds)
	if err != nil {
		return
	}
	outTotalRWL := outModelList.GetTotalRWL()
	// 获取盘点数据
	checkWeightList, err := mysql.DetailWarehouseCheckDataRWLByDetailIDs(u.tx, q, detailStockIds)
	if err != nil {
		return
	}
	checkTotalRWL := checkWeightList.GetTotalRWL()

	// 上期结余
	totalRWL[0] = inTotalRWL[0] - outTotalRWL[0] + checkTotalRWL[0]
	totalRWL[1] = inTotalRWL[1] - outTotalRWL[1] + checkTotalRWL[1]
	totalRWL[2] = inTotalRWL[2] - outTotalRWL[2] + checkTotalRWL[2]
	data.TotalRoll = totalRWL[0]
	data.TotalWeight = totalRWL[1]
	data.TotalLength = totalRWL[2]

	// 获取日期范围数据
	q.Ids = detailStockIds
	o, count, err = mysql.FindWarehouseOutAndInSumList(u.tx, q)
	if err != nil {
		return
	}
	for _, src := range o {
		dst := structure.GetStockProductOutAndInDetailData{}
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		totalRWL[0] = totalRWL[0] + src.InRoll - src.OutRoll + src.DifferenceRoll
		totalRWL[1] = totalRWL[1] + src.InWeight - src.OutWeight + src.DifferenceWeight
		totalRWL[2] = totalRWL[2] + src.InLength - src.OutLength + src.DifferenceLength
		u.swapStockProductOutAndInDetail2GetData(src, &dst, totalRWL)

		list = append(list, dst)
	}

	data.List = list
	return
}

func (u *StockProductRepo) GetColorDetailList(ctx context.Context, req *structure.GetColorDetailQuery) (data structure.GetTotalData, total int, err error) {
	var (
		stockProducts model.StockProductList
	)

	stockProducts, total, err = mysql.SearchColorDetail(u.tx, req)
	if err != nil {
		return
	}

	productSvc := product2.NewProductClient()
	productIds := mysql_base.GetUInt64List(stockProducts, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	// 初始化统计变量
	var totalStockRoll, totalBookRoll, totalAvailableRoll, totalAvailableWeight int

	for _, stockProduct := range stockProducts.List() {
		o := structure.GetStockProductData{}
		o.Id = stockProduct.Id
		o.CreateTime = tools.MyTime(stockProduct.CreateTime)
		o.UpdateTime = tools.MyTime(stockProduct.UpdateTime)
		o.CreatorId = stockProduct.CreatorId
		o.CreatorName = stockProduct.CreatorName
		o.UpdaterId = stockProduct.UpdaterId
		o.UpdateUserName = stockProduct.UpdaterName
		o.ProductColorId = stockProduct.ProductColorId
		// o.ProductColorCode = productColorItem[stockProduct.ProductColorId][0]
		// o.ProductColorName = productColorItem[stockProduct.ProductColorId][1]
		o.ProductColorKindId = stockProduct.ProductColorKindId
		// o.ProductColorKindName = productColorKind[stockProduct.ProductColorKindId]
		o.WarehouseId = stockProduct.WarehouseId
		// o.WarehouseName = warehouseName[stockProduct.WarehouseId]
		o.CustomerId = stockProduct.CustomerId
		// o.CustomerName = bizUnit[stockProduct.CustomerId]
		o.ProductId = stockProduct.ProductId
		// o.ProductCode = productItem[stockProduct.ProductId][0]
		// o.ProductName = productItem[stockProduct.ProductId][1]
		o.ProductLevelId = stockProduct.ProductLevelId
		// o.ProductLevelName = productLevel[stockProduct.ProductLevelId]
		o.ProductRemark = stockProduct.ProductRemark
		o.Weight = stockProduct.Weight
		o.Length = stockProduct.Length
		o.BookRoll = stockProduct.BookRoll
		o.BookWeight = stockProduct.BookWeight
		o.StockRoll = stockProduct.StockRoll
		o.AvailableRoll = stockProduct.StockRoll - stockProduct.BookRoll
		o.AvailableWeight = stockProduct.Weight - stockProduct.BookWeight
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProduct.ProductId]; ok {
			o.MeasurementUnitId = product.MeasurementUnitId
		}
		// o.MeasurementUnitId = stockProduct.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProduct.MeasurementUnitId]
		o.Remark = stockProduct.Remark

		// 添加统计信息
		o.StockRoll = stockProduct.StockRoll
		o.BookRoll = stockProduct.BookRoll

		// 更新总统计信息
		totalStockRoll += stockProduct.StockRoll
		totalBookRoll += stockProduct.BookRoll
		totalAvailableRoll += o.AvailableRoll
		totalAvailableWeight += o.AvailableWeight

		data.List = append(data.List, o)
	}

	totalData := structure.GetTotalData{
		TotalStockRoll:       totalStockRoll,
		TotalBookRoll:        totalBookRoll,
		TotalAvailableRoll:   totalAvailableRoll,
		TotalAvailableWeight: totalAvailableWeight,
		List:                 data.List,
	}

	// return data, total, totalData
	return totalData, total, nil
}

func (u *StockProductRepo) GetDropdownList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDropdownDataList, total int, err error) {
	var (
		stockProducts            model.StockProductList
		salePriceColorKindRels   salePriceModel.SalePriceColorKindRelList
		salePriceColorKinds      salePriceModel.SalePriceColorKindList
		customerSalePriceAdjusts salePriceModel.CustomerSalePriceAdjustList
		saleLevelPrices          salePriceModel.SalePriceLevelList
		saleLevel                map[uint64]string
		productSvc               = product2.NewProductClient()
		productColorSvc          = product2.NewProductColorClient()
		res                      = make(structure.GetStockProductDropdownDataList, 0)
	)
	// 获取成品id
	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	// 获取成品颜色id
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}
	// 获取成品库存信息
	stockProducts, total, err = mysql.SearchStockProduct(u.tx, req)
	if err != nil {
		return
	}
	// 获取成品等级
	productLevelIds := mysql_base.GetUInt64List(stockProducts, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}
	// 获取成品种类
	productColorKindIds := mysql_base.GetUInt64List(stockProducts, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}
	// 获取仓库名称
	warehouseIds := mysql_base.GetUInt64List(stockProducts, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProducts, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}
	// 获取成品信息map[product_id]product_info
	productIds := mysql_base.GetUInt64List(stockProducts, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}
	// 获取成品颜色数据
	productColorIds := mysql_base.GetUInt64List(stockProducts, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProducts, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }
	// 是否显示价格
	if req.WithPrice {
		salePriceColorKindRels, err = salePriceMysql.FindSalePriceColorKindRelByProductAndColorKind(u.tx, productIds, productColorKindIds)
		if err != nil {
			return
		}

		salePriceColorKindRelIds := mysql_base.GetUInt64List(salePriceColorKindRels, "sale_price_color_kind_rel_id")
		versions := mysql_base.GetIntList(salePriceColorKindRels, "version")

		// 获取正在生效以及下一次生效的版本组合salePriceColorKindRelId查询
		salePriceColorKinds, err = salePriceMysql.FindSalePriceColorKindByParentIdsAndVersions(u.tx, salePriceColorKindRelIds, versions)
		if err != nil {
			return
		}

		salePriceColorKindIds := mysql_base.GetUInt64List(salePriceColorKinds, "sale_price_color_kind_id")
		saleLevelPrices, err = salePriceMysql.FindSalePriceLevelByParentIDs(u.tx, salePriceColorKindIds)
		if err != nil {
			return
		}

		customerIds := mysql_base.GetUInt64List(req, "customer_id")
		customerSalePriceAdjusts, err = salePriceMysql.FindCustomerSalePriceAdjustByCustomerIds(u.tx, customerIds)
		if err != nil {
			return
		}

		saleLevelSvc := sale_price.NewSaleLevelClient()
		saleLevel, err = saleLevelSvc.GetAllEnableSaleLevel(ctx)
		if err != nil {
			return
		}
	}

	for _, stockProduct := range stockProducts.List() {
		o := structure.GetStockProductDropdownData{}
		o.Id = stockProduct.Id
		o.StockProductId = stockProduct.Id
		o.CreateTime = tools.MyTime(stockProduct.CreateTime)
		o.UpdateTime = tools.MyTime(stockProduct.UpdateTime)
		o.CreatorId = stockProduct.CreatorId
		o.CreatorName = stockProduct.CreatorName
		o.UpdaterId = stockProduct.UpdaterId
		o.UpdateUserName = stockProduct.UpdaterName
		o.ProductColorId = stockProduct.ProductColorId
		o.ProductColorCode = productColorItem[stockProduct.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProduct.ProductColorId][1]
		o.ProductColorKindId = stockProduct.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProduct.ProductColorKindId]
		o.WarehouseId = stockProduct.WarehouseId
		o.WarehouseName = warehouseName[stockProduct.WarehouseId]
		o.CustomerId = stockProduct.CustomerId
		o.CustomerName = bizUnit[stockProduct.CustomerId]
		o.ProductId = stockProduct.ProductId
		o.ProductLevelId = stockProduct.ProductLevelId
		o.ProductLevelName = productLevel[stockProduct.ProductLevelId]
		o.ProductRemark = stockProduct.ProductRemark
		o.Weight = stockProduct.Weight
		o.Length = stockProduct.Length
		o.BookRoll = stockProduct.BookRoll
		o.StockRoll = stockProduct.StockRoll
		o.AvailableRoll = stockProduct.StockRoll - stockProduct.BookRoll
		o.AvailableWeight = stockProduct.Weight - stockProduct.BookWeight
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProduct.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.ProductCraft = product.FinishProductCraft
			o.ProductIngredient = product.FinishProductIngredient
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
			o.StandardWeight = product.StandardWeight
		}
		// o.MeasurementUnitId = stockProduct.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProduct.MeasurementUnitId]
		o.Remark = stockProduct.Remark
		// 是否带上价格
		if req.WithPrice {
			salePriceColorKind := salePriceColorKinds.Pick(stockProduct.ProductId, stockProduct.ProductColorKindId)
			salePrice := salePriceAggs.CalcPurchaserLadderSalePrice(
				ctx,
				u.tx,
				saleLevel,
				salePriceColorKind,
				customerSalePriceAdjusts,
				saleLevelPrices,
				stockProduct.ProductId,
				stockProduct.ProductColorKindId,
				stockProduct.ProductColorId,
			)
			o.SalePrice = salePrice
		}
		res = append(res, o)
	}
	list = res
	return
}
func (u *StockProductRepo) MPGetDropdownList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDropdownDataList, total int, err error) {
	var (
		stockProducts            model.StockProductList
		salePriceColorKindRels   salePriceModel.SalePriceColorKindRelList
		salePriceColorKinds      salePriceModel.SalePriceColorKindList
		customerSalePriceAdjusts salePriceModel.CustomerSalePriceAdjustList
		saleLevelPrices          salePriceModel.SalePriceLevelList
		saleLevel                map[uint64]string
		productSvc               = product2.NewProductClient()
		productColorSvc          = product2.NewProductColorClient()
		res                      = make(structure.GetStockProductDropdownDataList, 0)
		saleSystem               sale_sys_pb.Res
	)
	// 获取营销体系信息
	saleSystem, _ = sale_sys_pb.NewSaleSystemClient().GetSaleSystemDetailById(ctx, req.SaleSystemId)
	if err != nil {
		return
	}
	// 获取成品id
	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	// 获取成品颜色id
	if req.ColorQuery != "" {
		// 判断是否为颜色编号格式 (包含 # 号)
		if strings.Contains(req.ColorQuery, "#") {
			// 是颜色编号，只用编号查询
			colorCode := strings.ReplaceAll(req.ColorQuery, "#", "")
			req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, colorCode, "", "")
		} else {
			req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, "", "", req.ColorQuery)
		}
		if err != nil {
			return
		}
	}
	// 获取成品库存信息
	stockProducts, total, err = mysql.MPSearchStockProduct(u.tx, req)
	if err != nil {
		return
	}
	// 获取成品等级
	productLevelIds := mysql_base.GetUInt64List(stockProducts, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}
	// 获取成品种类
	productColorKindIds := mysql_base.GetUInt64List(stockProducts, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}
	// 获取仓库名称
	warehouseIds := mysql_base.GetUInt64List(stockProducts, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.MPGetPhysicalWarehouseByIds(ctx, warehouseIds, req.OrderType)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProducts, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}
	// 获取成品信息map[product_id]product_info
	productIds := mysql_base.GetUInt64List(stockProducts, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}
	// 获取成品颜色数据
	productColorIds := mysql_base.GetUInt64List(stockProducts, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	// 是否显示价格
	if req.WithPrice {
		salePriceColorKindRels, err = salePriceMysql.FindSalePriceColorKindRelByProductAndColorKind(u.tx, productIds, productColorKindIds)
		if err != nil {
			return
		}

		salePriceColorKindRelIds := mysql_base.GetUInt64List(salePriceColorKindRels, "sale_price_color_kind_rel_id")
		versions := mysql_base.GetIntList(salePriceColorKindRels, "version")

		// 获取正在生效以及下一次生效的版本组合salePriceColorKindRelId查询
		salePriceColorKinds, err = salePriceMysql.FindSalePriceColorKindByParentIdsAndVersions(u.tx, salePriceColorKindRelIds, versions)
		if err != nil {
			return
		}

		salePriceColorKindIds := mysql_base.GetUInt64List(salePriceColorKinds, "sale_price_color_kind_id")
		saleLevelPrices, err = salePriceMysql.FindSalePriceLevelByParentIDs(u.tx, salePriceColorKindIds)
		if err != nil {
			return
		}

		customerIds := mysql_base.GetUInt64List(req, "customer_id")
		customerSalePriceAdjusts, err = salePriceMysql.FindCustomerSalePriceAdjustByCustomerIds(u.tx, customerIds)
		if err != nil {
			return
		}

		saleLevelSvc := sale_price.NewSaleLevelClient()
		saleLevel, err = saleLevelSvc.GetAllEnableSaleLevel(ctx)
		if err != nil {
			return
		}
	}

	for _, stockProduct := range stockProducts.List() {
		o := structure.GetStockProductDropdownData{}
		var saleWeightErr int
		if warehouseName[stockProduct.WarehouseId] == "" { // 过滤掉没启用可预约的仓库
			continue
		}
		o.Id = stockProduct.Id
		o.StockProductId = stockProduct.Id
		o.CreateTime = tools.MyTime(stockProduct.CreateTime)
		o.UpdateTime = tools.MyTime(stockProduct.UpdateTime)
		o.CreatorId = stockProduct.CreatorId
		o.CreatorName = stockProduct.CreatorName
		o.UpdaterId = stockProduct.UpdaterId
		o.UpdateUserName = stockProduct.UpdaterName
		o.ProductColorId = stockProduct.ProductColorId
		o.ProductColorCode = productColorItem.PickByProductColorId(stockProduct.ProductColorId).ProductColorCode
		o.ProductColorName = productColorItem.PickByProductColorId(stockProduct.ProductColorId).ProductColorName
		if colorInfo := productColorItem.PickByProductColorId(stockProduct.ProductColorId); len(colorInfo.TextureURL) > 0 {
			o.TextureUrl = colorInfo.TextureURL[0]
		}
		o.ProductColorKindId = stockProduct.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProduct.ProductColorKindId]
		o.WarehouseId = stockProduct.WarehouseId
		o.WarehouseName = warehouseName[stockProduct.WarehouseId]
		o.CustomerId = stockProduct.CustomerId
		o.CustomerName = bizUnit[stockProduct.CustomerId]
		o.ProductId = stockProduct.ProductId
		o.ProductLevelId = stockProduct.ProductLevelId
		o.ProductLevelName = productLevel[stockProduct.ProductLevelId]
		o.ProductRemark = stockProduct.ProductRemark
		o.Weight = stockProduct.Weight
		o.Length = stockProduct.Length
		o.BookRoll = stockProduct.BookRoll
		o.StockRoll = stockProduct.StockRoll
		o.AvailableRoll = stockProduct.StockRoll - stockProduct.BookRoll
		o.AvailableWeight = stockProduct.Weight - stockProduct.BookWeight
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProduct.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.ProductCraft = product.FinishProductCraft
			o.ProductIngredient = product.FinishProductIngredient
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
			saleWeightErr = product.WeightError
		}
		// o.MeasurementUnitId = stockProduct.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProduct.MeasurementUnitId]
		o.Remark = stockProduct.Remark
		// 是否带上价格
		if req.WithPrice {
			salePriceColorKind := salePriceColorKinds.Pick(stockProduct.ProductId, stockProduct.ProductColorKindId)
			salePrice := salePriceAggs.MPCalcPurchaserLadderSalePrice(
				ctx,
				u.tx,
				saleLevel,
				salePriceColorKind,
				customerSalePriceAdjusts,
				saleLevelPrices,
				req.SaleCustomerId,
				stockProduct.ProductId,
				stockProduct.ProductColorKindId,
				stockProduct.ProductColorId,
				saleSystem,
			)
			o.SalePrice = salePrice
			o.WeightError = saleWeightErr
		}
		// 返回标准数量
		for _, item := range productMap {
			o.StandardWeight = item.StandardWeight
		}
		res = append(res, o)
	}
	list = res
	return
}

// 汇总库存- 库存进出情况
func (u *StockProductRepo) GetSumStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (data structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {
	var (
		outItemFcQuery structure.GetFpmOutOrderItemFcListQuery
		list           = make(structure.GetStockProductOutAndInDetailDataList, 0)
		o              mysql.UnionOutAndInBaseList
		inModelList    model.FpmInOrderItemFcList
		outModelList   model.FpmOutOrderItemFcList
		totalRWL       = make([]int, 3)
	)
	outItemFcQuery.SumStockId = q.StockProductId
	outItemFcQuery.BeginOrderTime = q.BeginTime
	outItemFcQuery.EndOrderTime = q.EndTime

	// if q.BeginTime == "" && q.EndTime == "" {
	// q.BeginTime = tools.QueryTime(time.Now().AddDate(0, -1, 0).Format("2006-01-02 15:04:05"))
	// q.EndTime = tools.QueryTime(time.Now().Format("2006-01-02 15:04:05"))
	// }

	// 汇总日期前的入库数据 下标：0 roll ; 1 weight ; 2 length
	inModelList, err = mysql.SumWarehouseInDataRWL(u.tx, q)
	if err != nil {
		return
	}
	inTotalRWL := inModelList.GetTotalRWL()
	// 汇总日期前的出库数据
	outModelList, err = mysql.SumWarehouseOutDataRWL(u.tx, q)
	if err != nil {
		return
	}
	outTotalRWL := outModelList.GetTotalRWL()

	// 获取盘点数据
	checkWeightList, err := mysql.SumWarehouseCheckDataRWL(u.tx, q)
	if err != nil {
		return
	}
	checkTotalRWL := checkWeightList.GetTotalRWL()

	// 上期结余
	totalRWL[0] = inTotalRWL[0] - outTotalRWL[0] + checkTotalRWL[0]
	totalRWL[1] = inTotalRWL[1] - outTotalRWL[1] + checkTotalRWL[1]
	totalRWL[2] = inTotalRWL[2] - outTotalRWL[2] + checkTotalRWL[2]
	data.TotalRoll = totalRWL[0]
	data.TotalWeight = totalRWL[1]
	data.TotalLength = totalRWL[2]

	// 获取日期范围数据
	o, count, err = mysql.FindWarehouseOutAndInSumList(u.tx, q)
	if err != nil {
		return
	}
	for _, src := range o {
		dst := structure.GetStockProductOutAndInDetailData{}
		totalRWL[0] = totalRWL[0] + src.InRoll - src.OutRoll + src.DifferenceRoll
		totalRWL[1] = totalRWL[1] + src.InWeight - src.OutWeight + src.DifferenceWeight
		totalRWL[2] = totalRWL[2] + src.InLength - src.OutLength + src.DifferenceLength
		u.swapStockProductOutAndInDetail2GetData(src, &dst, totalRWL)
		list = append(list, dst)
	}

	data.List = list
	return
}

func (u *StockProductRepo) swapStockProductOutAndInDetail2GetData(src mysql.UnionOutAndInBase, dst *structure.GetStockProductOutAndInDetailData, rwl []int) {

	dst.Id = src.Id
	dst.OrderNo = src.OrderNo
	dst.OrderTime = src.OrderTime
	dst.InRoll = src.InRoll
	dst.InWeight = src.InWeight
	dst.InLength = src.InLength
	dst.OutRoll = src.OutRoll
	dst.OutWeight = src.OutWeight
	dst.OutLength = src.OutLength
	outTypeMap := cus_const.GetWarehouseGoodOutTypeMap()
	inTypeMap := cus_const.GetWarehouseGoodInTypeMap()
	dst.DataType = src.DataType
	dst.OrderType = src.OrderType

	if src.DataType == cus_const.WarehouseGoodTypeIn {
		dst.OrderTypeName = inTypeMap[cus_const.WarehouseGoodInType(src.OrderType)]
	}
	if src.DataType == cus_const.WarehouseGoodTypeOut {
		dst.OrderTypeName = outTypeMap[cus_const.WarehouseGoodOutType(src.OrderType)]
	}
	if src.DataType == cus_const.WarehouseGoodTypeCheck {
		if src.DifferenceRoll > 0 {
			dst.InRoll = src.DifferenceRoll
		}
		if src.DifferenceRoll < 0 {
			dst.OutRoll = src.DifferenceRoll
		}
		if src.DifferenceWeight > 0 {
			dst.InWeight = src.DifferenceWeight
		}
		if src.DifferenceWeight < 0 {
			dst.OutWeight = src.DifferenceWeight
		}
		if src.DifferenceLength > 0 {
			dst.InLength = src.DifferenceLength
		}
		if src.DifferenceLength < 0 {
			dst.OutLength = src.DifferenceLength
		}

		dst.OrderTypeName = "成品库存盘点单"
	}
	// 结余
	dst.ResultRoll = rwl[0]
	dst.ResultWeight = rwl[1]
	dst.ResultLength = rwl[2]
}

// 定义了调拨进仓单信息的结构体
type allocateInInfo struct {
	Id             uint64
	OrderId        uint64 // 原始调拨进仓单ID
	OrderNo        string // 原始调拨进仓单编号
	SaleOrderId    uint64 // 关联的销售单ID
	SaleOrderNo    string // 关联的销售单编号
	OrderType      int
	BookRoll       int
	BookWeight     int
	CreateTime     string
	SaleUserId     uint64
	SaleUserName   string
	SaleFollowId   uint64
	SaleFollowName string
	BookOrderId    uint64 // 添加这个字段用于后续匹配
}

// 通过stock_id查询成品详细信息
func (u *StockProductRepo) GetStockProductDetailInfo(ctx context.Context, req *structure.GetStockProductDetailInfoQuery) (data structure.GetStockProductDetailInfoData, err error) {
	// 查询库存id相关信息
	var (
		stockProduct model.StockProduct
		bookLogs     model.StockProductBookLogList
		// 获取配布单信息
		saleProductOrderDao = dao.NewSaleProductOrderDao(ctx, false)
	)

	// 获取库存产品信息
	stockProduct, err = mysql.MustFirstStockProductByID(u.tx, req.StockId)
	if err != nil {
		return
	}

	// 查询相关的库存占用日志
	bookLogs, err = mysql.FindStockProductBookLogByStockId(u.tx, req.StockId)
	if err != nil {
		return
	}

	// 跟踪不同类型的记录数量
	outOrderIds := make(map[uint64]bool)            // 需要过滤的出仓单ID
	offsetOrderIds := make(map[uint64]bool)         // 被对冲的订单ID，包括销售单、预约单、调拨进仓单
	offsetLogIds := make(map[uint64]bool)           // 被对冲的具体记录ID与出仓单的关系映射，用于判断哪些调拨进仓单已被对冲
	allocateInToOutMap := make(map[uint64][]uint64) // 每个调拨进仓单ID对应的出仓单ID列表
	outToAllocateInMap := make(map[uint64]uint64)   // 每个出仓单ID对应的调拨进仓单ID

	// 创建消审记录与出仓单的关系映射
	cancelToOutMap := make(map[uint64]uint64) // 每个消审记录ID对应的出仓单ID
	outToCancelMap := make(map[uint64]uint64) // 每个出仓单ID对应的消审记录ID

	// 创建用于存储每个BookOrderId对应的最新消审记录的映射
	latestCancelLogs := make(map[uint64]model.StockProductBookLog)
	// 创建用于存储每个OrderId对应的最新调拨出仓单消审记录的映射
	latestAllocateOutCancelLogs := make(map[uint64]model.StockProductBookLog)

	// 先收集所有消审记录，并按BookOrderId分组，只保留每组中最新的一条
	for _, log := range bookLogs {
		if log.OrderType == 11 && log.BookOrderId > 0 {
			// 检查是否已存在该BookOrderId的记录
			existingLog, exists := latestCancelLogs[log.BookOrderId]
			// 如果不存在或当前日志比已存在的更新，则更新映射
			if !exists || log.CreateTime.After(existingLog.CreateTime) {
				latestCancelLogs[log.BookOrderId] = log
			}
		} else if log.OrderType == 25 {
			// 对于调拨出仓单消审记录，按OrderId分组，只保留最新的一条
			existingLog, exists := latestAllocateOutCancelLogs[log.OrderId]
			// 如果不存在或当前日志比已存在的更新，则更新映射
			if !exists || log.CreateTime.After(existingLog.CreateTime) {
				latestAllocateOutCancelLogs[log.OrderId] = log
			}
		}
	}

	// 创建订单类型为28的调拨进仓单映射，用于快速查找
	allocateInMap := make(map[uint64]allocateInInfo)

	// 收集所有OrderType=28的订单ID
	allocateInOrderIds := make([]uint64, 0)
	for _, log := range bookLogs {
		if log.OrderType == 28 {
			allocateInOrderIds = append(allocateInOrderIds, log.OrderId)
		}
	}

	// 获取所有调拨进仓单信息
	if len(allocateInOrderIds) > 0 {
		// 使用FindFpmInOrderByIDs代替FindFpmSaleAllocateInOrderByIDs
		allocateInOrders, errIn := mysql.FindFpmInOrderByIDs(u.tx, allocateInOrderIds)
		if errIn == nil && len(allocateInOrders) > 0 {
			for _, order := range allocateInOrders {
				// 存储调拨进仓单信息，便于后续处理
				info := allocateInInfo{
					Id:         order.Id,
					OrderId:    order.Id,
					OrderNo:    order.OrderNo,
					OrderType:  28,
					BookRoll:   0,
					BookWeight: 0,
					CreateTime: tools.MyTime(order.CreateTime).String(),
				}

				// 尝试通过调拨进仓单关联的配布单ID获取销售单信息
				if order.ArrangeOrderId > 0 {
					arrangeOrder, exist, errArrange := mysql.FirstFpmArrangeOrderByID(u.tx, order.ArrangeOrderId)
					if errArrange == nil && exist && arrangeOrder.SrcId > 0 && arrangeOrder.SrcType == 2 {
						// 通过配布单的SrcId获取销售单信息
						saleOrder, exist, _ := saleProductOrderDao.FirstById(ctx, u.tx, arrangeOrder.SrcId)
						if exist {
							info.SaleOrderId = arrangeOrder.SrcId
							info.SaleOrderNo = saleOrder.OrderNo
							info.SaleUserId = saleOrder.SaleUserId
							info.SaleFollowId = saleOrder.SaleFollowerId

							// 获取人员名称
							if info.SaleUserId > 0 {
								saleUserClient := employee.NewClientEmployeeService()
								info.SaleUserName, _ = saleUserClient.GetEmployeeName(ctx, info.SaleUserId)
							}
							if info.SaleFollowId > 0 {
								saleFollowerClient := employee.NewClientEmployeeService()
								info.SaleFollowName, _ = saleFollowerClient.GetEmployeeName(ctx, info.SaleFollowId)
							}
						}
					}
				}

				allocateInMap[order.Id] = info
			}
		}
	}

	// 获取需要过滤的销售出仓单和预约单的对冲记录
	for _, log := range bookLogs {
		// 处理OrderType=1的预约单和OrderType=8的销售单
		if log.OrderType == 1 || log.OrderType == 8 {
			// 查找出仓单记录
			for _, outLog := range bookLogs {
				if (outLog.OrderType == 10 && log.OrderType == 1) || // 预约单对应类型10的出仓单
					((outLog.OrderType == 10 || outLog.OrderType == 24) && log.OrderType == 8) { // 销售单可能对应类型10或24的出仓单
					// 检查BookOrderId匹配或者直接通过ID匹配
					if outLog.BookOrderId == log.OrderId {
						// 找到关联的出仓记录，记录需要过滤的ID
						outOrderIds[outLog.OrderId] = true
						// 需要同时记录OrderId和log.Id，确保完全过滤
						offsetOrderIds[log.OrderId] = true
						offsetLogIds[log.Id] = true
						break
					}
				}
			}
		}

		// 处理调拨进仓单(OrderType=28)的对冲记录
		if log.OrderType == 28 {
			// 统计每个调拨进仓单的BookRoll和BookWeight
			allocateInRolls := make(map[uint64]int)
			allocateInWeights := make(map[uint64]int)

			// 统计每个调拨进仓单的BookRoll和BookWeight
			for _, allocateLog := range bookLogs {
				if allocateLog.OrderType == 28 && allocateLog.OrderId == log.OrderId {
					allocateInRolls[log.OrderId] += allocateLog.BookRoll
					allocateInWeights[log.OrderId] += allocateLog.BookWeight
				}
			}

			// 检查是否有对应的出仓记录（类型为10）
			for _, outLog := range bookLogs {
				if outLog.OrderType == 10 && outLog.BookOrderId == log.OrderId {
					// 负值表示出仓
					if outLog.BookRoll < 0 && outLog.BookWeight < 0 {
						// 判断BookRoll和BookWeight是否与调拨进仓单完全匹配
						if -outLog.BookRoll == allocateInRolls[log.OrderId] && -outLog.BookWeight == allocateInWeights[log.OrderId] {
							// 找到完全匹配的记录，记录调拨进仓单和出仓单的对应关系
							allocateInToOutMap[log.OrderId] = append(allocateInToOutMap[log.OrderId], outLog.Id)
							outToAllocateInMap[outLog.Id] = log.OrderId

							// 标记需要过滤的记录ID
							outOrderIds[outLog.OrderId] = true
							// 需要同时记录OrderId和每条记录的Id
							offsetOrderIds[log.OrderId] = true
							offsetLogIds[log.Id] = true

							// 如果此调拨单关联了销售单，也将销售单标记为需要过滤
							if info, exists := allocateInMap[log.OrderId]; exists && info.SaleOrderId > 0 {
								// 将关联的销售单也标记为对冲，避免显示
								offsetOrderIds[info.SaleOrderId] = true
								// 查找销售单对应的记录ID并标记
								for _, saleLog := range bookLogs {
									if saleLog.OrderType == 8 && saleLog.OrderId == info.SaleOrderId {
										offsetLogIds[saleLog.Id] = true
									}
								}
							}
							break // 增加break语句，避免一条调拨单被多次处理
						}
					}
				}
			}
		}
	}

	// 创建一个映射，用于快速查找每个ID对应的调拨进仓单信息
	allocateInOrderMap := make(map[uint64]allocateInInfo)
	for orderId, info := range allocateInMap {
		allocateInOrderMap[orderId] = info
	}

	// 创建销售单ID到其对应记录ID的映射，方便后续过滤
	saleOrderToLogIds := make(map[uint64][]uint64)
	for _, log := range bookLogs {
		if log.OrderType == 8 {
			saleOrderToLogIds[log.OrderId] = append(saleOrderToLogIds[log.OrderId], log.Id)
		}
	}

	// 跟踪销售单和调拨单的关联关系
	saleToAllocateMap := make(map[uint64][]uint64) // 销售单ID -> 调拨单ID列表
	allocateToSaleMap := make(map[uint64]uint64)   // 调拨单ID -> 销售单ID

	// 建立销售单和调拨单的双向映射
	for allocateId, info := range allocateInMap {
		if info.SaleOrderId > 0 {
			saleToAllocateMap[info.SaleOrderId] = append(saleToAllocateMap[info.SaleOrderId], allocateId)
			allocateToSaleMap[allocateId] = info.SaleOrderId
		}
	}

	// 第二阶段过滤：对已对冲的销售单和调拨单进行深度处理
	// 创建一个映射来跟踪所有应该过滤的记录ID
	recordsToFilter := make(map[uint64]bool)
	for _, log := range bookLogs {
		// 标记所有销售单和相关调拨单的关系
		if log.OrderType == 8 { // 销售单
			if offsetOrderIds[log.OrderId] || offsetLogIds[log.Id] {
				// 如果销售单被对冲，则标记其所有记录
				for _, saleLog := range bookLogs {
					if saleLog.OrderType == 8 && saleLog.OrderId == log.OrderId {
						recordsToFilter[saleLog.Id] = true
					}
				}

				// 如果销售单有关联的调拨单，也标记调拨单
				if allocateIds, ok := saleToAllocateMap[log.OrderId]; ok {
					for _, allocateId := range allocateIds {
						offsetOrderIds[allocateId] = true
						// 标记调拨单对应的所有记录
						for _, allocateLog := range bookLogs {
							if allocateLog.OrderType == 28 && allocateLog.OrderId == allocateId {
								recordsToFilter[allocateLog.Id] = true
							}
						}
					}
				}
			}
		}

		// 处理调拨进仓单
		if log.OrderType == 28 { // 调拨进仓单
			if offsetOrderIds[log.OrderId] || offsetLogIds[log.Id] {
				// 如果调拨单被对冲，则标记其所有记录
				for _, allocateLog := range bookLogs {
					if allocateLog.OrderType == 28 && allocateLog.OrderId == log.OrderId {
						recordsToFilter[allocateLog.Id] = true
					}
				}

				// 如果调拨单有关联的销售单，也标记销售单
				if saleOrderId, ok := allocateToSaleMap[log.OrderId]; ok {
					offsetOrderIds[saleOrderId] = true
					// 标记销售单对应的所有记录
					for _, saleLog := range bookLogs {
						if saleLog.OrderType == 8 && saleLog.OrderId == saleOrderId {
							recordsToFilter[saleLog.Id] = true
						}
					}
				}
			}
		}
	}

	// 将所有应过滤的记录ID添加到offsetLogIds中
	for id := range recordsToFilter {
		offsetLogIds[id] = true
	}

	// 结果列表
	bookDetailList := make([]structure.StockProductBookLogData, 0)

	// 先处理调拨进仓单，将聚合后的数据添加到结果中
	addedOrderIds := make(map[uint64]bool) // 记录已添加的OrderId，防止重复
	// 收集每个调拨进仓单的BookRoll和BookWeight总和
	allocateInRolls := make(map[uint64]int)
	allocateInWeights := make(map[uint64]int)

	// 统计每个调拨进仓单的BookRoll和BookWeight
	for _, log := range bookLogs {
		if log.OrderType == 28 {
			allocateInRolls[log.OrderId] += log.BookRoll
			allocateInWeights[log.OrderId] += log.BookWeight
		}
	}

	for orderId, info := range allocateInMap {
		// 只有当订单未被过滤掉时才添加
		if !offsetOrderIds[orderId] && !addedOrderIds[orderId] {
			// 从统计映射中获取BookRoll和BookWeight总和
			totalRoll := allocateInRolls[orderId]
			totalWeight := allocateInWeights[orderId]

			// 确保有效的数量信息
			if totalRoll <= 0 || totalWeight <= 0 {
				continue // 跳过无效数据
			}

			// 创建一个StockProductBookLogData对象来保存该记录
			bookDetail := structure.StockProductBookLogData{
				Id:             info.Id,
				OrderId:        info.SaleOrderId,
				OrderNo:        info.SaleOrderNo,
				OrderType:      8,
				BookRoll:       totalRoll,
				BookWeight:     totalWeight,
				CreateTime:     info.CreateTime,
				SaleUserId:     info.SaleUserId,
				SaleUserName:   info.SaleUserName,
				SaleFollowId:   info.SaleFollowId,
				SaleFollowName: info.SaleFollowName,
				ArrangeOrderId: 0,
				ArrangeOrderNo: "/",
			}
			// 添加到结果列表
			bookDetailList = append(bookDetailList, bookDetail)
			addedOrderIds[orderId] = true
		}
	}

	// 处理消审记录与其对应的出仓记录的对冲关系

	// 收集所有出仓单和消审记录之间的关系
	for _, log := range bookLogs {
		if log.OrderType == 11 || log.OrderType == 25 { // 消审记录或调拨出仓单消审
			// 查找匹配的出仓记录
			for _, outLog := range bookLogs {
				// 对于消审记录，查找匹配的出仓单
				if log.OrderType == 11 && outLog.OrderType == 10 &&
					outLog.BookOrderId == log.BookOrderId &&
					outLog.BookRoll == -log.BookRoll &&
					outLog.BookWeight == -log.BookWeight {
					// 找到匹配的记录，记录消审记录和出仓记录的对应关系
					outToCancelMap[outLog.Id] = log.Id
					cancelToOutMap[log.Id] = outLog.Id

					// 标记出仓单需要过滤，但不过滤消审记录
					outOrderIds[outLog.Id] = true
					break
				}

				// 对于调拨出仓单消审，查找匹配的调拨出仓单
				if log.OrderType == 25 && outLog.OrderType == 24 &&
					outLog.BookOrderId == log.BookOrderId &&
					outLog.BookRoll == -log.BookRoll &&
					outLog.BookWeight == -log.BookWeight {
					// 找到匹配的记录，记录消审记录和出仓记录的对应关系
					outToCancelMap[outLog.Id] = log.Id
					cancelToOutMap[log.Id] = outLog.Id

					// 标记调拨出仓单需要过滤，但不过滤消审记录
					outOrderIds[outLog.Id] = true
					break
				}
			}
		}
	}

	// 创建映射存储每个OrderId对应的最新出仓记录和调拨出仓记录
	latestOutLogs := make(map[uint64]model.StockProductBookLog)
	latestAllocateOutLogs := make(map[uint64]model.StockProductBookLog)

	// 先收集所有出仓记录和调拨出仓记录，按OrderId分组，只保留每组中最新的一条
	for _, log := range bookLogs {
		if log.OrderType == 10 {
			// 检查是否已存在该OrderId的记录
			existingLog, exists := latestOutLogs[log.OrderId]
			// 如果不存在或当前日志比已存在的更新，则更新映射
			if !exists || log.CreateTime.After(existingLog.CreateTime) {
				latestOutLogs[log.OrderId] = log
			}
		} else if log.OrderType == 24 { // 调拨出仓单
			// 检查是否已存在该OrderId的记录
			existingLog, exists := latestAllocateOutLogs[log.OrderId]
			// 如果不存在或当前日志比已存在的更新，则更新映射
			if !exists || log.CreateTime.After(existingLog.CreateTime) {
				latestAllocateOutLogs[log.OrderId] = log
			}
		}
	}

	// 处理非调拨进仓单的记录
	for _, log := range bookLogs {
		// 对于出仓单（OrderType=10）和调拨出仓单（OrderType=24），需要特别处理以避免重复
		if log.OrderType == 10 {
			// 只保留最新的出仓记录
			latestLog, exists := latestOutLogs[log.OrderId]
			if !exists || latestLog.Id != log.Id {
				continue
			}
		}

		// 跳过已经确认需要过滤的记录，但保留消审记录和调拨出仓单消审
		if (outOrderIds[log.OrderId] && log.OrderType != 11 && log.OrderType != 25) || offsetOrderIds[log.Id] || offsetLogIds[log.Id] || offsetOrderIds[log.OrderId] {
			continue
		}

		// 跳过需要对冲的记录 - 增加更严格的过滤条件
		if offsetOrderIds[log.OrderId] || offsetLogIds[log.Id] || (log.BookOrderId > 0 && (offsetOrderIds[log.BookOrderId] || offsetLogIds[log.BookOrderId])) {
			continue
		}

		// 增强对销售单的过滤
		if log.OrderType == 8 {
			// 直接检查这个销售单ID是否在过滤列表中
			if offsetOrderIds[log.OrderId] {
				continue
			}

			// 增强的过滤逻辑：检查关联的调拨单
			hasDeallocatedAllocateOrder := false
			if allocateIds, exists := saleToAllocateMap[log.OrderId]; exists {
				for _, allocateId := range allocateIds {
					// 查找此调拨单是否被对冲
					allocateOffsetted := false
					if offsetOrderIds[allocateId] {
						allocateOffsetted = true
					} else {
						// 检查调拨单对应的出仓单
						for _, outLogs := range allocateInToOutMap[allocateId] {
							if outLogs > 0 {
								allocateOffsetted = true
								break
							}
						}
					}
					if allocateOffsetted {
						hasDeallocatedAllocateOrder = true
						break
					}
				}
			}
			if hasDeallocatedAllocateOrder {
				continue
			}
		}

		// 对于出仓单，如果存在对应的消审记录，则跳过
		if log.OrderType == 10 && outToCancelMap[log.Id] > 0 {
			continue
		}

		// 对于调拨出仓单，如果存在对应的消审记录，则跳过
		if log.OrderType == 24 && outToCancelMap[log.Id] > 0 {
			continue
		}

		// 对于调拨进仓单，如果有对应的出仓单完全匹配，则跳过
		if log.OrderType == 28 && len(allocateInToOutMap[log.OrderId]) > 0 {
			continue
		}

		// 跳过调拨进仓单，因为已在前面处理
		if log.OrderType == 28 {
			continue
		}

		// 对于消审记录(OrderType=11)和调拨出仓单消审(OrderType=25)，只保留最新记录
		if log.OrderType == 11 {
			// 查找latestCancelLogs中是否有该log.Id对应的最新记录
			found := false
			for _, latestLog := range latestCancelLogs {
				if latestLog.Id == log.Id {
					found = true
					break
				}
			}

			// 如果不是最新的消审记录，则跳过
			if !found {
				continue
			}

			// 检查是否有后续重新审核的出仓记录
			hasNewerOutRecord := false
			for _, outLog := range bookLogs {
				if outLog.OrderType == 10 &&
					outLog.BookOrderId == log.BookOrderId &&
					outLog.CreateTime.After(log.CreateTime) {
					// 有更新的出仓记录，说明消审后又重新审核了
					hasNewerOutRecord = true
					break
				}
			}

			// 如果有更新的出仓记录，不显示这条消审记录
			if hasNewerOutRecord {
				continue
			}
		} else if log.OrderType == 25 {
			// 对于调拨出仓单消审记录，只保留latestAllocateOutCancelLogs中的最新记录
			found := false
			for _, latestLog := range latestAllocateOutCancelLogs {
				if latestLog.Id == log.Id {
					found = true
					break
				}
			}

			// 如果不是最新的调拨出仓单消审记录，则跳过
			if !found {
				continue
			}

			// 检查是否有后续重新审核的调拨出仓记录
			hasNewerAllocateOutRecord := false
			for _, outLog := range bookLogs {
				if outLog.OrderType == 24 &&
					outLog.OrderId == log.OrderId &&
					outLog.CreateTime.After(log.CreateTime) {
					// 有更新的调拨出仓记录，说明消审后又重新审核了
					hasNewerAllocateOutRecord = true
					break
				}
			}

			// 如果有更新的调拨出仓记录，不显示这条消审记录
			if hasNewerAllocateOutRecord {
				continue
			}
		}

		// 初始化销售人员和销售跟单信息
		var saleUserId uint64 = 0
		var saleUserName string = ""
		var saleFollowerId uint64 = 0
		var saleFollowerName string = ""

		// 根据OrderType和OrderId查询对应的单据信息
		if log.OrderType == 1 { // 预约单
			// 查询预约单信息
			reservationOrder, err := mysql.MustFirstFpmOutReservationOrderByID(u.tx, log.OrderId)
			if err == nil {
				// 获取预约单编号
				log.OrderNo = reservationOrder.OrderNo

				// 使用预约单ID作为源ID查询配布单
				// 预约单的SrcType为1
				arrangeOrders, err := mysql.FindFpmArrangeOrderBySrcIds(u.tx, []uint64{log.OrderId})
				if err == nil && len(arrangeOrders) > 0 {
					// 根据仓库ID筛选配布单
					var matchedArrangeOrder *model.FpmArrangeOrder

					// 先尝试根据仓库ID查找对应的配布单
					for i, arrangeOrder := range arrangeOrders {
						if arrangeOrder.SrcType == 1 { // SrcType=1表示来源于预约单
							// 如果存在仓库筛选条件并且配布单的仓库ID与请求参数匹配
							if req.WarehouseId > 0 && arrangeOrder.WarehouseId == req.WarehouseId {
								matchedArrangeOrder = &arrangeOrders[i]
								break
							} else if matchedArrangeOrder == nil {
								// 如果没有指定仓库筛选或没有找到匹配的，先保存第一个
								matchedArrangeOrder = &arrangeOrders[i]
							}
						}
					}

					// 如果找到了匹配的配布单，设置到bookDetail中
					if matchedArrangeOrder != nil {
						// 创建一个StockProductBookLogData对象来保存该记录
						bookDetail := structure.StockProductBookLogData{
							Id:           log.Id,
							OrderId:      log.OrderId,
							OrderNo:      log.OrderNo,
							OrderType:    int(log.OrderType),
							BookRoll:     log.BookRoll,
							BookWeight:   log.BookWeight,
							CreateTime:   tools.MyTime(log.CreateTime).String(),
							SaleUserId:   saleUserId,
							SaleUserName: saleUserName,
							// 设置配布单信息
							ArrangeOrderId: matchedArrangeOrder.Id,
							ArrangeOrderNo: matchedArrangeOrder.OrderNo,
						}
						// 添加到结果列表
						bookDetailList = append(bookDetailList, bookDetail)
						continue
					}
				}
			}
		} else if log.OrderType == 8 { // 销售单
			var (
				srcId uint64
			)
			// 查询销售单信息
			saleOrder, exist, _ := saleProductOrderDao.FirstById(ctx, u.tx, log.OrderId)
			if exist {
				// 获取销售单编号和销售人员信息
				log.OrderNo = saleOrder.OrderNo
				saleUserId = saleOrder.SaleUserId
				saleFollowerId = saleOrder.SaleFollowerId

				// 获取销售人员姓名
				if saleUserId > 0 {
					saleUserClient := employee.NewClientEmployeeService()
					saleUserName, _ = saleUserClient.GetEmployeeName(ctx, saleUserId)
				}

				// 获取销售跟单员姓名
				if saleFollowerId > 0 {
					saleFollowerClient := employee.NewClientEmployeeService()
					saleFollowerName, _ = saleFollowerClient.GetEmployeeName(ctx, saleFollowerId)
				}

				// 使用销售单ID作为源ID查询配布单
				// 销售单的SrcType为8
				if log.BookOrderId == 0 {
					srcId = log.OrderId
				} else {
					srcId = log.BookOrderId
				}
				matchedArrangeOrder := MatchFpmArrangeOrderBySrcId(u.tx, srcId, req)
				// 如果找到了匹配的配布单，设置到bookDetail中
				if matchedArrangeOrder != nil {
					// 创建一个StockProductBookLogData对象来保存该记录
					bookDetail := structure.StockProductBookLogData{
						Id:             log.Id,
						OrderId:        log.OrderId,
						OrderNo:        log.OrderNo,
						OrderType:      int(log.OrderType),
						BookRoll:       log.BookRoll,
						BookWeight:     log.BookWeight,
						CreateTime:     tools.MyTime(log.CreateTime).String(),
						SaleUserId:     saleUserId,
						SaleUserName:   saleUserName,
						SaleFollowId:   saleFollowerId,
						SaleFollowName: saleFollowerName,
						// 设置配布单信息
						ArrangeOrderId: matchedArrangeOrder.Id,
						ArrangeOrderNo: matchedArrangeOrder.OrderNo,
					}
					// 添加到结果列表
					bookDetailList = append(bookDetailList, bookDetail)
					continue
				}
			}
		} else if log.OrderType == 25 { // 调拨出仓单
			// 查询调拨出仓单信息
			outOrder, exist, errOut := mysql.FirstFpmOutOrderByID(u.tx, log.OrderId)
			if exist && errOut == nil {
				// 获取调拨出仓单编号
				log.OrderNo = outOrder.OrderNo

				// 如果调拨出仓单关联了配布单，尝试获取配布单信息
				if outOrder.ArrangeOrderId > 0 {
					arrangeOrder, exist, errArrange := mysql.FirstFpmArrangeOrderByID(u.tx, outOrder.ArrangeOrderId)
					if exist && errArrange == nil && arrangeOrder.SrcId > 0 && arrangeOrder.SrcType == 2 {
						// 通过配布单的SrcId获取销售单信息
						saleOrder, exist, _ := saleProductOrderDao.FirstById(ctx, u.tx, arrangeOrder.SrcId)
						if exist {
							// 获取销售单编号和销售人员信息
							saleUserId = saleOrder.SaleUserId
							saleFollowerId = saleOrder.SaleFollowerId

							// 获取销售人员姓名
							if saleUserId > 0 {
								saleUserClient := employee.NewClientEmployeeService()
								saleUserName, _ = saleUserClient.GetEmployeeName(ctx, saleUserId)
							}

							// 获取销售跟单员姓名
							if saleFollowerId > 0 {
								saleFollowerClient := employee.NewClientEmployeeService()
								saleFollowerName, _ = saleFollowerClient.GetEmployeeName(ctx, saleFollowerId)
							}

							// 创建一个StockProductBookLogData对象来保存该记录
							bookDetail := structure.StockProductBookLogData{
								Id:             log.Id,
								OrderId:        saleOrder.Id,      // 使用销售单ID而不是调拨出仓单ID
								OrderNo:        saleOrder.OrderNo, // 使用销售单编号而不是调拨出仓单编号
								OrderType:      8,
								BookRoll:       log.BookRoll,
								BookWeight:     log.BookWeight,
								CreateTime:     tools.MyTime(log.CreateTime).String(),
								SaleUserId:     saleUserId,
								SaleUserName:   saleUserName,
								SaleFollowId:   saleFollowerId,
								SaleFollowName: saleFollowerName,
								// 设置配布单信息
								ArrangeOrderId: arrangeOrder.Id,
								ArrangeOrderNo: arrangeOrder.OrderNo,
							}
							// 添加到结果列表
							bookDetailList = append(bookDetailList, bookDetail)
							continue
						}
					}
				}

				// 如果没有找到配布单或销售单，仍然添加调拨出仓单记录
				bookDetail := structure.StockProductBookLogData{
					Id:           log.Id,
					OrderId:      log.OrderId,
					OrderNo:      log.OrderNo,
					OrderType:    int(log.OrderType),
					BookRoll:     log.BookRoll,
					BookWeight:   log.BookWeight,
					CreateTime:   tools.MyTime(log.CreateTime).String(),
					SaleUserId:   saleUserId,
					SaleUserName: saleUserName,
				}
				// 添加到结果列表
				bookDetailList = append(bookDetailList, bookDetail)
				continue
			}
		} else if log.OrderType == 11 {
			// 消审单需要查找关联的销售单（如果存在BookOrderId）
			if log.OrderType == 11 && log.BookOrderId > 0 {
				// 尝试查询销售单信息
				saleOrder, exist, _ := saleProductOrderDao.FirstById(ctx, u.tx, log.BookOrderId)
				// 如果不存在，尝试从调拨进仓单找配布单id
				if !exist {
					// 尝试查询调拨进仓单信息
					allocateInOrder, exist, errIn := mysql.FirstFpmInOrderByID(u.tx, log.BookOrderId)
					if exist && errIn == nil {
						// 如果调拨进仓单关联了配布单，尝试获取配布单信息
						if allocateInOrder.ArrangeOrderId > 0 {
							arrangeOrder, exist, errArrange := mysql.FirstFpmArrangeOrderByID(u.tx, allocateInOrder.ArrangeOrderId)
							if exist && errArrange == nil && arrangeOrder.SrcId > 0 && arrangeOrder.SrcType == 2 {
								// 通过配布单的SrcId获取销售单信息
								saleOrder, exist, _ := saleProductOrderDao.FirstById(ctx, u.tx, arrangeOrder.SrcId)
								if exist {
									// 获取销售单编号和销售人员信息
									log.OrderNo = saleOrder.OrderNo
									saleUserId = saleOrder.SaleUserId
									saleFollowerId = saleOrder.SaleFollowerId

									// 获取销售人员姓名
									if saleUserId > 0 {
										saleUserClient := employee.NewClientEmployeeService()
										saleUserName, _ = saleUserClient.GetEmployeeName(ctx, saleUserId)
									}

									// 获取销售跟单员姓名
									if saleFollowerId > 0 {
										saleFollowerClient := employee.NewClientEmployeeService()
										saleFollowerName, _ = saleFollowerClient.GetEmployeeName(ctx, saleFollowerId)
									}
								}
							}
						}
					}
				}
				arrangeOrder := MatchFpmArrangeOrderBySrcId(u.tx, saleOrder.Id, req)
				if exist {
					// 获取销售单编号和销售人员信息
					log.OrderNo = saleOrder.OrderNo
					saleUserId = saleOrder.SaleUserId
					saleFollowerId = saleOrder.SaleFollowerId

					// 获取销售人员姓名
					if saleUserId > 0 {
						saleUserClient := employee.NewClientEmployeeService()
						saleUserName, _ = saleUserClient.GetEmployeeName(ctx, saleUserId)
					}

					// 获取销售跟单员姓名
					if saleFollowerId > 0 {
						saleFollowerClient := employee.NewClientEmployeeService()
						saleFollowerName, _ = saleFollowerClient.GetEmployeeName(ctx, saleFollowerId)
					}
				}
				if arrangeOrder != nil {
					// 创建一个StockProductBookLogData对象来保存该记录
					bookDetail := structure.StockProductBookLogData{
						Id:             log.Id,
						OrderId:        log.OrderId,
						OrderNo:        log.OrderNo,
						OrderType:      int(log.OrderType),
						BookRoll:       log.BookRoll,
						BookWeight:     log.BookWeight,
						CreateTime:     tools.MyTime(log.CreateTime).String(),
						SaleUserId:     saleUserId,
						SaleUserName:   saleUserName,
						SaleFollowId:   saleFollowerId,
						SaleFollowName: saleFollowerName,
						// 设置配布单信息
						ArrangeOrderId: arrangeOrder.Id,
						ArrangeOrderNo: arrangeOrder.OrderNo,
					}
					// 添加到结果列表
					bookDetailList = append(bookDetailList, bookDetail)
					continue
				}
			}
		}

		// 如果没有找到配布单，也需要添加销售单记录到结果列表中
		bookDetail := structure.StockProductBookLogData{
			Id:             log.Id,
			OrderId:        log.OrderId,
			OrderNo:        log.OrderNo,
			OrderType:      int(log.OrderType),
			BookRoll:       log.BookRoll,
			BookWeight:     log.BookWeight,
			CreateTime:     tools.MyTime(log.CreateTime).String(),
			SaleUserId:     saleUserId,
			SaleUserName:   saleUserName,
			SaleFollowId:   saleFollowerId,
			SaleFollowName: saleFollowerName,
		}
		// 添加到结果列表
		bookDetailList = append(bookDetailList, bookDetail)
	}

	// 设置返回数据
	data = structure.GetStockProductDetailInfoData{
		BookDetails: bookDetailList,
	}

	// 从stockProduct中填充数据
	data.ProductId = stockProduct.ProductId
	data.WarehouseId = stockProduct.WarehouseId
	data.CustomerId = stockProduct.CustomerId
	data.ProductColorId = stockProduct.ProductColorId
	data.ProductLevelId = stockProduct.ProductLevelId
	data.ProductRemark = stockProduct.ProductRemark

	// 获取成品和颜色名称
	if stockProduct.ProductId > 0 {
		productSvc := product2.NewProductClient()
		productItem, err := productSvc.GetProduct(ctx, product2.ProductReq{Id: stockProduct.ProductId})
		if err == nil {
			data.ProductCode = productItem.FinishProductCode
			data.ProductName = productItem.FinishProductName
		}
	}

	if stockProduct.ProductColorId > 0 {
		productColorSvc := product2.NewProductColorClient()
		productColorItem, err := productColorSvc.GetProductColorById(ctx, stockProduct.ProductColorId)
		if err == nil {
			data.ProductColorCode = productColorItem.ProductColorCode
			data.ProductColorName = productColorItem.ProductColorName
		}
	}

	if stockProduct.CustomerId > 0 {
		customerSvc := biz_unit.NewClientBizUnitService()
		customerNames, _ := customerSvc.GetBizUnitNameByIds(ctx, []uint64{stockProduct.CustomerId})
		if name, exists := customerNames[stockProduct.CustomerId]; exists {
			data.CustomerName = name
		}
	}

	if stockProduct.WarehouseId > 0 {
		warehouseSvc := warehouse.NewPhysicalWarehouseClient()
		warehouseNames, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, []uint64{stockProduct.WarehouseId})
		if err == nil {
			if name, exists := warehouseNames[stockProduct.WarehouseId]; exists {
				data.WarehouseName = name
			}
		}
	}

	if stockProduct.ProductLevelId > 0 {
		productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
		productLevelNames, _ := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, []uint64{stockProduct.ProductLevelId})
		if name, exists := productLevelNames[stockProduct.ProductLevelId]; exists {
			data.ProductLevelName = name
		}
	}

	return data, nil
}

func MatchFpmArrangeOrderBySrcId(tx *mysql_base.Tx, srcId uint64, req *structure.GetStockProductDetailInfoQuery) (matchedArrangeOrder *model.FpmArrangeOrder) {
	arrangeOrders, err := mysql.FindFpmArrangeOrderBySrcIds(tx, []uint64{srcId})
	if err == nil && len(arrangeOrders) > 0 {
		// 先尝试根据仓库ID查找对应的配布单
		for i, arrangeOrder := range arrangeOrders {
			if arrangeOrder.SrcType == 2 { // SrcType=8表示来源于销售单
				// 如果存在仓库筛选条件并且配布单的仓库ID与请求参数匹配
				if req.WarehouseId > 0 && arrangeOrder.WarehouseId == req.WarehouseId {
					matchedArrangeOrder = &arrangeOrders[i]
					break
				} else if matchedArrangeOrder == nil {
					// 如果没有指定仓库筛选或没有找到匹配的，先保存第一个
					matchedArrangeOrder = &arrangeOrders[i]
				}
			}
		}
	}
	return matchedArrangeOrder
}
