package tenant_management

import (
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	"hcscm/vars"
	"time"
)

// 充值记录
type RechargeHistory struct {
	mysql_base.Model
	Id            uint64 `gorm:"column:id"`
	CodeListOrcID uint64 `gorm:"column:code_list_orc_id"`
	// RechargeTime            int       `gorm:"column:recharge_time"`   // 充值时间
	DeadLine       time.Time           `gorm:"column:deadline"`         // 截止时间
	Remark         string              `gorm:"column:remark"`           // 备注
	Voucher        string              `gorm:"column:voucher"`          // 凭证
	UpdateUserName string              `gorm:"column:update_user_name"` // 更新人
	Type           common.RechargeType `gorm:"column:type"`             // 充值类型
}

func (RechargeHistory) OnceComplexKey() [][]string {
	return [][]string{}
}

func (r RechargeHistory) GetId() uint64 {
	return r.Id
}

func (RechargeHistory) TableName() string {
	return "recharge_history"
}

func (RechargeHistory) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeRechargeHistoryNotExist
}

func (RechargeHistory) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeRechargeHistoryAlreadyExist
}

type RechargeHistoryList []RechargeHistory

func (l RechargeHistoryList) Pick(id uint64) RechargeHistory {
	for _, rechargeHistory := range l {
		if rechargeHistory.Id == id {
			return rechargeHistory
		}
	}
	return RechargeHistory{}
}

func NewRechargeHistory(codeListOrcManagementID uint64, deadline time.Time, remark string, voucher string) RechargeHistory {
	return RechargeHistory{
		Id:            vars.Snowflake.GenerateId().UInt64(),
		CodeListOrcID: codeListOrcManagementID,
		DeadLine:      deadline,
		Remark:        remark,
		Voucher:       voucher,
	}
}

// func NewRechargeHistory(rechargeHistory RechargeHistory, param tenant_management.AddRechargeHistoryParam, deadline time.Time, remark string, voucher string) RechargeHistory {}
