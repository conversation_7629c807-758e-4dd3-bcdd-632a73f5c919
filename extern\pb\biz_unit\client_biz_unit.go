package biz_unit

import (
	"context"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	"hcscm/service/biz_unit"
	structure_base "hcscm/structure/system"
)

type IClientBizUnitService interface {
	GetBizUnitNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error)
	Create(ctx context.Context, category common_system.Category, saleSystemId uint64, name string, isSimpleAdd bool) (uint64, error)
	GetBizUnitByIds(ctx context.Context, ids []uint64) (map[uint64][2]string, error)
	GetBizUnitCodeAndNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, map[uint64]string, error)
	GetBizUnitIdsByNameLike(ctx context.Context, name string) ([]uint64, error)
	GetBizUnitByName(ctx context.Context, name []string) (res ResList, err error)
	GetBizUnitDetailByID(ctx context.Context, req Req) (data Res, err error)
	GetSaleAreaNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error)
	GetSaleAreaByIds(ctx context.Context, ids []uint64) (map[uint64][2]string, error)
	GetSaleAreaByCodeOrName(ctx context.Context, code, name []string) (res SaleResList, err error)
	GetSaleGroupByCodeOrName(ctx context.Context, name []string) (res SaleResList, err error)
	GetSaleGroupNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error)
	GetBizUnitTypeById(ctx context.Context, ids []uint64) (map[uint64][]uint64, error)
	GetBizUnitTypeByIdSingle(ctx context.Context, id uint64) ([]uint64, error)
	QuerySettleTypeById(ctx context.Context, ids []uint64) (map[uint64][2]int, error)
	GetBizUnitDnfChargingMethod(ctx context.Context, id uint64) (int, error)
	GetBizUnitFactoryLogisticsNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error)
	GetBizUnitNMPAById(ctx context.Context, ids []uint64) (map[uint64][4]string, error)
	GetCustomerByQuery(ctx context.Context, req Req) (list ResList, total int, err error)
	GetEnableSupplierDyeingFactoryId(ctx context.Context, ids []uint64) ([]uint64, error)
	GetBizUnitIdsByBizUnitTypeId(ctx context.Context, ids []uint64, category uint8, unitTypeId uint64) ([]uint64, error)
	GetSupplierDnfChargeMethod(ctx context.Context, id uint64) (int, error)
	QueryBizUnitListById(ctx context.Context, ids []uint64) (map[uint64]Res, error)
	GetBizUnitMapListByCodeOrName(ctx context.Context, code, name []string) (list structure_base.GetBizUnitListResponse, err error)
}

type ClientBizUnitService struct {
}

func NewClientBizUnitService() IClientBizUnitService {
	return &ClientBizUnitService{}
}

func (s *ClientBizUnitService) GetBizUnitNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error) {
	if len(ids) == 0 {
		return map[uint64]string{}, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitNameByIds(ctx, tx, ids)
}

func (s *ClientBizUnitService) Create(ctx context.Context, category common_system.Category, saleSystemId uint64, name string, isSimpleAdd bool) (uint64, error) {
	if len(name) == 0 {
		return 0, nil
	}
	client := biz_unit.NewBizUnitService()
	// 先查是否存在编号
	id, err := client.AddBizUnit(ctx, &structure_base.AddUnitDTOReq{
		BizUnitInfo: structure_base.BizUnitInfo{
			Name:            name,
			FullName:        name,
			CustomCode:      name,
			UnitTypeID:      []uint64{},
			SaleSystemID:    saleSystemId,
			ContactName:     "",
			Phone:           "",
			Address:         "",
			Email:           "",
			Remark:          "",
			SettleType:      0,
			SettleCycle:     0,
			CreditLimit:     0,
			CreditLevel:     0,
			SellerID:        0,
			OrderFollowerID: 0,
			OrderQcUserId:   0,
			SaleAreaID:      0,
			SaleGroupID:     0,
			CreditCode:      "",
		},
		Category:    int(category),
		IsSimpleAdd: isSimpleAdd,
	})
	if err != nil {
		return 0, err
	}
	return id, nil
}

// 根据id获取编号和名称
func (s *ClientBizUnitService) GetBizUnitByIds(ctx context.Context, ids []uint64) (map[uint64][2]string, error) {
	if len(ids) == 0 {
		return map[uint64][2]string{}, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitByIds(ctx, tx, ids)
}

// 根据id获取编号和名称
func (s *ClientBizUnitService) GetBizUnitCodeAndNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, map[uint64]string, error) {
	if len(ids) == 0 {
		return nil, nil, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	list, err := client.GetBizUnitByIds(ctx, tx, ids)
	if err != nil {
		return nil, nil, err
	}
	cm, nm := make(map[uint64]string), make(map[uint64]string)
	for _, item := range list {
		cm[item.Id] = item.CustomCode
		nm[item.Id] = item.Name
	}
	return cm, nm, nil
}

// GetBizUnitIdsByNameLike 根据模糊名称查询ids
func (s *ClientBizUnitService) GetBizUnitIdsByNameLike(ctx context.Context, name string) ([]uint64, error) {
	if name == "" {
		return nil, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitIdsByNameLike(ctx, tx, name)
}

// 根据名称获取往来单位
func (s *ClientBizUnitService) GetBizUnitByName(ctx context.Context, name []string) (res ResList, err error) {
	if len(name) == 0 {
		return nil, nil
	}
	var (
		items = make(ResList, 0)
	)
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	bizUnitList, err := client.QueryBizUnitByName(ctx, tx, name)
	if err != nil {
		return
	}
	for _, bizUnit := range bizUnitList {
		item := Res{}
		buildBizUnitResponse(ctx, &item, bizUnit)
		items = append(items, item)
	}

	res = items
	return
}

// 根据id获取详情
func (s *ClientBizUnitService) GetBizUnitDetailByID(ctx context.Context, req Req) (data Res, err error) {
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	getBizData, err := client.QueryBizUnitDetail(ctx, tx, &biz_unit.QueryBizUnitDetailReq{Id: req.Id, Category: req.Category, Phone: req.Phone})
	if err != nil {
		return
	}
	data.Id = getBizData.Id
	data.Code = getBizData.Code
	data.CustomCode = getBizData.CustomCode
	data.Name = getBizData.Name
	data.Address = getBizData.Address
	data.Phone = getBizData.Phone
	data.FaxNumber = getBizData.FaxNumber
	data.ContactName = getBizData.ContactName
	data.Category = getBizData.Category
	data.MainUnitTypeId = getBizData.MainUnitTypeId
	data.MainUnitTypeName = getBizData.MainUnitTypeName
	data.MainBizUnitType = int(getBizData.MainBizUnitType)
	return
}

// 拼装往来单位返参
func buildBizUnitResponse(ctx context.Context, res *Res, bizUnit *structure_base.GetBizUnitListItem) {
	res.Id = bizUnit.Id
	res.Name = bizUnit.Name
	res.FullName = bizUnit.FullName
	res.Code = bizUnit.Code
	res.CustomCode = bizUnit.CustomCode
	res.UnitTypeID = bizUnit.UnitTypeID
	res.SaleSystemID = bizUnit.SaleSystemID
	res.SaleSystemIds = bizUnit.SaleSystemIds
	res.ContactName = bizUnit.ContactName
	res.Phone = bizUnit.Phone
	res.Address = bizUnit.Address
	res.Email = bizUnit.Email
	res.Remark = bizUnit.Remark
	res.SettleType = bizUnit.SettleType
	res.SettleCycle = bizUnit.SettleCycle
	res.CreditLimit = bizUnit.CreditLimit
	res.CreditLevel = bizUnit.CreditLevel
	res.OrderFollowerID = bizUnit.OrderFollowerID
	res.OrderFollowerName = bizUnit.OrderFollowerName
	res.OrderQcUserId = bizUnit.OrderQcUserId
	res.OrderQcUserName = bizUnit.OrderQcUserName
	res.SaleAreaID = bizUnit.SaleAreaID
	res.SaleGroupID = bizUnit.SaleGroupID
	res.CreditCode = bizUnit.CreditCode
	res.SaleUserId = bizUnit.SellerID
	res.SaleUserName = bizUnit.SellerName
	res.CategoryName = bizUnit.CategoryName
	res.UnitTypeName = bizUnit.UnitTypeName
	res.SaleSystemName = bizUnit.SaleSystemName
	res.SaleSystemNames = bizUnit.SaleSystemNames
	res.Status = bizUnit.Status
	res.IsBlacklist = bizUnit.IsBlacklist
	res.SaleGroupName = bizUnit.SaleGroupName
	res.SaleAreaName = bizUnit.SaleAreaName
	res.SettleTypeName = bizUnit.SettleTypeName
	res.CreditLevelName = bizUnit.CreditLevelName
	res.DnfChargingMethod = bizUnit.DnfChargingMethod
	res.OrderFollowerPhone = bizUnit.OrderFollowerPhone
	res.FaxNumber = bizUnit.FaxNumber
}

func (s *ClientBizUnitService) GetSaleAreaNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error) {
	if len(ids) == 0 {
		return map[uint64]string{}, nil
	}
	client := biz_unit.NewSaleAreaQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QuerySaleAreaNameByIds(ctx, tx, ids)
}

func (s *ClientBizUnitService) GetSaleAreaByIds(ctx context.Context, ids []uint64) (map[uint64][2]string, error) {
	if len(ids) == 0 {
		return map[uint64][2]string{}, nil
	}
	client := biz_unit.NewSaleAreaQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QuerySaleAreaByIds(ctx, tx, ids)
}

func (s *ClientBizUnitService) GetSaleAreaByCodeOrName(ctx context.Context, code, name []string) (res SaleResList, err error) {
	if len(code) == 0 && len(name) == 0 {
		return
	}
	client := biz_unit.NewSaleAreaQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	list, err := client.QueryMultiSaleAreaByCodeOrName(ctx, tx, code, name)
	if err != nil {
		return
	}
	for _, item := range list {
		o := SaleRes{}
		o.SaleAreaID = item.Id
		o.SaleAreaCode = item.CustomCode
		o.SaleAreaName = item.Name
		res = append(res, o)
	}
	return
}

func (s *ClientBizUnitService) GetSaleGroupByCodeOrName(ctx context.Context, name []string) (res SaleResList, err error) {
	if len(name) == 0 {
		return
	}
	client := biz_unit.NewSaleAreaQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	list, err := client.QueryMultiSaleGroupByCodeOrName(ctx, tx, name)
	if err != nil {
		return
	}
	for _, item := range list {
		o := SaleRes{}
		o.SaleGroupID = item.Id
		o.SaleGroupName = item.Name
		res = append(res, o)
	}
	return
}

func (s *ClientBizUnitService) GetSaleGroupNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error) {
	if len(ids) == 0 {
		return map[uint64]string{}, nil
	}
	client := biz_unit.NewSaleGroupQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QuerySaleGroupNameByIds(ctx, tx, ids)
}

// 获取往来单位的类型
func (s *ClientBizUnitService) GetBizUnitTypeById(ctx context.Context, ids []uint64) (map[uint64][]uint64, error) {
	if len(ids) == 0 {
		return map[uint64][]uint64{}, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitTypeById(ctx, tx, ids)
}

// 获取往来单位的类型单id
func (s *ClientBizUnitService) GetBizUnitTypeByIdSingle(ctx context.Context, id uint64) ([]uint64, error) {
	if id == 0 {
		return []uint64{}, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitTypeByIdSingle(ctx, tx, id)
}

// 获取结算类型
func (s *ClientBizUnitService) QuerySettleTypeById(ctx context.Context, ids []uint64) (map[uint64][2]int, error) {
	if len(ids) == 0 {
		return map[uint64][2]int{}, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QuerySettleTypeById(ctx, tx, ids)
}

// 获取往来单位的染整收费方式
func (s *ClientBizUnitService) GetBizUnitDnfChargingMethod(ctx context.Context, id uint64) (int, error) {
	if id <= 0 {
		return 0, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitDnfChargingMethod(ctx, tx, id)
}

// 获取加工厂名称
func (s *ClientBizUnitService) GetBizUnitFactoryLogisticsNameByIds(ctx context.Context, ids []uint64) (map[uint64]string, error) {
	if len(ids) == 0 {
		return map[uint64]string{}, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitFactoryLogisticsById(ctx, tx, ids)
}

// 获取收货单位名N、联系人M、收货电话P、收货地址A (NMPA)
func (s *ClientBizUnitService) GetBizUnitNMPAById(ctx context.Context, ids []uint64) (map[uint64][4]string, error) {
	m := make(map[uint64][4]string)
	if len(ids) == 0 {
		return m, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitNMPAById(ctx, tx, ids)
}

// 根据条件获取客户
func (s *ClientBizUnitService) GetCustomerByQuery(ctx context.Context, req Req) (list ResList, total int, err error) {
	client := biz_unit.NewBizUnitQueryService()
	var (
		items = make(ResList, 0)
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	// 获取列表
	rawlist, total, err := client.QueryBizUnitList(ctx, tx, &structure_base.GetBizUnitListParams{ListQuery: req.ListQuery, Id: req.Ids, SaleSystemID: req.SaleSystemID, SellerID: req.SaleUserId, Status: 1})
	if err != nil {
		return
	}
	if total == 0 {
		return items, total, nil
	}

	for _, bizUnit := range rawlist {
		item := Res{}
		buildBizUnitResponse(ctx, &item, bizUnit)
		items = append(items, item)
	}

	list = items
	return
}

// GetEnableSupplierDyeingFactoryId 获取有效染厂（供应商）的id
func (s *ClientBizUnitService) GetEnableSupplierDyeingFactoryId(ctx context.Context, ids []uint64) ([]uint64, error) {
	client := biz_unit.NewBizUnitQueryService()
	// 1供应商; 12染整厂
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryEnableBizUnitIdsByCategoryAndType(ctx, tx, ids, 1, 12)
}

// GetBizUnitIdsByBizUnitTypeId 获取往来单位的id 1供应商 2客户
func (s *ClientBizUnitService) GetBizUnitIdsByBizUnitTypeId(ctx context.Context, ids []uint64, category uint8, unitTypeId uint64) ([]uint64, error) {
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryEnableBizUnitIdsByCategoryAndType(ctx, tx, ids, category, unitTypeId)
}

// GetSupplierDnfChargeMethod 获取供应商的染费收费方式
func (s *ClientBizUnitService) GetSupplierDnfChargeMethod(ctx context.Context, id uint64) (int, error) {
	if id == 0 {
		return 0, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return client.QueryBizUnitDnfChargingMethod(ctx, tx, id)
}

func (s *ClientBizUnitService) QueryBizUnitListById(ctx context.Context, ids []uint64) (map[uint64]Res, error) {
	m := make(map[uint64]Res)
	if len(ids) == 0 {
		return m, nil
	}
	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	list, _, err := client.QueryBizUnitList(ctx, tx, &structure_base.GetBizUnitListParams{Ids: ids})
	if err != nil {
		return m, err
	}
	for _, bizUnit := range list {
		item := Res{}
		buildBizUnitResponse(ctx, &item, bizUnit)
		m[bizUnit.Id] = item
	}
	return m, nil
}

// 根据编号或名称获取往来单位
func (s ClientBizUnitService) GetBizUnitMapListByCodeOrName(ctx context.Context, code, name []string) (list structure_base.GetBizUnitListResponse, err error) {

	if len(code) == 0 && len(name) == 0 {
		return
	}

	client := biz_unit.NewBizUnitQueryService()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	unitList, _, err := client.QueryBizUnitList(ctx, tx, &structure_base.GetBizUnitListParams{
		IsFindByCodeOrName: true,
		Codes:              code,
		Names:              name,
	})
	if err != nil {
		return
	}
	list = unitList
	return
}
