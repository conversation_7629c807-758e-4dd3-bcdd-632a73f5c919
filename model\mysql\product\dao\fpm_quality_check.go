package dao

import (
	consts "hcscm/common/product"
	"hcscm/model/mysql/mysql_base"
	. "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"time"
)

func MustCreateFpmQualityCheck(tx *mysql_base.Tx, r FpmQualityCheck) (o FpmQualityCheck, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateFpmQualityCheck(tx *mysql_base.Tx, r FpmQualityCheck) (o FpmQualityCheck, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteFpmQualityCheck(tx *mysql_base.Tx, r FpmQualityCheck) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstFpmQualityCheckByID(tx *mysql_base.Tx, id uint64) (r FpmQualityCheck, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstFpmQualityCheckByID(tx *mysql_base.Tx, id uint64) (r FpmQualityCheck, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FindFpmQualityCheckByFpmQualityCheckID(tx *mysql_base.Tx, objects ...interface{}) (o FpmQualityCheckList, err error) {
	ids := GetFpmQualityCheckIdList(objects)
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
		list []FpmQualityCheck
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmQualityCheckByIDs(tx *mysql_base.Tx, ids []uint64) (o FpmQualityCheckList, err error) {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
		list []FpmQualityCheck
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddContainMatch("id", ids)
	cond.AddSort("volume_number")
	err = mysql_base.FindByCondSort(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmQualityCheckBySameDyelot(tx *mysql_base.Tx, dyelotNumber string, sumStockId, colorId uint64) (o FpmQualityCheckList, err error) {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
		list []FpmQualityCheck
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableEqual(r, "dyelot_number", dyelotNumber)
	cond.AddTableEqual(r, "sum_stock_id", sumStockId)
	cond.AddTableEqual(r, "product_color_id", colorId)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmQualityCheck(tx *mysql_base.Tx, q *structure.GetFpmQualityCheckListQuery) (o FpmQualityCheckList, count int, err error) {
	var (
		r           FpmQualityCheck
		s           StockProductDetail
		cond        = mysql_base.NewCondition()
		list        []FpmQualityCheck
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	groupFields = []string{}
	// if q.QualityCheckDate.IsYMDZero() {
	//	cond.AddTableEqual(r, "quality_check_date", time.Now().Format("2006-01-02"))
	// } else {
	//	cond.AddTableEqual(r, "quality_check_date", q.QualityCheckDate.StringYMD())
	// }
	cond.AddTableLeftJoiner(r, s, "stock_id", "id")
	if !q.QualityCheckDateBegin.IsYMDZero() && !q.QualityCheckDateEnd.IsYMDZero() {
		cond.AddTableBetween(r, "quality_check_date", q.QualityCheckDateBegin.StringYMD(), q.QualityCheckDateEnd.StringYMD2DayListTimeYMDHMS())
	}
	// 判断缸号
	if q.DyelotNumber != "" {
		cond.AddTableMultiFieldLikeMatch(r, []string{"dyelot_number"}, q.DyelotNumber)
	}
	// 判断卷号
	if q.VolumeNumber != 0 {
		cond.AddTableEqual(s, "volume_number", q.VolumeNumber)
	}
	// 筛选目标产品
	if q.ProductId > 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	// 筛选目标产品颜色
	if q.ProductColorId > 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	// 筛选来源供货商信息
	if q.SourceSupplierId > 0 {
		cond.AddTableEqual(s, "supplier_id", q.SourceSupplierId)
	}
	// 筛选质检员信息
	if q.QualityCheckerId > 0 {
		cond.AddTableEqual(r, "quality_checker_id", q.QualityCheckerId)
	}
	// if !q.IsShowAll {
	//	cond.AddTableEqual(r, "quality_checker_id", q.QualityCheckerId)
	// }
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据ids删除
func MustDeleteFpmQualityCheckByIds(tx *mysql_base.Tx, ids []uint64) (err error) {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
	)
	cond.AddContainMatchToOR("id", ids)
	err = mysql_base.MustDeleteModelByCond(tx, r, cond)
	if err != nil {
		return
	}
	return
}

// 根据上级id获取
func JudgeFpmQualityCheckDefectIsExit(tx *mysql_base.Tx, stockId uint64, qcDate tools.QueryTime, qcerId uint64) (data FpmQualityCheck, exit bool, err error) {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "stock_id", stockId)
	cond.AddTableEqual(r, "quality_check_date", qcDate.StringYMD())
	cond.AddTableEqual(r, "quality_checker_id", qcerId)

	exit, err = mysql_base.FirstByCond(tx, &r, cond)
	if err != nil {
		return
	}
	data = r
	return
}

// 根据缸号或者供货商id获取质检报表
func SearchFpmQualityCheckBySameDyelot(tx *mysql_base.Tx, q *structure.GetFpmQualityCheckDyelotNumberListQuery) (o FpmQualityCheckDyelotNumberList, count int, err error) {
	var (
		r           FpmQualityCheck
		s           StockProductDetail
		cond        = mysql_base.NewCondition()
		list        []FpmQualityCheckDyelotNumber
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	// 关联库存表
	cond.AddTableLeftJoiner(r, s, "stock_id", "id")
	// 根据缸号查询
	if q.DyelotNumber != "" {
		cond.AddTableMultiFieldLikeMatch(r, []string{"dyelot_number"}, q.DyelotNumber)
	}
	if !q.StartWarehouseInTime.IsYMDZero() && !q.EndWarehouseInTime.IsYMDZero() {
		cond.AddTableBetween(s, "warehouse_in_time", q.StartWarehouseInTime.StringYMD(), q.EndWarehouseInTime.StringYMD2DayListTimeYMDHMS())
	}
	// 根据客户查询
	if q.CustomerId > 0 {
		cond.AddTableEqual(s, "customer_id", q.SupplierId)
	}
	// 根据供应商查询
	if q.SupplierId > 0 {
		cond.AddTableEqual(s, "supplier_id", q.SupplierId)
	}
	// 根据供应商查询
	if q.ProductId > 0 {
		cond.AddTableEqual(s, "product_id", q.ProductId)
	}
	// 根据供应商查询
	if q.ProductColorId > 0 {
		cond.AddTableEqual(s, "product_color_id", q.ProductColorId)
	}
	// cond.AddTableNotEqual(s, "roll", 0)
	cond.AddTableNotEqual(s, "weight", 0)
	groupFields = []string{"fpm_quality_check.product_color_id", "fpm_quality_check.dyelot_number", "stock_product_detail.stock_product_id", "stock_product_detail.supplier_id"}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据缸号查询质检信息
func FindFpmQualityCheckByDyelot(tx *mysql_base.Tx, dyelotNumber []string) (o FpmQualityCheckList, err error) {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
		list []FpmQualityCheck
	)
	r.BuildReadCond(tx.Context, cond)
	if len(dyelotNumber) != 0 {
		cond.AddTableContainMatch(r, "dyelot_number", dyelotNumber)
	}
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

type FpmQualityCheckDyelotNumber struct {
	ProductId        uint64    `gorm:"column:product_id"  sqlf:"fpm_quality_check.product_id"  relate:"product_id"`                       // 成品id
	ProductColorId   uint64    `gorm:"column:product_color_id"  sqlf:"fpm_quality_check.product_color_id"  relate:"product_color_id"`     // 成品颜色id
	DefectWeight     int       `gorm:"column:defect_weight"  sqlf:"sum(fpm_quality_check.defect_weight)"`                                 // 次布数量KG
	ActuallyWeight   int       `gorm:"column:actually_weight"  sqlf:"sum(fpm_quality_check.actually_weight)"`                             // 实际数量
	DyelotNumber     string    `gorm:"column:dyelot_number"  sqlf:"fpm_quality_check.dyelot_number"`                                      // 缸号
	SumStockId       uint64    `gorm:"column:sum_stock_id"  sqlf:"fpm_quality_check.sum_stock_id" relate:"stock_product_id,sum_stock_id"` // 合计库存id
	SupplierId       uint64    `gorm:"column:supplier_id"  sqlf:"stock_product_detail.supplier_id"`                                       // 供应商id
	CustomerId       uint64    `gorm:"column:customer_id"  sqlf:"stock_product_detail.customer_id"`
	IsGenerateReport int       `gorm:"column:is_generate_report"  sqlf:"fpm_quality_check.is_generate_report"` // 是否生成报表
	WarehouseInTime  time.Time `gorm:"column:warehouse_in_time" sqlf:"stock_product_detail.warehouse_in_time"` // 入仓时间
}

type FpmQualityCheckDyelotNumberList []FpmQualityCheckDyelotNumber

func UpdateFpmQualityCheckStatusByIds(tx *mysql_base.Tx, ids []uint64) error {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
	)
	if len(ids) == 0 {
		return nil
	}
	cond.AddContainMatchToOR("id", ids)
	err := mysql_base.MustUpdateModelByCond(tx, r, cond, map[string]interface{}{"is_generate_report": consts.GenerateReportStatusIsGen})
	if err != nil {
		return err
	}
	return nil
}

// UpdateFpmQualityCheckIsGenerateReportByParentIds 更新质检项的 is_generate_report 字段
func UpdateFpmQualityCheckIsGenerateReportByParentIds(tx *mysql_base.Tx, Ids []uint64, status consts.GenerateReportStatus) error {
	var (
		r    FpmQualityCheck
		cond = mysql_base.NewCondition()
	)
	r.BuildReadCond(tx.Context, cond)
	if len(Ids) == 0 {
		return nil
	}
	cond.AddContainMatch("id", Ids)
	err := mysql_base.MustUpdateModelByCond(tx, r, cond, map[string]interface{}{"is_generate_report": status})
	if err != nil {
		return err
	}
	return nil
}
