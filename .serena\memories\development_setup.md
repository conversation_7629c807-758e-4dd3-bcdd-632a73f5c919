# HCSCM 开发环境设置

## 前置要求

### 必需工具
1. **Go 1.23.0+** - 编程语言运行时
2. **Git** - 版本控制
3. **Docker** - 容器化服务
4. **IDE/编辑器** - 推荐VS Code或GoLand

### 开发工具
1. **swag** - Swagger文档生成工具
   ```bash
   go install github.com/swaggo/swag/cmd/swag@latest
   ```

2. **generate** - 枚举_map.go文件生成工具
   - 从 https://git.online.zzfzyc.com/go/ast.git 下载

3. **stringer** - 枚举_string.go文件生成工具
   - 从 https://git.online.zzfzyc.com/go/ast.git 下载

## 数据库设置

### MySQL (MariaDB)
```bash
docker run -itd --name mariadb -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password mariadb
```
- 创建数据库: `hcscm`
- 用户名: `root`
- 密码: `password`
- 端口: `3306`

### Redis
```bash
docker run -itd --name redis -p 6379:6379 redis
```
- 端口: `6379`
- 无密码

### MongoDB (可选)
```bash
docker run -itd --name mongo -p 27017:27017 mongo
```
- 端口: `27017`
- 数据库名: `hcscm`

### RabbitMQ 延迟队列服务
```bash
git clone https://git.online.zzfzyc.com/go/rabbitmq_svc.git
cd ./rabbitmq_svc/LatestTag
docker-compose up -d
```

## 项目启动

### 1. 克隆项目
```bash
git clone <repository_url>
cd hcscm
```

### 2. 安装依赖
```bash
go mod download
go mod tidy
```

### 3. 配置文件
```bash
cp config.yaml config.local.yaml
# 根据本地环境修改配置
```

### 4. 启动应用
```bash
go run main.go config.local.yaml
```

## 开发流程

1. **创建功能分支**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **开发代码**
   - 遵循项目代码规范
   - 编写单元测试
   - 更新文档

3. **测试验证**
   ```bash
   go test ./...
   go build -o hcscm.exe ./main.go
   ```

4. **提交代码**
   ```bash
   git add .
   git commit -m "feat: add new feature"
   git push origin feature/your-feature-name
   ```

## 常见问题

### 端口冲突
- Web服务默认端口: 50001
- 可在config.yaml中修改

### 数据库连接问题
- 检查Docker容器是否正常运行
- 验证配置文件中的连接参数

### 依赖问题
- 运行 `go mod tidy` 解决依赖冲突
- 检查Go版本是否符合要求