package info_basic_data

import (
	"github.com/gin-gonic/gin"
	"hcscm/server/system"
	svc "hcscm/service/basic_data/info_basic_data"
	structure "hcscm/structure/basic_data/info_basic_data"
)

// @Tags 【销售出货类型】
// @Summary 添加销售出货类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param   body   body   structure.AddSaleShipmentTypeParam{}  true "创建SaleShipmentType"
// @Success 200 {object}  structure.AddSaleShipmentTypeData{}
// @Router /hcscm/admin/v1/info_basic_data/infoSaleShipmentType/addInfoSaleShipmentType [post]
func AddInfoSaleShipmentType(c *gin.Context) {
	var (
		q    = &structure.AddSaleShipmentTypeParam{}
		data = structure.AddSaleShipmentTypeData{}
		svc  = svc.NewInfoSaleShipmentTypeService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售出货类型】
// @Summary 删除销售出货类型
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param   body   body   structure.DeleteInfoBasicDefectParam{}  true "创建InfoBasicDefect"
// @Success 200 {object}  structure.DeleteInfoBasicDefectData{}
// @Router /hcscm/admin/v1/info_basic_data/infoBasicDefect [delete]
func DeleteInfoSaleShipmentType(c *gin.Context) {
	var (
		q    = &structure.DeleteSaleShipmentTypeParam{}
		data = structure.DeleteSaleShipmentTypeData{}
		svc  = svc.NewInfoSaleShipmentTypeService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Delete(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【疵点】
// @Summary 更新疵点
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param   body   body   structure.UpdateInfoBasicDefectParam{}  true "创建InfoBasicDefect"
// @Success 200 {object}  structure.UpdateInfoBasicDefectData{}
// @Router /hcscm/admin/v1/info_basic_data/infoBasicDefect [put]
func UpdateInfoSaleShipmentType(c *gin.Context) {
	var (
		q    = &structure.UpdateSaleShipmentTypeParam{}
		data = structure.UpdateSaleShipmentTypeData{}
		svc  = svc.NewInfoSaleShipmentTypeService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【疵点】
// @Summary 获取疵点
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetInfoBasicDefectData{}
// @Router /hcscm/admin/v1/info_basic_data/infoBasicDefect [get]
func GetInfoSaleShipmentType(c *gin.Context) {
	var (
		q    = &structure.GetSaleShipmentTypeQuery{}
		data = structure.GetSaleShipmentTypeData{}
		svc  = svc.NewInfoSaleShipmentTypeService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【疵点】
// @Summary 获取疵点列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  	  false  "page"
// @Param     size      query     int	  false  "size"
// @Param     offset    query     int 	  false  "offset"
// @Param     limit     query     int  	  false  "limit"
// @Param     status    query     int 	  false  "状态"
// @Param     name      query     string  false  "名称"
// @Param     download  query     int 	  false  "download"
// @Param     Platform  header    int	  true   "终端ID"
// @Success 200 {object}  structure.GetInfoBasicDefectDataList{}
// @Router /hcscm/admin/v1/info_basic_data/infoBasicDefect/list [get]
func GetInfoSaleShipmentTypeList(c *gin.Context) {
	var (
		q     = &structure.GetSaleShipmentTypeListQuery{}
		list  = make(structure.GetSaleShipmentDataList, 0)
		total int
		err   error
		svc   = svc.NewInfoSaleShipmentTypeService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponseV2(c, q, "销售出货类型", err, list, nil, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}
