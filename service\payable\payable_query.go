package payable

import (
	"context"
	"hcscm/aggs/payable"
	payable_repo "hcscm/aggs/payable2"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	info_basic_pb "hcscm/extern/pb/basic_data/info_basic_data"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	"hcscm/extern/pb/employee"
	"hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/user"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/payable"
	"hcscm/structure/payable2"
	structure "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/set"
)

type PayableQueryService struct {
}

func NewPayableQueryService() *PayableQueryService {
	return &PayableQueryService{}
}

// QueryPayableList 查询列表
func (s *PayableQueryService) QueryPayableList(ctx context.Context, tx *mysql_base.Tx, p *payable2.GetPayableListParams) (*payable2.GetPayableListResponse, int, error) {
	var (
		repo            = payable.NewPayableRepo(tx)
		payableItemList []*mysql.PayableItem
	)

	// 查询列表
	list, total, err := repo.QueryPayableList(ctx, tx, p)
	if err != nil {
		return nil, 0, err
	}
	if total == 0 {
		return nil, 0, nil
	}

	payableItemList, err = repo.QueryPayableItemsByOrderIds(ctx, mysql_base.GetUInt64List(list, "payable_id"))
	if err != nil {
		return nil, 0, err
	}

	saleSystemIds := set.NewUint64Set()
	bizUnitIds := set.NewUint64Set()
	userIds := set.NewUint64Set()
	for _, item := range list {
		saleSystemIds.Add(item.SaleSystemId)
		bizUnitIds.Add(item.SupplierId)
		userIds.Add(item.HandlerId)
	}

	saleSystemMap := make(map[uint64]string)
	bizUnitMap := make(map[uint64]string)
	employeeMap := make(map[uint64]string)

	g := errgroup.WithCancel(ctx)

	// 查询营销系统
	g.Go(func(ctx context.Context) error {
		var err error
		saleSystemService := sale_system.NewSaleSystemClient()
		saleSystemMap, err = saleSystemService.GetSaleSystemByIds(ctx, saleSystemIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	// 查询供方
	g.Go(func(ctx context.Context) error {
		var err error
		bizUnitService := biz_unit.NewClientBizUnitService()
		bizUnitMap, err = bizUnitService.GetBizUnitNameByIds(ctx, bizUnitIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	// 查询用户
	g.Go(func(ctx context.Context) error {
		var err error
		employeeService := employee.NewClientEmployeeService()
		employeeMap, err = employeeService.GetEmployeeNameByIds(ctx, userIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	var rsp payable2.GetPayableListResponse = make([]*payable2.GetPayableListItem, 0, len(list))
	for _, item := range list {
		var (
			weight int
		)
		for _, payableItem := range payableItemList {
			if payableItem.OrderId == item.Id {
				weight += payableItem.Weight
			}
		}
		tmp := &payable2.GetPayableListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			Settle: payable2.Settle{
				TotalPrice:    item.TotalPrice,
				DiscountPrice: item.DiscountPrice,
				ReducePrice:   item.ReducePrice,
				PaidPrice:     item.PaidPrice,
				UnpaidPrice:   item.UnpaidPrice,
				OffsetPrice:   item.OffsetPrice,
				Weight:        weight,
			},
			OrderNo:          item.OrderNo,
			SrcOrderType:     item.SrcOrderType,
			SrcOrderTypeName: item.SrcOrderType.String(),
			SrcOrderNo:       item.SrcOrderNo,
			SrcOrderId:       item.SrcOrderId,
			SaleSystemId:     item.SaleSystemId,
			SaleSystemName:   saleSystemMap[item.SaleSystemId],
			SupplierId:       item.SupplierId,
			SupplierName:     bizUnitMap[item.SupplierId],
			PayDate:          tools.MyTime(item.PayDate).Date(),
			HandlerId:        item.HandlerId,
			HandlerName:      employeeMap[item.HandlerId],
			VoucherNum:       item.VoucherNum,
			Remark:           item.Remark,
			Status:           item.AuditStatus,
			StatusName:       item.AuditStatus.String(),
			AuditTime:        tools.MyTime(item.AuditDate),
			AuditorId:        item.AuditorId,
			AuditorName:      item.AuditorName,
			SaleMode:         item.SaleMode,
			SaleModeName:     item.SaleMode.String(),
		}
		rsp = append(rsp, tmp)
	}

	return &rsp, total, nil
}

// QueryPayableDetail 查询详情
func (s *PayableQueryService) QueryPayableDetail(ctx context.Context, tx *mysql_base.Tx, id uint64) (*payable2.QueryPayableDetailDTOResponse, error) {
	var (
		repo          = payable.NewPayableRepo(tx)
		arrearsAmount int
	)
	// 查订单
	order, err := repo.QueryPayable(ctx, id)
	if err != nil {
		return nil, err
	}
	if order == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return nil, err
	}

	// 查成品信息
	orderItems, err := repo.QueryPayableItems(ctx, order.Id)
	if err != nil {
		return nil, err
	}

	// 查细码
	orderItemFcs, err := repo.QueryPayableItemFcsByPayableIDs(ctx, mysql_base.GetUInt64List(order, "payable_id"))
	if err != nil {
		return nil, err
	}

	saleSystemMap := make(map[uint64]string)
	bizUnitMap := make(map[uint64]string)
	employeeMap := make(map[uint64]string)
	dicNameMap := make(map[uint64][2]string)
	munitMap := make(map[uint64]string)
	binName := make(map[uint64]string)

	g := errgroup.WithCancel(ctx)

	// 查询营销系统
	g.Go(func(ctx context.Context) error {
		var err error
		saleSystemService := sale_system.NewSaleSystemClient()
		saleSystemMap, err = saleSystemService.GetSaleSystemByIds(ctx, []uint64{order.SaleSystemId})
		if err != nil {
			return err
		}
		return nil
	})

	// 查询供方
	g.Go(func(ctx context.Context) error {
		var err error
		bizUnitService := biz_unit.NewClientBizUnitService()
		bizUnitMap, err = bizUnitService.GetBizUnitNameByIds(ctx, []uint64{order.SupplierId})
		if err != nil {
			return err
		}
		return nil
	})

	// 查询用户
	g.Go(func(ctx context.Context) error {
		var err error
		employeeService := employee.NewClientEmployeeService()
		employeeMap, err = employeeService.GetEmployeeNameByIds(ctx, []uint64{order.HandlerId})
		if err != nil {
			return err
		}
		return nil
	})

	// 幅宽克重
	g.Go(func(ctx context.Context) error {
		dicIds := mysql_base.GetUInt64List(orderItems, "dictionary_detail_id")
		dicSvc := dictionary.NewDictionaryClient()
		dicNameMap, _ = dicSvc.GetDictionaryNameByIds(ctx, dicIds)
		return nil
	})

	// 计量单位
	g.Go(func(ctx context.Context) error {
		munitIds := mysql_base.GetUInt64List(orderItems, "measurement_unit_id")
		munitMap, _ = info_basic_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, munitIds)
		return nil
	})

	// 查仓位
	g.Go(func(ctx context.Context) error {
		binIds := mysql_base.GetUInt64List(orderItemFcs, "warehouse_bin_id")
		binName, _ = warehouse_pb.NewPhysicalWarehouseClient().GetPhysicalWarehouseBinNameByIds(ctx, binIds)
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, err
	}

	// 统计累计欠款
	list, count, err := mysql.FindSupplierOweMoneyList(tx, &payable2.GetSupplierOweMoneyListQuery{SupplierId: order.SupplierId}, false)
	if err != nil {
		return nil, err
	}
	if count != 0 {
		customerOweMoney := list.List()[0]
		arrearsAmount += customerOweMoney.BalancePrice - customerOweMoney.BalanceAdvancePrice
	}

	var items = make([]*payable2.PayableItem, 0)
	for _, item := range orderItems {
		o := &payable2.PayableItem{}
		o.Id = item.Id
		o.OrderId = item.OrderId
		o.SrcId = item.SrcId
		o.Roll = item.Roll
		o.Length = item.Length
		o.Weight = item.Weight
		o.UnitPrice = item.UnitPrice
		o.FinishingUnitPrice = item.FinishingUnitPrice
		o.PaperTubeUnitPrice = item.PaperTubeUnitPrice
		o.PlasticBagUnitPrice = item.PlasticBagUnitPrice
		o.OtherPrice = item.OtherPrice
		o.Date = item.Date
		o.OrderType = item.OrderType
		// o.SaleMode = item.SaleMode
		o.ProjectNo = item.ProjectNo
		o.ProjectName = item.ProjectName
		o.VoucherNum = item.VoucherNum
		o.Price = item.Price
		o.SumStockId = item.SumStockId
		o.MaterialId = item.MaterialId
		o.ColorId = item.ColorId
		o.DyeFactoryColorCode = item.DyeFactoryColorCode
		o.DyelotNumber = item.DyelotNumber
		o.MeasurementUnitId = item.MeasurementUnitId
		o.MeasurementUnitName = munitMap[item.MeasurementUnitId]
		o.AuxiliaryUnitId = item.AuxiliaryUnitId
		o.AuxiliaryUnitName = munitMap[item.AuxiliaryUnitId]
		o.Width = item.Width
		o.GramWeight = item.GramWeight
		o.WidthUnitId = item.WidthUnitId
		o.WidthUnitName = dicNameMap[o.WidthUnitId][1]
		o.GramWeightUnitId = item.GramWeightUnitId
		o.GramWeightUnitName = dicNameMap[o.GramWeightUnitId][1]
		o.YarnBatch = item.YarnBatch
		o.Brand = item.Brand
		o.BatchNum = item.BatchNum
		o.Remark = item.Remark
		o.BuoyantWeightPrice = item.BuoyantWeightPrice
		o.NetWeightPrice = item.NetWeightPrice
		o.ReceiveWeight = item.ReceiveWeight
		o.PTWeightAndWeightError = item.PTWeightAndWeightError
		o.ShouldPayWeight = item.ShouldPayWeight
		o.SettleWeight = item.Weight
		for _, fc := range orderItemFcs {
			if fc.PayableItemId == item.Id {
				o.FcDataList = append(o.FcDataList, payable2.GetProductPurOrderItemFcData{
					RecordData:        structure.RecordData{Id: fc.Id},
					ParentId:          fc.PayableItemId,
					Roll:              fc.Roll,
					SumStockId:        fc.SumStockId,
					BaseUnitWeight:    fc.BaseUnitWeight,
					PaperTubeWeight:   fc.PaperTubeWeight,
					WeightError:       fc.WeightError,
					ActuallyWeight:    fc.ActuallyWeight,
					SettleErrorWeight: fc.SettleErrorWeight,
					UnitId:            fc.UnitId,
					UnitName:          munitMap[fc.UnitId],
					Length:            fc.Length,
					SettleWeight:      fc.SettleWeight,
					VolumeNumber:      fc.VolumeNumber,
					WarehouseBinId:    fc.WarehouseBinId,
					WarehouseBinName:  binName[fc.WarehouseBinId],
				})
				o.SettleErrorWeight += fc.SettleErrorWeight
			}
		}
		items = append(items, o)
	}

	dtoRsp := &payable2.QueryPayableDetailDTOResponse{
		RecordData: structure.RecordData{
			Id:             order.Id,
			CreatorId:      order.CreatorId,
			CreatorName:    order.CreatorName,
			CreateTime:     tools.MyTime(order.CreateTime),
			UpdaterId:      order.UpdaterId,
			UpdateUserName: order.UpdaterName,
			UpdateTime:     tools.MyTime(order.UpdateTime),
		},
		TotalPrice:       order.TotalPrice,
		DiscountPrice:    order.DiscountPrice,
		ReducePrice:      order.ReducePrice,
		OffsetPrice:      order.OffsetPrice,
		ArrearsAmount:    arrearsAmount,
		PaidPrice:        order.PaidPrice,
		UnpaidPrice:      order.UnpaidPrice,
		OrderNo:          order.OrderNo,
		SrcOrderType:     order.SrcOrderType,
		SrcOrderTypeName: order.SrcOrderType.String(),
		SrcOrderNo:       order.SrcOrderNo,
		SrcOrderId:       order.SrcOrderId,
		SaleSystemId:     order.SaleSystemId,
		SaleSystemName:   saleSystemMap[order.SaleSystemId],
		SupplierId:       order.SupplierId,
		SupplierName:     bizUnitMap[order.SupplierId],
		PayDate:          tools.MyTime(order.PayDate).Date(),
		HandlerId:        order.HandlerId,
		HandlerName:      employeeMap[order.HandlerId],
		VoucherNum:       order.VoucherNum,
		Remark:           order.Remark,
		Status:           order.AuditStatus,
		StatusName:       order.AuditStatus.String(),
		AuditTime:        tools.MyTime(order.AuditDate),
		AuditorId:        order.AuditorId,
		AuditorName:      order.AuditorName,
		Items:            items,
		OrderType:        uint8(order.OrderType),
		OrderTypeName:    order.OrderType.String(),
		SaleMode:         order.SaleMode,
		SaleModeName:     order.SaleMode.String(),
	}
	return dtoRsp, nil
}

func buildPayableDetailBaseInfo(ctx context.Context, order *payable2.QueryPayableDetailDTOResponse) payable2.PayableDetailBaseInfo {
	var (
		saleSystemMap    map[uint64]string
		bizMap           map[uint64]string
		employeeMap      map[uint64]string
		TotalOffSetPrice int
	)
	g := errgroup.WithCancel(ctx)

	g.Go(func(ctx context.Context) error {
		saleSystemMap, _ = sale_system.NewSaleSystemClient().GetSaleSystemByIds(ctx, []uint64{order.SaleSystemId})
		return nil
	})
	g.Go(func(ctx context.Context) error {
		bizMap, _ = biz_unit.NewClientBizUnitService().GetBizUnitNameByIds(ctx, []uint64{order.SupplierId})
		return nil
	})
	g.Go(func(ctx context.Context) error {
		employeeMap, _ = employee.NewClientEmployeeService().GetEmployeeNameByIds(ctx, []uint64{order.HandlerId})
		return nil
	})

	if err := g.Wait(); err != nil {
		return payable2.PayableDetailBaseInfo{}
	}
	for _, i := range order.Items {
		for _, fc := range i.FcDataList {
			TotalOffSetPrice += i.UnitPrice * fc.SettleErrorWeight
		}
	}
	baseInfo := payable2.PayableDetailBaseInfo{
		PayableBaseInfo: payable2.PayableBaseInfo{
			SrcOrderType: order.SrcOrderType,
			SrcOrderId:   order.SrcOrderId,
			SrcOrderNo:   order.SrcOrderNo,
			SaleSystemId: order.SaleSystemId,
			SupplierId:   order.SupplierId,
			HandlerId:    order.HandlerId,
			VoucherNum:   order.VoucherNum,
			PayDate:      tools.QueryTime(order.PayDate),
			Remark:       order.Remark,
			OrderType:    order.OrderType,
			SaleMode:     order.SaleMode,
		},
		Settle: payable2.Settle{
			TotalPrice:    order.TotalPrice - TotalOffSetPrice/1000000,
			DiscountPrice: order.DiscountPrice,
			ReducePrice:   order.ReducePrice,
			PaidPrice:     order.PaidPrice,
			UnpaidPrice:   order.UnpaidPrice - TotalOffSetPrice/1000000,
			OffsetPrice:   order.OffsetPrice,
			ArrearsAmount: order.ArrearsAmount,
		},
		OrderNo:          order.OrderNo,
		Status:           order.Status,
		StatusName:       order.Status.String(),
		SrcOrderTypeName: order.SrcOrderType.String(),
		SaleSystemName:   saleSystemMap[order.SaleSystemId],
		SupplierName:     bizMap[order.SupplierId],
		HandlerName:      employeeMap[order.HandlerId],
		OrderTypeName:    order.OrderTypeName,
		AuditDate:        order.AuditTime,
		AuditorId:        order.AuditorId,
		AuditorName:      order.AuditorName,
		SaleModeName:     order.SaleModeName,
		SaleMode:         order.SaleMode,
	}
	return baseInfo
}

// QueryPayableListEnum 查询列表
func (s *PayableQueryService) QueryPayableListEnum(ctx context.Context, tx *mysql_base.Tx, p *payable2.GetPayableListParams) (*payable2.GetPayableListResponse, int, payable2.GetPayableOrderDropdownDataSummary, error) {
	var (
		repo         = payable.NewPayableRepo(tx)
		summary      payable2.GetPayableOrderDropdownDataSummary
		orderItemFcs []*mysql.PayableItemFc
		orderItems   []*mysql.PayableItem
		// orderItemFcsMap = make(map[uint64][]*mysql.PayableItemFc)
		totalWeightOffset = make(map[uint64]int)
		totalOffsetPrice  = make(map[uint64]int)
	)
	m, err := p.GetPayStatusMap()
	if err != nil {
		return nil, 0, summary, err
	}

	// 查询列表
	list, total, err := repo.QueryPayableListEnum(ctx, tx, p)
	if err != nil {
		return nil, 0, summary, err
	}
	if total == 0 {
		return nil, 0, summary, nil
	}

	saleSystemIds := set.NewUint64Set()
	bizUnitIds := set.NewUint64Set()
	employeeIds := set.NewUint64Set()
	for _, item := range list {
		saleSystemIds.Add(item.SaleSystemId)
		bizUnitIds.Add(item.SupplierId)
		employeeIds.Add(item.HandlerId)
	}

	saleSystemMap := make(map[uint64]string)
	bizUnitMap := make(map[uint64]string)
	employeeMap := make(map[uint64]string)

	g := errgroup.WithCancel(ctx)

	// 查询营销系统
	g.Go(func(ctx context.Context) error {
		var err error
		saleSystemService := sale_system.NewSaleSystemClient()
		saleSystemMap, err = saleSystemService.GetSaleSystemByIds(ctx, saleSystemIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	// 查询供方
	g.Go(func(ctx context.Context) error {
		var err error
		bizUnitService := biz_unit.NewClientBizUnitService()
		bizUnitMap, err = bizUnitService.GetBizUnitNameByIds(ctx, bizUnitIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	// 查询员工
	g.Go(func(ctx context.Context) error {
		var err error
		employeeService := employee.NewClientEmployeeService()
		employeeMap, err = employeeService.GetEmployeeNameByIds(ctx, employeeIds.List())
		if err != nil {
			return err
		}
		return nil
	})
	// 查询fc
	g.Go(func(ctx context.Context) error {
		payableIds := mysql_base.GetUInt64List(list, "payable_id")
		orderItemFcs, err = repo.QueryPayableItemFcsByPayableIDs(ctx, payableIds)
		if err != nil {
			return err
		}
		return nil
	})
	// 查询item
	g.Go(func(ctx context.Context) error {
		payableIds := mysql_base.GetUInt64List(list, "payable_id")
		orderItems, err = repo.QueryPayableItemsByOrderIds(ctx, payableIds)
		if err != nil {
			return err
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		return nil, 0, summary, err
	}
	for _, fc := range orderItemFcs {
		if _, ok := totalWeightOffset[fc.PayableItemId]; ok {
			totalWeightOffset[fc.PayableItemId] += fc.SettleErrorWeight
		} else {
			totalWeightOffset[fc.PayableItemId] = fc.SettleErrorWeight
		}
	}
	for _, i := range orderItems {
		if _, ok := totalOffsetPrice[i.OrderId]; ok {
			totalOffsetPrice[i.OrderId] += i.UnitPrice * totalWeightOffset[i.Id]
		} else {
			totalOffsetPrice[i.OrderId] = i.UnitPrice * totalWeightOffset[i.Id]
		}

	}
	var rsp payable2.GetPayableListResponse = make([]*payable2.GetPayableListItem, 0, len(list))
	for _, item := range list {
		payStatus := item.GetPayStatus()
		_, ok := m[payStatus]
		// 如果筛选条件选了付款状态，但是应付单的状态不在筛选条件中则跳过
		if p.PayStatus != "" && !ok {
			total--
			continue
		}
		tmp := &payable2.GetPayableListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			Settle: payable2.Settle{
				TotalPrice:    item.TotalPrice - totalOffsetPrice[item.Id]/1000000,
				DiscountPrice: item.DiscountPrice,
				ReducePrice:   item.ReducePrice,
				PaidPrice:     item.PaidPrice,
				UnpaidPrice:   item.UnpaidPrice,
				OffsetPrice:   item.OffsetPrice,
			},
			OrderType:        item.OrderType,
			OrderTypeName:    item.OrderType.String(),
			OrderNo:          item.OrderNo,
			SrcOrderType:     item.SrcOrderType,
			SrcOrderTypeName: item.SrcOrderType.String(),
			SrcOrderNo:       item.SrcOrderNo,
			SrcOrderId:       item.SrcOrderId,
			SaleSystemId:     item.SaleSystemId,
			SaleSystemName:   saleSystemMap[item.SaleSystemId],
			SupplierId:       item.SupplierId,
			SupplierName:     bizUnitMap[item.SupplierId],
			PayDate:          tools.MyTime(item.PayDate).Date(),
			HandlerId:        item.HandlerId,
			HandlerName:      employeeMap[item.HandlerId],
			VoucherNum:       item.VoucherNum,
			Remark:           item.Remark,
			Status:           item.AuditStatus,
			StatusName:       item.AuditStatus.String(),
			AuditTime:        tools.MyTime(item.AuditDate),
			AuditorId:        item.AuditorId,
			AuditorName:      item.AuditorName,
			PayStatus:        payStatus,
			PayStatusName:    payStatus.ToString(),
			OrderTime:        tools.MyTime(item.PayDate),
			SaleMode:         item.SaleMode,
			SaleModeName:     item.SaleMode.String(),
		}
		summary.TotalPrice += tmp.Settle.TotalPrice
		tmp.Paths = payable_repo.GetPayOrderPaths(tmp.OrderType)
		rsp = append(rsp, tmp)
	}

	return &rsp, total, summary, nil
}

func (s *PayableQueryService) GetAutoWriteOffList(ctx context.Context, p *payable2.GetPayableList) (*payable2.GetPayableListResponse, error) {

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	repo := payable.NewPayableOrderRepo(tx)

	// 查询列表
	list, writeOffMoney, err := repo.GetAutoWriteOffList(ctx, p)
	if err != nil {
		return nil, err
	}

	saleSystemIds := set.NewUint64Set()
	bizUnitIds := set.NewUint64Set()
	userIds := set.NewUint64Set()
	for _, item := range list {
		saleSystemIds.Add(item.SaleSystemId)
		bizUnitIds.Add(item.SupplierId)
		userIds.Add(item.AuditorId)
	}

	saleSystemMap := make(map[uint64]string)
	bizUnitMap := make(map[uint64]string)
	userMap := make(map[uint64]string)

	g := errgroup.WithCancel(ctx)

	// 查询营销系统
	g.Go(func(ctx context.Context) error {
		var err error
		saleSystemService := sale_system.NewSaleSystemClient()
		saleSystemMap, err = saleSystemService.GetSaleSystemByIds(ctx, saleSystemIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	// 查询供方
	g.Go(func(ctx context.Context) error {
		var err error
		bizUnitService := biz_unit.NewClientBizUnitService()
		bizUnitMap, err = bizUnitService.GetBizUnitNameByIds(ctx, bizUnitIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	// 查询用户
	g.Go(func(ctx context.Context) error {
		var err error
		userService := user.NewUserClient()
		userMap, err = userService.GetUserNameByIds(ctx, userIds.List())
		if err != nil {
			return err
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, err
	}

	var rsp payable2.GetPayableListResponse = make([]*payable2.GetPayableListItem, 0, len(list))
	for _, item := range list {
		tmp := &payable2.GetPayableListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			Settle: payable2.Settle{
				TotalPrice:    item.TotalPrice,
				DiscountPrice: item.DiscountPrice,
				ReducePrice:   item.ReducePrice,
				PaidPrice:     item.PaidPrice,
				UnpaidPrice:   item.UnpaidPrice,
				OffsetPrice:   item.OffsetPrice,
			},
			OrderType:         item.OrderType,
			OrderTypeName:     item.OrderType.String(),
			OrderNo:           item.OrderNo,
			SrcOrderType:      item.SrcOrderType,
			SrcOrderTypeName:  item.SrcOrderType.String(),
			SrcOrderNo:        item.SrcOrderNo,
			SrcOrderId:        item.SrcOrderId,
			SaleSystemId:      item.SaleSystemId,
			SaleSystemName:    saleSystemMap[item.SaleSystemId],
			SupplierId:        item.SupplierId,
			SupplierName:      bizUnitMap[item.SupplierId],
			PayDate:           tools.MyTime(item.PayDate).Date(),
			HandlerId:         item.HandlerId,
			HandlerName:       userMap[item.HandlerId],
			VoucherNum:        item.VoucherNum,
			Remark:            item.Remark,
			Status:            item.AuditStatus,
			StatusName:        item.AuditStatus.String(),
			AuditTime:         tools.MyTime(item.AuditDate),
			AuditorId:         item.AuditorId,
			AuditorName:       item.AuditorName,
			AutoWriteOffMoney: writeOffMoney[item.Id],
		}
		rsp = append(rsp, tmp)
	}

	return &rsp, nil
}

// QueryPayableBySrcOrderId 查询应付单基础信息（根据来源单号）
func (s *PayableQueryService) QueryPayableBySrcOrderId(ctx context.Context, tx *mysql_base.Tx, srcOrderId uint64) (order mysql.Payable, exist bool, err error) {
	var repo = payable.NewPayableRepo(tx)
	// 查订单
	order, exist, err = repo.QueryPayableBySrcOrderId(ctx, srcOrderId)
	if err != nil {
		return
	}
	return
}

func (s *PayableQueryService) GetPayableIsPassBySrcOrderId(ctx context.Context, tx *mysql_base.Tx, srcOrderId uint64) (bool, error) {
	payableOrder, exist, err := s.QueryPayableBySrcOrderId(ctx, tx, srcOrderId)
	if err != nil {
		return false, err
	}
	if exist {
		return payableOrder.AuditStatus == common_system.OrderStatusAudited, nil
	}
	return false, nil
}
