package product

import (
	"fmt"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/middleware"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddProductCheckOrderParam struct {
	structure_base.Param
	SaleSystemId       uint64                            `json:"sale_system_id" mean:"营销体系ID" validates:"required"` // 营销体系ID
	WarehouseId        uint64                            `json:"warehouse_id" mean:"仓库id" validates:"required"`     // 仓库id
	WarehouseBinId     uint64                            `json:"warehouse_bin_id"`                                  // 仓位id
	CheckTime          tools.QueryTime                   `json:"check_time" mean:"盘点日期" validates:"required"`       // 盘点日期
	WarehouseManagerId uint64                            `json:"warehouse_manager_id"`                              // 仓管员ID
	DepartmentId       uint64                            `json:"department_id"`                                     // 下单用户所属部门
	CompanyId          uint64                            `json:"company_id"`                                        // 公司ID
	Remark             string                            `json:"remark" mean:"备注" validates:"sizeMax=5"`            // 备注
	ItemData           AddProductCheckOrderItemParamList `json:"item_data"`                                         // 盘点成品信息
}

type AddProductCheckOrderParamList []AddProductCheckOrderParam

func (r AddProductCheckOrderParamList) Adjust() {

}

type AddProductCheckOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateProductCheckOrderParam struct {
	structure_base.Param
	Id                 uint64                            `json:"id"`
	SaleSystemId       uint64                            `json:"sale_system_id"`       // 营销体系ID
	WarehouseId        uint64                            `json:"warehouse_id"`         // 仓库id
	WarehouseBinId     uint64                            `json:"warehouse_bin_id"`     // 仓位id
	CheckTime          tools.QueryTime                   `json:"check_time"`           // 盘点日期
	WarehouseManagerId uint64                            `json:"warehouse_manager_id"` // 仓管员ID
	DepartmentId       uint64                            `json:"department_id"`        // 下单用户所属部门
	CompanyId          uint64                            `json:"company_id"`           // 公司ID
	Remark             string                            `json:"remark"`               // 备注
	ItemData           AddProductCheckOrderItemParamList `json:"item_data"`            // 盘点成品信息
}

type UpdateProductCheckOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateProductCheckOrderAuditStatusParam struct {
	structure_base.Param
	Id uint64 `json:"id"`
}

type UpdateProductCheckOrderAuditStatusData struct {
	structure_base.ResponseData
	CheckTime tools.MyTime `json:"check_time"` // 盘点时间
}

type UpdateProductCheckOrderBusinessCloseParam struct {
	structure_base.Param
	Id            uint64                      `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateProductCheckOrderBusinessCloseData struct {
	structure_base.ResponseData
}

type GetProductCheckOrderQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetProductCheckOrderListQuery struct {
	structure_base.ListQuery
	ProductCheckOrderId uint64                      `form:"product_check_order_id"` // 成品盘点单ID
	SaleSystemId        uint64                      `form:"sale_system_id"`         // 营销体系ID
	WarehouseId         uint64                      `form:"warehouse_id"`           // 仓库id
	WarehouseBinId      uint64                      `form:"warehouse_bin_id"`       // 仓位id
	StartCheckTime      tools.QueryTime             `form:"start_check_time"`       // 盘点日期
	EndCheckTime        tools.QueryTime             `form:"end_check_time"`         // 盘点日期
	WarehouseManagerId  uint64                      `form:"warehouse_manager_id"`   // 仓管员ID
	OrderNo             string                      `form:"order_no"`               // 生产变更单号
	AuditStatus         tools.QueryIntList          `form:"audit_status"`           // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId        uint64                      `form:"department_id"`          // 下单用户所属部门
	CompanyId           uint64                      `form:"company_id"`             // 公司ID
	AuditorId           uint64                      `form:"auditor_id"`             // 审核人ID
	StartAuditDate      tools.QueryTime             `form:"start_audit_date"`       // 开始审核时间
	EndAuditDate        tools.QueryTime             `form:"end_audit_date"`         // 结束审核时间
	BusinessClose       common_system.BusinessClose `form:"business_close"`         // 业务关闭
	BusinessCloseUserId uint64                      `form:"business_close_user_id"` // 业务关闭操作人
	BusinessCloseTime   tools.QueryTime             `form:"business_close_time"`    // 业务关闭时间
	Remark              string                      `form:"remark"`                 // 备注
}

func (r GetProductCheckOrderListQuery) Adjust() {

}

type GetProductCheckOrderData struct {
	structure_base.RecordData
	SaleSystemId                uint64                      `json:"sale_system_id"`                  // 营销体系ID
	SaleSystemName              string                      `json:"sale_system_name"`                // 营销体系ID名称
	WarehouseId                 uint64                      `json:"warehouse_id"`                    // 仓库id
	WarehouseName               string                      `json:"warehouse_name"`                  // 仓库id名称
	WarehouseBinId              uint64                      `json:"warehouse_bin_id"`                // 仓位id
	WarehouseBinName            string                      `json:"warehouse_bin_name"`              // 仓位名称
	CheckTime                   tools.MyTime                `json:"check_time"`                      // 盘点日期
	WarehouseManagerId          uint64                      `json:"warehouse_manager_id"`            // 仓管员ID
	WarehouseManagerName        string                      `json:"warehouse_manager_name"`          // 仓管员ID名称
	OrderNo                     string                      `json:"order_no"`                        // 生产变更单号
	AuditStatus                 common_system.OrderStatus   `json:"audit_status"`                    // 订单状态 1待审核 2已审核 3已驳回 4已作废
	AuditStatusName             string                      `json:"audit_status_name"`               // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId                uint64                      `json:"department_id"`                   // 下单用户所属部门
	DepartmentName              string                      `json:"department_name"`                 // 下单用户所属部门名称
	CompanyId                   uint64                      `json:"company_id"`                      // 公司ID
	CompanyName                 string                      `json:"company_name"`                    // 公司ID名称
	AuditorId                   uint64                      `json:"auditor_id"`                      // 审核人ID
	AuditorName                 string                      `json:"auditor_name"`                    // 审核人ID名称
	AuditDate                   tools.MyTime                `json:"audit_date"`                      // 审核时间
	BusinessClose               common_system.BusinessClose `json:"business_close"`                  // 业务关闭
	BusinessCloseUserId         uint64                      `json:"business_close_user_id"`          // 业务关闭操作人
	BusinessCloseUserName       string                      `json:"business_close_user_name"`        // 业务关闭操作人名称
	BusinessCloseTime           tools.MyTime                `json:"business_close_time"`             // 业务关闭时间
	Remark                      string                      `json:"remark"`                          // 备注
	WarehouseBinCheckBeforeRoll int                         `json:"warehouse_bin_check_before_roll"` // 本架盘前匹数（本仓位）
	WarehouseBinCheckRoll       int                         `json:"warehouse_bin_check_roll"`        // 本架实盘匹数（本仓位）
}

type GetProductCheckOrderDataList []GetProductCheckOrderData

func (g GetProductCheckOrderDataList) Adjust() {

}

type GetProductCheckOrderDetailData struct {
	structure_base.RecordData
	SaleSystemId          uint64                           `json:"sale_system_id"`           // 营销体系ID
	SaleSystemName        string                           `json:"sale_system_name"`         // 营销体系ID名称
	WarehouseId           uint64                           `json:"warehouse_id"`             // 仓库id
	WarehouseName         string                           `json:"warehouse_name"`           // 仓库id名称
	WarehouseBinId        uint64                           `json:"warehouse_bin_id"`         // 仓位id
	WarehouseBinName      string                           `json:"warehouse_bin_name"`       // 仓位id名称
	CheckTime             tools.MyTime                     `json:"check_time"`               // 盘点日期
	WarehouseManagerId    uint64                           `json:"warehouse_manager_id"`     // 仓管员ID
	WarehouseManagerName  string                           `json:"warehouse_manager_name"`   // 仓管员ID名称
	OrderNo               string                           `json:"order_no"`                 // 生产变更单号
	AuditStatus           common_system.OrderStatus        `json:"audit_status"`             // 订单状态 1待审核 2已审核 3已驳回 4已作废
	AuditStatusName       string                           `json:"audit_status_name"`        // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId          uint64                           `json:"department_id"`            // 下单用户所属部门
	DepartmentName        string                           `json:"department_name"`          // 下单用户所属部门名称
	CompanyId             uint64                           `json:"company_id"`               // 公司ID
	CompanyName           string                           `json:"company_name"`             // 公司ID名称
	AuditorId             uint64                           `json:"auditor_id"`               // 审核人ID
	AuditorName           string                           `json:"auditor_name"`             // 审核人ID名称
	AuditDate             tools.MyTime                     `json:"audit_date"`               // 审核时间
	BusinessClose         common_system.BusinessClose      `json:"business_close"`           // 业务关闭
	BusinessCloseUserId   uint64                           `json:"business_close_user_id"`   // 业务关闭操作人
	BusinessCloseUserName string                           `json:"business_close_user_name"` // 业务关闭操作人名称
	BusinessCloseTime     tools.MyTime                     `json:"business_close_time"`      // 业务关闭时间
	Remark                string                           `json:"remark"`                   // 备注
	ItemData              GetProductCheckOrderItemDataList `json:"item_data"`                // 盘点成品信息
}

type GetProductCheckOrderDetailDataList []GetProductCheckOrderDetailData

func (g GetProductCheckOrderDetailDataList) Adjust() {

}

func (param *AddProductCheckOrderParam) CheckVNumber() error {
	volumeVatMap := make(map[string]bool)
	for _, item := range param.ItemData {
		for _, fc := range item.ItemData {
			if fc.VolumeNumber > 0 {
				key := fmt.Sprintf("%s:%d", item.DyelotNumber, fc.VolumeNumber)
				if _, exists := volumeVatMap[key]; exists {
					return middleware.ErrorLog(errors.NewError(errors.ErrorVolumeNumberRepeat))
				}
				volumeVatMap[key] = true
			}
		}
	}

	return nil
}

func (param *UpdateProductCheckOrderParam) CheckVNumber() error {
	volumeVatMap := make(map[string]bool)
	for _, item := range param.ItemData {
		for _, fc := range item.ItemData {
			if fc.VolumeNumber > 0 {
				key := fmt.Sprintf("%s:%d", item.DyelotNumber, fc.VolumeNumber)
				if _, exists := volumeVatMap[key]; exists {
					return middleware.ErrorLog(errors.NewError(errors.ErrorVolumeNumberRepeat))
				}
				volumeVatMap[key] = true
			}
		}
	}

	return nil
}
