package sale_price

import (
	"context"
	"fmt"
	"hcscm/common/errors"
	common "hcscm/common/sale_price"
	product2 "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/department"
	"hcscm/extern/pb/employee"
	"hcscm/extern/pb/sale_price"
	"hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/should_collect_order"
	"hcscm/extern/pb/user"
	"hcscm/log"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/sale_price"
	mysql "hcscm/model/mysql/sale_price/dao"
	productStructure "hcscm/structure/product"
	structure "hcscm/structure/sale_price"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"time"
)

type CustomerSalePriceAdjustRepo struct {
	tx *mysql_base.Tx
}

func NewCustomerSalePriceAdjustRepo(tx *mysql_base.Tx) *CustomerSalePriceAdjustRepo {
	return &CustomerSalePriceAdjustRepo{tx: tx}
}

func (r *CustomerSalePriceAdjustRepo) Add(ctx context.Context, req *structure.AddCustomerSalePriceAdjustParam) (err error) {
	for _, itemReq := range req.CustomerItems {
		customerSalePriceAdjust := model.NewCustomerSalePriceAdjust(ctx, req, &itemReq)
		customerSalePriceAdjust, err = mysql.MustCreateCustomerSalePriceAdjust(r.tx, customerSalePriceAdjust)
		if err != nil {
			return
		}
	}

	return
}

func (r *CustomerSalePriceAdjustRepo) Update(ctx context.Context, req *structure.UpdateCustomerSalePriceAdjustParam) (id uint64, err error) {
	var (
		customerSalePriceAdjust model.CustomerSalePriceAdjust
	)
	customerSalePriceAdjust, err = mysql.MustFirstCustomerSalePriceAdjustByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 待审核状态
	if customerSalePriceAdjust.AuditStatus != common.AdjustStatusPendingAudit {
		err = middleware.WarnLog(errors.NewError(errors.ErrorCodeModifyByFalseStatus))
		return
	}

	// 判断是否有更新单据的权限
	err = mysql_base.CanUpdate(ctx, customerSalePriceAdjust.CreatorId, customerSalePriceAdjust.CreatorName)
	if err != nil {
		return
	}

	customerSalePriceAdjust.UpdateCustomerSalePriceAdjust(ctx, req)

	customerSalePriceAdjust, err = mysql.MustUpdateCustomerSalePriceAdjust(r.tx, customerSalePriceAdjust)
	if err != nil {
		return
	}

	return customerSalePriceAdjust.Id, err
}

func (r *CustomerSalePriceAdjustRepo) UpdateStatus(ctx context.Context, req *structure.UpdateCustomerSalePriceAdjustStatusParam) (data structure.UpdateCustomerSalePriceAdjustStatusData, err error) {
	var (
		customerSalePriceAdjustList model.CustomerSalePriceAdjustList
	)

	customerSalePriceAdjustList, err = mysql.FindCustomerSalePriceAdjustByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}

	for _, customerSalePriceAdjust := range customerSalePriceAdjustList {
		customerSalePriceAdjust.UpdateCustomerSalePriceAdjustStatus(ctx, req)
		customerSalePriceAdjust, err = mysql.MustUpdateCustomerSalePriceAdjust(r.tx, customerSalePriceAdjust)
		if err != nil {
			return
		}
	}
	return
}

func (r *CustomerSalePriceAdjustRepo) UpdateStatusPass(ctx context.Context, req *structure.UpdateCustomerSalePriceAdjustAuditStatusParam) (data structure.UpdateCustomerSalePriceAdjustAuditStatusData, err error) {
	var (
		customerSalePriceAdjust model.CustomerSalePriceAdjust
	)

	customerSalePriceAdjust, err = mysql.MustFirstCustomerSalePriceAdjustByID(r.tx, req.Id)
	if err != nil {
		return
	}
	// 审核
	if customerSalePriceAdjust.AuditStatus != common.AdjustStatusPendingAudit {
		err = middleware.WarnLog(errors.NewError(errors.ErrorCodePassOrderByFalseStatus))
		return
	}

	// 判断是否有审核自己创建的单据的权限
	if !metadata.GetIsAllowAuditSelf(ctx) {
		// 判断是否需要跳过验证
		if !metadata.GetIsSkipAudit(ctx) {
			// 如果单据创建人就是当前用户
			if customerSalePriceAdjust.GetCreatorId() == metadata.GetUserId(ctx) {
				err = log.WarnLog(errors.NewCustomError(errors.ErrCodeNotAllowAuditSelfOrder, "，请移交相关人处理。"))
				return
			}
		}
	}

	if customerSalePriceAdjust.DeadlineTime.Before(time.Now()) {
		customerSalePriceAdjust.Exprie(ctx)
	} else {
		customerSalePriceAdjust.Audit(ctx)
	}

	customerSalePriceAdjust, err = mysql.MustUpdateCustomerSalePriceAdjust(r.tx, customerSalePriceAdjust)
	if err != nil {
		return
	}

	data.Id = customerSalePriceAdjust.Id
	data.EffectiveTime = customerSalePriceAdjust.EffectiveTime
	data.AdjustStatus = customerSalePriceAdjust.AuditStatus
	return
}

func (r *CustomerSalePriceAdjustRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateCustomerSalePriceAdjustAuditStatusParam) (data structure.UpdateCustomerSalePriceAdjustAuditStatusData, err error) {
	var (
		customerSalePriceAdjust model.CustomerSalePriceAdjust
	)

	customerSalePriceAdjust, err = mysql.MustFirstCustomerSalePriceAdjustByID(r.tx, req.Id)
	if err != nil {
		return
	}
	// 作废
	if customerSalePriceAdjust.AuditStatus != common.AdjustStatusAudited {
		err = middleware.WarnLog(errors.NewError(errors.ErrorCodeCancelPassOrderByFalseStatus))
		return
	}

	// 判断是否有作废其他人创建的单据的权限
	if !metadata.GetIsAllowCancelOther(ctx) {
		// 判断是否需要跳过验证
		if !metadata.GetIsSkipAudit(ctx) {
			// 如果单据创建人不是当前用户
			if customerSalePriceAdjust.GetCreatorId() != metadata.GetUserId(ctx) {
				err = log.WarnLog(errors.NewCustomError(errors.ErrCodeNotAllowCancelOtherOrder, fmt.Sprintf("。创建人为【%v】", customerSalePriceAdjust.CreatorName)))
				return
			}
		}
	}

	customerSalePriceAdjust.Cancel(ctx, req)

	customerSalePriceAdjust.CancelRemark = req.CancelRemark
	customerSalePriceAdjust, err = mysql.MustUpdateCustomerSalePriceAdjust(r.tx, customerSalePriceAdjust)
	if err != nil {
		return
	}
	return
}

func (r *CustomerSalePriceAdjustRepo) Delete(ctx context.Context, req *structure.DeleteCustomerSalePriceAdjustParam) (data structure.DeleteCustomerSalePriceAdjustData, err error) {
	var (
		customerSalePriceAdjustList model.CustomerSalePriceAdjustList
	)

	customerSalePriceAdjustList, err = mysql.FindCustomerSalePriceAdjustByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}

	for _, customerSalePriceAdjust := range customerSalePriceAdjustList {
		// 删除
		err = mysql.MustDeleteCustomerSalePriceAdjust(r.tx, customerSalePriceAdjust)
		if err != nil {
			return
		}
		data.Id = append(data.Id, customerSalePriceAdjust.Id)
	}
	return
}

func (r *CustomerSalePriceAdjustRepo) Get(ctx context.Context, req *structure.GetCustomerSalePriceAdjustQuery) (data structure.GetCustomerSalePriceAdjustData, err error) {
	var (
		customerSalePriceAdjust model.CustomerSalePriceAdjust
	)
	customerSalePriceAdjust, err = mysql.MustFirstCustomerSalePriceAdjustByID(r.tx, req.Id)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	departmentIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "department_id")
	departmentSvc := department.NewDepartmentClient()
	departmentName, err := departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return
	}

	saleLevelIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "sale_level_id")
	saleLevelSvc := sale_price.NewSaleLevelClient()
	saleLevel, err := saleLevelSvc.GetSaleLevelNameByIds(ctx, saleLevelIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "product_id")
	productSvc := product2.NewProductClient()
	productItem, _, err := productSvc.GetProductByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	productKindNameIds := mysql_base.GetUInt64List(customerSalePriceAdjust, "product_kind_id")
	productKindNameSvc := type_basic_data.NewTypeFabricClient()
	productKindName, err := productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds)
	if err != nil {
		return
	}

	o := structure.GetCustomerSalePriceAdjustData{}
	o.Id = customerSalePriceAdjust.Id
	o.CreateTime = tools.MyTime(customerSalePriceAdjust.CreateTime)
	o.UpdateTime = tools.MyTime(customerSalePriceAdjust.UpdateTime)
	o.CreatorId = customerSalePriceAdjust.CreatorId
	o.CreatorName = customerSalePriceAdjust.CreatorName
	o.UpdaterId = customerSalePriceAdjust.UpdaterId
	o.UpdateUserName = customerSalePriceAdjust.UpdaterName
	o.SaleSystemId = customerSalePriceAdjust.SaleSystemId
	o.CustomerId = customerSalePriceAdjust.CustomerId
	o.CustomerName = bizUnit[customerSalePriceAdjust.CustomerId]
	o.SaleAreaId = customerSalePriceAdjust.SaleAreaId
	o.SaleGroupId = customerSalePriceAdjust.SaleGroupId
	o.SaleUserId = customerSalePriceAdjust.SaleUserId
	o.ProductId = customerSalePriceAdjust.ProductId
	o.ProductCode = productItem[customerSalePriceAdjust.ProductId][0]
	o.ProductName = productItem[customerSalePriceAdjust.ProductId][1]
	o.ProductKindId = customerSalePriceAdjust.ProductKindId
	o.ProductKindName = productKindName[customerSalePriceAdjust.ProductKindId]
	o.ProductColorKindId = customerSalePriceAdjust.ProductColorKindId
	o.ProductColorKindName = productColorKind[customerSalePriceAdjust.ProductColorKindId]
	o.ProductColorId = customerSalePriceAdjust.ProductColorId
	o.ProductColorCode = productColorItem[customerSalePriceAdjust.ProductColorId][0]
	o.ProductColorName = productColorItem[customerSalePriceAdjust.ProductColorId][1]
	o.SaleLevelId = customerSalePriceAdjust.SaleLevelId
	o.SaleLevelName = saleLevel[customerSalePriceAdjust.SaleLevelId]
	o.OffsetSalePrice = tools.Cent(customerSalePriceAdjust.OffsetSalePrice)
	o.OffsetWeightError = tools.Milligram(customerSalePriceAdjust.OffsetWeightError)
	o.OffsetLengthCutSalePrice = tools.Cent(customerSalePriceAdjust.OffsetLengthCutSalePrice)
	o.OffsetWeightCutSalePrice = tools.Cent(customerSalePriceAdjust.OffsetWeightCutSalePrice)
	o.Remark = customerSalePriceAdjust.Remark
	o.EffectiveTime = tools.MyTime(customerSalePriceAdjust.EffectiveTime)
	o.DeadlineTime = tools.MyTime(customerSalePriceAdjust.DeadlineTime)
	o.Status = customerSalePriceAdjust.Status
	o.StatusName = customerSalePriceAdjust.Status.String()
	o.DepartmentId = customerSalePriceAdjust.DepartmentId
	o.DepartmentName = departmentName[customerSalePriceAdjust.DepartmentId]
	o.IsDisplayPrice = customerSalePriceAdjust.IsDisplayPrice
	o.AdjustStatus = customerSalePriceAdjust.AuditStatus
	o.AdjustStatusName = customerSalePriceAdjust.AuditStatus.String()
	o.AuditorId = customerSalePriceAdjust.AuditorId
	o.AuditorName = user[customerSalePriceAdjust.AuditorId]
	o.AuditDate = tools.MyTime(customerSalePriceAdjust.AuditDate)
	o.CancelRemark = customerSalePriceAdjust.CancelRemark
	data = o
	return
}

func (r *CustomerSalePriceAdjustRepo) GetList(ctx context.Context, req *structure.GetCustomerSalePriceAdjustListQuery) (list structure.GetCustomerSalePriceAdjustDataList, total int, err error) {
	var (
		customerSalePriceAdjusts model.CustomerSalePriceAdjustList
	)
	customerSalePriceAdjusts, total, err = mysql.SearchCustomerSalePriceAdjust(r.tx, req)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	saleGroupIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_group_id")
	saleGroup, err := bizUnitSvc.GetSaleGroupNameByIds(ctx, saleGroupIds)
	if err != nil {
		return
	}

	saleAreaIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_area_id")
	saleArea, err := bizUnitSvc.GetSaleAreaNameByIds(ctx, saleAreaIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	departmentIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "department_id")
	departmentSvc := department.NewDepartmentClient()
	departmentName, err := departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return
	}

	saleLevelIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_level_id")
	saleLevelSvc := sale_price.NewSaleLevelClient()
	saleLevel, err := saleLevelSvc.GetSaleLevelNameByIds(ctx, saleLevelIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_id")
	productSvc := product2.NewProductClient()
	productItem, _, err := productSvc.GetProductByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	productKindNameIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_kind_id")
	productKindNameSvc := type_basic_data.NewTypeFabricClient()
	productKindName, err := productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds)
	if err != nil {
		return
	}

	for _, customerSalePriceAdjust := range customerSalePriceAdjusts.List() {
		o := structure.GetCustomerSalePriceAdjustData{}
		o.Id = customerSalePriceAdjust.Id
		o.CreateTime = tools.MyTime(customerSalePriceAdjust.CreateTime)
		o.UpdateTime = tools.MyTime(customerSalePriceAdjust.UpdateTime)
		o.CreatorId = customerSalePriceAdjust.CreatorId
		o.CreatorName = customerSalePriceAdjust.CreatorName
		o.UpdaterId = customerSalePriceAdjust.UpdaterId
		o.UpdateUserName = customerSalePriceAdjust.UpdaterName
		o.SaleSystemId = customerSalePriceAdjust.SaleSystemId
		o.SaleSystemName = saleSystem[customerSalePriceAdjust.SaleSystemId]
		o.CustomerId = customerSalePriceAdjust.CustomerId
		o.CustomerName = bizUnit[customerSalePriceAdjust.CustomerId]
		o.SaleAreaId = customerSalePriceAdjust.SaleAreaId
		o.SaleAreaName = saleArea[customerSalePriceAdjust.SaleAreaId]
		o.SaleGroupId = customerSalePriceAdjust.SaleGroupId
		o.SaleGroupName = saleGroup[customerSalePriceAdjust.SaleGroupId]
		o.SaleUserId = customerSalePriceAdjust.SaleUserId
		o.SaleUserName = employeeName[customerSalePriceAdjust.SaleUserId]
		o.ProductId = customerSalePriceAdjust.ProductId
		o.ProductCode = productItem[customerSalePriceAdjust.ProductId][0]
		o.ProductName = productItem[customerSalePriceAdjust.ProductId][1]
		o.ProductKindId = customerSalePriceAdjust.ProductKindId
		o.ProductKindName = productKindName[customerSalePriceAdjust.ProductKindId]
		o.ProductColorKindId = customerSalePriceAdjust.ProductColorKindId
		o.ProductColorKindName = productColorKind[customerSalePriceAdjust.ProductColorKindId]
		o.ProductColorId = customerSalePriceAdjust.ProductColorId
		o.ProductColorCode = productColorItem[customerSalePriceAdjust.ProductColorId][0]
		o.ProductColorName = productColorItem[customerSalePriceAdjust.ProductColorId][1]
		o.SaleLevelId = customerSalePriceAdjust.SaleLevelId
		o.SaleLevelName = saleLevel[customerSalePriceAdjust.SaleLevelId]
		o.OffsetSalePrice = tools.Cent(customerSalePriceAdjust.OffsetSalePrice)
		o.OffsetWeightError = tools.Milligram(customerSalePriceAdjust.OffsetWeightError)
		o.OffsetLengthCutSalePrice = tools.Cent(customerSalePriceAdjust.OffsetLengthCutSalePrice)
		o.OffsetWeightCutSalePrice = tools.Cent(customerSalePriceAdjust.OffsetWeightCutSalePrice)
		o.Remark = customerSalePriceAdjust.Remark
		o.EffectiveTime = tools.MyTime(customerSalePriceAdjust.EffectiveTime)
		o.DeadlineTime = tools.MyTime(customerSalePriceAdjust.DeadlineTime)
		o.Status = customerSalePriceAdjust.Status
		o.StatusName = customerSalePriceAdjust.Status.String()
		o.DepartmentId = customerSalePriceAdjust.DepartmentId
		o.DepartmentName = departmentName[customerSalePriceAdjust.DepartmentId]
		o.IsDisplayPrice = customerSalePriceAdjust.IsDisplayPrice
		o.AdjustStatus = customerSalePriceAdjust.AuditStatus
		o.AdjustStatusName = customerSalePriceAdjust.AuditStatus.String()
		o.AuditorId = customerSalePriceAdjust.AuditorId
		o.AuditorName = user[customerSalePriceAdjust.AuditorId]
		o.AuditDate = tools.MyTime(customerSalePriceAdjust.AuditDate)
		o.CancelRemark = customerSalePriceAdjust.CancelRemark
		list = append(list, o)
	}
	return
}

func (r *CustomerSalePriceAdjustRepo) GetDropdownList(ctx context.Context, req *structure.GetCustomerSalePriceAdjustListQuery) (list structure.GetCustomerSalePriceAdjustDropdownDataList, total int, err error) {
	var (
		customerSalePriceAdjusts model.CustomerSalePriceAdjustList
	)
	customerSalePriceAdjusts, total, err = mysql.SearchCustomerSalePriceAdjust(r.tx, req)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	saleGroupIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_group_id")
	saleGroup, err := bizUnitSvc.GetSaleGroupNameByIds(ctx, saleGroupIds)
	if err != nil {
		return
	}

	saleAreaIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_area_id")
	saleArea, err := bizUnitSvc.GetSaleAreaNameByIds(ctx, saleAreaIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	departmentIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "department_id")
	departmentSvc := department.NewDepartmentClient()
	departmentName, err := departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return
	}

	saleLevelIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "sale_level_id")
	saleLevelSvc := sale_price.NewSaleLevelClient()
	saleLevel, err := saleLevelSvc.GetSaleLevelNameByIds(ctx, saleLevelIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_id")
	productSvc := product2.NewProductClient()
	productItem, _, err := productSvc.GetProductByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	productKindNameIds := mysql_base.GetUInt64List(customerSalePriceAdjusts, "product_kind_id")
	productKindNameSvc := type_basic_data.NewTypeFabricClient()
	productKindName, err := productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds)
	if err != nil {
		return
	}

	for _, customerSalePriceAdjust := range customerSalePriceAdjusts.List() {
		o := structure.GetCustomerSalePriceAdjustDropdownData{}
		o.Id = customerSalePriceAdjust.Id
		o.CreateTime = tools.MyTime(customerSalePriceAdjust.CreateTime)
		o.UpdateTime = tools.MyTime(customerSalePriceAdjust.UpdateTime)
		o.CreatorId = customerSalePriceAdjust.CreatorId
		o.CreatorName = customerSalePriceAdjust.CreatorName
		o.UpdaterId = customerSalePriceAdjust.UpdaterId
		o.UpdateUserName = customerSalePriceAdjust.UpdaterName
		o.SaleSystemId = customerSalePriceAdjust.SaleSystemId
		o.SaleSystemName = saleSystem[customerSalePriceAdjust.SaleSystemId]
		o.CustomerId = customerSalePriceAdjust.CustomerId
		o.CustomerName = bizUnit[customerSalePriceAdjust.CustomerId]
		o.SaleAreaId = customerSalePriceAdjust.SaleAreaId
		o.SaleAreaName = saleArea[customerSalePriceAdjust.SaleAreaId]
		o.SaleGroupId = customerSalePriceAdjust.SaleGroupId
		o.SaleGroupName = saleGroup[customerSalePriceAdjust.SaleGroupId]
		o.SaleUserId = customerSalePriceAdjust.SaleUserId
		o.SaleUserName = employeeName[customerSalePriceAdjust.SaleUserId]
		o.ProductId = customerSalePriceAdjust.ProductId
		o.ProductCode = productItem[customerSalePriceAdjust.ProductId][0]
		o.ProductName = productItem[customerSalePriceAdjust.ProductId][1]
		o.ProductKindId = customerSalePriceAdjust.ProductKindId
		o.ProductKindName = productKindName[customerSalePriceAdjust.ProductKindId]
		o.ProductColorKindId = customerSalePriceAdjust.ProductColorKindId
		o.ProductColorKindName = productColorKind[customerSalePriceAdjust.ProductColorKindId]
		o.ProductColorId = customerSalePriceAdjust.ProductColorId
		o.ProductColorCode = productColorItem[customerSalePriceAdjust.ProductColorId][0]
		o.ProductColorName = productColorItem[customerSalePriceAdjust.ProductColorId][1]
		o.SaleLevelId = customerSalePriceAdjust.SaleLevelId
		o.SaleLevelName = saleLevel[customerSalePriceAdjust.SaleLevelId]
		o.OffsetSalePrice = customerSalePriceAdjust.OffsetSalePrice
		o.OffsetWeightError = customerSalePriceAdjust.OffsetWeightError
		o.OffsetLengthCutSalePrice = customerSalePriceAdjust.OffsetLengthCutSalePrice
		o.OffsetWeightCutSalePrice = customerSalePriceAdjust.OffsetWeightCutSalePrice
		o.Remark = customerSalePriceAdjust.Remark
		o.EffectiveTime = tools.MyTime(customerSalePriceAdjust.EffectiveTime)
		o.DeadlineTime = tools.MyTime(customerSalePriceAdjust.DeadlineTime)
		o.Status = customerSalePriceAdjust.Status
		o.StatusName = customerSalePriceAdjust.Status.String()
		o.DepartmentId = customerSalePriceAdjust.DepartmentId
		o.DepartmentName = departmentName[customerSalePriceAdjust.DepartmentId]
		o.IsDisplayPrice = customerSalePriceAdjust.IsDisplayPrice
		o.AdjustStatus = customerSalePriceAdjust.AuditStatus
		o.AdjustStatusName = customerSalePriceAdjust.AuditStatus.String()
		o.AuditorId = customerSalePriceAdjust.AuditorId
		o.AuditorName = user[customerSalePriceAdjust.AuditorId]
		o.AuditDate = tools.MyTime(customerSalePriceAdjust.AuditDate)
		o.CancelRemark = customerSalePriceAdjust.CancelRemark
		list = append(list, o)
	}
	return
}

// (面料+色号>面料+系列>面料>颜色系列)
func CalcPurchaserLadderSalePrice(
	ctx context.Context,
	tx *mysql_base.Tx,
	saleLevel map[uint64]string,
	salePriceColorKind model.SalePriceColorKind,
	customerSalePriceAdjusts model.CustomerSalePriceAdjustList,
	saleLevelPrices model.SalePriceLevelList,
	productID,
	productColorKindId,
	productColorId uint64,
) (
	// customerSalePriceAdjustID uint64,
	salePrice productStructure.SalePrice,
) {
	if salePriceColorKind.DeadlineTime.Before(time.Now()) {
		var (
			salePriceColorKinds model.SalePriceColorKindList
			count               int
			err                 error
		)
		salePriceColorKinds, count, err = mysql.SearchSalePriceColorKindLimit2(tx, productID, productColorKindId)
		if err != nil {
			return
		}
		if count != 0 {
			salePriceColorKind = salePriceColorKinds[0]
		}
	}
	customerAdjust, _ := customerSalePriceAdjusts.GetCustomerLatestSaleLevel()
	if len(customerSalePriceAdjusts) != 0 || customerAdjust.SaleLevelId != 0 {
		{
			colorAdjust, ok := customerSalePriceAdjusts.GetProductProductColor(productID, productColorId)
			if ok {
				saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, colorAdjust.SaleLevelId)
				salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
				salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
				salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
				salePrice.WeightError = salePriceColorKind.WeightError

				salePrice.SaleLevelId = colorAdjust.SaleLevelId
				salePrice.SaleLevelName = saleLevel[colorAdjust.SaleLevelId]
				salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + colorAdjust.OffsetSalePrice
				salePrice.OffsetLengthCutSalePrice = colorAdjust.OffsetLengthCutSalePrice
				salePrice.OffsetWeightCutSalePrice = colorAdjust.OffsetWeightCutSalePrice
				if colorAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = colorAdjust.OffsetWeightError
				} else {
					salePrice.OffsetWeightError = saleLevelPrice.WeightError
				}

				salePrice.SalePrice = salePrice.StandardSalePrice - salePrice.OffsetSalePrice
				salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
				salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

				salePrice.IsDisplayPrice = colorAdjust.IsDisplayPrice
				// customerSalePriceAdjustID = colorAdjust.Id
				return
			}
		}

		{
			kindAdjust, ok := customerSalePriceAdjusts.GetProductProductColorKind(productID, productColorKindId)
			if ok {
				saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, kindAdjust.SaleLevelId)
				salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
				salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
				salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
				salePrice.WeightError = salePriceColorKind.WeightError

				salePrice.SaleLevelId = kindAdjust.SaleLevelId
				salePrice.SaleLevelName = saleLevel[kindAdjust.SaleLevelId]
				salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + kindAdjust.OffsetSalePrice
				salePrice.OffsetLengthCutSalePrice = kindAdjust.OffsetLengthCutSalePrice
				salePrice.OffsetWeightCutSalePrice = kindAdjust.OffsetWeightCutSalePrice
				if kindAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = kindAdjust.OffsetWeightError
				} else {
					salePrice.OffsetWeightError = saleLevelPrice.WeightError
				}

				salePrice.SalePrice = salePrice.StandardSalePrice - salePrice.OffsetSalePrice
				salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
				salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

				salePrice.IsDisplayPrice = kindAdjust.IsDisplayPrice
				// customerSalePriceAdjustID = kindAdjust.Id
				return
			}
		}

		{
			productAdjust, ok := customerSalePriceAdjusts.GetProduct(productID)
			if ok {
				saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, productAdjust.SaleLevelId)
				salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
				salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
				salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
				salePrice.WeightError = salePriceColorKind.WeightError

				salePrice.SaleLevelId = productAdjust.SaleLevelId
				salePrice.SaleLevelName = saleLevel[productAdjust.SaleLevelId]
				salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + productAdjust.OffsetSalePrice
				salePrice.OffsetLengthCutSalePrice = productAdjust.OffsetLengthCutSalePrice
				salePrice.OffsetWeightCutSalePrice = productAdjust.OffsetWeightCutSalePrice
				if productAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = productAdjust.OffsetWeightError
				} else {
					salePrice.OffsetWeightError = saleLevelPrice.WeightError
				}

				salePrice.SalePrice = salePrice.StandardSalePrice - salePrice.OffsetSalePrice
				salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
				salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

				salePrice.IsDisplayPrice = productAdjust.IsDisplayPrice
				// customerSalePriceAdjustID = productAdjust.Id
				return
			}
		}

		{
			kindAdjust, ok := customerSalePriceAdjusts.GetProductColorKind(productColorKindId)
			if ok {
				saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, kindAdjust.SaleLevelId)
				salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
				salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
				salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
				salePrice.WeightError = salePriceColorKind.WeightError

				salePrice.SaleLevelId = kindAdjust.SaleLevelId
				salePrice.SaleLevelName = saleLevel[kindAdjust.SaleLevelId]
				salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + kindAdjust.OffsetSalePrice
				salePrice.OffsetLengthCutSalePrice = kindAdjust.OffsetLengthCutSalePrice
				salePrice.OffsetWeightCutSalePrice = kindAdjust.OffsetWeightCutSalePrice
				if kindAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = kindAdjust.OffsetWeightError
				} else {
					salePrice.OffsetWeightError = saleLevelPrice.WeightError
				}

				salePrice.SalePrice = salePrice.StandardSalePrice - salePrice.OffsetSalePrice
				salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
				salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

				salePrice.IsDisplayPrice = kindAdjust.IsDisplayPrice
				// customerSalePriceAdjustID = kindAdjust.Id
				return
			}
		}

		if customerAdjust.SaleLevelId != 0 {
			saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, customerAdjust.SaleLevelId)
			salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
			salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
			salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
			salePrice.WeightError = salePriceColorKind.WeightError

			salePrice.SaleLevelId = customerAdjust.SaleLevelId
			salePrice.SaleLevelName = saleLevel[customerAdjust.SaleLevelId]
			salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice
			salePrice.OffsetWeightCutSalePrice = 0
			salePrice.OffsetLengthCutSalePrice = 0
			salePrice.OffsetWeightError = saleLevelPrice.WeightError

			salePrice.SalePrice = saleLevelPrice.BulkSalePrice
			salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice
			salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice

			salePrice.IsDisplayPrice = true
			return
		}
	}

	salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
	salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
	salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
	salePrice.WeightError = salePriceColorKind.WeightError

	salePrice.SaleLevelId = 0
	salePrice.SaleLevelName = ""
	salePrice.OffsetSalePrice = 0
	salePrice.OffsetWeightCutSalePrice = 0
	salePrice.OffsetLengthCutSalePrice = 0
	salePrice.OffsetWeightError = 0

	salePrice.SalePrice = salePrice.StandardSalePrice
	salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice
	salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice

	salePrice.IsDisplayPrice = true

	return
}

// (面料+色号>面料+系列>面料>颜色系列)
func MPCalcPurchaserLadderSalePrice(
	ctx context.Context,
	tx *mysql_base.Tx,
	saleLevel map[uint64]string,
	salePriceColorKind model.SalePriceColorKind,
	customerSalePriceAdjusts model.CustomerSalePriceAdjustList,
	saleLevelPrices model.SalePriceLevelList,
	customerId,
	productID,
	productColorKindId,
	productColorId uint64,
	saleSystem sale_system.Res,
) (
	// customerSalePriceAdjustID uint64,
	salePrice productStructure.SalePrice,
) {
	// 启用最后一次销售价格
	if saleSystem.DefaultLastSalePrice == true && customerId != 0 {
		lastRecord, err := should_collect_order.NewShouldCollectOrderClient().GetLastOrderByCustomerId(ctx, customerId, productColorId, saleSystem.Id)
		if err != nil {
			return
		}
		if lastRecord.SalePrice != 0 {
			// 如果找到对应产品的最新销售记录
			salePrice.SalePrice = lastRecord.SalePrice // 主销售单价
		} else if lastRecord.LengthCutSalePrice != 0 {
			salePrice.SalePrice = lastRecord.LengthCutSalePrice // 主销售单价
		}
		salePrice.IsDisplayPrice = true
	} else {
		// 启用最后一次销售价格
		//if IsDefaultLastSalePrice {
		//	dao := sale_mysql.NewSaleProductOrderDetailDao(ctx, false)
		//	list, err := dao.FindLatest(ctx, tx, &sale_structure.GetLastSalePriceQuery{
		//		CustomerId:      customerId,
		//		ProductColorIds: tools.QueryIntList(strconv.FormatUint(productColorId, 10)),
		//	})
		//	if err != nil {
		//		return
		//	}
		//	if len(list) > 0 {
		//		// 根据productColorIds的id，取list和productcolorids的id对应的最新记录
		//		latestRecords := make(map[uint64]sale_model.SaleProductOrderDetail)
		//		// 遍历list,保存每个product_color_id对应的最新记录
		//		for _, detail := range list {
		//			if existing, ok := latestRecords[detail.ProductColorId]; !ok || detail.CreateTime.After(existing.CreateTime) {
		//				latestRecords[detail.ProductColorId] = detail
		//			}
		//		}
		//		// 如果找到对应产品的最新销售记录
		//		if lastDetail, ok := latestRecords[productColorId]; ok {
		//
		//			salePrice.LatestSalePrice = lastDetail.SalePrice                   // 主销售单价
		//			salePrice.LatestLengthCutSalePrice = lastDetail.LengthCutSalePrice // 辅销售单价
		//
		//			salePrice.IsDisplayPrice = true
		//		}
		//	}
		//}

		if salePriceColorKind.DeadlineTime.Before(time.Now()) {
			var (
				salePriceColorKinds model.SalePriceColorKindList
				count               int
				err                 error
			)
			salePriceColorKinds, count, err = mysql.SearchSalePriceColorKindLimit2(tx, productID, productColorKindId)
			if err != nil {
				return
			}
			if count != 0 {
				salePriceColorKind = salePriceColorKinds[0]
			}
		}
		customerAdjust, _ := customerSalePriceAdjusts.GetCustomerLatestSaleLevel()
		if len(customerSalePriceAdjusts) != 0 || customerAdjust.SaleLevelId != 0 {
			{
				colorAdjust, ok := customerSalePriceAdjusts.GetProductProductColor(productID, productColorId)
				if ok {
					saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, colorAdjust.SaleLevelId)
					salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
					salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
					salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
					//salePrice.WeightError = salePriceColorKind.WeightError

					salePrice.SaleLevelId = colorAdjust.SaleLevelId
					salePrice.SaleLevelName = saleLevel[colorAdjust.SaleLevelId]
					salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + colorAdjust.OffsetSalePrice
					salePrice.OffsetLengthCutSalePrice = colorAdjust.OffsetLengthCutSalePrice
					salePrice.OffsetWeightCutSalePrice = colorAdjust.OffsetWeightCutSalePrice
					//if colorAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = colorAdjust.OffsetWeightError + saleLevelPrice.WeightError
					//} else {
					//	salePrice.OffsetWeightError = saleLevelPrice.WeightError
					//}

					salePrice.SalePrice = saleLevelPrice.BulkSalePrice - colorAdjust.OffsetSalePrice
					salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
					salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

					salePrice.IsDisplayPrice = colorAdjust.IsDisplayPrice
					// customerSalePriceAdjustID = colorAdjust.Id
					return
				}
			}

			{
				kindAdjust, ok := customerSalePriceAdjusts.GetProductProductColorKind(productID, productColorKindId)
				if ok {
					saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, kindAdjust.SaleLevelId)
					salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
					salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
					salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
					//salePrice.WeightError = salePriceColorKind.WeightError

					salePrice.SaleLevelId = kindAdjust.SaleLevelId
					salePrice.SaleLevelName = saleLevel[kindAdjust.SaleLevelId]
					salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + kindAdjust.OffsetSalePrice
					salePrice.OffsetLengthCutSalePrice = kindAdjust.OffsetLengthCutSalePrice
					salePrice.OffsetWeightCutSalePrice = kindAdjust.OffsetWeightCutSalePrice
					//if kindAdjust.OffsetWeightError != 0 {
					//	salePrice.OffsetWeightError = kindAdjust.OffsetWeightError + saleLevelPrice.WeightError
					//} else {
					salePrice.OffsetWeightError = saleLevelPrice.WeightError + kindAdjust.OffsetWeightError
					//}

					salePrice.SalePrice = saleLevelPrice.BulkSalePrice - kindAdjust.OffsetSalePrice
					salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
					salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

					salePrice.IsDisplayPrice = kindAdjust.IsDisplayPrice
					// customerSalePriceAdjustID = kindAdjust.Id
					return
				}
			}

			{
				productAdjust, ok := customerSalePriceAdjusts.GetProduct(productID)
				if ok {
					saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, productAdjust.SaleLevelId)
					salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
					salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
					salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
					//salePrice.WeightError = salePriceColorKind.WeightError

					salePrice.SaleLevelId = productAdjust.SaleLevelId
					salePrice.SaleLevelName = saleLevel[productAdjust.SaleLevelId]
					salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + productAdjust.OffsetSalePrice
					salePrice.OffsetLengthCutSalePrice = productAdjust.OffsetLengthCutSalePrice
					salePrice.OffsetWeightCutSalePrice = productAdjust.OffsetWeightCutSalePrice
					//if productAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = productAdjust.OffsetWeightError + saleLevelPrice.WeightError
					//} else {
					//	salePrice.OffsetWeightError = saleLevelPrice.WeightError
					//}

					salePrice.SalePrice = saleLevelPrice.BulkSalePrice - productAdjust.OffsetSalePrice
					salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
					salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

					salePrice.IsDisplayPrice = productAdjust.IsDisplayPrice
					// customerSalePriceAdjustID = productAdjust.Id
					return
				}
			}

			{
				kindAdjust, ok := customerSalePriceAdjusts.GetProductColorKind(productColorKindId)
				if ok {
					saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, kindAdjust.SaleLevelId)
					salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
					salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
					salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
					//salePrice.WeightError = salePriceColorKind.WeightError

					salePrice.SaleLevelId = kindAdjust.SaleLevelId
					salePrice.SaleLevelName = saleLevel[kindAdjust.SaleLevelId]
					salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice + kindAdjust.OffsetSalePrice
					salePrice.OffsetLengthCutSalePrice = kindAdjust.OffsetLengthCutSalePrice
					salePrice.OffsetWeightCutSalePrice = kindAdjust.OffsetWeightCutSalePrice
					//if kindAdjust.OffsetWeightError != 0 {
					salePrice.OffsetWeightError = kindAdjust.OffsetWeightError + saleLevelPrice.WeightError
					//} else {
					//	salePrice.OffsetWeightError = saleLevelPrice.WeightError
					//}

					salePrice.SalePrice = saleLevelPrice.BulkSalePrice - kindAdjust.OffsetSalePrice
					salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice - salePrice.OffsetWeightCutSalePrice
					salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice - salePrice.OffsetLengthCutSalePrice

					salePrice.IsDisplayPrice = kindAdjust.IsDisplayPrice
					// customerSalePriceAdjustID = kindAdjust.Id
					return
				}
			}

			if customerAdjust.SaleLevelId != 0 {
				saleLevelPrice := saleLevelPrices.Pick(productID, productColorKindId, customerAdjust.SaleLevelId)
				salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
				salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
				salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
				//salePrice.WeightError = salePriceColorKind.WeightError

				salePrice.SaleLevelId = customerAdjust.SaleLevelId
				salePrice.SaleLevelName = saleLevel[customerAdjust.SaleLevelId]
				salePrice.OffsetSalePrice = salePriceColorKind.BulkSalePrice - saleLevelPrice.BulkSalePrice
				salePrice.OffsetWeightCutSalePrice = 0
				salePrice.OffsetLengthCutSalePrice = 0
				salePrice.OffsetWeightError = customerAdjust.OffsetWeightError + saleLevelPrice.WeightError

				salePrice.SalePrice = saleLevelPrice.BulkSalePrice - customerAdjust.OffsetSalePrice
				salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice
				salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice

				salePrice.IsDisplayPrice = true
				return
			}
		}
	}
	salePrice.StandardSalePrice = salePriceColorKind.BulkSalePrice
	salePrice.StandardWeightCutSalePrice = salePriceColorKind.WeightCutSalePrice
	salePrice.StandardLengthCutSalePrice = salePriceColorKind.LengthCutSalePrice
	//salePrice.WeightError = salePriceColorKind.WeightError

	salePrice.SaleLevelId = 0
	salePrice.SaleLevelName = ""
	salePrice.OffsetSalePrice = 0
	salePrice.OffsetWeightCutSalePrice = 0
	salePrice.OffsetLengthCutSalePrice = 0
	salePrice.OffsetWeightError = salePriceColorKind.WeightError
	if saleSystem.DefaultLastSalePrice != true {
		salePrice.SalePrice = salePriceColorKind.BulkSalePrice
	}
	salePrice.WeightCutSalePrice = salePrice.StandardWeightCutSalePrice
	salePrice.LengthCutSalePrice = salePrice.StandardLengthCutSalePrice

	salePrice.IsDisplayPrice = true

	return
}
