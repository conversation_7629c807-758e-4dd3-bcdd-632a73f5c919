package product

import (
	"context"
	aggs "hcscm/aggs/product"
	"hcscm/common/errors"
	product2 "hcscm/common/product"
	common "hcscm/common/system_consts"
	product_pb "hcscm/extern/pb/product"
	sale3 "hcscm/extern/pb/sale"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	product3 "hcscm/model/mysql/product"
	"hcscm/model/mysql/product/dao"
	sale_dao "hcscm/model/mysql/sale/dao"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/vars"
)

func NewFpmSaleAllocateInOrderService() *FpmSaleAllocateInOrderService {
	return &FpmSaleAllocateInOrderService{}
}

type FpmSaleAllocateInOrderService struct {
}

// 检查所有同源入库单是否已审核通过
func (u FpmSaleAllocateInOrderService) CheckIsPass(ctx context.Context, o sale3.SaleProductOrderRes, tx *mysql_base.Tx, arrnageId uint64) bool {
	// 查询销售单生成的配布单
	getOrders, err := dao.FindFpmArrangeOrderBySrcOrderNo(tx, o.OrderNo)
	if err != nil {
		return false
	}
	var filterOrders product3.FpmArrangeOrderList
	// 如果v.id==arrangeId,则跳过这个元素，并重新赋值给getOrders
	for _, v := range getOrders {
		if v.Id == arrnageId {
			continue
		}
		filterOrders = append(filterOrders, v)
	}
	for _, v := range filterOrders {
		if v.OutOrderType == product2.WarehouseGoodOutTypeSaleAllocate {
			getOrder, err2 := product_pb.NewFpmInOrderClient().GetFpmInOrderByArrangeId(ctx, v.Id)
			if err2 != nil {
				return false
			}
			if getOrder.Id == 0 || getOrder.AuditStatus != common.OrderStatusAudited {
				return false
			}
		}
		if v.OutOrderType == product2.WarehouseGoodOutTypeSale {
			order, err := product_pb.NewArrangeOrderClient().GetArrangeOrder(ctx, product_pb.ArrangeOrderReq{Id: v.Id})
			if err != nil {
				return false
			}
			if order.BusinessStatus != product2.BusinessStatusArrangeWaitOut || order.Id == 0 {
				return false
			}
		}
	}
	return true
}

func (u FpmSaleAllocateInOrderService) Add(ctx context.Context, req *structure.AddFpmSaleAllocateInOrderParam) (data structure.AddFpmSaleAllocateInOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, err = repo.Add(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) Update(ctx context.Context, req *structure.UpdateFpmSaleAllocateInOrderParam) (data structure.UpdateFpmSaleAllocateInOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, err = repo.Update(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmSaleAllocateInOrderBusinessCloseParam) (data structure.UpdateFpmSaleAllocateInOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, err = repo.UpdateBusinessClose(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) UpdateStatusPass(ctx context.Context, tx *mysql_base.Tx, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, addItems structure.AddStockProductDetailParamList, err error) {

	var (
	//dictionaryPB      = dictionary.NewDictionaryClient()
	//details           structure.GetFpmInOrderItemDropdownDataList
	)

	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, addItems, err = repo.UpdateStatusPass(ctx, id)
	if err != nil {
		return
	}
	// 过一层校验
	err = InspectOrder(ctx, structure.InspectOrderData{OrderTime: data.WarehouseInTime.ToTime()})
	if err != nil {
		return
	}
	return
}

func (u FpmSaleAllocateInOrderService) swapOrderData2SaleOutParam(data structure.GetFpmInOrderData, outOrder structure.GetFpmOutOrderData) structure.AddFpmSaleOutOrderParam {
	var (
		saleOutOrderParam = structure.AddFpmSaleOutOrderParam{}
	)
	saleOutOrderParam.SaleAlloInOrderId = data.Id
	saleOutOrderParam.ArrangeOrderId = outOrder.ArrangeOrderId
	saleOutOrderParam.ArrangeOrderNo = outOrder.ArrangeOrderNo
	saleOutOrderParam.SaleSystemId = data.SaleSystemId
	saleOutOrderParam.CustomerId = data.BizUnitId
	saleOutOrderParam.WarehouseId = data.WarehouseId
	saleOutOrderParam.WarehouseOutTime = tools.QueryTime(data.WarehouseInTime.Date())
	saleOutOrderParam.SaleUserId = data.SaleUserId
	saleOutOrderParam.SaleFollowerId = data.SaleFollowerId
	saleOutOrderParam.StoreKeeperId = data.StoreKeeperId
	saleOutOrderParam.DriverId = data.DriverId
	saleOutOrderParam.LogisticsCompanyId = data.LogisticsCompanyId
	saleOutOrderParam.InternalRemark = data.Remark
	saleOutOrderParam.SaleMode = data.SaleMode
	return saleOutOrderParam
}

func (u FpmSaleAllocateInOrderService) swapOrderItemData2SaleOutItemParam(item structure.GetFpmInOrderItemData) structure.AddFpmOutOrderItemParam {
	param := structure.AddFpmOutOrderItemParam{}
	param.SumStockId = item.SumStockId
	param.QuoteOrderNo = item.QuoteOrderNo
	param.QuoteOrderItemId = item.QuoteOrderItemId
	param.ProductCode = item.ProductCode
	param.ProductName = item.ProductName
	param.CustomerId = item.CustomerId
	param.ProductColorId = item.ProductColorId
	param.ProductId = item.ProductId
	param.ProductColorCode = item.ProductColorCode
	param.ProductColorName = item.ProductColorName
	param.ProductLevelId = item.ProductLevelId
	param.ProductWidth = item.ProductWidth
	param.DyeFactoryColorCode = item.DyeFactoryColorCode
	param.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
	param.ProductGramWeight = item.ProductGramWeight
	param.ProductRemark = item.ProductRemark
	param.ProductCraft = item.ProductCraft
	param.ProductIngredient = item.ProductIngredient
	param.OutRoll = item.InRoll
	param.TotalWeight = item.TotalWeight
	param.WeightError = item.WeightError
	param.PaperTubeWeight = item.PaperTubeWeight
	param.SettleWeight = item.SettleWeight
	param.SettleErrorWeight = item.SettleErrorWeight
	param.ActuallyWeight = item.ActuallyWeight
	param.UnitId = item.UnitId
	param.UnitPrice = item.UnitPrice
	param.OutLength = item.OutLength
	param.LengthUnitPrice = item.LengthUnitPrice
	param.OtherPrice = item.OtherPrice
	param.TotalPrice = item.TotalPrice
	param.Remark = item.Remark
	param.ArrangeItemId = item.ArrangeOrderItemId
	param.FpmInOrderItemId = item.Id
	return param
}

func (u FpmSaleAllocateInOrderService) swapOrderItemFcData2SaleOutItemFcParam(fc structure.GetFpmInOrderItemFcData) structure.AddFpmOutOrderItemFcParam {
	param := structure.AddFpmOutOrderItemFcParam{}
	param.Roll = fc.Roll
	param.WarehouseId = fc.WarehouseId
	param.WarehouseBinId = fc.WarehouseBinId
	param.VolumeNumber = fc.VolumeNumber
	param.WarehouseInType = fc.WarehouseInType
	param.WarehouseInOrderId = fc.WarehouseInOrderId
	param.WarehouseInOrderNo = fc.WarehouseInOrderNo
	param.StockId = fc.StockId
	param.SumStockId = fc.SumStockId
	param.BaseUnitWeight = fc.BaseUnitWeight
	param.PaperTubeWeight = fc.PaperTubeWeight
	param.WeightError = fc.WeightError
	param.UnitId = fc.UnitId
	param.Length = fc.Length
	param.SettleWeight = fc.SettleWeight
	param.ActuallyWeight = fc.ActuallyWeight
	param.SettleErrorWeight = fc.SettleErrorWeight
	param.DigitalCode = fc.DigitalCode
	param.ShelfNo = fc.ShelfNo
	param.ContractNumber = fc.ContractNumber
	param.AccountNum = fc.AccountNum
	param.DyeFactoryColorCode = fc.DyeFactoryColorCode
	param.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
	param.ProductWidth = fc.ProductWidth
	param.ProductGramWeight = fc.ProductGramWeight
	param.StockRemark = fc.StockRemark
	param.Remark = fc.Remark
	param.InternalRemark = fc.InternalRemark
	param.ScanUserId = fc.ScanUserId
	param.ScanUserName = fc.ScanUserName
	param.ScanTime = tools.QueryTime(fc.ScanTime.Date())
	param.OrderTime = fc.OrderTime
	return param
}

func (u FpmSaleAllocateInOrderService) UpdateStatusWait(ctx context.Context, tx *mysql_base.Tx, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {

	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, updateItems, err = repo.UpdateStatusWait(ctx, id)
	if err != nil {
		return
	}
	// 过一层校验
	err = InspectOrder(ctx, structure.InspectOrderData{OrderTime: data.WarehouseInTime.ToTime()})
	if err != nil {
		return
	}
	return
}

func (u FpmSaleAllocateInOrderService) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, err = repo.UpdateStatusCancel(ctx, id)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, err = repo.UpdateStatusReject(ctx, id)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) Get(ctx context.Context, req *structure.GetFpmSaleAllocateInOrderQuery) (data structure.GetFpmSaleAllocateInOrderData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	data, err = repo.Get(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) GetList(ctx context.Context, req *structure.GetFpmInOrderListQuery) (list structure.GetFpmSaleAllocateInOrderDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFpmSaleAllocateInOrderRepo(tx)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u FpmSaleAllocateInOrderService) UpdateDetailStockDetailId(ctx context.Context, tx *mysql_base.Tx, ids map[uint64]uint64, sumIds map[uint64]uint64, id uint64, isUpdateStock bool, order structure.GetFpmSaleAllocateInOrderData) (err error) {
	var (
		in2ArrangeFcDetail = make(map[uint64]uint64)
		in2ArrangeFcSumId  = make(map[uint64]uint64)
	)
	repo := aggs.NewFpmOrderFcRepo(tx)
	err = repo.UpdateDetailStockDetailId2OrderItemFc(ctx, ids, sumIds, id, isUpdateStock)
	if err != nil {
		return
	}
	arrangeOrder, err := aggs.NewFpmArrangeOrderRepo(tx).Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: order.ArrangeOrderId})
	//if arrangeOrder.PickUpGoodsInOrder == true {
	// 先创建从进仓fc的ArrangeItemFcId到库存ID的映射
	for _, item := range order.ItemData {
		for _, fc := range item.ItemFCData {
			in2ArrangeFcDetail[fc.ArrangeItemFcId] = ids[fc.Id]
			in2ArrangeFcSumId[fc.ArrangeItemFcId] = sumIds[fc.Id]
		}
	}

	// 创建映射存储item的ID与sum_stock_id的关系
	itemToSumStockMap := make(map[uint64]uint64)

	for _, item := range arrangeOrder.ItemData {
		for _, fc := range item.ItemFCData {
			// 只更新有变动的记录
			if fc.StockId > 0 || fc.SumStockId > 0 {
				// 查询现有记录mysql.FirstFpmArrangeOrderItemFcByID(tx, fc.Id)
				arrangeItemFc, exist, err := dao.FirstFpmArrangeOrderItemFcByID(tx, fc.Id)
				if exist && err == nil {
					// 更新StockId和SumStockId字段
					arrangeItemFc.StockId = in2ArrangeFcDetail[fc.Id]
					arrangeItemFc.SumStockId = in2ArrangeFcSumId[fc.Id]
					// 记录item和sum_stock_id的关系
					if in2ArrangeFcSumId[fc.Id] > 0 {
						itemToSumStockMap[item.Id] = in2ArrangeFcSumId[fc.Id]
					}
					// 保存回数据库mysql.MustUpdateFpmArrangeOrderItemFc(tx, arrangeItemFc)
					_, err = dao.MustUpdateFpmArrangeOrderItemFc(tx, arrangeItemFc)
				}
			}
		}
	}

	// 更新item的sum_stock_id
	for itemId, sumStockId := range itemToSumStockMap {
		arrangeItem, exist, err := dao.FirstFpmArrangeOrderItemByID(tx, itemId)
		if exist && err == nil {
			arrangeItem.SumStockId = sumStockId
			_, err = dao.MustUpdateFpmArrangeOrderItem(tx, arrangeItem)
			if err != nil {
				return err
			}
		}
	}
	//}
	return
}

func (u FpmSaleAllocateInOrderService) UpdateStockParams(ctx context.Context, tx *mysql_base.Tx, addItems structure.AddStockProductDetailParamList, ids map[uint64]uint64, sumIds map[uint64]uint64, saleAllocateOutId uint64) {
	var (
		stockReport product3.StockProduct
		err         error
	)

	// 1. 遍历ids的值，查询对应的库存详情，将状态改为已占用(2)
	for _, detailId := range ids {
		// 查询库存详情
		detail, err := dao.MustFirstStockProductDetailByID(tx, detailId)
		if err != nil {
			return
		}
		detail.Status = 2 // 2 表示已占用
		// 更新库存详情
		_, err = dao.MustUpdateStockProductDetail(tx, detail)
		if err != nil {
			return
		}
	}
	var stockId uint64
	var bookRollUpdate int
	var bookWeightUpdate int
	// 2. 得到库存汇总记录ID
	for k, sumId := range sumIds {
		var bookRoll int
		var bookWeight int
		stockReport, err = dao.MustFirstStockProductByID(tx, sumId)
		if err != nil {
			return
		}
		stockId = sumId // 记录库存ID

		// 3. 遍历addItems，累加roll和weight到对应的汇总库存的book_roll和book_weight
		for _, item := range addItems {
			if item.AddWeightItemId != k {
				continue
			}
			// 累加匹数和重量
			bookRoll += item.Roll
			bookWeight += item.Weight
			bookRollUpdate += bookRoll
			bookWeightUpdate += bookWeight
		}
		stockReport.BookRoll = stockReport.BookRoll + bookRoll
		stockReport.BookWeight = stockReport.BookWeight + bookWeight
		// 更新汇总库存
		_, err = dao.MustUpdateStockProduct(tx, stockReport)
		if err != nil {
			return
		}
	}
	// 4. 根据 saleAllocateOutId 查找配布单
	if saleAllocateOutId > 0 && stockId > 0 {
		// 查询销售调拨出仓单
		outOrder, err := aggs.NewFpmOutOrderRepo(tx).Get(ctx, &structure.GetFpmOutOrderQuery{Id: saleAllocateOutId})
		if err != nil {
			return
		}

		// 获取配布单ID
		arrangeOrderId := outOrder.ArrangeOrderId // 使用 ArrangeOrderId 而非 SrcId
		if arrangeOrderId <= 0 {
			return
		}

		// 查询配布单
		arrangeOrder, err := aggs.NewFpmArrangeOrderRepo(tx).Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: arrangeOrderId})
		if err != nil {
			return
		}

		// 获取销售单ID
		saleOrderId := arrangeOrder.SrcId
		if saleOrderId <= 0 {
			return
		}

		// 直接新建一条库存占用日志记录
		newBookLog := new(product3.StockProductBookLog)
		newBookLog.Id = vars.Snowflake.GenerateId().UInt64()
		newBookLog.OrderId = saleOrderId                           // 订单ID
		newBookLog.StockId = stockId                               // 库存ID
		newBookLog.BookRoll = bookRollUpdate                       // 匹数
		newBookLog.BookWeight = bookWeightUpdate                   // 重量
		newBookLog.OrderType = common.BookOrderTypeProductSalePass // 订单类型（成品销售调拨进仓单审核）
		newBookLog.StockType = 3
		// 通过销售单ID查询销售单号
		// 创建销售订单DAO
		saleOrderDao := sale_dao.NewSaleProductOrderDao(ctx, false)
		saleOrder, exist, err := saleOrderDao.FirstById(ctx, tx, saleOrderId)
		if err == nil && exist && saleOrder.Id > 0 && saleOrder.OrderNo != "" {
			// 如果查询到销售单，使用销售单号
			newBookLog.OrderNo = saleOrder.OrderNo
		}
		// 创建新的库存占用日志
		// 创建库存日志DAO并调用创建方法
		bookLogDao := dao.NewStockProductBookLogDao(tx)
		_, err = bookLogDao.MustCreateStockProductBookLog(*newBookLog)
		if err != nil {
			return
		}
	}
}

// CreateSaleOutOrderAfterInOrderPass 在销售调拨进仓单审核通过后创建销售出仓单
func (u FpmSaleAllocateInOrderService) CreateSaleOutOrderAfterInOrderPass(ctx context.Context, tx *mysql_base.Tx, order structure.GetFpmSaleAllocateInOrderData, outOrder structure.GetFpmOutOrderData, stockIds map[uint64]uint64, sumStockIds map[uint64]uint64) (err error) {
	var (
		saleOutOrderParam structure.AddFpmSaleOutOrderParam
		saleOutRepo       = aggs.NewFpmSaleOutOrderRepo(tx)
		repo              = aggs.NewFpmInOrderRepo(tx)
	)

	// 获取销售单信息
	saleOrder, err := sale3.NewSaleClient().GetSaleOrderByAllocateInOrderId(ctx, order.Id)
	if err != nil {
		return
	}

	// 如果不是自提，直接创建出仓单
	if saleOrder.PickUpGoodsInOrder != true {
		// 将GetFpmSaleAllocateInOrderData转换为GetFpmInOrderData类型
		inOrderData := structure.GetFpmInOrderData{
			RecordData:            order.RecordData,
			ItemData:              order.ItemData,
			SaleSystemId:          order.SaleSystemId,
			WarehouseId:           order.WarehouseId,
			WarehouseInTime:       order.WarehouseInTime,
			StoreKeeperId:         order.StoreKeeperId,
			Remark:                order.Remark,
			BusinessClose:         order.BusinessClose,
			BusinessCloseUserId:   order.BusinessCloseUserId,
			BusinessCloseUserName: order.BusinessCloseUserName,
			BusinessCloseTime:     order.BusinessCloseTime,
			DepartmentId:          order.DepartmentId,
			BizUnitId:             order.CustomerId,
			OrderNo:               order.OrderNo,
			AuditStatus:           order.AuditStatus,
			AuditorId:             order.AuditorId,
			AuditorName:           order.AuditorName,
			SaleFollowerId:        order.SaleFollowerId,
			AuditTime:             order.AuditTime,
			TotalRoll:             order.TotalRoll,
			TotalWeight:           order.TotalWeight,
			TotalLength:           order.TotalLength,
			UnitId:                order.UnitId,
			SrcId:                 order.ArrangeOrderId, // 使用ArrangeOrderId作为SrcId
			SrcOrderNo:            order.ArrangeOrderNo, // 使用ArrangeOrderNo作为SrcOrderNo
			SaleUserId:            order.SaleUserId,
			DriverId:              order.DriverId,
			LogisticsCompanyId:    order.LogisticsCompanyId,
			SaleMode:              saleOrder.SaleMode,
		}
		// 生成一张出仓单
		saleOutOrderParam = u.swapOrderData2SaleOutParam(inOrderData, outOrder)
		for _, item := range order.ItemData {
			itemParam := u.swapOrderItemData2SaleOutItemParam(item)
			// 清空原有的ItemFCData，防止重复
			itemParam.ItemFCData = make([]structure.AddFpmOutOrderItemFcParam, 0)
			for _, fc := range item.ItemFCData {
				o := u.swapOrderItemFcData2SaleOutItemFcParam(fc)
				if _, ok := stockIds[fc.Id]; ok {
					// 如果当前入库单的库存映射中存在该细码的库存ID，直接使用
					o.StockId = stockIds[fc.Id]
					o.SumStockId = sumStockIds[fc.Id]
					if itemParam.SumStockId != o.SumStockId {
						itemParam.SumStockId = sumStockIds[fc.Id]
					}
				}
				itemParam.ItemFCData = append(itemParam.ItemFCData, o)
			}
			saleOutOrderParam.ItemData = append(saleOutOrderParam.ItemData, itemParam)
		}
		_, err = saleOutRepo.Add(ctx, &saleOutOrderParam)
		if err != nil {
			return err
		}
		return nil
	}

	// 如果是自提，则需要检查所有同源入库单
	if saleOrder.PickUpGoodsInOrder == true {
		// 生成一张出仓单
		if u.CheckIsPass(ctx, saleOrder, tx, order.ArrangeOrderId) {
			// 获取成品销售单下所有的配布单
			arrangeOrders, _, err2 := aggs.NewFpmArrangeOrderRepo(tx).GetList(ctx, &structure.GetFpmArrangeOrderListQuery{SrcOrderNo: saleOrder.OrderNo})
			if err2 != nil {
				return err2
			}
			// 初始化销售出仓单参数
			// 将GetFpmSaleAllocateInOrderData转换为GetFpmInOrderData类型
			inOrderData := structure.GetFpmInOrderData{
				RecordData:            order.RecordData,
				ItemData:              order.ItemData,
				SaleSystemId:          order.SaleSystemId,
				WarehouseId:           order.WarehouseId,
				WarehouseInTime:       order.WarehouseInTime,
				StoreKeeperId:         order.StoreKeeperId,
				Remark:                order.Remark,
				BusinessClose:         order.BusinessClose,
				BusinessCloseUserId:   order.BusinessCloseUserId,
				BusinessCloseUserName: order.BusinessCloseUserName,
				BusinessCloseTime:     order.BusinessCloseTime,
				DepartmentId:          order.DepartmentId,
				BizUnitId:             order.CustomerId,
				OrderNo:               order.OrderNo,
				AuditStatus:           order.AuditStatus,
				AuditorId:             order.AuditorId,
				AuditorName:           order.AuditorName,
				SaleFollowerId:        order.SaleFollowerId,
				AuditTime:             order.AuditTime,
				TotalRoll:             order.TotalRoll,
				TotalWeight:           order.TotalWeight,
				TotalLength:           order.TotalLength,
				UnitId:                order.UnitId,
				SrcId:                 order.ArrangeOrderId, // 使用ArrangeOrderId作为SrcId
				SrcOrderNo:            order.ArrangeOrderNo, // 使用ArrangeOrderNo作为SrcOrderNo
				SaleUserId:            order.SaleUserId,
				DriverId:              order.DriverId,
				LogisticsCompanyId:    order.LogisticsCompanyId,
				SaleMode:              saleOrder.SaleMode,
			}
			saleOutOrderParam = u.swapOrderData2SaleOutParam(inOrderData, outOrder)
			if order.ArrangeOrderId > 0 {
				// 遍历获取到的所有配布单
				for _, arrangeOrder := range arrangeOrders {
					// 获取配布单明细
					arrangeRepo := aggs.NewFpmArrangeOrderRepo(tx)
					arrangeItems, errFind := arrangeRepo.FindItemByOrderId(ctx, arrangeOrder.Id)
					if errFind != nil {
						middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "获取配布单明细失败"))
						continue
					}

					// 获取配布单明细的ID列表
					itemIds := make([]uint64, 0, len(arrangeItems))
					for _, item := range arrangeItems {
						itemIds = append(itemIds, item.Id)
					}

					// 获取配布单细码数据
					fcList, errFc := dao.FindFpmArrangeOrderItemFcByParenTIDs(tx, itemIds)
					if errFc != nil {
						middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "获取配布单细码数据失败"))
					}

					// 构建细码映射关系
					fcMap := make(map[uint64][]product3.FpmArrangeOrderItemFc)
					for _, fc := range fcList {
						fcMap[fc.ParentId] = append(fcMap[fc.ParentId], fc)
					}

					// 根据配布单类型决定如何处理数据
					if arrangeOrder.OutOrderType == product2.WarehouseGoodOutTypeSaleAllocate {
						inOrder, err3 := product_pb.NewFpmInOrderClient().GetFpmInOrderByArrangeId(ctx, arrangeOrder.Id)
						if err3 != nil {
							return
						}
						getInOrder, err4 := repo.Get(ctx, &structure.GetFpmInOrderQuery{Id: inOrder.Id})
						if err4 != nil {
							return
						}

						// 如果是销调类型，使用进仓单数据
						for _, item := range getInOrder.ItemData {
							// 使用转换函数确保所有字段正确复制
							itemParam := u.swapOrderItemData2SaleOutItemParam(item)
							// 清空原有的ItemFCData，防止重复
							itemParam.ItemFCData = make([]structure.AddFpmOutOrderItemFcParam, 0)
							// 直接使用进仓单细码数据
							for _, fc := range item.ItemFCData {
								o := u.swapOrderItemFcData2SaleOutItemFcParam(fc)
								if _, ok := stockIds[fc.Id]; ok {
									// 如果当前入库单的库存映射中存在该细码的库存ID，直接使用
									o.StockId = stockIds[fc.Id]
									o.SumStockId = sumStockIds[fc.Id]
									itemParam.ItemFCData = append(itemParam.ItemFCData, o)
								} else {
									itemParam.ItemFCData = append(itemParam.ItemFCData, o)
								}
							}
							// 添加到销售出仓单参数中
							saleOutOrderParam.ItemData = append(saleOutOrderParam.ItemData, itemParam)
						}
					} else {
						// 如果是出货类型，直接使用配布单的fc数据
						// 将细码数据添加到配布单明细中
						for i := range arrangeItems {
							if fcs, ok := fcMap[arrangeItems[i].Id]; ok {
								arrangeItems[i].ItemFCData = make(structure.GetFpmArrangeOrderItemFcDataList, 0, len(fcs))
								for _, fc := range fcs {
									// 创建细码数据
									var fcData structure.GetFpmArrangeOrderItemFcData
									fcData.Id = fc.Id
									fcData.ParentId = fc.ParentId
									fcData.Roll = fc.Roll
									fcData.WarehouseId = fc.WarehouseId
									fcData.WarehouseBinId = fc.WarehouseBinId
									fcData.StockId = fc.StockId
									fcData.SumStockId = fc.SumStockId
									fcData.VolumeNumber = fc.VolumeNumber
									fcData.BaseUnitWeight = fc.BaseUnitWeight
									fcData.PaperTubeWeight = fc.PaperTubeWeight
									fcData.WeightError = fc.WeightError
									fcData.SettleErrorWeight = fc.SettleErrorWeight
									fcData.SettleWeight = fc.SettleWeight
									fcData.Length = fc.Length
									fcData.DigitalCode = fc.DigitalCode
									fcData.ShelfNo = fc.ShelfNo
									fcData.DyeFactoryColorCode = fc.DyeFactoryColorCode
									fcData.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
									fcData.ProductWidth = fc.ProductWidth
									fcData.ProductGramWeight = fc.ProductGramWeight
									fcData.StockRemark = fc.StockRemark
									fcData.Remark = fc.Remark
									fcData.InternalRemark = fc.InternalRemark
									fcData.ContractNumber = fc.ContractNumber
									fcData.AccountNum = fc.AccountNum
									fcData.UnitId = fc.UnitId
									arrangeItems[i].ItemFCData = append(arrangeItems[i].ItemFCData, fcData)
								}
							}
						}

						// 将配布单明细转换为销售出仓单明细
						for _, item := range arrangeItems {
							// 创建销售出仓单明细
							outOrderItem := structure.AddFpmOutOrderItemParam{
								ProductId:              item.ProductId,
								ProductCode:            item.ProductCode,
								ProductName:            item.ProductName,
								CustomerId:             item.CustomerId,
								ProductColorId:         item.ProductColorId,
								ProductColorCode:       item.ProductColorCode,
								ProductColorName:       item.ProductColorName,
								DyeFactoryColorCode:    item.DyeFactoryColorCode,
								DyeFactoryDyelotNumber: item.DyeFactoryDyelotNumber,
								ProductWidth:           item.ProductWidth,
								ProductGramWeight:      item.ProductGramWeight,
								ProductLevelId:         item.ProductLevelId,
								ProductRemark:          item.ProductRemark,
								ProductCraft:           item.ProductCraft,
								ProductIngredient:      item.ProductIngredient,
								OutRoll:                item.ArrangeRoll,
								TotalWeight:            item.ArrangeWeight,
								WeightError:            item.WeightError,
								PaperTubeWeight:        item.PaperTubeWeight,
								SettleWeight:           item.SettleWeight,
								UnitId:                 item.UnitId,
								AuxiliaryUnitId:        item.AuxiliaryUnitId,
								OutLength:              item.ArrangeLength,
								Remark:                 item.Remark,
								SumStockId:             item.SumStockId,
								ArrangeItemId:          item.Id,
							}

							// 转换细码数据
							for _, fc := range item.ItemFCData {
								outOrderItemFc := structure.AddFpmOutOrderItemFcParam{
									WarehouseId:            fc.WarehouseId,
									WarehouseBinId:         fc.WarehouseBinId,
									Roll:                   fc.Roll,
									BaseUnitWeight:         fc.BaseUnitWeight,
									WeightError:            fc.WeightError,
									PaperTubeWeight:        fc.PaperTubeWeight,
									SettleErrorWeight:      fc.SettleErrorWeight,
									SettleWeight:           fc.SettleWeight,
									Length:                 fc.Length,
									Remark:                 fc.Remark,
									StockId:                fc.StockId,
									SumStockId:             fc.SumStockId,
									DigitalCode:            fc.DigitalCode,
									ShelfNo:                fc.ShelfNo,
									DyeFactoryColorCode:    fc.DyeFactoryColorCode,
									DyeFactoryDyelotNumber: fc.DyeFactoryDyelotNumber,
									ProductWidth:           fc.ProductWidth,
									ProductGramWeight:      fc.ProductGramWeight,
									StockRemark:            fc.StockRemark,
									InternalRemark:         fc.InternalRemark,
									ContractNumber:         fc.ContractNumber,
									AccountNum:             fc.AccountNum,
									UnitId:                 fc.UnitId,
									VolumeNumber:           fc.VolumeNumber,
								}
								outOrderItem.ItemFCData = append(outOrderItem.ItemFCData, outOrderItemFc)
							}

							// 添加到销售出仓单参数中
							saleOutOrderParam.ItemData = append(saleOutOrderParam.ItemData, outOrderItem)
						}
					}
				}
			}

			// 创建销售出仓单
			_, err := saleOutRepo.Add(ctx, &saleOutOrderParam)
			if err != nil {
				return err
			}

		}
	}

	return nil
}
