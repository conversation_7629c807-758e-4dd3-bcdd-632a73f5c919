package sale

import (
	"context"
	"fmt"
	"hcscm/aggs/basic_data/warehouse"
	"hcscm/aggs/gen_order_no"
	product2 "hcscm/aggs/product"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	sale_common "hcscm/common/sale"
	common "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	basic_data_pb "hcscm/extern/pb/basic_data"
	info_base_data_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	biz_pb "hcscm/extern/pb/biz_unit"
	emp_pb "hcscm/extern/pb/employee"
	payable_pb "hcscm/extern/pb/payable"
	sale_system_pb "hcscm/extern/pb/sale_system"
	shouldcollectorderpb "hcscm/extern/pb/should_collect_order"
	user_pb "hcscm/extern/pb/user"
	"hcscm/log"
	"hcscm/middleware"
	typeMysql "hcscm/model/mysql/basic_data/type_basic_data"
	typeMysqlDao "hcscm/model/mysql/basic_data/type_basic_data/dao"
	"hcscm/model/mysql/mysql_base"
	_product "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	model "hcscm/model/mysql/sale"
	saleDao "hcscm/model/mysql/sale/dao"
	mysqlSystem "hcscm/model/mysql/system"
	"hcscm/model/mysql/tenant_management"
	tenantManagementDao "hcscm/model/mysql/tenant_management/dao"
	basic_data_structure "hcscm/structure/basic_data"
	structure_type "hcscm/structure/basic_data/type_basic_data"
	warehouse2 "hcscm/structure/basic_data/warehouse"
	structure_product "hcscm/structure/product"
	structure "hcscm/structure/sale"
	"hcscm/structure/should_collect_order"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strconv"
	"strings"
	"time"
)

type ISaleTransferOrderRepo interface {
	Create(ctx context.Context, param structure.AddSaleTransferOrderParam) (id uint64, err error)
	Search(ctx context.Context, query structure.GetSaleTransferOrderListQuery) (list structure.GetSaleTransferOrderDataList, total int, err error)
	GetDetailList(ctx context.Context, query structure.GetSaleTransferDetailListQuery) (list structure.GetSaleTransferOrderDetailInfoDataList, total int, err error)
	Get(ctx context.Context, query structure.GetSaleTransferOrderQuery) (data structure.GetSaleTransferOrderDetailData, err error)
	UpdatePassIn(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, fpmInOrderIDs []uint64, err error)
	UpdatePassOut(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, fpmSaleOutOrderIDs []uint64, err error)
	UpdateCancel(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error)
	UpdateReject(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error)
	Update(ctx context.Context, param structure.UpdateSaleTransferOrderParam) (id uint64, err error)
	UpdateDetail(ctx context.Context, param *structure.BackWrite2SrcItemParam) (err error)
	UpdateRollback(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, backWriteMap map[uint64]*structure.BackWrite2SrcItemParam, err error)
	GetHomePageSaleTransferOrder(ctx context.Context) (data structure.GetHomePageSaleTransferOrderData, err error)
	GetLatestOrderPrice(ctx context.Context, query structure.GetLatestOrderPriceQuery) (data structure.GetLatestOrderPriceData, err error)
	// 退货
	AddReturn(ctx context.Context, param *structure.AddSaleTransferReturnOrderParam) (id uint64, err error)
	UpdateReturn(ctx context.Context, param *structure.UpdateSaleTransferReturnOrderParam) (id uint64, err error)
	GetReturn(ctx context.Context, query *structure.GetSaleTransferOrderQuery) (data *structure.GetSaleTransferReturnOrderDetailData, err error)
	UpdatePassReturn(ctx context.Context, param *structure.GetSaleTransferReturnOrderDetailData) (data structure.ResSaleTransferOrderIDData, backWriteMap map[uint64]*structure.BackWrite2SrcItemParam, err error)
	UpdateCancelReturn(ctx context.Context, param *structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error)
	UpdateRejectReturn(ctx context.Context, param *structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error)
	UpdateWaitReturn(ctx context.Context, param *structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error)
	GetTx() *mysql_base.Tx
}

type saleTransferOrderRepo struct {
	saleTransferOrderDao          saleDao.ISaleTransferOrderDao
	saleTransferOrderDetailDao    saleDao.ISaleTransferOrderDetailDao
	saleTransferOrderWeightDao    saleDao.ISaleTransferOrderWeightDao
	productColorClient            product.ProductColorClient
	productClient                 product.ProductClient
	infoBaseMeasurementUnitClient info_base_data_pb.InfoBaseMeasurementUnitClient
	clientBizUnitService          biz_pb.IClientBizUnitService
	employeeClient                *emp_pb.ClientEmployeeService
	productSaleClient             shouldcollectorderpb.ProductSaleClient
	clientPayableService          payable_pb.IClientPayableService
	tenantManagementDao           tenantManagementDao.ITenantManagementDao
	tx                            *mysql_base.Tx
	fpmInOrderRepo                product2.IFpmInOrderRepo
	fpmSaleOutOrderRepo           *product2.FpmSaleOutOrderRepo
	warehouseRepo                 warehouse.IWarehouseRepo
	isCache                       bool
}

// todo:没有保证事务原子性 修改工程大
func NewSaleTransferOrderRepo(ctx context.Context, tx *mysql_base.Tx, isCache bool) ISaleTransferOrderRepo {
	return &saleTransferOrderRepo{
		saleTransferOrderDao:          saleDao.NewSaleTransferOrderDao(ctx, isCache),
		saleTransferOrderDetailDao:    saleDao.NewSaleTransferOrderDetailDao(ctx, isCache),
		saleTransferOrderWeightDao:    saleDao.NewSaleTransferOrderWeightDao(ctx, isCache),
		productColorClient:            product.NewProductColorClient(),
		productClient:                 product.NewProductClient(),
		infoBaseMeasurementUnitClient: info_base_data_pb.NewInfoBaseMeasurementUnitClient(),
		clientBizUnitService:          biz_pb.NewClientBizUnitService(),
		employeeClient:                emp_pb.NewClientEmployeeService(),
		productSaleClient:             shouldcollectorderpb.NewProductSaleClient(),
		clientPayableService:          payable_pb.NewClientPayableService(),
		tenantManagementDao:           tenantManagementDao.NewTenantManagementDao(),
		fpmInOrderRepo:                product2.NewFpmInOrderRepo(tx),
		fpmSaleOutOrderRepo:           product2.NewFpmSaleOutOrderRepo(tx),
		warehouseRepo:                 warehouse.NewWarehouseRepo(tx),
		tx:                            tx,
		isCache:                       isCache,
	}
}

func (repo *saleTransferOrderRepo) Create(ctx context.Context, param structure.AddSaleTransferOrderParam) (id uint64, err error) {
	var (
		saleTransferOrder   model.SaleTransferOrder
		productMaps         = make(map[uint64]*product.ProductRes)
		productNewMaps      = make(map[string]*product.ProductRes) // 避免同单据重复提交新数据导致的重复新增
		productColorMaps    = make(map[uint64]*product.ProductColorRes)
		productColorNewMaps = make(map[string]*product.ProductColorRes)
		orderNo             string
		noNumber            int64
		orderPrefix         mysqlSystem.OrderPrefix
		prefix, dateFormat  string
		numLength           int
		exist               bool
		sale_sys_svc        = sale_system_pb.NewSaleSystemClient()
		saleSysData         = sale_system_pb.Res{}
	)
	param.AdjustParam()
	saleTransferOrder = model.NewSaleTransferOrderForSale(ctx, param)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_system_pb.Req{Id: param.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(repo.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.SaleTransferOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.SaleTransferOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	orderNo, noNumber, err = gen_order_no.NewOrderNoRepo(ctx, repo.tx).GenOrderNoByYearWithOutSymbol(ctx, "sale", saleTransferOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	saleTransferOrder.OrderNo = orderNo
	saleTransferOrder.Number = int(noNumber)
	// 新增客户
	if param.CustomerID == 0 {
		cid, _err := biz_pb.NewClientBizUnitService().Create(ctx, common_system.CategoryCustomer, param.SaleSystemId, param.CustomerName, true)
		if _err != nil {
			err = _err
			return
		}
		saleTransferOrder.CustomerID = cid
	}
	saleTransferOrder, err = repo.saleTransferOrderDao.MustCreate(repo.tx, saleTransferOrder)
	if err != nil {
		return
	}

	productMaps, err = repo.productClient.GetProductMapByIds(ctx, mysql_base.GetUInt64List(param.SaleTransferOrderDetailParamList, "product_id"))
	if err != nil {
		return
	}

	productColorMaps, err = repo.productColorClient.GetProductColorMapByIds(ctx, mysql_base.GetUInt64List(param.SaleTransferOrderDetailParamList, "product_color_id"))
	if err != nil {
		return
	}

	for _, saleTransferOrderDetailInfo := range param.SaleTransferOrderDetailParamList {
		err = saleTransferOrderDetailInfo.Validate()
		if err != nil {
			return
		}
		var (
			finishProduct           _product.FinishProduct
			finishProductColor      _product.FinishProductColor
			saleTransferOrderDetail model.SaleTransferOrderDetail
			productMap              *product.ProductRes
			productColorMap         *product.ProductColorRes
			ok                      bool
			colorId                 uint64
		)
		if productMap, ok = productMaps[saleTransferOrderDetailInfo.ProductID]; !ok {
			if productMap, ok = productNewMaps[fmt.Sprintf("%v%v", saleTransferOrderDetailInfo.ProductCode, saleTransferOrderDetailInfo.ProductName)]; !ok {
				finishProduct.Id = vars.Snowflake.GenerateId().UInt64()
				finishProduct.FinishProductName = saleTransferOrderDetailInfo.ProductName
				finishProduct.FinishProductCode = saleTransferOrderDetailInfo.ProductCode
				finishProduct.FinishProductFullName = saleTransferOrderDetailInfo.ProductName
				finishProduct.Status = common_system.StatusEnable
				err = repo.productClient.MustCreateProduct(ctx, finishProduct)
				if err != nil {
					return
				}
				productMap = new(product.ProductRes)
				productMap.Id = finishProduct.Id
				productMap.FinishProductCode = finishProduct.FinishProductCode
				productMap.FinishProductName = finishProduct.FinishProductName
				productMap.FinishProductFullName = finishProduct.FinishProductFullName
				productMaps[finishProduct.Id] = productMap
				productNewMaps[fmt.Sprintf("%v%v", productMap.FinishProductCode, productMap.FinishProductName)] = productMap
			}
		}

		if productColorMap, ok = productColorMaps[saleTransferOrderDetailInfo.ProductColorID]; !ok && (saleTransferOrderDetailInfo.ProductColorCode != "" || saleTransferOrderDetailInfo.ProductColorName != "") {
			if productColorMap, ok = productColorNewMaps[fmt.Sprintf("%v%v", saleTransferOrderDetailInfo.ProductColorCode, saleTransferOrderDetailInfo.ProductColorName)]; !ok {
				finishProductColor.Id = vars.Snowflake.GenerateId().UInt64()
				finishProductColor.ProductColorName = saleTransferOrderDetailInfo.ProductColorName
				finishProductColor.ProductColorCode = saleTransferOrderDetailInfo.ProductColorCode
				finishProductColor.FinishProductId = productMap.Id
				finishProductColor.Status = common_system.StatusEnable
				err = repo.productColorClient.MustCreateProductColor(ctx, finishProductColor)
				if err != nil {
					return
				}
				productColorMap = new(product.ProductColorRes)
				colorId = finishProductColor.Id
				productColorMap.Id = finishProductColor.Id
				productColorMap.ProductColorCode = finishProductColor.ProductColorCode
				productColorMap.ProductColorName = finishProductColor.ProductColorName
				productColorMap.FinishProductId = finishProductColor.FinishProductId
				productColorMaps[productColorMap.Id] = productColorMap
				productColorNewMaps[fmt.Sprintf("%v%v", saleTransferOrderDetailInfo.ProductColorCode, saleTransferOrderDetailInfo.ProductColorName)] = productColorMap
			}
		}

		if productColorMap, ok = productColorMaps[saleTransferOrderDetailInfo.ProductColorID]; ok {
			colorId = productColorMap.Id
		}

		saleTransferOrderDetail, err = repo.saleTransferOrderDetailDao.MustCreate(repo.tx, model.NewSaleTransferOrderDetail(saleTransferOrder,
			saleTransferOrderDetailInfo, productMap.Id, colorId))
		if err != nil {
			return
		}
		// if len(saleTransferOrderDetailInfo.SaleTransferOrderWeightParamList) == 0 {
		//	var (
		//		saleTransferOrderWeight     model.SaleTransferOrderWeight
		//		saleTransferOrderWeightInfo structure.SaleTransferOrderWeightParam
		//	)
		//
		//	if saleTransferOrderDetailInfo.Roll != 0 || saleTransferOrderDetailInfo.SaleSettleWeight != 0 {
		//		saleTransferOrderWeightInfo.SaleWeight = saleTransferOrderDetailInfo.SaleSettleWeight
		//		saleTransferOrderWeightInfo.SupplierWeight = saleTransferOrderDetailInfo.SaleSettleWeight
		//		saleTransferOrderWeight = model.NewSaleTransferOrderWeight(saleTransferOrder, saleTransferOrderDetail, saleTransferOrderWeightInfo)
		//		saleTransferOrderWeight, err = repo.saleTransferOrderWeightDao.MustCreate(repo.tx, saleTransferOrderWeight)
		//		if err != nil {
		//			return
		//		}
		//	}
		// } else {
		// if len(saleTransferOrderDetailInfo.SaleTransferOrderWeightParamList) != (saleTransferOrderDetailInfo.Roll / vars.Roll) {
		//	err = middleware.WarnLog(errors.NewError(errors.ErrCodeWeightRollMustEqualTotalRoll))
		//	return
		// }
		for _, saleTransferOrderWeightInfo := range saleTransferOrderDetailInfo.SaleTransferOrderWeightParamList {
			var saleTransferOrderWeight model.SaleTransferOrderWeight
			saleTransferOrderWeight = model.NewSaleTransferOrderWeight(saleTransferOrder, saleTransferOrderDetail, saleTransferOrderWeightInfo)
			saleTransferOrderWeight, err = repo.saleTransferOrderWeightDao.MustCreate(repo.tx, saleTransferOrderWeight)
			if err != nil {
				return
			}
		}
	}
	// }

	return saleTransferOrder.Id, err
}

func (repo *saleTransferOrderRepo) Search(ctx context.Context, query structure.GetSaleTransferOrderListQuery) (list structure.GetSaleTransferOrderDataList, total int, err error) {
	var (
		saleTransferOrders             model.SaleTransferOrderList
		saleTransferOrderDetails       model.SaleTransferOrderDetailList
		customerMaps                   = make(map[uint64]string)
		employeeMaps                   = make(map[uint64]string)
		productSaleShouldCollectOrders shouldcollectorderpb.ProductSaleResList
		measurementUnits               = make(map[uint64]string)
		info                           = metadata.GetLoginInfo(ctx)
	)

	query.Platform = info.GetPlatform()

	saleTransferOrders, total, err = repo.saleTransferOrderDao.Search(repo.tx, query)
	if err != nil {
		return
	}

	customerMaps, err = repo.clientBizUnitService.GetBizUnitNameByIds(ctx, mysql_base.GetUInt64List(saleTransferOrders, "biz_unit_id"))
	if err != nil {
		return
	}

	employeeMaps, err = repo.employeeClient.GetEmployeeNameByIds(ctx, mysql_base.GetUInt64List(saleTransferOrders, "employee_id"))
	if err != nil {
		return
	}

	productSaleShouldCollectOrders, err = repo.productSaleClient.GetProductSaleBySrcIds(ctx, mysql_base.GetUInt64List(saleTransferOrders, "sale_transfer_order_id"))
	if err != nil {
		return
	}

	saleTransferOrderDetails, err = repo.saleTransferOrderDetailDao.FindBySaleTransferOrderIDs(repo.tx, mysql_base.GetUInt64List(saleTransferOrders, "sale_transfer_order_id"))
	if err != nil {
		return
	}

	userMap, err := user_pb.NewUserClient().GetUserNameByIds(ctx, mysql_base.GetUInt64List(saleTransferOrders, "user_id"))
	if err != nil {
		return
	}

	measurementUnits, err = repo.infoBaseMeasurementUnitClient.GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "measurement_unit_id"))
	if err != nil {
		return
	}

	for _, saleTransferOrder := range saleTransferOrders {
		var (
			countSaleWeightMap   = make(map[uint64]int)
			countReturnWeightMap = make(map[uint64]int)
		)
		productSaleShouldCollectOrder := productSaleShouldCollectOrders.PickBySrcID(saleTransferOrder.Id)
		_saleTransferOrderDetails := saleTransferOrderDetails.PickBySaleTransferOrderID(saleTransferOrder.Id)
		for _, detail := range _saleTransferOrderDetails {
			switch saleTransferOrder.OrderType {
			case common_system.OrderTypeSale:
				countSaleWeightMap[detail.MeasurementUnitID] += detail.SaleSettleWeight
			case common_system.OrderTypeReturn:
				countReturnWeightMap[detail.MeasurementUnitID] += detail.ReturnSettleWeight
			}
		}
		list = append(list, structure.GetSaleTransferOrderData{
			// Id:           saleTransferOrder.Id,
			OrderNo:      saleTransferOrder.OrderNo,
			OrderTime:    tools.MyTime(saleTransferOrder.OrderTime),
			CustomerID:   saleTransferOrder.CustomerID,
			CustomerName: customerMaps[saleTransferOrder.CustomerID],
			SaleUserID:   saleTransferOrder.SaleUserID,
			SaleUserName: employeeMaps[saleTransferOrder.SaleUserID],
			OrderRemark:  saleTransferOrder.Remark,
			Phone:        saleTransferOrder.Phone,
			Address:      saleTransferOrder.Address,
			ContactName:  saleTransferOrder.ContactName,
			RecordData: structure_base.RecordData{
				Id:             saleTransferOrder.Id,
				UpdateTime:     tools.MyTime(saleTransferOrder.UpdateTime),
				UpdaterId:      saleTransferOrder.UpdaterId,
				UpdateUserName: saleTransferOrder.UpdaterName,
				CreatorId:      saleTransferOrder.CreatorId,
				CreatorName:    saleTransferOrder.CreatorName,
				CreateTime:     tools.MyTime(saleTransferOrder.CreateTime),
			},
			AuditStatus:       saleTransferOrder.AuditStatus,
			AuditUserID:       saleTransferOrder.AuditorId,
			AuditUserName:     userMap[saleTransferOrder.AuditorId],
			AuditTime:         tools.MyTime(saleTransferOrder.AuditDate),
			AuditStatusName:   saleTransferOrder.AuditStatus.String(),
			CollectStatus:     common.CollectStatus(productSaleShouldCollectOrder.CollectStatus),
			CollectStatusName: common.CollectStatus(productSaleShouldCollectOrder.CollectStatus).String(),
			OrderType:         saleTransferOrder.OrderType,
			OrderTypeName:     saleTransferOrder.OrderType.String(),
			SaleMode:          saleTransferOrder.SaleMode,
			SaleModeName:      saleTransferOrder.SaleMode.String(),
			SaleAmount: func() (amount int) {
				for _, detail := range _saleTransferOrderDetails {
					switch saleTransferOrder.OrderType {
					case common_system.OrderTypeSale:
						amount += detail.TotalSaleAmount
					case common_system.OrderTypeReturn:
						amount -= detail.TotalReturnAmount
					}
				}
				return
			}(),
			LastChangeTime: tools.MyTime(saleTransferOrder.UpdateTime),
			TotalRoll: func() int {
				switch saleTransferOrder.OrderType {
				case common_system.OrderTypeSale:
					return saleTransferOrder.Roll
				case common_system.OrderTypeReturn:
					return -saleTransferOrder.Roll
				}
				return 0
			}(),
			MeasurementUnitDataList: func() (list []structure.MeasurementUnitData) {
				for _, saleTransferOrderDetail := range _saleTransferOrderDetails {
					list = append(list, structure.MeasurementUnitData{
						MeasurementUnitID:   saleTransferOrderDetail.MeasurementUnitID,
						MeasurementUnitName: measurementUnits[saleTransferOrderDetail.MeasurementUnitID],
						BasicData:           saleTransferOrderDetail.SaleSettleWeight,
					})
				}
				return
			}(),
			MergeWeightInfo: func() (str string) {
				switch saleTransferOrder.OrderType {
				case common_system.OrderTypeSale:
					for k, v := range countSaleWeightMap {
						fmtRound := tools.GetRound(v, 2)
						if str == "" {
							str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnits[k]
						} else {
							str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnits[k]
						}
					}
				case common_system.OrderTypeReturn:
					for k, v := range countReturnWeightMap {
						v *= -1
						fmtRound := tools.GetRound(v, 2)
						if str == "" {
							// divNum := tools.DecimalDiv(float64(v), vars.Weight)
							str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnits[k]
						} else {
							str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnits[k]
						}
					}
				}
				return
			}(),
		})
	}
	return
}

// 获取销售调货单详情分录行的那些列表
func (repo *saleTransferOrderRepo) GetDetailList(ctx context.Context, query structure.GetSaleTransferDetailListQuery) (
	list structure.GetSaleTransferOrderDetailInfoDataList, total int, err error) {

	var (
		details       model.SaleTransferOrderDetailList
		weightList    model.SaleTransferOrderWeightList
		orders        model.SaleTransferOrderList
		fpmInOrderFcs structure_product.GetFpmInOrderItemFcDataList
		orderMap      = make(map[uint64]model.SaleTransferOrder)
		employeeMaps  = make(map[uint64]string)
		orderIds      = set.NewUint64Set()
		detailIds     = set.NewUint64Set()
		weightIds     = set.NewUint64Set()
		tx            = repo.tx
		setProductIds = set.NewUint64Set()
		productIds    = make([]uint64, 0)
		colorIds      = make([]uint64, 0)
	)

	// 模糊获取成品ids
	if query.ProductCode != "" || query.ProductName != "" {
		productIds, _ = repo.productClient.GetProductIds(ctx, query.ProductCode, query.ProductName, "")
		setProductIds.AddList(productIds)
	}
	// 小程序的需求，后台没有
	if query.OrFindStr != "" {
		// 查成品
		productIds, _ = repo.productClient.GetProductIds(ctx, "", "", query.OrFindStr)
		setProductIds.AddList(productIds)
		// 查颜色
		colorIds, _ = repo.productColorClient.GetProductColorIds(ctx, "", "", query.OrFindStr)
		query.ProductColorIds = colorIds
	}

	query.ProductIds = setProductIds.List()

	details, total, err = repo.saleTransferOrderDetailDao.Search(tx, query)
	for _, detail := range details {
		orderIds.Add(detail.SaleTransferOrderID)
		detailIds.Add(detail.Id)
	}

	if len(orderIds.List()) > 0 {
		orders, err = repo.saleTransferOrderDao.GetByIds(tx, orderIds.List())
		if err != nil {
			return
		}
		for _, order := range orders {
			orderMap[order.Id] = order
		}
	}
	weightList, err = repo.saleTransferOrderWeightDao.FindBySaleTransferOrderDetailIDs(tx, detailIds.List())
	if err != nil {
		return
	}

	for _, weight := range weightList {
		weightIds.Add(weight.Id)
	}

	fpmInOrderFcs, err = repo.fpmInOrderRepo.GetFcList(ctx, &structure_product.GetFpmInOrderItemFcListQuery{SrcIds: weightIds.List()})
	if err != nil {
		return
	}

	productMaps, err := repo.productClient.GetProductMapByIds(ctx, mysql_base.GetUInt64List(details, "product_id"))
	if err != nil {
		return
	}

	productColorMaps, err := repo.productColorClient.GetProductColorMapByIds(ctx, mysql_base.GetUInt64List(details, "product_color_id"))
	if err != nil {
		return
	}

	employeeMaps, err = repo.employeeClient.GetEmployeeNameByIds(ctx, mysql_base.GetUInt64List(orders, "employee_id"))
	if err != nil {
		return
	}

	bizMap, err := repo.clientBizUnitService.GetBizUnitNameByIds(ctx, mysql_base.GetUInt64ListV2("biz_unit_id", orders, details))
	if err != nil {
		return
	}

	measurementUnits, err := repo.infoBaseMeasurementUnitClient.GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(details, "measurement_unit_id"))
	if err != nil {
		return
	}

	for _, detail := range details {
		var (
			getWeightList = make([]structure.GetSaleTransferOrderWeightInfo, 0)
		)
		getDetailData := &structure.GetSaleTransferOrderDetailInfoData{}
		detail.BuildRespForSaleDetail(getDetailData)

		getDetailData.SaleUserID = orderMap[detail.SaleTransferOrderID].SaleUserID
		getDetailData.SaleUserName = employeeMaps[orderMap[detail.SaleTransferOrderID].SaleUserID]
		getDetailData.OrderTime = tools.MyTime(orderMap[detail.SaleTransferOrderID].OrderTime)

		if productMaps[detail.ProductID] != nil {
			getDetailData.ProductCode = productMaps[detail.ProductID].FinishProductCode
			getDetailData.ProductName = productMaps[detail.ProductID].FinishProductName
		}
		if productColorMaps[detail.ProductColorID] != nil {
			getDetailData.ProductColorCode = productColorMaps[detail.ProductColorID].ProductColorCode
			getDetailData.ProductColorName = productColorMaps[detail.ProductColorID].ProductColorName
		}
		getDetailData.MeasurementUnitName = measurementUnits[detail.MeasurementUnitID]
		getDetailData.SupplierName = bizMap[detail.SupplierID]
		getDetailData.SupplierMeasurementUnitName = measurementUnits[detail.SupplierMeasurementUnitId]
		getDetailData.ReturnSupplierType = sale_common.ReturnSupplierTypeOriginalPrice
		getDetailData.ReturnSupplierTypeName = getDetailData.ReturnSupplierType.String()

		tmpWeightList := weightList.PickList(detail.Id)
		for _, weight := range tmpWeightList {
			fpmInOrderFc := fpmInOrderFcs.PickBySrcId(weight.Id)
			tmp := &structure.GetSaleTransferOrderWeightInfo{}
			weight.BuildWeightResp(tmp)
			tmp.StockId = fpmInOrderFc.StockId
			tmp.SumStockId = fpmInOrderFc.SumStockId
			getWeightList = append(getWeightList, *tmp)
		}

		getDetailData.ItemData = getWeightList
		list = append(list, *getDetailData)
	}

	return
}

func (repo *saleTransferOrderRepo) Get(ctx context.Context, query structure.GetSaleTransferOrderQuery) (data structure.GetSaleTransferOrderDetailData, err error) {
	var (
		saleTransferOrder        model.SaleTransferOrder
		saleTransferOrderDetails model.SaleTransferOrderDetailList
		saleTransferOrderWeights model.SaleTransferOrderWeightList
		measurementUnits         = make(map[uint64]string)
		customerMaps             = make(map[uint64]string)
		employeeMaps             = make(map[uint64]string)
		productMaps              = make(map[uint64]*product.ProductRes)
		productColorMaps         = make(map[uint64]*product.ProductColorRes)
		countSaleWeightMap       = make(map[uint64]int)
		tenantManagement         tenant_management.TenantManagement
		shouldCollectMoney       int // 总销售金额(应收金额)
		otherPrice               int // 总其他金额
	)

	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, query.Id)
	if err != nil {
		return
	}

	saleTransferOrderDetails, err = repo.saleTransferOrderDetailDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	saleTransferOrderWeights, err = repo.saleTransferOrderWeightDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	productMaps, err = repo.productClient.GetProductMapByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "product_id"))
	if err != nil {
		return
	}

	productColorMaps, err = repo.productColorClient.GetProductColorMapByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "product_color_id"))
	if err != nil {
		return
	}

	customerMaps, err = repo.clientBizUnitService.GetBizUnitNameByIds(ctx, mysql_base.GetUInt64ListV2("biz_unit_id", saleTransferOrder, saleTransferOrderDetails))
	if err != nil {
		return
	}

	employeeMaps, err = repo.employeeClient.GetEmployeeNameByIds(ctx, mysql_base.GetUInt64List(saleTransferOrder, "employee_id"))
	if err != nil {
		return
	}

	userNameMap, err := user_pb.NewUserClient().GetUserNameByIds(ctx, []uint64{saleTransferOrder.AuditorId})

	measurementUnits, err = repo.infoBaseMeasurementUnitClient.GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "measurement_unit_id"))
	if err != nil {
		return
	}

	if metadata.GetTenantManagementId(ctx) != 0 {
		tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, repo.tx, metadata.GetTenantManagementId(ctx))
		if err != nil {
			return
		}
	}

	data = structure.GetSaleTransferOrderDetailData{
		Id:                   saleTransferOrder.Id,
		OrderNo:              saleTransferOrder.OrderNo,
		AuditStatus:          saleTransferOrder.AuditStatus,
		AuditStatusName:      saleTransferOrder.AuditStatus.String(),
		CustomerID:           saleTransferOrder.CustomerID,
		CustomerName:         customerMaps[saleTransferOrder.CustomerID],
		OrderTime:            tools.MyTime(saleTransferOrder.OrderTime),
		SaleUserID:           saleTransferOrder.SaleUserID,
		SaleUserName:         employeeMaps[saleTransferOrder.SaleUserID],
		OrderRemark:          saleTransferOrder.Remark,
		Phone:                saleTransferOrder.Phone,
		Address:              saleTransferOrder.Address,
		ContactName:          saleTransferOrder.ContactName,
		CreateUserID:         saleTransferOrder.CreatorId,
		CreatorName:          saleTransferOrder.CreatorName,
		CreateTime:           tools.MyTime(saleTransferOrder.CreateTime),
		UpdateUserID:         saleTransferOrder.UpdaterId,
		UpdateUserName:       saleTransferOrder.UpdaterName,
		UpdateTime:           tools.MyTime(saleTransferOrder.UpdateTime),
		AuditTime:            tools.MyTime(saleTransferOrder.AuditDate),
		AuditUserID:          saleTransferOrder.AuditorId,
		AuditUserName:        userNameMap[saleTransferOrder.AuditorId],
		TenantManagementID:   tenantManagement.Id,
		TenantManagementName: tenantManagement.TenantCompanyName,
		TotalShouldPayAmount: func() (totalShouldPayAmount int) {
			for _, saleTransferOrderDetail := range saleTransferOrderDetails {
				totalShouldPayAmount += saleTransferOrderDetail.TotalSaleAmount - saleTransferOrderDetail.OtherPrice
			}
			return
		}(),
		OrderType:     saleTransferOrder.OrderType,
		OrderTypeName: saleTransferOrder.OrderType.String(),
		SaleMode:      saleTransferOrder.SaleMode,
		SaleModeName:  saleTransferOrder.SaleMode.String(),
		SaleTransferOrderDetailInfo: func() (list []structure.GetSaleTransferOrderDetailInfo) {
			list = make([]structure.GetSaleTransferOrderDetailInfo, 0)
			for _, saleTransferOrderDetail := range saleTransferOrderDetails {
				var (
					productMap      *product.ProductRes
					productColorMap *product.ProductColorRes
					ok              bool
				)
				if productMap, ok = productMaps[saleTransferOrderDetail.ProductID]; !ok {
					productMap = new(product.ProductRes)
				}

				if productColorMap, ok = productColorMaps[saleTransferOrderDetail.ProductColorID]; !ok {
					productColorMap = new(product.ProductColorRes)
				}
				// 总销售金额(应收金额)
				shouldCollectMoney += saleTransferOrderDetail.TotalSaleAmount
				otherPrice += saleTransferOrderDetail.OtherPrice
				o := structure.GetSaleTransferOrderDetailInfo{
					Id:                    saleTransferOrderDetail.Id,
					ProductID:             productMap.Id,
					ProductCode:           productMap.FinishProductCode,
					ProductName:           productMap.FinishProductName,
					ProductColorID:        productColorMap.Id,
					ProductColorCode:      productColorMap.ProductColorCode,
					ProductColorName:      productColorMap.ProductColorName,
					DyelotNumber:          saleTransferOrderDetail.DyelotNumber,
					SalePrice:             saleTransferOrderDetail.SalePrice,
					CostPrice:             saleTransferOrderDetail.CostPrice,
					SaleSettleWeight:      saleTransferOrderDetail.SaleSettleWeight,
					GrossProfit:           saleTransferOrderDetail.GrossProfit,
					SupplierID:            saleTransferOrderDetail.SupplierID,
					SupplierName:          customerMaps[saleTransferOrderDetail.SupplierID],
					Remark:                saleTransferOrderDetail.Remark,
					MeasurementUnitID:     saleTransferOrderDetail.MeasurementUnitID,
					MeasurementUnitName:   measurementUnits[saleTransferOrderDetail.MeasurementUnitID],
					Roll:                  saleTransferOrderDetail.Roll,
					TotalSaleAmount:       saleTransferOrderDetail.TotalSaleAmount,
					TotalCostAmount:       saleTransferOrderDetail.TotalCostAmount,
					TotalGrossProfit:      saleTransferOrderDetail.TotalGrossProfit,
					OtherPrice:            saleTransferOrderDetail.OtherPrice,
					PhaseDifferenceWeight: saleTransferOrderDetail.PhaseDifferenceWeight,

					SupplierMeasurementUnitId:   saleTransferOrderDetail.SupplierMeasurementUnitId,
					SupplierMeasurementUnitName: measurementUnits[saleTransferOrderDetail.SupplierMeasurementUnitId],
					SplitType:                   saleTransferOrderDetail.SplitType,

					SaleTransferOrderWeightInfoList: func() (list []structure.GetSaleTransferOrderWeightInfo) {
						for _, saleTransferOrderWeight := range saleTransferOrderWeights {
							if saleTransferOrderWeight.SaleTransferOrderDetailID == saleTransferOrderDetail.Id {
								list = append(list, structure.GetSaleTransferOrderWeightInfo{
									Id:                   saleTransferOrderWeight.Id,
									SaleWeight:           saleTransferOrderWeight.SaleWeight,
									SaleWeightError:      saleTransferOrderWeight.SaleWeightError,
									SaleSettleWeight:     saleTransferOrderWeight.SaleSettleWeight,
									SupplierWeight:       saleTransferOrderWeight.SupplierWeight,
									SupplierWeightError:  saleTransferOrderWeight.SupplierWeightError,
									SupplierSettleWeight: saleTransferOrderWeight.SupplierSettleWeight,
									StockId:              saleTransferOrderWeight.StockId,
									SumStockId:           saleTransferOrderWeight.SumStockId,
								})
							}
						}
						return
					}(),
				}
				// 处理幅宽克重
				o.FinishProductWidthAndWightUnit = productMap.FinishProductWidthAndWightUnit

				list = append(list, o)
				countSaleWeightMap[saleTransferOrderDetail.MeasurementUnitID] += saleTransferOrderDetail.SaleSettleWeight
			}
			return
		}(),
		CollectRecords: func() (list should_collect_order.GetCollectRecordDataList) {
			return
		}(),
		TotalRoll: saleTransferOrder.Roll,
		MeasurementUnitDataList: func() (list []structure.MeasurementUnitData) {
			for _, saleTransferOrderDetail := range saleTransferOrderDetails {
				list = append(list, structure.MeasurementUnitData{
					MeasurementUnitID:   saleTransferOrderDetail.MeasurementUnitID,
					MeasurementUnitName: measurementUnits[saleTransferOrderDetail.MeasurementUnitID],
					BasicData:           saleTransferOrderDetail.SaleSettleWeight,
				})
			}
			return
		}(),
	}

	for k, v := range countSaleWeightMap {
		fmtRound := tools.GetRound(v, 2)
		if data.MergeWeightInfo == "" {
			data.MergeWeightInfo = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnits[k]
		} else {
			data.MergeWeightInfo += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnits[k]
		}
	}
	tenantReceiveAddressSvc := basic_data_pb.NewTenantReceiveAddressClient()
	var (
		tenantReceiveAddressList basic_data_structure.GetTenantReceiveAddrDataList
	)
	tenantReceiveAddressList, _, err = tenantReceiveAddressSvc.GetTenantReceiveAddrDropdownList(ctx,
		&basic_data_structure.GetTenantReceiveAddrListQuery{
			CommonTenantReceiveAddr: basic_data_structure.CommonTenantReceiveAddr{
				SaleSystemId: saleTransferOrder.SaleSystemID,
				AddrType:     2,
			},
		})
	if len(tenantReceiveAddressList) != 0 {
		data.CompanyAddress = tenantReceiveAddressList[0].Addr
	}

	// 统计累计欠款
	var totalArrearsAmount int
	totalArrearsAmount, err = shouldcollectorderpb.NewShouldCollectOrderClient().GetCustomerOweMoney(ctx, saleTransferOrder.CustomerID)
	data.TotalArrearsAmount = tools.Cent(totalArrearsAmount)
	data.TotalSettleMoney = tools.Cent(shouldCollectMoney)
	data.TotalOtherMoney = tools.Cent(otherPrice)
	// 单据未审核不需要处理本单金额
	if saleTransferOrder.AuditStatus == common_system.OrderStatusAudited {
		data.BeforeArrearsAmount = tools.Cent(totalArrearsAmount - shouldCollectMoney - otherPrice)
	} else {
		data.BeforeArrearsAmount = tools.Cent(totalArrearsAmount)
	}
	return
}

func (repo *saleTransferOrderRepo) GetLatestOrderPrice(ctx context.Context, query structure.GetLatestOrderPriceQuery) (data structure.GetLatestOrderPriceData, err error) {
	var (
		// saleTransferOrder       model.SaleTransferOrder
		saleTransferOrderDetail model.SaleTransferOrderDetail
		exist                   bool
		bizUnitMaps             = make(map[uint64]string)
		saleSystem              sale_system_pb.Res
	)

	if query.ProductId == 0 && query.ProductColorId == 0 {
		return
	}

	saleTransferOrderDetail, exist, err = repo.saleTransferOrderDetailDao.FirstLatest(repo.tx, query)
	if err != nil {
		return
	}

	saleSystem, err = sale_system_pb.NewSaleSystemClient().GetSaleSystemDetailById(ctx, metadata.GetSaleSystemId(ctx))
	if err != nil {
		return
	}

	bizUnitMaps, err = repo.clientBizUnitService.GetBizUnitNameByIds(ctx, []uint64{saleTransferOrderDetail.SupplierID, saleSystem.DefaultCustomerId, saleSystem.DefaultSupplierId})
	if err != nil {
		return
	}

	data = structure.GetLatestOrderPriceData{}
	data.DefaultCustomerId = saleSystem.DefaultCustomerId
	data.DefaultCustomerName = bizUnitMaps[saleSystem.DefaultCustomerId]
	data.DefaultSupplierId = saleSystem.DefaultSupplierId
	data.DefaultSupplierName = bizUnitMaps[saleSystem.DefaultSupplierId]
	data.DefaultLastSalePrice = saleSystem.DefaultLastSalePrice
	data.DefaultLastCostPrice = saleSystem.DefaultLastCostPrice

	// 不存在上一单，返回
	if !exist {
		return data, nil
	}

	// saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, saleTransferOrderDetail.SaleTransferOrderID)
	// if err != nil {
	// 	return
	// }

	data.SalePrice = saleTransferOrderDetail.SalePrice
	data.CostPrice = saleTransferOrderDetail.CostPrice
	data.SupplierId = saleTransferOrderDetail.SupplierID
	data.SupplierName = bizUnitMaps[saleTransferOrderDetail.SupplierID]
	return
}

// UpdatePassIn 审核调货单 生成成品采购入仓单（待审核，后面生成应收应付的时候直接审核下推)
func (repo *saleTransferOrderRepo) UpdatePassIn(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, fpmInOrderIDs []uint64, err error) {
	var (
		saleTransferOrder        model.SaleTransferOrder
		saleTransferOrderDetails model.SaleTransferOrderDetailList
		// shouldCollectOrder       shouldCollectOrderModel.ShouldCollectOrder
		productMaps        = make(map[uint64]*product.ProductRes)
		productColorMaps   = make(map[uint64]*product.ProductColorRes)
		addFpmInOrderData  structure_product.AddFpmInOrderData
		warehouseTypeList  typeMysqlDao.TypeWarehouseList
		warehouseList      warehouse2.GetPhysicalWarehouseDropdownDataList
		maxVolumeNumberMap = make(map[string]int)
		orderPrefix        mysqlSystem.OrderPrefix
		prefix             string
		exist              bool
		sale_sys_svc       = sale_system_pb.NewSaleSystemClient()
		saleSysData        = sale_system_pb.Res{}
	)
	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, param.Id)
	if err != nil {
		return
	}

	if saleTransferOrder.AuditStatus != common_system.OrderStatusPendingAudit {
		err = middleware.WarnLog(errors.NewError(errors.ErrorCodePassOrderByFalseStatus), saleTransferOrder.OrderNo)
		return
	}

	saleTransferOrderDetails, err = repo.saleTransferOrderDetailDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	saleTransferOrderWeights, err := repo.saleTransferOrderWeightDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})

	for _, detail := range saleTransferOrderDetails {
		if detail.Roll == 0 && detail.SaleSettleWeight == 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeLackMustField, "匹数或者数量不能都为0"))
			return
		}
		tempWeightList := saleTransferOrderWeights.PickList(detail.Id)
		totalSettleWeight := tempWeightList.SumSettleWeight()

		// if detail.Roll > 0 && len(tempWeightList) != 0 && len(tempWeightList) != detail.Roll/vars.Roll {
		//	err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "匹数和细码不对等"))
		//	return
		// }

		if len(tempWeightList) == 0 {
			// 默认创建一条给他
			var (
				saleTransferOrderWeight     model.SaleTransferOrderWeight
				saleTransferOrderWeightInfo structure.SaleTransferOrderWeightParam
			)

			saleTransferOrderWeightInfo.SaleWeight = detail.SaleSettleWeight
			saleTransferOrderWeightInfo.SupplierWeight = detail.SaleSettleWeight
			saleTransferOrderWeight = model.NewSaleTransferOrderWeight(saleTransferOrder, detail, saleTransferOrderWeightInfo)
			saleTransferOrderWeight, err = repo.saleTransferOrderWeightDao.MustCreate(repo.tx, saleTransferOrderWeight)
			if err != nil {
				return
			}
			saleTransferOrderWeights = append(saleTransferOrderWeights, saleTransferOrderWeight)
		} else {
			if totalSettleWeight != detail.SaleSettleWeight {
				err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "细码数量与分录行不一致"))
				return
			}
		}

	}

	if len(saleTransferOrderWeights) == 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeLackMustField, "物料信息不能为空"))
		return
	}

	productMaps, err = repo.productClient.GetProductMapByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "product_id"))
	if err != nil {
		return
	}

	productColorMaps, err = repo.productColorClient.GetProductColorMapByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "product_color_id"))
	if err != nil {
		return
	}

	// 审核
	err = saleTransferOrder.Audit(ctx)
	if err != nil {
		return
	}
	saleTransferOrder, err = repo.saleTransferOrderDao.MustUpdate(repo.tx, saleTransferOrder)
	if err != nil {
		return
	}

	// 找一个成品仓 TODO：待修改为查找
	warehouseTypeList, _, err = typeMysql.SearchTypeWarehouse(&mysql_base.Tx{Context: ctx}, &structure_type.GetTypeWarehouseListQuery{Name: "成品仓"})
	if err != nil {
		return
	}
	if len(warehouseTypeList) == 0 {
		err = log.ErrorLog(errors.NewDetailError(errors.ErrCodeWarehouseNotExist, err), "未找到成品仓库")
		return
	}
	warehouseList, err, _ = repo.warehouseRepo.GetDropdownList(ctx, &warehouse2.GetPhysicalWarehouseListQuery{WarehouseTypeID: warehouseTypeList[0].Id})
	if err != nil {
		return
	}
	if len(warehouseList) == 0 {
		err = log.ErrorLog(errors.NewDetailError(errors.ErrCodePhysicalWarehouseNotExist, err), "未找到成品仓库")
		return
	}
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_system_pb.Req{Id: saleTransferOrder.SaleSystemID})

	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(repo.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmPurchaseInOrderPrefix
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmPurchaseInOrder
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}

	supplierIDs := mysql_base.GetUInt64List(saleTransferOrderDetails, "supplier_id")
	for _, bizUnitID := range supplierIDs {
		// 生成已审核的采购进仓单, 大货才有采购进仓单
		// if saleTransferOrder.SaleMode == sale_common.BulkTypeOrder {
		addFpmInOrderData, err = repo.fpmInOrderRepo.Add(ctx, &structure_product.AddFpmInOrderParam{
			InOrderType:     cus_const.WarehouseGoodInTypePurchase,
			OrderNoPre:      prefix,
			SaleSystemId:    saleTransferOrder.SaleSystemID,
			BizUnitId:       bizUnitID,
			WarehouseInTime: tools.QueryTime(saleTransferOrder.OrderTime.Format("2006-01-02")),
			Remark:          saleTransferOrder.Remark,
			SrcId:           saleTransferOrder.Id,
			SrcOrderNo:      saleTransferOrder.OrderNo,
			VoucherNumber:   saleTransferOrder.VoucherNumber,
			TextureUrl:      saleTransferOrder.TextureUrl,
			WarehouseId:     warehouseList[0].Id,
			SaleMode:        saleTransferOrder.SaleMode,
			ItemData: func() (list structure_product.AddFpmInOrderItemParamList) {
				for _, detail := range saleTransferOrderDetails {
					key := fmt.Sprintf("%v%v", detail.ProductColorID, strings.TrimSpace(detail.DyelotNumber))
					maxVolumeNumber := 0
					if total, ok := maxVolumeNumberMap[key]; ok {
						maxVolumeNumber = total
					} else {
						var (
							exist bool
						)
						// 获取颜色和缸号的的最大卷号，判断map中是否有该颜色和缸号，如果有则不使用
						total, exist, err = mysql.GetMaxVolumeNumber(repo.tx, detail.DyelotNumber, detail.ProductColorID, 0)
						if err != nil {
							return
						}
						if !exist {
							total = 1
						}
						maxVolumeNumberMap[key] = total
						maxVolumeNumber = total
					}

					if detail.SupplierID == bizUnitID {
						var (
							roll       int
							itemFcList = make(structure_product.AddFpmInOrderItemFcParamList, 0)
						)
						tmpSaleTransferOrderWeights := saleTransferOrderWeights.PickList(detail.Id)
						// 处理多余的匹数到第一条细码中
						if len(tmpSaleTransferOrderWeights) > 1 {
							roll = detail.Roll - (len(tmpSaleTransferOrderWeights)-1)*100
						} else {
							roll = detail.Roll
						}
						// 散剪类型没有匹数，所以需要特殊处理
						if detail.Roll == 0 {
							roll = 0
						}
						for i, weight := range tmpSaleTransferOrderWeights {
							maxVolumeNumber++
							// 散剪类型没有匹数，所以需要特殊处理
							if i > 0 && detail.Roll != 0 {
								roll = vars.Roll
							}
							itemFc := structure_product.AddFpmInOrderItemFcParam{
								Roll:              roll,
								BaseUnitWeight:    weight.SaleWeight,
								WeightError:       0,
								UnitId:            saleTransferOrderDetails.Pick(weight.SaleTransferOrderDetailID).MeasurementUnitID,
								ActuallyWeight:    weight.SaleWeight,
								SettleErrorWeight: weight.SaleWeightError,
								SettleWeight:      weight.SaleSettleWeight,
								Length:            weight.SupplierSettleWeight,
								OrderTime:         tools.QueryTime(saleTransferOrder.OrderTime.Format("2006-01-02")),
								SrcId:             weight.Id,
								// zqx todo:卷号自增
								VolumeNumber:           maxVolumeNumber,
								DyeFactoryDyelotNumber: detail.DyelotNumber,
							}
							if product, ok := productMaps[detail.ProductID]; ok {
								itemFc.PaperTubeWeight = product.PaperTubeWeight
								itemFc.ProductWidth = product.ProductWidth
								itemFc.ProductGramWeight = product.FinishProductGramWeight
								itemFc.FinishProductWidthUnitId = product.FinishProductWidthUnitId
								itemFc.FinishProductGramWeightUnitId = product.FinishProductGramWeightUnitId
							}
							itemFcList = append(itemFcList, itemFc)
						}
						item := structure_product.AddFpmInOrderItemParam{
							QuoteOrderNo:           saleTransferOrder.OrderNo,
							QuoteOrderItemId:       detail.Id,
							CustomerId:             saleTransferOrder.CustomerID,
							ProductColorId:         detail.ProductColorID,
							ProductId:              detail.ProductID,
							QuoteRoll:              detail.Roll,
							QuoteTotalWeight:       tmpSaleTransferOrderWeights.SumSettleWeight() + tmpSaleTransferOrderWeights.SumSaleWeightError(),
							InRoll:                 detail.Roll,
							TotalWeight:            tmpSaleTransferOrderWeights.SumSettleWeight() + tmpSaleTransferOrderWeights.SumSaleWeightError(),
							SettleWeight:           tmpSaleTransferOrderWeights.SumSettleWeight(),
							QuoteWeight:            saleTransferOrder.Weight,
							UnitId:                 detail.MeasurementUnitID,
							AuxiliaryUnitId:        detail.SupplierMeasurementUnitId,
							OtherPrice:             detail.OtherPrice,
							InLength:               detail.SupplierSettleWeight,
							TotalPrice:             detail.TotalCostAmount,
							Remark:                 detail.Remark,
							ActuallyWeight:         tmpSaleTransferOrderWeights.SumSettleWeight() + tmpSaleTransferOrderWeights.SumSaleWeightError(),
							SettleErrorWeight:      tmpSaleTransferOrderWeights.SumSaleWeightError(),
							DyeFactoryDyelotNumber: detail.DyelotNumber,
							ItemFCData:             itemFcList,
						}
						if product, ok := productMaps[detail.ProductID]; ok {
							item.ProductCode = product.FinishProductCode
							item.ProductName = product.FinishProductName
							item.ProductWidth = product.FinishProductWidth
							item.ProductGramWeight = product.FinishProductGramWeight
							item.ProductLevelId = product.FinishProductLevelId
							item.ProductRemark = product.Remark
							item.ProductCraft = product.FinishProductCraft
							item.ProductIngredient = product.FinishProductIngredient
							item.PaperTubeWeight = product.PaperTubeWeight
							item.FinishProductWidthUnitId = product.FinishProductWidthUnitId
							item.FinishProductGramWeightUnitId = product.FinishProductGramWeightUnitId
						}
						if productColor, ok := productColorMaps[detail.ProductColorID]; ok {
							item.ProductColorCode = productColor.ProductColorCode
							item.ProductColorName = productColor.ProductColorName
						}
						// 单位不一致，使用辅助单位和辅助数量单价进仓
						if detail.SupplierMeasurementUnitId != detail.MeasurementUnitID {
							item.LengthUnitPrice = detail.CostPrice
						} else {
							item.UnitPrice = detail.CostPrice
						}
						list = append(list, item)
					}
					maxVolumeNumberMap[key] = maxVolumeNumber
				}
				return
			}(),
		})
		if err != nil {
			return data, fpmInOrderIDs, err
		}
		fpmInOrderIDs = append(fpmInOrderIDs, addFpmInOrderData.Id)
		// }
	}

	return structure.ResSaleTransferOrderIDData{
		Id: saleTransferOrder.Id,
	}, fpmInOrderIDs, nil
}

// UpdatePassOut 生成成品销售出仓单（待审核，后面生成应收应付的时候直接审核下推)
func (repo *saleTransferOrderRepo) UpdatePassOut(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, fpmSaleOutOrderIDs []uint64, err error) {
	var (
		saleTransferOrder        model.SaleTransferOrder
		saleTransferOrderDetails model.SaleTransferOrderDetailList
		// shouldCollectOrder       shouldCollectOrderModel.ShouldCollectOrder
		productMaps            = make(map[uint64]*product.ProductRes)
		productColorMaps       = make(map[uint64]*product.ProductColorRes)
		addFpmSaleOutOrderData structure_product.AddFpmSaleOutOrderData
		warehouseTypeList      typeMysqlDao.TypeWarehouseList
		warehouseList          warehouse2.GetPhysicalWarehouseDataList
	)
	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, param.Id)
	if err != nil {
		return
	}

	saleTransferOrderDetails, err = repo.saleTransferOrderDetailDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	saleTransferOrderWeights, err := repo.saleTransferOrderWeightDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	if len(saleTransferOrderWeights) == 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeLackMustField, "物料信息不能为空"))
		return
	}

	productMaps, err = repo.productClient.GetProductMapByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "product_id"))
	if err != nil {
		return
	}

	productColorMaps, err = repo.productColorClient.GetProductColorMapByIds(ctx, mysql_base.GetUInt64List(saleTransferOrderDetails, "product_color_id"))
	if err != nil {
		return
	}

	// 找一个成品仓 TODO：待修改为查找
	warehouseTypeList, _, err = typeMysql.SearchTypeWarehouse(&mysql_base.Tx{Context: ctx}, &structure_type.GetTypeWarehouseListQuery{Name: "成品仓"})
	if err != nil {
		return
	}
	if len(warehouseTypeList) == 0 {
		err = log.ErrorLog(errors.NewDetailError(errors.ErrCodeWarehouseNotExist, err), "未找到成品仓库")
		return
	}
	warehouseList, err, _ = repo.warehouseRepo.GetList(ctx, &warehouse2.GetPhysicalWarehouseListQuery{WarehouseTypeID: warehouseTypeList[0].Id})
	if err != nil {
		return
	}
	if len(warehouseList) == 0 {
		err = log.ErrorLog(errors.NewDetailError(errors.ErrCodePhysicalWarehouseNotExist, err), "未找到成品仓库")
		return
	}

	// 生成待审核的销售出仓单, 大货才有销售出仓单
	var (
		addFpmSaleOutOrderItemParams = make(structure_product.AddFpmOutOrderItemParamList, 0)
	)
	for _, fpmInOrderData := range param.FpmInOrderDataMap {
		for _, fpmInOrderItemData := range fpmInOrderData.ItemData {
			var (
				addFpmOutOrderItemFcParams = make(structure_product.AddFpmOutOrderItemFcParamList, 0)
			)
			product, _ := productMaps[fpmInOrderItemData.ProductId]
			productColor, _ := productColorMaps[fpmInOrderItemData.ProductColorId]
			detail := saleTransferOrderDetails.Pick(fpmInOrderItemData.QuoteOrderItemId)
			for _, fpmInOrderItemFcData := range fpmInOrderItemData.ItemFCData {
				addFpmOutOrderItemFcParam := structure_product.AddFpmOutOrderItemFcParam{
					Roll:                          fpmInOrderItemFcData.Roll,
					BaseUnitWeight:                fpmInOrderItemFcData.BaseUnitWeight,
					WeightError:                   0,
					UnitId:                        fpmInOrderItemFcData.UnitId,
					PaperTubeWeight:               product.PaperTubeWeight,
					ActuallyWeight:                fpmInOrderItemFcData.ActuallyWeight,
					SettleErrorWeight:             fpmInOrderItemFcData.SettleErrorWeight,
					SettleWeight:                  fpmInOrderItemFcData.SettleWeight,
					ProductWidth:                  product.ProductWidth,
					ProductGramWeight:             product.FinishProductGramWeight,
					OrderTime:                     tools.QueryTime(saleTransferOrder.OrderTime.Format("2006-01-02")),
					FinishProductWidthUnitId:      product.FinishProductWidthUnitId,
					FinishProductGramWeightUnitId: product.FinishProductGramWeightUnitId,
					VolumeNumber:                  fpmInOrderItemFcData.VolumeNumber,
					StockId:                       fpmInOrderItemFcData.StockId,
					SumStockId:                    fpmInOrderItemFcData.SumStockId,
					WarehouseId:                   fpmInOrderItemFcData.WarehouseId,
					WarehouseBinId:                fpmInOrderItemFcData.WarehouseBinId,
				}
				addFpmOutOrderItemFcParams = append(addFpmOutOrderItemFcParams, addFpmOutOrderItemFcParam)
			}
			addFpmSaleOutOrderItemParam := structure_product.AddFpmOutOrderItemParam{
				SumStockId:             fpmInOrderItemData.SumStockId,
				QuoteOrderNo:           saleTransferOrder.OrderNo,
				QuoteOrderItemId:       detail.Id,
				ProductCode:            product.FinishProductCode,
				ProductName:            product.FinishProductName,
				CustomerId:             saleTransferOrder.CustomerID,
				ProductColorId:         productColor.Id,
				ProductId:              product.Id,
				ProductColorCode:       productColor.ProductColorCode,
				ProductColorName:       productColor.ProductColorName,
				ProductLevelId:         product.FinishProductLevelId,
				ProductWidth:           product.ProductWidth,
				DyeFactoryColorCode:    fpmInOrderItemData.DyeFactoryColorCode,
				DyeFactoryDyelotNumber: fpmInOrderItemData.DyeFactoryDyelotNumber,
				ProductGramWeight:      product.ProductGramWeight,
				ProductRemark:          product.Remark,
				ProductCraft:           product.FinishProductCraft,
				ProductIngredient:      product.FinishProductIngredient,
				OutRoll:                fpmInOrderItemData.InRoll,
				TotalWeight:            fpmInOrderItemData.TotalWeight,
				WeightError:            fpmInOrderItemData.WeightError,
				PaperTubeWeight:        product.PaperTubeWeight,
				SettleWeight:           fpmInOrderItemData.SettleWeight,
				SettleErrorWeight:      fpmInOrderItemData.SettleErrorWeight,
				ActuallyWeight:         fpmInOrderItemData.ActuallyWeight,
				UnitId:                 detail.MeasurementUnitID,
				AuxiliaryUnitId:        detail.MeasurementUnitID,
				UnitPrice:              detail.SalePrice,
				OtherPrice:             detail.OtherPrice,
				TotalPrice:             detail.TotalSaleAmount,
				Remark:                 detail.Remark,
				FpmInOrderItemId:       fpmInOrderItemData.Id,
				ItemFCData:             addFpmOutOrderItemFcParams,
			}
			addFpmSaleOutOrderItemParams = append(addFpmSaleOutOrderItemParams, addFpmSaleOutOrderItemParam)
		}
	}
	if len(addFpmSaleOutOrderItemParams) != 0 {
		addFpmSaleOutOrderParam := &structure_product.AddFpmSaleOutOrderParam{
			SaleSystemId:     saleTransferOrder.SaleSystemID,
			CustomerId:       saleTransferOrder.CustomerID,
			WarehouseOutTime: tools.QueryTime(saleTransferOrder.OrderTime.Format("2006-01-02")),
			SaleUserId:       saleTransferOrder.SaleUserID,
			SaleRemark:       saleTransferOrder.Remark,
			SrcOrderID:       saleTransferOrder.Id,
			SrcOrderNo:       saleTransferOrder.OrderNo,
			WarehouseId:      warehouseList[0].Id,
			SaleMode:         saleTransferOrder.SaleMode,
			ProcessFactoryId: saleTransferOrder.AddressId,
			ReceiveName:      saleTransferOrder.ContactName,
			ReceiveAddr:      saleTransferOrder.Address,
			ReceivePhone:     saleTransferOrder.Phone,
			ItemData:         addFpmSaleOutOrderItemParams,
		}
		addFpmSaleOutOrderData, err = repo.fpmSaleOutOrderRepo.Add(ctx, addFpmSaleOutOrderParam)
		if err != nil {
			return
		}
		fpmSaleOutOrderIDs = append(fpmSaleOutOrderIDs, addFpmSaleOutOrderData.Id)
	}

	return structure.ResSaleTransferOrderIDData{
		Id: saleTransferOrder.Id,
	}, fpmSaleOutOrderIDs, nil
}

func (repo *saleTransferOrderRepo) UpdateCancel(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error) {
	var (
		saleTransferOrder model.SaleTransferOrder
	)
	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, param.Id)
	if err != nil {
		return
	}

	if saleTransferOrder.AuditStatus != common_system.OrderStatusPendingAudit && saleTransferOrder.AuditStatus != common_system.OrderStatusRejected {
		err = middleware.WarnLog(errors.NewError(errors.ErrorCodeCancelOrderByFalseStatus), saleTransferOrder.OrderNo)
		return
	}
	// 作废
	err = saleTransferOrder.Cancel(ctx)
	if err != nil {
		return
	}
	saleTransferOrder.CancelRemark = param.Remark
	saleTransferOrder, err = repo.saleTransferOrderDao.MustUpdate(repo.tx, saleTransferOrder)
	if err != nil {
		return
	}

	return structure.ResSaleTransferOrderIDData{
		Id: saleTransferOrder.Id,
	}, nil
}

func (repo *saleTransferOrderRepo) UpdateReject(ctx context.Context, param structure.AuditSaleTransferOrderParam) (data structure.ResSaleTransferOrderIDData, err error) {
	var (
		saleTransferOrder model.SaleTransferOrder
	)
	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, param.Id)
	if err != nil {
		return
	}

	if saleTransferOrder.AuditStatus != common_system.OrderStatusPendingAudit {
		err = middleware.WarnLog(errors.NewError(errors.ErrorCodeRejectOrderByFalseStatus), saleTransferOrder.OrderNo)
		return
	}

	// 拒绝
	err = saleTransferOrder.Reject(ctx)
	if err != nil {
		return
	}
	saleTransferOrder, err = repo.saleTransferOrderDao.MustUpdate(repo.tx, saleTransferOrder)
	if err != nil {
		return
	}

	return structure.ResSaleTransferOrderIDData{
		Id: saleTransferOrder.Id,
	}, nil
}

func (repo *saleTransferOrderRepo) UpdateRollback(ctx context.Context, param structure.AuditSaleTransferOrderParam) (
	data structure.ResSaleTransferOrderIDData,
	backWriteMap map[uint64]*structure.BackWrite2SrcItemParam,
	err error) {
	var (
		saleTransferOrder       model.SaleTransferOrder
		orders                  model.SaleTransferOrderList
		detailIds               = make([]uint64, 0)
		_backWriteMap           = make(map[uint64]*structure.BackWrite2SrcItemParam)
		fpmInOrderIDs           = make([]uint64, 0)
		fpmSaleOutOrderIDs      = make([]uint64, 0)
		fpmSaleReturnInOrderIDs = make([]uint64, 0)
		fpmOutOrderIDs          = make([]uint64, 0)
		updateItems             structure_product.UpdateStockProductDetailParamList
		stockRepo               = product2.NewStockProductRepo(ctx, repo.tx, make(map[uint64]_product.StockProduct), make(map[uint64]_product.StockProductDetail), set.NewConcurrentMap[string, uint64]())
	)
	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, param.Id)
	if err != nil {
		return
	}

	if saleTransferOrder.AuditStatus != common_system.OrderStatusAudited {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeCancelOrderError), saleTransferOrder.OrderNo)
		return
	}

	// 消审
	err = saleTransferOrder.Wait(ctx)
	if err != nil {
		return
	}
	// 销售调货单消审完后后续单据跳过权限判断
	ctx = metadata.SetMDToIncoming(ctx, metadata.IsSkipAuditSelf, tools.BooleanToIntString(true))

	details, err := repo.saleTransferOrderDetailDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	// 判断是否被退货单引用(调货销售单)
	if saleTransferOrder.OrderType == common_system.OrderTypeSale {
		for _, detail := range details {
			detailIds = append(detailIds, detail.Id)
		}
		orders, err = repo.saleTransferOrderDao.FindQuoteReturnOrder(repo.tx, detailIds)
		if err != nil {
			return
		}
		if len(orders) > 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, orders.GetOrders()))
			return
		}
	}
	if saleTransferOrder.OrderType == common_system.OrderTypeReturn {
		// 反写因素
		for _, detail := range details {
			_backWriteMap[detail.SrcId] = &structure.BackWrite2SrcItemParam{
				Id:           detail.SrcId,
				IsAdd:        false,
				Roll:         detail.ReturnRoll,
				Weight:       detail.ReturnSettleWeight,
				ReturnAmount: detail.TotalReturnAmount,
			}
		}
	}
	srcOrderIDs := []uint64{saleTransferOrder.Id}

	if param.OrderType == common_system.OrderTypeReturn {
		// 查询采购退货出仓单
		fpmOutOrderRepo := product2.NewFpmOutOrderRepo(repo.tx)
		fpmOutOrderIDs, err = fpmOutOrderRepo.GetIDsBySrcID(ctx, srcOrderIDs)
		if err != nil {
			return
		}
		// 根据采购退货出仓单消审应付单
		err = payable_pb.NewClientPayableService().WaitPayableSaleTransferList(ctx, repo.tx, fpmOutOrderIDs)
		if err != nil {
			return
		}

		// 根据采购退货出仓单作废应付单
		err = payable_pb.NewClientPayableService().CancelPayableSaleTransferList(ctx, repo.tx, fpmOutOrderIDs)
		if err != nil {
			return
		}

		// 消审作废采购退货出仓单
		for _, fpmOutOrderID := range fpmOutOrderIDs {
			_, updateItems, _, err = fpmOutOrderRepo.UpdateStatusWait(ctx, fpmOutOrderID)
			if err != nil {
				return
			}
			if len(updateItems) != 0 {
				for _, updateItem := range updateItems {
					_, err = stockRepo.AddOrUpdate(ctx, updateItem.ToAddStockProductParam())
					if err != nil {
						return
					}
					// type为4只更新汇总库存
					if updateItem.Type != 4 {
						_, err = stockRepo.UpdateDetail(ctx, updateItem)
						if err != nil {
							return
						}
					}
				}
			}
			_, err = fpmOutOrderRepo.UpdateStatusCancel(ctx, fpmOutOrderID, 0)
			if err != nil {
				return
			}
		}

		// 查询销售退货进仓单
		fpmSaleReturnInOrderRepo := product2.NewFpmSaleReturnInOrderRepo(repo.tx)
		fpmSaleReturnInOrderIDs, err = fpmSaleReturnInOrderRepo.GetIDsBySrcIDs(ctx, srcOrderIDs)
		if err != nil {
			return
		}

		// 消审应收单
		err = repo.productSaleClient.WaitShouldCollectOrderBySrcIds(ctx, fpmSaleReturnInOrderIDs)
		if err != nil {
			return
		}

		// 作废应收单
		err = repo.productSaleClient.CancelShouldCollectOrderBySrcIds(ctx, fpmSaleReturnInOrderIDs)
		if err != nil {
			return
		}

		// 消审作废销售退货进仓单
		for _, fpmSaleReturnInOrderID := range fpmSaleReturnInOrderIDs {
			var productSaleBackList = make(map[uint64]should_collect_order.ModifyProductSaleShouldCollectOrder)
			_, updateItems, _, err = fpmSaleReturnInOrderRepo.UpdateStatusWait(ctx, fpmSaleReturnInOrderID, productSaleBackList)
			if err != nil {
				return
			}
			if len(updateItems) != 0 {
				for _, updateItem := range updateItems {
					_, err = stockRepo.AddOrUpdate(ctx, updateItem.ToAddStockProductParam())
					if err != nil {
						return
					}
					// type为4只更新汇总库存
					if updateItem.Type != 4 {
						_, err = stockRepo.UpdateDetail(ctx, updateItem)
						if err != nil {
							return
						}
					}
				}
			}
			_, err = fpmSaleReturnInOrderRepo.UpdateStatusCancel(ctx, fpmSaleReturnInOrderID)
			if err != nil {
				return
			}
		}
	} else {
		// 查询销售出仓单
		fpmSaleOutOrderIDs, err = repo.fpmSaleOutOrderRepo.GetIDsBySrcOrderID(ctx, srcOrderIDs)
		if err != nil {
			return
		}

		// 消审应收单
		err = repo.productSaleClient.WaitShouldCollectOrderBySrcIds(ctx, fpmSaleOutOrderIDs)
		if err != nil {
			return
		}

		// 作废应收单
		err = repo.productSaleClient.CancelShouldCollectOrderBySrcIds(ctx, fpmSaleOutOrderIDs)
		if err != nil {
			return
		}

		// 消审作废销售出仓单
		for _, fpmSaleOutOrderID := range fpmSaleOutOrderIDs {
			_, updateItems, _, err = repo.fpmSaleOutOrderRepo.UpdateStatusWait(ctx, fpmSaleOutOrderID)
			if err != nil {
				return
			}
			if len(updateItems) != 0 {
				for _, updateItem := range updateItems {
					_, err = stockRepo.AddOrUpdate(ctx, updateItem.ToAddStockProductParam())
					if err != nil {
						return
					}
					// type为4只更新汇总库存
					if updateItem.Type != 4 {
						_, err = stockRepo.UpdateDetail(ctx, updateItem)
						if err != nil {
							return
						}
					}
				}
			}
			_, err = repo.fpmSaleOutOrderRepo.UpdateStatusCancel(ctx, fpmSaleOutOrderID)
			if err != nil {
				return
			}
		}

		// 查询采购进仓单
		fpmInOrderIDs, err = repo.fpmInOrderRepo.GetIDsBySrcID(ctx, srcOrderIDs)
		if err != nil {
			return
		}

		// 消审应付单
		err = payable_pb.NewClientPayableService().WaitPayableSaleTransferList(ctx, repo.tx, fpmInOrderIDs)
		if err != nil {
			return
		}

		// 作废应付单
		err = payable_pb.NewClientPayableService().CancelPayableSaleTransferList(ctx, repo.tx, fpmInOrderIDs)
		if err != nil {
			return
		}

		// 消审作废采购进仓单
		for _, fpmInOrderID := range fpmInOrderIDs {
			_, updateItems, _, err = repo.fpmInOrderRepo.UpdateStatusWait(ctx, fpmInOrderID)
			if err != nil {
				return
			}
			if len(updateItems) != 0 {
				for _, updateItem := range updateItems {
					_, err = stockRepo.AddOrUpdate(ctx, updateItem.ToAddStockProductParam())
					if err != nil {
						return
					}
					// type为4只更新汇总库存
					if updateItem.Type != 4 {
						_, err = stockRepo.UpdateDetail(ctx, updateItem)
						if err != nil {
							return
						}
					}
				}
			}
			_, err = repo.fpmInOrderRepo.UpdateStatusCancel(ctx, fpmInOrderID)
			if err != nil {
				return
			}
		}
	}

	saleTransferOrder, err = repo.saleTransferOrderDao.MustUpdate(repo.tx, saleTransferOrder)
	if err != nil {
		return
	}

	backWriteMap = _backWriteMap

	return structure.ResSaleTransferOrderIDData{
		Id: saleTransferOrder.Id,
	}, backWriteMap, nil
}

func (repo *saleTransferOrderRepo) Update(ctx context.Context, param structure.UpdateSaleTransferOrderParam) (id uint64, err error) {
	var (
		saleTransferOrder        model.SaleTransferOrder
		saleTransferOrderDetails model.SaleTransferOrderDetailList
		saleTransferOrderWeights model.SaleTransferOrderWeightList
		productMaps              = make(map[uint64]*product.ProductRes)
		productColorMaps         = make(map[uint64]*product.ProductColorRes)
	)

	saleTransferOrder, err = repo.saleTransferOrderDao.MustFirst(repo.tx, param.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := saleTransferOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	saleTransferOrderDetails, err = repo.saleTransferOrderDetailDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	saleTransferOrderWeights, err = repo.saleTransferOrderWeightDao.FindBySaleTransferOrderIDs(repo.tx, []uint64{saleTransferOrder.Id})
	if err != nil {
		return
	}

	param.AddSaleTransferOrderParam.AdjustParam()
	saleTransferOrder.UpdateSaleTransferOrder(ctx, param)

	if saleTransferOrder.AuditStatus == common_system.OrderStatusRejected {
		saleTransferOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	saleTransferOrder, err = repo.saleTransferOrderDao.MustUpdate(repo.tx, saleTransferOrder)
	if err != nil {
		return
	}

	productMaps, err = repo.productClient.GetProductMapByIds(ctx, mysql_base.GetUInt64List(param.SaleTransferOrderDetailParamList, "product_id"))
	if err != nil {
		return
	}

	productColorMaps, err = repo.productColorClient.GetProductColorMapByIds(ctx, mysql_base.GetUInt64List(param.SaleTransferOrderDetailParamList, "product_color_id"))
	if err != nil {
		return
	}

	for _, saleTransferOrderDetail := range saleTransferOrderDetails {
		err = repo.saleTransferOrderDetailDao.MustDelete(repo.tx, saleTransferOrderDetail)
		if err != nil {
			return
		}
	}

	for _, saleTransferOrderWeight := range saleTransferOrderWeights {
		err = repo.saleTransferOrderWeightDao.MustDelete(repo.tx, saleTransferOrderWeight)
		if err != nil {
			return
		}
	}

	for _, saleTransferOrderDetailInfo := range param.SaleTransferOrderDetailParamList {
		err = saleTransferOrderDetailInfo.Validate()
		if err != nil {
			return
		}
		var (
			finishProduct           _product.FinishProduct
			finishProductColor      _product.FinishProductColor
			saleTransferOrderDetail model.SaleTransferOrderDetail
			productMap              *product.ProductRes
			productColorMap         *product.ProductColorRes
			ok                      bool
		)
		if productMap, ok = productMaps[saleTransferOrderDetailInfo.ProductID]; !ok {
			finishProduct.Id = vars.Snowflake.GenerateId().UInt64()
			finishProduct.FinishProductName = saleTransferOrderDetailInfo.ProductName
			finishProduct.FinishProductCode = saleTransferOrderDetailInfo.ProductCode
			finishProduct.FinishProductFullName = saleTransferOrderDetailInfo.ProductName
			finishProduct.Status = common_system.StatusEnable
			err = repo.productClient.MustCreateProduct(ctx, finishProduct)
			if err != nil {
				return
			}
			productMap = new(product.ProductRes)
			productMap.Id = finishProduct.Id
			productMap.FinishProductCode = finishProduct.FinishProductCode
			productMap.FinishProductName = finishProduct.FinishProductName
			productMap.FinishProductFullName = finishProduct.FinishProductFullName
			productMaps[finishProduct.Id] = productMap
		}

		if productColorMap, ok = productColorMaps[saleTransferOrderDetailInfo.ProductColorID]; !ok && (saleTransferOrderDetailInfo.ProductColorCode != "" ||
			saleTransferOrderDetailInfo.ProductColorName != "") {
			finishProductColor.Id = vars.Snowflake.GenerateId().UInt64()
			finishProductColor.ProductColorName = saleTransferOrderDetailInfo.ProductColorName
			finishProductColor.ProductColorCode = saleTransferOrderDetailInfo.ProductColorCode
			finishProductColor.FinishProductId = productMap.Id
			finishProductColor.Status = common_system.StatusEnable
			err = repo.productColorClient.MustCreateProductColor(ctx, finishProductColor)
			if err != nil {
				return
			}
			productColorMap = new(product.ProductColorRes)
			productColorMap.Id = finishProductColor.Id
			productColorMap.ProductColorCode = finishProductColor.ProductColorCode
			productColorMap.ProductColorName = finishProductColor.ProductColorName
			productColorMap.FinishProductId = finishProductColor.FinishProductId
			productColorMaps[productColorMap.Id] = productColorMap
		}

		if productColorMap == nil {
			productColorMap = new(product.ProductColorRes)
		}
		saleTransferOrderDetail, err = repo.saleTransferOrderDetailDao.MustCreate(repo.tx, model.NewSaleTransferOrderDetail(saleTransferOrder, saleTransferOrderDetailInfo, productMap.Id, productColorMap.Id))
		if err != nil {
			return
		}

		// if len(saleTransferOrderDetailInfo.SaleTransferOrderWeightParamList) == 0 {
		//	var (
		//		saleTransferOrderWeight     model.SaleTransferOrderWeight
		//		saleTransferOrderWeightInfo structure.SaleTransferOrderWeightParam
		//	)
		//
		//	if saleTransferOrderDetailInfo.Roll != 0 || saleTransferOrderDetailInfo.SaleSettleWeight != 0 {
		//		saleTransferOrderWeightInfo.SaleWeight = saleTransferOrderDetailInfo.SaleSettleWeight
		//		saleTransferOrderWeightInfo.SupplierWeight = saleTransferOrderDetailInfo.SaleSettleWeight
		//		saleTransferOrderWeight = model.NewSaleTransferOrderWeight(saleTransferOrder, saleTransferOrderDetail, saleTransferOrderWeightInfo)
		//		saleTransferOrderWeight, err = repo.saleTransferOrderWeightDao.MustCreate(repo.tx, saleTransferOrderWeight)
		//		if err != nil {
		//			return
		//		}
		//	}
		// } else {
		// if len(saleTransferOrderDetailInfo.SaleTransferOrderWeightParamList) != (saleTransferOrderDetailInfo.Roll / vars.Roll) {
		//	err = middleware.WarnLog(errors.NewError(errors.ErrCodeWeightRollMustEqualTotalRoll))
		//	return
		// }
		for _, saleTransferOrderWeightInfo := range saleTransferOrderDetailInfo.SaleTransferOrderWeightParamList {
			var saleTransferOrderWeight model.SaleTransferOrderWeight
			saleTransferOrderWeight = model.NewSaleTransferOrderWeight(saleTransferOrder, saleTransferOrderDetail, saleTransferOrderWeightInfo)
			saleTransferOrderWeight, err = repo.saleTransferOrderWeightDao.MustCreate(repo.tx, saleTransferOrderWeight)
			if err != nil {
				return
			}
		}
	}
	// }

	return saleTransferOrder.Id, err
}

func (repo *saleTransferOrderRepo) UpdateDetail(ctx context.Context, param *structure.BackWrite2SrcItemParam) (err error) {
	var (
		tx     = repo.tx
		detail = model.SaleTransferOrderDetail{}
	)
	detail, err = repo.saleTransferOrderDetailDao.MustFirst(tx, param.Id)
	if err != nil {
		return
	}

	err = detail.UpdateWriteBack(param)
	if err != nil {
		return
	}

	detail, err = repo.saleTransferOrderDetailDao.MustUpdate(tx, detail)
	if err != nil {
		return
	}
	return
}

func (repo *saleTransferOrderRepo) GetHomePageSaleTransferOrder(ctx context.Context) (data structure.GetHomePageSaleTransferOrderData, err error) {
	var (
		saleTransferOrders       model.SaleTransferOrderList
		saleTransferOrderDetails model.SaleTransferOrderDetailList
		saleTransferOrderMap     = make(map[uint64]model.SaleTransferOrder)
	)

	saleTransferOrderDetails, err = repo.saleTransferOrderDetailDao.SearchForHomePage(repo.tx, structure.GetSaleTransferOrderListQuery{})
	if err != nil {
		return
	}

	tools.FinishVoid(func() {
		saleTransferOrders, err = repo.saleTransferOrderDao.FindByIDs(repo.tx, mysql_base.GetUInt64List(saleTransferOrderDetails, "sale_transfer_order_id"))
		if err != nil {
			return
		}
	}, func() {
		data.WaitOrders.ProductSaleOrderCount, err = repo.productSaleClient.GetWaitAuditOrderCount(ctx)
		if err != nil {
			return
		}
	}, func() {
		data.WaitOrders.PayableOrderCount, err = repo.clientPayableService.GetWaitAuditOrderCount(ctx)
		if err != nil {
			return
		}
	})

	for _, saleTransferOrder := range saleTransferOrders {
		saleTransferOrderMap[saleTransferOrder.Id] = saleTransferOrder
	}
	for _, saleTransferOrderDetail := range saleTransferOrderDetails {
		var saleTransferOrder = saleTransferOrderMap[saleTransferOrderDetail.SaleTransferOrderID]
		if saleTransferOrder.OrderTime.Year() == time.Now().Year() && saleTransferOrder.OrderTime.Month() == time.Now().Month() && saleTransferOrder.OrderTime.Day() == time.Now().Day() {
			data.Today.SaleAmount += saleTransferOrderDetail.TotalSaleAmount
			data.Today.Roll += saleTransferOrderDetail.Roll
		}
		if saleTransferOrder.OrderTime.Year() == time.Now().Year() && saleTransferOrder.OrderTime.Month() == time.Now().Month() && saleTransferOrder.OrderTime.Day() == time.Now().Day()-1 {
			data.Yesterday.SaleAmount += saleTransferOrderDetail.TotalSaleAmount
			data.Yesterday.Roll += saleTransferOrderDetail.Roll
		}
		if saleTransferOrder.OrderTime.Year() == time.Now().Year() && saleTransferOrder.OrderTime.Month() == time.Now().Month()-1 {
			data.LastMonth.SaleAmount += saleTransferOrderDetail.TotalSaleAmount
			data.LastMonth.Roll += saleTransferOrderDetail.Roll
		}
		if saleTransferOrder.OrderTime.Year() == time.Now().Year() && saleTransferOrder.OrderTime.Month() == time.Now().Month() {
			data.ThisMonth.SaleAmount += saleTransferOrderDetail.TotalSaleAmount
			data.ThisMonth.Roll += saleTransferOrderDetail.Roll
		}
	}
	return
}

func (repo *saleTransferOrderRepo) GetTx() *mysql_base.Tx {
	return repo.tx
}
