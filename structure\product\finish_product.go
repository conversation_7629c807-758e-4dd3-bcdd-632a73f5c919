package product

import (
	common_system "hcscm/common/system_consts"
	"hcscm/msg/msg_publish"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFinishProductParamList []AddFinishProductParam

func (r AddFinishProductParamList) Adjust() {

}

type AddFinishProductParam struct {
	structure_base.Param
	FinishProductCode             string                            `json:"finish_product_code" mean:"成品编号" validates:"required"` // 成品编号
	FinishProductName             string                            `json:"finish_product_name" mean:"成品名称" validates:"required"` // 成品名称
	FinishProductFullName         string                            `json:"finish_product_full_name"`                             // 成品全称
	TypeGreyFabricId              uint64                            `json:"type_grey_fabric_id" mean:"布种类型" validates:"required"` // 布种类型id
	TypeGreyFabricCode            string                            `json:"type_grey_fabric_code"`                                // 布种类型编号
	TypeGreyFabricName            string                            `json:"type_grey_fabric_name" `                               // 布种类型名称
	FinishProductIngredient       string                            `json:"finish_product_ingredient"`                            // 成品成分(根据格式已拼接数据)
	IngredientItem                structure_base.IngredientItemList `json:"ingredient_item"`                                      // 成分组成信息
	MeasurementUnitId             uint64                            `json:"measurement_unit_id"`                                  // 计量单位id
	SupplierMeasurementUnitId     uint64                            `json:"supplier_measurement_unit_id"`                         // 供应商计量单位id
	GreyFabricId                  uint64                            `json:"grey_fabric_id"`                                       // 坯布信息ID
	GreyFabricCode                string                            `json:"grey_fabric_code"`                                     // 坯布编号
	GreyFabricName                string                            `json:"grey_fabric_name"`                                     // 坯布名称
	IsColorCard                   bool                              `json:"is_color_card"`                                        // 是否启用色卡编号
	WarehouseId                   uint64                            `json:"warehouse_id"`                                         // 仓库id
	WarehouseName                 string                            `json:"warehouse_name"`                                       // 仓库名称
	StorageArea                   string                            `json:"storage_area"`                                         // 存放区域
	DyeFactoryOrderFollowerId     uint64                            `json:"dye_factory_order_follower_id"`                        // 染厂跟单用户ID
	Remark                        string                            `json:"remark"`                                               // 备注
	FinishProductWidth            string                            `json:"finish_product_width"`                                 // 成品幅宽
	FinishProductGramWeight       string                            `json:"finish_product_gram_weight"`                           // 成品克重
	LengthToWeightRate            int                               `json:"length_to_weight_rate"`                                // 长度转数量(公斤/米)
	StandardWeight                int                               `json:"standard_weight"`                                      // 标准数量(公斤)
	PaperTubeWeight               int                               `json:"paper_tube_weight"`                                    // 纸筒数量(公斤)
	WeightError                   int                               `json:"weight_error"`                                         // 空差数量(公斤)
	FinishProductCraft            string                            `json:"finish_product_craft"`                                 // 成品工艺
	Status                        common_system.Status              `json:"status"`                                               // 状态
	DyeingCraft                   string                            `json:"dyeing_craft"`                                         // 染整工艺
	FinishProductLevelId          uint64                            `json:"finish_product_level_id"`                              // 成品等级
	TouchStyle                    string                            `json:"touch_style"`                                          // 手感风格
	DyeingLoss                    int                               `json:"dyeing_loss"`                                          // 染损(两位小数)
	Center                        string                            `json:"center"`                                               // 经纬度
	Density                       string                            `json:"density"`                                              // 密度
	Size                          string                            `json:"size"`                                                 // 尺寸
	TextureURL                    tools.QueryStringList             `json:"texture_url"`                                          // 纹理图片URL（详情显示）
	CoverTextureURL               string                            `json:"cover_texture_url"`                                    // 封面纹理图片URL（详情显示）
	SupplierId                    tools.QueryIntList                `json:"supplier_id"`                                          // 供应商id,多选
	YarnCount                     string                            `json:"yarn_count"`                                           // 纱支
	FinishProductWidthUnitId      uint64                            `json:"finish_product_width_unit_id"`                         // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                            `json:"finish_product_gram_weight_unit_id"`                   // 成品克重单位id(字典)
	WeavingOrganizationId         uint64                            `json:"weaving_organization_id"`                              // 织造组织id
	BleachId                      uint64                            `json:"bleach_id"`                                            // 漂染性id（字典）
	ShrinkageWarp                 string                            `json:"shrinkage_warp"`                                       // 缩率率(经向)
}

func (p AddFinishProductParam) Validate() (err error) {
	_, err = p.IngredientItem.ValidateCount()
	return
}

type AddFinishProductData struct {
	structure_base.ResponseData
	Id                    uint64 `json:"id"`
	SearchImageUploadList msg_publish.SearchImageUploadList
}

type AddQuickFinishProductParam struct {
	structure_base.Param
	FinishProductCode     string `json:"finish_product_code"`                                  // 成品编号
	FinishProductName     string `json:"finish_product_name" mean:"成品名称" validates:"required"` // 成品名称
	FinishProductFullName string `json:"finish_product_full_name"`                             // 成品全称
}

type UpdateFinishProductParam struct {
	structure_base.Param
	Id                            uint64                            `json:"id"`
	FinishProductCode             string                            `json:"finish_product_code"  mean:"成品编号" validates:"required"` // 成品编号
	FinishProductName             string                            `json:"finish_product_name" mean:"成品名称" validates:"required"`  // 成品名称
	FinishProductFullName         string                            `json:"finish_product_full_name"`                              // 成品全称
	TypeGreyFabricId              uint64                            `json:"type_grey_fabric_id" mean:"布种类型" validates:"required"`  // 布种类型id
	TypeGreyFabricCode            string                            `json:"type_grey_fabric_code" `                                // 布种类型编号
	TypeGreyFabricName            string                            `json:"type_grey_fabric_name"`                                 // 布种类型名称
	FinishProductIngredient       string                            `json:"finish_product_ingredient"`                             // 成品成分
	IngredientItem                structure_base.IngredientItemList `json:"ingredient_item"`                                       // 成分组成信息
	MeasurementUnitId             uint64                            `json:"measurement_unit_id"`                                   // 计量单位id
	SupplierMeasurementUnitId     uint64                            `json:"supplier_measurement_unit_id"`                          // 供应商计量单位id
	GreyFabricId                  uint64                            `json:"grey_fabric_id"`                                        // 坯布信息ID
	GreyFabricCode                string                            `json:"grey_fabric_code"`                                      // 坯布编号
	GreyFabricName                string                            `json:"grey_fabric_name"`                                      // 坯布名称
	IsColorCard                   bool                              `json:"is_color_card"`                                         // 是否启用色卡编号
	WarehouseId                   uint64                            `json:"warehouse_id"`                                          // 仓库id
	WarehouseName                 string                            `json:"warehouse_name"`                                        // 仓库名称
	StorageArea                   string                            `json:"storage_area"`                                          // 存放区域
	DyeFactoryOrderFollowerId     uint64                            `json:"dye_factory_order_follower_id"`                         // 染厂跟单用户ID
	Remark                        string                            `json:"remark"`                                                // 备注
	FinishProductWidth            string                            `json:"finish_product_width"`                                  // 成品幅宽
	FinishProductGramWeight       string                            `json:"finish_product_gram_weight"`                            // 成品克重
	LengthToWeightRate            int                               `json:"length_to_weight_rate"`                                 // 长度转数量(公斤/米)
	StandardWeight                int                               `json:"standard_weight"`                                       // 标准数量(公斤)
	PaperTubeWeight               int                               `json:"paper_tube_weight"`                                     // 纸筒数量(公斤)
	WeightError                   int                               `json:"weight_error"`                                          // 空差数量(公斤)
	FinishProductCraft            string                            `json:"finish_product_craft"`                                  // 成品工艺
	Status                        common_system.Status              `json:"status"`                                                // 状态
	DyeingCraft                   string                            `json:"dyeing_craft"`                                          // 染整工艺
	FinishProductLevelId          uint64                            `json:"finish_product_level_id"`                               // 成品等级
	TouchStyle                    string                            `json:"touch_style"`                                           // 手感风格
	DyeingLoss                    int                               `json:"dyeing_loss"`                                           // 染损(两位小数)
	Center                        string                            `json:"center"`                                                // 经纬度
	Density                       string                            `json:"density"`                                               // 密度
	Size                          string                            `json:"size"`                                                  // 尺寸
	TextureURL                    tools.QueryStringList             `json:"texture_url"`                                           // 纹理图片URL（详情显示）
	CoverTextureURL               string                            `json:"cover_texture_url"`                                     // 封面纹理图片URL（详情显示）
	SupplierId                    tools.QueryIntList                `json:"supplier_id"`                                           // 供应商id,多选
	YarnCount                     string                            `json:"yarn_count"`                                            // 纱支
	FinishProductWidthUnitId      uint64                            `json:"finish_product_width_unit_id"`                          // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                            `json:"finish_product_gram_weight_unit_id"`                    // 成品克重单位id(字典)
	WeavingOrganizationId         uint64                            `json:"weaving_organization_id"`                               // 织造组织id
	BleachId                      uint64                            `json:"bleach_id"`                                             // 漂染性id（字典）
	ShrinkageWarp                 string                            `json:"shrinkage_warp"`                                        // 缩率率(经向)
}

func (p UpdateFinishProductParam) Validate() (err error) {
	_, err = p.IngredientItem.ValidateCount()
	return
}

func (p *UpdateFinishProductParam) ToProductColorParam() *UpdateFinishProductColorParam {
	var r = &UpdateFinishProductColorParam{}
	r.FinishProductId = p.Id
	r.FinishProductCraft = p.FinishProductCraft
	r.GreyFabricId = p.GreyFabricId
	r.LengthToWeightRate = p.LengthToWeightRate
	r.YarnCount = p.YarnCount
	r.Density = p.Density
	r.FinishProductWidth = p.FinishProductWidth
	r.FinishProductGramWeight = p.FinishProductGramWeight
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.WeavingOrganizationId = p.WeavingOrganizationId
	r.BleachId = p.BleachId
	r.ShrinkageWarp = p.ShrinkageWarp
	return r
}

type UpdateFinishProductData struct {
	structure_base.ResponseData
	Id                    uint64 `json:"id"`
	SearchImageUploadList msg_publish.SearchImageUploadList
}

type DeleteFinishProductParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFinishProductData struct {
	structure_base.ResponseData
	Id                    []uint64 `json:"id"`
	SearchImageUploadList msg_publish.SearchImageUploadList
}

type UpdateFinishProductBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateFinishProductStatusParam struct {
	structure_base.Param
	Id     tools.QueryIntList   `json:"id"`
	Status common_system.Status `json:"status"`
}

func (p *UpdateFinishProductStatusParam) ToProductColorParam() *UpdateFinishProductColorParam {
	var r = &UpdateFinishProductColorParam{}
	r.FinishProductIds = p.Id
	r.Status = p.Status
	return r
}

type UpdateFinishProductStatusData struct {
	structure_base.ResponseData
	SearchImageUploadList msg_publish.SearchImageUploadList
}

type GetFinishProductQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFinishProductListQuery struct {
	structure_base.ListQuery
	Code                      string               `form:"code"`                          // 成品编号
	Name                      string               `form:"name"`                          // 成品名称或全称
	FinishProductCode         string               `form:"finish_product_code"`           // 成品编号
	FinishProductName         string               `form:"finish_product_name"`           // 成品名称或全称
	FinishProductCodeOrName   string               `form:"finish_product_code_or_name"`   // 成品编号或名称
	TypeGreyFabricId          tools.QueryIntList   `form:"type_grey_fabric_id"`           // 布种类型id,逗号拼接
	TypeGreyFabricIds         []uint64             `form:"type_grey_fabric_ids"`          // 布种类型ids
	FinishProductIngredient   string               `form:"finish_product_ingredient"`     // 成品成分
	MeasurementUnitId         uint64               `form:"measurement_unit_id"`           // 计量单位id
	GreyFabricId              uint64               `form:"grey_fabric_id"`                // 坯布信息ID
	GreyFabricCode            string               `form:"grey_fabric_code"`              // 坯布编号
	GreyFabricName            string               `form:"grey_fabric_name"`              // 坯布名称
	IsColorCard               bool                 `form:"is_color_card"`                 // 是否启用色卡编号
	WarehouseId               uint64               `form:"warehouse_id"`                  // 仓库id
	StorageArea               string               `form:"storage_area"`                  // 存放区域
	DyeFactoryOrderFollowerId uint64               `form:"dye_factory_order_follower_id"` // 染厂跟单用户ID
	FinishProductWidth        string               `form:"finish_product_width"`          // 成品幅宽
	FinishProductGramWeight   string               `form:"finish_product_gram_weight"`    // 成品克重
	LengthToWeightRate        int                  `form:"length_to_weight_rate"`         // 长度转数量(公斤/米)
	FinishProductCraft        string               `form:"finish_product_craft"`          // 成品工艺
	Status                    common_system.Status `form:"status"`                        // 状态
	DyeingCraft               string               `form:"dyeing_craft"`                  // 染整工艺
	FinishProductLevelId      uint64               `form:"finish_product_level_id"`       // 成品等级
	TouchStyle                string               `form:"touch_style"`                   // 手感风格
	FieldSearch               string               `form:"field_search"`                  // 字段搜索
	FromColorCard             bool                 `form:"-"`                             // 是否来自电子色卡

	Codes              []string
	Names              []string
	IsFindByCodeOrName bool
	SupplierIds        []uint64 // 供应商（用于全文筛选）
}

func (r *GetFinishProductListQuery) Adjust() {
	if r.FinishProductCode == "" {
		r.FinishProductCode = r.Code
	}
	if r.FinishProductName == "" {
		r.FinishProductName = r.Name
	}
}

type GetFinishProductData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	FinishProductCode           string                            `json:"finish_product_code" excel:"成品编号,必填"`                                            // 成品编号
	FinishProductName           string                            `json:"finish_product_name" excel:"成品名称,必填"`                                            // 成品名称
	FinishProductFullName       string                            `json:"finish_product_full_name" excel:"成品全称"`                                          // 成品全称
	TypeGreyFabricId            uint64                            `json:"type_grey_fabric_id"`                                                            // 布种类型id
	TypeGreyFabricCode          string                            `json:"type_grey_fabric_code" excel:"布种类型编号,必填"`                                        // 布种类型编号
	TypeGreyFabricName          string                            `json:"type_grey_fabric_name" excel:"布种类型名称,必填"`                                        // 布种类型名称
	FinishProductIngredient     string                            `json:"finish_product_ingredient" excel:"成品成分,必填"`                                      // 成品成分
	IngredientItem              structure_base.IngredientItemList `json:"ingredient_item"`                                                                // 成分组成信息
	MeasurementUnitId           uint64                            `json:"measurement_unit_id"`                                                            // 计量单位id
	MeasurementUnitName         string                            `json:"measurement_unit_name" excel:"计量单位,必填"`                                          // 计量单位名称
	SupplierMeasurementUnitId   uint64                            `json:"supplier_measurement_unit_id"`                                                   // 供应商计量单位id
	SupplierMeasurementUnitName string                            `json:"supplier_measurement_unit_name"`                                                 // 供应商计量单位名称
	GreyFabricId                uint64                            `json:"grey_fabric_id"`                                                                 // 坯布信息ID
	GreyFabricCode              string                            `json:"grey_fabric_code" excel:"坯布编号"`                                                  // 坯布编号
	GreyFabricName              string                            `json:"grey_fabric_name"`                                                               // 坯布名称
	GreyFabricColorId           uint64                            `json:"grey_fabric_color_id"`                                                           // 坯布颜色id
	GreyFabricColorName         string                            `json:"grey_fabric_color_name"`                                                         // 坯布颜色name
	IsColorCard                 bool                              `json:"is_color_card" excel:"是否启用色卡编号"`                                                 // 是否启用色卡编号
	WarehouseId                 uint64                            `json:"warehouse_id"`                                                                   // 仓库id
	WarehouseName               string                            `json:"warehouse_name" excel:"仓库"`                                                      // 仓库名称
	StorageArea                 string                            `json:"storage_area" excel:"存放区域"`                                                      // 存放区域
	DyeFactoryOrderFollowerId   uint64                            `json:"dye_factory_order_follower_id"`                                                  // 染厂跟单用户ID
	DyeFactoryOrderFollowerName string                            `json:"dye_factory_order_follower_name" excel:"染厂跟单用户"`                                 // 染厂跟单用户名称
	Remark                      string                            `json:"remark" excel:"备注"`                                                              // 备注
	LengthToWeightRate          int                               `json:"length_to_weight_rate" excel:"长度转数量(公斤/米)"`                                      // 长度转数量(公斤/米)
	StandardWeight              int                               `json:"standard_weight" excel:"标准数量"`                                                   // 标准数量(公斤)
	PaperTubeWeight             int                               `json:"paper_tube_weight" excel:"纸筒数量"`                                                 // 纸筒数量(公斤)
	WeightError                 int                               `json:"weight_error" excel:"空差数量"`                                                      // 空差数量(公斤)
	FinishProductCraft          string                            `json:"finish_product_craft" excel:"成品工艺"`                                              // 成品工艺
	Status                      common_system.Status              `json:"status"`                                                                         // 状态
	StatusName                  string                            `json:"status_name"`                                                                    // 状态
	DyeingCraft                 string                            `json:"dyeing_craft"`                                                                   // 染整工艺
	FinishProductLevelId        uint64                            `json:"finish_product_level_id"`                                                        // 成品等级
	FinishProductLevelName      string                            `json:"finish_product_level_name"`                                                      // 成品等级名称
	TouchStyle                  string                            `json:"touch_style" excel:"手感风格"`                                                       // 手感风格
	DyeingLoss                  int                               `json:"dyeing_loss" excel:"染损"`                                                         // 染损(两位小数)
	Center                      string                            `json:"center" excel:"经纬度"`                                                             // 经纬度
	Density                     string                            `json:"density" excel:"密度"`                                                             // 密度
	Size                        string                            `json:"size" excel:"尺寸"`                                                                // 尺寸
	TextureURL                  []string                          `json:"texture_url"`                                                                    // 纹理图片URL（详情显示）
	CoverTextureURL             string                            `json:"cover_texture_url"`                                                              // 封面纹理图片URL（详情显示）
	SupplierId                  []uint64                          `json:"supplier_id" desensitization:"ids=decryption_supplier_name"`                     // 供应商id,多选
	SupplierName                string                            `json:"supplier_name" excel:"供应商,逗号隔开" desensitization:"name=decryption_supplier_name"` // 供应商名称,逗号拼接
	YarnCount                   string                            `json:"yarn_count" excel:"纱支"`                                                          // 纱支
	WeavingOrganizationId       uint64                            `json:"weaving_organization_id"`                                                        // 织造组织id
	WeavingOrganizationCode     string                            `json:"weaving_organization_code" excel:"织造组织编号"`                                       // 织造组织编号
	WeavingOrganizationName     string                            `json:"weaving_organization_name" excel:"织造组织名称"`                                       // 织造组织名称
	BleachId                    uint64                            `json:"bleach_id"`                                                                      // 漂染性id（字典）
	BleachName                  string                            `json:"bleach_name" excel:"漂染性名称"`                                                      // 漂染性名称
	MainTextureUrl              string                            `json:"main_texture_url"`                                                               // 主图纹理图片URL
	ShrinkageWarp               string                            `json:"shrinkage_warp" excel:"缩率"`                                                      // 缩率率(经向)
}

type GetFinishProductDataList []GetFinishProductData

func (g GetFinishProductDataList) Adjust() {

}

type GetFinishProductDropdownData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	FinishProductCode           string   `json:"finish_product_code"`             // 成品编号
	FinishProductName           string   `json:"finish_product_name"`             // 成品名称
	FinishProductFullName       string   `json:"finish_product_full_name"`        // 成品全称
	TypeGreyFabricId            uint64   `json:"type_grey_fabric_id"`             // 布种类型id
	TypeGreyFabricCode          string   `json:"type_grey_fabric_code"`           // 布种类型编号
	TypeGreyFabricName          string   `json:"type_grey_fabric_name"`           // 布种类型名称
	MeasurementUnitId           uint64   `json:"measurement_unit_id"`             // 计量单位id
	MeasurementUnitName         string   `json:"measurement_unit_name"`           // 计量单位名称
	SupplierMeasurementUnitId   uint64   `json:"supplier_measurement_unit_id"`    // 供应商计量单位id
	SupplierMeasurementUnitName string   `json:"supplier_measurement_unit_name"`  // 供应商计量单位名称
	GreyFabricId                uint64   `json:"grey_fabric_id"`                  // 坯布信息ID
	GreyFabricCode              string   `json:"grey_fabric_code"`                // 坯布编号
	GreyFabricName              string   `json:"grey_fabric_name"`                // 坯布名称
	WarehouseId                 uint64   `json:"warehouse_id"`                    // 仓库id
	WarehouseName               string   `json:"warehouse_name"`                  // 仓库名称
	StorageArea                 string   `json:"storage_area"`                    // 存放区域
	DyeFactoryOrderFollowerId   uint64   `json:"dye_factory_order_follower_id"`   // 染厂跟单用户ID
	DyeFactoryOrderFollowerName string   `json:"dye_factory_order_follower_name"` // 染厂跟单用户名称
	LengthToWeightRate          int      `json:"length_to_weight_rate"`           // 长度转数量(公斤/米)
	StandardWeight              int      `json:"standard_weight"`                 // 标准数量(公斤)
	PaperTubeWeight             int      `json:"paper_tube_weight"`               // 纸筒数量(公斤)
	WeightError                 int      `json:"weight_error"`                    // 空差数量(公斤)
	FinishProductLevelId        uint64   `json:"finish_product_level_id"`         // 成品等级
	FinishProductLevelName      string   `json:"finish_product_level_name"`       // 成品等级名称
	FinishProductCraft          string   `json:"finish_product_craft"`            // 成品工艺
	DyeingCraft                 string   `json:"dyeing_craft"`                    // 染整工艺
	FinishProductIngredient     string   `json:"finish_product_ingredient"`       // 成品成分
	IsColorCard                 bool     `json:"is_color_card"`                   // 是否启用色卡编号
	Remark                      string   `json:"remark"`                          // 备注
	TouchStyle                  string   `json:"touch_style"`                     // 手感风格
	DyeingLoss                  int      `json:"dyeing_loss"`                     // 染损(两位小数)
	Center                      string   `json:"center"`                          // 经纬度
	Density                     string   `json:"density"`                         // 密度
	Size                        string   `json:"size"`                            // 尺寸
	TextureURL                  []string `json:"texture_url"`                     // 纹理图片URL（详情显示）
	CoverTextureURL             string   `json:"cover_texture_url"`               // 封面纹理图片URL（详情显示）
	YarnCount                   string   `json:"yarn_count"`                      // 纱支
	WeavingOrganizationId       uint64   `json:"weaving_organization_id"`         // 织造组织id
	WeavingOrganizationCode     string   `json:"weaving_organization_code"`       // 织造组织编号
	WeavingOrganizationName     string   `json:"weaving_organization_name"`       // 织造组织名称
	BleachId                    uint64   `json:"bleach_id"`                       // 漂染性id（字典）
	BleachName                  string   `json:"bleach_name"`                     // 漂染性名称
	ShrinkageWarp               string   `json:"shrinkage_warp"`                  // 缩率率(经向)
	SumColors                   int      `json:"sum_colors"`                      // 成品色号总数
}

type GetFinishProductDropdownDataList []GetFinishProductDropdownData

func (g GetFinishProductDropdownDataList) Adjust() {

}

type GetKindAndProductDropdownData struct {
	structure_base.RecordData
	FinishProductCode  string `json:"finish_product_code"`   // 成品编号
	FinishProductName  string `json:"finish_product_name"`   // 成品名称
	TypeGreyFabricId   uint64 `json:"type_grey_fabric_id"`   // 布种类型id
	TypeGreyFabricCode string `json:"type_grey_fabric_code"` // 布种类型编号
	TypeGreyFabricName string `json:"type_grey_fabric_name"` // 布种类型名称
}

type GetKindAndProductDropdownDataList []GetKindAndProductDropdownData

func (g GetKindAndProductDropdownDataList) Adjust() {

}

type GetSomeProductFieldData struct {
	Id                uint64 `json:"id"`
	FinishProductCode string `json:"finish_product_code"`
}

type GetSomeProductFieldDataList []*GetSomeProductFieldData

func (g GetSomeProductFieldDataList) Adjust() {

}

// 搜索相似图片
type SearchImageListQuery struct {
	structure_base.ListQuery
	ImageUrl string `json:"image_url" form:"image_url"`
}

type SearchImageData struct {
	structure_base.ResponseData
	ProductId   uint64  `json:"product_id"`
	ProductCode string  `json:"product_code"`
	ProductName string  `json:"product_name"`
	Score       float32 `json:"score"`
	Url         string  `json:"url"`
}

type SearchImageDataList []SearchImageData

func (s SearchImageDataList) Adjust() {

}
