package should_collect_order

import (
	"context"
	"hcscm/common/errors"
	common "hcscm/common/should_collect_order"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"time"

	"gorm.io/gorm"

	"hcscm/vars"
)

func GetShouldCollectOrderDetailIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "should_collect_order_detail_id")
}

type ShouldCollectOrderDetailList []ShouldCollectOrderDetail

func (r ShouldCollectOrderDetailList) List() []ShouldCollectOrderDetail {
	return r
}

func (r ShouldCollectOrderDetailList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r ShouldCollectOrderDetailList) GetShouldCollectOrderIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.ShouldCollectOrderId)
	}
	return o
}

func (r ShouldCollectOrderDetailList) GetFirstShouldCollectOrderId() uint64 {
	if len(r) > 0 {
		return r[0].ShouldCollectOrderId
	}
	return 0 // 或者返回一个错误，或者一个特定的默认值
}

func (r ShouldCollectOrderDetailList) GetSrcDetailIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.SrcDetailId)
	}
	return o
}

func (r ShouldCollectOrderDetailList) GetSumStockIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.SumStockId)
	}
	return o
}

func (r ShouldCollectOrderDetailList) One() ShouldCollectOrderDetail {
	return r[0]
}

func (r ShouldCollectOrderDetailList) Pick(id uint64) (o ShouldCollectOrderDetail) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r ShouldCollectOrderDetailList) PickByShouldCollectOrderId(shouldCollectOrderId uint64) (o ShouldCollectOrderDetailList) {
	var (
		list = make(ShouldCollectOrderDetailList, 0)
	)
	for _, t := range r {
		if t.ShouldCollectOrderId == shouldCollectOrderId {
			list = append(list, t)
		}
	}
	o = list
	return
}

// 获取总结算金额
func (r ShouldCollectOrderDetailList) GetTotal() (roll, weight, settlePrice, originPrice int) {
	for _, t := range r {
		if t.CollectType == common.CollectTypeRawMaterial || t.CollectType == common.CollectTypeRawMaterialReturn {
			roll += t.WholePieceCount + t.BulkPieceCount
		} else {
			roll += t.Roll
		}
		weight += t.SettleWeight
		settlePrice += t.SettlePrice
		originPrice += t.OriginPrice
	}
	return
}

// ShouldCollectOrderDetail 应收单详情
type ShouldCollectOrderDetail struct {
	mysql_base.Model
	Id                         uint64             `gorm:"column:id;primaryKey"`
	ShouldCollectOrderId       uint64             `gorm:"column:should_collect_order_id" relate:"should_collect_order_id"`     // 应收单id
	MergeOrderId               uint64             `gorm:"column:merge_order_id" relate:"merge_should_collect_order_id"`        // 合并应收单id
	MergeDetailId              uint64             `gorm:"column:merge_detail_id"`                                              // 合并应收单详情id
	OriginMergeOrderId         uint64             `gorm:"column:origin_merge_order_id" relate:"merge_should_collect_order_id"` // 源合并应收单id
	OriginMergeDetailId        uint64             `gorm:"column:origin_merge_detail_id"`                                       // 源合并应收单详情id
	DyeFactoryColorCode        string             `gorm:"column:dye_factory_color_code"`                                       // 染厂色号
	SrcOrderId                 uint64             `gorm:"column:src_order_id" relate:"src_order_id"`                           // 来源单ID、可能多张来源单，该字段作废
	SrcDetailId                uint64             `gorm:"column:src_detail_id" relate:"src_detail_id"`                         // 来源单详情ID
	CollectType                common.CollectType `gorm:"column:collect_type"`                                                 // 应收单类型 1成品 2成品退款 34原料 56坯布 7其他
	VoucherNumber              string             `gorm:"column:voucher_number"`                                               // 凭证单号,款号
	OrderTime                  time.Time          `gorm:"column:order_time"`                                                   // 单据日期
	MaterialId                 uint64             `gorm:"column:material_id" relate:"material_id,product_id"`                  // 物料ID(原料id,坯布id,成品颜色id)
	Code                       string             `gorm:"column:code"`                                                         // 物料编号（其他应收单使用）
	Name                       string             `gorm:"column:name"`                                                         // 物料名称（其他应收单使用）
	ProductColorId             uint64             `gorm:"column:product_color_id" relate:"product_color_id,color_id"`          // 成品颜色id
	ProductColorCode           string             `gorm:"column:product_color_code"`                                           // 成品颜色编号（其他应收单使用）
	ProductColorName           string             `gorm:"column:product_color_name"`                                           // 成品颜色名称（其他应收单使用）
	DyelotNumber               string             `gorm:"column:dyelot_number"`                                                // 染厂缸号
	WholePieceWeight           int                `gorm:"column:whole_piece_weight"`                                           // 整件件重（原料,坯布）
	WholePieceCount            int                `gorm:"column:whole_piece_count"`                                            // 整件件数（原料）
	WholeWeight                int                `gorm:"column:whole_weight"`                                                 // 整件总数量（原料）
	BulkPieceCount             int                `gorm:"column:bulk_piece_count"`                                             // 散装件数（原料）
	BulkWeight                 int                `gorm:"column:bulk_weight"`                                                  // 散装总数量（原料）
	Roll                       int                `gorm:"column:roll"`                                                         // 匹数
	Length                     int                `gorm:"column:length"`                                                       // 长度
	Weight                     int                `gorm:"column:weight"`                                                       // 数量
	ReturnRoll                 int                `gorm:"column:return_roll"`                                                  // 退货匹数
	ReturnLength               int                `gorm:"column:return_length"`                                                // 退货长度
	ReturnWeight               int                `gorm:"column:return_weight"`                                                // 退货数量
	WeightError                int                `gorm:"column:weight_error"`                                                 // 码单空差数量
	ActuallyWeight             int                `gorm:"column:actually_weight"`                                              // 码单数量
	SettleErrorWeight          int                `gorm:"column:settle_error_weight"`                                          // 结算空差数量
	SettleWeight               int                `gorm:"column:settle_weight"`                                                // 结算数量
	StandardSalePrice          int                `gorm:"column:standard_sale_price"`                                          // 标准销售报价
	SaleLevelId                uint64             `gorm:"column:sale_level_id" relate:"sale_level_id"`                         // 销售等级ID
	OffsetSalePrice            int                `gorm:"column:offset_sale_price"`                                            // 优惠单价
	SalePrice                  int                `gorm:"column:sale_price"`                                                   // 销售单价
	ReturnPrice                int                `gorm:"column:return_price"`                                                 // 退货单价
	StandardWeightError        int                `gorm:"column:standard_weight_error"`                                        // 标准空差
	OffsetWeightError          int                `gorm:"column:offset_weight_error"`                                          // 优惠空差
	AdjustWeightError          int                `gorm:"column:adjust_weight_error"`                                          // 调整空差
	StandardLengthCutSalePrice int                `gorm:"column:standard_length_cut_sale_price"`                               // 剪板销售价格
	OffsetLengthCutSalePrice   int                `gorm:"column:offset_length_cut_sale_price"`                                 // 剪版优惠单价
	LengthCutSalePrice         int                `gorm:"column:length_cut_sale_price"`                                        // 剪版销售单价
	LengthCutReturnPrice       int                `gorm:"column:length_cut_return_price"`                                      // 剪版退货单价
	OriginPrice                int                `gorm:"column:origin_price"`                                                 // 原金额(结算金额-其他金额)
	OtherPrice                 int                `gorm:"column:other_price"`                                                  // 其他金额，其他应退金额(成退)，折扣金额(其他)
	SaleTaxRate                int                `gorm:"column:sale_tax_rate"`                                                // 销售税率
	SettlePrice                int                `gorm:"column:settle_price"`                                                 // 结算金额，应退金额(成退)，实收金额(其他)
	Remark                     string             `gorm:"column:remark"`                                                       // 备注
	MeasurementUnitId          uint64             `gorm:"column:measurement_unit_id" relate:"measurement_unit_id"`             // 计量单位id
	AuxiliaryUnitId            uint64             `gorm:"column:auxiliary_unit_id" relate:"measurement_unit_id"`               // 辅助单位id（用于判断计算金额时使用哪个数量）
	SumStockId                 uint64             `gorm:"column:sum_stock_id" relate:"sum_stock_id"`                           // 汇总库存成品id
	SalePlanOrderItemId        uint64             `gorm:"column:sale_plan_order_item_id"`                                      // 成品销售计划单子项信息id
	SalePlanOrderItemNo        string             `gorm:"column:sale_plan_order_item_no"`                                      // 成品销售计划单子项单号
	ProductCraft               string             `gorm:"column:product_craft"`                                                // 成品工艺
	ProductRemark              string             `gorm:"column:product_remark"`                                               // 成品备注
	ProductIngredient          string             `gorm:"column:product_ingredient"`                                           // 成品成分
	ProductLevelId             uint64             `gorm:"column:product_level_id" relate:"product_level_id"`                   // 成品等级ID
	OrderType                  string             `gorm:"column:order_type"`                                                   // 单据类型
}

// 查询后的钩子
func (r *ShouldCollectOrderDetail) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r ShouldCollectOrderDetail) GetId() uint64 {
	return r.Id
}

func (r ShouldCollectOrderDetail) GetShouldCollectOrderId() uint64 {
	return r.ShouldCollectOrderId
}

// TableName ShouldCollectOrderDetail 表名
func (ShouldCollectOrderDetail) TableName() string {
	return "should_collect_order_detail"
}

func (r ShouldCollectOrderDetail) IsMain() bool {
	return false
}

func (r ShouldCollectOrderDetail) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (ShouldCollectOrderDetail) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (ShouldCollectOrderDetail) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeShouldCollectOrderDetailNotExist
}

func (ShouldCollectOrderDetail) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeShouldCollectOrderDetailAlreadyExist
}

func (r ShouldCollectOrderDetail) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

// 长度金额(100) = 长度(100) * 销售单价(10000)  *（1 + 税率%(100)）
func lengthMoney(length, lengthCutSalePrice, saleTaxRate int) (lengthMoney int) {
	lengthMoney = length * lengthCutSalePrice * (10000 + saleTaxRate) / 10000
	return
}

// 数量金额(100) = （数量(10000) - 匹数(100) * 结算空差(10000)）* 销售单价(10000) * （1 + 税率%(100)）
func weightMoney(weight, roll, settleWeightError, salePrice, saleTaxRate int) (weightMoney int) {
	// weightMoney = (weight - roll/vars.Roll*settleWeightError) * salePrice * (10000 + saleTaxRate) / 10000 // settleWeightError就已经是包含*匹数了
	weightMoney = (weight - settleWeightError) * salePrice * (10000 + saleTaxRate) / 10000
	return
}

// 结算金额(100) = 数量金额(100) + 长度金额(100) + 其他金额(100)
func SettleMoney(roll, weight, length, salePrice, lengthCutSalePrice, settleWeightError, saleTaxRate, otherMoney int, isWithTaxRate bool) (settleMoney int) {
	// if !isWithTaxRate {
	saleTaxRate = 0
	// }
	settleMoney = tools.HandlerProcessing(weightMoney(weight, roll, settleWeightError, salePrice, saleTaxRate), vars.WeightUnitPriceMult) +
		tools.HandlerProcessing(lengthMoney(length, lengthCutSalePrice, saleTaxRate), vars.LengthUnitPriceMult) +
		otherMoney
	return
}

// 退货长度金额(100) = 退货长度(100) * 销售单价(10000)  *（1 + 税率%(100)）
func returnLengthMoney(length, lengthCutSalePrice, saleTaxRate int) (lengthMoney int) {
	lengthMoney = length * lengthCutSalePrice * (10000 + saleTaxRate) / 10000
	return
}

// 退货数量金额(100) = 退货数量(10000) * 销售单价(10000) * （1 + 税率%(100)）
func returnWeightMoney(weight, salePrice, saleTaxRate int) (weightMoney int) {
	weightMoney = weight * salePrice * (10000 + saleTaxRate) / 10000
	return
}

// 退货结算金额(100) = 数量金额(100) + 长度金额(100) + 其他金额(100)
func ReturnSettleMoney(weight, length, salePrice, lengthCutSalePrice, saleTaxRate, otherMoney int, isWithTaxRate bool) (settleMoney int) {
	// if !isWithTaxRate {
	saleTaxRate = 0
	// }
	settleMoney = tools.HandlerProcessing(returnWeightMoney(weight, salePrice, saleTaxRate), vars.WeightUnitPriceMult) +
		tools.HandlerProcessing(returnLengthMoney(length, lengthCutSalePrice, saleTaxRate), vars.LengthUnitPriceMult) +
		otherMoney
	return
}

// 销售送货单
func NewProductSaleShouldCollectOrderDetail(
	ctx context.Context,
	req *structure.AddProductSaleShouldCollectOrderParam,
	detailReq *structure.AddProductSaleShouldCollectOrderDetailParam,
	shouldCollectOrderId uint64,
	dyelotNumber string,
	fcList structure.AddProductSaleShouldCollectOrderDetailFcParamList,
) (r ShouldCollectOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ShouldCollectOrderId = shouldCollectOrderId
	r.SrcDetailId = detailReq.SrcDetailId
	r.MaterialId = detailReq.MaterialId
	r.CollectType = common.CollectTypeProductSale
	r.VoucherNumber = detailReq.VoucherNumber
	r.OrderTime = detailReq.OrderTime.ToTimeYMD()
	r.Code = detailReq.Code
	r.Name = detailReq.Name
	r.ProductColorId = detailReq.ProductColorId
	r.ProductColorCode = detailReq.ProductColorCode
	r.ProductColorName = detailReq.ProductColorName
	r.DyelotNumber = dyelotNumber
	for _, fc := range fcList {
		r.Roll += fc.Roll
		r.Weight += fc.BaseUnitWeight
		r.WeightError += fc.WeightError
		r.ActuallyWeight += fc.ActuallyWeight
		r.SettleErrorWeight += fc.SettleErrorWeight
		r.SettleWeight += fc.SettleWeight
		r.Length += fc.Length
	}
	r.SaleLevelId = detailReq.SaleLevelId
	r.StandardWeightError = detailReq.StandardWeightError
	r.OffsetWeightError = detailReq.OffsetWeightError
	r.AdjustWeightError = detailReq.AdjustWeightError
	r.OtherPrice = detailReq.OtherPrice
	r.SaleTaxRate = detailReq.SaleTaxRate
	r.MeasurementUnitId = detailReq.MeasurementUnitId
	r.AuxiliaryUnitId = detailReq.AuxiliaryUnitId
	r.SumStockId = detailReq.SumStockId
	r.SalePlanOrderItemId = detailReq.SalePlanOrderItemId
	r.SalePlanOrderItemNo = detailReq.SalePlanOrderItemNo
	r.ProductCraft = detailReq.ProductCraft
	r.ProductRemark = detailReq.ProductRemark
	r.ProductIngredient = detailReq.ProductIngredient
	r.ProductLevelId = detailReq.ProductLevelId
	// 单位不一致的情况
	if r.MeasurementUnitId != detailReq.AuxiliaryUnitId {
		r.StandardLengthCutSalePrice = detailReq.StandardLengthCutSalePrice
		r.OffsetLengthCutSalePrice = detailReq.OffsetLengthCutSalePrice
		r.LengthCutSalePrice = detailReq.LengthCutSalePrice
		// 如果单位不一致，单价转换到长度单价去
		if detailReq.LengthCutSalePrice == 0 {
			r.StandardLengthCutSalePrice = detailReq.StandardSalePrice
			r.OffsetLengthCutSalePrice = detailReq.OffsetSalePrice
			r.LengthCutSalePrice = detailReq.SalePrice
		}
	} else {
		r.StandardSalePrice = detailReq.StandardSalePrice
		r.OffsetSalePrice = detailReq.OffsetSalePrice
		r.SalePrice = detailReq.SalePrice
		// 如果单位一致的情况，单价转移到数量单价去
		if detailReq.SalePrice == 0 {
			r.StandardSalePrice = detailReq.StandardLengthCutSalePrice
			r.OffsetSalePrice = detailReq.OffsetLengthCutSalePrice
			r.SalePrice = detailReq.LengthCutSalePrice
		}
	}
	if req.SrcOrderType == common.SrcOrderTypeSelf {
		r.SettlePrice = detailReq.SettlePrice
	} else {
		r.SettlePrice = SettleMoney(r.Roll, r.ActuallyWeight, r.Length, r.SalePrice, r.LengthCutSalePrice, r.SettleErrorWeight, r.SaleTaxRate, r.OtherPrice, req.IsWithTaxRate)
	}
	return
}

// 销售送货单(合并)
func (p *ShouldCollectOrderDetail) NewMergeProductSaleShouldCollectOrderDetail(
	shouldCollectOrderId uint64,
	isWithTaxRate bool,
) (r ShouldCollectOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ShouldCollectOrderId = shouldCollectOrderId
	r.MergeDetailId = p.Id
	r.MergeOrderId = p.ShouldCollectOrderId
	if r.OriginMergeOrderId != 0 {
		r.OriginMergeOrderId = p.ShouldCollectOrderId
		r.OriginMergeDetailId = p.Id
	}
	r.SrcDetailId = p.SrcDetailId
	r.MaterialId = p.MaterialId
	r.CollectType = common.CollectTypeProductSale
	r.VoucherNumber = p.VoucherNumber
	r.OrderTime = p.OrderTime
	r.Code = p.Code
	r.Name = p.Name
	r.ProductColorId = p.ProductColorId
	r.ProductColorCode = p.ProductColorCode
	r.ProductColorName = p.ProductColorName
	r.DyelotNumber = p.DyelotNumber
	r.Roll = p.Roll
	r.Weight = p.Weight
	r.WeightError = p.WeightError
	r.ActuallyWeight = p.ActuallyWeight
	r.SettleErrorWeight = p.SettleErrorWeight
	r.SettleWeight = p.SettleWeight
	r.StandardSalePrice = p.StandardSalePrice
	r.SaleLevelId = p.SaleLevelId
	r.OffsetSalePrice = p.OffsetSalePrice
	r.SalePrice = p.SalePrice
	r.StandardWeightError = p.StandardWeightError
	r.OffsetWeightError = p.OffsetWeightError
	r.AdjustWeightError = p.AdjustWeightError
	r.Length = p.Length
	r.StandardLengthCutSalePrice = p.StandardLengthCutSalePrice
	r.OffsetLengthCutSalePrice = p.OffsetLengthCutSalePrice
	r.LengthCutSalePrice = p.LengthCutSalePrice
	r.OtherPrice = p.OtherPrice
	r.SaleTaxRate = p.SaleTaxRate
	r.SettlePrice = SettleMoney(p.Roll, p.ActuallyWeight, p.Length, p.SalePrice, p.LengthCutSalePrice, p.SettleErrorWeight, p.SaleTaxRate, p.OtherPrice, isWithTaxRate)
	r.Remark = p.Remark
	r.MeasurementUnitId = p.MeasurementUnitId
	r.AuxiliaryUnitId = p.AuxiliaryUnitId
	r.SumStockId = p.SumStockId
	r.SalePlanOrderItemId = p.SalePlanOrderItemId
	r.SalePlanOrderItemNo = p.SalePlanOrderItemNo
	return
}

// 销售送货单
func (r *ShouldCollectOrderDetail) UpdateProductSaleShouldCollectOrderDetail(
	ctx context.Context,
	shouldCollectOrder ShouldCollectOrder,
	detailReq *structure.AddProductSaleShouldCollectOrderDetailParam,
) {
	r.WeightError = detailReq.WeightError
	r.ActuallyWeight = r.Weight - r.WeightError
	r.SettleErrorWeight = detailReq.SettleErrorWeight
	r.SettleWeight = r.ActuallyWeight - r.SettleErrorWeight
	r.OffsetSalePrice = detailReq.OffsetSalePrice
	r.OffsetLengthCutSalePrice = detailReq.OffsetLengthCutSalePrice
	r.OtherPrice = detailReq.OtherPrice
	r.SalePrice = detailReq.SalePrice
	r.LengthCutSalePrice = detailReq.LengthCutSalePrice
	r.SaleTaxRate = detailReq.SaleTaxRate
	r.SettlePrice = SettleMoney(detailReq.Roll, detailReq.ActuallyWeight, detailReq.Length, detailReq.SalePrice, detailReq.LengthCutSalePrice, detailReq.SettleErrorWeight, detailReq.SaleTaxRate, detailReq.OtherPrice, shouldCollectOrder.IsWithTaxRate)
	r.Remark = detailReq.Remark
	r.AuxiliaryUnitId = detailReq.AuxiliaryUnitId
}

// 销售退货单
func NewProductReturnShouldCollectOrderDetail(
	ctx context.Context,
	req *structure.AddProductReturnShouldCollectOrderParam,
	detailReq *structure.AddProductReturnShouldCollectOrderDetailParam,
	shouldCollectOrderId uint64,
) (r ShouldCollectOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ShouldCollectOrderId = shouldCollectOrderId
	r.SrcDetailId = detailReq.SrcDetailId
	r.MaterialId = detailReq.MaterialId
	r.CollectType = common.CollectTypeProductReturn
	r.Code = detailReq.Code
	r.Name = detailReq.Name
	r.ProductColorId = detailReq.ProductColorId
	r.ProductColorCode = detailReq.ProductColorCode
	r.ProductColorName = detailReq.ProductColorName
	r.DyelotNumber = detailReq.DyelotNumber
	r.Roll = detailReq.Roll
	r.Length = detailReq.Length
	r.Weight = detailReq.Weight
	r.ActuallyWeight = r.Weight - detailReq.WeightError
	r.SettleWeight = r.Weight - detailReq.WeightError - detailReq.SettleErrorWeight // 结算数量=数量 -码单空差 -结算空差
	r.SaleLevelId = detailReq.SaleLevelId
	r.StandardSalePrice = detailReq.StandardSalePrice
	r.OffsetSalePrice = detailReq.OffsetSalePrice
	r.SalePrice = detailReq.SalePrice
	r.WeightError = detailReq.WeightError
	r.StandardWeightError = detailReq.StandardWeightError
	r.OffsetWeightError = detailReq.OffsetWeightError
	r.AdjustWeightError = detailReq.AdjustWeightError
	r.SettleErrorWeight = detailReq.SettleErrorWeight
	r.StandardLengthCutSalePrice = detailReq.StandardLengthCutSalePrice
	r.OffsetLengthCutSalePrice = detailReq.OffsetLengthCutSalePrice
	r.LengthCutSalePrice = detailReq.LengthCutSalePrice
	r.OtherPrice = detailReq.OtherPrice
	r.SaleTaxRate = detailReq.SaleTaxRate
	r.OriginPrice = r.SettlePrice - detailReq.OtherPrice
	r.Remark = detailReq.Remark
	r.MeasurementUnitId = detailReq.MeasurementUnitId
	r.AuxiliaryUnitId = detailReq.AuxiliaryUnitId
	r.SumStockId = detailReq.SumStockId
	r.SalePlanOrderItemId = detailReq.SalePlanOrderItemId
	r.SalePlanOrderItemNo = detailReq.SalePlanOrderItemNo
	// 单位不一致情况
	if r.MeasurementUnitId != r.AuxiliaryUnitId {
		r.LengthCutReturnPrice = detailReq.LengthCutReturnPrice
		r.ReturnPrice = 0 // 将退货单价置为0
		// 如果单位不一致，单价转换到长度单价去
		if detailReq.LengthCutReturnPrice == 0 {
			r.LengthCutReturnPrice = detailReq.ReturnPrice
		}
	} else {
		r.LengthCutReturnPrice = 0 // 将长度单价置为0
		r.ReturnPrice = detailReq.ReturnPrice
		// 如果单位一致的情况，单价转移到数量单价去
		if detailReq.ReturnPrice == 0 {
			r.ReturnPrice = detailReq.LengthCutReturnPrice
		}
	}
	r.SettlePrice = ReturnSettleMoney(r.SettleWeight, r.Length, r.ReturnPrice, r.LengthCutReturnPrice, r.SaleTaxRate, r.OtherPrice, req.IsWithTaxRate)
	return
}

// 销售退货单
func (r *ShouldCollectOrderDetail) UpdateProductReturnShouldCollectOrderDetail(
	ctx context.Context,
	shouldCollectOrder ShouldCollectOrder,
	detailReq *structure.AddProductReturnShouldCollectOrderDetailParam,
) {
	r.OtherPrice = detailReq.OtherPrice
	r.Remark = detailReq.Remark
	r.SettleErrorWeight = detailReq.SettleErrorWeight
	r.WeightError = detailReq.WeightError
	r.ActuallyWeight = r.Weight - detailReq.WeightError
	r.SettleWeight = r.Weight - detailReq.WeightError - detailReq.SettleErrorWeight // 结算数量=数量 -码单空差 -结算空差
	r.AuxiliaryUnitId = detailReq.AuxiliaryUnitId
	if r.AuxiliaryUnitId == 0 {
		r.AuxiliaryUnitId = r.MeasurementUnitId
	}
	// 单位不一致情况
	if r.MeasurementUnitId != r.AuxiliaryUnitId {
		r.LengthCutReturnPrice = detailReq.LengthCutReturnPrice
		r.ReturnPrice = 0 // 将退货单价置为0
		// 如果单位不一致，单价转换到长度单价去
		if detailReq.LengthCutReturnPrice == 0 {
			r.LengthCutReturnPrice = detailReq.ReturnPrice
		}
	} else {
		r.LengthCutReturnPrice = 0 // 将长度单价置为0
		r.ReturnPrice = detailReq.ReturnPrice
		// 如果单位一致的情况，单价转移到数量单价去
		if detailReq.ReturnPrice == 0 {
			r.ReturnPrice = detailReq.LengthCutReturnPrice
		}
	}
	r.SettlePrice = ReturnSettleMoney(r.SettleWeight, r.Length, r.ReturnPrice, r.LengthCutReturnPrice, r.SaleTaxRate, r.OtherPrice, shouldCollectOrder.IsWithTaxRate)
}

// 原料销售应收单
func NewRawMaterialShouldCollectOrderDetail(
	ctx context.Context,
	detailReq *structure.AddRawMaterialShouldCollectOrderDetailParam,
	shouldCollectOrderId uint64,
) (r ShouldCollectOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ShouldCollectOrderId = shouldCollectOrderId
	r.SrcDetailId = detailReq.SrcDetailId
	r.CollectType = common.CollectType(detailReq.CollectType)
	r.MaterialId = detailReq.MaterialId
	r.Code = detailReq.Code
	r.Name = detailReq.Name
	r.ProductColorId = detailReq.ColorId
	r.ProductColorCode = detailReq.ColorCode
	r.ProductColorName = detailReq.ColorName
	if r.CollectType == common.CollectTypeRawMaterial {
		r.WholePieceWeight = detailReq.WholePieceWeight
		r.WholePieceCount = detailReq.WholePieceCount
		r.WholeWeight = detailReq.WholeWeight
		r.BulkPieceCount = detailReq.BulkPieceCount
		r.BulkWeight = detailReq.BulkWeight
		r.SettleWeight = detailReq.SettleWeight
		r.OtherPrice = detailReq.OtherPrice
		r.SalePrice = detailReq.SalePrice
		r.SettlePrice = r.SettleWeight*r.SalePrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	if r.CollectType == common.CollectTypeRawMaterialReturn {
		r.WholePieceWeight = -detailReq.WholePieceWeight
		r.WholePieceCount = -detailReq.WholePieceCount
		r.WholeWeight = -detailReq.WholeWeight
		r.BulkPieceCount = -detailReq.BulkPieceCount
		r.BulkWeight = -detailReq.BulkWeight
		r.SettleWeight = -detailReq.SettleWeight
		r.OtherPrice = -detailReq.OtherPrice
		r.ReturnPrice = detailReq.ReturnPrice
		r.SettlePrice = r.SettleWeight*r.ReturnPrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	r.Remark = detailReq.Remark
	r.MeasurementUnitId = detailReq.MeasurementUnitId
	return
}

// 原料销售应收单
func (r *ShouldCollectOrderDetail) UpdateRawMaterialShouldCollectOrderDetail(
	ctx context.Context,
	detailReq *structure.AddRawMaterialShouldCollectOrderDetailParam,
) {
	r.OtherPrice = detailReq.OtherPrice
	if r.CollectType == common.CollectTypeRawMaterial {
		r.SalePrice = detailReq.SalePrice
		r.SettlePrice = r.SettleWeight*r.SalePrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	if r.CollectType == common.CollectTypeRawMaterialReturn {
		r.ReturnPrice = detailReq.ReturnPrice
		r.SettlePrice = r.SettleWeight*r.ReturnPrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	r.Remark = detailReq.Remark
}

// 坯布销售应收单
func NewGreyFabricShouldCollectOrderDetail(
	ctx context.Context,
	detailReq *structure.AddGreyFabricShouldCollectOrderDetailParam,
	shouldCollectOrderId uint64,
) (r ShouldCollectOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ShouldCollectOrderId = shouldCollectOrderId
	r.SrcDetailId = detailReq.SrcDetailId
	r.MaterialId = detailReq.MaterialId
	r.CollectType = common.CollectType(detailReq.CollectType)
	r.Code = detailReq.Code
	r.Name = detailReq.Name
	r.ProductColorId = detailReq.ColorId
	r.ProductColorCode = detailReq.ColorCode
	r.ProductColorName = detailReq.ColorName
	if r.CollectType == common.CollectTypeGreyFabric {
		r.Roll = detailReq.Roll
		r.WholePieceWeight = detailReq.WholePieceWeight
		r.Weight = detailReq.Weight
		r.ActuallyWeight = detailReq.ActuallyWeight
		r.SettleWeight = detailReq.SettleWeight
		r.OtherPrice = detailReq.OtherPrice
		r.SalePrice = detailReq.SalePrice
		r.SettlePrice = r.SettleWeight*r.SalePrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	if r.CollectType == common.CollectTypeGreyFabricReturn {
		r.Roll = -detailReq.Roll
		r.WholePieceWeight = -detailReq.WholePieceWeight
		r.Weight = -detailReq.Weight
		r.ActuallyWeight = -detailReq.ActuallyWeight
		r.SettleWeight = -detailReq.SettleWeight
		r.OtherPrice = -detailReq.OtherPrice
		r.ReturnPrice = detailReq.ReturnPrice
		r.SettlePrice = r.SettleWeight*r.ReturnPrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	r.Remark = detailReq.Remark
	r.MeasurementUnitId = detailReq.MeasurementUnitId
	r.AuxiliaryUnitId = detailReq.AuxiliaryUnitId
	r.SalePlanOrderItemId = detailReq.SalePlanOrderItemId
	r.SalePlanOrderItemNo = detailReq.SalePlanOrderItemNo
	return
}

// 坯布销售应收单
func (r *ShouldCollectOrderDetail) UpdateGreyFabricShouldCollectOrderDetail(
	ctx context.Context,
	detailReq *structure.AddGreyFabricShouldCollectOrderDetailParam,
) {
	r.OtherPrice = detailReq.OtherPrice
	r.Remark = detailReq.Remark
	if r.CollectType == common.CollectTypeGreyFabric {
		r.SalePrice = detailReq.SalePrice
		r.SettlePrice = r.SettleWeight*r.SalePrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
	if r.CollectType == common.CollectTypeGreyFabricReturn {
		r.ReturnPrice = detailReq.ReturnPrice
		r.SettlePrice = r.SettleWeight*r.ReturnPrice/vars.WeightUnitPriceMult + r.OtherPrice
	}
}

// 其他应收单
func NewShouldCollectOrderDetail(
	ctx context.Context,
	detailReq *structure.AddShouldCollectOrderDetailParam,
	shouldCollectOrderId uint64,
) (r ShouldCollectOrderDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ShouldCollectOrderId = shouldCollectOrderId
	r.CollectType = common.CollectTypeOther
	r.VoucherNumber = detailReq.VoucherNumber
	r.OrderTime = detailReq.OrderTime.ToTimeYMD()
	r.Code = detailReq.Code
	r.Name = detailReq.Name
	r.ProductColorCode = detailReq.ProductColorCode
	r.ProductColorName = detailReq.ProductColorName
	r.DyelotNumber = detailReq.DyelotNumber
	r.Roll = detailReq.Roll
	r.Weight = detailReq.Weight
	r.SettleErrorWeight = detailReq.SettleErrorWeight
	// 如果有相关参数填写则帮他计算，没有就不处理
	r.SettleWeight = detailReq.SettleWeight
	if detailReq.Weight != 0 && detailReq.Roll != 0 && detailReq.SettleErrorWeight != 0 && detailReq.SettleWeight == 0 {
		r.SettleWeight = countSettleWeight(detailReq.Weight, detailReq.Roll, detailReq.SettleErrorWeight) // 结算数量 = 数量 - 匹数*空差
	}
	r.SalePrice = detailReq.SalePrice
	r.OriginPrice = detailReq.OriginPrice
	if detailReq.SalePrice != 0 && detailReq.OriginPrice == 0 {
		r.OriginPrice = countOriginPrice(detailReq.SalePrice, r.SettleWeight)
	}
	r.OtherPrice = detailReq.OtherPrice
	r.SettlePrice = detailReq.SettlePrice
	if detailReq.OtherPrice != 0 && detailReq.SettlePrice == 0 {
		r.SettlePrice = r.OriginPrice - detailReq.OtherPrice
	}
	r.Remark = detailReq.Remark
	r.OrderType = detailReq.OrderType
	return
}

// 获取结算数量（ 数量 - 匹数 * 空差）
func countSettleWeight(weight int, roll int, errorWeight int) int {
	return weight - tools.IntRoundHalf2One(tools.DecimalDiv(tools.DecimalMul(float64(roll), float64(errorWeight)), vars.Roll))
}

// 获取原金额（ 结算数量 * 单价）
func countOriginPrice(settleWeight int, salePrice int) int {
	return tools.IntRoundHalf2One(tools.DecimalDiv(tools.DecimalMul(float64(settleWeight), float64(salePrice)), vars.WeightUnitPriceMult))
}

// 其他应收单
func (r *ShouldCollectOrderDetail) UpdateShouldCollectOrderDetail(
	ctx context.Context,
	detailReq *structure.AddShouldCollectOrderDetailParam,
) {
	r.Id = detailReq.Id
	r.ShouldCollectOrderId = detailReq.ShouldCollectOrderId
	r.VoucherNumber = detailReq.VoucherNumber
	r.OrderTime = detailReq.OrderTime.ToTimeYMD()
	r.Code = detailReq.Code
	r.Name = detailReq.Name
	r.ProductColorCode = detailReq.ProductColorCode
	r.ProductColorName = detailReq.ProductColorName
	r.DyelotNumber = detailReq.DyelotNumber
	r.Roll = detailReq.Roll
	r.Weight = detailReq.Weight
	r.SettleErrorWeight = detailReq.SettleErrorWeight
	// 如果有相关参数填写则帮他计算，没有就不处理
	r.SettleWeight = detailReq.SettleWeight
	if detailReq.Weight != 0 && detailReq.Roll != 0 && detailReq.SettleErrorWeight != 0 && detailReq.SettleWeight == 0 {
		r.SettleWeight = countSettleWeight(detailReq.Weight, detailReq.Roll, detailReq.SettleErrorWeight) // 结算数量 = 数量 - 匹数*空差
	}
	r.SalePrice = detailReq.SalePrice
	r.OriginPrice = detailReq.OriginPrice
	if detailReq.SalePrice != 0 && detailReq.OriginPrice == 0 {
		r.OriginPrice = countOriginPrice(detailReq.SalePrice, r.SettleWeight)
	}
	r.OtherPrice = detailReq.OtherPrice
	r.SettlePrice = detailReq.SettlePrice
	if detailReq.OtherPrice != 0 && detailReq.SettlePrice == 0 {
		r.SettlePrice = r.OriginPrice - detailReq.OtherPrice
	}
	r.Remark = detailReq.Remark
}

func (r *ShouldCollectOrderDetail) BuildResp(resp *structure.GetSaleSimpleReportGroupMaterialData) {
	resp.Roll = r.Roll
	resp.Price = r.SettlePrice
	resp.MaterialId = r.MaterialId
}

func (r *ShouldCollectOrderDetail) BuildDetailResp(resp *structure.GetSaleSimpleReportGroupMaterialDetailData) {
	resp.Roll = r.Roll
	resp.Price = r.SettlePrice
	resp.ColorId = r.ProductColorId
}
