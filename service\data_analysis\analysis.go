package data_analysis

import (
	"context"
	"hcscm/aggs/data_analysis"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/data_analysis"
)

type IDataAnalysisService interface {
	GetWorkbenchData(ctx context.Context, req *structure.GetWorkbenchDataReq) (res structure.GetWorkbenchDataRes, err error)
	GetAnalysisHomeData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetAnalysisHomeRes, err error)
	GetProAnalysisTrendData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetProAnalysisTrendRes, err error)
	GetProAnalysisProductData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetProAnalysisProductRes, err error)
	GetProAnalysisCustomerData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetProAnalysisCustomerRes, err error)
	GetCusAnalysisCustomerData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetCusAnalysisCustomerRes, err error)
	GetCusAnalysisProductData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetCusAnalysisProductRes, err error)
	GetTopProductData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetTopDataRes, err error)
	GetTopColorData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetTopDataRes, err error)
	GetTopCustomerData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetTopDataRes, err error)
	GetUnifiedHomeAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedAnalysisRes, err error)
	GetUnifiedProductDetailAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedProductDetailAnalysisRes, err error)
	GetUnifiedCustomerDetailAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedCustomerDetailAnalysisRes, err error)
	GetUnifiedCustomerDetailCustomerAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedCustomerDetailCustomerAnalysisRes, err error)
	GetUnifiedProductDetailCustomerAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedProductDetailCustomerAnalysisRes, err error)
	GetReceivableOrderList(ctx context.Context, req *structure.GetReceivableOrderListReq) (res structure.GetReceivableOrderListRes, err error)
}

type DataAnalysisService struct {
	tx *mysql_base.Tx
}

func NewDataAnalysisService(tx *mysql_base.Tx) IDataAnalysisService {
	return &DataAnalysisService{
		tx: tx,
	}
}

// GetWorkbenchData 获取工作台数据
func (s *DataAnalysisService) GetWorkbenchData(ctx context.Context, req *structure.GetWorkbenchDataReq) (res structure.GetWorkbenchDataRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetWorkbenchData(ctx, req)
	return
}

// GetAnalysisHomeData 获取产品分析首页数据
func (s *DataAnalysisService) GetAnalysisHomeData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetAnalysisHomeRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetAnalysisHomeData(ctx, req)
	return
}

// GetProAnalysisTrendData 获取产品分析趋势数据
func (s *DataAnalysisService) GetProAnalysisTrendData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetProAnalysisTrendRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetProAnalysisTrendData(ctx, req)
	return
}

// GetProAnalysisProductData 获取产品维度分析数据
func (s *DataAnalysisService) GetProAnalysisProductData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetProAnalysisProductRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetProAnalysisProductData(ctx, req)
	return
}

// GetProAnalysisCustomerData 获取产品客户维度分析数据
func (s *DataAnalysisService) GetProAnalysisCustomerData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetProAnalysisCustomerRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetProAnalysisCustomerData(ctx, req)
	return
}

// GetCusAnalysisCustomerData 获取客户维度分析数据
func (s *DataAnalysisService) GetCusAnalysisCustomerData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetCusAnalysisCustomerRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetCusAnalysisCustomerData(ctx, req)
	return
}

// GetCusAnalysisProductData 获取客户产品维度分析数据
func (s *DataAnalysisService) GetCusAnalysisProductData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetCusAnalysisProductRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetCusAnalysisProductData(ctx, req)
	return
}

// GetTop10ProductData 获取前十产品数据
func (s *DataAnalysisService) GetTopProductData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetTopDataRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetTopProductData(ctx, req)
	return
}

// GetTopColorData 获取前十颜色数据
func (s *DataAnalysisService) GetTopColorData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetTopDataRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetTopColorData(ctx, req)
	return
}

// GetTopCustomerData 获取前十客户数据
func (s *DataAnalysisService) GetTopCustomerData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetTopDataRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetTopCustomerData(ctx, req)
	return
}

// GetUnifiedHomeAnalysisData 获取统一Home分析数据
func (s *DataAnalysisService) GetUnifiedHomeAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedAnalysisRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetUnifiedHomeAnalysisRes(ctx, req)
	return
}

// GetUnifiedHomeAnalysisData 获取统一Home分析数据
func (s *DataAnalysisService) GetUnifiedProductDetailAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedProductDetailAnalysisRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetUnifiedProductDeatilAnalysisRes(ctx, req)
	return
}

func (s *DataAnalysisService) GetUnifiedCustomerDetailAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedCustomerDetailAnalysisRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetUnifiedCustomerDeatilAnalysisRes(ctx, req)
	return
}

func (s *DataAnalysisService) GetUnifiedCustomerDetailCustomerAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedCustomerDetailCustomerAnalysisRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetUnifiedCustomerDeatilCustomerAnalysisRes(ctx, req)
	return
}

func (s *DataAnalysisService) GetUnifiedProductDetailCustomerAnalysisData(ctx context.Context, req *structure.GetAnalysisHomeReq) (res structure.GetUnifiedProductDetailCustomerAnalysisRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetUnifiedProductDeatilCustomerAnalysisRes(ctx, req)
	return
}

func (s *DataAnalysisService) GetReceivableOrderList(ctx context.Context, req *structure.GetReceivableOrderListReq) (res structure.GetReceivableOrderListRes, err error) {
	repo := data_analysis.NewDataAnalysisRepo(s.tx)
	res, err = repo.GetReceivableOrderList(ctx, req)
	return
}
