package dao

import (
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/mysql/third_party_extra"
	"hcscm/structure/third_party_extra_structure"
	"time"
)

// CreateUserBehaviorTracking 创建用户行为跟踪记录
func CreateUserBehaviorTracking(tx *mysql_base.Tx, tracking *third_party_extra.UserBehaviorTracking) (err error) {
	tracking.ActionTime = time.Now()
	err = mysql_base.MustCreateModel(tx, tracking)
	return
}

// GetUserBehaviorTrackingList 获取用户行为跟踪列表
func GetUserBehaviorTrackingList(tx *mysql_base.Tx, req third_party_extra_structure.GetUserBehaviorTrackingListRequest) (list third_party_extra.UserBehaviorTrackingList, total int, err error) {
	cond := mysql_base.NewCondition()
	if req.UserId != 0 {
		cond.AddEqual("user_id", req.UserId)
	}
	if req.ActionType != "" {
		cond.AddEqual("action_type", req.ActionType)
	}
	if req.ActionTarget != "" {
		cond.AddLike("action_target", req.ActionTarget)
	}
	if req.SessionId != "" {
		cond.AddEqual("session_id", req.SessionId)
	}
	if !req.StartTime.IsZero() {
		cond.AddGreaterEqual("action_time", req.StartTime)
	}
	if !req.EndTime.IsZero() {
		cond.AddLessEqual("action_time", req.EndTime)
	}
	cond.AddOrderBy("action_time", "desc")
	total, err = mysql_base.SearchListGroupForPaging(tx, &third_party_extra.UserBehaviorTracking{}, req, &list, cond)
	return
}

// GetUserBehaviorStatistics 获取用户行为统计
func GetUserBehaviorStatistics(tx *mysql_base.Tx, userId uint64, startTime, endTime time.Time) (statistics map[string]int64, err error) {
	statistics = make(map[string]int64)

	cond := mysql_base.NewCondition()
	cond.AddEqual("user_id", userId)
	if !startTime.IsZero() {
		cond.AddGreaterEqual("action_time", startTime)
	}
	if !endTime.IsZero() {
		cond.AddLessEqual("action_time", endTime)
	}

	var results []struct {
		ActionType string `gorm:"column:action_type"`
		Count      int64  `gorm:"column:count"`
	}

	err = tx.Model(&third_party_extra.UserBehaviorTracking{}).
		Select("action_type, count(*) as count").
		Where(cond.GetWhereCondition(), cond.GetWhereArgs()...).
		Group("action_type").
		Scan(&results).Error

	if err != nil {
		return
	}

	for _, result := range results {
		statistics[result.ActionType] = result.Count
	}

	return
}

// DeleteUserBehaviorTracking 删除用户行为跟踪记录（按时间范围）
func DeleteUserBehaviorTracking(tx *mysql_base.Tx, beforeTime time.Time) (err error) {
	cond := mysql_base.NewCondition()
	cond.AddLess("action_time", beforeTime)
	err = mysql_base.MustDeleteByCond(tx, &third_party_extra.UserBehaviorTracking{}, cond)
	return
}
