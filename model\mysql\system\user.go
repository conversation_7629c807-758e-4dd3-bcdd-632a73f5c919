package mysql

import (
	"context"
	"gorm.io/gorm"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/system"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"time"

	"github.com/gin-gonic/gin"

	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	common "hcscm/common/user"
	"hcscm/tools"
)

func GetUserIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "user_id")
}

func GetSaleUserIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "sale_user_id")
}

type UserList []User

func (r UserList) List() []User {
	return []User(r)
}

func (r UserList) One() User {
	return r[0]
}

func (r UserList) Len() (total int) {
	total = len(r)
	return
}

func (r UserList) Pick(id uint64) (o User) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r UserList) PickAdminTenant(tenantManagementID uint64) (o User) {
	for _, t := range r {
		if t.TenantManagementID == tenantManagementID && t.IsTenantAdmin {
			return t
		}
	}
	return
}

func (r UserList) PickAdmin() (o User) {
	for _, t := range r {
		if t.IsTenantAdmin {
			return t
		}
	}
	return
}

func (r UserList) PickByCompanyID(companyID uint64) (o User) {
	for _, t := range r {
		if t.CompanyID == companyID {
			return t
		}
	}
	return
}

func (r UserList) PickByBizUnitID(bizUnitID uint64) (o User) {
	for _, t := range r {
		if t.BizUnitID == bizUnitID {
			return t
		}
	}
	return
}

func (r UserList) PickByName(name string) (o UserList) {
	list := make(UserList, 0)
	for _, t := range r {
		if t.EmployeeName == name {
			list = append(list, t)
		}
	}
	o = list
	return
}

func (r UserList) PickByDepartmentID(departmentID uint64) (o UserList) {
	list := make(UserList, 0)
	for _, t := range r {
		if t.DepartmentID == departmentID {
			list = append(list, t)
		}
	}
	o = list
	return
}

func (r UserList) PickByTenantManagementID(tenantManagementID uint64) (o UserList) {
	list := make(UserList, 0)
	for _, t := range r {
		if t.TenantManagementID == tenantManagementID {
			list = append(list, t)
		}
	}
	o = list
	return
}

// User 用户
type User struct {
	mysql_base.Model
	Id                         uint64                            `gorm:"column:id" relate:"user_id,u_id"`
	Account                    string                            `gorm:"column:account"`                                             // 账号
	Password                   string                            `gorm:"column:password"`                                            // 密码 md5
	Status                     common_system.Status              `gorm:"column:status"`                                              // 账号状态
	Phone                      string                            `gorm:"column:phone"`                                               // 手机号码
	Email                      string                            `gorm:"column:email"`                                               // 邮箱
	WechatOpenUserID           uint64                            `gorm:"column:wechat_open_user_id" relate:"wechat_open_user_id"`    // 微信用户表ID
	IsOutsideEmployee          bool                              `gorm:"column:is_outside_employee"`                                 // 是否为外部员工
	EmployeeID                 uint64                            `gorm:"column:employee_id" relate:"employee_id"`                    // 员工ID(关联EmployeeID)
	EmployeeCode               string                            `gorm:"column:employee_code"`                                       // 工号
	EmployeeName               string                            `gorm:"column:employee_name"`                                       // 姓名
	DepartmentID               uint64                            `gorm:"column:department_id" relate:"department_id"`                // 用户所属部门
	DepartmentName             string                            `gorm:"column:department_name"`                                     // 用户所属部门
	CompanyID                  uint64                            `gorm:"column:company_id" relate:"company_id"`                      // 公司ID
	BizUnitID                  uint64                            `gorm:"column:biz_unit_id" relate:"biz_unit_id"`                    // 往来单位ID
	DefaultPhysicalWarehouseID uint64                            `gorm:"column:default_physical_warehouse_id" relate:"warehouse_id"` // 默认仓库ID
	DefaultSaleSystemID        uint64                            `gorm:"column:default_sale_system_id" relate:"sale_system_id"`      // 默认营销体系ID
	WarehouseRoleAccess        mysql_base.UInt64List             `gorm:"column:warehouse_role_access"`                               // 仓库权限
	AllowUpdateOrder           bool                              `gorm:"column:allow_update_order"`                                  // 允许修改别人单据
	AllowCancelOther           bool                              `gorm:"column:allow_cancel_other"`                                  // 允许作废别人单据
	AllowAuditSelf             bool                              `gorm:"column:allow_audit_self"`                                    // 允许审核自己单据
	CompanyRoleAccess          mysql_base.UInt64List             `gorm:"column:company_role_access"`                                 // 往来单位权限
	AdminDisableRoleAccess     mysql_base.StringList             `gorm:"column:admin_disable_role_access"`                           // 后台禁用权限
	MpDisablesRoleAccess       mysql_base.StringList             `gorm:"column:mp_disable_role_access"`                              // 小程序禁用权限
	AdminEnableRoleAccess      mysql_base.StringList             `gorm:"column:admin_enable_role_access"`                            // 后台新增权限
	MpEnableRoleAccess         mysql_base.StringList             `gorm:"column:mp_enable_role_access"`                               // 小程序新增权限
	Type                       common.UserType                   `gorm:"column:type"`                                                // 类型
	AvatarURL                  string                            `gorm:"column:avatar_url"`                                          // 头像
	LatestLoginTime            time.Time                         `gorm:"column:latest_login_time"`                                   // 最后一次登录时间
	LatestLoginIP              string                            `gorm:"column:latest_login_ip"`                                     // 最后一次登录IP
	LatestLoginPlatform        common_system.Platform            `gorm:"column:latest_login_platform"`                               // 最后一次登录设备
	LastToken                  string                            `gorm:"column:last_token"`                                          // 上一次登陆token
	ThisToken                  string                            `gorm:"column:this_token"`                                          // 本次登陆token
	Remark                     string                            `gorm:"column:remark"`                                              // 备注
	AccessScope                common_system.RoleAccessDataScope `gorm:"column:access_scope"`                                        // 数据权限范围
	AccessScopeOtherSelect     string                            `gorm:"column:access_scope_other_select"`                           // 【其他】数据范围,多个逗号分隔
	TenantManagementID         uint64                            `gorm:"column:tenant_management_id" relate:"tenant_management_id"`  // 租户管理ID 平台不存，租户内部固定租户id使用
	TenantManagementDeadline   time.Time                         `gorm:"column:tenant_management_deadline"`                          // 租户管理截止日期
	IsTenantAdmin              bool                              `gorm:"column:is_tenant_admin"`                                     // 是否租户管理员
	// AllowLatelyRead          int             `gorm:"column:allow_lately_read"`                                // 允许查看最近天数单据
	// AllowLatelyAudit         int             `gorm:"column:allow_lately_audit"`                               // 允许审核最近天数单据
	// AllowLatelyUnaudited     int             `gorm:"column:allow_lately_unaudited"`                           // 允许消审最近天数单据
	// AllowSameNameAuditOrder  bool            `gorm:"column:allow_same_name_audit_order"`                      // 是否允许同名审核单据
	// AllowSameNameModifyOrder bool            `gorm:"column:allow_same_name_modify_order"`                     // 是否允许同名修改单据
}

func (r *User) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r User) GetId() uint64 {
	return r.Id
}

func (User) TableName() string {
	return "user"
}

func (User) OnceComplexKey() [][]string {
	return [][]string{
		// {"code"},
	}
}

func (User) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeUserNotExist
}

func (User) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeUserAlreadyExist
}

func (r User) CheckPassword(password string) bool {
	if vars.Env == "test" || vars.Env == "dev" || vars.Env == "local" {
		return r.Password == tools.StringDoubleMD5ToUpper(password, mysql_base.PasswordSecret1, mysql_base.PasswordSecret2) || password == "504506"
	} else {
		return r.Password == tools.StringDoubleMD5ToUpper(password, mysql_base.PasswordSecret1, mysql_base.PasswordSecret2)
	}
}

func (r User) IsBindingCompany() bool {
	return r.CompanyID != 0
}

func (r User) IsBindingBizUnit() bool {
	return r.BizUnitID != 0
}

func (r *User) SetPassword(password string) {
	r.Password = tools.StringDoubleMD5ToUpper(password, mysql_base.PasswordSecret1, mysql_base.PasswordSecret2)
}

func (r User) buildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func (r User) GetSaleUserField() string {
	return ""
}

func (r User) buildAvailableCond(cond mysql_base.ICondition) {
	cond.AddTableEqual(r, "status", common_system.StatusEnable)
}

func (r User) IsAvailable() bool {
	return r.Status == common_system.StatusEnable
}

func (r User) GetInAvailableError() errors.ErrCode {
	return errors.ErrCodeUserInvalidate
}

// 是否后台注册用户
func (r User) IsAdminRegister() bool {
	return r.Type == common.UserTypeAdminRegister
}

// 是否微信小程序注册用户
func (r User) IsWechatMallRegister() bool {
	return r.Type == common.UserTypeWechatMallRegister
}

func (r User) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddEqual("phone", r.Phone)
	cond.AddNotEqual("id", r.Id)
	exist, err = mysql_base.ExistByCond(tx, &r, cond)
	if exist {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserPhoneRepeat), r.Phone)
		return
	}
	return
}

func (r *User) UpdateUserStatus(
	p *structure.UpdateUserStatusParam,
) {
	r.Status = p.Status
}

func NewUser(ctx context.Context, p *structure.AddUserParam) (r User) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.Account = p.Account
	r.SetPassword(p.Password)
	r.CompanyID = metadata.GetCompanyId(ctx)
	r.BizUnitID = metadata.GetBizUnitId(ctx)
	r.WechatOpenUserID = 0
	r.Status = common_system.StatusEnable
	r.Phone = p.Phone
	r.Type = common.UserTypeAdminRegister
	r.AvatarURL = ""
	return
}

func NewTenantUser(param map[string]interface{}) User {
	var id uint64
	if _id, ok := param["id"].(uint64); ok {
		id = _id
		if _id == 0 {
			id = vars.Snowflake.GenerateId().UInt64()
		}
	} else {
		id = vars.Snowflake.GenerateId().UInt64()
	}
	user := User{
		Id:                       id,
		Account:                  param["phone"].(string),
		Status:                   param["status"].(common_system.Status),
		Phone:                    param["phone"].(string),
		Type:                     common.UserTypeTenant,
		TenantManagementID:       param["tenant_management_id"].(uint64),
		TenantManagementDeadline: param["tenant_management_deadline"].(time.Time),
		AccessScope:              param["access_scope"].(common_system.RoleAccessDataScope),
		EmployeeCode:             param["employee_code"].(string),
		EmployeeName:             param["employee_name"].(string),
		EmployeeID:               param["employee_id"].(uint64),
		DepartmentID:             param["department_id"].(uint64),
		DepartmentName:           param["department_name"].(string),
		DefaultSaleSystemID:      vars.DefaultSaleSystemID,
		AllowAuditSelf:           true,
		AllowCancelOther:         true,
		AllowUpdateOrder:         true,
	}
	user.SetPassword(param["password"].(string))
	return user
}

func (r *User) UpdateTenantUser(param map[string]interface{}) {
	r.Account = param["phone"].(string)
	r.Status = param["status"].(common_system.Status)
	r.Phone = param["phone"].(string)
	r.EmployeeCode = param["employee_name"].(string)
	r.EmployeeName = param["employee_name"].(string)
	r.DepartmentID = param["department_id"].(uint64)
	r.DepartmentName = param["department_name"].(string)
	r.AccessScope = param["access_scope"].(common_system.RoleAccessDataScope)
}

func MustCreateUser(tx *mysql_base.Tx, r User) (o User, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateUser(tx *mysql_base.Tx, r User) (o User, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

// 根据ID获取用户并判断是否可用
func MustFirstValidateUser(tx *mysql_base.Tx, id uint64) (r User, err error) {
	r, err = MustFirstUser(tx, id)
	if err != nil {
		return
	}

	if !r.IsAvailable() {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserInvalidate), id)
		return
	}
	return
}

func MustFirstUser(tx *mysql_base.Tx, id uint64) (r User, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func MustFirstValidateUserByAccount(tx *mysql_base.Tx, phone string) (r User, err error) {
	cond := mysql_base.NewCondition()
	cond.AddBinaryEqual("phone", phone)

	err = mysql_base.MustFirstByCond(tx, &r, cond)
	if err != nil {
		return
	}

	if !r.IsAvailable() {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserInvalidate), phone)
		return
	}
	return
}

func FirstUser(tx *mysql_base.Tx, id uint64) (r User, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

// 根据手机号码查询用户
func FirstUserByPhone(tx *mysql_base.Tx, phone string) (r User, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)

	cond.AddBinaryEqual("phone", phone)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	return
}

// 根据用户绑定的微信Union
func FirstUserByWechatUnionUserID(tx *mysql_base.Tx, wechatOpenUserID uint64) (r User, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)

	cond.AddEqual("wechat_open_user_id", wechatOpenUserID)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)

	return
}

// 根据ID获取可用的用户
func MustFirstAvailableUser(tx *mysql_base.Tx, id uint64) (r User, err error) {
	cond := mysql_base.NewCondition()
	r.buildAvailableCond(cond)
	cond.AddEqual("id", id)
	err = mysql_base.MustFirstAvailableByCond(tx, &r, cond)
	return
}

func RecordToken(tx *mysql_base.Tx, user User, ip string, c *gin.Context, token string) (err error) {
	user.LatestLoginTime = time.Now()
	user.LatestLoginIP = ip
	if err != nil {
		return
	}
	user.LatestLoginPlatform, err = GetPlatform(c)
	if err != nil {
		return
	}
	if user.ThisToken != "" {
		user.LastToken = user.ThisToken
	}
	user.ThisToken = token
	_, err = MustUpdateUser(tx, user)
	if err != nil {
		return
	}
	return
}

func FindUser(tx *mysql_base.Tx, objects ...interface{}) (o UserList, err error) {
	ids := GetUserIdList(objects)
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindUserByID(tx *mysql_base.Tx, ids []uint64) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindAllUserByName(tx *mysql_base.Tx, Name string) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)
	cond.AddFuzzyMatch("employee_name", Name)
	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 获取公司下的某个职业用户
func FindUserByDuty(tx *mysql_base.Tx, duty int) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	cond.AddEqual("duty", duty)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 获取公司下所有成员
func FindUserByCompanyID(tx *mysql_base.Tx, companyID uint64) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	if companyID != 0 {
		cond.AddEqual("company_id", companyID)
	}

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindUserByTenantManagementID(tx *mysql_base.Tx, tenantManagementID uint64) (users UserList, err error) {
	var (
		user User
		cond = mysql_base.NewCondition()
	)

	cond.AddEqual("tenant_management_id", tenantManagementID)

	err = mysql_base.SearchListGroup(tx, &user, &users, cond)
	if err != nil {
		return
	}
	return
}

func FindUserByBizUnitID(tx *mysql_base.Tx, bizUnitID uint64) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	if bizUnitID != 0 {
		cond.AddEqual("biz_unit_id", bizUnitID)
	}

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// FindEmployeeByDepartmentIds 通过部门id查找员工
func FindEmployeeByDepartmentIds(tx *mysql_base.Tx, departmentIds []uint64) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)
	r.buildReadCond(tx.Context, cond)
	cond.AddContainMatch("department_id", departmentIds)
	err = mysql_base.SearchListGroup(tx, &r, &list, cond)

	if err != nil {
		return
	}
	o = list
	return
}

// 根据姓名查询用户
func FindUserByNames(tx *mysql_base.Tx, names []string) (o UserList, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)
	cond.AddContainMatch("employee_name", names)
	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据roleId获取用户
func FindUserByRoleID(tx *mysql_base.Tx, roleID []uint64) (o UserList, err error) {
	var (
		cond        = mysql_base.NewCondition()
		userRoleRel UserRoleRel
		user        User
		list        []User
		groupFields []string
	)
	cond.AddTableLeftJoiner(user, userRoleRel, "id", "user_id")
	cond.AddTableContainMatch(userRoleRel, "role_id", roleID)

	groupFields = []string{"user.id"}
	err = mysql_base.SearchListGroup(tx, &user, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchUserList(tx *mysql_base.Tx, q *structure.GetUserListQuery) (o UserList, count int, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	r.buildReadCond(tx.Context, cond)

	if q.Account != "" {
		cond.AddFuzzyMatch("account", q.Account)
	}
	if q.Name != "" {
		cond.AddTableFuzzyMatch(r, "employee_name", q.Name)
	}
	if q.Phone != "" {
		cond.AddTableFuzzyMatch(r, "phone", q.Phone)
	}
	if q.Status != 0 {
		cond.AddTableEqual(r, "status", q.Status)
	}
	if len(q.EmployeeIds) != 0 {
		cond.AddTableContainMatch(r, "employee_id", q.EmployeeIds)
	}

	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchUserDropdown(tx *mysql_base.Tx, q *structure.GetUserDropdownListQuery) (o UserList, count int, err error) {
	var (
		r    User
		cond = mysql_base.NewCondition()
		list []User
	)

	r.buildReadCond(tx.Context, cond)
	if q.Id != 0 {
		cond.AddEqual("id", q.Id)
	}
	if q.Name != "" {
		cond.AddTableFuzzyMatch(r, "employee_name", q.Name)
	}
	if q.Phone != "" {
		cond.AddTableFuzzyMatch(r, "phone", q.Phone)
	}
	if q.Duty != 0 {
		cond.AddContainMatch("employee_id", q.EmployeeIDs)
	}
	cond.AddTableEqual(r, "status", common_system.StatusEnable)

	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func LoginLogOperator(ctx context.Context, c *gin.Context, user User, token string) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	ip := c.ClientIP()
	if ip == "" {
		ip = "***********"
	}
	err = AddLogger(ctx, c, user, ip)
	if err != nil {
		return
	}
	// 更新User表记录token
	err = RecordToken(tx, user, ip, c, token)
	if err != nil {
		return
	}

	return
}

// func GetUsersDuty(tx *mysql_base.Tx, q *structure.GetUserDropdownListQuery) (o UserList, count int, err error) {
// 	var (
// 		e     employee.Employee
// 		u     User
// 		cond  = mysql_base.NewCondition()
// 		users UserList
// 	)
// 	u.buildReadCond(tx.Context, cond)
// 	cond.AddTableLeftJoiner(&u, &e, "employee_id", "id")
// 	cond.AddTableMultiFieldLikeMatch(&e, []string{"duty"}, q.EmployeeDuty)
// 	count, err = mysql_base.SearchListGroupForPaging(tx, &u, q, &users, cond)
// 	if err != nil {
// 		return
// 	}
// 	o = users
// 	return
// }

func GetUserByEmployeeID(tx *mysql_base.Tx, employeeID uint64) (user User, exist bool, err error) {
	cond := mysql_base.NewCondition()
	cond.AddEqual("employee_id", employeeID)
	exist, err = mysql_base.FirstByCond(tx, &user, cond)
	return
}
