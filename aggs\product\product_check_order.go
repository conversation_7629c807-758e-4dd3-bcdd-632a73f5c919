package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product2 "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/basic_data/warehouse"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/department"
	"hcscm/extern/pb/dictionary"
	"hcscm/extern/pb/employee"
	"hcscm/extern/pb/sale_system"
	user_pb "hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strings"
	"time"
)

type IProductCheckOrderRepo interface {
	Add(ctx context.Context, req *structure.AddProductCheckOrderParam) (id uint64, err error)
	Swag2AddProductCheckOrderParam(ctx context.Context, req *structure.SplitStockDetailParam) (swap structure.AddProductCheckOrderParamList, err error)
	AddProductCheckOrderItemDetails(ctx context.Context, req *structure.AddProductCheckOrderParam, productCheckOrderId uint64) (err error)
	AddProductCheckOrderWeightItemDetails(ctx context.Context, req *structure.AddProductCheckOrderItemParam, productCheckOrderId, productCheckOrderItemItemId, warehouseId uint64) (err error)
	Update(ctx context.Context, req *structure.UpdateProductCheckOrderParam) (id uint64, err error)
	UpdateDetailStockDetailId(ctx context.Context, ids map[uint64]uint64, sumIds map[uint64]uint64, productCheckOrderId uint64) (err error)
	UpdateProductCheckOrderItemDetails(ctx context.Context, req *structure.UpdateProductCheckOrderParam, productCheckOrderId uint64) (err error)
	UpdateProductCheckOrderWeightItemDetails(ctx context.Context, req *structure.AddProductCheckOrderItemParam, productCheckOrderId, productCheckOrderItemItemId, warehouseId uint64) (err error)
	UpdateStatusPass(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, addItems structure.AddStockProductDetailParamList, updateItems structure.UpdateStockProductDetailParamList, err error)
	UpdateStatusWait(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, updateItems structure.UpdateStockProductDetailParamList, err error)
	UpdateStatusReject(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, err error)
	UpdateStatusCancel(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, err error)
	UpdateBusinessClose(ctx context.Context, req *structure.UpdateProductCheckOrderBusinessCloseParam) (data structure.UpdateProductCheckOrderBusinessCloseData, err error)
	Get(ctx context.Context, id uint64) (data structure.GetProductCheckOrderDetailData, err error)
	GetWithoutItems(ctx context.Context, id uint64) (data structure.PDAGetProductCheckOrderDetailData, err error)
	GetList(ctx context.Context, req *structure.GetProductCheckOrderListQuery) (list structure.GetProductCheckOrderDataList, total int, err error)
	GetDetailList(ctx context.Context, req *structure.GetProductCheckOrderListQuery) (list structure.GetProductCheckOrderItemDataList, total int, err error)

	// 更新仓库仓位
	ChangeWarehouseBin(ctx context.Context, stockDetailId, warehouseBinId uint64) (err error)
	// 新增盘点单
	AddProductCheckOrderWeightItemDetail(ctx context.Context, item structure.AddProductCheckOrderWeightItemParam, productCheckOrderId, productCheckOrderItemItemId, warehouseId uint64) (err error)
	UpdateProductCheckOrderWeightItemDetail(ctx context.Context, item structure.AddProductCheckOrderWeightItemParam, productCheckOrderItemItemId, productCheckOrderItemWeightId uint64) (err error)
	DeleteProductCheckOrderWeightItemDetail(ctx context.Context, item structure.AddProductCheckOrderWeightItemParam, productCheckOrderItemItemId, productCheckOrderItemWeightId uint64) (err error)
	GetProductCheckOrderWeightItemDetail(ctx context.Context, req *structure.GetProductCheckOrderWeightItemQuery) (data structure.GetProductCheckOrderWeightItemData, exist bool, err error)

	GetCheckOrderItemReport(ctx context.Context, req *structure.GetProductCheckOrderItemQuery) (data structure.GetProductCheckOrderItemReportDataList, count int, err error)
}

type ProductCheckOrderRepo struct {
	tx *mysql_base.Tx
}

func NewProductCheckOrderRepo(tx *mysql_base.Tx) IProductCheckOrderRepo {
	return &ProductCheckOrderRepo{tx: tx}
}

// 新增成品盘点单主单
func (r *ProductCheckOrderRepo) Add(ctx context.Context, req *structure.AddProductCheckOrderParam) (id uint64, err error) {
	var (
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_system.NewSaleSystemClient()
		saleSysData        = sale_system.Res{}
	)
	productCheckOrder := model.NewProductCheckOrder(ctx, req)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_system.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmStockCheckOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmStockCheckOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", productCheckOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	productCheckOrder.OrderNo = orderNo
	productCheckOrder.Number = int(number)

	productCheckOrder, err = mysql.MustCreateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}

	return productCheckOrder.Id, err
}

// 转换成AddProductCheckOrderParam
func (r *ProductCheckOrderRepo) Swag2AddProductCheckOrderParam(ctx context.Context, req *structure.SplitStockDetailParam) (
	swap structure.AddProductCheckOrderParamList, err error) {

	var (
		// volumeNumber               = 0
		// maxVolumeNumber            = 0
		// volumeNumberMaxMap         = make(map[string]int)
		stockDetailIds             = set.NewUint64Set()
		stockSumIds                = set.NewUint64Set()
		productIds                 = set.NewUint64Set()
		stockSumList               = model.StockProductList{}
		stockDetailList            = model.StockProductDetailList{}
		stockSumMap                = make(map[uint64]model.StockProduct)
		stockDetailMap             = make(map[uint64]model.StockProductDetail)
		uniqKeyList                = make(map[uint64]model.StockProductDetailList)
		splitWeightMap             = make(map[uint64]structure.SplitStockDetailParam)
		productMap                 = make(map[uint64]*product2.ProductRes)
		checkProductOrderParamList = structure.AddProductCheckOrderParamList{}
	)

	// for _, param := range req.SplitStockDetailParamList {
	//	stockDetailIds.Add(param.StockDetailId)
	//	stockSumIds.Add(param.StockSumId)
	//	splitWeightMap[param.StockDetailId] = param
	// }
	stockDetailIds.Add(req.StockDetailId)
	stockSumIds.Add(req.StockSumId)
	splitWeightMap[req.StockDetailId] = *req
	// 找详细库存
	stockDetailList, err = mysql.FindStockProductDetailByIDs(r.tx, stockDetailIds.List())
	if err != nil {
		return
	}
	// 找汇总库存
	stockSumList, err = mysql.FindStockProductByIDs(r.tx, stockSumIds.List())
	if err != nil {
		return
	}

	for _, sumStock := range stockSumList {
		stockSumMap[sumStock.Id] = sumStock
		productIds.Add(sumStock.ProductId)
	}
	for _, StockDetail := range stockDetailList {
		uniqKeyList[StockDetail.WarehouseId+StockDetail.WarehouseBinId] = append(uniqKeyList[StockDetail.WarehouseId+StockDetail.WarehouseBinId], StockDetail)
		stockDetailMap[StockDetail.Id] = StockDetail
	}

	productMap, err = product2.NewProductClient().GetProductMapByIds(ctx, productIds.List())
	if err != nil {
		return
	}

	for _, detailList := range uniqKeyList {
		var (
			sumAndDetailListMap            = make(map[uint64]model.StockProductDetailList)
			checkProductOrderParam         = structure.AddProductCheckOrderParam{}
			ProductCheckOrderItemParamList = structure.AddProductCheckOrderItemParamList{}
		)

		// 分配给不同的汇总库存
		for _, detail := range detailList {
			sumAndDetailListMap[detail.StockProductId] = append(sumAndDetailListMap[detail.StockProductId], detail)
		}

		// 已处理完分类--赋值操作
		// 赋值主单
		checkProductOrderParam.SaleSystemId = metadata.GetSaleSystemId(ctx)
		checkProductOrderParam.CheckTime = tools.QueryTime(time.Now().Format("2006-01-02"))
		checkProductOrderParam.DepartmentId = metadata.GetDepartmentId(ctx)
		for k, v := range sumAndDetailListMap {
			// item
			var (
				ProductCheckOrderItemParam    = structure.AddProductCheckOrderItemParam{}
				checkOrderWeightItemParamList = structure.AddProductCheckOrderWeightItemParamList{}
				sumStock                      = stockSumMap[k]
			)
			newBySumStockInfo(&ProductCheckOrderItemParam, sumStock, k)
			// 库存计量单位取基础资料的
			if product, ok := productMap[sumStock.ProductId]; ok {
				ProductCheckOrderItemParam.MeasurementUnitId = product.MeasurementUnitId
				ProductCheckOrderItemParam.MeasurementUnitName = product.MeasurementUnitName
			}

			// 细码
			for _, detail := range v {
				splitWeight := splitWeightMap[detail.Id]
				// 转换赋值细码
				checkOrderWeightItemParam := structure.AddProductCheckOrderWeightItemParam{}
				err = newByDetailStockInfo(&checkOrderWeightItemParam, detail, splitWeight, sumStock.Remark)
				if err != nil {
					return
				}
				// 库存计量单位取基础资料的
				if product, ok := productMap[detail.ProductId]; ok {
					checkOrderWeightItemParam.MeasurementUnitId = product.MeasurementUnitId
					checkOrderWeightItemParam.MeasurementUnitName = product.MeasurementUnitName
				}
				// copy一份当做盘盈新增
				// checkOrderWeightItemParamCopy := checkOrderWeightItemParam
				// 找最大的卷号
				// if val, ok := volumeNumberMaxMap[detail.DyelotNumber+strconv.Itoa(int(detail.ProductColorId))]; ok {
				//	volumeNumberMaxMap[detail.DyelotNumber+strconv.Itoa(int(detail.ProductColorId))] = val + 1
				// } else {
				//	volumeNumber, _, err = mysql.GetMaxVolumeNumber(r.tx, detail.DyelotNumber, detail.ProductColorId, 0)
				//	if err != nil {
				//		return
				//	}
				//	volumeNumberMaxMap[detail.DyelotNumber+strconv.Itoa(int(detail.ProductColorId))] = volumeNumber + 1
				// }
				// maxVolumeNumber = volumeNumberMaxMap[detail.DyelotNumber+strconv.Itoa(int(detail.ProductColorId))]
				for _, splitWeightParam := range splitWeight.SplitWeightParamList {
					// copy一份当做盘盈新增 checkOrderWeightItemParamCopy 盘盈新增
					checkOrderWeightItemParamCopy := checkOrderWeightItemParam
					copy2CheckOrderWeightItemParam(&checkOrderWeightItemParamCopy, splitWeightParam)
					checkOrderWeightItemParamList = append(checkOrderWeightItemParamList, checkOrderWeightItemParamCopy)
					ProductCheckOrderItemParam.CheckRoll += checkOrderWeightItemParamCopy.CheckRoll
					ProductCheckOrderItemParam.CheckWeight += checkOrderWeightItemParamCopy.CheckWeight
					ProductCheckOrderItemParam.CheckLength += checkOrderWeightItemParamCopy.CheckLength
				}
				checkOrderWeightItemParamList = append(checkOrderWeightItemParamList, checkOrderWeightItemParam)
				checkProductOrderParam.Remark = "细码拆布处理"
				checkProductOrderParam.WarehouseId = detail.WarehouseId
				checkProductOrderParam.WarehouseBinId = detail.WarehouseBinId
				ProductCheckOrderItemParam.DyelotNumber = detail.DyelotNumber
				ProductCheckOrderItemParam.ProductRemark = detail.ProductRemark
				ProductCheckOrderItemParam.CustomerId = detail.CustomerId
				ProductCheckOrderItemParam.ProductLevelId = detail.ProductLevelId

				ProductCheckOrderItemParam.CheckRoll += checkOrderWeightItemParam.CheckRoll
				ProductCheckOrderItemParam.CheckWeight += checkOrderWeightItemParam.CheckWeight
				ProductCheckOrderItemParam.CheckLength += checkOrderWeightItemParam.CheckLength
			}
			ProductCheckOrderItemParam.ItemData = checkOrderWeightItemParamList
			ProductCheckOrderItemParamList = append(ProductCheckOrderItemParamList, ProductCheckOrderItemParam)
		}
		checkProductOrderParam.ItemData = ProductCheckOrderItemParamList
		checkProductOrderParamList = append(checkProductOrderParamList, checkProductOrderParam)
	}

	swap = checkProductOrderParamList
	return
}
func copy2CheckOrderWeightItemParam(param *structure.AddProductCheckOrderWeightItemParam, split structure.SplitWeightParam) {
	param.Roll = 0
	param.Weight = 0
	param.Length = 0
	param.StockProductDetailId = 0
	param.QrCode = ""
	param.CheckRoll = split.SplitRoll
	param.CheckWeight = split.SplitWeight
	param.CheckLength = split.SplitLength
	// param.VolumeNumber = volumeNumber
	// if split.SplitVolumeNumber > 0 {
	param.VolumeNumber = split.SplitVolumeNumber

	// }
	return
}

func newBySumStockInfo(p *structure.AddProductCheckOrderItemParam, sumStock model.StockProduct, sumStockId uint64) {
	p.StockProductId = sumStockId
	p.ProductId = sumStock.ProductId
	p.ProductColorId = sumStock.ProductColorId
	p.CustomerId = sumStock.CustomerId
	p.ProductLevelId = sumStock.ProductLevelId
	p.ProductRemark = sumStock.ProductRemark
	p.Remark = sumStock.Remark
	// p.MeasurementUnitId = sumStock.MeasurementUnitId
	p.Roll = sumStock.StockRoll
	p.Weight = sumStock.Weight
	p.Length = sumStock.Length
}

func newByDetailStockInfo(p *structure.AddProductCheckOrderWeightItemParam, detail model.StockProductDetail,
	splitWeight structure.SplitStockDetailParam, remark string) error {
	p.StockProductId = detail.StockProductId
	p.StockProductDetailId = detail.Id
	p.WarehouseInType = detail.WarehouseInType
	p.WarehouseInOrderId = detail.WarehouseInOrderId
	p.WarehouseInOrderNo = detail.WarehouseInOrderNo
	p.FinishProductWidth = detail.FinishProductWidth
	p.FinishProductGramWeight = detail.FinishProductGramWeight
	p.WeightError = detail.WeightError
	p.PaperTubeWeight = detail.PaperTubeWeight
	p.DyeFactoryColorCode = detail.DyeFactoryColorCode
	p.ProductColorId = detail.ProductColorId
	p.ProductColorKindId = detail.ProductColorKindId
	p.WarehouseId = detail.WarehouseId
	p.CustomerId = detail.CustomerId
	p.ProductId = detail.ProductId
	p.DigitalCode = detail.DigitalCode
	p.ProductLevelId = detail.ProductLevelId
	p.ProductKindId = detail.ProductKindId
	p.ContractNumber = detail.ContractNumber
	p.CustomerPoNum = detail.CustomerPoNum
	p.CustomerAccountNum = detail.CustomerAccountNum
	p.ShelfNo = detail.ShelfNo
	p.StockRemark = remark
	p.InternalRemark = detail.InternalRemark
	p.Remark = detail.Remark
	// p.MeasurementUnitId = detail.MeasurementUnitId
	p.DyelotNumber = detail.DyelotNumber
	p.VolumeNumber = detail.VolumeNumber
	p.WarehouseBinId = detail.WarehouseBinId
	p.Roll = detail.Roll
	p.Weight = detail.Weight
	p.Length = detail.Length
	p.FinishProductWidthUnitId = detail.FinishProductWidthUnitId
	p.FinishProductGramWeightUnitId = detail.FinishProductGramWeightUnitId
	p.QrCode = detail.QrCode

	totalSplitWeight := 0
	totalSplitLength := 0
	totalSplitRoll := 0
	for _, param := range splitWeight.SplitWeightParamList {
		totalSplitWeight += param.SplitWeight
		totalSplitLength += param.SplitLength
		totalSplitRoll += param.SplitRoll
	}
	// p.CheckRoll = detail.Roll - totalSplitRoll
	// p.CheckWeight = detail.Weight - totalSplitWeight
	// p.CheckLength = detail.Length - totalSplitLength
	p.CheckRoll = splitWeight.CheckRoll
	p.CheckWeight = splitWeight.CheckWeight
	p.CheckLength = splitWeight.CheckLength
	// if detail.Weight != totalSplitWeight+splitWeight.CheckWeight {
	//	return errors.NewCustomError(errors.ErrCodeMysqlCreate, " 拆布与原来库存数量不相等")
	// }
	if totalSplitWeight+p.CheckWeight != detail.Weight {
		return errors.NewCustomError(errors.ErrCodeMysqlCreate, " 拆布与原来库存数量不相等")
	}
	return nil
}

// 新增成品盘点单成品信息
func (r *ProductCheckOrderRepo) AddProductCheckOrderItemDetails(ctx context.Context, req *structure.AddProductCheckOrderParam, productCheckOrderId uint64) (err error) {

	for _, item := range req.ItemData {
		for _, detail := range item.ItemData {
			item.Roll += detail.Roll
			item.Weight += detail.Weight
			item.Length += detail.Length
		}

		productCheckOrderItemItem := model.NewProductCheckOrderItem(ctx, &item, productCheckOrderId)

		productCheckOrderItemItem, err = mysql.MustCreateProductCheckOrderItem(r.tx, productCheckOrderItemItem)
		if err != nil {
			return
		}
		// 新增成品细码信息
		err = r.AddProductCheckOrderWeightItemDetails(ctx, &item, productCheckOrderId, productCheckOrderItemItem.Id, req.WarehouseId)
		if err != nil {
			return
		}
	}
	return
}

// 新增成品盘点单成品细码信息
func (r *ProductCheckOrderRepo) AddProductCheckOrderWeightItemDetails(ctx context.Context, req *structure.AddProductCheckOrderItemParam, productCheckOrderId, productCheckOrderItemItemId, warehouseId uint64) (err error) {
	productIds := mysql_base.GetUInt64List(req, "product_id")
	productSvc := product2.NewProductClient()
	productItems, err := productSvc.GetProductList(ctx, product2.ProductReq{Ids: productIds})
	if err != nil {
		return
	}
	productColorIds := mysql_base.GetUInt64List(req, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItems, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	for _, item := range req.ItemData {
		productItem := productItems.PickByProductId(item.ProductId)
		productColorItem := productColorItems.PickByProductColorId(item.ProductColorId)
		item.WarehouseId = warehouseId
		item.ProductId = req.ProductId
		item.ProductColorId = req.ProductColorId
		item.CustomerId = req.CustomerId
		item.ProductColorKindId = productColorItem.TypeFinishedProductKindId
		item.ProductKindId = productItem.TypeGreyFabricId
		item.DyelotNumber = req.DyelotNumber
		item.ProductLevelId = req.ProductLevelId
		productCheckOrderWeightItemItem := model.NewProductCheckOrderWeightItem(ctx, &item, productCheckOrderId, productCheckOrderItemItemId)

		productCheckOrderWeightItemItem, err = mysql.MustCreateProductCheckOrderWeightItem(r.tx, productCheckOrderWeightItemItem)
		if err != nil {
			return
		}
	}
	return
}

// 新增成品盘点单成品细码信息
func (r *ProductCheckOrderRepo) UpdateProductCheckOrderWeightItemDetails(ctx context.Context, req *structure.AddProductCheckOrderItemParam, productCheckOrderId, productCheckOrderItemItemId, warehouseId uint64) (err error) {
	var (
		weightList model.ProductCheckOrderWeightItemList
		itemMap    = make(map[uint64]bool)
	)
	weightList, err = mysql.FindProductCheckOrderWeightItemByParentID(r.tx, productCheckOrderItemItemId)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(req, "product_id")
	productSvc := product2.NewProductClient()
	productItems, err := productSvc.GetProductList(ctx, product2.ProductReq{Ids: productIds})
	if err != nil {
		return
	}
	productColorIds := mysql_base.GetUInt64List(req, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItems, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	for _, item := range req.ItemData {
		if item.Id == 0 {
			productItem := productItems.PickByProductId(item.ProductId)
			productColorItem := productColorItems.PickByProductColorId(item.ProductColorId)
			item.WarehouseId = warehouseId
			item.ProductId = req.ProductId
			item.ProductColorId = req.ProductColorId
			item.CustomerId = req.CustomerId
			item.ProductColorKindId = productColorItem.TypeFinishedProductKindId
			item.ProductKindId = productItem.TypeGreyFabricId
			item.DyelotNumber = req.DyelotNumber
			item.ProductLevelId = req.ProductLevelId
			productCheckOrderWeightItemItem := model.NewProductCheckOrderWeightItem(ctx, &item, productCheckOrderId, productCheckOrderItemItemId)

			productCheckOrderWeightItemItem, err = mysql.MustCreateProductCheckOrderWeightItem(r.tx, productCheckOrderWeightItemItem)
			if err != nil {
				return
			}
			continue
		}
		itemMap[item.Id] = true
		productCheckOrderWeightItemItem := weightList.Pick(item.Id)
		productCheckOrderWeightItemItem.UpdateProductCheckOrderWeightItem(ctx, &item, productCheckOrderId, productCheckOrderItemItemId)

		productCheckOrderWeightItemItem, err = mysql.MustUpdateProductCheckOrderWeightItem(r.tx, productCheckOrderWeightItemItem)
		if err != nil {
			return
		}
	}

	delItemIds := make([]uint64, 0)
	for _, item := range weightList {
		if !itemMap[item.Id] {
			delItemIds = append(delItemIds, item.Id)
		}
	}
	if len(delItemIds) != 0 {
		err = mysql.MustDeleteProductCheckOrderWeightItemByIDs(r.tx, delItemIds)
		if err != nil {
			return
		}
	}
	return
}

// 更新成品盘点单
func (r *ProductCheckOrderRepo) Update(ctx context.Context, req *structure.UpdateProductCheckOrderParam) (id uint64, err error) {
	var (
		productCheckOrder model.ProductCheckOrder
	)
	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := productCheckOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	productCheckOrder.UpdateProductCheckOrder(ctx, req)

	productCheckOrder, err = mysql.MustUpdateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}
	return productCheckOrder.Id, err
}

// 更新库存详情id到成品盘点单成品细码信息
func (r *ProductCheckOrderRepo) UpdateDetailStockDetailId(ctx context.Context, ids map[uint64]uint64, sumIds map[uint64]uint64, productCheckOrderId uint64) (err error) {
	var (
		weightList        model.ProductCheckOrderWeightItemList
		ItemList          model.ProductCheckOrderItemList
		itemSumStockIdMap = make(map[uint64]uint64, 0)
	)

	//
	weightList, err = mysql.FindProductCheckOrderWeightItemByGrandParentID(r.tx, productCheckOrderId)
	if err != nil {
		return
	}
	ItemList, err = mysql.FindProductCheckOrderItemByParentID(r.tx, productCheckOrderId)
	if err != nil {
		return
	}
	for _, weight := range weightList {
		if stockDetailId, ok := ids[weight.Id]; ok {
			weight.StockProductDetailId = stockDetailId
		}
		if stockId, ok := sumIds[weight.Id]; ok {
			itemSumStockIdMap[weight.ProductCheckOrderItemId] = stockId
			weight.StockProductId = stockId
		}
		weight, err = mysql.MustUpdateProductCheckOrderWeightItem(r.tx, weight)
		if err != nil {
			return
		}
	}
	for _, item := range ItemList {
		if stockId, ok := itemSumStockIdMap[item.Id]; ok {
			item.StockProductId = stockId
		}
		item, err = mysql.MustUpdateProductCheckOrderItem(r.tx, item)
		if err != nil {
			return
		}
	}
	return
}

// 更新成品盘点单成品信息
func (r *ProductCheckOrderRepo) UpdateProductCheckOrderItemDetails(ctx context.Context, req *structure.UpdateProductCheckOrderParam, productCheckOrderId uint64) (err error) {
	var (
		itemList model.ProductCheckOrderItemList
		itemMap  = make(map[uint64]bool)
	)
	// 找出该单下的信息，并删除
	itemList, err = mysql.FindProductCheckOrderItemByParentID(r.tx, productCheckOrderId)
	if err != nil {
		return
	}

	// 新增成品信息
	for _, item := range req.ItemData {
		var (
			checkRoll, checkWeight, checkLength int
		)
		checkRoll, checkWeight, checkLength = item.ItemData.GetTotal()
		item.CheckRoll = checkRoll
		item.CheckWeight = checkWeight
		item.CheckLength = checkLength
		if item.Id == 0 {
			productCheckOrderItemItem := model.NewProductCheckOrderItem(ctx, &item, productCheckOrderId)

			productCheckOrderItemItem, err = mysql.MustCreateProductCheckOrderItem(r.tx, productCheckOrderItemItem)
			if err != nil {
				return
			}

			// 新增成品细码信息
			err = r.AddProductCheckOrderWeightItemDetails(ctx, &item, productCheckOrderId, productCheckOrderItemItem.Id, req.WarehouseId)
			if err != nil {
				return
			}
			continue
		}
		itemMap[item.Id] = true
		productCheckOrderItemItem := itemList.Pick(item.Id)
		productCheckOrderItemItem.UpdateProductCheckOrderItem(ctx, &item, productCheckOrderId)

		productCheckOrderItemItem, err = mysql.MustUpdateProductCheckOrderItem(r.tx, productCheckOrderItemItem)
		if err != nil {
			return
		}

		// 更新成品细码信息
		err = r.UpdateProductCheckOrderWeightItemDetails(ctx, &item, productCheckOrderId, productCheckOrderItemItem.Id, req.WarehouseId)
		if err != nil {
			return
		}
	}

	delItemIds := make([]uint64, 0)
	for _, item := range itemList {
		if !itemMap[item.Id] {
			delItemIds = append(delItemIds, item.Id)
		}
	}

	if len(delItemIds) != 0 {
		err = mysql.MustDeleteProductCheckOrderItemByIds(r.tx, delItemIds)
		if err != nil {
			return
		}

		err = mysql.MustDeleteProductCheckOrderWeightItemByParentIDs(r.tx, delItemIds)
		if err != nil {
			return
		}
	}
	return
}

// 审核
func (r *ProductCheckOrderRepo) UpdateStatusPass(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, addItems structure.AddStockProductDetailParamList, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		productCheckOrder         model.ProductCheckOrder
		productCheckOrderItemList model.ProductCheckOrderItemList
		weightList                model.ProductCheckOrderWeightItemList
		addWeight                 = make([]*structure.AddStockProductDetailParam, 0)
		updateWeight              = make([]*structure.UpdateStockProductDetailParam, 0)
	)

	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	productCheckOrderItemList, err = mysql.FindProductCheckOrderItemByParentID(r.tx, productCheckOrder.Id)
	if err != nil {
		return
	}

	// 获取需要新增以及需要修改的相关库存信息(有库存细码id的就更新,没有的就新增)
	weightList, err = mysql.FindProductCheckOrderWeightItemByGrandParentID(r.tx, productCheckOrder.Id)
	if err != nil {
		return
	}
	for _, weight := range weightList {
		productCheckOrderItem := productCheckOrderItemList.Pick(weight.ProductCheckOrderItemId)
		if weight.WarehouseBinId == 0 {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeDataError, fmt.Sprintf(",缸号:%v卷号:%v,仓位未填", productCheckOrderItem.DyelotNumber, weight.VolumeNumber)), productCheckOrder.OrderNo)
			return
		}
		// 判断条码或者二维码为旧码的要先查询一下是否有生成新码，如果有则根据新码查询库存信息，如果没有则直接使用旧码查询库存信息
		if weight.QrCode != "" {
			var split = &tools.FabricPieceCodeQrCode66{}
			split, err = tools.ParseFabricPieceCodeQrCode66(weight.QrCode)
			if err != nil {
				return
			}
			if strings.HasPrefix(split.BarCode, "001001") {
				var (
					fabricPieceCode model.FabricPieceCode
					exist           bool
					newStockDetail  = structure.GetStockProductDetailDropdownData{}
				)

				fabricPieceCode, exist, err = mysql.FirstFabricPieceCodeByID(r.tx, productCheckOrder.WarehouseId, split.DyelotNumber, weight.ProductColorId, split.SequenceNumber)
				if err != nil {
					return
				}
				if exist {
					newStockDetail, err = NewStockProductRepo(ctx, r.tx, make(map[uint64]model.StockProduct), make(map[uint64]model.StockProductDetail), set.NewConcurrentMap[string, uint64]()).GetDetailByCond(ctx, &structure.GetStockProductDetailListQuery{QrCode: fabricPieceCode.QrCode})
					if err != nil {
						return
					}
					if newStockDetail.Id > 0 {
						weight.StockProductDetailId = newStockDetail.Id
					}
				}
			}
			weight, err = mysql.MustUpdateProductCheckOrderWeightItem(r.tx, weight)
			if err != nil {
				return
			}
		}
		if weight.StockProductDetailId != 0 {
			updateWeight = append(updateWeight, weight.ToUpdateStockProductDetailParam(ctx, productCheckOrder, productCheckOrderItem))
		} else {
			addWeight = append(addWeight, weight.ToAddStockProductDetailParam(ctx, productCheckOrder, productCheckOrderItem))
		}
	}

	// 审核
	err = productCheckOrder.Audit(ctx)
	if err != nil {
		return
	}

	productCheckOrder, err = mysql.MustUpdateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}

	addItems = addWeight
	updateItems = updateWeight
	data.CheckTime = tools.MyTime(productCheckOrder.CheckTime)
	return
}

// 消审
func (r *ProductCheckOrderRepo) UpdateStatusWait(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		productCheckOrder model.ProductCheckOrder
		weightList        model.ProductCheckOrderWeightItemList
		updateWeight      = make([]*structure.UpdateStockProductDetailParam, 0)
	)

	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 获取修改的相关库存信息
	weightList, err = mysql.FindProductCheckOrderWeightItemByGrandParentID(r.tx, productCheckOrder.Id)
	if err != nil {
		return
	}
	for _, weight := range weightList {
		updateWeight = append(updateWeight, weight.ToUpdateStockProductDetailParamBack(ctx))
	}

	// 消审
	err = productCheckOrder.Wait(ctx)
	if err != nil {
		return
	}

	productCheckOrder, err = mysql.MustUpdateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}

	updateItems = updateWeight
	data.CheckTime = tools.MyTime(productCheckOrder.CheckTime)
	return
}

func (r *ProductCheckOrderRepo) UpdateStatusReject(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, err error) {
	var (
		productCheckOrder model.ProductCheckOrder
	)

	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 驳回
	err = productCheckOrder.Reject(ctx)
	if err != nil {
		return
	}

	productCheckOrder, err = mysql.MustUpdateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}

	return
}

func (r *ProductCheckOrderRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateProductCheckOrderAuditStatusParam) (data structure.UpdateProductCheckOrderAuditStatusData, err error) {
	var (
		productCheckOrder model.ProductCheckOrder
	)

	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 作废
	err = productCheckOrder.Cancel(ctx)
	if err != nil {
		return
	}

	productCheckOrder, err = mysql.MustUpdateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}

	return
}

func (r *ProductCheckOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateProductCheckOrderBusinessCloseParam) (data structure.UpdateProductCheckOrderBusinessCloseData, err error) {
	var (
		productCheckOrder model.ProductCheckOrder
	)
	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	err = productCheckOrder.UpdateBusinessClose(ctx, req.BusinessClose)
	if err != nil {
		return
	}
	productCheckOrder, err = mysql.MustUpdateProductCheckOrder(r.tx, productCheckOrder)
	if err != nil {
		return
	}

	return
}

func (r *ProductCheckOrderRepo) Get(ctx context.Context, id uint64) (data structure.GetProductCheckOrderDetailData, err error) {
	var (
		productCheckOrder               model.ProductCheckOrder
		productCheckOrderItemList       model.ProductCheckOrderItemList
		items                           = make(structure.GetProductCheckOrderItemDataList, 0)
		productCheckOrderWeightItemList model.ProductCheckOrderWeightItemList
	)
	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, id)
	if err != nil {
		return
	}

	productCheckOrderItemList, err = mysql.FindProductCheckOrderItemByParentID(r.tx, productCheckOrder.Id)
	if err != nil {
		return
	}

	parentIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_check_order_item_id")
	productCheckOrderWeightItemList, err = mysql.FindProductCheckOrderWeightItemByParentIDs(r.tx, parentIds)
	if err != nil {
		return
	}

	var (
		productSvc          = product2.NewProductClient()
		productColorSvc     = product2.NewProductColorClient()
		bizUnitSvc          = biz_unit.NewClientBizUnitService()
		productLevelSvc     = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		warehouseSvc        = warehouse.NewPhysicalWarehouseClient()
		productColorKindSvc = type_basic_data.NewTypeFinishedProductColorClient()
		productKindNameSvc  = type_basic_data.NewTypeFabricClient()
		userSvc             = user_pb.NewUserClient()
		dicSvc              = dictionary.NewDictionaryClient()
		saleSystemSvc       = sale_system.NewSaleSystemClient()
		departmentSvc       = department.NewDepartmentClient()
		employeeSvc         = employee.NewClientEmployeeService()
		productItem         map[uint64][6]string
		productColorItem    map[uint64][2]string
		bizUnit             map[uint64]string
		productLevel        map[uint64]string
		warehouseName       map[uint64]string
		warehouseBin        map[uint64]string
		productColorKind    map[uint64]string
		productKindName     map[uint64]string
		user                map[uint64]string
		dicNameMap          map[uint64][2]string
		saleSystem          map[uint64]string
		departmentName      map[uint64]string
		employeeName        map[uint64]string
	)

	err = errgroup.Finish(ctx, 0,
		func(ctx context.Context) error {
			productIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_id")
			productItem, _, _ = productSvc.GetProductByIds(ctx, productIds)
			return nil
		},
		func(ctx context.Context) error {
			productColorIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_color_id")
			productColorItem, _ = productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
			return nil
		},
		func(ctx context.Context) error {
			bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productCheckOrderItemList)
			bizUnit, _ = bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
			return nil
		},
		func(ctx context.Context) error {
			productLevelIds := mysql_base.GetUInt64ListV2("product_level_id", productCheckOrderItemList)
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseIds := mysql_base.GetUInt64List(productCheckOrder, "warehouse_id")
			warehouseName, _ = warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseBinIds := mysql_base.GetUInt64ListV2("warehouse_bin_id", productCheckOrderWeightItemList, productCheckOrder)
			warehouseBin, _ = warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
			return nil
		},
		func(ctx context.Context) error {
			productColorKindIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "product_color_kind_id")
			productColorKind, _ = productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
			return nil
		},
		func(ctx context.Context) error {
			productKindNameIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "product_kind_id")
			productKindName, _ = productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds)
			return nil
		},
		func(ctx context.Context) error {
			userIds := mysql_base.GetUInt64ListV2("user_id", productCheckOrderWeightItemList, productCheckOrderItemList, productCheckOrder)
			user, _ = userSvc.GetUserNameByIds(ctx, userIds)
			return nil
		},
		func(ctx context.Context) error {
			dicIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "dictionary_detail_id")
			dicNameMap, _ = dicSvc.GetDictionaryNameByIds(ctx, dicIds)
			return nil
		},
		func(ctx context.Context) error {
			saleSystemIds := mysql_base.GetUInt64List(productCheckOrder, "sale_system_id")
			saleSystem, _ = saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
			return nil
		},
		func(ctx context.Context) error {
			departmentIds := mysql_base.GetUInt64List(productCheckOrder, "department_id")
			departmentName, _ = departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
			return nil
		},
		func(ctx context.Context) error {
			employeeIds := mysql_base.GetUInt64List(productCheckOrder, "employee_id")
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
			return nil
		},
	)

	o := structure.GetProductCheckOrderDetailData{}
	o.Id = productCheckOrder.Id
	o.CreateTime = tools.MyTime(productCheckOrder.CreateTime)
	o.UpdateTime = tools.MyTime(productCheckOrder.UpdateTime)
	o.CreatorId = productCheckOrder.CreatorId
	o.CreatorName = productCheckOrder.CreatorName
	o.UpdaterId = productCheckOrder.UpdaterId
	o.UpdateUserName = productCheckOrder.UpdaterName
	o.SaleSystemId = productCheckOrder.SaleSystemId
	o.SaleSystemName = saleSystem[productCheckOrder.SaleSystemId]
	o.WarehouseId = productCheckOrder.WarehouseId
	o.WarehouseName = warehouseName[productCheckOrder.WarehouseId]
	o.WarehouseBinId = productCheckOrder.WarehouseBinId
	o.WarehouseBinName = warehouseBin[productCheckOrder.WarehouseBinId]
	o.CheckTime = tools.MyTime(productCheckOrder.CheckTime)
	o.WarehouseManagerId = productCheckOrder.WarehouseManagerId
	o.WarehouseManagerName = employeeName[productCheckOrder.WarehouseManagerId]
	o.OrderNo = productCheckOrder.OrderNo
	o.AuditStatus = productCheckOrder.AuditStatus
	o.AuditStatusName = productCheckOrder.AuditStatus.String()
	o.DepartmentId = productCheckOrder.DepartmentId
	o.DepartmentName = departmentName[productCheckOrder.DepartmentId]
	o.CompanyId = productCheckOrder.CompanyId
	o.AuditorId = productCheckOrder.AuditorId
	o.AuditorName = user[productCheckOrder.AuditorId]
	o.AuditDate = tools.MyTime(productCheckOrder.AuditDate)
	o.BusinessClose = productCheckOrder.BusinessClose
	o.BusinessCloseUserId = productCheckOrder.BusinessCloseUserId
	o.BusinessCloseUserName = user[productCheckOrder.BusinessCloseUserId]
	o.BusinessCloseTime = tools.MyTime(productCheckOrder.BusinessCloseTime)
	o.Remark = productCheckOrder.Remark
	for _, item := range productCheckOrderItemList {
		var weightItems = make(structure.GetProductCheckOrderWeightItemDataList, 0)
		productMap, _ := productSvc.GetProductMapByIds(ctx, []uint64{item.ProductId})
		itemProductCheckOrderWeightItems := productCheckOrderWeightItemList.PickByProductCheckOrderItemId(item.Id)
		orderItemData := structure.GetProductCheckOrderItemData{}
		orderItemData.Id = item.Id
		orderItemData.ProductCheckOrderId = item.ProductCheckOrderId
		orderItemData.StockProductId = item.StockProductId
		orderItemData.ProductId = item.ProductId
		if product, ok := productMap[item.ProductId]; ok {
			orderItemData.FinishProductWidthAndWightUnit.FinishProductWidthUnitId = product.FinishProductWidthUnitId
			orderItemData.FinishProductWidthAndWightUnit.FinishProductWidthUnitName = product.FinishProductWidthUnitName
			orderItemData.FinishProductWidthAndWightUnit.FinishProductWidthAndUnitName = product.FinishProductWidthAndUnitName
			orderItemData.FinishProductWidthAndWightUnit.ProductGramWeight = product.ProductGramWeight
			orderItemData.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitId = product.FinishProductGramWeightUnitId
			orderItemData.FinishProductWidthAndWightUnit.FinishProductGramWeightAndUnitName = product.FinishProductGramWeightAndUnitName
			orderItemData.FinishProductWidthAndWightUnit.ProductWidth = product.ProductWidth
			orderItemData.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitName = product.FinishProductGramWeightUnitName
		}
		orderItemData.ProductCode = productItem[item.ProductId][0]
		orderItemData.ProductName = productItem[item.ProductId][1]
		orderItemData.ProductCraft = productItem[item.ProductId][2]
		orderItemData.ProductIngredient = productItem[item.ProductId][3]
		orderItemData.ProductColorId = item.ProductColorId
		orderItemData.ProductColorCode = productColorItem[item.ProductColorId][0]
		orderItemData.ProductColorName = productColorItem[item.ProductColorId][1]
		orderItemData.CustomerId = item.CustomerId
		orderItemData.CustomerName = bizUnit[item.CustomerId]
		orderItemData.ProductLevelId = item.ProductLevelId
		orderItemData.ProductLevelName = productLevel[item.ProductLevelId]
		orderItemData.DyelotNumber = item.DyelotNumber
		orderItemData.ProductRemark = item.ProductRemark
		orderItemData.Remark = item.Remark
		orderItemData.MeasurementUnitId = item.MeasurementUnitId
		orderItemData.MeasurementUnitName = item.MeasurementUnitName
		// 读最近的库存
		// orderItemData.Roll = item.Roll
		// orderItemData.Weight = item.Weight
		// orderItemData.Length = item.Length
		orderItemData.CheckRoll = item.CheckRoll
		orderItemData.CheckWeight = item.CheckWeight
		orderItemData.CheckLength = item.CheckLength
		orderItemData.IsStock = item.IsStock
		for _, orderWeightItem := range itemProductCheckOrderWeightItems {
			w := structure.GetProductCheckOrderWeightItemData{}
			w.Id = orderWeightItem.Id
			w.ProductCheckOrderItemId = orderWeightItem.ProductCheckOrderItemId
			w.ProductCheckOrderId = orderWeightItem.ProductCheckOrderId
			w.StockProductId = orderWeightItem.StockProductId
			w.StockProductDetailId = orderWeightItem.StockProductDetailId
			w.ProductId = item.ProductId
			w.ProductCode = productItem[item.ProductId][0]
			w.ProductName = productItem[item.ProductId][1]
			w.ProductColorId = item.ProductColorId
			w.ProductColorCode = productColorItem[item.ProductColorId][0]
			w.ProductColorName = productColorItem[item.ProductColorId][1]
			w.WarehouseInType = orderWeightItem.WarehouseInType
			w.WarehouseInTypeName = orderWeightItem.WarehouseInType.String()
			w.WarehouseInOrderId = orderWeightItem.WarehouseInOrderId
			w.WarehouseInOrderNo = orderWeightItem.WarehouseInOrderNo
			w.WeightError = orderWeightItem.WeightError
			w.PaperTubeWeight = orderWeightItem.PaperTubeWeight
			w.DyeFactoryColorCode = orderWeightItem.DyeFactoryColorCode
			w.ProductColorKindId = orderWeightItem.ProductColorKindId
			w.ProductColorKindName = productColorKind[orderWeightItem.ProductColorKindId]
			w.WarehouseId = orderWeightItem.WarehouseId
			w.WarehouseName = warehouseName[orderWeightItem.WarehouseId]
			w.CustomerId = orderWeightItem.CustomerId
			w.CustomerName = bizUnit[orderWeightItem.CustomerId]
			w.DigitalCode = orderWeightItem.DigitalCode
			w.ProductLevelId = orderWeightItem.ProductLevelId
			w.ProductLevelName = productLevel[orderWeightItem.ProductLevelId]
			w.ProductKindId = orderWeightItem.ProductKindId
			w.ProductKindName = productKindName[orderWeightItem.ProductKindId]
			w.ContractNumber = orderWeightItem.ContractNumber
			w.CustomerPoNum = orderWeightItem.CustomerPoNum
			w.CustomerAccountNum = orderWeightItem.CustomerAccountNum
			w.ShelfNo = orderWeightItem.ShelfNo
			w.StockRemark = orderWeightItem.StockRemark
			w.InternalRemark = orderWeightItem.InternalRemark
			w.Remark = orderWeightItem.Remark
			w.MeasurementUnitId = orderWeightItem.MeasurementUnitId
			w.MeasurementUnitName = orderWeightItem.MeasurementUnitName
			w.DyelotNumber = orderWeightItem.DyelotNumber
			w.VolumeNumber = orderWeightItem.VolumeNumber
			w.WarehouseBinId = orderWeightItem.WarehouseBinId
			w.WarehouseBinName = warehouseBin[orderWeightItem.WarehouseBinId]
			w.Roll = orderWeightItem.Roll
			w.Weight = orderWeightItem.Weight
			w.Length = orderWeightItem.Length
			w.CheckRoll = orderWeightItem.CheckRoll
			w.CheckWeight = orderWeightItem.CheckWeight
			w.CheckLength = orderWeightItem.CheckLength
			w.DifferenceRoll = orderWeightItem.DifferenceRoll
			w.DifferenceWeight = orderWeightItem.DifferenceWeight
			w.DifferenceLength = orderWeightItem.DifferenceLength
			w.FinishedCheck = orderWeightItem.FinishedCheck
			w.CheckUserId = orderWeightItem.CheckUserId
			w.CheckUserName = user[orderWeightItem.CheckUserId]
			w.CheckTime = tools.MyTime(orderWeightItem.CheckTime)
			w.IsStock = orderWeightItem.IsStock

			w.BuildFPResp(orderWeightItem.FinishProductWidth, orderWeightItem.FinishProductGramWeight, dicNameMap[orderWeightItem.FinishProductWidthUnitId][1],
				dicNameMap[orderWeightItem.FinishProductGramWeightUnitId][1], orderWeightItem.FinishProductWidthUnitId, orderWeightItem.FinishProductGramWeightUnitId)

			orderItemData.Roll += orderWeightItem.Roll
			orderItemData.Weight += orderWeightItem.Weight
			orderItemData.Length += orderWeightItem.Length
			weightItems = append(weightItems, w)
		}
		orderItemData.ItemData = weightItems
		items = append(items, orderItemData)
	}
	o.ItemData = items
	data = o
	return
}

func (r *ProductCheckOrderRepo) GetWithoutItems(ctx context.Context, id uint64) (data structure.PDAGetProductCheckOrderDetailData, err error) {
	var (
		productCheckOrder               model.ProductCheckOrder
		productCheckOrderItemList       model.ProductCheckOrderItemList
		items                           = make(structure.PDAGetProductCheckOrderItemDataList, 0)
		productCheckOrderWeightItemList model.ProductCheckOrderWeightItemList
	)
	productCheckOrder, err = mysql.MustFirstProductCheckOrderByID(r.tx, id)
	if err != nil {
		return
	}

	productCheckOrderItemList, err = mysql.FindProductCheckOrderItemByParentID(r.tx, productCheckOrder.Id)
	if err != nil {
		return
	}

	parentIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_check_order_item_id")
	productCheckOrderWeightItemList, err = mysql.FindProductCheckOrderWeightItemByParentIDs(r.tx, parentIds)
	if err != nil {
		return
	}

	var (
		productSvc       = product2.NewProductClient()
		productColorSvc  = product2.NewProductColorClient()
		bizUnitSvc       = biz_unit.NewClientBizUnitService()
		productLevelSvc  = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		warehouseSvc     = warehouse.NewPhysicalWarehouseClient()
		userSvc          = user_pb.NewUserClient()
		saleSystemSvc    = sale_system.NewSaleSystemClient()
		departmentSvc    = department.NewDepartmentClient()
		employeeSvc      = employee.NewClientEmployeeService()
		productItem      map[uint64][6]string
		productColorItem map[uint64][2]string
		bizUnit          map[uint64]string
		productLevel     map[uint64]string
		warehouseName    map[uint64]string
		warehouseBin     map[uint64]string
		user             map[uint64]string
		saleSystem       map[uint64]string
		departmentName   map[uint64]string
		employeeName     map[uint64]string
	)

	err = errgroup.Finish(ctx, 0,
		func(ctx context.Context) error {
			productIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_id")
			productItem, _, _ = productSvc.GetProductByIds(ctx, productIds)
			return nil
		},
		func(ctx context.Context) error {
			productColorIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_color_id")
			productColorItem, _ = productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
			return nil
		},
		func(ctx context.Context) error {
			bizUnitIds := mysql_base.GetUInt64List(productCheckOrderItemList, "biz_unit_id")
			bizUnit, _ = bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
			return nil
		},
		func(ctx context.Context) error {
			productLevelIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_level_id")
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "warehouse_id")
			warehouseName, _ = warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseBinIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "warehouse_bin_id")
			warehouseBin, _ = warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
			return nil
		},
		func(ctx context.Context) error {
			userIds := mysql_base.GetUInt64ListV2("user_id", productCheckOrderWeightItemList)
			user, _ = userSvc.GetUserNameByIds(ctx, userIds)
			return nil
		},
		func(ctx context.Context) error {
			saleSystemIds := mysql_base.GetUInt64List(productCheckOrder, "sale_system_id")
			saleSystem, _ = saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
			return nil
		},
		func(ctx context.Context) error {
			departmentIds := mysql_base.GetUInt64List(productCheckOrder, "department_id")
			departmentName, _ = departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
			return nil
		},
		func(ctx context.Context) error {
			employeeIds := mysql_base.GetUInt64List(productCheckOrder, "employee_id")
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
			return nil
		},
	)

	o := structure.PDAGetProductCheckOrderDetailData{}
	o.Id = productCheckOrder.Id
	o.CreateTime = tools.MyTime(productCheckOrder.CreateTime)
	o.UpdateTime = tools.MyTime(productCheckOrder.UpdateTime)
	o.CreatorId = productCheckOrder.CreatorId
	o.CreatorName = productCheckOrder.CreatorName
	o.UpdaterId = productCheckOrder.UpdaterId
	o.UpdateUserName = productCheckOrder.UpdaterName
	o.SaleSystemId = productCheckOrder.SaleSystemId
	o.SaleSystemName = saleSystem[productCheckOrder.SaleSystemId]
	o.WarehouseId = productCheckOrder.WarehouseId
	o.WarehouseName = warehouseName[productCheckOrder.WarehouseId]
	o.WarehouseBinId = productCheckOrder.WarehouseBinId
	o.WarehouseBinName = warehouseBin[productCheckOrder.WarehouseBinId]
	o.CheckTime = tools.MyTime(productCheckOrder.CheckTime)
	o.WarehouseManagerId = productCheckOrder.WarehouseManagerId
	o.WarehouseManagerName = employeeName[productCheckOrder.WarehouseManagerId]
	o.OrderNo = productCheckOrder.OrderNo
	o.AuditStatus = productCheckOrder.AuditStatus
	o.AuditStatusName = productCheckOrder.AuditStatus.String()
	o.DepartmentId = productCheckOrder.DepartmentId
	o.DepartmentName = departmentName[productCheckOrder.DepartmentId]
	o.AuditorId = productCheckOrder.AuditorId
	o.AuditorName = user[productCheckOrder.AuditorId]
	o.AuditDate = tools.MyTime(productCheckOrder.AuditDate)
	o.Remark = productCheckOrder.Remark
	for _, item := range productCheckOrderItemList {
		orderItemData := structure.PDAGetProductCheckOrderItemData{}
		orderItemData.Id = item.Id
		orderItemData.ProductCheckOrderId = item.ProductCheckOrderId
		orderItemData.StockProductId = item.StockProductId
		orderItemData.ProductId = item.ProductId
		orderItemData.ProductCode = productItem[item.ProductId][0]
		orderItemData.ProductName = productItem[item.ProductId][1]
		orderItemData.ProductCraft = productItem[item.ProductId][2]
		orderItemData.ProductIngredient = productItem[item.ProductId][3]
		orderItemData.ProductColorId = item.ProductColorId
		orderItemData.ProductColorCode = productColorItem[item.ProductColorId][0]
		orderItemData.ProductColorName = productColorItem[item.ProductColorId][1]
		orderItemData.CustomerId = item.CustomerId
		orderItemData.CustomerName = bizUnit[item.CustomerId]
		orderItemData.ProductLevelId = item.ProductLevelId
		orderItemData.ProductLevelName = productLevel[item.ProductLevelId]
		orderItemData.DyelotNumber = item.DyelotNumber
		orderItemData.ProductRemark = item.ProductRemark
		orderItemData.Remark = item.Remark
		orderItemData.MeasurementUnitId = item.MeasurementUnitId
		orderItemData.MeasurementUnitName = item.MeasurementUnitName
		orderItemData.IsStock = item.IsStock
		orderItemData.Roll = item.Roll
		orderItemData.Weight = item.Weight
		orderItemData.Length = item.Length
		orderItemData.CheckRoll = item.CheckRoll
		orderItemData.CheckWeight = item.CheckWeight
		orderItemData.CheckLength = item.CheckLength
		orderItemData.DifferentRoll = item.CheckRoll - item.Roll
		orderItemData.DifferentWeight = item.CheckWeight - item.Weight
		orderItemData.DifferentLength = item.CheckLength - item.Length
		items = append(items, orderItemData)
		_productCheckOrderWeightItemList := productCheckOrderWeightItemList.PickByProductCheckOrderItemId(item.Id)
		for _, weightItem := range _productCheckOrderWeightItemList {
			o.WarehouseBinCheckBeforeRoll += weightItem.Roll
			o.WarehouseBinCheckRoll += weightItem.CheckRoll
		}
	}
	o.ItemData = items
	data = o
	return
}

func (r *ProductCheckOrderRepo) GetList(ctx context.Context, req *structure.GetProductCheckOrderListQuery) (list structure.GetProductCheckOrderDataList, total int, err error) {
	var (
		productCheckOrders        model.ProductCheckOrderList
		productCheckOrderItemList model.ProductCheckOrderItemList
	)
	productCheckOrders, total, err = mysql.SearchProductCheckOrder(r.tx, req)
	if err != nil {
		return
	}

	productCheckOrderItemList, err = mysql.FindProductCheckOrderItemByParentIDs(r.tx, productCheckOrders.GetIds())
	if err != nil {
		return
	}

	var (
		warehouseSvc   = warehouse.NewPhysicalWarehouseClient()
		userSvc        = user_pb.NewUserClient()
		saleSystemSvc  = sale_system.NewSaleSystemClient()
		departmentSvc  = department.NewDepartmentClient()
		employeeSvc    = employee.NewClientEmployeeService()
		warehouseName  map[uint64]string
		warehouseBin   map[uint64]string
		user           map[uint64]string
		saleSystem     map[uint64]string
		departmentName map[uint64]string
		employeeName   map[uint64]string
	)

	err = errgroup.Finish(ctx, 0,
		func(ctx context.Context) error {
			warehouseIds := mysql_base.GetUInt64List(productCheckOrders, "warehouse_id")
			warehouseName, _ = warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseBinIds := mysql_base.GetUInt64List(productCheckOrders, "warehouse_bin_id")
			warehouseBin, _ = warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
			return nil
		},
		func(ctx context.Context) error {
			userIds := mysql_base.GetUInt64ListV2("user_id", productCheckOrders)
			user, _ = userSvc.GetUserNameByIds(ctx, userIds)
			return nil
		},
		func(ctx context.Context) error {
			saleSystemIds := mysql_base.GetUInt64List(productCheckOrders, "sale_system_id")
			saleSystem, _ = saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
			return nil
		},
		func(ctx context.Context) error {
			departmentIds := mysql_base.GetUInt64List(productCheckOrders, "department_id")
			departmentName, _ = departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
			return nil
		},
		func(ctx context.Context) error {
			employeeIds := mysql_base.GetUInt64List(productCheckOrders, "employee_id")
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
			return nil
		},
	)

	for _, productCheckOrder := range productCheckOrders.List() {
		o := structure.GetProductCheckOrderData{}
		o.Id = productCheckOrder.Id
		o.CreateTime = tools.MyTime(productCheckOrder.CreateTime)
		o.UpdateTime = tools.MyTime(productCheckOrder.UpdateTime)
		o.CreatorId = productCheckOrder.CreatorId
		o.CreatorName = productCheckOrder.CreatorName
		o.UpdaterId = productCheckOrder.UpdaterId
		o.UpdateUserName = productCheckOrder.UpdaterName
		o.SaleSystemId = productCheckOrder.SaleSystemId
		o.SaleSystemName = saleSystem[productCheckOrder.SaleSystemId]
		o.WarehouseId = productCheckOrder.WarehouseId
		o.WarehouseName = warehouseName[productCheckOrder.WarehouseId]
		o.WarehouseBinId = productCheckOrder.WarehouseBinId
		o.WarehouseBinName = warehouseBin[productCheckOrder.WarehouseBinId]
		o.CheckTime = tools.MyTime(productCheckOrder.CheckTime)
		o.WarehouseManagerId = productCheckOrder.WarehouseManagerId
		o.WarehouseManagerName = employeeName[productCheckOrder.WarehouseManagerId]
		o.OrderNo = productCheckOrder.OrderNo
		o.AuditStatus = productCheckOrder.AuditStatus
		o.AuditStatusName = productCheckOrder.AuditStatus.String()
		o.DepartmentId = productCheckOrder.DepartmentId
		o.DepartmentName = departmentName[productCheckOrder.DepartmentId]
		o.CompanyId = productCheckOrder.CompanyId
		o.AuditorId = productCheckOrder.AuditorId
		o.AuditorName = user[productCheckOrder.AuditorId]
		o.AuditDate = tools.MyTime(productCheckOrder.AuditDate)
		o.BusinessClose = productCheckOrder.BusinessClose
		o.BusinessCloseUserId = productCheckOrder.BusinessCloseUserId
		o.BusinessCloseUserName = user[productCheckOrder.BusinessCloseUserId]
		o.BusinessCloseTime = tools.MyTime(productCheckOrder.BusinessCloseTime)
		o.Remark = productCheckOrder.Remark
		_productCheckOrderItemList := productCheckOrderItemList.PickByParentId(productCheckOrder.Id)
		for _, item := range _productCheckOrderItemList {
			o.WarehouseBinCheckBeforeRoll += item.Roll
			o.WarehouseBinCheckRoll += item.CheckRoll
		}
		list = append(list, o)
	}
	return
}

// 根据盘点单id获取成品信息列表(带细码)
func (r *ProductCheckOrderRepo) GetDetailList(ctx context.Context, req *structure.GetProductCheckOrderListQuery) (list structure.GetProductCheckOrderItemDataList, total int, err error) {
	var (
		productCheckOrderItemList       model.ProductCheckOrderItemList
		items                           = make(structure.GetProductCheckOrderItemDataList, 0)
		productCheckOrderWeightItemList model.ProductCheckOrderWeightItemList
	)
	productCheckOrderItemList, total, err = mysql.SearchProductCheckOrderItem(r.tx, &structure.GetProductCheckOrderItemQuery{ProductCheckOrderId: req.ProductCheckOrderId})
	if err != nil {
		return
	}

	parentIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_check_order_item_id")
	productCheckOrderWeightItemList, err = mysql.FindProductCheckOrderWeightItemByParentIDs(r.tx, parentIds)
	if err != nil {
		return
	}

	var (
		productSvc          = product2.NewProductClient()
		productColorSvc     = product2.NewProductColorClient()
		bizUnitSvc          = biz_unit.NewClientBizUnitService()
		productLevelSvc     = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		warehouseSvc        = warehouse.NewPhysicalWarehouseClient()
		productColorKindSvc = type_basic_data.NewTypeFinishedProductColorClient()
		productKindNameSvc  = type_basic_data.NewTypeFabricClient()
		userSvc             = user_pb.NewUserClient()
		dicSvc              = dictionary.NewDictionaryClient()
		productItem         map[uint64][6]string
		productColorItem    map[uint64][2]string
		bizUnit             map[uint64]string
		productLevel        map[uint64]string
		warehouseName       map[uint64]string
		warehouseBin        map[uint64]string
		productColorKind    map[uint64]string
		productKindName     map[uint64]string
		user                map[uint64]string
		dicNameMap          map[uint64][2]string
	)

	err = errgroup.Finish(ctx, 0,
		func(ctx context.Context) error {
			productIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_id")
			productItem, _, _ = productSvc.GetProductByIds(ctx, productIds)
			return nil
		},
		func(ctx context.Context) error {
			productColorIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_color_id")
			productColorItem, _ = productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
			return nil
		},
		func(ctx context.Context) error {
			bizUnitIds := mysql_base.GetUInt64List(productCheckOrderItemList, "biz_unit_id")
			bizUnit, _ = bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
			return nil
		},
		func(ctx context.Context) error {
			productLevelIds := mysql_base.GetUInt64List(productCheckOrderItemList, "product_level_id")
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "warehouse_id")
			warehouseName, _ = warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
			return nil
		},
		func(ctx context.Context) error {
			warehouseBinIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "warehouse_bin_id")
			warehouseBin, _ = warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
			return nil
		},
		func(ctx context.Context) error {
			productColorKindIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "product_color_kind_id")
			productColorKind, _ = productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
			return nil
		},
		func(ctx context.Context) error {
			productKindNameIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "product_kind_id")
			productKindName, _ = productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds)
			return nil
		},
		func(ctx context.Context) error {
			userIds := mysql_base.GetUInt64ListV2("user_id", productCheckOrderWeightItemList)
			user, _ = userSvc.GetUserNameByIds(ctx, userIds)
			return nil
		},
		func(ctx context.Context) error {
			dicIds := mysql_base.GetUInt64List(productCheckOrderWeightItemList, "dictionary_detail_id")
			dicNameMap, _ = dicSvc.GetDictionaryNameByIds(ctx, dicIds)
			return nil
		},
	)

	for _, item := range productCheckOrderItemList {
		var weightItems = make(structure.GetProductCheckOrderWeightItemDataList, 0)
		itemProductCheckOrderWeightItems := productCheckOrderWeightItemList.PickByProductCheckOrderItemId(item.Id)
		var o structure.GetProductCheckOrderItemData
		o = buildItemResp(item, productItem, productColorItem, bizUnit, productLevel)
		for _, orderWeightItem := range itemProductCheckOrderWeightItems {
			var w structure.GetProductCheckOrderWeightItemData
			w = buildWeightItemResp(
				orderWeightItem, productItem, productColorItem, productColorKind, warehouseName,
				bizUnit, productLevel, productKindName, warehouseBin, user, dicNameMap)
			weightItems = append(weightItems, w)
		}
		o.ItemData = weightItems
		items = append(items, o)
	}
	list = items
	return
}

func buildItemResp(
	orderItem model.ProductCheckOrderItem,
	productItem map[uint64][6]string,
	productColorItem map[uint64][2]string,
	bizUnit map[uint64]string,
	productLevel map[uint64]string,
) (o structure.GetProductCheckOrderItemData) {
	o.Id = orderItem.Id
	o.ProductCheckOrderId = orderItem.ProductCheckOrderId
	o.StockProductId = orderItem.StockProductId
	o.ProductId = orderItem.ProductId
	o.ProductCode = productItem[orderItem.ProductId][0]
	o.ProductName = productItem[orderItem.ProductId][1]
	o.ProductCraft = productItem[orderItem.ProductId][2]
	o.ProductIngredient = productItem[orderItem.ProductId][3]
	o.ProductColorId = orderItem.ProductColorId
	o.ProductColorCode = productColorItem[orderItem.ProductColorId][0]
	o.ProductColorName = productColorItem[orderItem.ProductColorId][1]
	o.CustomerId = orderItem.CustomerId
	o.CustomerName = bizUnit[orderItem.CustomerId]
	o.ProductLevelId = orderItem.ProductLevelId
	o.ProductLevelName = productLevel[orderItem.ProductLevelId]
	o.DyelotNumber = orderItem.DyelotNumber
	o.ProductRemark = orderItem.ProductRemark
	o.Remark = orderItem.Remark
	o.MeasurementUnitId = orderItem.MeasurementUnitId
	o.MeasurementUnitName = orderItem.MeasurementUnitName
	o.Roll = orderItem.Roll
	o.Weight = orderItem.Weight
	o.Length = orderItem.Length
	o.CheckRoll = orderItem.CheckRoll
	o.CheckWeight = orderItem.CheckWeight
	o.CheckLength = orderItem.CheckLength
	o.IsStock = orderItem.IsStock
	return
}

func buildWeightItemResp(
	orderWeightItem model.ProductCheckOrderWeightItem,
	productItem map[uint64][6]string,
	productColorItem map[uint64][2]string,
	productColorKind map[uint64]string,
	warehouseName map[uint64]string,
	bizUnit map[uint64]string,
	productLevel map[uint64]string,
	productKindName map[uint64]string,
	warehouseBin map[uint64]string,
	user map[uint64]string,
	dicNameMap map[uint64][2]string,
) (itemData structure.GetProductCheckOrderWeightItemData) {
	itemData.Id = orderWeightItem.Id
	itemData.ProductCheckOrderItemId = orderWeightItem.ProductCheckOrderItemId
	itemData.ProductCheckOrderId = orderWeightItem.ProductCheckOrderId
	itemData.StockProductId = orderWeightItem.StockProductId
	itemData.StockProductDetailId = orderWeightItem.StockProductDetailId
	itemData.ProductId = orderWeightItem.ProductId
	itemData.ProductCode = productItem[orderWeightItem.ProductId][0]
	itemData.ProductName = productItem[orderWeightItem.ProductId][1]
	itemData.ProductColorId = orderWeightItem.ProductColorId
	itemData.ProductColorCode = productColorItem[orderWeightItem.ProductColorId][0]
	itemData.ProductColorName = productColorItem[orderWeightItem.ProductColorId][1]
	itemData.WarehouseInType = orderWeightItem.WarehouseInType
	itemData.WarehouseInTypeName = orderWeightItem.WarehouseInType.String()
	itemData.WarehouseInOrderId = orderWeightItem.WarehouseInOrderId
	itemData.WarehouseInOrderNo = orderWeightItem.WarehouseInOrderNo
	itemData.BuildFPResp(orderWeightItem.FinishProductWidth, orderWeightItem.FinishProductGramWeight, dicNameMap[orderWeightItem.FinishProductWidthUnitId][1],
		dicNameMap[orderWeightItem.FinishProductGramWeightUnitId][1], orderWeightItem.FinishProductWidthUnitId, orderWeightItem.FinishProductGramWeightUnitId)

	itemData.WeightError = orderWeightItem.WeightError
	itemData.PaperTubeWeight = orderWeightItem.PaperTubeWeight
	itemData.DyeFactoryColorCode = orderWeightItem.DyeFactoryColorCode
	itemData.ProductColorKindId = orderWeightItem.ProductColorKindId
	itemData.ProductColorKindName = productColorKind[orderWeightItem.ProductColorKindId]
	itemData.WarehouseId = orderWeightItem.WarehouseId
	itemData.WarehouseName = warehouseName[orderWeightItem.WarehouseId]
	itemData.CustomerId = orderWeightItem.CustomerId
	itemData.CustomerName = bizUnit[orderWeightItem.CustomerId]
	itemData.DigitalCode = orderWeightItem.DigitalCode
	itemData.ProductLevelId = orderWeightItem.ProductLevelId
	itemData.ProductLevelName = productLevel[orderWeightItem.ProductLevelId]
	itemData.ProductKindId = orderWeightItem.ProductKindId
	itemData.ProductKindName = productKindName[orderWeightItem.ProductKindId]
	itemData.ContractNumber = orderWeightItem.ContractNumber
	itemData.CustomerPoNum = orderWeightItem.CustomerPoNum
	itemData.CustomerAccountNum = orderWeightItem.CustomerAccountNum
	itemData.ShelfNo = orderWeightItem.ShelfNo
	itemData.StockRemark = orderWeightItem.StockRemark
	itemData.InternalRemark = orderWeightItem.InternalRemark
	itemData.Remark = orderWeightItem.Remark
	itemData.MeasurementUnitId = orderWeightItem.MeasurementUnitId
	itemData.MeasurementUnitName = orderWeightItem.MeasurementUnitName
	itemData.DyelotNumber = orderWeightItem.DyelotNumber
	itemData.VolumeNumber = orderWeightItem.VolumeNumber
	itemData.WarehouseBinId = orderWeightItem.WarehouseBinId
	itemData.WarehouseBinName = warehouseBin[orderWeightItem.WarehouseBinId]
	itemData.Roll = orderWeightItem.Roll
	itemData.Weight = orderWeightItem.Weight
	itemData.Length = orderWeightItem.Length
	itemData.CheckRoll = orderWeightItem.CheckRoll
	itemData.CheckWeight = orderWeightItem.CheckWeight
	itemData.CheckLength = orderWeightItem.CheckLength
	itemData.DifferenceRoll = orderWeightItem.DifferenceRoll
	itemData.DifferenceWeight = orderWeightItem.DifferenceWeight
	itemData.DifferenceLength = orderWeightItem.DifferenceLength
	itemData.FinishedCheck = orderWeightItem.FinishedCheck
	itemData.CheckUserId = orderWeightItem.CheckUserId
	itemData.CheckUserName = user[orderWeightItem.CheckUserId]
	itemData.CheckTime = tools.MyTime(orderWeightItem.CheckTime)
	itemData.IsStock = orderWeightItem.IsStock
	return
}

// 仓位不一致时处理盘点单详情及细码
func (r ProductCheckOrderRepo) ChangeWarehouseBin(ctx context.Context, stockDetailId, warehouseBinId uint64) (err error) {
	var (
		productCheckOrderItem       model.ProductCheckOrderItem
		productCheckOrderWeightItem model.ProductCheckOrderWeightItem
		exist                       bool
		stockDetail                 model.StockProductDetail
	)
	// 找出对应的库存,实时修改仓位
	stockDetail, err = mysql.MustFirstStockProductDetailByID(r.tx, stockDetailId)
	if err != nil {
		return
	}
	stockDetail.WarehouseBinId = warehouseBinId

	stockDetail, err = mysql.MustUpdateStockProductDetail(r.tx, stockDetail)
	if err != nil {
		return
	}

	productCheckOrderWeightItem, exist, err = mysql.FirstProductCheckOrderWeightItemByStockDetailID(r.tx, stockDetailId)
	if err != nil {
		return
	}
	if !exist {
		return
	}
	productCheckOrderItem, err = mysql.MustFirstProductCheckOrderItemByID(r.tx, productCheckOrderWeightItem.ProductCheckOrderItemId)
	if err != nil {
		return
	}

	// 删除并添加删除记录
	err = mysql.MustDeleteProductCheckOrderWeightItem(r.tx, productCheckOrderWeightItem)
	if err != nil {
		return
	}

	// 减少详情盘点数
	productCheckOrderItem.Roll -= productCheckOrderWeightItem.Roll
	productCheckOrderItem.Weight -= productCheckOrderWeightItem.Weight
	productCheckOrderItem.Length -= productCheckOrderWeightItem.Length
	productCheckOrderItem.CheckRoll -= productCheckOrderWeightItem.CheckRoll
	productCheckOrderItem.CheckWeight -= productCheckOrderWeightItem.CheckWeight
	productCheckOrderItem.CheckLength -= productCheckOrderWeightItem.CheckLength
	productCheckOrderItem, err = mysql.MustUpdateProductCheckOrderItem(r.tx, productCheckOrderItem)
	if err != nil {
		return
	}

	_, exist, err = mysql.FirstProductCheckOrderWeightItemByParentID(r.tx, productCheckOrderItem.Id)
	if err != nil {
		return
	}
	if !exist {
		err = mysql.MustDeleteProductCheckOrderItem(r.tx, productCheckOrderItem)
		if err != nil {
			return
		}
	}
	return
}

// 新增成品盘点单成品细码信息(pda扫码插入细码)
func (r *ProductCheckOrderRepo) AddProductCheckOrderWeightItemDetail(
	ctx context.Context,
	item structure.AddProductCheckOrderWeightItemParam,
	productCheckOrderId, productCheckOrderItemId, warehouseId uint64,
) (err error) {
	var (
		productCheckOrderItem model.ProductCheckOrderItem
	)
	productIds := mysql_base.GetUInt64List(item, "product_id")
	productSvc := product2.NewProductClient()
	productItems, err := productSvc.GetProductList(ctx, product2.ProductReq{Ids: productIds})
	if err != nil {
		return
	}
	productColorIds := mysql_base.GetUInt64List(item, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItems, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	// 盘盈新增
	if productCheckOrderItemId == 0 {
		req := &structure.AddProductCheckOrderItemParam{}
		req.StockProductId = item.StockProductId
		req.ProductId = item.ProductId
		req.ProductColorId = item.ProductColorId
		req.CustomerId = item.CustomerId
		req.ProductLevelId = item.ProductLevelId
		req.DyelotNumber = item.DyelotNumber
		req.ProductRemark = item.Remark
		req.MeasurementUnitId = item.MeasurementUnitId
		req.MeasurementUnitName = item.MeasurementUnitName
		req.Roll = item.Roll
		req.Weight = item.Weight
		req.Length = item.Length

		if item.CheckRoll > 0 {
			req.CheckRoll += item.CheckRoll
		} else {
			req.CheckRoll += item.Roll
		}
		if item.CheckWeight > 0 {
			req.CheckWeight += item.CheckWeight
		} else {
			req.CheckWeight += item.Weight
		}
		if item.CheckLength > 0 {
			req.CheckLength += item.CheckLength
		} else {
			req.CheckLength += item.Length
		}
		// 扫码参数默认为库存盘点
		req.IsStock = true
		productCheckOrderItem = model.NewProductCheckOrderItem(ctx, req, productCheckOrderId)

		productCheckOrderItem, err = mysql.MustCreateProductCheckOrderItem(r.tx, productCheckOrderItem)
		if err != nil {
			return
		}

		productCheckOrderItemId = productCheckOrderItem.Id
	} else {
		productCheckOrderItem, err = mysql.MustFirstProductCheckOrderItemByID(r.tx, productCheckOrderItemId)
		if err != nil {
			return
		}

		productCheckOrderItem.Roll += item.Roll
		productCheckOrderItem.Weight += item.Weight
		productCheckOrderItem.Length += item.Length
		productCheckOrderItem.CheckRoll += item.CheckRoll
		productCheckOrderItem.CheckWeight += item.CheckWeight
		productCheckOrderItem.CheckLength += item.CheckLength

		productCheckOrderItem, err = mysql.MustUpdateProductCheckOrderItem(r.tx, productCheckOrderItem)
		if err != nil {
			return
		}
	}

	productItem := productItems.PickByProductId(item.ProductId)
	productColorItem := productColorItems.PickByProductColorId(item.ProductColorId)
	item.WarehouseId = warehouseId
	item.ProductColorKindId = productColorItem.TypeFinishedProductKindId
	item.ProductKindId = productItem.TypeGreyFabricId
	// 扫码参数默认为库存盘点
	item.IsStock = true
	productCheckOrderWeightItemItem := model.NewProductCheckOrderWeightItem(ctx, &item, productCheckOrderId, productCheckOrderItemId)

	productCheckOrderWeightItemItem, err = mysql.MustCreateProductCheckOrderWeightItem(r.tx, productCheckOrderWeightItemItem)
	if err != nil {
		return
	}
	return
}

// 更新成品盘点单成品细码信息
func (r *ProductCheckOrderRepo) UpdateProductCheckOrderWeightItemDetail(ctx context.Context, req structure.AddProductCheckOrderWeightItemParam, productCheckOrderItemId, productCheckOrderItemWeightId uint64) (err error) {
	var (
		productCheckOrderItem model.ProductCheckOrderItem
		weightItem            model.ProductCheckOrderWeightItem
	)

	productCheckOrderItem, err = mysql.MustFirstProductCheckOrderItemByID(r.tx, productCheckOrderItemId)
	if err != nil {
		return
	}

	productCheckOrderItem.CheckRoll += req.CheckRoll
	productCheckOrderItem.CheckWeight += req.CheckWeight
	productCheckOrderItem.CheckLength += req.CheckLength

	productCheckOrderItem, err = mysql.MustUpdateProductCheckOrderItem(r.tx, productCheckOrderItem)
	if err != nil {
		return
	}

	weightItem, err = mysql.MustFirstProductCheckOrderWeightItemByID(r.tx, productCheckOrderItemWeightId)
	if err != nil {
		return
	}

	weightItem.CheckRoll = req.CheckRoll
	weightItem.CheckWeight = req.CheckWeight
	weightItem.CheckLength = req.CheckLength
	weightItem.DifferenceRoll = weightItem.CheckRoll - weightItem.Roll
	weightItem.DifferenceWeight = weightItem.CheckWeight - weightItem.Weight
	weightItem.DifferenceLength = weightItem.CheckLength - weightItem.Length
	weightItem.FinishedCheck = req.FinishedCheck
	if weightItem.FinishedCheck {
		weightItem.CheckTime = time.Now()
		weightItem.CheckUserId = metadata.GetUserId(ctx)
	}

	weightItem, err = mysql.MustUpdateProductCheckOrderWeightItem(r.tx, weightItem)
	if err != nil {
		return
	}
	return
}

// 删除成品盘点单成品细码信息
func (r *ProductCheckOrderRepo) DeleteProductCheckOrderWeightItemDetail(ctx context.Context, req structure.AddProductCheckOrderWeightItemParam, productCheckOrderItemId, productCheckOrderItemWeightId uint64) (err error) {
	var (
		productCheckOrderItem model.ProductCheckOrderItem
		weightItem            model.ProductCheckOrderWeightItem
		weightItems           model.ProductCheckOrderWeightItemList
	)

	weightItem, err = mysql.MustFirstProductCheckOrderWeightItemByID(r.tx, productCheckOrderItemWeightId)
	if err != nil {
		return
	}

	weightItems, err = mysql.FindProductCheckOrderWeightItemByParentID(r.tx, productCheckOrderItemId)
	if err != nil {
		return
	}

	err = mysql.MustDeleteProductCheckOrderWeightItem(r.tx, weightItem)
	if err != nil {
		return
	}

	productCheckOrderItem, err = mysql.MustFirstProductCheckOrderItemByID(r.tx, productCheckOrderItemId)
	if err != nil {
		return
	}

	productCheckOrderItem.Roll -= weightItem.Roll
	productCheckOrderItem.Weight -= weightItem.Weight
	productCheckOrderItem.Length -= weightItem.Length
	productCheckOrderItem.CheckRoll -= weightItem.CheckRoll
	productCheckOrderItem.CheckWeight -= weightItem.CheckWeight
	productCheckOrderItem.CheckLength -= weightItem.CheckLength

	if len(weightItems) == 1 {
		err = mysql.MustDeleteProductCheckOrderItem(r.tx, productCheckOrderItem)
		if err != nil {
			return
		}
	} else {
		productCheckOrderItem, err = mysql.MustUpdateProductCheckOrderItem(r.tx, productCheckOrderItem)
		if err != nil {
			return
		}
	}
	return
}

func (r *ProductCheckOrderRepo) GetProductCheckOrderWeightItemDetail(ctx context.Context, req *structure.GetProductCheckOrderWeightItemQuery) (data structure.GetProductCheckOrderWeightItemData, exist bool, err error) {
	var (
		weightItem model.ProductCheckOrderWeightItem
	)
	weightItem, exist, err = mysql.FirstProductCheckOrderWeightItemByQrCode(r.tx, req.QrCode)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(weightItem, "product_id")
	productSvc := product2.NewProductClient()
	productItems, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(weightItem, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	o := structure.GetProductCheckOrderWeightItemData{}
	o.Id = weightItem.Id
	o.ProductCheckOrderItemId = weightItem.ProductCheckOrderItemId
	o.ProductCheckOrderId = weightItem.ProductCheckOrderId
	o.StockProductId = weightItem.StockProductId
	o.StockProductDetailId = weightItem.StockProductDetailId
	o.ProductId = weightItem.ProductId
	if productItem, ok := productItems[weightItem.ProductId]; ok {
		o.ProductCode = productItem.FinishProductCode
		o.ProductName = productItem.FinishProductName
	}
	o.ProductColorId = weightItem.ProductColorId
	o.ProductColorCode = productColorItem[weightItem.ProductColorId][0]
	o.ProductColorName = productColorItem[weightItem.ProductColorId][1]
	o.WarehouseInType = weightItem.WarehouseInType
	o.WarehouseInTypeName = weightItem.WarehouseInType.String()
	o.WarehouseInOrderId = weightItem.WarehouseInOrderId
	o.WarehouseInOrderNo = weightItem.WarehouseInOrderNo
	o.WeightError = weightItem.WeightError
	o.PaperTubeWeight = weightItem.PaperTubeWeight
	o.DyeFactoryColorCode = weightItem.DyeFactoryColorCode
	o.ProductColorKindId = weightItem.ProductColorKindId
	// o.ProductColorKindName=weightItem.ProductColorKindName
	o.WarehouseId = weightItem.WarehouseId
	// o.WarehouseName=weightItem.WarehouseName
	o.CustomerId = weightItem.CustomerId
	// o.CustomerName=weightItem.CustomerName
	o.DigitalCode = weightItem.DigitalCode
	o.ProductLevelId = weightItem.ProductLevelId
	// o.ProductLevelName=weightItem.ProductLevelName
	o.ProductKindId = weightItem.ProductKindId
	// o.ProductKindName=weightItem.ProductKindName
	o.ContractNumber = weightItem.ContractNumber
	o.CustomerPoNum = weightItem.CustomerPoNum
	o.CustomerAccountNum = weightItem.CustomerAccountNum
	o.ShelfNo = weightItem.ShelfNo
	o.StockRemark = weightItem.StockRemark
	o.InternalRemark = weightItem.InternalRemark
	o.Remark = weightItem.Remark
	o.MeasurementUnitId = weightItem.MeasurementUnitId
	o.MeasurementUnitName = weightItem.MeasurementUnitName
	o.DyelotNumber = weightItem.DyelotNumber
	o.VolumeNumber = weightItem.VolumeNumber
	o.WarehouseBinId = weightItem.WarehouseBinId
	// o.WarehouseBinName=weightItem.WarehouseBinName
	o.Roll = weightItem.Roll
	o.Weight = weightItem.Weight
	o.Length = weightItem.Length
	o.CheckRoll = weightItem.CheckRoll
	o.CheckWeight = weightItem.CheckWeight
	o.CheckLength = weightItem.CheckLength
	o.DifferenceRoll = weightItem.DifferenceRoll
	o.DifferenceWeight = weightItem.DifferenceWeight
	o.DifferenceLength = weightItem.DifferenceLength
	o.FinishedCheck = weightItem.FinishedCheck
	o.CheckUserId = weightItem.CheckUserId
	// o.CheckUserName = weightItem.CheckUserName
	o.CheckTime = tools.MyTime(weightItem.CheckTime)
	o.IsStock = weightItem.IsStock
	data = o
	return
}

func (r *ProductCheckOrderRepo) GetCheckOrderItemReport(ctx context.Context, req *structure.GetProductCheckOrderItemQuery) (
	data structure.GetProductCheckOrderItemReportDataList, count int, err error) {
	var (
		_data          = make(structure.GetProductCheckOrderItemReportDataList, 0)
		itemList       = model.ProductCheckOrderItemList{}
		orderList      = model.ProductCheckOrderList{}
		orderMap       = make(map[uint64]model.ProductCheckOrder)
		orderIds       = set.NewUint64Set()
		bizUnitIds     = set.NewUint64Set()
		bizUnitNameMap = make(map[uint64]string)
		productIds     = set.NewUint64Set()
		productNameMap = make(map[uint64]*product2.ProductRes)
		colorIds       = set.NewUint64Set()
		colorNameMap   = make(map[uint64][2]string)
		levelIds       = set.NewUint64Set()
		levelNameMap   = make(map[uint64]string)
	)

	itemList, count, err = mysql.SearchProductCheckOrderItemForReport(r.tx, req)
	if err != nil {
		return
	}

	for _, item := range itemList {
		orderIds.Add(item.ProductCheckOrderId)
		bizUnitIds.Add(item.CustomerId)
		productIds.Add(item.ProductId)
		colorIds.Add(item.ProductColorId)
		levelIds.Add(item.ProductLevelId)
	}

	// 主单信息
	orderList, err = mysql.FindProductCheckOrderByIDs(r.tx, orderIds.List())
	if err != nil {
		return
	}

	var (
		warehouseIds        = set.NewUint64Set()
		warehouseNameMap    = make(map[uint64]string)
		warehouseBinIds     = set.NewUint64Set()
		warehouseBinNameMap = make(map[uint64]string)
		empIds              = set.NewUint64Set()
		empNameMap          = make(map[uint64]string)
		userIds             = set.NewUint64Set()
		userNameMap         = make(map[uint64]string)
	)
	// 查主单字段名称
	for _, order := range orderList {
		orderMap[order.Id] = order
		warehouseIds.Add(order.WarehouseId)
		warehouseBinIds.Add(order.WarehouseBinId)
		empIds.Add(order.WarehouseManagerId)
		userIds.Add(order.CreatorId)
		userIds.Add(order.AuditorId)
	}

	errgroup.Finish(ctx, 0,
		// 仓库
		func(ctx context.Context) error {
			warehouseNameMap, _ = warehouse.NewPhysicalWarehouseClient().GetPhysicalWarehouseByIds(ctx, warehouseIds.List())
			return nil
		},
		// 仓位
		func(ctx context.Context) error {
			warehouseBinNameMap, _ = warehouse.NewPhysicalWarehouseClient().GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds.List())
			return nil
		},
		// 员工
		func(ctx context.Context) error {
			empNameMap, _ = employee.NewClientEmployeeService().GetEmployeeNameByIds(ctx, empIds.List())
			return nil
		},
		// 员工
		func(ctx context.Context) error {
			userNameMap, _ = user_pb.NewUserClient().GetUserNameByIds(ctx, userIds.List())
			return nil
		},
		// 往来单位
		func(ctx context.Context) error {
			bizUnitNameMap, _ = biz_unit.NewClientBizUnitService().GetBizUnitNameByIds(ctx, bizUnitIds.List())
			return nil
		},
		// 颜色
		func(ctx context.Context) error {
			colorNameMap, _ = product2.NewProductColorClient().GetProductColorItemByIds(ctx, colorIds.List())
			return nil
		},
		// 颜色
		func(ctx context.Context) error {
			levelNameMap, _ = info_basic_data.NewInfoBaseFinishedProductLevelClient().GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds.List())
			return nil
		},
		// 成品
		func(ctx context.Context) error {
			productNameMap, _ = product2.NewProductClient().GetProductMapByIds(ctx, productIds.List())
			return nil
		},
	)

	// 最后组装数据
	for _, item := range itemList {
		itemData := item.BuildItemResp()
		// item 组装数据
		itemData.CustomerName = bizUnitNameMap[item.CustomerId]
		itemData.ProductLevelName = levelNameMap[item.ProductLevelId]
		itemData.ProductRemark = item.Remark
		itemData.ColorCode = colorNameMap[item.ProductColorId][0]
		itemData.ColorName = colorNameMap[item.ProductColorId][1]
		if val, ok := productNameMap[item.ProductId]; ok {
			itemData.ProductCode = val.FinishProductCode
			itemData.ProductName = val.FinishProductName
			itemData.FinishProductWidthAndWightUnit = val.FinishProductWidthAndWightUnit
		}
		// order 组装数据
		order := orderMap[item.ProductCheckOrderId]
		itemData.OrderNo = order.OrderNo
		itemData.OrderDate = tools.MyTime(order.CheckTime)
		itemData.WarehouseName = warehouseNameMap[order.WarehouseId]
		itemData.WarehouseBinName = warehouseBinNameMap[order.WarehouseBinId]
		itemData.StoreKeeperName = empNameMap[order.WarehouseManagerId]
		itemData.Remark = order.Remark
		itemData.CreateOrderUserName = userNameMap[order.CreatorId]
		itemData.CreateOrderTime = tools.MyTime(order.CreateTime)
		itemData.AuditorName = userNameMap[order.AuditorId]
		itemData.AuditTime = tools.MyTime(order.AuditDate)
		_data = append(_data, itemData)
	}

	data = _data
	return
}
