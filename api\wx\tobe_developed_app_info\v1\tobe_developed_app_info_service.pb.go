// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.29.1
// source: api/wx/tobe_developed_app_info/tobe_developed_app_info_service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto protoreflect.FileDescriptor

var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_rawDesc = []byte{
	0x0a, 0x44, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x2f, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64,
	0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x3c, 0x61, 0x70,
	0x69, 0x2f, 0x77, 0x78, 0x2f, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2f, 0x74, 0x6f, 0x62,
	0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xda, 0x05, 0x0a, 0x14, 0x54,
	0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0xae, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f,
	0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x47, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74,
	0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x45, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x00, 0x12, 0xbd, 0x01, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54,
	0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x4c, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x4a, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x00, 0x12, 0xa5, 0x01, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x62, 0x65,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x12, 0x44, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62,
	0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x42, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0xa8, 0x01, 0x0a,
	0x18, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x45, 0x2e, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x43, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62,
	0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x62, 0x65,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x49, 0x0a, 0x24, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x42,
	0x16, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_goTypes = []any{
	(*UpdateTobeDevelopedAppInfoRequest)(nil),      // 0: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRequest
	(*UpdateTobeDevelopedAppInfoRobotRequest)(nil), // 1: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRobotRequest
	(*GetTobeDevelopedAppInfoRequest)(nil),         // 2: wx.api.wx.tobe_developed_app_info.v1.GetTobeDevelopedAppInfoRequest
	(*ListTobeDevelopedAppInfoRequest)(nil),        // 3: wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoRequest
	(*UpdateTobeDevelopedAppInfoReply)(nil),        // 4: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoReply
	(*UpdateTobeDevelopedAppInfoRobotReply)(nil),   // 5: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRobotReply
	(*GetTobeDevelopedAppInfoReply)(nil),           // 6: wx.api.wx.tobe_developed_app_info.v1.GetTobeDevelopedAppInfoReply
	(*ListTobeDevelopedAppInfoReply)(nil),          // 7: wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoReply
}
var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_depIdxs = []int32{
	0, // 0: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.UpdateTobeDevelopedAppInfo:input_type -> wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRequest
	1, // 1: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.UpdateTobeDevelopedAppInfoRobot:input_type -> wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRobotRequest
	2, // 2: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.GetTobeDevelopedAppInfo:input_type -> wx.api.wx.tobe_developed_app_info.v1.GetTobeDevelopedAppInfoRequest
	3, // 3: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.ListTobeDevelopedAppInfo:input_type -> wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoRequest
	4, // 4: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.UpdateTobeDevelopedAppInfo:output_type -> wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoReply
	5, // 5: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.UpdateTobeDevelopedAppInfoRobot:output_type -> wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRobotReply
	6, // 6: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.GetTobeDevelopedAppInfo:output_type -> wx.api.wx.tobe_developed_app_info.v1.GetTobeDevelopedAppInfoReply
	7, // 7: wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo.ListTobeDevelopedAppInfo:output_type -> wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoReply
	4, // [4:8] is the sub-list for method output_type
	0, // [0:4] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_init() }
func file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_init() {
	if File_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto != nil {
		return
	}
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_goTypes,
		DependencyIndexes: file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_depIdxs,
	}.Build()
	File_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto = out.File
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_rawDesc = nil
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_goTypes = nil
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_service_proto_depIdxs = nil
}
