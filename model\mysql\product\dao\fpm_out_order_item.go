package dao

import (
	common "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	. "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
)

func MustCreateFpmOutOrderItem(tx *mysql_base.Tx, r FpmOutOrderItem) (o FpmOutOrderItem, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateFpmOutOrderItem(tx *mysql_base.Tx, r FpmOutOrderItem) (o FpmOutOrderItem, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteFpmOutOrderItem(tx *mysql_base.Tx, r FpmOutOrderItem) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstFpmOutOrderItemByID(tx *mysql_base.Tx, id uint64) (r FpmOutOrderItem, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstFpmOutOrderItemByID(tx *mysql_base.Tx, id uint64) (r FpmOutOrderItem, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FindFpmOutOrderItemByFpmOutOrderItemID(tx *mysql_base.Tx, objects ...interface{}) (o FpmOutOrderItemList, err error) {
	ids := GetFpmOutOrderItemIdList(objects)
	var (
		r    FpmOutOrderItem
		cond = mysql_base.NewCondition()
		list []FpmOutOrderItem
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmOutOrderItemByIDs(tx *mysql_base.Tx, ids []uint64) (o FpmOutOrderItemList, err error) {
	var (
		r    FpmOutOrderItem
		cond = mysql_base.NewCondition()
		list []FpmOutOrderItem
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmSaleOutOrderItemByArrangeItemIDs(tx *mysql_base.Tx, ids []uint64) (list FpmOutOrderItemList, err error) {
	var (
		o    FpmOutOrder
		r    FpmOutOrderItem
		cond = mysql_base.NewCondition()
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableLeftJoiner(r, o, "parent_id", "id")
	cond.AddTableContainMatch(r, "arrange_item_id", ids)
	cond.AddTableEqual(o, "out_order_type", common.WarehouseGoodOutTypeSale)
	cond.AddTableNotEqual(o, "audit_status", common_system.OrderStatusVoided)
	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	return
}

// 根据上级id获取
func FindFpmOutOrderItemByParentID(tx *mysql_base.Tx, pid uint64) (o FpmOutOrderItemList, err error) {
	var (
		r    FpmOutOrderItem
		cond = mysql_base.NewCondition()
		list []FpmOutOrderItem
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "parent_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 获取成品销售出仓单成品信息通过父id
func FindFpmOutOrderItemByParenTIDs(tx *mysql_base.Tx, pid []uint64) (o FpmOutOrderItemList, err error) {
	var (
		r    FpmOutOrderItem
		cond = mysql_base.NewCondition()
		list []FpmOutOrderItem
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableContainMatch(r, "parent_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmOutOrderItem(tx *mysql_base.Tx, q *structure.GetFpmOutOrderItemListQuery) (o FpmOutOrderItemList, count int, err error) {
	var (
		r           FpmOutOrderItem
		cond        = mysql_base.NewCondition()
		list        []FpmOutOrderItem
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.ParentOrderNo != "" {
		cond.AddFuzzyMatch("parent_order_no", q.ParentOrderNo)
	}
	if q.ProductId > 0 {
		cond.AddEqual("product_id", q.ProductId)
	}
	if q.ProductCode != "" {
		cond.AddEqual("product_code", q.ProductCode)
	}
	if q.ProductName != "" {
		cond.AddFuzzyMatch("product_name", q.ProductName)
	}
	if q.JudgeUseByDye {
		cond.AddContainMatch("parent_id", q.ListIDs)
		cond.AddMultiFieldLikeMatchRangeGE([]string{"out_roll"}, "dye_roll+return_roll")
	}

	groupFields = []string{}
	cond.AddSort("-id")
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmOutOrderItemForReport(tx *mysql_base.Tx, q *structure.GetFpmOutOrderItemListQuery) (o FpmOutOrderItemList, count int, err error) {
	var (
		r           FpmOutOrderItem
		cond        = mysql_base.NewCondition()
		list        []FpmOutOrderItem
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	groupFields = []string{}
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}

	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}
