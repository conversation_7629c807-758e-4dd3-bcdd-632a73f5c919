package product

import (
	"context"
	"github.com/gin-gonic/gin"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/info_basic_data"
	basic_data "hcscm/extern/pb/basic_data/ingredient_rel"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/middleware"
	"hcscm/msg/msg_publish"
	"hcscm/server/system"
	svc "hcscm/service/basic_data"
	structure "hcscm/structure/product"
	structure_base "hcscm/structure/system"
	"hcscm/vars"
	"strings"
)

// @Tags 【成品资料】
// @Summary 添加成品资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddFinishProductParam{}  true "创建FinishProduct"
// @Success 200 {object}  structure.AddFinishProductData{}
// @Router /hcscm/mp/v1/product/finishProduct/addFinishProduct [post]
// @Tags 【成品资料】
// @Summary 添加成品资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddFinishProductParam{}  true "创建FinishProduct"
// @Success 200 {object}  structure.AddFinishProductData{}
// @Router /hcscm/admin/v1/product/finishProduct/addFinishProduct [post]
func AddFinishProduct(c *gin.Context) {
	var (
		q    = &structure.AddFinishProductParam{}
		data = structure.AddFinishProductData{}
		svc  = svc.NewFinishProductService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	err = addIngredientRel(ctx, data.Id, q.IngredientItem)
	if err != nil {
		return
	}

	// 图片搜索上传
	if len(data.SearchImageUploadList) > 0 {
		msg_publish.PushSearchImageUpload(ctx, data.SearchImageUploadList)
	}
	return
}

func AddQuickFinishProduct(c *gin.Context) {
	var (
		q    = &structure.AddQuickFinishProductParam{}
		data = structure.AddFinishProductData{}
		svc  = svc.NewFinishProductService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	s := strings.SplitAfterN(q.FinishProductName, "#", 2)
	if len(s) != 2 {
		s = strings.SplitN(q.FinishProductName, " ", 2)
	}
	for i, s2 := range s {
		if i == 0 {
			q.FinishProductCode = s2
		}
		if i == 1 {
			q.FinishProductName = s2
		}
	}
	// 获取启用的第一个计量单位id
	unit, _ := info_basic_data.NewInfoBaseMeasurementUnitClient().GetFirstinfoBaseMeasurementUnit(ctx)

	typeFabric, _ := type_basic_data.NewTypeFabricClient().GetLatestTypeFabric(ctx)

	req := &structure.AddFinishProductParam{
		FinishProductCode:     q.FinishProductCode,
		FinishProductName:     q.FinishProductName,
		FinishProductFullName: q.FinishProductFullName,
		MeasurementUnitId:     unit.Id,
		TypeGreyFabricId:      typeFabric.Id,
		TypeGreyFabricCode:    typeFabric.Code,
		TypeGreyFabricName:    typeFabric.Name,
	}
	data, err = svc.Add(ctx, req)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品资料】
// @Summary 删除成品资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.DeleteFinishProductParam{}  true "创建FinishProduct"
// @Success 200 {object}  structure.DeleteFinishProductData{}
// @Router /hcscm/admin/v1/product/finishProduct/deleteFinishProduct [delete]
func DeleteFinishProduct(c *gin.Context) {
	var (
		q    = &structure.DeleteFinishProductParam{}
		data = structure.DeleteFinishProductData{}
		svc  = svc.NewFinishProductService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Delete(ctx, q)
	if err != nil {
		return
	}

	// 图片搜索上传
	if len(data.SearchImageUploadList) > 0 {
		msg_publish.PushSearchImageUpload(ctx, data.SearchImageUploadList)
	}
	return
}

// @Tags 【成品资料】
// @Summary 更新成品资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateFinishProductParam{}  true "创建FinishProduct"
// @Success 200 {object}  structure.UpdateFinishProductData{}
// @Router /hcscm/admin/v1/product/finishProduct/updateFinishProduct [put]
func UpdateFinishProduct(c *gin.Context) {
	var (
		q                   = &structure.UpdateFinishProductParam{}
		data                = structure.UpdateFinishProductData{}
		productService      = svc.NewFinishProductService()
		productColorService = svc.NewFinishProductColorService()
		err                 error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = productService.Update(ctx, q)
	if err != nil {
		return
	}

	err = addIngredientRel(ctx, data.Id, q.IngredientItem)
	if err != nil {
		return
	}

	// 同步信息到对应的颜色中(坯布id，长度转数量比率，成品成份，纱支，密度，门幅，门幅单位，克重，克重单位，漂染性id，织造组织id，缩水率)
	if vars.IsSyncProductColorMessage {
		err = productColorService.UpdateProductColorOnlyByProductId(ctx, q.ToProductColorParam())
		if err != nil {
			return
		}
	}

	// 图片搜索上传
	if len(data.SearchImageUploadList) > 0 {
		msg_publish.PushSearchImageUpload(ctx, data.SearchImageUploadList)
	}
	return
}

// @Tags 【成品资料】
// @Summary 更新成品资料状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateFinishProductStatusParam{}  true "创建FinishProduct"
// @Success 200 {object}  structure.UpdateFinishProductStatusData{}
// @Router /hcscm/admin/v1/product/finishProduct/updateFinishProductStatus [put]
func UpdateFinishProductStatus(c *gin.Context) {
	var (
		q                   = &structure.UpdateFinishProductStatusParam{}
		data                = structure.UpdateFinishProductStatusData{}
		productService      = svc.NewFinishProductService()
		productColorService = svc.NewFinishProductColorService()
		err                 error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = productService.UpdateStatus(ctx, q)
	if err != nil {
		return
	}

	// 同步状态到对应的颜色中
	if vars.IsSyncProductColorMessage {
		if q.Status == common_system.StatusDisable {
			err = productColorService.UpdateProductColorOnlyByProductId(ctx, q.ToProductColorParam())
			if err != nil {
				return
			}
		}
	}

	// 图片搜索上传
	if len(data.SearchImageUploadList) > 0 {
		msg_publish.PushSearchImageUpload(ctx, data.SearchImageUploadList)
	}
	return
}

// @Tags 【成品资料】
// @Summary 获取成品资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetFinishProductData{}
// @Router /hcscm/mp/v1/product/finishProduct/getFinishProduct [get]
// @Tags 【成品资料】
// @Summary 获取成品资料
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetFinishProductData{}
// @Router /hcscm/admin/v1/product/finishProduct/getFinishProduct [get]
func GetFinishProduct(c *gin.Context) {
	var (
		q    = &structure.GetFinishProductQuery{}
		data = structure.GetFinishProductData{}
		svc  = svc.NewFinishProductService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	data.IngredientItem, err = getIngredientRel(ctx, data.Id)
	if err != nil {
		return
	}
	return
}

// @Tags 【成品资料】
// @Summary 获取成品资料(枚举)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetFinishProductData{}
// @Router /hcscm/admin/v1/product/finishProduct/getFinishProductEnum [get]
func GetFinishProductEnum(c *gin.Context) {
	var (
		q    = &structure.GetFinishProductQuery{}
		data = structure.GetFinishProductData{}
		svc  = svc.NewFinishProductService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	data.IngredientItem, err = getIngredientRel(ctx, data.Id)
	if err != nil {
		return
	}
	return
}

// @Tags 【成品资料】
// @Summary 获取成品资料列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_code     query     string  false  "成品编号"
// @Param     finish_product_name     query     string  false  "成品名称或全称"
// @Param     finish_product_code_or_name     query     string  false  "成品编号或名称"
// @Param     type_grey_fabric_id     query     string  false  "布种类型id"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     grey_fabric_id     query     int  false  "坯布信息ID"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     dye_factory_order_follower_id     query     int  false  "染厂跟单用户ID"
// @Param     finish_product_width     query     string  false  "成品幅宽"
// @Param     finish_product_gram_weight     query     string  false  "成品克重"
// @Param     finish_product_craft     query     string  false  "成品工艺"
// @Param     status     query     int  false  "状态"
// @Param     dyeing_craft     query     string  false  "染整工艺"
// @Param     finish_product_level_id     query     int  false  "成品等级id"
// @Param		field_search			query		string	false	"字段搜索"
// @Success 200 {object}  structure.GetFinishProductDataList{}
// @Router /hcscm/admin/v1/product/finishProduct/getFinishProductList [get]
func GetFinishProductList(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductListQuery{}
		list  = make(structure.GetFinishProductDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品资料】
// @Summary 获取成品资料列表(只获取一些字段，填编号时候用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     download  query     int  false  "download"
// @Param     finish_product_code     query     string  false  "成品编号"
// @Success 200 {object}  structure.GetSomeProductFieldDataList{}
// @Router /hcscm/admin/v1/product/finishProduct/searchForSomeProductField [get]
func SearchForSomeProductField(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductListQuery{}
		list  = make(structure.GetSomeProductFieldDataList, 0)
		err   error
		total int
		svc   = svc.NewFinishProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	if q.FinishProductCode == "" {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, "请输入编号"))
		return
	}

	if q.Page == 0 && q.Size == 0 {
		q.Page = 1
		q.Size = 50
	}

	list, err = svc.SearchForSomeProductField(ctx, q)
	if err != nil {
		return
	}

	total = len(list)

	return
}

// @Tags 【成品资料】
// @Summary 获取成品资料下拉列表(可用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_code     query     string  false  "成品编号"
// @Param     finish_product_name     query     string  false  "成品名称或全称"
// @Param     finish_product_code_or_name     query     string  false  "成品编号或名称"
// @Param     type_grey_fabric_id     query     int  false  "布种类型id"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     grey_fabric_id     query     int  false  "坯布信息ID"
// @Param     grey_fabric_code     query     string  false  "坯布信息编号"
// @Param     grey_fabric_name     query     string  false  "坯布信息名称"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     dye_factory_order_follower_id     query     int  false  "染厂跟单用户ID"
// @Param     finish_product_width     query     string  false  "成品幅宽"
// @Param     finish_product_gram_weight     query     string  false  "成品克重"
// @Param     finish_product_craft     query     string  false  "成品工艺"
// @Param     status     query     int  false  "状态"
// @Param     dyeing_craft     query     string  false  "染整工艺"
// @Param     finish_product_level_id     query     int  false  "成品等级id"
// @Success 200 {object}  structure.GetFinishProductDropdownDataList{}
// @Router /hcscm/mp/v1/product/finishProduct/getMPFinishProductDropdownList [get]
// @Tags 【成品资料】
// @Summary 获取成品资料下拉列表(可用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_code     query     string  false  "成品编号"
// @Param     finish_product_name     query     string  false  "成品名称或全称"
// @Param     finish_product_code_or_name     query     string  false  "成品编号或名称"
// @Param     type_grey_fabric_id     query     int  false  "布种类型id"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     grey_fabric_id     query     int  false  "坯布信息ID"
// @Param     grey_fabric_code     query     string  false  "坯布信息编号"
// @Param     grey_fabric_name     query     string  false  "坯布信息名称"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     dye_factory_order_follower_id     query     int  false  "染厂跟单用户ID"
// @Param     finish_product_width     query     string  false  "成品幅宽"
// @Param     finish_product_gram_weight     query     string  false  "成品克重"
// @Param     finish_product_craft     query     string  false  "成品工艺"
// @Param     status     query     int  false  "状态"
// @Param     dyeing_craft     query     string  false  "染整工艺"
// @Param     finish_product_level_id     query     int  false  "成品等级id"
// @Success 200 {object}  structure.GetFinishProductDropdownDataList{}
// @Router /hcscm/admin/v1/product/finishProduct/getFinishProductDropdownList [get]
func GetFinishProductDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductListQuery{}
		list  = make(structure.GetFinishProductDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品资料】
// @Summary 获取成品资料下拉列表(可用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_code     query     string  false  "成品编号"
// @Param     finish_product_name     query     string  false  "成品名称或全称"
// @Param     finish_product_code_or_name     query     string  false  "成品编号或名称"
// @Param     type_grey_fabric_id     query     int  false  "布种类型id"
// @Param     measurement_unit_id     query     int  false  "计量单位id"
// @Param     grey_fabric_id     query     int  false  "坯布信息ID"
// @Param     grey_fabric_code     query     string  false  "坯布信息编号"
// @Param     grey_fabric_name     query     string  false  "坯布信息名称"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     dye_factory_order_follower_id     query     int  false  "染厂跟单用户ID"
// @Param     finish_product_width     query     string  false  "成品幅宽"
// @Param     finish_product_gram_weight     query     string  false  "成品克重"
// @Param     finish_product_craft     query     string  false  "成品工艺"
// @Param     status     query     int  false  "状态"
// @Param     dyeing_craft     query     string  false  "染整工艺"
// @Param     finish_product_level_id     query     int  false  "成品等级id"
// @Success 200 {object}  structure.GetFinishProductDropdownDataList{}
// @Router /hcscm/mp/v1/product/finishProduct/getFinishProductDropdownList [get]
func MPGetFinishProductDropdownList(c *gin.Context) {
	GetFinishProductDropdownList(c)
}

// @Tags 【成品资料】
// @Summary 获取成品资料下拉列表(可用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_code     query     string  false  "成品编号"
// @Param     type_grey_fabric_id     query     string  false  "布种类型id"
// @Success 200 {object}  structure.GetKindAndProductDropdownDataList{}
// @Router /hcscm/admin/v1/product/finishProduct/getKindAndProductList [get]
func GetKindAndProductList(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductListQuery{}
		list  = make(structure.GetKindAndProductDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetKindAndProductList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品资料】
// @Summary 搜索相似图片
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     image_url     query     string  false  "图片url"
// @Success 200 {object}  structure.SearchImageDataList{}
// @Router /hcscm/admin/v1/product/finishProduct/search_image [get]
// @Router /hcscm/third_party/v1/product/finishProduct/search_image [get]
func SearchImageByUrl(c *gin.Context) {
	var (
		q     = &structure.SearchImageListQuery{}
		list  = make(structure.SearchImageDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	// todo:检查用户登录状态
	// info := metadata.GetLoginInfo(ctx)
	// if info.GetUserLoginType() == common.UserLoginTypeTenantManagement {
	// 	err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "用户登录类型错误"))
	// 	return
	// }

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.SearchImageByUrl(ctx, q)
	if err != nil {
		return
	}

	return
}

// 新增修改 原料、坯布、成品资料 成分
// ingredientType:1 原料 2 坯布 3 成品
func addIngredientRel(ctx context.Context, materialId uint64, req structure_base.IngredientItemList) (err error) {
	var (
		ingredientRelSvc = basic_data.NewIngredientRelClient() // 成分组成
		ingredientItem   = basic_data.IngredientItemList{}     // 成分组成
	)
	for _, item := range req {
		ingredientItem = append(ingredientItem, basic_data.IngredientItem{
			FibreId: item.FibreId,
			Count:   item.Count,
		})
	}
	_, err = ingredientRelSvc.AddIngredientRel(ctx, materialId, 3, ingredientItem)
	if err != nil {
		return
	}
	return
}

func getIngredientRel(ctx context.Context, materialId uint64) (res structure_base.IngredientItemList, err error) {
	var (
		ingredientRelSvc = basic_data.NewIngredientRelClient() // 成分组成
		ingredientItem   = basic_data.IngredientItemList{}     // 成分组成
	)
	ingredientItem, err = ingredientRelSvc.GetIngredientRel(ctx, materialId)
	if err != nil {
		return
	}
	res = ingredientItem.ToStructure()
	return
}
