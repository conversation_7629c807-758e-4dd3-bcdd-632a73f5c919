package system

import (
	"context"
	"encoding/json"
	"fmt"
	"hcscm/aggs/biz_unit"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	biz_unit_pb "hcscm/extern/pb/biz_unit"
	departmentPb "hcscm/extern/pb/department"
	"hcscm/extern/pb/employee"
	sale_system_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	biz_unit2 "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/mysql/qywx"
	qywxDao "hcscm/model/mysql/qywx/dao"
	mysql "hcscm/model/mysql/system"
	tenant_mysql "hcscm/model/mysql/tenant_management"
	tenantManagementDao "hcscm/model/mysql/tenant_management/dao"
	"hcscm/model/redis"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"strconv"
	"strings"
	"time"
)

var (
	SystemOperator metadata.IOperator = &WashSystemUser{}
)

func initSystemOperator() {
	// SystemOperator, _ = NewLoginInfoByUserID(nil, "", 1, 1)
}

// func initializeTheMessageTemplate() {
//	CreateInitializeTheMessageTemplateV2(nil)
// }

type Access struct {
	AccessScope              common.RoleAccessDataScope
	AccessScopeOtherSelect   string
	MPAccessScope            common.RoleAccessDataScope
	MPAccessScopeOtherSelect string
}

type AccessList []Access

type LoginInfo struct {
	User                  mysql.User                  `json:"user,omitempty"`
	BizUnit               biz_unit2.BizUnit           `json:"biz_unit,omitempty"`
	SaleSystem            sale_system_pb.Res          `json:"sale_system,omitempty"`
	Department            departmentPb.Department     `json:"department,omitempty"`
	SubDepartments        departmentPb.DepartmentList `json:"sub_departments,omitempty"` // 当前登录用户所在部门的子部门
	Platform              common.Platform             `json:"platform,omitempty"`
	LoginTime             time.Time                   `json:"login_time,omitempty"`
	Token                 string                      `json:"token,omitempty"`
	RoleAccess            mysql.RoleAccessList        `json:"role_access_list,omitempty"` // 用户所属角色权限列表
	ResourceRouterNames   []string                    `json:"resource_router_names,omitempty"`
	ButtonCodes           []string                    `json:"button_codes,omitempty"`
	MPResourceRouterNames []string                    `json:"mp_resource_router_names,omitempty"`
	MPButtonCodes         []string                    `json:"mp_button_codes,omitempty"`
	MenuIDs               []uint64                    `json:"menu_ids,omitempty"`
	WarehouseIDs          []uint64                    `json:"warehouse_ids,omitempty"`
	SaleSystemIDs         []uint64                    `json:"sale_system_ids,omitempty"`
	BizUnitIDs            []uint64                    `json:"biz_unit_ids,omitempty"`
	AccessList            AccessList                  `json:"access_list,omitempty"`
	DataAccessScope       []string                    `json:"data_access_scope,omitempty"`    // 数据脱敏权限
	MPDataAccessScope     []string                    `json:"mp_data_access_scope,omitempty"` // 内部商城数据脱敏权限
	// 特殊操作权限,判断创建人是不是该用户
	AllowUpdateOrder   bool             `json:"allow_update_order"`   // 是否允许修改别人的单据
	AllowCancelOther   bool             `json:"allow_cancel_other"`   // 是否允许作废别人的单据
	AllowAuditSelf     bool             `json:"allow_audit_self"`     // 是否允许审核自己的单据
	TenantManagement   TenantManagement `json:"tenant_management"`    // 登录的租户
	TenantManagementID uint64           `json:"tenant_management_id"` // 登录的租户id
}

type TenantManagement struct {
	Id                        uint64    `json:"id,omitempty"`
	TenantPhoneNumber         string    `json:"tenant_phone_number"`           // 租户手机号
	TenantContacts            string    `json:"tenant_phone_contacts"`         // 租户联系人
	TenantCompanyName         string    `json:"tenant_company_name"`           // 租户公司名称
	DatabaseName              string    `json:"database_name"`                 // 数据库名称
	ActivationTime            time.Time `json:"activation_time"`               // 激活时间
	ConfigJson                string    `json:"config_json"`                   // 配置文件json
	AssignPort                int       `json:"assign_port"`                   // 分配端口
	CorpID                    string    `json:"corp_id"`                       // 企业id
	TobeDevelopedAppEndpoint  string    `json:"tobe_developed_app_endpoint"`   // 代开发应用endpoint
	TobeDevelopedAppRobotCode string    `json:"tobe_developed_app_robot_code"` // 代开发应用阿布机器人编码
}

func (info *LoginInfo) GetUserId() uint64 {
	return info.User.Id
}

func (info *LoginInfo) GetEmpId() uint64 { // 获取用户员工id
	return info.User.EmployeeID
}

func (info *LoginInfo) GetTenantManagementDbName() string {
	return info.TenantManagement.DatabaseName
}

func (info *LoginInfo) SetTenantManagement(Id uint64, corpID, tobeDevelopedAppEndpoint, tobeDevelopedAppRobotCode, TenantPhoneNumber string, TenantContacts string, TenantCompanyName string, DatabaseName string, ActivationTime time.Time, ConfigJson string, AssignPort int) {
	info.TenantManagement = TenantManagement{
		Id:                        Id,
		TenantPhoneNumber:         TenantPhoneNumber,
		TenantContacts:            TenantContacts,
		TenantCompanyName:         TenantCompanyName,
		DatabaseName:              DatabaseName,
		ActivationTime:            ActivationTime,
		ConfigJson:                ConfigJson,
		AssignPort:                AssignPort,
		CorpID:                    corpID,
		TobeDevelopedAppEndpoint:  tobeDevelopedAppEndpoint,
		TobeDevelopedAppRobotCode: tobeDevelopedAppRobotCode,
	}
	info.TenantManagementID = Id
}

func (info *LoginInfo) GetTobeDevelopedAppEndpoint() string {
	return info.TenantManagement.TobeDevelopedAppEndpoint
}

func (info *LoginInfo) GetTobeDevelopedAppRobotCode() string {
	return info.TenantManagement.TobeDevelopedAppRobotCode
}

func (info *LoginInfo) GetCorpID() string {
	return info.TenantManagement.CorpID
}

func (info *LoginInfo) GetTenantManagementId() uint64 {
	return info.TenantManagementID
}

func (info *LoginInfo) GetTenantManagementName() string {
	return info.TenantManagement.TenantCompanyName
}

func (info *LoginInfo) SetTenantManagementId(tenantManagementId uint64) {
	info.TenantManagementID = tenantManagementId
	return
}

func (info *LoginInfo) GetUserName() string {
	if info.User.EmployeeName == "" {
		return info.User.Account
	}
	return info.User.EmployeeName
}

func (info *LoginInfo) GetBizUnitId() uint64 {
	return info.BizUnit.Id
}

func (info *LoginInfo) GetDefaultSaleSystemId() uint64 {
	return info.SaleSystem.Id
}

func (info *LoginInfo) GetDepartmentId() uint64 {
	return info.Department.Id
}

func (info *LoginInfo) GetToken() string {
	return info.Token
}

func (info *LoginInfo) IsSaasCompany() bool {
	return false
}

func (info *LoginInfo) IsAvailable() bool {
	return info.User.IsAvailable()
}

// func (info *LoginInfo) IsITDepartment() bool {
//	return info.Department.IsITDepartment()
// }
//
// func (info *LoginInfo) FilterDepartment(departmentTypes []common.DepartmentType) (r []uint64) {
//	for _, department := range info.Departments {
//		for _, departmentType := range departmentTypes {
//			if department.IsDepartmentType(departmentType) {
//				r = append(r, department.Id)
//			}
//		}
//	}
//	return
// }

func (info *LoginInfo) IsMallUser() bool {
	return false
}

func (info *LoginInfo) GetUserType() common.UserType {
	return common.UserType(info.User.Type)
}

func (info *LoginInfo) GetSubDepartmentId() (r []uint64) {
	for _, department := range info.SubDepartments {
		r = append(r, department.Id)
	}
	return r
}

func (info *LoginInfo) GetPlatform() (r common.Platform) {
	return info.Platform
}

func (info *LoginInfo) GetLoginTime() time.Time {
	return info.LoginTime
}

func (info *LoginInfo) GetRoleAccessIds() (r []uint64) {
	for _, roleAccess := range info.RoleAccess {
		r = append(r, roleAccess.Id)
	}
	return
}

func (info *LoginInfo) GetMenuIds() (r []uint64) {
	return info.MenuIDs
}

func (info *LoginInfo) GetResourceRouterNames() (r []string) {
	return info.ResourceRouterNames
}

func (info *LoginInfo) GetButtonCodes() (r []string) {
	return info.ButtonCodes
}

func (info *LoginInfo) GetMPResourceRouterNames() (r []string) {
	return info.MPResourceRouterNames
}

func (info *LoginInfo) GetMPButtonCodes() (r []string) {
	return info.MPButtonCodes
}

func (info *LoginInfo) GetDataAccessScope() []byte {
	access, _ := json.Marshal(info.AccessList)
	return access
}

func (info *LoginInfo) GetDataSeparate() (data []string) {
	return info.DataAccessScope
}

func (info *LoginInfo) GetMPDataSeparate() (data []string) {
	return info.MPDataAccessScope
}

func (info *LoginInfo) GetWarehouseIds() (r []uint64) {
	return info.WarehouseIDs
}

func (info *LoginInfo) GetSaleSystemIds() (r []uint64) {
	return info.SaleSystemIDs
}

func (info *LoginInfo) GetBizUnitIds() (r []uint64) {
	return info.BizUnitIDs
}

func (info *LoginInfo) IsAllowUpdateOrder() (r bool) {
	return info.AllowUpdateOrder
}

func (info *LoginInfo) IsAllowCancelOther() (r bool) {
	return info.AllowCancelOther
}

func (info *LoginInfo) IsAllowAuditSelf() (r bool) {
	return info.AllowAuditSelf
}

func (info *LoginInfo) GetUserLoginType() common.UserLoginType {
	return common.UserLoginTypeUser
}

func NewLoginInfoByAdminToken(ctx context.Context, tx *mysql_base.Tx, token string, platform common.Platform) (r metadata.IOperator, authSuccess bool, err error) {
	var (
		userID uint64
		exist  bool           // 登录态缓存用
		module []byte         // 登录态缓存用
		info   = &LoginInfo{} // 登录态缓存用
	)
	userID, authSuccess, err = redis.GetAdminUserByToken(ctx, token)
	if err != nil {
		return
	}

	// token不存在或已过期
	if !authSuccess {
		// 删除登录态缓存
		redis.DeleteUserInfo(ctx, userID, platform)
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize))
		return
	}

	// 缓存获取登录态
	module, exist, err = redis.GetUserInfo(ctx, userID, platform)
	if err != nil {
		return
	}
	if exist {
		err = json.Unmarshal(module, &info)
		fmt.Println("登录态命中缓存")
		if err != nil {
			return
		}
		r = info
	} else {
		r, err = NewLoginInfoByUserID(tx, token, userID, platform)
	}
	return
}

func NewLoginInfoByH5Token(ctx context.Context, tx *mysql_base.Tx, token string, platform common.Platform) (r metadata.IOperator, authSuccess bool, err error) {
	var (
		userID uint64
		exist  bool           // 登录态缓存用
		module []byte         // 登录态缓存用
		info   = &LoginInfo{} // 登录态缓存用
	)
	userID, authSuccess, err = redis.GetH5UserByToken(ctx, token)
	if err != nil {
		return
	}

	// token不存在或已过期
	if !authSuccess {
		// 删除登录态缓存
		redis.DeleteUserInfo(ctx, userID, platform)
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize))
		return
	}

	// 缓存获取登录态
	module, exist, err = redis.GetUserInfo(ctx, userID, platform)
	if err != nil {
		return
	}
	if exist {
		err = json.Unmarshal(module, &info)
		fmt.Println("登录态命中缓存")
		if err != nil {
			return
		}
		r = info
	} else {
		r, err = NewLoginInfoByUserID(tx, token, userID, platform)
	}
	return
}

func NewLoginInfoByPDAToken(ctx context.Context, tx *mysql_base.Tx, token string, platform common.Platform) (r metadata.IOperator, authSuccess bool, err error) {
	var (
		userID uint64
		exist  bool           // 登录态缓存用
		module []byte         // 登录态缓存用
		info   = &LoginInfo{} // 登录态缓存用
	)
	userID, authSuccess, err = redis.GetPDAUserByToken(ctx, token)
	if err != nil {
		return
	}

	// token不存在或已过期
	if !authSuccess {
		// 删除登录态缓存
		redis.DeleteUserInfo(ctx, userID, platform)
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize))
		return
	}

	// 缓存获取登录态
	module, exist, err = redis.GetUserInfo(ctx, userID, platform)
	if err != nil {
		return
	}
	if exist {
		err = json.Unmarshal(module, &info)
		if err != nil {
			return
		}
		r = info
	} else {
		r, err = NewLoginInfoByUserID(tx, token, userID, platform)
	}
	return
}

func NewLoginInfoByMPToken(ctx context.Context, tx *mysql_base.Tx, token string, platform common.Platform) (r metadata.IOperator, authSuccess bool, err error) {
	var (
		userID uint64
		exist  bool           // 登录态缓存用
		module []byte         // 登录态缓存用
		info   = &LoginInfo{} // 登录态缓存用
	)
	userID, authSuccess, err = redis.GetMPUserByToken(ctx, token)
	if err != nil {
		return
	}

	// token不存在或已过期
	if !authSuccess {
		// 删除登录态缓存
		redis.DeleteUserInfo(ctx, userID, platform)
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize))
		return
	}

	// 缓存获取登录态
	module, exist, err = redis.GetUserInfo(ctx, userID, platform)
	if err != nil {
		return
	}
	if exist {
		err = json.Unmarshal(module, &info)
		if err != nil {
			return
		}
		r = info
	} else {
		r, err = NewLoginInfoByUserID(tx, token, userID, platform)
	}
	return
}

func NewLoginInfoByThirdPartyToken(ctx context.Context, tx *mysql_base.Tx, token string, platform common.Platform) (r metadata.IOperator, authSuccess bool, err error) {
	var (
		tenantManagementId uint64
		tenantManagement   tenant_mysql.TenantManagement
		exist              bool   // 登录态缓存用
		module             []byte // 登录态缓存用
	)
	// 尝试获取租户管理信息
	tenantManagementId, authSuccess, err = redis.GetMallTenantManagementByToken(ctx, token)
	if err != nil {
		return
	}
	// 判断是否能成功获取到租户管理信息，如果获取成功则判断租户电子色卡是否已经过期，过期直接返回
	if authSuccess && tenantManagementId != 0 {
		tenantManagement, err = tenantManagementDao.NewTenantManagementDao().MustFirst(ctx, tx, tenantManagementId)
		if err != nil {
			return
		}
		// tenantManagement中的电子色卡有效期到了就返回404状态码
		if tenantManagement.ElectronicColorCardDeadLine.Before(time.Now()) || tenantManagement.ElectronicColorCardStatus == common.ElectronicColorCardStatusDisable {
			// 删除租户登录态缓存
			redis.DeleteTenantManagementUserInfo(ctx, tenantManagementId, platform)
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTenantPackageAlreadyExpire, "服务已过期"))
			return
		}
		var info = &WashSystemUser{} // 租户登录态缓存用
		// 缓存获取租户登录态
		module, exist, err = redis.GetTenantManagementUserInfo(ctx, tenantManagementId, platform)
		if err != nil {
			return
		}
		if exist {
			err = json.Unmarshal(module, &info)
			if err != nil {
				return
			}
			r = info
		} else {
			rel, _, _ := qywxDao.FirstTobeDevelopedAppRelByTenantManagementID(nil, tenantManagementId)
			tobeDevelopedApp, _, _ := qywxDao.FirstTobeDevelopedAppByID(nil, rel.TobeDevelopedAppID)
			info.SetTenantManagement(tenantManagement.Id, tobeDevelopedApp.CorpID, tobeDevelopedApp.Endpoint, tobeDevelopedApp.RobotCode, tenantManagement.TenantPhoneNumber, tenantManagement.TenantContacts, tenantManagement.TenantCompanyName, tenantManagement.DatabaseName, tenantManagement.ActivationTime, tenantManagement.ConfigJson, tenantManagement.AssignPort)
			info.UserLoginType = common.UserLoginTypeTenantManagement
			infoBytes, err2 := json.Marshal(info)
			if err2 != nil {
				return
			}
			redis.AddTenantManagementUserInfo(ctx, tenantManagementId, platform, infoBytes)
			r = info
		}
		return
	}

	// 尝试获取用户管理信息
	var (
		userId uint64
	)
	userId, authSuccess, err = redis.GetMallUserByToken(ctx, token)
	if err != nil {
		return
	}

	// 能成功获取用户管理信息
	if authSuccess && userId != 0 {
		var info = &LoginInfo{} // 登录态缓存用
		// 缓存获取登录态
		module, exist, err = redis.GetUserInfo(ctx, userId, platform)
		if err != nil {
			return
		}
		if exist {
			err = json.Unmarshal(module, &info)
			fmt.Println("登录态命中缓存")
			if err != nil {
				return
			}
			r = info
		} else {
			r, err = NewLoginInfoByUserID(tx, token, userId, platform)
		}
		return
	}
	// 删除用户登录态缓存
	redis.DeleteUserInfo(ctx, userId, platform)

	// 尝试获取往来单位管理信息
	var (
		bizUnitId uint64
		bizUnit   map[uint64]string
	)
	bizUnitId, authSuccess, err = redis.GetMallBizUnitByToken(ctx, token)
	if err != nil {
		return
	}

	// 不能成功获取往来单位管理信息，或者已过期
	if !authSuccess || bizUnitId == 0 {
		// 如果往来单位管理信息也过期，报错
		// 删除登录态缓存
		redis.DeleteBizUnitUserInfo(ctx, bizUnitId, platform)
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize))
		return
	}

	// 获取往来单位管理信息
	bizUnit, err = biz_unit_pb.NewClientBizUnitService().GetBizUnitNameByIds(ctx, []uint64{bizUnitId})
	if err != nil {
		return
	}

	var info = &WashSystemUser{} // 往来单位登录态缓存用
	// 缓存获取租户登录态
	module, exist, err = redis.GetBizUnitUserInfo(ctx, bizUnitId, platform)
	if err != nil {
		return
	}
	if exist {
		err = json.Unmarshal(module, &info)
		if err != nil {
			return
		}
		r = info
	} else {
		rel, _, _ := qywxDao.FirstTobeDevelopedAppRelByTenantManagementID(nil, tenantManagementId)
		tobeDevelopedApp, _, _ := qywxDao.FirstTobeDevelopedAppByID(nil, rel.TobeDevelopedAppID)
		info.SetTenantManagement(tenantManagement.Id, tobeDevelopedApp.CorpID, tobeDevelopedApp.Endpoint, tobeDevelopedApp.RobotCode, tenantManagement.TenantPhoneNumber, tenantManagement.TenantContacts, tenantManagement.TenantCompanyName, tenantManagement.DatabaseName, tenantManagement.ActivationTime, tenantManagement.ConfigJson, tenantManagement.AssignPort)
		info.BizUnitId = bizUnitId
		info.UserName = bizUnit[bizUnitId]
		info.UserLoginType = common.UserLoginTypeBizUnit
		infoBytes, err2 := json.Marshal(info)
		if err2 != nil {
			return
		}
		redis.AddBizUnitUserInfo(ctx, bizUnitId, platform, infoBytes)
		r = info
	}
	return
}

func NewLoginInfoByUserID(tx *mysql_base.Tx, token string, userID uint64, platform common.Platform) (r metadata.IOperator, err error) {
	var (
		bizUnit          = &biz_unit2.BizUnit{}
		user, tenantUser mysql.User
		department       departmentPb.Department
		saleSystem       sale_system_pb.Res
		subDepartments   departmentPb.DepartmentList // 子部门
		userRoleRels     mysql.UserRoleRelList       // 用户角色关联表
		departmentSvc    = departmentPb.NewDepartmentClient()
		saleSystemSvc    = sale_system_pb.NewSaleSystemClient()
		ctx              = context.Background()
	)

	if tx == nil {
		var info WashSystemUser
		ctx = context.WithValue(context.Background(), metadata.LoginInfo, info)
		tx = mysql_base.TransactionSlaveEx(nil, ctx, true)
	}

	user, err = mysql.MustFirstValidateUser(tx, userID)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, metadata.UserId, strconv.FormatUint(user.Id, 10))

	if user.BizUnitID != 0 {
		var repo = biz_unit.NewRepo(tx)
		bizUnit, err = repo.QueryBizUnit(ctx, user.BizUnitID)
		if err != nil {
			return
		}
	}

	loginLog, _, err := mysql.FirstLoginLogByUserID(tx, user.Id)
	if err != nil {
		return
	}

	operator := &LoginInfo{}
	operator.User = user
	operator.BizUnit = *bizUnit
	operator.Platform = platform
	operator.Token = token
	operator.LoginTime = loginLog.LoginTime
	// 获取子部门
	subDepartments, err = departmentSvc.GetSubDepartmentListById(ctx, user.DepartmentID)
	if err == nil {
		operator.SubDepartments = subDepartments
	}

	// 根据是否有缓存
	var (
		tenantManagementID uint64
	)
	tenantManagementID, _, err = redis.GetUint64ValueByKey(ctx, token)
	if err != nil {
		return
	}

	if tenantManagementID != 0 {
		var (
			_ctx             = context.Background()
			tenantManagement tenant_mysql.TenantManagement
			tobeDevelopedApp qywx.TobeDevelopedApp
			tenantUserExist  bool
			rel              qywx.TobeDevelopedAppRel
		)
		tenantManagement, err = tenantManagementDao.NewTenantManagementDao().MustFirst(ctx, tx, tenantManagementID)
		if err != nil {
			return
		}
		// zqx todo: 这里只考虑了账套和应用一对一绑定关系，如果后期账套和应用一对多绑定关系，这里需要修改
		rel, _, err = qywxDao.FirstTobeDevelopedAppRelByTenantManagementID(tx, tenantManagementID)
		if err != nil {
			return
		}
		tobeDevelopedApp, _, err = qywxDao.FirstTobeDevelopedAppByID(tx, rel.TobeDevelopedAppID)
		if err != nil {
			return
		}
		operator.SetTenantManagement(tenantManagement.Id, tobeDevelopedApp.CorpID, tobeDevelopedApp.Endpoint, tobeDevelopedApp.RobotCode, tenantManagement.TenantPhoneNumber, tenantManagement.TenantContacts, tenantManagement.TenantCompanyName, tenantManagement.DatabaseName, tenantManagement.ActivationTime, tenantManagement.ConfigJson, tenantManagement.AssignPort)

		_ctx = metadata.SetMDToIncoming(_ctx, metadata.TenantManagementId, strconv.FormatUint(tenantManagementID, 10))
		_ctx = metadata.SetMDToIncoming(_ctx, metadata.TenantManagementDbName, tenantManagement.DatabaseName)
		_ctx = metadata.SetMDToIncoming(_ctx, metadata.UserId, strconv.FormatUint(user.Id, 10))
		_ctx = context.WithValue(_ctx, metadata.LoginInfo, operator)
		_tx := mysql_base.TransactionSlaveEx(nil, _ctx, true)

		tenantUser, tenantUserExist, err = mysql.FirstUserByPhone(_tx, user.Phone)
		if err != nil {
			return
		}
		if !tenantUserExist {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "该用户不存在"))
			return
		}

		department, err = departmentSvc.GetDepartmentById(_ctx, tenantUser.DepartmentID)
		if err != nil {
			return
		}

		saleSystem, err = saleSystemSvc.GetSaleSystemByQuery(_ctx, sale_system_pb.Req{Id: tenantUser.DefaultSaleSystemID})
		if err != nil {
			return
		}

		// 通过用户id,获取用户角色关联
		userRoleRels, err = mysql.FindUserRoleRelByUserID(_tx, tenantUser.Id, []uint64{})
		// 遍历，得到每个用户所属角色的权限
		operator.GetAccess(_ctx, _tx, userRoleRels, tenantUser.Id)

		// 获取租户信息
		operator.TenantManagement = TenantManagement{
			Id:                        tenantManagement.Id,
			TenantPhoneNumber:         tenantManagement.TenantPhoneNumber,
			TenantContacts:            tenantManagement.TenantContacts,
			TenantCompanyName:         tenantManagement.TenantCompanyName,
			DatabaseName:              tenantManagement.DatabaseName,
			ActivationTime:            tenantManagement.ActivationTime,
			ConfigJson:                tenantManagement.ConfigJson,
			AssignPort:                tenantManagement.AssignPort,
			CorpID:                    tobeDevelopedApp.CorpID,
			TobeDevelopedAppEndpoint:  tobeDevelopedApp.Endpoint,
			TobeDevelopedAppRobotCode: tobeDevelopedApp.RobotCode,
		}
		operator.User = tenantUser
	} else {
		department, err = departmentSvc.GetDepartmentById(ctx, user.DepartmentID)
		if err != nil {
			return
		}

		saleSystem, err = saleSystemSvc.GetSaleSystemByQuery(ctx, sale_system_pb.Req{Id: user.DefaultSaleSystemID})
		if err != nil {
			return
		}
		// 通过用户id,获取用户角色关联
		userRoleRels, err = mysql.FindUserRoleRelByUserID(tx, user.Id, []uint64{})
		// 遍历，得到每个用户所属角色的权限
		operator.GetAccess(ctx, tx, userRoleRels, userID)
	}
	operator.TenantManagementID = tenantManagementID
	operator.SaleSystem = saleSystem
	operator.Department = department
	r = operator
	// 登录态缓存
	module, err := json.Marshal(operator)
	if err != nil {
		return
	}
	redis.AddUserInfo(ctx, userID, platform, module)
	return

}

func (info *LoginInfo) GetAccess(ctx context.Context, tx *mysql_base.Tx, userRoleRels mysql.UserRoleRelList, userID uint64) {
	var (
		roleAccessList               mysql.RoleAccessList // 角色权限列表
		roleAccess                   mysql.RoleAccess     // 角色权限
		user                         mysql.User           // 用户
		exist                        bool                 // 角色权限是否存在
		resourceRouterNames          []string
		buttonCodes                  []string
		mpResourceRouterNames        []string
		mpButtonCodes                []string
		dataAccessScopes             []string
		mpDataAccessScopes           []string
		disableResourceRouterNames   []string
		disableButtonCodes           []string
		disableMpResourceRouterNames []string
		disableMpButtonCodes         []string
		disableDataAccessScopes      []string
		disableMpDataAccessScopes    []string
		menuIDs                      []uint64
		disableMenuIDs               []uint64
		err                          error
	)
	for _, rel := range userRoleRels {
		roleAccess, exist, err = mysql.FirstRoleAccessByRoleID(tx, rel.RoleID)
		if err != nil {
			return
		}
		if exist {
			roleAccessList = append(roleAccessList, roleAccess)
			if roleAccess.MenuIDs != "" {
				menuTempIDs, _ := tools.StringArr2UInt64Arr(strings.Split(roleAccess.MenuIDs, ","))
				menuIDs = append(menuIDs, menuTempIDs...)
			}
			resourceRouterNames = append(resourceRouterNames, strings.Split(roleAccess.ResourceRouterNames, ",")...)
			buttonCodes = append(buttonCodes, strings.Split(roleAccess.ButtonCodes, ",")...)
			mpResourceRouterNames = append(mpResourceRouterNames, strings.Split(roleAccess.MPResourceRouterNames, ",")...)
			mpButtonCodes = append(mpButtonCodes, strings.Split(roleAccess.MPButtonCodes, ",")...)
			dataAccessScopes = append(dataAccessScopes, strings.Split(roleAccess.DataAccessScope, ",")...)
			mpDataAccessScopes = append(mpDataAccessScopes, strings.Split(roleAccess.MPDataAccessScope, ",")...)
		}
	}

	roleAccess, exist, err = mysql.FirstRoleAccessByUserID(tx, userID, common.StatusEnable)
	if err != nil {
		return
	}
	if exist {
		roleAccessList = append(roleAccessList, roleAccess)
		if roleAccess.MenuIDs != "" {
			menuTempIDs, _ := tools.StringArr2UInt64Arr(strings.Split(roleAccess.MenuIDs, ","))
			menuIDs = append(menuIDs, menuTempIDs...)
		}
		resourceRouterNames = append(resourceRouterNames, strings.Split(roleAccess.ResourceRouterNames, ",")...)
		buttonCodes = append(buttonCodes, strings.Split(roleAccess.ButtonCodes, ",")...)
		mpResourceRouterNames = append(mpResourceRouterNames, strings.Split(roleAccess.MPResourceRouterNames, ",")...)
		mpButtonCodes = append(mpButtonCodes, strings.Split(roleAccess.MPButtonCodes, ",")...)
		dataAccessScopes = append(dataAccessScopes, strings.Split(roleAccess.DataAccessScope, ",")...)
		mpDataAccessScopes = append(mpDataAccessScopes, strings.Split(roleAccess.MPDataAccessScope, ",")...)
	}

	roleAccess, exist, err = mysql.FirstRoleAccessByUserID(tx, userID, common.StatusDisable)
	if err != nil {
		return
	}
	if exist {
		roleAccessList = append(roleAccessList, roleAccess)
		if roleAccess.MenuIDs != "" {
			disableMenuIDs, _ = tools.StringArr2UInt64Arr(strings.Split(roleAccess.MenuIDs, ","))
		}
		disableResourceRouterNames = append(disableResourceRouterNames, strings.Split(roleAccess.ResourceRouterNames, ",")...)
		disableButtonCodes = append(disableButtonCodes, strings.Split(roleAccess.ButtonCodes, ",")...)
		disableMpResourceRouterNames = append(disableMpResourceRouterNames, strings.Split(roleAccess.MPResourceRouterNames, ",")...)
		disableMpButtonCodes = append(disableMpButtonCodes, strings.Split(roleAccess.MPButtonCodes, ",")...)
		disableDataAccessScopes = append(disableDataAccessScopes, strings.Split(roleAccess.DataAccessScope, ",")...)
		disableMpDataAccessScopes = append(disableMpDataAccessScopes, strings.Split(roleAccess.MPDataAccessScope, ",")...)
	}

	// 获取仓库权限
	user, err = mysql.MustFirstUser(tx, userID)
	if err != nil {
		return
	}

	// 获取所属营销体系
	employeeSvc := employee.NewClientEmployeeService()
	saleSystemIDs, err := employeeSvc.QueryEmployeeSaleSystem(ctx, tx, user.EmployeeID)
	if err != nil {
		return
	}

	// 用户为外部员工且没有绑定员工，查出所有营销体系
	if user.IsOutsideEmployee && user.EmployeeID == 0 {
		saleSystemIDs, err = sale_system_pb.NewSaleSystemClient().GetAllSaleSystemIds(ctx, 0)
		if err != nil {
			return
		}
	}

	// 用户所属角色权限列表
	info.ResourceRouterNames, _, _, _ = tools.CheckStringSet(tools.StringDistinctAndFilterZero(resourceRouterNames), disableResourceRouterNames)
	info.ButtonCodes, _, _, _ = tools.CheckStringSet(tools.StringDistinctAndFilterZero(buttonCodes), disableButtonCodes)
	info.MPResourceRouterNames, _, _, _ = tools.CheckStringSet(tools.StringDistinctAndFilterZero(mpResourceRouterNames), disableMpResourceRouterNames)
	info.MPButtonCodes, _, _, _ = tools.CheckStringSet(tools.StringDistinctAndFilterZero(mpButtonCodes), disableMpButtonCodes)
	info.MenuIDs, _, _, _ = tools.CheckUint64Set(tools.UInt64DistinctAndFilterZero(menuIDs), disableMenuIDs)
	info.WarehouseIDs = user.WarehouseRoleAccess.ToInt()
	info.SaleSystemIDs = saleSystemIDs
	info.BizUnitIDs = user.CompanyRoleAccess.ToInt()
	info.AllowUpdateOrder = user.AllowUpdateOrder
	info.AllowCancelOther = user.AllowCancelOther
	info.AllowAuditSelf = user.AllowAuditSelf
	info.AccessList = append(info.AccessList, Access{AccessScope: user.AccessScope, AccessScopeOtherSelect: user.AccessScopeOtherSelect})
	info.DataAccessScope, _, _, _ = tools.CheckStringSet(tools.StringDistinctAndFilterZero(dataAccessScopes), disableDataAccessScopes)
	info.MPDataAccessScope, _, _, _ = tools.CheckStringSet(tools.StringDistinctAndFilterZero(mpDataAccessScopes), disableMpDataAccessScopes)
	info.RoleAccess = roleAccessList

	err = redis.SetMenuIDsForRealTime(ctx, metadata.GetUserId(ctx), info.MenuIDs...)
	if err != nil {
		return
	}
	return
}

func GetAdminUserByToken(ctx context.Context, tx *mysql_base.Tx, token string) (user mysql.User, err error) {
	var (
		userID uint64
		exist  bool
	)
	userID, exist, err = redis.GetAdminUserByToken(ctx, token)
	if err != nil {
		return
	}
	if !exist {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize), token)
		return
	}
	user, err = mysql.MustFirstValidateUser(tx, userID)
	return
}

type WashSystemUser struct {
	TenantManagement   TenantManagement `json:"tenant_management"`    // 登录的租户
	TenantManagementId uint64           `json:"tenant_management_id"` // 登录的租户id
	BizUnitId          uint64
	UserName           string
	UserLoginType      common.UserLoginType
}

func (w WashSystemUser) GetTenantManagementName() string {
	return w.TenantManagement.TenantCompanyName
}

func (w WashSystemUser) GetTobeDevelopedAppEndpoint() string {
	return w.TenantManagement.TobeDevelopedAppEndpoint
}

func (w WashSystemUser) GetTobeDevelopedAppRobotCode() string {
	return w.TenantManagement.TobeDevelopedAppRobotCode
}

func (w WashSystemUser) GetCorpID() string {
	return w.TenantManagement.CorpID
}

func (w *WashSystemUser) SetTenantManagement(Id uint64, corpID, tobeDevelopedAppEndpoint, tobeDevelopedAppRobotCode, TenantPhoneNumber string, TenantContacts string, TenantCompanyName string, DatabaseName string, ActivationTime time.Time, ConfigJson string, AssignPort int) {
	w.TenantManagement = TenantManagement{
		Id:                        Id,
		TenantPhoneNumber:         TenantPhoneNumber,
		TenantContacts:            TenantContacts,
		TenantCompanyName:         TenantCompanyName,
		DatabaseName:              DatabaseName,
		ActivationTime:            ActivationTime,
		ConfigJson:                ConfigJson,
		AssignPort:                AssignPort,
		CorpID:                    corpID,
		TobeDevelopedAppEndpoint:  tobeDevelopedAppEndpoint,
		TobeDevelopedAppRobotCode: tobeDevelopedAppRobotCode,
	}
	w.TenantManagementId = Id
}

func (w WashSystemUser) GetDataAccessScope() []byte {
	return make([]byte, 0)
}

func (w WashSystemUser) GetEmpId() uint64 {
	return 0
}

func (w WashSystemUser) GetDefaultSaleSystemId() uint64 {
	return vars.DefaultSaleSystemID
}

func (w WashSystemUser) GetWarehouseIds() (r []uint64) {
	return []uint64{}
}

func (w WashSystemUser) GetSaleSystemIds() (r []uint64) {
	return []uint64{}
}

func (w WashSystemUser) GetBizUnitIds() (r []uint64) {
	return []uint64{}
}

func (w WashSystemUser) IsAllowUpdateOrder() (r bool) {
	return true
}

func (w WashSystemUser) IsAllowCancelOther() (r bool) {
	return true
}

func (w WashSystemUser) IsAllowAuditSelf() (r bool) {
	return true
}

func (w WashSystemUser) GetMenuIds() (r []uint64) {
	return []uint64{}
}

func (w WashSystemUser) GetResourceRouterNames() (r []string) {
	return []string{}
}

func (w WashSystemUser) GetButtonCodes() (r []string) {
	return []string{}
}

func (w WashSystemUser) GetMPResourceRouterNames() (r []string) {
	return []string{}
}

func (w WashSystemUser) GetMPButtonCodes() (r []string) {
	return []string{}
}

func (w WashSystemUser) GetDataSeparate() (data []string) {
	return vars.DataMaskingDefault
}

func (w WashSystemUser) GetMPDataSeparate() (data []string) {
	return vars.DataMaskingDefault
}

func (w WashSystemUser) GetUserId() uint64 {
	return vars.AdminUserID
}

func (w WashSystemUser) GetUserName() string {
	if w.UserName == "" {
		return "数据导入"
	}
	return w.UserName
}

func (w WashSystemUser) GetBizUnitId() uint64 {
	return w.BizUnitId
}

func (w WashSystemUser) GetDepartmentId() uint64 {
	return vars.DefaultDepartmentID
}

func (w WashSystemUser) IsSaasCompany() bool {
	return true
}

func (w WashSystemUser) IsAvailable() bool {
	return true
}

func (w WashSystemUser) GetToken() string {
	return ""
}

func (w WashSystemUser) GetUserType() common.UserType {
	return common.UserTypeAdminRegister
}

func (w WashSystemUser) GetSubDepartmentId() []uint64 {
	return []uint64{}
}
func (w WashSystemUser) GetRoleAccessIds() []uint64 {
	return []uint64{}
}
func (w WashSystemUser) GetPlatform() common.Platform {
	return 1
}
func (w WashSystemUser) GetLoginTime() time.Time {
	return time.Now()
}

func (info WashSystemUser) GetTenantManagementDbName() string {
	return info.TenantManagement.DatabaseName
}

func (info WashSystemUser) GetTenantManagementId() uint64 {
	return info.TenantManagementId
}

// 获取用户登录类型
func (info WashSystemUser) GetUserLoginType() common.UserLoginType {
	return info.UserLoginType
}
