package system

import (
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/middleware"
	"hcscm/tools"
)

// 登录登出
type AdminLoginParam struct {
	Param
	JsCode             string `json:"js_code"` // jsCode
	Code               string `json:"code"`    // code
	Phone              string `json:"phone"`
	Password           string `json:"password"`
	TenantManagementId uint64 `json:"tenant_management_id"` // 选择要登录的租户id
	EncryptedStr       string `json:"encrypted_str"`
}

type AdminLoginData struct {
	ResponseData
	Token                        string `json:"token"`                           // 令牌
	UserID                       uint64 `json:"user_id"`                         // 用户ID
	UserName                     string `json:"user_name"`                       // 员工名称
	DefaultSaleSystemID          uint64 `json:"default_sale_system_id"`          // 默认营销体系ID
	DefaultSaleSystemName        string `json:"default_sale_system_name"`        // 默认营销体系名称
	DefaultPhysicalWarehouseID   uint64 `json:"default_physical_warehouse_id"`   // 默认仓库ID
	DefaultPhysicalWarehouseName string `json:"default_physical_warehouse_name"` // 默认仓库名称
	TenantManagementID           uint64 `json:"tenant_management_id"`            // 租户ID
	ApiDomain                    string `json:"api_domain"`                      // api域名
	ApiPrefix                    string `json:"api_prefix"`                      // api前缀
	IsSearchImage                bool   `json:"is_search_image"`                 // 是否可以搜索图片(电子色卡用)
}

// 手机验证码登录
type PhoneCodeLoginParam struct {
	Param
	Phone          string `json:"phone"`           // 手机号码
	IsValid        bool   `json:"is_valid"`        // 是否用于判断
	Code           string `json:"code"`            // 验证码
	OldPassword    string `json:"old_password"`    // 旧密码
	Password       string `json:"password"`        // 新密码
	RepeatPassword string `json:"repeat_password"` // 确认新密码
}

type GetLoginInformationData struct {
	ResponseData
	UserID                uint64             `json:"user_id"`                  // 用户ID
	UserName              string             `json:"user_name"`                // 用户名
	DefaultSaleSystemID   uint64             `json:"default_sale_system_id"`   // 默认营销体系ID
	DefaultSaleSystemName string             `json:"default_sale_system_name"` // 默认营销体系名称
	DefaultCustomerID     uint64             `json:"default_customer_id"`      // 默认所属客户ID
	DefaultCustomerName   string             `json:"default_customer_name"`    // 默认所属客户名称
	ResourceRouterNames   []string           `json:"resource_router_names"`    // 资源路由名称
	ButtonCodes           []string           `json:"button_codes"`             // 按钮名称
	DataAccessScope       []string           `json:"data_access_scope"`        // 数据脱敏权限(菜单/路由)
	KeyId                 string             `json:"key_id"`                   // key_id
	LeaderMenuList        LeaderMenuDataList `json:"leader_menu_list"`
	RoleID                []uint64           `json:"role_id"`        // 角色ID
	Phone                 string             `json:"phone"`          // 电话
	AvatarURL             string             `json:"avatar_url"`     // 头像
	EmployeeID            uint64             `json:"employee_id"`    // 员工id
	Deadline              tools.MyTime       `json:"deadline"`       // 过期时间
	FollowUserId          string             `json:"follow_user_id"` // 企业微信用户id
}

type MPGetLoginInformationData struct {
	ResponseData
	UserID                   uint64       `json:"user_id"`                    // 用户ID
	UserName                 string       `json:"user_name"`                  // 用户名
	DefaultSaleSystemID      uint64       `json:"default_sale_system_id"`     // 默认营销体系ID
	DefaultSaleSystemName    string       `json:"default_sale_system_name"`   // 默认营销体系名称
	MPResourceRouterNames    []string     `json:"mp_resource_router_names"`   // 资源路由名称
	MPButtonCodes            []string     `json:"mp_button_codes"`            // 按钮名称
	MPDataAccessScope        []string     `json:"mp_data_access_scope"`       // 内部商城数据脱敏权限(菜单/路由)
	RoleID                   []uint64     `json:"role_id"`                    // 角色ID
	Phone                    string       `json:"phone"`                      // 电话
	AvatarURL                string       `json:"avatar_url"`                 // 头像
	EmployeeID               uint64       `json:"employee_id"`                // 员工id
	EmployeeCode             string       `json:"employee_code"`              // 员工工号
	EmployeeName             string       `json:"employee_name"`              // 员工名称
	CompanyID                uint64       `json:"company_id"`                 // Id
	CompanyName              string       `json:"company_name"`               // 公司名称
	TenantManagementID       uint64       `json:"tenant_management_id"`       // 租户ID
	TenantManagementDeadline tools.MyTime `json:"tenant_management_deadline"` // 过期时间
	TenantOcrDeadline        tools.MyTime `json:"tenant_ocr_deadline"`        // ocr识别过期时间
	IsFirstLogin             bool         `json:"is_first_login"`             // 是否是首次登录
	FollowUserId             string       `json:"follow_user_id"`             // 企业微信用户id
}

// 导航栏菜单列表
type LeaderMenu struct {
	Id                 uint64       `json:"id"`
	Name               string       `json:"name"`                 // 菜单名称
	AvatarUrl          string       `json:"avatar_url"`           // 图标
	ResourceID         uint64       `json:"resource_id"`          // 资源ID
	ResourceName       string       `json:"resource_name"`        // 资源名称
	ResourceRouterName string       `json:"resource_router_name"` // 资源路由名称
	ParentID           uint64       `json:"parent_id"`            // 父级菜单ID
	SubMenu            []LeaderMenu `json:"sub_menu"`
}

type LeaderMenuDataList []LeaderMenu

func (r LeaderMenuDataList) Adjust() {

}

// 用户管理
type AddUserParam struct {
	Param
	Account  string `json:"account"`  // 用户名账号
	Phone    string `json:"phone"`    // 手机号码
	Password string `json:"password"` // 登录密码
}

type AddUserData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type UpdateUserParam struct {
	Param
	Id                         uint64                            `json:"id" mean:"id" validates:"numberMin=1"`
	Account                    string                            `json:"account"`                             // 用户名账号
	Status                     common_system.Status              `json:"status"`                              // 状态
	Phone                      string                            `json:"phone"`                               // 手机号码
	Email                      string                            `json:"email"`                               // 邮箱
	EmployeeID                 uint64                            `json:"employee_id"`                         // 员工ID(关联EmployeeID)
	EmployeeName               string                            `json:"employee_name"`                       // 员工名称
	DepartmentID               uint64                            `json:"department_id"`                       // 部门ID
	DepartmentName             string                            `json:"department_name"`                     // 部门名称
	DefaultPhysicalWarehouseID uint64                            `json:"default_physical_warehouse_id"`       // 默认仓库ID
	DefaultSaleSystemID        uint64                            `json:"default_sale_system_id"`              // 默认营销体系ID
	RoleID                     []uint64                          `json:"role_id"`                             // 角色ID
	AccessScope                common_system.RoleAccessDataScope `json:"access_scope,omitempty"`              // 数据权限范围
	AccessScopeOtherSelect     []uint64                          `json:"access_scope_other_select,omitempty"` // 【其他】时选择的数据范围
	WarehouseRoleAccess        []uint64                          `json:"warehouse_role_access"`               // 仓库权限
	CompanyRoleAccess          []uint64                          `json:"company_role_access"`                 // 往来单位权限
	AllowUpdateOrder           bool                              `json:"allow_update_order"`                  // 允许修改别人单据
	AllowCancelOther           bool                              `json:"allow_cancel_other"`                  // 允许作废别人单据
	AllowAuditSelf             bool                              `json:"allow_audit_self"`                    // 允许审核自己单据
	AdminDisableRoleAccess     UserAccess                        `json:"admin_disable_role_access"`           // 后台禁用权限
	MpDisablesRoleAccess       MPUserAccess                      `json:"mp_disable_role_access"`              // 小程序禁用权限
	AdminEnableRoleAccess      UserAccess                        `json:"admin_enable_role_access"`            // 后台新增权限
	MpEnableRoleAccess         MPUserAccess                      `json:"mp_enable_role_access"`               // 小程序新增权限
	IsOutsideEmployee          bool                              `json:"is_outside_employee"`                 // 是否为外部员工
}

type UpdateUserData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type UpdateUserPasswordParam struct {
	Param
	UserID   uint64 `json:"user_id"`                          // 用户ID
	Password string `json:"password" validates:"lengthMin=6"` // 密码
}

type UserUpdateUserPasswordParam struct {
	Param
	SourcePassword string `json:"source_password" validates:"lengthMin=6"` // 旧密码
	Password       string `json:"password" validates:"lengthMin=6"`        // 新密码
	RepeatPassword string `json:"repeat_password" validates:"lengthMin=6"` // 确认新密码
}

func (q UserUpdateUserPasswordParam) Validate() (err error) {
	if q.RepeatPassword == "" {
		// err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，确认密码为空，请重试！"))
		return
	}
	if q.RepeatPassword != q.Password {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，两次密码不相同，请重试！"))
		return
	}
	return
}

type UpdateResetUserPasswordParam struct {
	Param
	UserID []uint64 `json:"user_id" relate:"user_id"` // 用户ID
}

type GetUserQuery struct {
	Query
	Id uint64 `form:"id"`
}

type GetUserListQuery struct {
	ListQuery
	Account     string               `form:"account"` // 用户名
	Name        string               `form:"name"`    // 名称
	Phone       string               `form:"phone"`   // 手机号码
	Status      common_system.Status `form:"status"`  // 状态
	EmployeeIds []uint64
}

type GetUserRole struct {
	Id   uint64 `json:"id"`
	Name string `json:"name"`
}

type GetUserData struct {
	RecordData
	Account                       string                            `json:"account"`                             // 账号
	Status                        common_system.Status              `json:"status"`                              // 账号状态
	StatusName                    string                            `json:"status_name"`                         // 账号状态名称
	Phone                         string                            `json:"phone"`                               // 手机号码
	Email                         string                            `json:"email"`                               // 邮箱
	WechatOpenUserID              uint64                            `json:"wechat_open_user_id"`                 // 微信用户表ID
	IsOutsideEmployee             bool                              `json:"is_outside_employee"`                 // 是否为外部员工
	EmployeeID                    uint64                            `json:"employee_id"`                         // 员工ID
	EmployeeName                  string                            `json:"employee_name"`                       // 员工姓名
	DepartmentID                  uint64                            `json:"department_id"`                       // 用户所属部门
	DepartmentName                string                            `json:"department_name"`                     // 用户所属部门名称
	DefaultPhysicalWarehouseID    uint64                            `json:"default_physical_warehouse_id"`       // 默认仓库ID
	DefaultPhysicalWarehouseName  string                            `json:"default_physical_warehouse_name"`     // 默认仓库名称
	DefaultSaleSystemID           uint64                            `json:"default_sale_system_id"`              // 默认营销体系ID
	DefaultSaleSystemName         string                            `json:"default_sale_system_name"`            // 默认营销体系名称
	RoleID                        []uint64                          `json:"role_id"`                             // 角色ID
	RoleList                      []GetUserRole                     `json:"role_list"`                           // 角色列表
	AccessScope                   common_system.RoleAccessDataScope `json:"access_scope,omitempty"`              // 数据权限范围
	AccessScopeName               string                            `json:"access_scope_name"`                   // 数据权限范围
	AccessScopeOtherSelect        []uint64                          `json:"access_scope_other_select,omitempty"` // 【其他】时选择的数据范围
	WarehouseRoleAccess           WarehouseRoleAccessList           `json:"warehouse_role_access"`               // 仓库权限
	AllowUpdateOrder              bool                              `json:"allow_update_order"`                  // 允许修改别人单据
	AllowCancelOther              bool                              `json:"allow_cancel_other"`                  // 允许作废别人单据
	AllowAuditSelf                bool                              `json:"allow_audit_self"`                    // 允许审核自己单据
	CompanyRoleAccess             CompanyRoleAccessList             `json:"company_role_access"`                 // 往来单位权限
	RoleAccess                    RoleAccess                        `json:"role_access"`
	MPRoleAccess                  MPRoleAccess                      `json:"mp_role_access"`
	AdminDisableRoleAccess        UserAccess                        `json:"admin_disable_role_access"`         // 后台禁用权限
	MpDisablesRoleAccess          MPUserAccess                      `json:"mp_disable_role_access"`            // 小程序禁用权限
	AdminEnableRoleAccess         UserAccess                        `json:"admin_enable_role_access"`          // 后台新增权限
	MpEnableRoleAccess            MPUserAccess                      `json:"mp_enable_role_access"`             // 小程序新增权限
	EnableButtonCodeList          []string                          `json:"enable_button_code_list"`           // 按钮名称(前端key)
	EnableResourceRouterNameList  []string                          `json:"enable_resource_router_name_list"`  // 资源路由名称(前端key)
	EnableResourceID              []uint64                          `json:"enable_resource_id"`                // 资源路由ID
	DisableButtonCodeList         []string                          `json:"disable_button_code_list"`          // 按钮名称(前端key)
	DisableResourceRouterNameList []string                          `json:"disable_resource_router_name_list"` // 资源路由名称(前端key)
	DisableResourceID             []uint64                          `json:"disable_resource_id"`               // 资源路由ID
	BizUnitID                     uint64                            `json:"biz_unit_id"`                       // 往来单位ID
	BizUnitName                   string                            `json:"biz_unit_name"`                     // 往来名称
	Remark                        string                            `json:"remark"`                            // 备注
}

type GetUserDataList []GetUserData

func (l GetUserDataList) Adjust() {

}

type UpdateUserStatusParam struct {
	Param
	Id     tools.QueryIntList   `json:"id"`
	Status common_system.Status `json:"status"` // 状态
}

type UpdateUserStatusData struct {
	ResponseData
}

type GetSaleUserListQuery struct {
	ListQuery
}

type GetUserNameListQuery struct {
	ListQuery
	Name string `form:"name"`
}

type GetUserDropdownListQuery struct {
	ListQuery
	Id          uint64   `form:"id"`
	Name        string   `form:"name"`  // 名称
	Phone       string   `form:"phone"` // 联系电话
	Duty        uint64   `form:"duty"`  // 职责
	EmployeeIDs []uint64 `form:"-"`     // 员工ID，职责查询时使用
}

func (r GetUserDropdownListQuery) Adjust() {
}

type GetUserDropdownData struct {
	RecordData
	Name  string `json:"name"`  // 名称
	Phone string `json:"phone"` // 联系电话
}

type GetUserDropdownDataList []GetUserDropdownData

func (g GetUserDropdownDataList) Adjust() {

}

type GetUserWithoutRoleAccessData struct {
	RecordData
	Account                      string `json:"account"`                         // 账号
	Phone                        string `json:"phone"`                           // 手机号码
	Email                        string `json:"email"`                           // 邮箱
	WechatOpenUserID             uint64 `json:"wechat_open_user_id"`             // 微信用户表ID
	EmployeeID                   uint64 `json:"employee_id"`                     // 员工ID
	EmployeeName                 string `json:"employee_name"`                   // 员工姓名
	DepartmentID                 uint64 `json:"department_id"`                   // 用户所属部门
	DepartmentName               string `json:"department_name"`                 // 用户所属部门名称
	DefaultPhysicalWarehouseID   uint64 `json:"default_physical_warehouse_id"`   // 默认仓库ID
	DefaultPhysicalWarehouseName string `json:"default_physical_warehouse_name"` // 默认仓库名称
	DefaultSaleSystemID          uint64 `json:"default_sale_system_id"`          // 默认营销体系ID
	DefaultSaleSystemName        string `json:"default_sale_system_name"`        // 默认营销体系名称
	Remark                       string `json:"remark"`                          // 备注
}

type SendModifyPasswordVerificationCodeParam struct {
	Param
	Phone string `json:"phone"` // 手机号码
}

type LoginOutData struct {
	ResponseData
	ApiDomain string `json:"api_domain"` // api域名
	ApiPrefix string `json:"api_prefix"` // api前缀
}

type GetUserLocationQuery struct {
	Query
	Lat          float64 `form:"lat"`           // 纬度
	Lng          float64 `form:"lng"`           // 经度
	LocationType string  `form:"location_type"` // 坐标系类型 wgs84 gcj02
}

// GetUserLocationResponse 用户地理位置信息返回结构
type GetUserLocationResponse struct {
	ResponseData
	TencentGeocodeReverseResult
}

type TencentTranslateResult struct {
	Status    int    `json:"status"`
	Message   string `json:"message"`
	Locations []struct {
		Lat float64 `json:"lat"` // 纬度
		Lng float64 `json:"lng"` // 经度
	} `json:"locations"` // 坐标
}

type TencentIpLocResult struct {
	Status  int    `json:"status"`
	Message string `json:"message"`
	Result  struct {
		Location struct {
			Lat float64 `json:"lat"`
			Lng float64 `json:"lng"`
		} `json:"location"`
	} `json:"result"`
}

type TencentGeocodeResult struct {
	Status  int                         `json:"status"`
	Message string                      `json:"message"`
	Result  TencentGeocodeReverseResult `json:"result"`
}

type TencentGeocodeReverseResult struct {
	Address            string `json:"address"` // 以行政区划+道路+门牌号等信息组成的标准格式化地址
	FormattedAddresses struct {
		Recommend       string `json:"recommend"`        // 推荐使用的地址描述，描述精确性较高
		Rough           string `json:"rough"`            // 粗略位置描述
		StandardAddress string `json:"standard_address"` // 基于附近关键地点（POI）的精确地址
	} `json:"formatted_addresses,omitempty"` // 结合知名地点形成的描述性地址，更具人性化特点
	AddressComponent struct {
		Nation       string `json:"nation"`        // 国家
		Province     string `json:"province"`      // 省
		City         string `json:"city"`          // 市
		District     string `json:"district"`      // 区，可能为空字串
		Street       string `json:"street"`        // 道路，可能为空字串
		StreetNumber string `json:"street_number"` // 门牌，可能为空字串
	} `json:"address_component,omitempty"` // 地址部件，address不满足需求时可自行拼接
	AdInfo struct {
		NationCode    string `json:"nation_code"`     // 国家代码（ISO3166标准3位数字码）
		Adcode        string `json:"adcode"`          // 行政区划代码
		CityCode      string `json:"city_code"`       // 城市代码，由国家码+行政区划代码（提出城市级别）组合而来，总共为9位
		PhoneAreaCode string `json:"phone_area_code"` // 城市电话区号
		Name          string `json:"name"`            // 行政区划名称
		Location      struct {
			Lat float64 `json:"lat"` // 纬度
			Lng float64 `json:"lng"` // 经度
		} `json:"location"` // 坐标
		Nation   string `json:"nation"`   // 国家
		Province string `json:"province"` // 省
		City     string `json:"city"`     // 市
		District string `json:"district"` // 区
	} `json:"ad_info,omitempty"` // 行政区划信息
	AddressReference struct {
		FamousArea   FamousAreaType `json:"famous_area,omitempty"`   // 知名区域，如商圈或人们普遍认为有较高知名度的区域
		BusinessArea FamousAreaType `json:"business_area,omitempty"` // 商圈
		Town         FamousAreaType `json:"town,omitempty"`          // 乡镇/街道（四级行政区划）
		LandmarkL1   FamousAreaType `json:"landmark_l1,omitempty"`   // 一级地标，可识别性较强、规模较大的地点、小区等
		LandmarkL2   FamousAreaType `json:"landmark_l2,omitempty"`   // 二级地标，较一级地标更为精确，规模更小
		Street       FamousAreaType `json:"street,omitempty"`        // 道路
	} `json:"address_reference,omitempty"` // 坐标相对位置参考
	PoiCount float64 `json:"poi_count,omitempty"` // 查询的周边poi的总数，仅在传入参数get_poi=1时返回
	PoiList  []struct {
		Id       string `json:"id"`       // 地点（POI）唯一标识
		Title    string `json:"title"`    // 名称
		Address  string `json:"address"`  // 地址
		Category string `json:"category"` // 地点分类信息
		Location struct {
			Lat float64 `json:"lat"` // 纬度
			Lng float64 `json:"lng"` // 经度
		} `json:"location"` // 坐标
		AdInfo struct {
			Adcode   string `json:"adcode"`   // 行政区划代码
			Province string `json:"province"` // 省
			City     string `json:"city"`     // 市
			District string `json:"district"` // 区
		} `json:"ad_info"` // 行政区划信息
		Distance float64 `json:"_distance"` // 此参考位置到输入坐标的直线距离
		DirDesc  string  `json:"_dir_desc"` // 此参考位置到输入坐标的方位关系，如：北、南、内
	} `json:"pois,omitempty"` // 周边地点（POI/AOI）列表，数组中每个子项为一个POI/AOI对象
}

type FamousAreaType struct {
	Id       string `json:"id"`    // 乡镇/街道唯一标识（行政区划代码adcode）
	Title    string `json:"title"` // 名称/标题
	Location struct {
		Lat float64 `json:"lat"` // 纬度
		Lng float64 `json:"lng"` // 经度
	} `json:"location"` // 坐标
	Distance float64 `json:"_distance"` // 此参考位置到输入坐标的直线距离
	DirDesc  string  `json:"_dir_desc"` // 此参考位置到输入坐标的方位关系，如：北、南、内
}

// 手机验证码登录
type ThirdPartyPhoneCodeLoginParam struct {
	Param
	Phone              string `json:"phone"`                // 手机号码
	Code               string `json:"code"`                 // 验证码
	EncryptedStr       string `json:"encrypted_str"`        // 租户加密字符串
	TenantManagementId uint64 `json:"tenant_management_id"` // 租户ID
}

type ThirdPartyLoginData struct {
	ResponseData
	Token              string `json:"token"`                // 令牌
	TenantManagementId uint64 `json:"tenant_management_id"` // 租户ID
	IsBizUnit          bool   `json:"is_biz_unit"`          // 是否是往来单位(客户使用往来单位id和名称显示)

	UserId      uint64 `json:"user_id"`       // 用户ID
	UserName    string `json:"user_name"`     // 员工名称
	BizUnitId   uint64 `json:"biz_unit_id"`   // 往来单位ID
	BizUnitName string `json:"biz_unit_name"` // 往来单位名称

	DefaultSaleSystemId          uint64 `json:"default_sale_system_id"`          // 默认营销体系ID
	DefaultSaleSystemName        string `json:"default_sale_system_name"`        // 默认营销体系名称
	DefaultPhysicalWarehouseId   uint64 `json:"default_physical_warehouse_id"`   // 默认仓库ID
	DefaultPhysicalWarehouseName string `json:"default_physical_warehouse_name"` // 默认仓库名称
	ApiDomain                    string `json:"api_domain"`                      // api域名
	ApiPrefix                    string `json:"api_prefix"`                      // api前缀
	IsSearchImage                bool   `json:"is_search_image"`                 // 是否可以搜索图片(电子色卡用)
}
