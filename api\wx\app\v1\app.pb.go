// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.28.2
// source: api/wx/app/app.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type SyncUserInfoRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TenantManagementId uint64                 `protobuf:"varint,1,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SyncUserInfoRequest) Reset() {
	*x = SyncUserInfoRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUserInfoRequest) ProtoMessage() {}

func (x *SyncUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUserInfoRequest.ProtoReflect.Descriptor instead.
func (*SyncUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{0}
}

func (x *SyncUserInfoRequest) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

type SyncUserInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncUserInfoReply) Reset() {
	*x = SyncUserInfoReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncUserInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncUserInfoReply) ProtoMessage() {}

func (x *SyncUserInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncUserInfoReply.ProtoReflect.Descriptor instead.
func (*SyncUserInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{1}
}

type SyncExternalContactRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TenantManagementId uint64                 `protobuf:"varint,1,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SyncExternalContactRequest) Reset() {
	*x = SyncExternalContactRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncExternalContactRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncExternalContactRequest) ProtoMessage() {}

func (x *SyncExternalContactRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncExternalContactRequest.ProtoReflect.Descriptor instead.
func (*SyncExternalContactRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{2}
}

func (x *SyncExternalContactRequest) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

type SyncExternalContactReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncExternalContactReply) Reset() {
	*x = SyncExternalContactReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncExternalContactReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncExternalContactReply) ProtoMessage() {}

func (x *SyncExternalContactReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncExternalContactReply.ProtoReflect.Descriptor instead.
func (*SyncExternalContactReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{3}
}

type SyncGroupChatRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TenantManagementId uint64                 `protobuf:"varint,1,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *SyncGroupChatRequest) Reset() {
	*x = SyncGroupChatRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncGroupChatRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncGroupChatRequest) ProtoMessage() {}

func (x *SyncGroupChatRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncGroupChatRequest.ProtoReflect.Descriptor instead.
func (*SyncGroupChatRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{4}
}

func (x *SyncGroupChatRequest) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

type SyncGroupChatReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SyncGroupChatReply) Reset() {
	*x = SyncGroupChatReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SyncGroupChatReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SyncGroupChatReply) ProtoMessage() {}

func (x *SyncGroupChatReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SyncGroupChatReply.ProtoReflect.Descriptor instead.
func (*SyncGroupChatReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{5}
}

type GetQYWXUsersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Page          int64                  `protobuf:"varint,2,opt,name=page,proto3" json:"page,omitempty"`
	Size          int64                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQYWXUsersRequest) Reset() {
	*x = GetQYWXUsersRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQYWXUsersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQYWXUsersRequest) ProtoMessage() {}

func (x *GetQYWXUsersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQYWXUsersRequest.ProtoReflect.Descriptor instead.
func (*GetQYWXUsersRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{6}
}

func (x *GetQYWXUsersRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetQYWXUsersRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetQYWXUsersRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type QYWXUser struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	UserId        string                 `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *QYWXUser) Reset() {
	*x = QYWXUser{}
	mi := &file_api_wx_app_app_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *QYWXUser) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*QYWXUser) ProtoMessage() {}

func (x *QYWXUser) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use QYWXUser.ProtoReflect.Descriptor instead.
func (*QYWXUser) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{7}
}

func (x *QYWXUser) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *QYWXUser) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetQYWXUsersReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=Total,proto3" json:"Total,omitempty"`
	Users         []*QYWXUser            `protobuf:"bytes,2,rep,name=Users,proto3" json:"Users,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetQYWXUsersReply) Reset() {
	*x = GetQYWXUsersReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetQYWXUsersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetQYWXUsersReply) ProtoMessage() {}

func (x *GetQYWXUsersReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetQYWXUsersReply.ProtoReflect.Descriptor instead.
func (*GetQYWXUsersReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{8}
}

func (x *GetQYWXUsersReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetQYWXUsersReply) GetUsers() []*QYWXUser {
	if x != nil {
		return x.Users
	}
	return nil
}

type GetUserInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          string                 `protobuf:"bytes,1,opt,name=code,proto3" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoRequest) Reset() {
	*x = GetUserInfoRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoRequest) ProtoMessage() {}

func (x *GetUserInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoRequest.ProtoReflect.Descriptor instead.
func (*GetUserInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{9}
}

func (x *GetUserInfoRequest) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

type GetUserInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	UserId        string                 `protobuf:"bytes,1,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserInfoReply) Reset() {
	*x = GetUserInfoReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserInfoReply) ProtoMessage() {}

func (x *GetUserInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserInfoReply.ProtoReflect.Descriptor instead.
func (*GetUserInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{10}
}

func (x *GetUserInfoReply) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

type GetCustomersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	FollowUserId  string                 `protobuf:"bytes,2,opt,name=follow_user_id,json=followUserId,proto3" json:"follow_user_id,omitempty"`
	Page          int64                  `protobuf:"varint,4,opt,name=page,proto3" json:"page,omitempty"`
	Size          int64                  `protobuf:"varint,5,opt,name=size,proto3" json:"size,omitempty"`
	BizUnitIds    []uint64               `protobuf:"varint,6,rep,packed,name=biz_unit_ids,json=bizUnitIds,proto3" json:"biz_unit_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomersRequest) Reset() {
	*x = GetCustomersRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersRequest) ProtoMessage() {}

func (x *GetCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersRequest.ProtoReflect.Descriptor instead.
func (*GetCustomersRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{11}
}

func (x *GetCustomersRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetCustomersRequest) GetFollowUserId() string {
	if x != nil {
		return x.FollowUserId
	}
	return ""
}

func (x *GetCustomersRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetCustomersRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *GetCustomersRequest) GetBizUnitIds() []uint64 {
	if x != nil {
		return x.BizUnitIds
	}
	return nil
}

type GetCustomersReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=Total,proto3" json:"Total,omitempty"`
	List          []*Customers           `protobuf:"bytes,2,rep,name=List,proto3" json:"List,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomersReply) Reset() {
	*x = GetCustomersReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersReply) ProtoMessage() {}

func (x *GetCustomersReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersReply.ProtoReflect.Descriptor instead.
func (*GetCustomersReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{12}
}

func (x *GetCustomersReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetCustomersReply) GetList() []*Customers {
	if x != nil {
		return x.List
	}
	return nil
}

type Customers struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Avatar        string                 `protobuf:"bytes,3,opt,name=avatar,proto3" json:"avatar,omitempty"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	CorpName      string                 `protobuf:"bytes,5,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	CorpFullName  string                 `protobuf:"bytes,6,opt,name=corp_full_name,json=corpFullName,proto3" json:"corp_full_name,omitempty"`
	Gender        string                 `protobuf:"bytes,7,opt,name=gender,proto3" json:"gender,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Customers) Reset() {
	*x = Customers{}
	mi := &file_api_wx_app_app_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Customers) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Customers) ProtoMessage() {}

func (x *Customers) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Customers.ProtoReflect.Descriptor instead.
func (*Customers) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{13}
}

func (x *Customers) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Customers) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Customers) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *Customers) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *Customers) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

func (x *Customers) GetCorpFullName() string {
	if x != nil {
		return x.CorpFullName
	}
	return ""
}

func (x *Customers) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

type GetGroupChatListRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Page          int64                  `protobuf:"varint,3,opt,name=page,proto3" json:"page,omitempty"`
	Size          int64                  `protobuf:"varint,4,opt,name=size,proto3" json:"size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupChatListRequest) Reset() {
	*x = GetGroupChatListRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupChatListRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupChatListRequest) ProtoMessage() {}

func (x *GetGroupChatListRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupChatListRequest.ProtoReflect.Descriptor instead.
func (*GetGroupChatListRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{14}
}

func (x *GetGroupChatListRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetGroupChatListRequest) GetPage() int64 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *GetGroupChatListRequest) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

type GetGroupChatListReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Total         int64                  `protobuf:"varint,1,opt,name=Total,proto3" json:"Total,omitempty"`
	List          []*GroupChat           `protobuf:"bytes,2,rep,name=List,proto3" json:"List,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupChatListReply) Reset() {
	*x = GetGroupChatListReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupChatListReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupChatListReply) ProtoMessage() {}

func (x *GetGroupChatListReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupChatListReply.ProtoReflect.Descriptor instead.
func (*GetGroupChatListReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{15}
}

func (x *GetGroupChatListReply) GetTotal() int64 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetGroupChatListReply) GetList() []*GroupChat {
	if x != nil {
		return x.List
	}
	return nil
}

type GroupChat struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Owner         string                 `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	ChatId        string                 `protobuf:"bytes,3,opt,name=chat_id,json=chatId,proto3" json:"chat_id,omitempty"`
	CreateTime    string                 `protobuf:"bytes,4,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GroupChat) Reset() {
	*x = GroupChat{}
	mi := &file_api_wx_app_app_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GroupChat) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GroupChat) ProtoMessage() {}

func (x *GroupChat) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GroupChat.ProtoReflect.Descriptor instead.
func (*GroupChat) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{16}
}

func (x *GroupChat) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GroupChat) GetOwner() string {
	if x != nil {
		return x.Owner
	}
	return ""
}

func (x *GroupChat) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *GroupChat) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

// 获取客户列表（带详情）
type GetCustomersWithDetailReply struct {
	state         protoimpl.MessageState                            `protogen:"open.v1"`
	Total         int32                                             `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*GetCustomersWithDetailReply_CustomerWithDetail `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	BizUnitIds    []uint64                                          `protobuf:"varint,3,rep,packed,name=biz_unit_ids,json=bizUnitIds,proto3" json:"biz_unit_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCustomersWithDetailReply) Reset() {
	*x = GetCustomersWithDetailReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomersWithDetailReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersWithDetailReply) ProtoMessage() {}

func (x *GetCustomersWithDetailReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersWithDetailReply.ProtoReflect.Descriptor instead.
func (*GetCustomersWithDetailReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{17}
}

func (x *GetCustomersWithDetailReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetCustomersWithDetailReply) GetList() []*GetCustomersWithDetailReply_CustomerWithDetail {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetCustomersWithDetailReply) GetBizUnitIds() []uint64 {
	if x != nil {
		return x.BizUnitIds
	}
	return nil
}

type CorpWeChatFriendInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	AddCreateTime string                 `protobuf:"bytes,2,opt,name=add_create_time,json=addCreateTime,proto3" json:"add_create_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CorpWeChatFriendInfo) Reset() {
	*x = CorpWeChatFriendInfo{}
	mi := &file_api_wx_app_app_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CorpWeChatFriendInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CorpWeChatFriendInfo) ProtoMessage() {}

func (x *CorpWeChatFriendInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CorpWeChatFriendInfo.ProtoReflect.Descriptor instead.
func (*CorpWeChatFriendInfo) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{18}
}

func (x *CorpWeChatFriendInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CorpWeChatFriendInfo) GetAddCreateTime() string {
	if x != nil {
		return x.AddCreateTime
	}
	return ""
}

type CorpGroupChatInfo struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	GroupChatName string                 `protobuf:"bytes,1,opt,name=group_chat_name,json=groupChatName,proto3" json:"group_chat_name,omitempty"`
	JoinTime      string                 `protobuf:"bytes,2,opt,name=join_time,json=joinTime,proto3" json:"join_time,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CorpGroupChatInfo) Reset() {
	*x = CorpGroupChatInfo{}
	mi := &file_api_wx_app_app_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CorpGroupChatInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CorpGroupChatInfo) ProtoMessage() {}

func (x *CorpGroupChatInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CorpGroupChatInfo.ProtoReflect.Descriptor instead.
func (*CorpGroupChatInfo) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{19}
}

func (x *CorpGroupChatInfo) GetGroupChatName() string {
	if x != nil {
		return x.GroupChatName
	}
	return ""
}

func (x *CorpGroupChatInfo) GetJoinTime() string {
	if x != nil {
		return x.JoinTime
	}
	return ""
}

// 绑定企微客户(删除原本的重新创建新的)
type BindCustomersRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BizUnitId       uint64                 `protobuf:"varint,1,opt,name=biz_unit_id,json=bizUnitId,proto3" json:"biz_unit_id,omitempty"`
	ExternalUserIds []string               `protobuf:"bytes,2,rep,name=external_user_ids,json=externalUserIds,proto3" json:"external_user_ids,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *BindCustomersRequest) Reset() {
	*x = BindCustomersRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindCustomersRequest) ProtoMessage() {}

func (x *BindCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindCustomersRequest.ProtoReflect.Descriptor instead.
func (*BindCustomersRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{20}
}

func (x *BindCustomersRequest) GetBizUnitId() uint64 {
	if x != nil {
		return x.BizUnitId
	}
	return 0
}

func (x *BindCustomersRequest) GetExternalUserIds() []string {
	if x != nil {
		return x.ExternalUserIds
	}
	return nil
}

type BindCustomersReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *BindCustomersReply) Reset() {
	*x = BindCustomersReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BindCustomersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindCustomersReply) ProtoMessage() {}

func (x *BindCustomersReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindCustomersReply.ProtoReflect.Descriptor instead.
func (*BindCustomersReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{21}
}

// 获取绑定企微客户
type GetBindCustomersRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	BizUnitIds      []uint64               `protobuf:"varint,1,rep,packed,name=biz_unit_ids,json=bizUnitIds,proto3" json:"biz_unit_ids,omitempty"`
	ExternalUserIds []string               `protobuf:"bytes,2,rep,name=external_user_ids,json=externalUserIds,proto3" json:"external_user_ids,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetBindCustomersRequest) Reset() {
	*x = GetBindCustomersRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBindCustomersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBindCustomersRequest) ProtoMessage() {}

func (x *GetBindCustomersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBindCustomersRequest.ProtoReflect.Descriptor instead.
func (*GetBindCustomersRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{22}
}

func (x *GetBindCustomersRequest) GetBizUnitIds() []uint64 {
	if x != nil {
		return x.BizUnitIds
	}
	return nil
}

func (x *GetBindCustomersRequest) GetExternalUserIds() []string {
	if x != nil {
		return x.ExternalUserIds
	}
	return nil
}

type GetBindCustomersReply struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Total         int32                                 `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*GetBindCustomersReply_BizUnit      `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`                                        // 按往来单位分组列举
	BindCustomers []*GetBindCustomersReply_BindCustomer `protobuf:"bytes,3,rep,name=bind_customers,json=bindCustomers,proto3" json:"bind_customers,omitempty"` // 全部企微客户关系列举
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBindCustomersReply) Reset() {
	*x = GetBindCustomersReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBindCustomersReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBindCustomersReply) ProtoMessage() {}

func (x *GetBindCustomersReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBindCustomersReply.ProtoReflect.Descriptor instead.
func (*GetBindCustomersReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{23}
}

func (x *GetBindCustomersReply) GetTotal() int32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *GetBindCustomersReply) GetList() []*GetBindCustomersReply_BizUnit {
	if x != nil {
		return x.List
	}
	return nil
}

func (x *GetBindCustomersReply) GetBindCustomers() []*GetBindCustomersReply_BindCustomer {
	if x != nil {
		return x.BindCustomers
	}
	return nil
}

// 获取绑定企微客户
type GetBindCustomerRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ExternalUserId string                 `protobuf:"bytes,1,opt,name=external_user_id,json=externalUserId,proto3" json:"external_user_id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetBindCustomerRequest) Reset() {
	*x = GetBindCustomerRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBindCustomerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBindCustomerRequest) ProtoMessage() {}

func (x *GetBindCustomerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBindCustomerRequest.ProtoReflect.Descriptor instead.
func (*GetBindCustomerRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{24}
}

func (x *GetBindCustomerRequest) GetExternalUserId() string {
	if x != nil {
		return x.ExternalUserId
	}
	return ""
}

type GetBindCustomerReply struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CustomerId       uint64                 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ExternalUserId   string                 `protobuf:"bytes,2,opt,name=external_user_id,json=externalUserId,proto3" json:"external_user_id,omitempty"`
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Avatar           string                 `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	ExternalType     int32                  `protobuf:"varint,5,opt,name=external_type,json=externalType,proto3" json:"external_type,omitempty"`
	ExternalTypeName string                 `protobuf:"bytes,6,opt,name=external_type_name,json=externalTypeName,proto3" json:"external_type_name,omitempty"`
	CorpName         string                 `protobuf:"bytes,7,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	CorpFullName     string                 `protobuf:"bytes,8,opt,name=corp_full_name,json=corpFullName,proto3" json:"corp_full_name,omitempty"`
	BizUnitId        uint64                 `protobuf:"varint,11,opt,name=biz_unit_id,json=bizUnitId,proto3" json:"biz_unit_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetBindCustomerReply) Reset() {
	*x = GetBindCustomerReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBindCustomerReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBindCustomerReply) ProtoMessage() {}

func (x *GetBindCustomerReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBindCustomerReply.ProtoReflect.Descriptor instead.
func (*GetBindCustomerReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{25}
}

func (x *GetBindCustomerReply) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetBindCustomerReply) GetExternalUserId() string {
	if x != nil {
		return x.ExternalUserId
	}
	return ""
}

func (x *GetBindCustomerReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetBindCustomerReply) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetBindCustomerReply) GetExternalType() int32 {
	if x != nil {
		return x.ExternalType
	}
	return 0
}

func (x *GetBindCustomerReply) GetExternalTypeName() string {
	if x != nil {
		return x.ExternalTypeName
	}
	return ""
}

func (x *GetBindCustomerReply) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

func (x *GetBindCustomerReply) GetCorpFullName() string {
	if x != nil {
		return x.CorpFullName
	}
	return ""
}

func (x *GetBindCustomerReply) GetBizUnitId() uint64 {
	if x != nil {
		return x.BizUnitId
	}
	return 0
}

type GetSignatureRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NonceStr      string                 `protobuf:"bytes,1,opt,name=nonce_str,json=nonceStr,proto3" json:"nonce_str,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	Url           string                 `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSignatureRequest) Reset() {
	*x = GetSignatureRequest{}
	mi := &file_api_wx_app_app_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSignatureRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSignatureRequest) ProtoMessage() {}

func (x *GetSignatureRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSignatureRequest.ProtoReflect.Descriptor instead.
func (*GetSignatureRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{26}
}

func (x *GetSignatureRequest) GetNonceStr() string {
	if x != nil {
		return x.NonceStr
	}
	return ""
}

func (x *GetSignatureRequest) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

func (x *GetSignatureRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type GetSignatureReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CorpSignature string                 `protobuf:"bytes,1,opt,name=corp_signature,json=corpSignature,proto3" json:"corp_signature,omitempty"`
	AppSignature  string                 `protobuf:"bytes,2,opt,name=app_signature,json=appSignature,proto3" json:"app_signature,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSignatureReply) Reset() {
	*x = GetSignatureReply{}
	mi := &file_api_wx_app_app_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSignatureReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSignatureReply) ProtoMessage() {}

func (x *GetSignatureReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSignatureReply.ProtoReflect.Descriptor instead.
func (*GetSignatureReply) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{27}
}

func (x *GetSignatureReply) GetCorpSignature() string {
	if x != nil {
		return x.CorpSignature
	}
	return ""
}

func (x *GetSignatureReply) GetAppSignature() string {
	if x != nil {
		return x.AppSignature
	}
	return ""
}

type GetCustomersWithDetailReply_CustomerWithDetail struct {
	state                 protoimpl.MessageState  `protogen:"open.v1"`
	CustomerId            uint64                  `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ExternalUserId        string                  `protobuf:"bytes,2,opt,name=external_user_id,json=externalUserId,proto3" json:"external_user_id,omitempty"`
	Name                  string                  `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Avatar                string                  `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	ExternalType          int32                   `protobuf:"varint,5,opt,name=external_type,json=externalType,proto3" json:"external_type,omitempty"`
	ExternalTypeName      string                  `protobuf:"bytes,6,opt,name=external_type_name,json=externalTypeName,proto3" json:"external_type_name,omitempty"`
	CorpName              string                  `protobuf:"bytes,7,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	CorpFullName          string                  `protobuf:"bytes,8,opt,name=corp_full_name,json=corpFullName,proto3" json:"corp_full_name,omitempty"`
	BizUnitId             uint64                  `protobuf:"varint,11,opt,name=biz_unit_id,json=bizUnitId,proto3" json:"biz_unit_id,omitempty"`
	IsBind                bool                    `protobuf:"varint,12,opt,name=is_bind,json=isBind,proto3" json:"is_bind,omitempty"`
	CorpWechatFriendCount int32                   `protobuf:"varint,13,opt,name=corp_wechat_friend_count,json=corpWechatFriendCount,proto3" json:"corp_wechat_friend_count,omitempty"`
	CorpGroupChatCount    int32                   `protobuf:"varint,14,opt,name=corp_group_chat_count,json=corpGroupChatCount,proto3" json:"corp_group_chat_count,omitempty"`
	Gender                string                  `protobuf:"bytes,15,opt,name=gender,proto3" json:"gender,omitempty"`
	CorpWechatFriendInfo  []*CorpWeChatFriendInfo `protobuf:"bytes,20,rep,name=corp_wechat_friend_info,json=corpWechatFriendInfo,proto3" json:"corp_wechat_friend_info,omitempty"`
	CorpGroupChatInfo     []*CorpGroupChatInfo    `protobuf:"bytes,21,rep,name=corp_group_chat_info,json=corpGroupChatInfo,proto3" json:"corp_group_chat_info,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) Reset() {
	*x = GetCustomersWithDetailReply_CustomerWithDetail{}
	mi := &file_api_wx_app_app_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCustomersWithDetailReply_CustomerWithDetail) ProtoMessage() {}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCustomersWithDetailReply_CustomerWithDetail.ProtoReflect.Descriptor instead.
func (*GetCustomersWithDetailReply_CustomerWithDetail) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{17, 0}
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetExternalUserId() string {
	if x != nil {
		return x.ExternalUserId
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetExternalType() int32 {
	if x != nil {
		return x.ExternalType
	}
	return 0
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetExternalTypeName() string {
	if x != nil {
		return x.ExternalTypeName
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCorpFullName() string {
	if x != nil {
		return x.CorpFullName
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetBizUnitId() uint64 {
	if x != nil {
		return x.BizUnitId
	}
	return 0
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetIsBind() bool {
	if x != nil {
		return x.IsBind
	}
	return false
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCorpWechatFriendCount() int32 {
	if x != nil {
		return x.CorpWechatFriendCount
	}
	return 0
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCorpGroupChatCount() int32 {
	if x != nil {
		return x.CorpGroupChatCount
	}
	return 0
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetGender() string {
	if x != nil {
		return x.Gender
	}
	return ""
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCorpWechatFriendInfo() []*CorpWeChatFriendInfo {
	if x != nil {
		return x.CorpWechatFriendInfo
	}
	return nil
}

func (x *GetCustomersWithDetailReply_CustomerWithDetail) GetCorpGroupChatInfo() []*CorpGroupChatInfo {
	if x != nil {
		return x.CorpGroupChatInfo
	}
	return nil
}

type GetBindCustomersReply_BindCustomer struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	CustomerId       uint64                 `protobuf:"varint,1,opt,name=customer_id,json=customerId,proto3" json:"customer_id,omitempty"`
	ExternalUserId   string                 `protobuf:"bytes,2,opt,name=external_user_id,json=externalUserId,proto3" json:"external_user_id,omitempty"`
	Name             string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	Avatar           string                 `protobuf:"bytes,4,opt,name=avatar,proto3" json:"avatar,omitempty"`
	ExternalType     int32                  `protobuf:"varint,5,opt,name=external_type,json=externalType,proto3" json:"external_type,omitempty"`
	ExternalTypeName string                 `protobuf:"bytes,6,opt,name=external_type_name,json=externalTypeName,proto3" json:"external_type_name,omitempty"`
	CorpName         string                 `protobuf:"bytes,7,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	CorpFullName     string                 `protobuf:"bytes,8,opt,name=corp_full_name,json=corpFullName,proto3" json:"corp_full_name,omitempty"`
	BizUnitId        uint64                 `protobuf:"varint,11,opt,name=biz_unit_id,json=bizUnitId,proto3" json:"biz_unit_id,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetBindCustomersReply_BindCustomer) Reset() {
	*x = GetBindCustomersReply_BindCustomer{}
	mi := &file_api_wx_app_app_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBindCustomersReply_BindCustomer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBindCustomersReply_BindCustomer) ProtoMessage() {}

func (x *GetBindCustomersReply_BindCustomer) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBindCustomersReply_BindCustomer.ProtoReflect.Descriptor instead.
func (*GetBindCustomersReply_BindCustomer) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{23, 0}
}

func (x *GetBindCustomersReply_BindCustomer) GetCustomerId() uint64 {
	if x != nil {
		return x.CustomerId
	}
	return 0
}

func (x *GetBindCustomersReply_BindCustomer) GetExternalUserId() string {
	if x != nil {
		return x.ExternalUserId
	}
	return ""
}

func (x *GetBindCustomersReply_BindCustomer) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetBindCustomersReply_BindCustomer) GetAvatar() string {
	if x != nil {
		return x.Avatar
	}
	return ""
}

func (x *GetBindCustomersReply_BindCustomer) GetExternalType() int32 {
	if x != nil {
		return x.ExternalType
	}
	return 0
}

func (x *GetBindCustomersReply_BindCustomer) GetExternalTypeName() string {
	if x != nil {
		return x.ExternalTypeName
	}
	return ""
}

func (x *GetBindCustomersReply_BindCustomer) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

func (x *GetBindCustomersReply_BindCustomer) GetCorpFullName() string {
	if x != nil {
		return x.CorpFullName
	}
	return ""
}

func (x *GetBindCustomersReply_BindCustomer) GetBizUnitId() uint64 {
	if x != nil {
		return x.BizUnitId
	}
	return 0
}

type GetBindCustomersReply_BizUnit struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	BizUnitId     uint64                                `protobuf:"varint,1,opt,name=biz_unit_id,json=bizUnitId,proto3" json:"biz_unit_id,omitempty"`
	BindCustomers []*GetBindCustomersReply_BindCustomer `protobuf:"bytes,2,rep,name=bind_customers,json=bindCustomers,proto3" json:"bind_customers,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetBindCustomersReply_BizUnit) Reset() {
	*x = GetBindCustomersReply_BizUnit{}
	mi := &file_api_wx_app_app_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetBindCustomersReply_BizUnit) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetBindCustomersReply_BizUnit) ProtoMessage() {}

func (x *GetBindCustomersReply_BizUnit) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_app_app_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetBindCustomersReply_BizUnit.ProtoReflect.Descriptor instead.
func (*GetBindCustomersReply_BizUnit) Descriptor() ([]byte, []int) {
	return file_api_wx_app_app_proto_rawDescGZIP(), []int{23, 1}
}

func (x *GetBindCustomersReply_BizUnit) GetBizUnitId() uint64 {
	if x != nil {
		return x.BizUnitId
	}
	return 0
}

func (x *GetBindCustomersReply_BizUnit) GetBindCustomers() []*GetBindCustomersReply_BindCustomer {
	if x != nil {
		return x.BindCustomers
	}
	return nil
}

var File_api_wx_app_app_proto protoreflect.FileDescriptor

var file_api_wx_app_app_proto_rawDesc = []byte{
	0x0a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x61, 0x70, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x22, 0x47, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63,
	0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x22, 0x13, 0x0a, 0x11, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x4e, 0x0a, 0x1a, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x1a, 0x0a, 0x18, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x48, 0x0a, 0x14, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x68, 0x61, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x14, 0x0a, 0x12,
	0x53, 0x79, 0x6e, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x51, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x51, 0x59, 0x57, 0x58, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x37, 0x0a, 0x08, 0x51, 0x59, 0x57, 0x58, 0x55, 0x73, 0x65,
	0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0x5b,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x51, 0x59, 0x57, 0x58, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x30, 0x0a, 0x05, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x51, 0x59, 0x57, 0x58,
	0x55, 0x73, 0x65, 0x72, 0x52, 0x05, 0x55, 0x73, 0x65, 0x72, 0x73, 0x22, 0x28, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x63, 0x6f, 0x64, 0x65, 0x22, 0x2b, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72,
	0x49, 0x64, 0x22, 0x99, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x24,
	0x0a, 0x0e, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x66, 0x6f, 0x6c, 0x6c, 0x6f, 0x77, 0x55, 0x73,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x20, 0x0a, 0x0c,
	0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x06, 0x20, 0x03,
	0x28, 0x04, 0x52, 0x0a, 0x62, 0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x73, 0x22, 0x5a,
	0x0a, 0x11, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2f, 0x0a, 0x04, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x22, 0xb6, 0x01, 0x0a, 0x09, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76,
	0x61, 0x74, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x72, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x72,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x66, 0x75,
	0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63,
	0x6f, 0x72, 0x70, 0x46, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x67,
	0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e,
	0x64, 0x65, 0x72, 0x22, 0x55, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x68, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x5e, 0x0a, 0x15, 0x47, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x03, 0x52, 0x05, 0x54, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x2f, 0x0a, 0x04, 0x4c, 0x69, 0x73,
	0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x68, 0x61, 0x74, 0x52, 0x04, 0x4c, 0x69, 0x73, 0x74, 0x22, 0x6f, 0x0a, 0x09, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x68, 0x61, 0x74, 0x49, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0xc1, 0x06, 0x0a, 0x1b,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x57, 0x69, 0x74, 0x68,
	0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x12, 0x54, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x40, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x57,
	0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x69, 0x7a, 0x5f, 0x75,
	0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x62,
	0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x73, 0x1a, 0x93, 0x05, 0x0a, 0x12, 0x43, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6f, 0x72, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f, 0x72, 0x70, 0x5f,
	0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x63, 0x6f, 0x72, 0x70, 0x46, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a,
	0x0b, 0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x09, 0x62, 0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x07, 0x69, 0x73, 0x5f, 0x62, 0x69, 0x6e, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06,
	0x69, 0x73, 0x42, 0x69, 0x6e, 0x64, 0x12, 0x37, 0x0a, 0x18, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x77,
	0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x05, 0x52, 0x15, 0x63, 0x6f, 0x72, 0x70, 0x57, 0x65,
	0x63, 0x68, 0x61, 0x74, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x31, 0x0a, 0x15, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x68,
	0x61, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x05, 0x52, 0x12,
	0x63, 0x6f, 0x72, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x43, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x67, 0x65, 0x6e, 0x64, 0x65, 0x72, 0x12, 0x5d, 0x0a, 0x17, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x77, 0x65, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x66, 0x72, 0x69, 0x65, 0x6e, 0x64,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x14, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x6f, 0x72, 0x70, 0x57, 0x65, 0x43, 0x68, 0x61, 0x74, 0x46, 0x72, 0x69, 0x65, 0x6e, 0x64, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x14, 0x63, 0x6f, 0x72, 0x70, 0x57, 0x65, 0x63, 0x68, 0x61, 0x74, 0x46,
	0x72, 0x69, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x54, 0x0a, 0x14, 0x63, 0x6f, 0x72,
	0x70, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x15, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6f, 0x72, 0x70, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x11, 0x63, 0x6f,
	0x72, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x22,
	0x52, 0x0a, 0x14, 0x43, 0x6f, 0x72, 0x70, 0x57, 0x65, 0x43, 0x68, 0x61, 0x74, 0x46, 0x72, 0x69,
	0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x61,
	0x64, 0x64, 0x5f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x64, 0x64, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x22, 0x58, 0x0a, 0x11, 0x43, 0x6f, 0x72, 0x70, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x43, 0x68, 0x61, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x26, 0x0a, 0x0f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x63, 0x68, 0x61, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0d, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f, 0x69, 0x6e, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6a, 0x6f, 0x69, 0x6e, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x62, 0x0a,
	0x14, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x69, 0x7a, 0x55,
	0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64,
	0x73, 0x22, 0x14, 0x0a, 0x12, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x67, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x42, 0x69,
	0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x04, 0x52, 0x0a, 0x62, 0x69, 0x7a, 0x55, 0x6e, 0x69,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x0f, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x73,
	0x22, 0x96, 0x05, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74,
	0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x12, 0x43, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x42, 0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x52,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x5b, 0x0a, 0x0e, 0x62, 0x69, 0x6e, 0x64, 0x5f, 0x63, 0x75,
	0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x1a, 0xbb, 0x02, 0x0a, 0x0c, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x2c, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x65, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x6f, 0x72, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x70, 0x46, 0x75, 0x6c, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64,
	0x1a, 0x86, 0x01, 0x0a, 0x07, 0x42, 0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x12, 0x1e, 0x0a, 0x0b,
	0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x62, 0x69, 0x7a, 0x55, 0x6e, 0x69, 0x74, 0x49, 0x64, 0x12, 0x5b, 0x0a, 0x0e,
	0x62, 0x69, 0x6e, 0x64, 0x5f, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x18, 0x02,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x42, 0x69,
	0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x0d, 0x62, 0x69, 0x6e, 0x64,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x22, 0x42, 0x0a, 0x16, 0x47, 0x65, 0x74,
	0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f,
	0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49, 0x64, 0x22, 0xc3, 0x02,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x0a, 0x63, 0x75, 0x73,
	0x74, 0x6f, 0x6d, 0x65, 0x72, 0x49, 0x64, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x78, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x55, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x76, 0x61, 0x74, 0x61, 0x72, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x54, 0x79, 0x70, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x72, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6f, 0x72, 0x70, 0x46, 0x75, 0x6c, 0x6c, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x0b, 0x62, 0x69, 0x7a, 0x5f, 0x75, 0x6e, 0x69, 0x74, 0x5f,
	0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x62, 0x69, 0x7a, 0x55, 0x6e, 0x69,
	0x74, 0x49, 0x64, 0x22, 0x62, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x6e, 0x6f,
	0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6e,
	0x6f, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x22, 0x5f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x69,
	0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x25, 0x0a, 0x0e,
	0x63, 0x6f, 0x72, 0x70, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63, 0x6f, 0x72, 0x70, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74,
	0x75, 0x72, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x70, 0x70, 0x5f, 0x73, 0x69, 0x67, 0x6e, 0x61,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x70, 0x70, 0x53,
	0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x42, 0x24, 0x0a, 0x10, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x42, 0x05, 0x41, 0x70,
	0x70, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wx_app_app_proto_rawDescOnce sync.Once
	file_api_wx_app_app_proto_rawDescData = file_api_wx_app_app_proto_rawDesc
)

func file_api_wx_app_app_proto_rawDescGZIP() []byte {
	file_api_wx_app_app_proto_rawDescOnce.Do(func() {
		file_api_wx_app_app_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wx_app_app_proto_rawDescData)
	})
	return file_api_wx_app_app_proto_rawDescData
}

var file_api_wx_app_app_proto_msgTypes = make([]protoimpl.MessageInfo, 31)
var file_api_wx_app_app_proto_goTypes = []any{
	(*SyncUserInfoRequest)(nil),                            // 0: wx.api.wx.app.v1.SyncUserInfoRequest
	(*SyncUserInfoReply)(nil),                              // 1: wx.api.wx.app.v1.SyncUserInfoReply
	(*SyncExternalContactRequest)(nil),                     // 2: wx.api.wx.app.v1.SyncExternalContactRequest
	(*SyncExternalContactReply)(nil),                       // 3: wx.api.wx.app.v1.SyncExternalContactReply
	(*SyncGroupChatRequest)(nil),                           // 4: wx.api.wx.app.v1.SyncGroupChatRequest
	(*SyncGroupChatReply)(nil),                             // 5: wx.api.wx.app.v1.SyncGroupChatReply
	(*GetQYWXUsersRequest)(nil),                            // 6: wx.api.wx.app.v1.GetQYWXUsersRequest
	(*QYWXUser)(nil),                                       // 7: wx.api.wx.app.v1.QYWXUser
	(*GetQYWXUsersReply)(nil),                              // 8: wx.api.wx.app.v1.GetQYWXUsersReply
	(*GetUserInfoRequest)(nil),                             // 9: wx.api.wx.app.v1.GetUserInfoRequest
	(*GetUserInfoReply)(nil),                               // 10: wx.api.wx.app.v1.GetUserInfoReply
	(*GetCustomersRequest)(nil),                            // 11: wx.api.wx.app.v1.GetCustomersRequest
	(*GetCustomersReply)(nil),                              // 12: wx.api.wx.app.v1.GetCustomersReply
	(*Customers)(nil),                                      // 13: wx.api.wx.app.v1.Customers
	(*GetGroupChatListRequest)(nil),                        // 14: wx.api.wx.app.v1.GetGroupChatListRequest
	(*GetGroupChatListReply)(nil),                          // 15: wx.api.wx.app.v1.GetGroupChatListReply
	(*GroupChat)(nil),                                      // 16: wx.api.wx.app.v1.GroupChat
	(*GetCustomersWithDetailReply)(nil),                    // 17: wx.api.wx.app.v1.GetCustomersWithDetailReply
	(*CorpWeChatFriendInfo)(nil),                           // 18: wx.api.wx.app.v1.CorpWeChatFriendInfo
	(*CorpGroupChatInfo)(nil),                              // 19: wx.api.wx.app.v1.CorpGroupChatInfo
	(*BindCustomersRequest)(nil),                           // 20: wx.api.wx.app.v1.BindCustomersRequest
	(*BindCustomersReply)(nil),                             // 21: wx.api.wx.app.v1.BindCustomersReply
	(*GetBindCustomersRequest)(nil),                        // 22: wx.api.wx.app.v1.GetBindCustomersRequest
	(*GetBindCustomersReply)(nil),                          // 23: wx.api.wx.app.v1.GetBindCustomersReply
	(*GetBindCustomerRequest)(nil),                         // 24: wx.api.wx.app.v1.GetBindCustomerRequest
	(*GetBindCustomerReply)(nil),                           // 25: wx.api.wx.app.v1.GetBindCustomerReply
	(*GetSignatureRequest)(nil),                            // 26: wx.api.wx.app.v1.GetSignatureRequest
	(*GetSignatureReply)(nil),                              // 27: wx.api.wx.app.v1.GetSignatureReply
	(*GetCustomersWithDetailReply_CustomerWithDetail)(nil), // 28: wx.api.wx.app.v1.GetCustomersWithDetailReply.CustomerWithDetail
	(*GetBindCustomersReply_BindCustomer)(nil),             // 29: wx.api.wx.app.v1.GetBindCustomersReply.BindCustomer
	(*GetBindCustomersReply_BizUnit)(nil),                  // 30: wx.api.wx.app.v1.GetBindCustomersReply.BizUnit
}
var file_api_wx_app_app_proto_depIdxs = []int32{
	7,  // 0: wx.api.wx.app.v1.GetQYWXUsersReply.Users:type_name -> wx.api.wx.app.v1.QYWXUser
	13, // 1: wx.api.wx.app.v1.GetCustomersReply.List:type_name -> wx.api.wx.app.v1.Customers
	16, // 2: wx.api.wx.app.v1.GetGroupChatListReply.List:type_name -> wx.api.wx.app.v1.GroupChat
	28, // 3: wx.api.wx.app.v1.GetCustomersWithDetailReply.list:type_name -> wx.api.wx.app.v1.GetCustomersWithDetailReply.CustomerWithDetail
	30, // 4: wx.api.wx.app.v1.GetBindCustomersReply.list:type_name -> wx.api.wx.app.v1.GetBindCustomersReply.BizUnit
	29, // 5: wx.api.wx.app.v1.GetBindCustomersReply.bind_customers:type_name -> wx.api.wx.app.v1.GetBindCustomersReply.BindCustomer
	18, // 6: wx.api.wx.app.v1.GetCustomersWithDetailReply.CustomerWithDetail.corp_wechat_friend_info:type_name -> wx.api.wx.app.v1.CorpWeChatFriendInfo
	19, // 7: wx.api.wx.app.v1.GetCustomersWithDetailReply.CustomerWithDetail.corp_group_chat_info:type_name -> wx.api.wx.app.v1.CorpGroupChatInfo
	29, // 8: wx.api.wx.app.v1.GetBindCustomersReply.BizUnit.bind_customers:type_name -> wx.api.wx.app.v1.GetBindCustomersReply.BindCustomer
	9,  // [9:9] is the sub-list for method output_type
	9,  // [9:9] is the sub-list for method input_type
	9,  // [9:9] is the sub-list for extension type_name
	9,  // [9:9] is the sub-list for extension extendee
	0,  // [0:9] is the sub-list for field type_name
}

func init() { file_api_wx_app_app_proto_init() }
func file_api_wx_app_app_proto_init() {
	if File_api_wx_app_app_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_app_app_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   31,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_wx_app_app_proto_goTypes,
		DependencyIndexes: file_api_wx_app_app_proto_depIdxs,
		MessageInfos:      file_api_wx_app_app_proto_msgTypes,
	}.Build()
	File_api_wx_app_app_proto = out.File
	file_api_wx_app_app_proto_rawDesc = nil
	file_api_wx_app_app_proto_goTypes = nil
	file_api_wx_app_app_proto_depIdxs = nil
}
