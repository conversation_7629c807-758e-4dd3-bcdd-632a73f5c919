// Code generated by "stringer -type=FabricPieceCodeStatus --linecomment"; DO NOT EDIT.

package product

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[FabricPieceCodeStatusEnable-0]
	_ = x[FabricPieceCodeStatusReturnCancel-1]
}

const _FabricPieceCodeStatus_name = "生效退货换标作废"

var _FabricPieceCodeStatus_index = [...]uint8{0, 6, 24}

func (i FabricPieceCodeStatus) String() string {
	if i < 0 || i >= FabricPieceCodeStatus(len(_FabricPieceCodeStatus_index)-1) {
		return ""
	}
	return _FabricPieceCodeStatus_name[_FabricPieceCodeStatus_index[i]:_FabricPieceCodeStatus_index[i+1]]
}
