package grey_fabric_manage

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	iconsts "hcscm/common/grey_fabric_manage"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/grey_fabric_manage"
	"hcscm/tools"
	"hcscm/vars"
	"time"
)

func GetGFMPurchaseReceiveIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "gfm_purchase_receive_id")
}

type GFMPurchaseReceiveList []GFMPurchaseReceive

func (r GFMPurchaseReceiveList) List() []GFMPurchaseReceive {
	return r
}

func (r GFMPurchaseReceiveList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r GFMPurchaseReceiveList) One() GFMPurchaseReceive {
	return r[0]
}

func (r GFMPurchaseReceiveList) Pick(id uint64) (o GFMPurchaseReceive) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

// GFMPurchaseReceive 坯布采购收货单
type GFMPurchaseReceive struct {
	mysql_base.Order
	Id                uint64    `gorm:"column:id;primaryKey"`
	DepartmentId      uint64    `gorm:"column:department_id"`         // 下单人部门
	SaleSystemId      uint64    `gorm:"column:sale_system_id"`        // 营销体系id，必填
	RecipientEntityId uint64    `gorm:"column:recipient_entity_id"`   // 收货单位id，必填
	SupplierId        uint64    `gorm:"column:supplier_id"`           // 供应商id，必填
	Number            int       `gorm:"column:number"`                // 流水号
	DocumentCode      string    `gorm:"column:document_code"`         // 单据编号
	VoucherNumber     string    `gorm:"column:voucher_number"`        // 凭证单号
	ReceiveTime       time.Time `gorm:"column:receive_time"`          // 收货日期，必
	Remark            string    `gorm:"column:remark"`                // 备注
	TotalRoll         int       `gorm:"column:total_roll"`            // 总匹数
	TotalWeight       int       `gorm:"column:total_weight"`          // 总数量
	TotalPrice        int       `gorm:"column:total_price"`           // 总价格
	PurchaseOrderId   uint64    `gorm:"column:purchase_order_id"`     // 坯布采购单id
	DyeUnitUseOrderNo string    `gorm:"column:dye_unit_use_order_no"` // 染厂用坯单号
}

// 查询后的钩子
func (r *GFMPurchaseReceive) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r GFMPurchaseReceive) GetId() uint64 {
	return r.Id
}

// TableName GFMPurchaseReceive 表名
func (GFMPurchaseReceive) TableName() string {
	return "gfm_purchase_receive"
}

func (r GFMPurchaseReceive) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	var (
		count   int
		list    []GFMPurchaseReceive
		mapCode = make(map[uint64]string, 0)
		okCode  bool
	)
	list, count, err = SearchGFMPurchaseReceive(tx, &structure.GetGFMPurchaseReceiveListQuery{DocumentCode: r.DocumentCode, JudgeExit: true})
	for _, v := range list {
		mapCode[v.Id] = v.DocumentCode
	}
	_, okCode = mapCode[r.Id]
	return tools.IsExitOrderNo(count, okCode, r.Id)
}

func (GFMPurchaseReceive) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (GFMPurchaseReceive) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeGFMPurchaseReceiveNotExist
}

func (GFMPurchaseReceive) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeGFMPurchaseReceiveAlreadyExist
}

func (r GFMPurchaseReceive) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func NewGFMPurchaseReceive(
	ctx context.Context,
	p *structure.AddGFMPurchaseReceiveParam,
) (r GFMPurchaseReceive) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.SaleSystemId = p.SaleSystemId
	r.RecipientEntityId = p.RecipientEntityId
	r.SupplierId = p.SupplierId
	r.VoucherNumber = p.VoucherNumber
	r.ReceiveTime = p.ReceiveTime.ToTimeYMD()
	r.AuditStatus = p.AuditStatus
	r.Remark = p.Remark
	r.PurchaseOrderId = p.PurchaseOrderId
	r.DyeUnitUseOrderNo = p.DyeUnitUseOrderNo
	return
}

func (r *GFMPurchaseReceive) UpdateGFMPurchaseReceive(
	ctx context.Context,
	p *structure.UpdateGFMPurchaseReceiveParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.RecipientEntityId = p.RecipientEntityId
	r.SupplierId = p.SupplierId
	r.VoucherNumber = p.VoucherNumber
	r.ReceiveTime = p.ReceiveTime.ToTimeYMD()
	r.Remark = p.Remark
	r.DyeUnitUseOrderNo = p.DyeUnitUseOrderNo
}

// func (r *GFMPurchaseReceive) UpdateGFMPurchaseReceiveStatus(
//	ctx context.Context,
//	p *structure.UpdateGFMPurchaseReceiveStatusParam,
// ) {
//	r.Status = p.Status
// }

func MustCreateGFMPurchaseReceive(tx *mysql_base.Tx, r GFMPurchaseReceive) (o GFMPurchaseReceive, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateGFMPurchaseReceive(tx *mysql_base.Tx, r GFMPurchaseReceive) (o GFMPurchaseReceive, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteGFMPurchaseReceive(tx *mysql_base.Tx, r GFMPurchaseReceive) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstGFMPurchaseReceiveByID(tx *mysql_base.Tx, id uint64) (r GFMPurchaseReceive, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstGFMPurchaseReceiveByID(tx *mysql_base.Tx, id uint64) (r GFMPurchaseReceive, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FirstGFMPurchaseReceiveByPurchaseOrderID(tx *mysql_base.Tx, id uint64) (r GFMPurchaseReceive, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddEqual("purchase_order_id", id)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)

	return
}

func FindGFMPurchaseReceiveByGFMPurchaseReceiveID(tx *mysql_base.Tx, objects ...interface{}) (o GFMPurchaseReceiveList, err error) {
	ids := GetGFMPurchaseReceiveIdList(objects)
	var (
		r    GFMPurchaseReceive
		cond = mysql_base.NewCondition()
		list []GFMPurchaseReceive
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindGFMPurchaseReceiveByIDs(tx *mysql_base.Tx, ids []uint64) (o GFMPurchaseReceiveList, err error) {
	var (
		r    GFMPurchaseReceive
		cond = mysql_base.NewCondition()
		list []GFMPurchaseReceive
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindGFMPurchaseReceiveByPurchaseOrderIDs(tx *mysql_base.Tx, ids []uint64) (o GFMPurchaseReceiveList, err error) {
	var (
		r    GFMPurchaseReceive
		cond = mysql_base.NewCondition()
		list []GFMPurchaseReceive
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddEqual("purchase_order_id", ids)
	cond.AddNotEqual("audit_status", common_system.OrderStatusVoided)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchGFMPurchaseReceive(tx *mysql_base.Tx, q *structure.GetGFMPurchaseReceiveListQuery) (o GFMPurchaseReceiveList, count int, err error) {
	var (
		r           GFMPurchaseReceive
		item        GFMPurchaseReceiveItem
		cond        = mysql_base.NewCondition()
		list        []GFMPurchaseReceive
		groupFields []string
	)
	groupFields = []string{}
	r.BuildReadCond(tx.Context, cond)
	if q.SaleSystemId != 0 {
		cond.AddTableEqual(r, "sale_system_id", q.SaleSystemId)
	}
	if q.RecipientEntityId != 0 {
		cond.AddTableEqual(r, "recipient_entity_id", q.RecipientEntityId)
	}
	if q.SupplierId != 0 {
		cond.AddTableEqual(r, "supplier_id", q.SupplierId)
	}
	if len(q.DocumentCode) > 0 {
		if q.JudgeExit == true {
			cond.AddTableEqual(r, "document_code", q.DocumentCode)
		} else {
			cond.AddTableFuzzyMatch(r, "document_code", q.DocumentCode)
		}
	}
	if q.VoucherNumber != "" {
		cond.AddTableFuzzyMatch(r, "voucher_number", q.VoucherNumber)
	}
	if !q.AuditStatus.IsNil() {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToUint64())
	} else {
		cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	}
	if !q.OrderNos.IsNil() {
		cond.AddTableContainMatch(r, "document_code", q.OrderNos.ToString())
	}
	if q.GreyFabricPurchaseId != 0 || q.GreyFabricPurchaseCode != "" {
		cond.AddTableLeftJoiner(r, item, "id", "grey_fabric_receive_id")
		cond.AddTableEqual(item, "grey_fabric_purchase_code", q.GreyFabricPurchaseCode)
		groupFields = []string{"gfm_purchase_receive.id"}
	}

	cond.AddSort("-gfm_purchase_receive.update_time")
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchGFMPurchaseReceiveNoPage(tx *mysql_base.Tx, q *structure.GetGFMPurchaseReceiveListQuery) (o GFMPurchaseReceiveList, err error) {
	var (
		r           GFMPurchaseReceive
		cond        = mysql_base.NewCondition()
		list        []GFMPurchaseReceive
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.GetPass == true {
		cond.AddTableEqual(r, "audit_status", iconsts.AuditStatusPass)
	}
	groupFields = []string{}
	err = mysql_base.SearchListGroup(tx, &r, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}
