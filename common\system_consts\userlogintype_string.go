// Code generated by "stringer -type=UserLoginType --linecomment"; DO NOT EDIT.

package common

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[UserLoginTypeUser-1]
	_ = x[UserLoginTypeTenantManagement-2]
	_ = x[UserLoginTypeBizUnit-3]
}

const _UserLoginType_name = "用户登录租户登录往来单位登录"

var _UserLoginType_index = [...]uint8{0, 12, 24, 42}

func (i UserLoginType) String() string {
	i -= 1
	if i < 0 || i >= UserLoginType(len(_UserLoginType_index)-1) {
		return ""
	}
	return _UserLoginType_name[_UserLoginType_index[i]:_UserLoginType_index[i+1]]
}
