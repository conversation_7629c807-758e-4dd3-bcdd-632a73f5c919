package employee

import (
	"github.com/gin-gonic/gin"
	"hcscm/domain/biz_unit/entity"
	employeeEntity "hcscm/domain/employee/entity"
	mysql "hcscm/model/mysql/employee"
	"hcscm/model/mysql/mysql_base"
	"hcscm/server/system"
	employeeSvc "hcscm/service/employee"
	structure "hcscm/structure/system"
	"hcscm/tools"
)

// GetEmployeeDutyList 员工职责列表
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	员工职责列表
//	@Produce	json
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.ListBaseDataResponse{}
//	@Router		/hcscm/admin/v1/employee/duty/list [get]
func GetEmployeeDutyList(c *gin.Context) {
	system.BuildMapResponseV2(c, employeeEntity.DutyMap)
	return
}

// GetEmployeeMaritalStatusList 员工婚姻状况列表
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	员工婚姻状况列表
//	@Produce	json
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.ListBaseDataResponse{}
//	@Router		/hcscm/admin/v1/employee/marital_status/list [get]
func GetEmployeeMaritalStatusList(c *gin.Context) {
	system.BuildMapResponseV2(c, employeeEntity.MaritalStatusMap)
	return
}

// GetEmployeeEducationLevelList 员工教育水平列表
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	员工教育水平列表
//	@Produce	json
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.ListBaseDataResponse{}
//	@Router		/hcscm/admin/v1/employee/education_level/list [get]
func GetEmployeeEducationLevelList(c *gin.Context) {
	system.BuildMapResponseV2(c, employeeEntity.EducationLevelMap)
	return
}

// AddEmployee 新建员工
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	新建员工
//	@Produce	json
//	@Param		body			body		structure.EmployeeParams{}	true	"新建员工-请求参数"
//	@Param		Platform		header		int							true	"终端ID"
//	@Param		Authorization	header		string						true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/employee [post]
func AddEmployee(c *gin.Context) {
	var (
		svc  = employeeSvc.NewEmployeeService()
		p    = &structure.AddEmployeeParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	err = svc.AddEmployee(ctx, p)
	if err != nil {
		return
	}

	return
}

// UpdateEmployee 更新员工
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	更新员工
//	@Produce	json
//	@Param		body			body		structure.UpdateEmployeeParams{}	true	"更新员工-请求参数"
//	@Param		Platform		header		int									true	"终端ID"
//	@Param		Authorization	header		string								true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/employee [put]
func UpdateEmployee(c *gin.Context) {
	var (
		svc  = employeeSvc.NewEmployeeService()
		p    = &structure.UpdateEmployeeParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	err = svc.UpdateEmployee(ctx, p)
	if err != nil {
		return
	}

	return
}

// UpdateEmployeeStatus 启用员工
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	更新员工状态
//	@Produce	json
//	@Param		body			body		structure.UpdateStatusEmployeeParams{}	true	"启用员工-请求参数"
//	@Param		Platform		header		int										true	"终端ID"
//	@Param		Authorization	header		string									true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/employee/updateStatus [put]
func UpdateEmployeeStatus(c *gin.Context) {
	var (
		svc  = employeeSvc.NewEmployeeService()
		p    = &structure.UpdateStatusEmployeeParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.Status == entity.StatusAvailable {
		err = svc.EnableEmployee(ctx, p)
		if err != nil {
			return
		}
		return
	}

	err = svc.DisableEmployee(ctx, p)
	if err != nil {
		return
	}

	return
}

// DeleteEmployee 删除员工
//
//	@Tags		员工管理
//	@Security	ApiKeyAuth
//	@Summary	删除员工
//	@Produce	json
//	@Param		body			body		structure.DeleteEmployeeParams{}	true	"删除员工-请求参数"
//	@Param		Platform		header		int									true	"终端ID"
//	@Param		Authorization	header		string								true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/employee [delete]
func DeleteEmployee(c *gin.Context) {
	var (
		svc  = employeeSvc.NewEmployeeService()
		p    = &structure.DeleteEmployeeParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	err = svc.DeleteEmployee(ctx, p)
	if err != nil {
		return
	}

	return
}

// GetEmployeeList 员工列表
//
//	@Tags		员工管理
//	@Summary	员工列表
//	@Security	ApiKeyAuth
//	@Param		page			query		int		false	"page"
//	@Param		size			query		int		false	"size"
//	@Param		offset			query		int		false	"offset"
//	@Param		limit			query		int		false	"limit"
//	@Param		download		query		int		false	"是否导出excel"
//	@Param		code			query		string	false	"编号"
//	@Param		name			query		string	false	"名称"
//	@Param		department_id	query		int		false	"部门id"
//	@Param		status			query		int		false	"状态"
//	@Param		duty			query		string	false	"职责，多个逗号分割"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.GetEmployeeList{}
//	@Router		/hcscm/admin/v1/employee/list [get]
func GetEmployeeList(c *gin.Context) {
	var (
		q            = &structure.GetEmployeeListParams{}
		list         = &structure.GetEmployeeList{}
		err          error
		total        int
		queryService = employeeSvc.NewEmployeeQueryService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		// system.BuildListResponse(c, err, list, total)
		system.BuildListResponseV2(c, q, "员工管理", err, *list, nil, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	dtoRsp, err := queryService.QueryEmployeeList(ctx, tx, q)
	if err != nil {
		return
	}
	total = dtoRsp.Total

	list = toGetEmployeeListData(dtoRsp)
}

func toGetEmployeeListData(dtoRsp *structure.QueryEmployeeListDtoRsp) *structure.GetEmployeeList {
	var list structure.GetEmployeeList
	esmap := mysql.GetEmployeeSaleSystemMap(dtoRsp.SaleSystemRel)

	for _, item := range dtoRsp.EmployeeList {
		dutyStr := make([]string, 0, len(item.Duty))
		for _, d := range item.Duty.ToInt() {
			dutyStr = append(dutyStr, dtoRsp.DutyMap[d][1])
		}
		saleSystemList := esmap[item.Id]
		saleSystemNameList := make([]string, 0, len(saleSystemList))
		for _, ssid := range saleSystemList {
			if name, ok := dtoRsp.SaleSystemMap[ssid]; ok {
				saleSystemNameList = append(saleSystemNameList, name)
			}
		}
		list = append(list, &structure.GetEmployeeListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			Name:               item.Name,
			Code:               item.Code,
			SaleSystemList:     saleSystemList,
			SaleSystemListName: saleSystemNameList,
			Duty:               item.Duty,
			DutyStr:            dutyStr,
			DepartmentID:       item.DepartmentID,
			DepartmentName:     dtoRsp.DepartmentMap[item.DepartmentID],
			Phone:              item.Phone,
			Email:              item.Email,
			Address:            item.Address,
			Birthday:           item.Birthday,
			IdentityNumber:     item.IdentityNumber,
			Nation:             item.Nation,
			MaritalStatus:      item.MaritalStatus,
			MaritalStatusName:  employeeEntity.MaritalStatusMap[item.MaritalStatus],
			EducationLevel:     item.EducationLevel,
			EducationLevelName: employeeEntity.EducationLevelMap[item.EducationLevel],
			GraduateSchool:     item.GraduateSchool,
			GraduateDate:       item.GraduateDate,
			ContractStartDate:  item.ContractStartDate,
			ContractEndDate:    item.ContractEndDate,
			ResignDate:         item.ResignDate,
			IsBlacklist:        item.IsBlacklist,
			Status:             item.Status,
			StatusName:         employeeEntity.EmployeeStatusMap[item.Status],
			QYWXUserName:       item.QYWXUserName,
			QYWXUserID:         item.QYWXUserID,
		})
	}
	return &list
}

// GetEmployeeDetail 员工详情
//
//	@Tags		员工管理
//	@Summary	员工详情
//	@Security	ApiKeyAuth
//	@Param		id				query		int		true	"员工id"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.GetEmployeeDetail{}
//	@Router		/hcscm/admin/v1/employee/detail [get]
func GetEmployeeDetail(c *gin.Context) {
	var (
		q   = &structure.GetEmployeeDetailParams{}
		r   = &structure.GetEmployeeDetail{}
		err error
		svc = employeeSvc.NewEmployeeQueryService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, r)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	if q.Id <= 0 {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	r, err = svc.QueryEmployeeDetail(ctx, tx, q.Id)
	if err != nil {
		return
	}
}

// GetEmployeeListEnum 员工列表（下拉）
//
//	@Tags		员工管理
//	@Summary	员工列表（下拉）
//	@Security	ApiKeyAuth
//	@Param		page			query		int		false	"page"
//	@Param		size			query		int		false	"size"
//	@Param		offset			query		int		false	"offset"
//	@Param		limit			query		int		false	"limit"
//	@Param		download		query		int		false	"是否导出excel"
//	@Param		code			query		string	false	"编号"
//	@Param		name			query		string	false	"名称"
//	@Param		department_id	query		int		false	"部门id"
//	@Param		status			query		int		false	"状态"
//	@Param		duty			query		string	false	"职责，多个逗号分割"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.GetEmployeeListEnum{}
//	@Router		/hcscm/admin/v1/employee/list_enum [get]
func GetEmployeeListEnum(c *gin.Context) {
	var (
		q            = &structure.GetEmployeeListParams{}
		list         = &structure.GetEmployeeListEnum{}
		err          error
		total        int
		queryService = employeeSvc.NewEmployeeQueryService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 默认启用
	if q.Status == 0 {
		q.Status = 1
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	dtoRsp, err := queryService.QueryEmployeeEnumList(ctx, tx, q)
	if err != nil {
		return
	}
	total = dtoRsp.Total

	list = toGetEmployeeListEnumData(dtoRsp)
}

func toGetEmployeeListEnumData(dtoRsp *structure.QueryEmployeeListDtoRsp) *structure.GetEmployeeListEnum {
	var list structure.GetEmployeeListEnum
	esmap := mysql.GetEmployeeSaleSystemMap(dtoRsp.SaleSystemRel)

	for _, item := range dtoRsp.EmployeeList {
		dutyStr := make([]string, 0, len(item.Duty))
		for _, d := range item.Duty.ToInt() {
			dutyStr = append(dutyStr, dtoRsp.DutyMap[d][1])
		}
		saleSystemList := esmap[item.Id]
		saleSystemNameList := make([]string, 0, len(saleSystemList))
		for _, ssid := range saleSystemList {
			if name, ok := dtoRsp.SaleSystemMap[ssid]; ok {
				saleSystemNameList = append(saleSystemNameList, name)
			}
		}
		list = append(list, &structure.GetEmployeeListEnumItem{
			Id:                 item.Id,
			Name:               item.Name,
			Code:               item.Code,
			SaleSystemList:     saleSystemList,
			SaleSystemListName: saleSystemNameList,
			Duty:               item.Duty,
			DutyName:           dutyStr,
			DepartmentID:       item.DepartmentID,
			DepartmentName:     dtoRsp.DepartmentMap[item.DepartmentID],
			Phone:              item.Phone,
		})
	}
	return &list
}
