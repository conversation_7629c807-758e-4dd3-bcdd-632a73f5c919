// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.29.1
// source: api/wx/tobe_developed_app_info/tobe_developed_app_info.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateTobeDevelopedAppInfoRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Token          string                 `protobuf:"bytes,1,opt,name=token,proto3" json:"token,omitempty"`
	EncodingAesKey string                 `protobuf:"bytes,2,opt,name=encoding_aes_key,json=encodingAesKey,proto3" json:"encoding_aes_key,omitempty"`
	Id             uint64                 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdateTobeDevelopedAppInfoRequest) Reset() {
	*x = UpdateTobeDevelopedAppInfoRequest{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTobeDevelopedAppInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTobeDevelopedAppInfoRequest) ProtoMessage() {}

func (x *UpdateTobeDevelopedAppInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTobeDevelopedAppInfoRequest.ProtoReflect.Descriptor instead.
func (*UpdateTobeDevelopedAppInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{0}
}

func (x *UpdateTobeDevelopedAppInfoRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *UpdateTobeDevelopedAppInfoRequest) GetEncodingAesKey() string {
	if x != nil {
		return x.EncodingAesKey
	}
	return ""
}

func (x *UpdateTobeDevelopedAppInfoRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateTobeDevelopedAppInfoReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTobeDevelopedAppInfoReply) Reset() {
	*x = UpdateTobeDevelopedAppInfoReply{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTobeDevelopedAppInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTobeDevelopedAppInfoReply) ProtoMessage() {}

func (x *UpdateTobeDevelopedAppInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTobeDevelopedAppInfoReply.ProtoReflect.Descriptor instead.
func (*UpdateTobeDevelopedAppInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateTobeDevelopedAppInfoReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateTobeDevelopedAppInfoRobotRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RobotCode       string                 `protobuf:"bytes,1,opt,name=robot_code,json=robotCode,proto3" json:"robot_code,omitempty"`
	RobotEffectTime string                 `protobuf:"bytes,2,opt,name=robot_effect_time,json=robotEffectTime,proto3" json:"robot_effect_time,omitempty"`
	Id              uint64                 `protobuf:"varint,3,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *UpdateTobeDevelopedAppInfoRobotRequest) Reset() {
	*x = UpdateTobeDevelopedAppInfoRobotRequest{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTobeDevelopedAppInfoRobotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTobeDevelopedAppInfoRobotRequest) ProtoMessage() {}

func (x *UpdateTobeDevelopedAppInfoRobotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTobeDevelopedAppInfoRobotRequest.ProtoReflect.Descriptor instead.
func (*UpdateTobeDevelopedAppInfoRobotRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateTobeDevelopedAppInfoRobotRequest) GetRobotCode() string {
	if x != nil {
		return x.RobotCode
	}
	return ""
}

func (x *UpdateTobeDevelopedAppInfoRobotRequest) GetRobotEffectTime() string {
	if x != nil {
		return x.RobotEffectTime
	}
	return ""
}

func (x *UpdateTobeDevelopedAppInfoRobotRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateTobeDevelopedAppInfoRobotReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTobeDevelopedAppInfoRobotReply) Reset() {
	*x = UpdateTobeDevelopedAppInfoRobotReply{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTobeDevelopedAppInfoRobotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTobeDevelopedAppInfoRobotReply) ProtoMessage() {}

func (x *UpdateTobeDevelopedAppInfoRobotReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTobeDevelopedAppInfoRobotReply.ProtoReflect.Descriptor instead.
func (*UpdateTobeDevelopedAppInfoRobotReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateTobeDevelopedAppInfoRobotReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetTobeDevelopedAppInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTobeDevelopedAppInfoRequest) Reset() {
	*x = GetTobeDevelopedAppInfoRequest{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTobeDevelopedAppInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTobeDevelopedAppInfoRequest) ProtoMessage() {}

func (x *GetTobeDevelopedAppInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTobeDevelopedAppInfoRequest.ProtoReflect.Descriptor instead.
func (*GetTobeDevelopedAppInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{4}
}

func (x *GetTobeDevelopedAppInfoRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetTobeDevelopedAppInfoReply struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime      string                 `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CreatorId       uint64                 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CreatorName     string                 `protobuf:"bytes,4,opt,name=creator_name,json=creatorName,proto3" json:"creator_name,omitempty"`
	UpdateTime      string                 `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UpdaterId       uint64                 `protobuf:"varint,6,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdaterName     string                 `protobuf:"bytes,7,opt,name=updater_name,json=updaterName,proto3" json:"updater_name,omitempty"`
	AgentId         uint32                 `protobuf:"varint,8,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Name            string                 `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	Token           string                 `protobuf:"bytes,10,opt,name=token,proto3" json:"token,omitempty"`
	EncodingAesKey  string                 `protobuf:"bytes,11,opt,name=encoding_aes_key,json=encodingAesKey,proto3" json:"encoding_aes_key,omitempty"`
	RobotCode       string                 `protobuf:"bytes,12,opt,name=robot_code,json=robotCode,proto3" json:"robot_code,omitempty"`
	RobotEffectTime string                 `protobuf:"bytes,13,opt,name=robot_effect_time,json=robotEffectTime,proto3" json:"robot_effect_time,omitempty"`
	CorpId          string                 `protobuf:"bytes,14,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	CorpName        string                 `protobuf:"bytes,15,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetTobeDevelopedAppInfoReply) Reset() {
	*x = GetTobeDevelopedAppInfoReply{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTobeDevelopedAppInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTobeDevelopedAppInfoReply) ProtoMessage() {}

func (x *GetTobeDevelopedAppInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTobeDevelopedAppInfoReply.ProtoReflect.Descriptor instead.
func (*GetTobeDevelopedAppInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{5}
}

func (x *GetTobeDevelopedAppInfoReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetTobeDevelopedAppInfoReply) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *GetTobeDevelopedAppInfoReply) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetUpdaterId() uint64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *GetTobeDevelopedAppInfoReply) GetUpdaterName() string {
	if x != nil {
		return x.UpdaterName
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *GetTobeDevelopedAppInfoReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetEncodingAesKey() string {
	if x != nil {
		return x.EncodingAesKey
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetRobotCode() string {
	if x != nil {
		return x.RobotCode
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetRobotEffectTime() string {
	if x != nil {
		return x.RobotEffectTime
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *GetTobeDevelopedAppInfoReply) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

type ListTobeDevelopedAppInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          uint32                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size          uint32                 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	CorpId        string                 `protobuf:"bytes,3,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	CorpName      string                 `protobuf:"bytes,4,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	Ids           string                 `protobuf:"bytes,5,opt,name=ids,proto3" json:"ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTobeDevelopedAppInfoRequest) Reset() {
	*x = ListTobeDevelopedAppInfoRequest{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTobeDevelopedAppInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTobeDevelopedAppInfoRequest) ProtoMessage() {}

func (x *ListTobeDevelopedAppInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTobeDevelopedAppInfoRequest.ProtoReflect.Descriptor instead.
func (*ListTobeDevelopedAppInfoRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{6}
}

func (x *ListTobeDevelopedAppInfoRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoRequest) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoRequest) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoRequest) GetIds() string {
	if x != nil {
		return x.Ids
	}
	return ""
}

type ListTobeDevelopedAppInfoReply struct {
	state         protoimpl.MessageState                                `protogen:"open.v1"`
	Total         uint32                                                `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTobeDevelopedAppInfoReply) Reset() {
	*x = ListTobeDevelopedAppInfoReply{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTobeDevelopedAppInfoReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTobeDevelopedAppInfoReply) ProtoMessage() {}

func (x *ListTobeDevelopedAppInfoReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTobeDevelopedAppInfoReply.ProtoReflect.Descriptor instead.
func (*ListTobeDevelopedAppInfoReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{7}
}

func (x *ListTobeDevelopedAppInfoReply) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoReply) GetList() []*ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo {
	if x != nil {
		return x.List
	}
	return nil
}

type ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime      string                 `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CreatorId       uint64                 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CreatorName     string                 `protobuf:"bytes,4,opt,name=creator_name,json=creatorName,proto3" json:"creator_name,omitempty"`
	UpdateTime      string                 `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UpdaterId       uint64                 `protobuf:"varint,6,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdaterName     string                 `protobuf:"bytes,7,opt,name=updater_name,json=updaterName,proto3" json:"updater_name,omitempty"`
	AgentId         uint32                 `protobuf:"varint,8,opt,name=agent_id,json=agentId,proto3" json:"agent_id,omitempty"`
	Name            string                 `protobuf:"bytes,9,opt,name=name,proto3" json:"name,omitempty"`
	Token           string                 `protobuf:"bytes,10,opt,name=token,proto3" json:"token,omitempty"`
	EncodingAesKey  string                 `protobuf:"bytes,11,opt,name=encoding_aes_key,json=encodingAesKey,proto3" json:"encoding_aes_key,omitempty"`
	RobotCode       string                 `protobuf:"bytes,12,opt,name=robot_code,json=robotCode,proto3" json:"robot_code,omitempty"`
	RobotEffectTime string                 `protobuf:"bytes,13,opt,name=robot_effect_time,json=robotEffectTime,proto3" json:"robot_effect_time,omitempty"`
	CorpId          string                 `protobuf:"bytes,14,opt,name=corp_id,json=corpId,proto3" json:"corp_id,omitempty"`
	CorpName        string                 `protobuf:"bytes,15,opt,name=corp_name,json=corpName,proto3" json:"corp_name,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) Reset() {
	*x = ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo{}
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) ProtoMessage() {}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo.ProtoReflect.Descriptor instead.
func (*ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) Descriptor() ([]byte, []int) {
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP(), []int{7, 0}
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetUpdaterId() uint64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetUpdaterName() string {
	if x != nil {
		return x.UpdaterName
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetAgentId() uint32 {
	if x != nil {
		return x.AgentId
	}
	return 0
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetEncodingAesKey() string {
	if x != nil {
		return x.EncodingAesKey
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetRobotCode() string {
	if x != nil {
		return x.RobotCode
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetRobotEffectTime() string {
	if x != nil {
		return x.RobotEffectTime
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetCorpId() string {
	if x != nil {
		return x.CorpId
	}
	return ""
}

func (x *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) GetCorpName() string {
	if x != nil {
		return x.CorpName
	}
	return ""
}

var File_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto protoreflect.FileDescriptor

var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDesc = []byte{
	0x0a, 0x3c, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x2f, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x2e, 0x76, 0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x73, 0x0a,
	0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c,
	0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x63, 0x6f,
	0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x65, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x65, 0x73, 0x4b,
	0x65, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x22, 0x31, 0x0a, 0x1f, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x62, 0x65,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x83, 0x01, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x2a, 0x0a, 0x11, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x36, 0x0a, 0x24, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65,
	0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x02, 0x69, 0x64, 0x22, 0x30, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xe4, 0x03, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x54, 0x6f, 0x62,
	0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74,
	0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x28, 0x0a, 0x10, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x65,
	0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x41, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x5f, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x72, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa3, 0x01, 0x0a,
	0x1f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1b, 0x0a, 0x04, 0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07,
	0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04,
	0x2a, 0x02, 0x28, 0x01, 0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72,
	0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6f, 0x72, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x69,
	0x64, 0x73, 0x22, 0x82, 0x05, 0x0a, 0x1d, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x6c, 0x0a, 0x04, 0x6c, 0x69,
	0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x58, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x2e, 0x54, 0x6f,
	0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xdc, 0x03, 0x0a, 0x14, 0x54, 0x6f, 0x62,
	0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x07, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x28, 0x0a, 0x10,
	0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x61, 0x65, 0x73, 0x5f, 0x6b, 0x65, 0x79,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67,
	0x41, 0x65, 0x73, 0x4b, 0x65, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x5f, 0x65,
	0x66, 0x66, 0x65, 0x63, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63, 0x74, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x07, 0x63, 0x6f, 0x72, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x63, 0x6f, 0x72, 0x70, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f,
	0x72, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63,
	0x6f, 0x72, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x42, 0x49, 0x0a, 0x24, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f,
	0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x76, 0x31, 0x42,
	0x16, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e, 0x2f, 0x76, 0x31, 0x3b,
	0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescOnce sync.Once
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescData = file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDesc
)

func file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescGZIP() []byte {
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescOnce.Do(func() {
		file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescData)
	})
	return file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDescData
}

var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_goTypes = []any{
	(*UpdateTobeDevelopedAppInfoRequest)(nil),                  // 0: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRequest
	(*UpdateTobeDevelopedAppInfoReply)(nil),                    // 1: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoReply
	(*UpdateTobeDevelopedAppInfoRobotRequest)(nil),             // 2: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRobotRequest
	(*UpdateTobeDevelopedAppInfoRobotReply)(nil),               // 3: wx.api.wx.tobe_developed_app_info.v1.UpdateTobeDevelopedAppInfoRobotReply
	(*GetTobeDevelopedAppInfoRequest)(nil),                     // 4: wx.api.wx.tobe_developed_app_info.v1.GetTobeDevelopedAppInfoRequest
	(*GetTobeDevelopedAppInfoReply)(nil),                       // 5: wx.api.wx.tobe_developed_app_info.v1.GetTobeDevelopedAppInfoReply
	(*ListTobeDevelopedAppInfoRequest)(nil),                    // 6: wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoRequest
	(*ListTobeDevelopedAppInfoReply)(nil),                      // 7: wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoReply
	(*ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo)(nil), // 8: wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoReply.TobeDevelopedAppInfo
}
var file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_depIdxs = []int32{
	8, // 0: wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoReply.list:type_name -> wx.api.wx.tobe_developed_app_info.v1.ListTobeDevelopedAppInfoReply.TobeDevelopedAppInfo
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_init() }
func file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_init() {
	if File_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_goTypes,
		DependencyIndexes: file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_depIdxs,
		MessageInfos:      file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_msgTypes,
	}.Build()
	File_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto = out.File
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_rawDesc = nil
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_goTypes = nil
	file_api_wx_tobe_developed_app_info_tobe_developed_app_info_proto_depIdxs = nil
}
