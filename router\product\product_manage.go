package product

import (
	web "hcscm/server/product"
	"hcscm/server/system"
)

func InitFpmInOrder(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")

	// 成品采购入库单(成品采购进仓单)
	{
		fpmPrcInOrder := product.Group("fpmPrcInOrder")
		fpmPrcInOrder.POSTNeedAuth("新增", "addFpmPrcInOrder", web.AddFpmPrcInOrder)
		fpmPrcInOrder.PUTNeedAuth("更新", "updateFpmPrcInOrder", web.UpdateFpmPrcInOrder)
		fpmPrcInOrder.PUTNeedAuth("更新采购业务状态", "updateFpmPrcInOrderBusinessClose", web.UpdateFpmPrcInOrderBusinessClose)
		fpmPrcInOrder.PUTNeedAuth("更新状态消审", "updateFpmPrcInOrderStatusWait", web.UpdateFpmPrcInOrderStatusWait)
		fpmPrcInOrder.PUTNeedAuth("更新状态审核", "updateFpmPrcInOrderStatusPass", web.UpdateFpmPrcInOrderStatusPass)
		fpmPrcInOrder.PUTNeedAuth("更新状态作废", "updateFpmPrcInOrderStatusCancel", web.UpdateFpmPrcInOrderStatusCancel)
		fpmPrcInOrder.PUTNeedAuth("更新状态驳回", "updateFpmPrcInOrderStatusReject", web.UpdateFpmPrcInOrderStatusReject)
		fpmPrcInOrder.GET("获取", "getFpmPrcInOrder", web.GetFpmPrcInOrder)
		fpmPrcInOrder.GET("获取列表", "getFpmPrcInOrderList", web.GetFpmPrcInOrderList)
		fpmPrcInOrder.GET("获取列表(及详情)", "getFpmPrcInOrderListAndDetails", web.GetFpmPrcInOrderListAndDetails)
	}

	// 成品采购退货出库单
	{
		fpmPrtOutOrder := product.Group("fpmPrtOutOrder")
		fpmPrtOutOrder.POSTNeedAuth("新增", "addFpmPrtOutOrder", web.AddFpmPrtOutOrder)
		fpmPrtOutOrder.PUTNeedAuth("更新", "updateFpmPrtOutOrder", web.UpdateFpmPrtOutOrder)
		fpmPrtOutOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmPrtOutOrderBusinessClose)
		fpmPrtOutOrder.PUTNeedAuth("更新状态消审", "updateFpmPrtOutOrderStatusWait", web.UpdateFpmPrtOutOrderStatusWait)
		fpmPrtOutOrder.PUTNeedAuth("更新状态审核", "updateFpmPrtOutOrderStatusPass", web.UpdateFpmPrtOutOrderStatusPass)
		fpmPrtOutOrder.PUTNeedAuth("更新状态作废", "updateFpmPrtOutOrderStatusCancel", web.UpdateFpmPrtOutOrderStatusCancel)
		fpmPrtOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmPrtOutOrderStatusReject", web.UpdateFpmPrtOutOrderStatusReject)
		// fpmPrtOutOrder.DELETENeedAuth("删除", "deleteFpmPrtOutOrder", web.DeleteFpmPrtOutOrder)
		fpmPrtOutOrder.GET("获取", "getFpmPrtOutOrder", web.GetFpmPrtOutOrder)
		fpmPrtOutOrder.GET("获取列表", "getFpmPrtOutOrderList", web.GetFpmPrtOutOrderList)
	}

	// 内部调拨入库单
	{
		fpmPrcInOrder := product.Group("fpmInternalAllocateInOrder")
		// fpmPrcInOrder.POSTNeedAuth("新增", "addFpmInternalAllocateInOrder", web.AddFpmInternalAllocateInOrder)
		fpmPrcInOrder.PUTNeedAuth("更新", "updateFpmInternalAllocateInOrder", web.UpdateFpmInternalAllocateInOrder)
		fpmPrcInOrder.PUTNeedAuth("更新采购业务状态", "updateFpmInternalAllocateInOrderBusinessClose", web.UpdateFpmInternalAllocateInOrderBusinessClose)
		fpmPrcInOrder.PUTNeedAuth("更新状态消审", "updateFpmInternalAllocateInOrderStatusWait", web.UpdateFpmInternalAllocateInOrderStatusWait)
		fpmPrcInOrder.PUTNeedAuth("更新状态审核", "updateFpmInternalAllocateInOrderStatusPass", web.UpdateFpmInternalAllocateInOrderStatusPass)
		fpmPrcInOrder.PUTNeedAuth("更新状态作废", "updateFpmInternalAllocateInOrderStatusCancel", web.UpdateFpmInternalAllocateInOrderStatusCancel)
		fpmPrcInOrder.PUTNeedAuth("更新状态驳回", "updateFpmInternalAllocateInOrderStatusReject", web.UpdateFpmInternalAllocateInOrderStatusReject)
		fpmPrcInOrder.GET("获取", "getFpmInternalAllocateInOrder", web.GetFpmInternalAllocateInOrder)
		fpmPrcInOrder.GET("获取列表", "getFpmInternalAllocateInOrderList", web.GetFpmInternalAllocateInOrderList)
	}

	// 内部调拨出仓单
	{
		fpmAllocateOutOrder := product.Group("fpmInternalAllocateOutOrder")
		fpmAllocateOutOrder.POSTNeedAuth("新增", "addFpmInternalAllocateOutOrder", web.AddFpmInternalAllocateOutOrder)
		fpmAllocateOutOrder.PUTNeedAuth("更新", "updateFpmInternalAllocateOutOrder", web.UpdateFpmInternalAllocateOutOrder)
		fpmAllocateOutOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmInternalAllocateOutOrderBusinessClose)
		fpmAllocateOutOrder.PUTNeedAuth("更新状态消审", "updateFpmInternalAllocateOutOrderStatusWait", web.UpdateFpmInternalAllocateOutOrderStatusWait)
		fpmAllocateOutOrder.PUTNeedAuth("更新状态审核", "updateFpmInternalAllocateOutOrderStatusPass", web.UpdateFpmInternalAllocateOutOrderStatusPass)
		fpmAllocateOutOrder.PUTNeedAuth("更新状态作废", "updateFpmInternalAllocateOutOrderStatusCancel", web.UpdateFpmInternalAllocateOutOrderStatusCancel)
		fpmAllocateOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmInternalAllocateOutOrderStatusReject", web.UpdateFpmInternalAllocateOutOrderStatusReject)
		fpmAllocateOutOrder.GET("获取", "getFpmInternalAllocateOutOrder", web.GetFpmInternalAllocateOutOrder)
		fpmAllocateOutOrder.GET("获取列表", "getFpmInternalAllocateOutOrderList", web.GetFpmInternalAllocateOutOrderList)
	}

	// 成品其他入库单
	{
		FpmOtherInOrder := product.Group("fpmOtherInOrder")
		FpmOtherInOrder.POSTNeedAuth("新增", "addFpmOtherInOrder", web.AddFpmOtherInOrder)
		FpmOtherInOrder.PUTNeedAuth("更新", "updateFpmOtherInOrder", web.UpdateFpmOtherInOrder)
		FpmOtherInOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmOtherInOrderBusinessClose)
		FpmOtherInOrder.PUTNeedAuth("更新状态消审", "updateFpmOtherInOrderStatusWait", web.UpdateFpmOtherInOrderStatusWait)
		FpmOtherInOrder.PUTNeedAuth("更新状态审核", "updateFpmOtherInOrderStatusPass", web.UpdateFpmOtherInOrderStatusPass)
		FpmOtherInOrder.PUTNeedAuth("更新状态作废", "updateFpmOtherInOrderStatusCancel", web.UpdateFpmOtherInOrderStatusCancel)
		FpmOtherInOrder.PUTNeedAuth("更新状态驳回", "updateFpmOtherInOrderStatusReject", web.UpdateFpmOtherInOrderStatusReject)
		// FpmOtherInOrder.DELETENeedAuth("删除", "deleteFpmOtherInOrder", web.DeleteFpmOtherInOrder)
		FpmOtherInOrder.GET("获取", "getFpmOtherInOrder", web.GetFpmOtherInOrder)
		FpmOtherInOrder.GET("获取列表", "getFpmOtherInOrderList", web.GetFpmOtherInOrderList)
		FpmOtherInOrder.GET("获取列表(及详情)", "getFpmOtherInOrderListAndDetails", web.GetFpmOtherInOrderListAndDetails)
	}

	// 成品其他出库单
	{
		FpmOtherOutOrder := product.Group("fpmOtherOutOrder")
		FpmOtherOutOrder.POSTNeedAuth("新增", "addFpmOtherOutOrder", web.AddFpmOtherOutOrder)
		FpmOtherOutOrder.PUTNeedAuth("更新", "updateFpmOtherOutOrder", web.UpdateFpmOtherOutOrder)
		FpmOtherOutOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmOtherOutOrderBusinessClose)
		FpmOtherOutOrder.PUTNeedAuth("更新状态消审", "updateFpmOtherOutOrderStatusWait", web.UpdateFpmOtherOutOrderStatusWait)
		FpmOtherOutOrder.PUTNeedAuth("更新状态审核", "updateFpmOtherOutOrderStatusPass", web.UpdateFpmOtherOutOrderStatusPass)
		FpmOtherOutOrder.PUTNeedAuth("更新状态作废", "updateFpmOtherOutOrderStatusCancel", web.UpdateFpmOtherOutOrderStatusCancel)
		FpmOtherOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmOtherOutOrderStatusReject", web.UpdateFpmOtherOutOrderStatusReject)
		// FpmOtherOutOrder.DELETENeedAuth("删除", "deleteFpmOtherOutOrder", web.DeleteFpmOtherOutOrder)
		FpmOtherOutOrder.GET("获取", "getFpmOtherOutOrder", web.GetFpmOtherOutOrder)
		FpmOtherOutOrder.GET("获取列表", "getFpmOtherOutOrderList", web.GetFpmOtherOutOrderList)
	}

	// 成品加工入库单
	{
		fpmProcessInOrder := product.Group("fpmProcessInOrder")
		fpmProcessInOrder.POSTNeedAuth("新增", "addFpmProcessInOrder", web.AddFpmProcessInOrder)
		fpmProcessInOrder.PUTNeedAuth("更新", "updateFpmProcessInOrder", web.UpdateFpmProcessInOrder)
		fpmProcessInOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmProcessInOrderBusinessClose)
		fpmProcessInOrder.PUTNeedAuth("更新状态消审", "updateFpmProcessInOrderStatusWait", web.UpdateFpmProcessInOrderStatusWait)
		fpmProcessInOrder.PUTNeedAuth("更新状态审核", "updateFpmProcessInOrderStatusPass", web.UpdateFpmProcessInOrderStatusPass)
		fpmProcessInOrder.PUTNeedAuth("更新状态作废", "updateFpmProcessInOrderStatusCancel", web.UpdateFpmProcessInOrderStatusCancel)
		fpmProcessInOrder.PUTNeedAuth("更新状态驳回", "updateFpmProcessInOrderStatusReject", web.UpdateFpmProcessInOrderStatusReject)
		fpmProcessInOrder.GET("获取", "getFpmProcessInOrder", web.GetFpmProcessInOrder)
		fpmProcessInOrder.GET("获取列表", "getFpmProcessInOrderList", web.GetFpmProcessInOrderList)
		fpmProcessInOrder.GET("根据染整通知单id获取分录行", "getFpmProcessInOrderItemListUseDyeEnum", web.GetFpmProcessInOrderItemListUseDyeEnum)
		fpmProcessInOrder.PUT("更新用坯成本价", "updateBuoyantWeightPrice", web.UpdateBuoyantWeightPrice)
	}

	// 成品加工退货入库单
	{
		fpmProcessReturnInOrder := product.Group("fpmProcessReturnInOrder")
		fpmProcessReturnInOrder.POSTNeedAuth("新增", "addFpmProcessReturnInOrder", web.AddFpmProcessReturnInOrder)
		fpmProcessReturnInOrder.PUTNeedAuth("更新", "updateFpmProcessReturnInOrder", web.UpdateFpmProcessReturnInOrder)
		fpmProcessReturnInOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmProcessReturnInOrderBusinessClose)
		fpmProcessReturnInOrder.PUTNeedAuth("更新状态消审", "updateFpmProcessReturnInOrderStatusWait", web.UpdateFpmProcessReturnInOrderStatusWait)
		fpmProcessReturnInOrder.PUTNeedAuth("更新状态审核", "updateFpmProcessReturnInOrderStatusPass", web.UpdateFpmProcessReturnInOrderStatusPass)
		fpmProcessReturnInOrder.PUTNeedAuth("更新状态作废", "updateFpmProcessReturnInOrderStatusCancel", web.UpdateFpmProcessReturnInOrderStatusCancel)
		fpmProcessReturnInOrder.PUTNeedAuth("更新状态驳回", "updateFpmProcessReturnInOrderStatusReject", web.UpdateFpmProcessReturnInOrderStatusReject)
		// FpmProcessReturnInOrder.DELETENeedAuth("删除", "deleteFpmProcessReturnInOrder", web.DeleteFpmProcessReturnInOrder)
		fpmProcessReturnInOrder.GET("获取", "getFpmProcessReturnInOrder", web.GetFpmProcessReturnInOrder)
		fpmProcessReturnInOrder.GET("获取列表", "getFpmProcessReturnInOrderList", web.GetFpmProcessReturnInOrderList)
	}

	// 成品加工出仓单
	{
		fpmProcessOutOrder := product.Group("fpmProcessOutOrder")
		fpmProcessOutOrder.POSTNeedAuth("新增", "addFpmProcessOutOrder", web.AddFpmProcessOutOrder)
		fpmProcessOutOrder.PUTNeedAuth("更新", "updateFpmProcessOutOrder", web.UpdateFpmProcessOutOrder)
		fpmProcessOutOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmProcessOutOrderBusinessClose)
		fpmProcessOutOrder.PUTNeedAuth("更新状态消审", "updateFpmProcessOutOrderStatusWait", web.UpdateFpmProcessOutOrderStatusWait)
		fpmProcessOutOrder.PUTNeedAuth("更新状态审核", "updateFpmProcessOutOrderStatusPass", web.UpdateFpmProcessOutOrderStatusPass)
		fpmProcessOutOrder.PUTNeedAuth("更新状态作废", "updateFpmProcessOutOrderStatusCancel", web.UpdateFpmProcessOutOrderStatusCancel)
		fpmProcessOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmProcessOutOrderStatusReject", web.UpdateFpmProcessOutOrderStatusReject)
		// fpmProcessOutOrder.DELETENeedAuth("删除", "deleteFpmProcessOutOrder", web.DeleteFpmProcessOutOrder)
		fpmProcessOutOrder.GET("获取", "getFpmProcessOutOrder", web.GetFpmProcessOutOrder)
		fpmProcessOutOrder.GET("获取列表", "getFpmProcessOutOrderList", web.GetFpmProcessOutOrderList)
		fpmProcessOutOrder.GET("获取成品加工出仓单成品信息列表", "getProcessOutItemEnumList", web.GetProcessOutItemEnumList)
		fpmProcessOutOrder.GET("获取成品加工出仓单成品信息的细码列表", "getProcessOutItemFcEnumList", web.GetProcessOutItemFcEnumList)
	}

	// 销售调拨出仓单
	{
		fpmSaleAllocateOutOrder := product.Group("fpmSaleAllocateOutOrder")
		fpmSaleAllocateOutOrder.POSTNeedAuth("新增", "addFpmSaleAllocateOutOrder", web.AddFpmSaleAllocateOutOrder)
		fpmSaleAllocateOutOrder.PUTNeedAuth("更新", "updateFpmSaleAllocateOutOrder", web.UpdateFpmSaleAllocateOutOrder)
		fpmSaleAllocateOutOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmSaleAllocateOutOrderBusinessClose)
		fpmSaleAllocateOutOrder.PUTNeedAuth("更新状态消审", "updateFpmSaleAllocateOutOrderStatusWait", web.UpdateFpmSaleAllocateOutOrderStatusWait)
		fpmSaleAllocateOutOrder.PUTNeedAuth("更新状态审核", "updateFpmSaleAllocateOutOrderStatusPass", web.UpdateFpmSaleAllocateOutOrderStatusPass)
		fpmSaleAllocateOutOrder.PUTNeedAuth("更新状态作废", "updateFpmSaleAllocateOutOrderStatusCancel", web.UpdateFpmSaleAllocateOutOrderStatusCancel)
		fpmSaleAllocateOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmSaleAllocateOutOrderStatusReject", web.UpdateFpmSaleAllocateOutOrderStatusReject)
		fpmSaleAllocateOutOrder.GET("获取", "getFpmSaleAllocateOutOrder", web.GetFpmSaleAllocateOutOrder)
		fpmSaleAllocateOutOrder.GET("获取列表", "getFpmSaleAllocateOutOrderList", web.GetFpmSaleAllocateOutOrderList)
	}

	// 销售调拨入库单
	{
		fpmSaleAllocateInOrder := product.Group("fpmSaleAllocateInOrder")
		fpmSaleAllocateInOrder.POSTNeedAuth("新增", "addFpmSaleAllocateInOrder", web.AddFpmSaleAllocateInOrder)
		fpmSaleAllocateInOrder.PUTNeedAuth("更新", "updateFpmSaleAllocateInOrder", web.UpdateFpmSaleAllocateInOrder)
		fpmSaleAllocateInOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmSaleAllocateInOrderBusinessClose)
		fpmSaleAllocateInOrder.PUTNeedAuth("更新状态消审", "updateFpmSaleAllocateInOrderStatusWait", web.UpdateFpmSaleAllocateInOrderStatusWait)
		fpmSaleAllocateInOrder.PUTNeedAuth("更新状态审核", "updateFpmSaleAllocateInOrderStatusPass", web.UpdateFpmSaleAllocateInOrderStatusPass)
		fpmSaleAllocateInOrder.PUTNeedAuth("更新状态作废", "updateFpmSaleAllocateInOrderStatusCancel", web.UpdateFpmSaleAllocateInOrderStatusCancel)
		fpmSaleAllocateInOrder.PUTNeedAuth("更新状态驳回", "updateFpmSaleAllocateInOrderStatusReject", web.UpdateFpmSaleAllocateInOrderStatusReject)
		fpmSaleAllocateInOrder.GET("获取", "getFpmSaleAllocateInOrder", web.GetFpmSaleAllocateInOrder)
		fpmSaleAllocateInOrder.GET("获取列表", "getFpmSaleAllocateInOrderList", web.GetFpmSaleAllocateInOrderList)
	}

	// 销售退货入库单
	{
		fpmSaleReturnInOrder := product.Group("fpmSaleReturnInOrder")
		fpmSaleReturnInOrder.POSTNeedAuth("新增", "addFpmSaleReturnInOrder", web.AddFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.PUTNeedAuth("更新", "updateFpmSaleReturnInOrder", web.UpdateFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmSaleReturnInOrderBusinessClose)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态消审", "updateFpmSaleReturnInOrderStatusWait", web.UpdateFpmSaleReturnInOrderStatusWait)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态审核", "updateFpmSaleReturnInOrderStatusPass", web.UpdateFpmSaleReturnInOrderStatusPass)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态作废", "updateFpmSaleReturnInOrderStatusCancel", web.UpdateFpmSaleReturnInOrderStatusCancel)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态驳回", "updateFpmSaleReturnInOrderStatusReject", web.UpdateFpmSaleReturnInOrderStatusReject)
		fpmSaleReturnInOrder.GET("获取", "getFpmSaleReturnInOrder", web.GetFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.GET("获取列表", "getFpmSaleReturnInOrderList", web.GetFpmSaleReturnInOrderList)
		fpmSaleReturnInOrder.GET("获取成品信息枚举列表", "getFpmSaleReturnInOrderItemEnumList", web.GetFpmSaleReturnInOrderItemEnumList)
	}

	// 成品销售出仓单
	{
		fpmSaleOutOrder := product.Group("fpmSaleOutOrder")
		fpmSaleOutOrder.POSTNeedAuth("新增", "addFpmSaleOutOrder", web.AddFpmSaleOutOrder)
		fpmSaleOutOrder.PUTNeedAuth("更新", "updateFpmSaleOutOrder", web.UpdateFpmSaleOutOrder)
		fpmSaleOutOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmSaleOutOrderBusinessClose)
		fpmSaleOutOrder.PUTNeedAuth("更新状态消审", "updateFpmSaleOutOrderStatusWait", web.UpdateFpmSaleOutOrderStatusWait)
		fpmSaleOutOrder.PUTNeedAuth("更新状态审核", "updateFpmSaleOutOrderStatusPass", web.UpdateFpmSaleOutOrderStatusPass)
		fpmSaleOutOrder.PUTNeedAuth("更新状态作废", "updateFpmSaleOutOrderStatusCancel", web.UpdateFpmSaleOutOrderStatusCancel)
		fpmSaleOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmSaleOutOrderStatusReject", web.UpdateFpmSaleOutOrderStatusReject)
		// fpmSaleOutOrder.DELETENeedAuth("删除", "deleteFpmSaleOutOrder", web.DeleteFpmSaleOutOrder)
		fpmSaleOutOrder.GET("获取", "getFpmSaleOutOrder", web.GetFpmSaleOutOrder)
		fpmSaleOutOrder.GET("获取列表", "getFpmSaleOutOrderList", web.GetFpmSaleOutOrderList)
	}

	// 成品扣款出库单
	{
		FpmDeductionOutOrder := product.Group("fpmDeductionOutOrder")
		FpmDeductionOutOrder.POSTNeedAuth("新增", "addFpmDeductionOutOrder", web.AddFpmDeductionOutOrder)
		FpmDeductionOutOrder.PUTNeedAuth("更新", "updateFpmDeductionOutOrder", web.UpdateFpmDeductionOutOrder)
		FpmDeductionOutOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmDeductionOutOrderBusinessClose)
		FpmDeductionOutOrder.PUTNeedAuth("更新状态消审", "updateFpmDeductionOutOrderStatusWait", web.UpdateFpmDeductionOutOrderStatusWait)
		FpmDeductionOutOrder.PUTNeedAuth("更新状态审核", "updateFpmDeductionOutOrderStatusPass", web.UpdateFpmDeductionOutOrderStatusPass)
		FpmDeductionOutOrder.PUTNeedAuth("更新状态作废", "updateFpmDeductionOutOrderStatusCancel", web.UpdateFpmDeductionOutOrderStatusCancel)
		FpmDeductionOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmDeductionOutOrderStatusReject", web.UpdateFpmDeductionOutOrderStatusReject)
		// FpmDeductionOutOrder.DELETENeedAuth("删除", "deleteFpmDeductionOutOrder", web.DeleteFpmDeductionOutOrder)
		FpmDeductionOutOrder.GET("获取", "getFpmDeductionOutOrder", web.GetFpmDeductionOutOrder)
		FpmDeductionOutOrder.GET("获取列表", "getFpmDeductionOutOrderList", web.GetFpmDeductionOutOrderList)
	}

	// 预约单
	{
		fpmOutReservationOrder := product.Group("fpmOutReservationOrder")
		fpmOutReservationOrder.POSTNeedAuth("新增", "addFpmOutReservationOrder", web.AddFpmOutReservationOrder)
		fpmOutReservationOrder.PUTNeedAuth("更新", "updateFpmOutReservationOrder", web.UpdateFpmOutReservationOrder)
		fpmOutReservationOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmOutReservationOrderBusinessClose)
		fpmOutReservationOrder.PUTNeedAuth("更新状态消审", "updateFpmOutReservationOrderStatusWait", web.UpdateFpmOutReservationOrderStatusWait)
		fpmOutReservationOrder.PUTNeedAuth("更新状态审核", "updateFpmOutReservationOrderStatusPass", web.UpdateFpmOutReservationOrderStatusPass)
		fpmOutReservationOrder.PUTNeedAuth("更新状态作废", "updateFpmOutReservationOrderStatusCancel", web.UpdateFpmOutReservationOrderStatusCancel)
		fpmOutReservationOrder.PUTNeedAuth("更新状态驳回", "updateFpmOutReservationOrderStatusReject", web.UpdateFpmOutReservationOrderStatusReject)
		fpmOutReservationOrder.GET("获取", "getFpmOutReservationOrder", web.GetFpmOutReservationOrder)
		fpmOutReservationOrder.GET("获取列表", "getFpmOutReservationOrderList", web.GetFpmOutReservationOrderList)
	}

	// 配布单
	{
		fpmArrangeOrder := product.Group("fpmArrangeOrder")
		fpmArrangeOrder.PUTNeedAuth("更新", "updateFpmArrangeOrder", web.UpdateFpmArrangeOrder)
		fpmArrangeOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmArrangeOrderBusinessClose)
		fpmArrangeOrder.PUTNeedAuth("更新状态消审", "updateFpmArrangeOrderStatusWait", web.UpdateFpmArrangeOrderStatusWait)
		fpmArrangeOrder.PUTNeedAuth("更新状态审核", "updateFpmArrangeOrderStatusPass", web.UpdateFpmArrangeOrderStatusPass)
		fpmArrangeOrder.PUTNeedAuth("更新状态作废", "updateFpmArrangeOrderStatusCancel", web.UpdateFpmArrangeOrderStatusCancel)
		fpmArrangeOrder.PUTNeedAuth("更新状态驳回", "updateFpmArrangeOrderStatusReject", web.UpdateFpmArrangeOrderStatusReject)
		fpmArrangeOrder.GET("获取", "getFpmArrangeOrder", web.GetFpmArrangeOrder)
		fpmArrangeOrder.GET("获取其他地方使用", "getFpmArrangeOrderEnum", web.GetFpmArrangeOrderEnum)
		fpmArrangeOrder.GET("获取列表", "getFpmArrangeOrderList", web.GetFpmArrangeOrderList)
		fpmArrangeOrder.PUTNeedAuth("确认出仓", "outFpmArrangeOrder", web.OutFpmArrangeOrder)
		fpmArrangeOrder.PUTNeedAuth("变更判断", "judgeChangeArrangeOrder", web.JudgeChangeArrangeOrder)
		fpmArrangeOrder.GET("打印现货配布单", "printFpmArrangeOrder", web.PrintFpmArrangeOrder)
	}

	// 销售配布变更单
	{
		fpmChangeArrangeOrder := product.Group("fpmChangeArrangeOrder")
		fpmChangeArrangeOrder.POSTNeedAuth("新增", "addFpmChangeArrangeOrder", web.AddFpmChangeArrangeOrder)
		fpmChangeArrangeOrder.PUTNeedAuth("更新", "updateFpmChangeArrangeOrder", web.UpdateFpmChangeArrangeOrder)
		fpmChangeArrangeOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmChangeArrangeOrderBusinessClose)
		fpmChangeArrangeOrder.PUTNeedAuth("更新状态消审", "updateFpmChangeArrangeOrderStatusWait", web.UpdateFpmChangeArrangeOrderStatusWait)
		fpmChangeArrangeOrder.PUTNeedAuth("更新状态审核", "updateFpmChangeArrangeOrderStatusPass", web.UpdateFpmChangeArrangeOrderStatusPass)
		fpmChangeArrangeOrder.PUTNeedAuth("更新状态作废", "updateFpmChangeArrangeOrderStatusCancel", web.UpdateFpmChangeArrangeOrderStatusCancel)
		fpmChangeArrangeOrder.PUTNeedAuth("更新状态驳回", "updateFpmChangeArrangeOrderStatusReject", web.UpdateFpmChangeArrangeOrderStatusReject)
		// fpmChangeArrangeOrder.DELETENeedAuth("删除", "deleteFpmChangeArrangeOrder", web.DeleteFpmChangeArrangeOrder)
		fpmChangeArrangeOrder.GET("获取", "getFpmChangeArrangeOrder", web.GetFpmChangeArrangeOrder)
		fpmChangeArrangeOrder.GET("获取列表", "getFpmChangeArrangeOrderList", web.GetFpmChangeArrangeOrderList)
	}
	// 报表
	{
		report := product.Group("productReport")
		report.GET("盘点进出仓信息", "getCheckOrderItemReport", web.GetCheckOrderItemReport)
		report.GET("获取成品进仓报表", "getProductInReport", web.GetProductInReport)
		report.GET("获取成品出仓报表", "getProductOutReport", web.GetProductOutReport)
	}
}

func MPInitFpmInOrder(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	// 成品采购进仓单
	{
		fpmPrcInOrder := product.Group("fpmPrcInOrder")
		fpmPrcInOrder.POSTNeedAuth("新增", "addFpmPrcInOrder", web.AddFpmPrcInOrder)
		fpmPrcInOrder.PUTNeedAuth("更新", "updateFpmPrcInOrder", web.UpdateFpmPrcInOrder)
		fpmPrcInOrder.PUTNeedAuth("更新采购业务状态", "updateFpmPrcInOrderBusinessClose", web.UpdateFpmPrcInOrderBusinessClose)
		fpmPrcInOrder.PUTNeedAuth("更新状态作废", "updateFpmPrcInOrderStatusCancel", web.UpdateFpmPrcInOrderStatusCancel)
		fpmPrcInOrder.PUTNeedAuth("更新状态审核", "updateFpmPrcInOrderStatusPass", web.MPUpdateFpmPrcInOrderStatusPass)
		fpmPrcInOrder.PUTNeedAuth("更新状态消审", "updateFpmPrcInOrderStatusWait", web.UpdateFpmPrcInOrderStatusWait)
		fpmPrcInOrder.PUTNeedAuth("更新状态驳回", "updateFpmPrcInOrderStatusReject", web.UpdateFpmPrcInOrderStatusReject)
		fpmPrcInOrder.GET("获取", "getFpmPrcInOrder", web.GetFpmPrcInOrder)
		fpmPrcInOrder.GET("获取列表", "getMPFpmPrcInOrderList", web.GetMPFpmPrcInOrderList)
		fpmPrcInOrder.GET("获取成品下拉列表(包含细码)", "getFpmPrcInOrderDetails", web.GetFpmPrcInOrderDetails)
	}
	// 成品采购退货出库单
	{
		fpmPrtOutOrder := product.Group("fpmPrtOutOrder")
		fpmPrtOutOrder.POSTNeedAuth("新增", "addFpmPrtOutOrder", web.AddFpmPrtOutOrder)
		fpmPrtOutOrder.PUTNeedAuth("更新", "updateFpmPrtOutOrder", web.UpdateFpmPrtOutOrder)
		fpmPrtOutOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmPrtOutOrderBusinessClose)
		fpmPrtOutOrder.PUTNeedAuth("更新状态消审", "updateFpmPrtOutOrderStatusWait", web.UpdateFpmPrtOutOrderStatusWait)
		fpmPrtOutOrder.PUTNeedAuth("更新状态审核", "updateFpmPrtOutOrderStatusPass", web.MPUpdateFpmPrtOutOrderStatusPass)
		fpmPrtOutOrder.PUTNeedAuth("更新状态作废", "updateFpmPrtOutOrderStatusCancel", web.UpdateFpmPrtOutOrderStatusCancel)
		fpmPrtOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmPrtOutOrderStatusReject", web.UpdateFpmPrtOutOrderStatusReject)
		fpmPrtOutOrder.GET("获取", "getFpmPrtOutOrder", web.GetFpmPrtOutOrder)
		fpmPrtOutOrder.GET("获取列表", "getFpmPrtOutOrderList", web.GetFpmPrtOutOrderList)
	}
	// 成品销售出仓单
	{
		fpmSaleOutOrder := product.Group("fpmSaleOutOrder")
		fpmSaleOutOrder.POSTNeedAuth("新增", "addFpmSaleOutOrder", web.AddFpmSaleOutOrder)
		fpmSaleOutOrder.PUTNeedAuth("更新", "updateFpmSaleOutOrder", web.UpdateFpmSaleOutOrder)
		fpmSaleOutOrder.PUTNeedAuth("更新采购业务状态", "updateFpmSaleOutOrderBusinessClose", web.UpdateFpmSaleOutOrderBusinessClose)
		fpmSaleOutOrder.PUTNeedAuth("更新状态消审", "mpupdateFpmSaleOutOrderStatusWait", web.MPUpdateFpmSaleOutOrderStatusWait)
		fpmSaleOutOrder.PUTNeedAuth("更新状态审核", "mpupdateFpmSaleOutOrderStatusPass", web.UpdateFpmSaleOutOrderStatusPass)
		fpmSaleOutOrder.PUTNeedAuth("更新状态作废", "mpupdateFpmSaleOutOrderStatusCancel", web.MPUpdateFpmSaleOutOrderStatusCancel)
		fpmSaleOutOrder.PUTNeedAuth("更新状态驳回", "updateFpmSaleOutOrderStatusReject", web.UpdateFpmSaleOutOrderStatusReject)
		fpmSaleOutOrder.GET("获取", "getFpmSaleOutOrder", web.GetFpmSaleOutOrder)
		fpmSaleOutOrder.GET("获取列表", "getFpmSaleOutOrderList", web.GetFpmSaleOutOrderList)
	}
	// 销售退货进仓单
	{
		fpmSaleReturnInOrder := product.Group("fpmSaleReturnInOrder")
		fpmSaleReturnInOrder.POSTNeedAuth("新增", "addFpmSaleReturnInOrder", web.AddFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.PUTNeedAuth("更新", "updateFpmSaleReturnInOrder", web.UpdateFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.PUTNeedAuth("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmSaleReturnInOrderBusinessClose)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态消审", "updateFpmSaleReturnInOrderStatusWait", web.UpdateFpmSaleReturnInOrderStatusWait)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态审核", "updateFpmSaleReturnInOrderStatusPass", web.UpdateFpmSaleReturnInOrderStatusPass)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态作废", "updateFpmSaleReturnInOrderStatusCancel", web.UpdateFpmSaleReturnInOrderStatusCancel)
		fpmSaleReturnInOrder.PUTNeedAuth("更新状态驳回", "updateFpmSaleReturnInOrderStatusReject", web.UpdateFpmSaleReturnInOrderStatusReject)
		fpmSaleReturnInOrder.GET("获取", "getFpmSaleReturnInOrder", web.GetFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.GET("获取列表", "getFpmSaleReturnInOrderList", web.GetFpmSaleReturnInOrderList)
		fpmSaleReturnInOrder.GET("获取成品信息枚举列表", "getFpmSaleReturnInOrderItemEnumList", web.GetFpmSaleReturnInOrderItemEnumList)
	}
	// 成品其他入库单
	{
		FpmOtherInOrder := product.Group("fpmOtherInOrder")
		FpmOtherInOrder.POSTNeedAuth("新增", "addFpmOtherInOrder", web.AddFpmOtherInOrder)
		FpmOtherInOrder.PUTNeedAuth("更新", "updateFpmOtherInOrder", web.UpdateFpmOtherInOrder)
		FpmOtherInOrder.PUTNeedAuth("更新采购业务状态", "updateBusinessClose", web.UpdateFpmOtherInOrderBusinessClose)
		FpmOtherInOrder.PUTNeedAuth("更新状态消审", "updateFpmOtherInOrderStatusWait", web.UpdateFpmOtherInOrderStatusWait)
		FpmOtherInOrder.PUTNeedAuth("更新状态审核", "updateFpmOtherInOrderStatusPass", web.UpdateFpmOtherInOrderStatusPass)
		FpmOtherInOrder.PUTNeedAuth("更新状态作废", "updateFpmOtherInOrderStatusCancel", web.UpdateFpmOtherInOrderStatusCancel)
		FpmOtherInOrder.PUTNeedAuth("更新状态驳回", "updateFpmOtherInOrderStatusReject", web.UpdateFpmOtherInOrderStatusReject)
		FpmOtherInOrder.GET("获取", "getFpmOtherInOrder", web.GetFpmOtherInOrder)
		// FpmOtherInOrder.GET("获取列表", "getFpmOtherInOrderList", web.GetFpmOtherInOrderList)
		FpmOtherInOrder.GET("获取列表", "getMpFpmOtherInOrderList", web.GetMPFpmInOrderList)
	}
	// 成品配布单
	{
		fpmArrangeOrder := product.Group("fpmArrangeOrder")
		fpmArrangeOrder.GET("获取", "getFpmArrangeOrder", web.GetFpmArrangeOrder)
		fpmArrangeOrder.GET("获取列表", "getFpmArrangeOrderList", web.GetFpmArrangeOrderList)
	}
}

func InitTemporaryRouter(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	product.GET("清洗导入数据遗漏进仓数据", "wash_in_order_for_import", web.WashInOrderForImport)
	product.GET("清洗统一进仓单详情数据", "wash_in_order_item_together", web.WashInOrderItemTogether)
	product.GET("清洗统一进仓单数据", "wash_in_order_together", web.WashInOrderTogether)
	product.GET("清洗统一出仓单详情数据", "wash_out_order_item_together", web.WashOutOrderItemTogether)
	product.GET("清洗统一出仓单数据", "wash_out_order_together", web.WashOutOrderTogether)
}

func H5InitFpmInOrder(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	// 成品采购进仓单
	{
		fpmPrcInOrder := product.Group("fpmPrcInOrder")
		fpmPrcInOrder.POST("新增", "addFpmPrcInOrder", web.AddFpmPrcInOrder)
		fpmPrcInOrder.PUT("更新", "updateFpmPrcInOrder", web.UpdateFpmPrcInOrder)
		fpmPrcInOrder.PUT("更新采购业务状态", "updateFpmPrcInOrderBusinessClose", web.UpdateFpmPrcInOrderBusinessClose)
		fpmPrcInOrder.PUT("更新状态作废", "updateFpmPrcInOrderStatusCancel", web.UpdateFpmPrcInOrderStatusCancel)
		fpmPrcInOrder.PUT("更新状态审核", "updateFpmPrcInOrderStatusPass", web.MPUpdateFpmPrcInOrderStatusPass)
		fpmPrcInOrder.PUT("更新状态消审", "updateFpmPrcInOrderStatusWait", web.UpdateFpmPrcInOrderStatusWait)
		fpmPrcInOrder.PUT("更新状态驳回", "updateFpmPrcInOrderStatusReject", web.UpdateFpmPrcInOrderStatusReject)
		fpmPrcInOrder.GET("获取", "getFpmPrcInOrder", web.GetFpmPrcInOrder)
		fpmPrcInOrder.GET("获取列表", "getMPFpmPrcInOrderList", web.GetMPFpmPrcInOrderList)
		fpmPrcInOrder.GET("获取成品下拉列表(包含细码)", "getFpmPrcInOrderDetails", web.GetFpmPrcInOrderDetails)
	}
	// 成品采购退货出库单
	{
		fpmPrtOutOrder := product.Group("fpmPrtOutOrder")
		fpmPrtOutOrder.POST("新增", "addFpmPrtOutOrder", web.AddFpmPrtOutOrder)
		fpmPrtOutOrder.PUT("更新", "updateFpmPrtOutOrder", web.UpdateFpmPrtOutOrder)
		fpmPrtOutOrder.PUT("更新采购业务状态", "updateBusinessClose", web.UpdateFpmPrtOutOrderBusinessClose)
		fpmPrtOutOrder.PUT("更新状态消审", "updateFpmPrtOutOrderStatusWait", web.UpdateFpmPrtOutOrderStatusWait)
		fpmPrtOutOrder.PUT("更新状态审核", "updateFpmPrtOutOrderStatusPass", web.MPUpdateFpmPrtOutOrderStatusPass)
		fpmPrtOutOrder.PUT("更新状态作废", "updateFpmPrtOutOrderStatusCancel", web.UpdateFpmPrtOutOrderStatusCancel)
		fpmPrtOutOrder.PUT("更新状态驳回", "updateFpmPrtOutOrderStatusReject", web.UpdateFpmPrtOutOrderStatusReject)
		fpmPrtOutOrder.GET("获取", "getFpmPrtOutOrder", web.GetFpmPrtOutOrder)
		fpmPrtOutOrder.GET("获取列表", "getFpmPrtOutOrderList", web.GetFpmPrtOutOrderList)
	}
	// 成品销售出仓单
	{
		fpmSaleOutOrder := product.Group("fpmSaleOutOrder")
		fpmSaleOutOrder.POST("新增", "addFpmSaleOutOrder", web.AddFpmSaleOutOrder)
		fpmSaleOutOrder.PUT("更新", "updateFpmSaleOutOrder", web.UpdateFpmSaleOutOrder)
		fpmSaleOutOrder.PUT("更新采购业务状态", "updateFpmSaleOutOrderBusinessClose", web.UpdateFpmSaleOutOrderBusinessClose)
		fpmSaleOutOrder.PUT("更新状态消审", "mpupdateFpmSaleOutOrderStatusWait", web.MPUpdateFpmSaleOutOrderStatusWait)
		fpmSaleOutOrder.PUT("更新状态审核", "mpupdateFpmSaleOutOrderStatusPass", web.UpdateFpmSaleOutOrderStatusPass)
		fpmSaleOutOrder.PUT("更新状态作废", "mpupdateFpmSaleOutOrderStatusCancel", web.MPUpdateFpmSaleOutOrderStatusCancel)
		fpmSaleOutOrder.PUT("更新状态驳回", "updateFpmSaleOutOrderStatusReject", web.UpdateFpmSaleOutOrderStatusReject)
		fpmSaleOutOrder.GET("获取", "getFpmSaleOutOrder", web.GetFpmSaleOutOrder)
		fpmSaleOutOrder.GET("获取列表", "getFpmSaleOutOrderList", web.GetFpmSaleOutOrderList)
	}
	// 销售退货进仓单
	{
		fpmSaleReturnInOrder := product.Group("fpmSaleReturnInOrder")
		fpmSaleReturnInOrder.POST("新增", "addFpmSaleReturnInOrder", web.AddFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.PUT("更新", "updateFpmSaleReturnInOrder", web.UpdateFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.PUT("更新坯布采购业务状态", "updateBusinessClose", web.UpdateFpmSaleReturnInOrderBusinessClose)
		fpmSaleReturnInOrder.PUT("更新状态消审", "updateFpmSaleReturnInOrderStatusWait", web.UpdateFpmSaleReturnInOrderStatusWait)
		fpmSaleReturnInOrder.PUT("更新状态审核", "updateFpmSaleReturnInOrderStatusPass", web.UpdateFpmSaleReturnInOrderStatusPass)
		fpmSaleReturnInOrder.PUT("更新状态作废", "updateFpmSaleReturnInOrderStatusCancel", web.UpdateFpmSaleReturnInOrderStatusCancel)
		fpmSaleReturnInOrder.PUT("更新状态驳回", "updateFpmSaleReturnInOrderStatusReject", web.UpdateFpmSaleReturnInOrderStatusReject)
		fpmSaleReturnInOrder.GET("获取", "getFpmSaleReturnInOrder", web.GetFpmSaleReturnInOrder)
		fpmSaleReturnInOrder.GET("获取列表", "getFpmSaleReturnInOrderList", web.GetFpmSaleReturnInOrderList)
		fpmSaleReturnInOrder.GET("获取成品信息枚举列表", "getFpmSaleReturnInOrderItemEnumList", web.GetFpmSaleReturnInOrderItemEnumList)
	}
	// 成品其他入库单
	{
		FpmOtherInOrder := product.Group("fpmOtherInOrder")
		FpmOtherInOrder.POST("新增", "addFpmOtherInOrder", web.AddFpmOtherInOrder)
		FpmOtherInOrder.PUT("更新", "updateFpmOtherInOrder", web.UpdateFpmOtherInOrder)
		FpmOtherInOrder.PUT("更新采购业务状态", "updateBusinessClose", web.UpdateFpmOtherInOrderBusinessClose)
		FpmOtherInOrder.PUT("更新状态消审", "updateFpmOtherInOrderStatusWait", web.UpdateFpmOtherInOrderStatusWait)
		FpmOtherInOrder.PUT("更新状态审核", "updateFpmOtherInOrderStatusPass", web.UpdateFpmOtherInOrderStatusPass)
		FpmOtherInOrder.PUT("更新状态作废", "updateFpmOtherInOrderStatusCancel", web.UpdateFpmOtherInOrderStatusCancel)
		FpmOtherInOrder.PUT("更新状态驳回", "updateFpmOtherInOrderStatusReject", web.UpdateFpmOtherInOrderStatusReject)
		FpmOtherInOrder.GET("获取", "getFpmOtherInOrder", web.GetFpmOtherInOrder)
		// FpmOtherInOrder.GET("获取列表", "getFpmOtherInOrderList", web.GetFpmOtherInOrderList)
		FpmOtherInOrder.GET("获取列表", "getMpFpmOtherInOrderList", web.GetMPFpmInOrderList)
	}
	// 成品配布单
	{
		fpmArrangeOrder := product.Group("fpmArrangeOrder")
		fpmArrangeOrder.GET("获取", "getFpmArrangeOrder", web.GetFpmArrangeOrder)
		fpmArrangeOrder.GET("获取列表", "getFpmArrangeOrderList", web.GetFpmArrangeOrderList)
	}
}
