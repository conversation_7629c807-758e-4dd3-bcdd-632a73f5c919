package product

import (
	common "hcscm/common/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFpmQualityCheckParamList []AddFpmQualityCheckParam

func (r AddFpmQualityCheckParamList) Adjust() {

}

type AddFpmQualityCheckParam struct {
	structure_base.Param
	StockId          uint64                          `json:"stock_id"`           // 详细库存id
	QualityCheckDate tools.QueryTime                 `json:"quality_check_date"` // 质检时间
	BarCode          string                          `json:"-"`                  // 条形码
	ProductId        uint64                          `json:"-"`                  // 成品id
	ProductColorId   uint64                          `json:"-"`                  // 成品颜色id
	DefectWeight     int                             `json:"defect_weight"`      // 次布数量KG
	Remark           string                          `json:"remark"`             // 质检备注
	ActuallyWeight   int                             `json:"actually_weight"`    // 实际重量
	EdgeWidth        int                             `json:"edge_width"`         // 包边门幅
	UsefulWidth      int                             `json:"useful_width"`       // 有效门幅
	QcGramWeight     int                             `json:"qc_gram_weight"`     // 质检克重
	DefectItem       []AddFpmQualityCheckDefectParam `json:"defect_item"`        // 质检信息条下的成品信息
}

func (d *AddFpmQualityCheckParam) NeedUpdate() bool {
	if d.DefectWeight > 0 {
		return true
	}
	if d.Remark != "" {
		return true
	}
	if d.ActuallyWeight > 0 {
		return true
	}
	if d.EdgeWidth > 0 {
		return true
	}
	if d.UsefulWidth > 0 {
		return true
	}
	if d.QcGramWeight > 0 {
		return true
	}
	return false
}

type AddFpmQualityCheckData struct {
	structure_base.ResponseData
	Id       uint64 `json:"id"`
	IsActAdd bool
}

type UpdateFpmQualityCheckParam struct {
	structure_base.Param
	Id               uint64          `json:"id"`
	StockId          uint64          `json:"stock_id"`           // 详细库存id
	QualityCheckDate tools.QueryTime `json:"quality_check_date"` // 质检时间
	// BarCode          string          `json:"bar_code"`           // 条形码
	// ProductId        uint64          `json:"product_id"`         // 成品id
	// ProductColorId   uint64          `json:"product_color_id"`   // 成品颜色id
	DefectWeight   int    `json:"defect_weight"`   // 次布数量KG
	Remark         string `json:"remark"`          // 质检备注
	ActuallyWeight int    `json:"actually_weight"` // 实际重量
	EdgeWidth      int    `json:"edge_width"`      // 包边门幅
	UsefulWidth    int    `json:"useful_width"`    // 有效门幅
	QcGramWeight   int    `json:"qc_gram_weight"`  // 质检克重
}

type UpdateFpmQualityCheckData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateFpmQualityCheckStatusData struct {
	structure_base.ResponseData
}

type GetFpmQualityCheckQuery struct {
	structure_base.Query
	Id      uint64 `form:"id"` // id
	StockId uint64 `form:"stock_id"`
}

type GetFpmQualityCheckListQuery struct {
	structure_base.ListQuery
	IsDropList            int             `form:"is_drop_list"`             // 回收站
	StockId               uint64          `form:"stock_id"`                 // 详细库存id
	QualityCheckDate      tools.QueryTime `form:"quality_check_date"`       // 质检时间
	QualityCheckDateBegin tools.QueryTime `form:"quality_check_date_begin"` // 质检时间开始
	QualityCheckDateEnd   tools.QueryTime `form:"quality_check_date_end"`   // 质检时间结束
	BarCode               string          `form:"bar_code"`                 // 条形码
	ProductId             uint64          `form:"product_id"`               // 成品id
	ProductColorId        uint64          `form:"product_color_id"`         // 成品颜色id
	DefectWeight          int             `form:"defect_weight"`            // 次布数量KG
	Remark                string          `form:"remark"`                   // 质检备注
	QualityCheckerId      uint64          `form:"quality_checker_id"`       // 检查员id（关联user.id）
	DepartmentId          uint64          `form:"department_id"`            // 用户所属部门
	IsShowAll             bool            // 展示所有还是只展示当前登录人
	SourceSupplierId      uint64          `form:"supplier_id"`   // 来源供应商id
	VolumeNumber          int             `form:"volume_number"` // 卷号
	DyelotNumber          string          `form:"dyelot_number"` // 缸号

}

func (r GetFpmQualityCheckListQuery) Adjust() {

}

type GetFpmQualityCheckData struct {
	structure_base.RecordData
	StockId                 uint64                           `json:"stock_id"`                  // 详细库存id
	QualityCheckDate        tools.MyTime                     `json:"quality_check_date"`        // 质检时间
	WarehouseInTime         tools.MyTime                     `json:"warehouse_in_time"`         // 到货时间（进仓时间）
	BarCode                 string                           `json:"bar_code"`                  // 条形码
	ProductId               uint64                           `json:"product_id"`                // 成品id
	ProductColorId          uint64                           `json:"product_color_id"`          // 成品颜色id
	DefectWeight            int                              `json:"defect_weight"`             // 次布数量KG
	Remark                  string                           `json:"remark"`                    // 质检备注
	QualityCheckerId        uint64                           `json:"quality_checker_id"`        // 检查员id（关联user.id）
	DepartmentId            uint64                           `json:"department_id"`             // 用户所属部门
	OrderNo                 string                           `json:"order_no"`                  // 单号
	OrderType               common.WarehouseGoodInType       `json:"order_type"`                // 单据类型
	OrderTypeName           string                           `json:"order_type_name"`           // 单据类型名
	ProductCode             string                           `json:"product_code"`              // 成品编号
	ProductName             string                           `json:"product_name"`              // 成品名称
	ProductColorCode        string                           `json:"product_color_code"`        // 成品颜色色号
	ProductColorName        string                           `json:"product_color_name"`        // 成品颜色名称
	DyelotNumber            string                           `json:"dyelot_number"`             // 缸号
	Roll                    int                              `json:"roll"`                      // 条数
	VolumeNumber            int                              `json:"volume_number"`             // 卷号
	Weight                  int                              `json:"weight"`                    // 数量
	DefectMergeStr          string                           `json:"defect_merge_str"`          // 疵点信息
	QualityCheckerName      string                           `json:"quality_checker_name"`      // 质检员名称
	ActuallyWeight          int                              `json:"actually_weight"`           // 实际重量
	EdgeWidth               int                              `json:"edge_width"`                // 包边门幅
	UsefulWidth             int                              `json:"useful_width"`              // 有效门幅
	QcGramWeight            int                              `json:"qc_gram_weight"`            // 质检克重
	SumStockId              uint64                           `json:"sum_stock_id"`              // 汇总库存id
	DetailList              GetFpmQualityCheckDefectDataList `json:"detail_list,omitempty"`     // 疵点信息
	SupplierName            string                           `json:"supplier_name"`             // 供应商名称
	UnitName                string                           `json:"unit_name"`                 // 单位名称
	Ingredient              string                           `json:"ingredient"`                // 成分
	YarnCount               string                           `json:"yarn_count"`                // 纱支
	Density                 string                           `json:"density"`                   // 密度
	WeavingOrganizationName string                           `json:"weaving_organization_name"` // 组织名称
	TotalScore              int                              `json:"total_score"`               // 总评分
	ConvertTotalScore       int                              `json:"convert_total_score"`       // 折算总评分
	IsPass                  bool                             `json:"is_pass"`                   // 是否合格
	IsGenerateReport        int                              `json:"is_generate_report"`        // 是否生成报表
}

type GetFpmQualityCheckDataList []GetFpmQualityCheckData

func (g GetFpmQualityCheckDataList) Adjust() {

}

type DeleteFpmQualityCheckParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmQualityCheckData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type (
	FpmQualityForPrint struct {
		Id                      uint64                      `json:"id"`
		SupplierName            string                      `json:"supplier_name"`               // 供应商名称
		ReceiveDate             tools.MyTime                `json:"receive_date"`                // 到货时间（进仓时间）
		QcDate                  tools.MyTime                `json:"qc_date"`                     // 检验日期
		QualityCheckerName      string                      `json:"quality_checker_name"`        // 检验员
		ProductCode             string                      `json:"product_code"`                // 成品编号
		ProductName             string                      `json:"product_name"`                // 成品名称
		DyelotNumber            string                      `json:"dyelot_number"`               // 染色批号
		ProductColorCode        string                      `json:"product_color_code"`          // 成品颜色色号
		ProductColorName        string                      `json:"product_color_name"`          // 成品颜色名称
		ProductColorCodeAndName string                      `json:"product_color_code_and_name"` // 成品颜色色号+名称
		ProductLevelId          uint64                      `json:"product_level_id"`            // 成品等级id
		ProductLevelName        string                      `json:"product_level_name"`          // 成品等级名称
		Ingredient              string                      `json:"ingredient"`                  // 成分
		TotalRoll               int                         `json:"total_roll"`                  // 总条数
		TotalWeight             int                         `json:"total_weight"`                // 收到总米/码/KG
		QcResult                bool                        `json:"qc_result"`                   // 检验结果
		QcResultName            string                      `json:"qc_result_name"`              // 检验结果名
		GramWeight              string                      `json:"qc_gram_weight"`              // 克重(质检)
		InspectRatio            int                         `json:"inspect_ratio"`               // 检验比例 (检验)
		InspectConvertAvgScore  int                         `json:"inspect_convert_avg_score"`   // 检验折算平均分 (检验)
		InspectTotalWeight      int                         `json:"inspect_total_weight"`        // 抽查总米/码/KG (检验)
		InspectPassRoll         int                         `json:"inspect_pass_roll"`           // 合格卷数 (检验)
		InspectTotalRoll        int                         `json:"inspect_total_roll"`          // 检验总条数 (检验)
		InspectNoPassRoll       int                         `json:"inspect_no_pass_roll"`        // 不合格卷数 (检验)
		InspectAvgUsefulWidth   int                         `json:"inspect_avg_useful_width"`    // 平均有效门幅 (检验)
		InspectAvgEdgeWidth     int                         `json:"inspect_avg_edge_width"`      // 平均包边门幅 (检验)
		InspectAvgGramWeight    int                         `json:"inspect_avg_gram_weight"`     // 平均克重 (检验)
		InspectTotalScore       int                         `json:"inspect_total_score"`         // 总评分 (检验)
		QcItem                  FpmQualityItemForPrintList  `json:"qc_item"`                     // 面料行信息
		DefectList              BasicDefectInfoForPrintList `json:"defect_list"`                 // 疵点识别代号
	}

	// 面料行
	FpmQualityItemForPrint struct {
		Id                  uint64 `json:"id"`
		VolumeNumber        int    `json:"volume_number"`         // 匹号
		StockWeight         int    `json:"stock_weight"`          // （标签）库存数量
		ActuallyWeight      int    `json:"actually_weight"`       // 实际数量
		EdgeWidth           int    `json:"edge_width"`            // 有效门幅
		UsefulWidth         int    `json:"useful_width"`          // 包边门幅
		QcGramWeight        int    `json:"qc_gram_weight"`        // 克重(质检)
		ScoreOneCount       int    `json:"score_one_count"`       // 1分条数
		ScoreTwoCount       int    `json:"score_two_count"`       // 2分条数
		ScoreThreeCount     int    `json:"score_three_count"`     // 3分条数
		ScoreFourCount      int    `json:"score_four_count"`      // 4分条数
		ScoreOneCountStr    string `json:"score_one_count_str"`   // 1分详情内容
		ScoreTwoCountStr    string `json:"score_two_count_str"`   // 2分详情内容
		ScoreThreeCountStr  string `json:"score_three_count_str"` // 3分详情内容
		ScoreFourCountStr   string `json:"score_four_count_str"`  // 4分详情内容
		TotalScore          int    `json:"total_score"`           // 总评分
		ConvertTotalScore   int    `json:"convert_total_score"`   // 折算总评分
		IsPass              bool   `json:"is_pass"`               // 是否合格
		Remark              string `json:"remark"`                // 备注
		MeasurementUnitId   uint64 `json:"measurement_unit_id"`   // 计量单位id
		MeasurementUnitName string `json:"measurement_unit_name"` // 计量单位名
	}
	FpmQualityItemForPrintList []*FpmQualityItemForPrint

	// 疵点识别代号
	BasicDefectInfoForPrint struct {
		Id   uint64 `json:"id"`
		Mark string `json:"mark"` // 代号
		Name string `json:"name"` // 疵点名称
	}
	BasicDefectInfoForPrintList []*BasicDefectInfoForPrint

	RecordStockInfo struct {
		Id                uint64
		DyelotNumber      string
		Roll              int
		Weight            int
		VolumeNumber      int
		MeasurementUnitId uint64
	}
)

func (f FpmQualityForPrint) Adjust() {
}

type FpmQualityItemForStockId struct {
	Id             uint64 `json:"id"`
	ActuallyWeight int    `json:"actually_weight"` // 实际重量
	EdgeWidth      int    `json:"edge_width"`      // 包边门幅
	UsefulWidth    int    `json:"useful_width"`    // 有效门幅
	DefectWeight   int    `json:"defect_weight"`   // 次布数量KG
	QcGramWeight   int    `json:"qc_gram_weight"`  // 质检克重
	Remark         string `json:"remark"`          // 质检备注
}

func (f FpmQualityItemForStockId) Adjust() {
}

// 根据缸号获取质检信息列表
type GetFpmQualityCheckDyelotNumberListQuery struct {
	structure_base.ListQuery
	DyelotNumber         string               `json:"dyelot_number" form:"dyelot_number"`                     // 缸号
	SupplierId           uint64               `json:"supplier_id" form:"supplier_id"`                         // 供应商id
	ProductId            uint64               `json:"product_id" form:"product_id"`                           // 成品id
	ProductColorId       uint64               `json:"product_color_id" form:"product_color_id"`               // 成品颜色id
	CustomerId           uint64               `json:"customer_id" form:"customer_id"`                         // 供应商id
	Type                 int                  `json:"type" form:"type"`                                       // 查看类型1仅看已质检2仅看未质检
	StartWarehouseInTime tools.QueryTime      `json:"start_warehouse_in_time" form:"start_warehouse_in_time"` // 开始进仓时间
	EndWarehouseInTime   tools.QueryTime      `json:"end_warehouse_in_time" form:"end_warehouse_in_time"`     // 结束进仓时间
	StockShowType        common.StockShowType `json:"stock_show_type" form:"stock_show_type"`                 // 显示方式
}

type GetFpmQualityCheckDyelotNumberData struct {
	structure_base.ResponseData
	DyelotNumber       string                     `json:"dyelot_number"`                // 缸号
	SupplierId         uint64                     `json:"supplier_id"`                  // 供应商id
	SupplierName       string                     `json:"supplier_name"`                // 供应商名称
	CustomerId         uint64                     `json:"customer_id"`                  // 客户id
	TotalRoll          int                        `json:"total_roll"`                   // 缸匹数
	TotalWeight        int                        `json:"total_weight"`                 // 缸数量
	InspectTotalRoll   int                        `json:"inspect_total_roll"`           // 抽查匹数 (检验)
	InspectTotalWeight int                        `json:"inspect_total_weight"`         // 抽查总米/码/KG (检验)
	InspectRatio       int                        `json:"inspect_ratio"`                // 检验比例 (检验)
	ItemList           GetFpmQualityCheckDataList `json:"item_list"`                    // 质检明细
	YarnCount          string                     `json:"yarn_count"`                   // 纱支
	NeedleSize         string                     `json:"needle_size"`                  // 针寸数
	Density            string                     `json:"density"`                      // 密度
	CustomerName       string                     `json:"customer_name" excel:"所属客户名称"` // 所属客户名称
	DyeFactoryName     string                     `json:"dye_factory_name,omitempty"`   // 染厂名称
	WeaveFactoryName   string                     `json:"weave_factory_name,omitempty"` // 织厂名称
	YarnBatch          string                     `json:"yarn_batch" excel:"纱批"`        // 纱批
	ProductId          uint64                     `json:"product_id"`                   // 成品id
	ProductCode        string                     `json:"product_code" excel:"成品编号,必填"` // 成品编号
	ProductName        string                     `json:"product_name" excel:"成品名称"`    // 成品名称
	ProductColorId     uint64                     `json:"product_color_id"`             // 成品颜色id
	ProductColorCode   string                     `json:"product_color_code"`           // 成品颜色色号
	ProductColorName   string                     `json:"product_color_name"`           // 成品颜色名称
	WarehouseInOrderNo string                     `json:"warehouse_in_order_no"`        // 进仓单号
	WarehouseInTime    tools.MyTime               `json:"warehouse_in_time"`            // 进仓日期
	structure_base.FinishProductWidthAndWightUnit
}

type GetFpmQualityCheckDyelotNumberDataList []GetFpmQualityCheckDyelotNumberData

func (f GetFpmQualityCheckDyelotNumberDataList) Adjust() {

}
