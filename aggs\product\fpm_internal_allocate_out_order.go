package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"time"
)

type FpmInternalAllocateOutOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmInternalAllocateOutOrderRepo(tx *mysql_base.Tx) *FpmInternalAllocateOutOrderRepo {
	return &FpmInternalAllocateOutOrderRepo{tx: tx}
}

func (r *FpmInternalAllocateOutOrderRepo) Add(ctx context.Context, req *structure.AddFpmInternalAllocateOutOrderParam) (data structure.AddFpmInternalAllocateOutOrderData, err error) {

	var (
		// rLock *redis.LockForRedis
		info               = metadata.GetLoginInfo(ctx)
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
	)

	fpmAllocateOutOrder := model.NewFpmInternalAllocateOutOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmAllocateOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmAllocateOutOrder.BusinessClose = common_system.BusinessCloseNo
	fpmAllocateOutOrder.DepartmentId = info.GetDepartmentId()
	// 在营销体系中获取一部分前缀
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})

	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmInternalAllocateOutOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmInternalAllocateOutOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmAllocateOutOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmAllocateOutOrder.OrderNo = orderNo
	fpmAllocateOutOrder.Number = int(number)

	fpmAllocateOutOrder.TotalWeight, fpmAllocateOutOrder.TotalRoll, fpmAllocateOutOrder.TotalLength = req.GetTotalWRL()
	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmAllocateOutOrder.UnitId = item.UnitId
			break
		}
	}
	fpmAllocateOutOrder, err = mysql.MustCreateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmAllocateOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		fpmAllocateOutOrderItem.ParentId = fpmAllocateOutOrder.Id
		fpmAllocateOutOrderItem.ParentOrderNo = fpmAllocateOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(0)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmAllocateOutOrderItem.TotalWeight = tw
		fpmAllocateOutOrderItem.TotalPrice = tp
		fpmAllocateOutOrderItem.OutLength = tl
		fpmAllocateOutOrderItem.WeightError = weightError
		fpmAllocateOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmAllocateOutOrderItem.PaperTubeWeight = tpp
		fpmAllocateOutOrderItem.SettleWeight = tsw
		fpmAllocateOutOrderItem.ActuallyWeight = taw
		fpmAllocateOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmAllocateOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmAllocateOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmAllocateOutOrderItem.Id
			itemFc.WarehouseId = fpmAllocateOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeInternalAllocate
			itemFc.WarehouseOutOrderId = fpmAllocateOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmAllocateOutOrder.OrderNo
			itemFc.OrderTime = fpmAllocateOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmAllocateOutOrder.Id
	return
}

func (r *FpmInternalAllocateOutOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmInternalAllocateOutOrderParam) (data structure.UpdateFpmInternalAllocateOutOrderData, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
		itemModel           model.FpmOutOrderItem
		findCodeModel       model.FpmOutOrderItemFc
		itemList            model.FpmOutOrderItemList
		sumStockMap         = make(map[uint64]uint64)
	)
	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmAllocateOutOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	fpmAllocateOutOrder.UpdateFpmInternalAllocateOutOrder(ctx, req)

	fpmAllocateOutOrder.TotalWeight, fpmAllocateOutOrder.TotalRoll, fpmAllocateOutOrder.TotalLength = req.GetTotalWRL()
	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmAllocateOutOrder.UnitId = item.UnitId
		}
	}

	if fpmAllocateOutOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmAllocateOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmAllocateOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	for _, item := range itemList {
		sumStockMap[item.SumStockId] = item.ArrangeItemId
	}
	itemIds := itemList.GetIds()
	arrangeItemIdMap := make(map[uint64]uint64) // 使用sum_stock_id作为键
	for _, item := range itemList {
		if item.SumStockId > 0 && item.ArrangeItemId > 0 {
			arrangeItemIdMap[item.SumStockId] = item.ArrangeItemId
		}
	}
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
		if err != nil {
			return
		}
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmAllocateOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		if item.SumStockId > 0 {
			if arrangeId, exists := arrangeItemIdMap[item.SumStockId]; exists && arrangeId > 0 {
				// 如果有，则使用原来的ArrangeItemId
				fpmAllocateOutOrderItem.ArrangeItemId = arrangeId
			}
		}
		fpmAllocateOutOrderItem.ParentId = fpmAllocateOutOrder.Id
		fpmAllocateOutOrderItem.ParentOrderNo = fpmAllocateOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(0)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmAllocateOutOrderItem.TotalWeight = tw
		fpmAllocateOutOrderItem.TotalPrice = tp
		fpmAllocateOutOrderItem.OutLength = tl
		fpmAllocateOutOrderItem.WeightError = weightError
		fpmAllocateOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmAllocateOutOrderItem.PaperTubeWeight = tpp
		fpmAllocateOutOrderItem.SettleWeight = tsw
		fpmAllocateOutOrderItem.ActuallyWeight = taw
		fpmAllocateOutOrderItem.ArrangeItemId = sumStockMap[fpmAllocateOutOrderItem.SumStockId]
		fpmAllocateOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmAllocateOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmAllocateOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmAllocateOutOrderItem.Id
			itemFc.WarehouseId = fpmAllocateOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeInternalAllocate
			itemFc.WarehouseOutOrderId = fpmAllocateOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmAllocateOutOrder.OrderNo
			itemFc.OrderTime = fpmAllocateOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmAllocateOutOrder.Id
	return
}

func (r *FpmInternalAllocateOutOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmInternalAllocateOutOrderBusinessCloseParam) (data structure.UpdateFpmInternalAllocateOutOrderData, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
		info                = metadata.GetLoginInfo(ctx)
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, v)
		if err != nil {
			return
		}
		fpmAllocateOutOrder.BusinessClose = req.BusinessClose
		fpmAllocateOutOrder.BusinessCloseUserId = info.GetUserId()
		fpmAllocateOutOrder.BusinessCloseUserName = info.GetUserName()
		fpmAllocateOutOrder.BusinessCloseTime = time.Now()
		fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmInternalAllocateOutOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (data structure.UpdateFpmInternalAllocateOutOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
		fpmInOrders         model.FpmInOrderList
	)

	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	updateItems, err = r.judgeAuditPass(id, fpmAllocateOutOrder, ctx)
	if err != nil {
		return
	}

	//
	fpmInOrders, err = mysql.FindFpmInOrderByPurchaseOrderIDs(r.tx, []uint64{fpmAllocateOutOrder.Id})
	if err != nil {
		return
	}
	if len(fpmInOrders) > 0 {
		data.IsHasInOrder = true
	}
	// 审核
	err = fpmAllocateOutOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmAllocateOutOrder.Id, true)
	if err != nil {
		return
	}
	data.WarehouseOutTime = tools.MyTime(fpmAllocateOutOrder.WarehouseOutTime)
	return
}

func (r *FpmInternalAllocateOutOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (data structure.UpdateFpmInternalAllocateOutOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
	)

	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	err = r.judgeIsQuoted(id, fpmAllocateOutOrder)
	if err != nil {
		return
	}
	updateItems, err = r.judgeAuditWait(id, fpmAllocateOutOrder, ctx)
	if err != nil {
		return
	}

	// 消审
	err = fpmAllocateOutOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}
	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmAllocateOutOrder.Id, false)
	if err != nil {
		return
	}
	data.WarehouseOutTime = tools.MyTime(fpmAllocateOutOrder.WarehouseOutTime)
	data.ArrangeId = fpmAllocateOutOrder.ArrangeOrderId
	return
}

func (r *FpmInternalAllocateOutOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmInternalAllocateOutOrderStatusData, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
	)

	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 拒绝/驳回
	err = fpmAllocateOutOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmInternalAllocateOutOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmInternalAllocateOutOrderStatusData, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
		fcOutList           model.FpmOutOrderItemFcList
	)

	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 作废
	err = r.judgeIsQuoted(id, fpmAllocateOutOrder)
	if err != nil {
		return
	}

	// 作废
	err = fpmAllocateOutOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}
	data.ArrangeId = fpmAllocateOutOrder.ArrangeOrderId
	data.Id = fpmAllocateOutOrder.Id

	// 获取细码的库存id，用来解除占用
	if fpmAllocateOutOrder.ArrangeOrderId > 0 {
		fcOutList, _ = mysql.FindFpmOutOrderItemFcByOrderId(r.tx, fpmAllocateOutOrder.Id)
		if len(fcOutList) > 0 {
			data.StockDetailIds = fcOutList.GetStockIDs()
		}
	}

	return
}

func (r *FpmInternalAllocateOutOrderRepo) Get(ctx context.Context, req *structure.GetFpmInternalAllocateOutOrderQuery) (data structure.GetFpmInternalAllocateOutOrderData, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
		itemDatas           model.FpmOutOrderItemList
		fineCodeList        model.FpmOutOrderItemFcList
		detailStockList     model.StockProductDetailList
		warehousePB         = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB              = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc              = dictionary.NewDictionaryClient()
	)
	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmInternalAllocateOutOrderData{}
	r.swapListModel2Data(fpmAllocateOutOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmOutOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmAllocateOutOrder.AuditStatus != common_system.OrderStatusAudited {
			stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
				StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
			for _, v := range stockList {
				// 库存信息,2023-12-20 需求1001412改为获取可用数量和匹数
				itemGetData.SumStockRoll = v.AvailableRoll
				itemGetData.SumStockWeight = v.AvailableWeight
				itemGetData.SumStockLength = v.Length
			}
		}
		// 添加细码信息
		fineCodeList, err = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, itemData.Id)
		if err != nil {
			return
		}

		mUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "measurement_unit_id")
		wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
		stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitFcIds)
		binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
		detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
		if err != nil {
			return
		}
		dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
		dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

		for _, fineCode := range fineCodeList {
			fineCodeGetData := structure.GetFpmOutOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
			fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
			fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.OrderTime = tools.MyTime(fineCode.OrderTime)
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			fineCodeGetData.IsBooked = fineCode.IsBooked
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			detailStock := detailStockList.Pick(fineCode.StockId)
			fineCodeGetData.QrCode = detailStock.QrCode

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)

			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmInternalAllocateOutOrderRepo) GetList(ctx context.Context, req *structure.GetFpmOutOrderListQuery) (list structure.GetFpmInternalAllocateOutOrderDataList, total int, err error) {
	var (
		orders model.FpmOutOrderList
		// bizPB       = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmOutOrder(r.tx, req)
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		// bizNameMap     map[uint64]string
		empNameMap map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orders, "unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	// g.Go(func(ctx context.Context) error {
	//	var err1 error
	//	bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
	//	bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
	//	if err1 != nil {
	//		middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
	//	}
	//	return nil
	// })

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		dst := structure.GetFpmInternalAllocateOutOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.ArrangeOrderId = src.ArrangeOrderId
		dst.ArrangeOrderNo = src.ArrangeOrderNo
		dst.SaleSystemId = src.SaleSystemId
		dst.OutWarehouseId = src.WarehouseId
		dst.InWarehouseId = src.InWarehouseId
		dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.DriverId = src.DriverId
		dst.LogisticsCompanyId = src.LogisticsCompanyId
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.TotalPrice = src.TotalPrice
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)

		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.OutWarehouseName = wareNameMap[src.WarehouseId]
		dst.InWarehouseName = wareNameMap[src.InWarehouseId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.UnitName = unitNameMap[src.UnitId]
		list = append(list, dst)
	}
	return
}

func (r *FpmInternalAllocateOutOrderRepo) swapListModel2Data(src model.FpmOutOrder, dst *structure.GetFpmInternalAllocateOutOrderData, ctx context.Context) {
	var (
		saleSysPBSerbice = sale_sys_pb.NewSaleSystemClient()
		userPB           = empl_pb.NewClientEmployeeService()
		userName         = make(map[uint64]string)
		unitPB           = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB      = warehouse_pb.NewPhysicalWarehouseClient()
		company          = base_info_pb.NewInfoSaleLogisticsCompanyClient()
	)

	saleSystemMap, err2 := saleSysPBSerbice.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	userName, _ = userPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId})
	driverIds := tools.String2UintArray(src.DriverId, ",")
	driverNameMap, _ := userPB.GetEmployeeNameByIds(r.tx.Context, driverIds)
	driverName := tools.GetMapValAppend2String(driverNameMap, ",")

	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseMap, _ := warehousePB.GetPhysicalWarehouseByIds(r.tx.Context, []uint64{src.InWarehouseId, src.WarehouseId})
	logisticsCompanyName, _ := company.GetInfoSaleLogisticsCompanyNameById(ctx, src.LogisticsCompanyId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ArrangeOrderId = src.ArrangeOrderId
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.SaleSystemId = src.SaleSystemId
	dst.OutWarehouseId = src.WarehouseId
	dst.InWarehouseId = src.InWarehouseId
	dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
	dst.StoreKeeperId = src.StoreKeeperId
	dst.DriverId = src.DriverId
	dst.LogisticsCompanyId = src.LogisticsCompanyId
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.TotalPrice = src.TotalPrice
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)

	// 转义
	dst.DriverName = driverName
	dst.LogisticsCompanyName = logisticsCompanyName
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()

	dst.UnitName = unitName
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	if val, ok := warehouseMap[src.InWarehouseId]; ok {
		dst.InWarehouseName = val
	}
	if val, ok := warehouseMap[src.WarehouseId]; ok {
		dst.OutWarehouseName = val
	}
}

func (r *FpmInternalAllocateOutOrderRepo) swapItemModel2Data(src model.FpmOutOrderItem, dst *structure.GetFpmOutOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
		productSvc = product.NewProductClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)
	productMap, _ := productSvc.GetProductMapByIds(ctx, []uint64{src.ProductId})
	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.QuoteOrderNo = src.QuoteOrderNo
	dst.QuoteOrderItemId = src.QuoteOrderItemId
	dst.ProductCode = src.ProductCode
	dst.ProductName = src.ProductName
	dst.CustomerId = src.CustomerId
	dst.ProductColorId = src.ProductColorId
	dst.ProductColorCode = src.ProductColorCode
	dst.ProductColorName = src.ProductColorName
	dst.ProductLevelId = src.ProductLevelId
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductWidth = src.ProductWidth
	dst.ProductGramWeight = src.ProductGramWeight
	dst.ProductRemark = src.ProductRemark
	dst.ProductCraft = src.ProductCraft
	dst.ProductIngredient = src.ProductIngredient
	dst.OutRoll = src.OutRoll
	dst.SumStockId = src.SumStockId
	dst.TotalWeight = src.TotalWeight
	dst.WeightError = src.WeightError
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.SettleWeight = src.SettleWeight
	dst.UnitId = src.UnitId
	dst.UnitPrice = src.UnitPrice
	dst.OutLength = src.OutLength
	dst.LengthUnitPrice = src.LengthUnitPrice
	dst.OtherPrice = src.OtherPrice
	dst.TotalPrice = src.TotalPrice
	dst.Remark = src.Remark
	dst.ProductId = src.ProductId
	dst.SumStockId = src.SumStockId
	// 转义

	dst.UnitName = unitName
	if val, ok := customerMap[src.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
	dst.SumStockRoll = src.SumStockRoll
	dst.SumStockLength = src.SumStockLength
	dst.SumStockWeight = src.SumStockWeight
	dst.SettleErrorWeight = src.SettleErrorWeight
	if productRes, ok := productMap[src.ProductId]; ok {
		dst.FinishProductWidthAndWightUnit.FinishProductWidthUnitId = productRes.FinishProductWidthUnitId
		dst.FinishProductWidthAndWightUnit.FinishProductWidthUnitName = productRes.FinishProductWidthUnitName
		dst.FinishProductWidthAndWightUnit.FinishProductWidthAndUnitName = productRes.FinishProductWidthAndUnitName
		dst.FinishProductWidthAndWightUnit.ProductGramWeight = productRes.ProductGramWeight
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitId = productRes.FinishProductGramWeightUnitId
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightAndUnitName = productRes.FinishProductGramWeightAndUnitName
		dst.FinishProductWidthAndWightUnit.ProductWidth = productRes.ProductWidth
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitName = productRes.FinishProductGramWeightUnitName
	}
}

func (r *FpmInternalAllocateOutOrderRepo) swapFcModel2Data(src model.FpmOutOrderItemFc, dst *structure.GetFpmOutOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, src.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.Roll = src.Roll
	dst.WarehouseBinId = src.WarehouseBinId
	dst.VolumeNumber = src.VolumeNumber
	dst.WarehouseOutType = src.WarehouseOutType
	dst.WarehouseOutOrderId = src.WarehouseOutOrderId
	dst.WarehouseOutOrderNo = src.WarehouseOutOrderNo
	dst.WarehouseInType = src.WarehouseInType
	dst.WarehouseInOrderId = src.WarehouseInOrderId
	dst.WarehouseInOrderNo = src.WarehouseInOrderNo
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.StockId = src.StockId
	dst.SumStockId = src.SumStockId
	dst.BaseUnitWeight = src.BaseUnitWeight
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.WeightError = src.WeightError
	dst.UnitId = src.UnitId
	dst.Length = src.Length
	dst.SettleWeight = src.SettleWeight
	dst.DigitalCode = src.DigitalCode
	dst.ShelfNo = src.ShelfNo
	dst.ContractNumber = src.ContractNumber
	dst.CustomerPoNum = src.CustomerPoNum
	dst.AccountNum = src.AccountNum
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductWidth = src.ProductWidth
	dst.ProductGramWeight = src.ProductGramWeight
	dst.StockRemark = src.StockRemark
	dst.Remark = src.Remark
	dst.InternalRemark = src.InternalRemark
	dst.ScanUserId = src.ScanUserId
	dst.ScanUserName = src.ScanUserName
	dst.ScanTime = tools.MyTime(src.ScanTime)
	dst.OrderTime = tools.MyTime(src.OrderTime)
	dst.ArrangeItemFcId = src.ArrangeItemFcId
	dst.IsBooked = src.IsBooked
	// 转义
	dst.WarehouseBinName = binName
	dst.UnitName = unitName
}

func (r *FpmInternalAllocateOutOrderRepo) judgeAuditPass(id uint64, order model.FpmOutOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {

	var (
		fineCodeList         = model.FpmOutOrderItemFcList{}
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		// arrangeItemIds       = make([]uint64, 0)
		arrangeOrder model.FpmArrangeOrder
	)
	// 判断成品数量是否符合
	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		totalRoll := 0
		totalLength := 0

		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]

		fineCodeList, _ = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		if item.OutRoll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(errors.NewError(errors.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(fineCodeList) == 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
			return
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			// 通过arrangeItem.ParentId获取配布单
			arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, arrangeItem.ParentId)
			if err != nil {
				return
			}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll -= arrangeItem.PushRoll
			updateItemWeight.BookWeight -= arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = order.Id
			updateItemWeight.BookOrderId = arrangeOrder.SrcId
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductIntAlloOutPass
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
		for _, fineCode := range fineCodeList {
			totalRoll = totalRoll + fineCode.Roll
			totalLength += fineCode.Length
			updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParamBack(ctx, swap2StockFieldParam))
		}

		if totalRoll != item.OutRoll {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.OutLength > 0 && totalLength > 0 && item.OutLength != totalLength {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}
	updateBookWeight = append(updateBookWeight, updateWeight...)
	updateItems = updateBookWeight
	return
}

func (r *FpmInternalAllocateOutOrderRepo) judgeAuditWait(id uint64, order model.FpmOutOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		fineCodeList         = model.FpmOutOrderItemFcList{}
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
	)
	// 判断成品数量是否符合
	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]

		fineCodeList, _ = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		for _, fineCode := range fineCodeList {
			updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam))
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll += arrangeItem.PushRoll
			updateItemWeight.BookWeight += arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductIntAlloOutWait
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
	}
	updateWeight = append(updateWeight, updateBookWeight...)
	updateItems = updateWeight
	return
}

func (r *FpmInternalAllocateOutOrderRepo) SwapAlloDate2InParam(req structure.GetFpmInternalAllocateOutOrderData) structure.AddFpmInOrderParam {
	var (
		sale_sys_svc = sale_sys_pb.NewSaleSystemClient()
		saleSysData  = sale_sys_pb.Res{}
		orderPrefix  mysqlSystem.OrderPrefix
		exist        bool
		err          error
	)

	// 在营销体系中获取一部分前缀
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(r.tx.Context, sale_sys_pb.Req{Id: req.SaleSystemId})

	param := structure.AddFpmInOrderParam{}
	param.Swap2ListParam(req)
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return param
	}
	if !exist {
		param.OrderNoPre = vars.FpmInternalAllocateInOrderPrefix
		if vars.UseSaleSystem {
			param.OrderNoPre = fmt.Sprintf("%s-%s-", param.OrderNoPre, saleSysData.Code)
		}
	} else {
		param.OrderNoPre = orderPrefix.FpmInternalAllocateInOrder
		if orderPrefix.UseSaleSystem {
			param.OrderNoPre = fmt.Sprintf("%s-%s-", param.OrderNoPre, saleSysData.Code)
		}
	}
	for _, item := range req.ItemData {
		itemParam := structure.AddFpmInOrderItemParam{}
		itemParam.Swap2ItemParam(item)
		itemParam.QuoteOrderItemId = item.Id
		itemParam.QuoteOrderNo = item.ParentOrderNo
		for _, itemFc := range item.ItemFCData {
			itemFcParam := structure.AddFpmInOrderItemFcParam{}
			itemFcParam.Swap2AddItemFcParam(itemFc)
			itemFcParam.WarehouseBinId = 0
			itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
		}
		param.ItemData = append(param.ItemData, itemParam)
	}
	return param
}

func (r *FpmInternalAllocateOutOrderRepo) judgeIsQuoted(id uint64, fpmAllocateOutOrder model.FpmOutOrder) (err error) {
	var (
		inOrderList model.FpmInOrderList
	)
	inOrderList, err = mysql.FindFpmInOrderByPurchaseOrderIDs(r.tx, []uint64{fpmAllocateOutOrder.Id})
	if err != nil {
		return
	}
	if len(inOrderList) > 0 {
		return errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, "操作失败,请先取消进仓单")
	}
	return
}

func (r *FpmInternalAllocateOutOrderRepo) UpdateStatusWaitUseByChangeOrder(ctx context.Context, id uint64) (data structure.UpdateFpmInternalAllocateOutOrderStatusData, err error) {
	var (
		fpmAllocateOutOrder model.FpmOutOrder
		info                = metadata.GetLoginInfo(ctx)
	)

	fpmAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 单据为待审核/已驳回/已作废状态
	if fpmAllocateOutOrder.AuditStatus == common_system.OrderStatusPendingAudit || fpmAllocateOutOrder.AuditStatus == common_system.OrderStatusRejected || fpmAllocateOutOrder.AuditStatus == common_system.OrderStatusVoided {
		fpmAllocateOutOrder.AuditorName = info.GetUserName()
		fpmAllocateOutOrder.AuditorId = info.GetUserId()
		fpmAllocateOutOrder.AuditDate = time.Now()
		fpmAllocateOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}
	// 单据已审核状态
	if fpmAllocateOutOrder.AuditStatus == common_system.OrderStatusAudited {
		// todo: 已审核状态逻辑待补充
	}
	fpmAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmAllocateOutOrder)
	if err != nil {
		return
	}

	return
}
