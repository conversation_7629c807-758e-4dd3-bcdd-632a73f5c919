package sale

import (
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddSaleProductOrderDetailParam struct {
	structure_base.Param
	StockProductId             uint64 `json:"stock_product_id"`                           // 库存汇总id
	SaleProductOrderId         uint64 `json:"sale_product_order_id"`                      // 成品销售单id
	ProductColorId             uint64 `json:"product_color_id" relate:"product_color_id"` // 颜色id
	ProductColorKindId         uint64 `json:"product_color_kind_id"`                      // 颜色类别id(关联type_finished_product_kind_id)
	CustomerId                 uint64 `json:"customer_id"`                                // 所属客户id
	ProductId                  uint64 `json:"product_id"`                                 // 成品id
	ProductLevelId             uint64 `json:"product_level_id"`                           // 成品等级id
	ProductKindId              uint64 `json:"product_kind_id"`                            // 布种类型id(关联type_grey_fabric_id)
	DyelotNumber               string `json:"dyelot_number"`                              // 缸号
	ProductRemark              string `json:"product_remark"`                             // 成品备注
	AuxiliaryUnitId            uint64 `json:"auxiliary_unit_id"`                          // 辅助单位id
	MeasurementUnitId          uint64 `json:"measurement_unit_id"`                        // 计量单位id
	MeasurementUnitName        string `json:"measurement_unit_name"`                      // 计量单位名称
	Weight                     int    `json:"weight"`                                     // 数量
	Roll                       int    `json:"roll"`                                       // 匹数
	CustomerAccountNum         string `json:"customer_account_num"`                       // 客户款号
	Remark                     string `json:"remark"`                                     // 备注
	StandardSalePrice          int    `json:"standard_sale_price"`                        // 标准销售报价(大货 散剪)
	SaleLevelId                uint64 `json:"sale_level_id"`                              // 销售等级ID
	OffsetSalePrice            int    `json:"offset_sale_price"`                          // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                  int    `json:"sale_price"`                                 // 销售单价(销售报价-优惠单价)(大货 散剪)
	WeightError                int    `json:"weight_error"`                               // 标准空差 /0.1g
	OffsetWeightError          int    `json:"offset_weight_error"`                        // 优惠空差 /0.1g
	AdjustWeightError          int    `json:"adjust_weight_error"`                        // 调整空差 /0.1g
	SettleWeightError          int    `json:"settle_weight_error"`                        // 结算空差 /0.1g
	PaperTubeWeight            int    `json:"paper_tube_weight"`                          // 纸筒数量（公斤）(成品用)
	Length                     int    `json:"length"`                                     // 长度
	StandardLengthCutSalePrice int    `json:"standard_length_cut_sale_price"`             // 剪板销售价格
	OffsetLengthCutSalePrice   int    `json:"offset_length_cut_sale_price"`               // 剪版优惠单价
	LengthCutSalePrice         int    `json:"length_cut_sale_price"`                      // 剪版销售单价(剪板销售价格-剪版优惠单价)
	OtherPrice                 int    `json:"other_price"`                                // 其他金额
	WarehouseId                uint64 `json:"warehouse_id"`                               // 出货仓库id
	StockRoll                  int    `json:"stock_roll"`                                 // 库存可用匹数(可用库存)
	StockWeight                int    `json:"stock_weight"`                               // 可用数量
	BookRoll                   int    `json:"book_roll"`                                  // 预约匹数
	PurchaseRoll               int    `json:"purchase_roll"`                              // 采购匹数
	PurchaseWeight             int    `json:"purchase_weight"`                            // 采购数量
	PurchaseLength             int    `json:"purchase_length"`                            // 采购长度
	ShortageRoll               int    `json:"shortage_roll"`                              // 欠货匹数
	ShortageWeight             int    `json:"shortage_weight"`                            // 欠货数量
	ShortageLength             int    `json:"shortage_length"`                            // 欠货长度
	IsDisplayPrice             bool   `json:"is_display_price"`                           // 是否显示单价
	// StockLength                int    `json:"stock_length"`                   // 库存可用长度
	PmcGreyPlanOrderSummaryDetailId uint64 `json:"pmc_grey_plan_order_summary_detail_id"` // pmc布料计划单下推单据详情id
	PlanDetailId                    uint64 `json:"plan_detail_id"`                        // 销售计划单详细id
}

// 小程序端新增销售计划单成品详细
type AddSaleProductOrderDetailParamV2 struct {
	structure_base.Param
	SaleProductOrderId uint64                               `json:"-"`            // 成品销售单id
	ProductId          uint64                               `json:"product_id"`   // 成品id
	ColorDetail        []SaleProductOrderProductColorDetail `json:"color_detail"` // 色号详情
	WarehouseId        uint64                               `json:"warehouse_id"`
}

// SaleProductOrderProductColorDetail 色号详情
type SaleProductOrderProductColorDetail struct {
	// Id                         uint64                `json:"id"`
	CustomerAccountNum         string                `json:"customer_account_num"`                       // 客户款号
	AdjustWeightError          int                   `json:"adjust_weight_error"`                        // 调整空差
	OtherPrice                 int                   `json:"other_price"`                                // 其他金额
	Remark                     string                `json:"remark"`                                     // 备注
	SaleProductOrderId         uint64                `json:"-"`                                          // 成品销售单id
	StockProductId             uint64                `json:"stock_product_id"`                           // 库存汇总id// 库存id
	ProductColorId             uint64                `json:"product_color_id" relate:"product_color_id"` // 颜色id
	Roll                       int                   `json:"roll"`                                       // 匹数
	Weight                     int                   `json:"weight"`                                     // 数量
	Length                     int                   `json:"length"`                                     // 长度
	DyelotNumber               tools.QueryStringList `json:"dyelot_number"`                              // 指定缸号
	AuxiliaryUnitId            uint64                `json:"auxiliary_unit_id"`                          // 辅助单位id
	MeasurementUnitId          uint64                `json:"measurement_unit_id"`                        // 计量单位id
	StandardSalePrice          int                   `json:"standard_sale_price"`                        // 大货标准销售价格
	StandardLengthCutSalePrice int                   `json:"standard_length_cut_sale_price"`             // 剪板标准销售价格
	LengthCutSalePrice         int                   `json:"length_cut_sale_price"`                      // 剪版销售价格
	SalePrice                  int                   `json:"sale_price"`                                 // 大货销售价格
	StockRoll                  int                   `json:"stock_roll"`                                 // 库存可用匹数(可用库存)
	BookRoll                   int                   `json:"book_roll"`                                  // 预约匹数
	StockWeight                int                   `json:"stock_weight"`                               // 库存可用数量
	WarehouseId                uint64                `json:"warehouse_id"`                               // 出货仓库id
	ProductLevelId             uint64                `json:"product_level_id" `                          // 成品等级id
	ProductRemark              string                `json:"product_remark"`                             // 成品备注
	CustomerId                 uint64                `json:"customer_id"`                                // 所属客户id
	ProductColorKindId         uint64                `json:"product_color_kind_id"`                      // 颜色类别id(关联type_finished_product_kind_id)
	ProductId                  uint64                `json:"product_id"`                                 // 成品id
	SaleLevelId                uint64                `json:"sale_level_id"`                              // 销售等级ID
	PurchaseRoll               int                   `json:"purchase_roll"`                              // 采购匹数
	PurchaseWeight             int                   `json:"purchase_weight"`                            // 采购数量
	PurchaseLength             int                   `json:"purchase_length"`                            // 采购长度

}

type AddSaleProductOrderDetailParamList []AddSaleProductOrderDetailParam
type AddSaleProductOrderDetailParamListV2 []AddSaleProductOrderDetailParamV2

func (r *AddSaleProductOrderDetailParam) Adjust() {

}

type GetSaleProductOrderDetailData struct {
	structure_base.RecordData
	StockProductId             uint64 `json:"stock_product_id"`               // 库存汇总id
	SaleProductOrderId         uint64 `json:"sale_product_order_id"`          // 成品销售单id
	ProductColorId             uint64 `json:"product_color_id"`               // 颜色id
	ProductColorCode           string `json:"product_color_code"`             // 颜色编号
	ProductColorName           string `json:"product_color_name"`             // 颜色名称
	ProductColorKindId         uint64 `json:"product_color_kind_id"`          // 颜色类别id(关联type_finished_product_kind_id)
	ProductColorKindName       string `json:"product_color_kind_name"`        // 颜色类别id(关联type_finished_product_kind_id)名称
	CustomerId                 uint64 `json:"customer_id"`                    // 所属客户id
	CustomerCode               string `json:"customer_code"`                  // 所属客户编号
	CustomerName               string `json:"customer_name"`                  // 所属客户名称
	ProductId                  uint64 `json:"product_id"`                     // 成品id
	ProductCode                string `json:"product_code"`                   // 成品编号
	ProductName                string `json:"product_name"`                   // 成品名称
	ProductLevelId             uint64 `json:"product_level_id"`               // 成品等级id
	ProductLevelName           string `json:"product_level_name"`             // 成品等级名称
	ProductKindId              uint64 `json:"product_kind_id"`                // 布种类型id(关联type_grey_fabric_id)
	ProductKindName            string `json:"product_kind_name"`              // 布种类型id(关联type_grey_fabric_id)名称
	DyelotNumber               string `json:"dyelot_number"`                  // 缸号
	ProductRemark              string `json:"product_remark"`                 // 成品备注
	AuxiliaryUnitId            uint64 `json:"auxiliary_unit_id"`              // 辅助单位id
	MeasurementUnitId          uint64 `json:"measurement_unit_id"`            // 计量单位id
	AuxiliaryUnitName          string `json:"auxiliary_unit_name"`            // 辅助单位名称
	MeasurementUnitName        string `json:"measurement_unit_name"`          // 计量单位名称
	Weight                     int    `json:"weight"`                         // 数量
	Roll                       int    `json:"roll"`                           // 匹数
	CustomerAccountNum         string `json:"customer_account_num"`           // 客户款号
	Remark                     string `json:"remark"`                         // 备注
	StandardSalePrice          int    `json:"standard_sale_price"`            // 标准销售报价(大货 散剪)
	SaleLevelId                uint64 `json:"sale_level_id"`                  // 销售等级ID
	SaleLevelName              string `json:"sale_level_name"`                // 销售等级ID名称
	OffsetSalePrice            int    `json:"offset_sale_price"`              // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                  int    `json:"sale_price"`                     // 销售单价(销售报价-优惠单价)(大货 散剪)
	WeightError                int    `json:"weight_error"`                   // 标准空差 /0.1g
	OffsetWeightError          int    `json:"offset_weight_error"`            // 优惠空差 /0.1g
	AdjustWeightError          int    `json:"adjust_weight_error"`            // 调整空差 /0.1g
	SettleWeightError          int    `json:"settle_weight_error"`            // 结算空差 /0.1g
	Length                     int    `json:"length"`                         // 长度
	StandardLengthCutSalePrice int    `json:"standard_length_cut_sale_price"` // 剪板销售价格
	StandardWeightCutSalePrice int    `json:"standard_weight_cut_sale_price"` // 散剪销售价格
	OffsetLengthCutSalePrice   int    `json:"offset_length_cut_sale_price"`   // 剪版优惠单价
	LengthCutSalePrice         int    `json:"length_cut_sale_price"`          // 剪版销售单价(剪板销售价格-剪版优惠单价)
	OtherPrice                 int    `json:"other_price"`                    // 其他金额
	WarehouseId                uint64 `json:"warehouse_id"`                   // 出货仓库id
	WarehouseName              string `json:"warehouse_name"`                 // 出货仓库名称
	StockRoll                  int    `json:"stock_roll"`                     // 库存可用匹数(可用库存)
	AvailableWeight            int    `json:"available_weight"`               // 可用数量
	BookRoll                   int    `json:"book_roll"`                      // 预约匹数
	PurchaseRoll               int    `json:"purchase_roll"`                  // 采购匹数
	PurchaseWeight             int    `json:"purchase_weight"`                // 采购数量
	PurchaseLength             int    `json:"purchase_length"`                // 采购长度
	ShortageRoll               int    `json:"shortage_roll"`                  // 欠货匹数
	ShortageWeight             int    `json:"shortage_weight"`                // 欠货数量
	ShortageLength             int    `json:"shortage_length"`                // 欠货长度
	IsDisplayPrice             bool   `json:"is_display_price"`               // 是否显示单价
	StockRemark                string `json:"stock_remark"`                   // 库存备注
	// StockLength                int    `json:"stock_length"`                   // 库存可用长度
	PmcGreyPlanOrderSummaryDetailId uint64 `json:"pmc_grey_plan_order_summary_detail_id"` // pmc布料计划单下推单据详情id
	PlanDetailId                    uint64 `json:"plan_detail_id"`                        // 计划单详细id
	TextureURL                      string `json:"texture_url"`                           // 纹理图片
}

// GetSaleProductOrderDetailDataV2 成品信息
type GetSaleProductOrderDetailDataV2 struct {
	structure_base.RecordData
	SaleProductOrderId uint64                               `json:"sale_product_order_id"` // 成品销售单id
	ProductId          uint64                               `json:"product_id"`            // 成品id
	ProductCode        string                               `json:"product_code"`          // 成品编号
	ProductName        string                               `json:"product_name"`          // 成品名称
	ProductLevelId     uint64                               `json:"product_level_id"`      // 成品等级id
	WarehouseId        uint64                               `json:"warehouse_id"`          // 出货仓库id
	WarehouseName      string                               `json:"warehouse_name"`        // 出货仓库名称
	ColorDetail        []GetSaleProductOrderColorDetailData `json:"color_detail"`          // 色号详情
}
type GetSaleProductOrderColorDetailData struct {
	ProductColorId             uint64  `json:"product_color_id"`               // 颜色id
	ProductColorCode           string  `json:"product_color_code"`             // 颜色编号
	ProductColorName           string  `json:"product_color_name"`             // 颜色名称
	StockProductId             uint64  `json:"stock_product_id"`               // 库存汇总id
	DyelotNumber               string  `json:"dyelot_number"`                  // 缸号
	AuxiliaryUnitId            uint64  `json:"auxiliary_unit_id"`              // 辅助单位id
	MeasurementUnitId          uint64  `json:"measurement_unit_id"`            // 计量单位id
	AuxiliaryUnitName          string  `json:"auxiliary_unit_name"`            // 辅助单位名称
	MeasurementUnitName        string  `json:"measurement_unit_name"`          // 计量单位名称
	Weight                     int     `json:"weight"`                         // 数量
	StandardWeight             int     `json:"standard_weight"`                // 标准数量
	Roll                       int     `json:"roll"`                           // 匹数
	BookRoll                   int     `json:"book_roll"`                      // 预定匹数
	AvailableRoll              int     `json:"available_roll"`                 // 可用匹数
	ShortageRoll               int     `json:"shortage_roll"`                  // 欠货匹数
	Length                     int     `json:"length"`                         // 长度
	StockRoll                  int     `json:"stock_roll"`                     // 库存可用匹数(可用库存)
	StockWeight                int     `json:"stock_weight"`                   // 库存可用数量
	TotalPrice                 float64 `json:"total_price"`                    // 总价格
	StandardSalePrice          int     `json:"standard_sale_price"`            // 大货标准销售价格
	StandardLengthCutSalePrice int     `json:"standard_length_cut_sale_price"` // 剪板标准销售价格
	LengthCutSalePrice         int     `json:"length_cut_sale_price"`          // 剪版销售价格
	SalePrice                  int     `json:"sale_price"`                     // 大货销售价格
	Remark                     string  `json:"remark"`                         // 备注
	WarehouseId                uint64  `json:"warehouse_id"`                   // 仓库id
	WarehouseName              string  `json:"warehouse_name"`                 // 仓库
	ProductRemark              string  `json:"product_remark"`                 // 成品备注
	CustomerId                 uint64  `json:"customer_id"`                    // 所属客户id
	CustomerCode               string  `json:"customer_code"`                  // 客户编号
	CustomerName               string  `json:"customer_name"`                  // 所属客户
	ProductColorKindId         uint64  `json:"product_color_kind_id"`          // 颜色类别id(关联type_finished_product_kind_id)
	ProductColorKindName       string  `json:"product_color_kind_name"`        // 成品颜色种类名称
	ProductId                  uint64  `json:"product_id"`                     // 成品id
	ProductName                string  `json:"product_name"`                   // 成品
	ProductCode                string  `json:"product_code"`                   // 成品编号
	ProductLevelId             uint64  `json:"product_level_id"`               // 成品等级id
	ProductLevelName           string  `json:"product_level_name"`             // 成品等级名称
	SaleLevelId                uint64  `json:"sale_level_id"`                  // 销售等级ID
	SaleLevelName              string  `json:"sale_level_name"`                // 销售等级
	PurchaseRoll               int     `json:"purchase_roll"`                  // 采购匹数
	PurchaseWeight             int     `json:"purchase_weight"`                // 采购数量
	PurchaseLength             int     `json:"purchase_length"`                // 采购长度
	TextureURL                 string  `json:"texture_url"`                    // 封面纹理图片URL（详情显示）
}

type GetSaleProductOrderDetailDataList []GetSaleProductOrderDetailData
type GetSaleProductOrderDetailDataListV2 []GetSaleProductOrderDetailDataV2

func (g GetSaleProductOrderDetailDataList) Adjust() {

}
func (g GetSaleProductOrderDetailDataListV2) Adjust() {}

type UpdateSaleProductOrderDetailItem struct {
	structure_base.Param
	StockProductId             uint64 `json:"stock_product_id"`               // 库存汇总id
	SaleProductOrderId         uint64 `json:"sale_product_order_id"`          // 成品销售单id
	ProductColorId             uint64 `json:"product_color_id"`               // 颜色id
	ProductColorKindId         uint64 `json:"product_color_kind_id"`          // 颜色类别id(关联type_finished_product_kind_id)
	CustomerId                 uint64 `json:"customer_id"`                    // 所属客户id
	ProductId                  uint64 `json:"product_id"`                     // 成品id
	ProductLevelId             uint64 `json:"product_level_id"`               // 成品等级id
	ProductKindId              uint64 `json:"product_kind_id"`                // 布种类型id(关联type_grey_fabric_id)
	DyelotNumber               string `json:"dyelot_number"`                  // 缸号
	ProductRemark              string `json:"product_remark"`                 // 成品备注
	MeasurementUnitId          uint64 `json:"measurement_unit_id"`            // 计量单位id
	MeasurementUnitName        string `json:"measurement_unit_name"`          // 计量单位名称
	Weight                     int    `json:"weight"`                         // 数量
	Roll                       int    `json:"roll"`                           // 匹数
	CustomerAccountNum         string `json:"customer_account_num"`           // 客户款号
	Remark                     string `json:"remark"`                         // 备注
	StandardSalePrice          int    `json:"standard_sale_price"`            // 标准销售报价(大货 散剪)
	SaleLevelId                uint64 `json:"sale_level_id"`                  // 销售等级ID
	OffsetSalePrice            int    `json:"offset_sale_price"`              // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                  int    `json:"sale_price"`                     // 销售单价(销售报价-优惠单价)(大货 散剪)
	WeightError                int    `json:"weight_error"`                   // 标准空差 /0.1g
	OffsetWeightError          int    `json:"offset_weight_error"`            // 优惠空差 /0.1g
	AdjustWeightError          int    `json:"adjust_weight_error"`            // 调整空差 /0.1g
	SettleWeightError          int    `json:"settle_weight_error"`            // 结算空差 /0.1g
	Length                     int    `json:"length"`                         // 长度
	StandardLengthCutSalePrice int    `json:"standard_length_cut_sale_price"` // 剪板销售价格
	OffsetLengthCutSalePrice   int    `json:"offset_length_cut_sale_price"`   // 剪版优惠单价
	LengthCutSalePrice         int    `json:"length_cut_sale_price"`          // 剪版销售单价(剪板销售价格-剪版优惠单价)
	OtherPrice                 int    `json:"other_price"`                    // 其他金额
	WarehouseId                uint64 `json:"warehouse_id"`                   // 出货仓库id
	StockRoll                  int    `json:"stock_roll"`                     // 库存可用匹数(可用库存)
	BookRoll                   int    `json:"book_roll"`                      // 预约匹数
	PurchaseRoll               int    `json:"purchase_roll"`                  // 采购匹数
	PurchaseWeight             int    `json:"purchase_weight"`                // 采购数量
	PurchaseLength             int    `json:"purchase_length"`                // 采购长度
	ShortageRoll               int    `json:"shortage_roll"`                  // 欠货匹数
	ShortageWeight             int    `json:"shortage_weight"`                // 欠货数量
	ShortageLength             int    `json:"shortage_length"`                // 欠货长度
	IsDisplayPrice             bool   `json:"is_display_price"`               // 是否显示单价
	// StockLength                int    `json:"stock_length"`                   // 库存可用长度
}

type UpdateSaleProductOrderDetailItemList []UpdateSaleProductOrderDetailItem

func (r UpdateSaleProductOrderDetailItemList) Adjust() {

}
