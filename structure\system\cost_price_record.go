package system

import (
	common_system "hcscm/common/system_consts"
	"hcscm/tools"
)

type CommonCostPriceRecord struct {
	RecordData
	SrcId                 uint64                      `json:"src_id" form:"src_id"`                                     // 源id(对应成本表id)
	Type                  common_system.CostPriceType `json:"type" form:"type"`                                         // 类型
	OldNetWeightPrice     int                         `json:"old_net_weight_price" form:"old_net_weight_price"`         // 旧净重成本单价
	OldBuoyantWeightPrice int                         `json:"old_buoyant_weight_price" form:"old_buoyant_weight_price"` // 旧毛重成本单价
	NewNetWeightPrice     int                         `json:"new_net_weight_price" form:"new_net_weight_price"`         // 新净重成本单价
	NewBuoyantWeightPrice int                         `json:"new_buoyant_weight_price" form:"new_buoyant_weight_price"` // 新毛重成本单价
}

type AddCostPriceRecordParamList []*AddCostPriceRecordParam

type AddCostPriceRecordParam struct {
	Param
	CommonCostPriceRecord
}

func (r *AddCostPriceRecordParam) Adjust() {
}

func (p AddCostPriceRecordParam) Validate() (err error) {
	return
}

type AddCostPriceRecordData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type UpdateCostPriceRecordParam struct {
	Param
	AddCostPriceRecordParam
	Id uint64 `json:"id"`
}

func (r *UpdateCostPriceRecordParam) Adjust() {
	r.AddCostPriceRecordParam.Adjust()
}

func (p UpdateCostPriceRecordParam) Validate() (err error) {
	err = p.AddCostPriceRecordParam.Validate()
	return
}

type UpdateCostPriceRecordData struct {
	ResponseData
	Id uint64 `json:"id"`
}

type UpdateCostPriceRecordBusinessCloseParam struct {
	Param
	Id            uint64                      `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateCostPriceRecordStatusParam struct {
	Param
	Id uint64 `json:"id"`
}

type GetCostPriceRecordQuery struct {
	Query
	Id uint64 `form:"id"` // id
}

type GetCostPriceRecordListQuery struct {
	ListQuery
	SrcIds tools.QueryIntList          `json:"src_ids" form:"src_ids"` // 源id(对应成本表id)
	Type   common_system.CostPriceType `json:"type" form:"type"`       // 类型
}

type GetCostPriceRecordData struct {
	RecordData
	CommonCostPriceRecord
}

type GetCostPriceRecordDataList []*GetCostPriceRecordData

func (g GetCostPriceRecordDataList) Adjust() {

}

type DeleteCostPriceRecordParam struct {
	Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteCostPriceRecordData struct {
	ResponseData
	Id []uint64 `json:"id"`
}
