package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	cus_error "hcscm/common/errors"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	dic_pb "hcscm/extern/pb/dictionary"
	dye_pb "hcscm/extern/pb/dyeing_and_finishing"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"time"
)

type FpmProcessOutOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmProcessOutOrderRepo(tx *mysql_base.Tx) *FpmProcessOutOrderRepo {
	return &FpmProcessOutOrderRepo{tx: tx}
}

func (r *FpmProcessOutOrderRepo) Add(ctx context.Context, req *structure.AddFpmProcessOutOrderParam) (data structure.AddFpmProcessOutOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
	)

	fpmProcessOutOrder := model.NewFpmProcessOutOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmProcessOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmProcessOutOrder.BusinessClose = common_system.BusinessCloseNo
	fpmProcessOutOrder.DepartmentId = info.GetDepartmentId()
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmProcessOutOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmProcessOutOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmProcessOutOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmProcessOutOrder.OrderNo = orderNo
	fpmProcessOutOrder.Number = int(number)

	fpmProcessOutOrder.TotalWeight, fpmProcessOutOrder.TotalRoll, fpmProcessOutOrder.TotalLength = req.GetTotalPWR()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmProcessOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmProcessOutOrder, err = mysql.MustCreateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmProcessOutOrderItem := model.NewFpmProcessOutOrderItem(ctx, &item)
		fpmProcessOutOrderItem.ParentId = fpmProcessOutOrder.Id
		fpmProcessOutOrderItem.ParentOrderNo = fpmProcessOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL()
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmProcessOutOrderItem.TotalWeight = tw
		fpmProcessOutOrderItem.TotalPrice = tp
		fpmProcessOutOrderItem.OutLength = tl
		fpmProcessOutOrderItem.WeightError = weightError
		fpmProcessOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmProcessOutOrderItem.PaperTubeWeight = tpp
		fpmProcessOutOrderItem.SettleWeight = tsw
		fpmProcessOutOrderItem.ActuallyWeight = taw
		fpmProcessOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmProcessOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmProcessOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmProcessOutOrderItem.Id
			itemFc.WarehouseId = fpmProcessOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeProcess
			itemFc.WarehouseOutOrderId = fpmProcessOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmProcessOutOrder.OrderNo
			itemFc.OrderTime = fpmProcessOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmProcessOutOrder.Id
	return
}

func (r *FpmProcessOutOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmProcessOutOrderParam) (data structure.UpdateFpmProcessOutOrderData, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
		itemModel          model.FpmOutOrderItem
		findCodeModel      model.FpmOutOrderItemFc
		BumModel           model.FpmProcessInOrderItemBum
		itemList           model.FpmOutOrderItemList
	)
	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmProcessOutOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，当前单据状态不能更新。")
		return
	}

	fpmProcessOutOrder.UpdateFpmProcessOutOrder(ctx, req)

	fpmProcessOutOrder.TotalWeight, fpmProcessOutOrder.TotalRoll, fpmProcessOutOrder.TotalLength = req.GetTotalPWR()

	if fpmProcessOutOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmProcessOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmProcessOutOrder.UnitId = item.UnitId
			break
		}
	}
	fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	itemIds := itemList.GetIds()

	arrangeItemIdMap := make(map[uint64]uint64) // 使用sum_stock_id作为键
	for _, item := range itemList {
		if item.SumStockId > 0 && item.ArrangeItemId > 0 {
			arrangeItemIdMap[item.SumStockId] = item.ArrangeItemId
		}
	}

	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		fcCount, _ := mysql.FindFpmOutOrderItemFcByParenTIDs(r.tx, itemIds)
		fcBumList, _ := mysql.FindFpmProcessInOrderItemBumByParenTIDs(r.tx, itemIds)
		if len(fcCount) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
			if err != nil {
				return
			}
		}
		if len(fcBumList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, BumModel, "parent_id")
			if err != nil {
				return
			}
		}
	}

	// 新增成品信息
	for _, item := range req.ItemData {
		if item.SumStockId > 0 {
			if arrangeId, exists := arrangeItemIdMap[item.SumStockId]; exists && arrangeId > 0 {
				// 如果有，则使用原来的ArrangeItemId
				item.ArrangeItemId = arrangeId
			}
		}

		fpmProcessOutOrderItem := model.NewFpmProcessOutOrderItem(ctx, &item)
		fpmProcessOutOrderItem.ParentId = fpmProcessOutOrder.Id
		fpmProcessOutOrderItem.ParentOrderNo = fpmProcessOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL()
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmProcessOutOrderItem.TotalWeight = tw
		fpmProcessOutOrderItem.TotalPrice = tp
		fpmProcessOutOrderItem.OutLength = tl
		fpmProcessOutOrderItem.WeightError = weightError
		fpmProcessOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmProcessOutOrderItem.PaperTubeWeight = tpp
		fpmProcessOutOrderItem.SettleWeight = tsw
		fpmProcessOutOrderItem.ActuallyWeight = taw
		fpmProcessOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmProcessOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmProcessOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmProcessOutOrderItem.Id
			itemFc.WarehouseId = fpmProcessOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeProcess
			itemFc.WarehouseOutOrderId = fpmProcessOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmProcessOutOrder.OrderNo
			itemFc.OrderTime = fpmProcessOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmProcessOutOrder.Id
	return
}

func (r *FpmProcessOutOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmProcessOutOrderBusinessCloseParam) (data structure.UpdateFpmProcessOutOrderData, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, v)
		if err != nil {
			return
		}
		// 更新业务状态
		err = fpmProcessOutOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmProcessOutOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (data structure.UpdateFpmProcessOutOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
	)

	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	updateItems, err = r.judgeAuditPass(id, fpmProcessOutOrder, ctx)

	// 审核
	err = fpmProcessOutOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return
	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmProcessOutOrder.Id, true)
	if err != nil {
		return
	}

	data.WarehouseOutTime = tools.MyTime(fpmProcessOutOrder.WarehouseOutTime)
	data.ArrangeId = fpmProcessOutOrder.ArrangeOrderId
	return
}

func (r *FpmProcessOutOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (data structure.UpdateFpmProcessOutOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		fpmProcessOutOrder         model.FpmOutOrder
		fpmProcessOutOrderItemList model.FpmOutOrderItemList
		dyeNotifySvc               = dye_pb.NewClientDNFService()
	)

	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 获取是否被染整引用
	fpmProcessOutOrderItemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, fpmProcessOutOrder.Id)
	if err != nil {
		return
	}
	itemIds := fpmProcessOutOrderItemList.GetIds()
	err = dyeNotifySvc.GetDNFItemChildBySrcIds(ctx, itemIds)
	if err != nil {
		return
	}

	updateItems, err = r.judgeAuditWait(id, fpmProcessOutOrder, ctx)

	// 消审
	err = fpmProcessOutOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return
	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmProcessOutOrder.Id, false)
	if err != nil {
		return
	}

	data.WarehouseOutTime = tools.MyTime(fpmProcessOutOrder.WarehouseOutTime)
	data.ArrangeId = fpmProcessOutOrder.ArrangeOrderId
	return
}

func (r *FpmProcessOutOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmProcessOutOrderStatusData, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
	)

	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 拒绝/驳回
	err = fpmProcessOutOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return

	}
	return
}

func (r *FpmProcessOutOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmProcessOutOrderStatusData, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
		fcOutList          model.FpmOutOrderItemFcList
	)

	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	data.Id = fpmProcessOutOrder.Id
	data.ArrangeId = fpmProcessOutOrder.ArrangeOrderId

	// 作废
	err = fpmProcessOutOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return
	}

	// 获取细码的库存id，用来解除占用
	if fpmProcessOutOrder.ArrangeOrderId > 0 {
		fcOutList, _ = mysql.FindFpmOutOrderItemFcByOrderId(r.tx, fpmProcessOutOrder.Id)
		if len(fcOutList) > 0 {
			data.StockDetailIds = fcOutList.GetStockIDs()
		}
	}

	return
}

func (r *FpmProcessOutOrderRepo) Get(ctx context.Context, req *structure.GetFpmProcessOutOrderQuery) (data structure.GetFpmProcessOutOrderData, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
		itemDatas          model.FpmOutOrderItemList
		fineCodeList       model.FpmOutOrderItemFcList
		detailStockList    model.StockProductDetailList
		warehousePB        = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB             = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc             = dic_pb.NewDictionaryClient()
	)
	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmProcessOutOrderData{}
	r.swapListModel2Data(fpmProcessOutOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmProcessOutOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmProcessOutOrder.AuditStatus != common_system.OrderStatusAudited {
			stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
				StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
			for _, v := range stockList {
				// 库存信息
				itemGetData.SumStockRoll = v.AvailableRoll
				itemGetData.SumStockWeight = v.AvailableWeight
				itemGetData.SumStockLength = v.Length
			}
		}
		// 添加细码信息
		fineCodeList, err = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, itemData.Id)
		if err != nil {
			return
		}

		mUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "measurement_unit_id")
		wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
		stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitFcIds)
		binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
		detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
		if err != nil {
			return
		}
		dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
		dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

		for _, fineCode := range fineCodeList {
			fineCodeGetData := structure.GetFpmOutOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
			fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
			fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.OrderTime = tools.MyTime(fineCode.OrderTime)
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			fineCodeGetData.IsBooked = fineCode.IsBooked
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			detailStock := detailStockList.Pick(fineCode.StockId)

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)

			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmProcessOutOrderRepo) GetList(ctx context.Context, req *structure.GetFpmOutOrderListQuery) (list structure.GetFpmProcessOutOrderDataList, total int, err error) {
	var (
		orders    model.FpmOutOrderList
		bizPB     = biz_pb.NewClientBizUnitService()
		saleSysPB = sale_sys_pb.NewSaleSystemClient()
		// emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmOutOrder(r.tx, req)
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		// empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orders, "unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	// g.Go(func(ctx context.Context) error {
	//	var err1 error
	//	storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
	//	empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
	//	if err1 != nil {
	//		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
	//	}
	//	return nil
	// })

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		dst := structure.GetFpmProcessOutOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.ArrangeOrderId = src.ArrangeOrderId
		dst.ArrangeOrderNo = src.ArrangeOrderNo
		dst.SaleSystemId = src.SaleSystemId
		dst.ProcessUnitId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.TotalPrice = src.TotalPrice
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.VoucherNumber = src.VoucherNumber
		dst.TextureUrl = src.TextureUrl

		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.ProcessName = bizNameMap[src.BizUnitId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.UnitName = unitNameMap[src.UnitId]
		list = append(list, dst)
	}
	return
}

func (r *FpmProcessOutOrderRepo) swapListModel2Data(src model.FpmOutOrder, dst *structure.GetFpmProcessOutOrderData, ctx context.Context) {
	var (
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		userPB      = empl_pb.NewClientEmployeeService()
		userName    = make(map[uint64]string)
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		bizService  = biz_pb.NewClientBizUnitService()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleSysMap, err2 := saleSysPB.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	userName, _ = userPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ArrangeOrderId = src.ArrangeOrderId
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.SaleSystemId = src.SaleSystemId
	dst.ProcessUnitId = src.BizUnitId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
	dst.StoreKeeperId = src.StoreKeeperId
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.TotalPrice = src.TotalPrice
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.VoucherNumber = src.VoucherNumber
	dst.TextureUrl = src.TextureUrl

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.WarehouseName = warehouseName
	dst.UnitName = unitName
	if val, ok := saleSysMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.ProcessName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	dst.OutOrderType = src.OutOrderType
	dst.OutOrderTypeName = src.OutOrderType.String()
}

func (r *FpmProcessOutOrderRepo) swapItemModel2Data(src model.FpmOutOrderItem, dst *structure.GetFpmProcessOutOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
		productSvc = product.NewProductClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)
	productMap, _ := productSvc.GetProductMapByIds(ctx, []uint64{src.ProductId})
	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.QuoteOrderNo = src.QuoteOrderNo
	dst.QuoteOrderItemId = src.QuoteOrderItemId
	dst.ProductId = src.ProductId
	dst.ProductCode = src.ProductCode
	dst.ProductName = src.ProductName
	dst.CustomerId = src.CustomerId
	dst.ProductColorId = src.ProductColorId
	dst.ProductColorCode = src.ProductColorCode
	dst.ProductColorName = src.ProductColorName
	dst.ProductLevelId = src.ProductLevelId
	dst.ProductWidth = src.ProductWidth
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductGramWeight = src.ProductGramWeight
	dst.ProductRemark = src.ProductRemark
	dst.ProductCraft = src.ProductCraft
	dst.ProductIngredient = src.ProductIngredient
	dst.OutRoll = src.OutRoll
	dst.SumStockId = src.SumStockId
	dst.TotalWeight = src.TotalWeight
	dst.WeightError = src.WeightError
	dst.SettleWeight = src.SettleWeight
	dst.UnitId = src.UnitId
	dst.OutLength = src.OutLength
	dst.Remark = src.Remark
	dst.ReturnRoll = src.ReturnRoll
	dst.ReturnWeight = src.ReturnWeight
	dst.ReturnLength = src.ReturnLength
	dst.DyeRoll = src.DyeRoll
	dst.DyeWeight = src.DyeWeight

	// 转义
	dst.UnitName = unitName
	if val, ok := customerMap[dst.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
	dst.SumStockRoll = src.SumStockRoll
	dst.SumStockLength = src.SumStockLength
	dst.SumStockWeight = src.SumStockWeight
	if productRes, ok := productMap[src.ProductId]; ok {
		dst.FinishProductWidthAndWightUnit.FinishProductWidthUnitId = productRes.FinishProductWidthUnitId
		dst.FinishProductWidthAndWightUnit.FinishProductWidthUnitName = productRes.FinishProductWidthUnitName
		dst.FinishProductWidthAndWightUnit.FinishProductWidthAndUnitName = productRes.FinishProductWidthAndUnitName
		dst.FinishProductWidthAndWightUnit.ProductGramWeight = productRes.ProductGramWeight
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitId = productRes.FinishProductGramWeightUnitId
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightAndUnitName = productRes.FinishProductGramWeightAndUnitName
		dst.FinishProductWidthAndWightUnit.ProductWidth = productRes.ProductWidth
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitName = productRes.FinishProductGramWeightUnitName
	}
	dst.SettleErrorWeight = src.SettleErrorWeight
}

func (r *FpmProcessOutOrderRepo) swapFcModel2Data(src model.FpmOutOrderItemFc, dst *structure.GetFpmOutOrderItemFcData, ctx context.Context) {
	var (
		warehousePB          = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB               = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dictionaryDetailsSvc = dic_pb.NewDictionaryClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, src.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	dicNameMap, _ := dictionaryDetailsSvc.GetDictionaryNameByIds(ctx, []uint64{src.FinishProductWidthUnitId, src.FinishProductGramWeightUnitId})

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.Roll = src.Roll
	dst.WarehouseBinId = src.WarehouseBinId
	dst.VolumeNumber = src.VolumeNumber
	dst.WarehouseOutType = src.WarehouseOutType
	dst.WarehouseOutOrderId = src.WarehouseOutOrderId
	dst.WarehouseOutOrderNo = src.WarehouseOutOrderNo
	dst.WarehouseInType = src.WarehouseInType
	dst.WarehouseInOrderId = src.WarehouseInOrderId
	dst.WarehouseInOrderNo = src.WarehouseInOrderNo
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.StockId = src.StockId
	dst.SumStockId = src.SumStockId
	dst.BaseUnitWeight = src.BaseUnitWeight
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.WeightError = src.WeightError
	dst.UnitId = src.UnitId
	dst.Length = src.Length
	dst.SettleWeight = src.SettleWeight
	dst.DigitalCode = src.DigitalCode
	dst.ShelfNo = src.ShelfNo
	dst.ContractNumber = src.ContractNumber
	dst.CustomerPoNum = src.CustomerPoNum
	dst.AccountNum = src.AccountNum
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.BuildFPResp(src.ProductWidth, src.ProductGramWeight, dicNameMap[src.FinishProductWidthUnitId][1],
		dicNameMap[src.FinishProductGramWeightUnitId][1], src.FinishProductWidthUnitId, src.FinishProductGramWeightUnitId)
	dst.StockRemark = src.StockRemark
	dst.Remark = src.Remark
	dst.InternalRemark = src.InternalRemark
	dst.ScanUserId = src.ScanUserId
	dst.ScanUserName = src.ScanUserName
	dst.ScanTime = tools.MyTime(src.ScanTime)
	dst.WarehouseId = src.WarehouseId
	dst.OrderTime = tools.MyTime(src.OrderTime)
	dst.ArrangeItemFcId = src.ArrangeItemFcId
	dst.IsBooked = src.IsBooked
	// 转义
	dst.WarehouseBinName = binName
	dst.UnitName = unitName
}

func (r *FpmProcessOutOrderRepo) judgeAuditPass(id uint64, order model.FpmOutOrder, ctx context.Context) (
	updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		// arrangeItemIds       = make([]uint64, 0)
		arrangeOrder model.FpmArrangeOrder
	)

	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		totalRoll := 0
		totalLength := 0

		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]

		fineCodeList, _ := mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		if item.OutRoll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(cus_error.NewError(cus_error.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(fineCodeList) == 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
			return
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			// 通过arrangeItem.ParentId获取配布单
			arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, arrangeItem.ParentId)
			if err != nil {
				return
			}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll -= arrangeItem.PushRoll
			updateItemWeight.BookWeight -= arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.BookOrderId = arrangeOrder.SrcId
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductProcessOutPass
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
		for _, fineCode := range fineCodeList {
			totalRoll = totalRoll + fineCode.Roll
			totalLength += fineCode.Length
			updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParamBack(ctx, swap2StockFieldParam))
		}
		//
		if totalRoll != item.OutRoll {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.OutLength > 0 && totalLength > 0 && item.OutLength != totalLength {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}
	updateBookWeight = append(updateBookWeight, updateWeight...)
	updateItems = updateBookWeight
	return
}

func (r *FpmProcessOutOrderRepo) judgeAuditWait(id uint64, order model.FpmOutOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
	)

	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		fineCodeList, _ := mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		for _, fineCode := range fineCodeList {
			updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam))
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll += arrangeItem.PushRoll
			updateItemWeight.BookWeight += arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductProcessOutWait
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
	}
	updateWeight = append(updateWeight, updateBookWeight...)
	updateItems = updateWeight
	return
}

func (r *FpmProcessOutOrderRepo) GetProcessOutItemEnumList(ctx context.Context, req *structure.GetFpmOutOrderItemListQuery) (data structure.GetFpmProcessOutOrderItemDataUseByDyeList, count int, err error) {
	var (
		judgeUseByDye    = true
		itemDataList     = model.FpmOutOrderItemList{}
		listQuery        = structure.GetFpmOutOrderListQuery{}
		orderList        = model.FpmOutOrderList{}
		bizPB            = biz_pb.NewClientBizUnitService()
		warehousePB      = warehouse_pb.NewPhysicalWarehouseClient()
		warehouseIds     = make([]uint64, 0)
		processIds       = make([]uint64, 0)
		warehouseNameMap = make(map[uint64]string, 0)
		processNameMap   = make(map[uint64]string, 0)
	)

	listQuery.GetParmsValues(req, judgeUseByDye)

	orderList, _, err = mysql.SearchFpmOutOrderNoPage(r.tx, &listQuery)
	if err != nil {
		return
	}
	req.ListIDs = orderList.GetIds()
	warehouseIds = orderList.GetWarehouseIDs()
	processIds = orderList.GetProcessUnitIDs()
	req.JudgeUseByDye = true
	itemDataList, count, err = mysql.SearchFpmOutOrderItem(r.tx, req)
	if err != nil {
		return
	}
	warehouseNameMap, _ = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	processNameMap, _ = bizPB.GetBizUnitNameByIds(ctx, processIds)
	for _, item := range itemDataList {
		var getItemData = structure.GetFpmProcessOutOrderItemDataUseByDye{}
		r.swapItemModel2UseByDyeData(item, &getItemData, ctx)
		if o := orderList.Pick(item.ParentId); o.Id > 0 {
			getItemData.ProcessUnitId = o.BizUnitId
			getItemData.ProcessUnitName = processNameMap[o.BizUnitId]
			getItemData.WarehouseId = o.WarehouseId
			getItemData.WarehouseOutTime = tools.MyTime(o.WarehouseOutTime)
			getItemData.WarehouseName = warehouseNameMap[o.WarehouseId]
		}
		data = append(data, getItemData)
	}
	return
}

func (r *FpmProcessOutOrderRepo) swapItemModel2UseByDyeData(src model.FpmOutOrderItem, dst *structure.GetFpmProcessOutOrderItemDataUseByDye, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.QuoteOrderNo = src.QuoteOrderNo
	dst.QuoteOrderItemId = src.QuoteOrderItemId
	dst.ProductId = src.ProductId
	dst.ProductCode = src.ProductCode
	dst.ProductName = src.ProductName
	dst.CustomerId = src.CustomerId
	dst.ProductColorId = src.ProductColorId
	dst.ProductColorCode = src.ProductColorCode
	dst.ProductColorName = src.ProductColorName
	dst.ProductLevelId = src.ProductLevelId
	dst.ProductWidth = src.ProductWidth
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductGramWeight = src.ProductGramWeight
	dst.ProductRemark = src.ProductRemark
	dst.ProductCraft = src.ProductCraft
	dst.ProductIngredient = src.ProductIngredient
	dst.OutRoll = src.OutRoll
	dst.SumStockId = src.SumStockId
	dst.TotalWeight = src.TotalWeight
	dst.WeightError = src.WeightError
	dst.SettleWeight = src.SettleWeight
	dst.UnitId = src.UnitId
	dst.OutLength = src.OutLength
	dst.Remark = src.Remark
	dst.ReturnRoll = src.ReturnRoll
	dst.ReturnWeight = src.ReturnWeight
	dst.ReturnLength = src.ReturnLength
	dst.DyeRoll = src.DyeRoll
	dst.DyeWeight = src.DyeWeight
	dst.WaitDyeRoll = src.OutRoll - src.DyeRoll
	dst.WaitDyeWeight = src.TotalWeight - src.DyeWeight

	// 转义
	dst.UnitName = unitName
	if val, ok := customerMap[dst.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
	stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{StockProductId: src.SumStockId, ProductColorId: src.ProductColorId, DyelotNumber: src.DyeFactoryDyelotNumber})
	for _, v := range stockList {
		// 库存信息,2023-12-20 需求1001412改为获取可用数量和匹数
		dst.SumStockRoll = v.AvailableRoll
		dst.SumStockWeight = v.AvailableWeight
		dst.SumStockLength = v.Length
	}
}

// 根据变更单作废单据
func (r *FpmProcessOutOrderRepo) UpdateStatusCancelUseByChangeOrder(ctx context.Context, id uint64) (data structure.UpdateFpmProcessOutOrderStatusData, err error) {
	var (
		fpmProcessOutOrder model.FpmOutOrder
		info               = metadata.GetLoginInfo(ctx)
	)

	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 单据为待审核/已驳回/已作废状态
	if fpmProcessOutOrder.AuditStatus == common_system.OrderStatusPendingAudit || fpmProcessOutOrder.AuditStatus == common_system.OrderStatusRejected || fpmProcessOutOrder.AuditStatus == common_system.OrderStatusVoided {
		fpmProcessOutOrder.AuditorName = info.GetUserName()
		fpmProcessOutOrder.AuditorId = info.GetUserId()
		fpmProcessOutOrder.AuditDate = time.Now()
		fpmProcessOutOrder.AuditStatus = common_system.OrderStatusVoided
	}
	// 单据已审核状态
	if fpmProcessOutOrder.AuditStatus == common_system.OrderStatusAudited {
		// todo: 已审核状态逻辑待补充
	}
	fpmProcessOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmProcessOutOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmProcessOutOrderRepo) GetByItemID(ctx context.Context, itemId uint64) (data structure.GetFpmProcessOutOrderData, err error) {
	var (
		fpmProcessOutOrder     model.FpmOutOrder
		fpmProcessOutOrderItem model.FpmOutOrderItem
	)
	fpmProcessOutOrderItem, err = mysql.MustFirstFpmOutOrderItemByID(r.tx, itemId)
	if err != nil {
		return
	}
	fpmProcessOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, fpmProcessOutOrderItem.ParentId)
	if err != nil {
		return
	}
	r.swapListModel2Data(fpmProcessOutOrder, &data, ctx)
	return
}

// 根据item_id获取细码
func (r *FpmProcessOutOrderRepo) GetFCListByItemID(ctx context.Context, itemId uint64) (data structure.GetFpmOutOrderItemFcDataList, err error) {
	var (
		fcList          = model.FpmOutOrderItemFcList{}
		_data           = make(structure.GetFpmOutOrderItemFcDataList, 0)
		stockIds        = set.NewUint64Set()
		warehouseBinIds = set.NewUint64Set()
		// munitIds                             = set.NewUint64Set()
		dicIds                               = set.NewUint64Set()
		productIds                           = set.NewUint64Set()
		stockDetailForProcessOutOrderItemMap = make(map[uint64]structure.GetStockDetailForProcessOutOrderItemData, 0)
	)

	fcList, err = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, itemId)
	if err != nil {
		return
	}

	for _, fc := range fcList {
		stockIds.Add(fc.StockId)
	}

	// 找库存
	stockList, err := mysql.FindStockProductDetailByIDs(r.tx, stockIds.List())
	if err != nil {
		return
	}
	for _, stockDetail := range stockList {
		warehouseBinIds.Add(stockDetail.WarehouseBinId)
		// munitIds.Add(stockDetail.MeasurementUnitId)
		dicIds.Add(stockDetail.FinishProductGramWeightUnitId)
		dicIds.Add(stockDetail.FinishProductWidthUnitId)
		productIds.Add(stockDetail.ProductId)
	}

	// 转义
	// munitNameMap, _ := base_info_pb.NewInfoBaseMeasurementUnitClient().
	// 	GetInfoBaseMeasurementUnitNameByIds(ctx, munitIds.List())
	warehouseBinNameMap, _ := warehouse_pb.NewPhysicalWarehouseClient().
		GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds.List())
	dicNameMap, _ := dic_pb.NewDictionaryClient().
		GetDictionaryNameByIds(ctx, dicIds.List())
	productMap, _ := product.NewProductClient().GetProductMapByIds(ctx, productIds.List())

	for _, stockDetail := range stockList {
		item := structure.GetStockDetailForProcessOutOrderItemData{
			Id:                  stockDetail.Id,
			WarehouseBinId:      stockDetail.WarehouseBinId,
			WarehouseBinName:    warehouseBinNameMap[stockDetail.WarehouseBinId],
			DyeFactoryColorCode: stockDetail.DyeFactoryColorCode,
			Remark:              stockDetail.Remark,
			ContractNumber:      stockDetail.ContractNumber,
			CustomerAccountNum:  stockDetail.CustomerAccountNum,
			DyelotNumber:        stockDetail.DyelotNumber,
		}
		// 幅宽克重处理
		item.BuildFPResp(stockDetail.FinishProductWidth, stockDetail.FinishProductGramWeight, dicNameMap[stockDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockDetail.FinishProductGramWeightUnitId][1], stockDetail.FinishProductWidthUnitId, stockDetail.FinishProductGramWeightUnitId)

		if product, ok := productMap[stockDetail.ProductId]; ok {
			item.MeasurementUnitId = product.MeasurementUnitId
			item.MeasurementUnitName = product.MeasurementUnitName
		}
		stockDetailForProcessOutOrderItemMap[stockDetail.Id] = item
	}
	for _, fc := range fcList {
		o := fc.BuildResp()
		stockDetailForProcessOutOrderItem := stockDetailForProcessOutOrderItemMap[fc.StockId]
		o.FinishProductWidthAndWightUnit = stockDetailForProcessOutOrderItem.FinishProductWidthAndWightUnit
		o.WarehouseBinId = stockDetailForProcessOutOrderItem.WarehouseBinId
		o.WarehouseBinName = stockDetailForProcessOutOrderItem.WarehouseBinName
		o.UnitId = stockDetailForProcessOutOrderItem.MeasurementUnitId
		o.UnitName = stockDetailForProcessOutOrderItem.MeasurementUnitName
		o.DyeFactoryColorCode = stockDetailForProcessOutOrderItem.DyeFactoryColorCode
		o.StockRemark = stockDetailForProcessOutOrderItem.Remark
		o.ContractNumber = stockDetailForProcessOutOrderItem.ContractNumber
		o.AccountNum = stockDetailForProcessOutOrderItem.CustomerAccountNum
		if o.DyeFactoryDyelotNumber == "" {
			o.DyeFactoryDyelotNumber = stockDetailForProcessOutOrderItem.DyelotNumber
		}
		_data = append(_data, o)
	}
	data = _data
	return
}

func (r *FpmProcessOutOrderRepo) UpdateProcessOutItemData(ctx context.Context, processItemRtn map[uint64][2]int, isAdd bool) (err error) {
	var (
		itemIds  = make([]uint64, 0)
		itemList = model.FpmOutOrderItemList{}
	)
	if len(processItemRtn) == 0 {
		return
	}

	for k, _ := range processItemRtn {
		itemIds = append(itemIds, k)
	}

	itemList, err = mysql.FindFpmOutOrderItemByIDs(r.tx, itemIds)
	if err != nil {
		return
	}

	for _, item := range itemList {

		err = item.UpdateRollAndWeight(processItemRtn[item.Id], isAdd)
		if err != nil {
			return
		}
		item, err = mysql.MustUpdateFpmOutOrderItem(r.tx, item)
		if err != nil {
			return
		}
	}

	return
}
