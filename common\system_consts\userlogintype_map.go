package common

func GetUserLoginTypeMap() (r map[UserLoginType]string) {
	l := []UserLoginType{UserLoginTypeUser, UserLoginTypeTenantManagement, UserLoginTypeBizUnit}
	r = make(map[UserLoginType]string)
	for _, k := range l {
		r[k] = k.String()
	}
	return r
}
func GetUserLoginTypeReverseMap() (r map[string]UserLoginType) {
	l := []UserLoginType{UserLoginTypeUser, UserLoginTypeTenantManagement, UserLoginTypeBizUnit}
	r = make(map[string]UserLoginType)
	for _, k := range l {
		r[k.String()] = k
	}
	return r
}
func GetUserLoginTypeReverseIntMap() (r map[string]int) {
	l := []UserLoginType{UserLoginTypeUser, UserLoginTypeTenantManagement, UserLoginTypeBizUnit}
	r = make(map[string]int)
	for _, k := range l {
		r[k.String()] = int(k)
	}
	return r
}

func (t UserLoginType) Check() bool {
	l := []UserLoginType{UserLoginTypeUser, UserLoginTypeTenantManagement, UserLoginTypeBizUnit}
	for i := range l {
		if l[i] == t {
			return true
		}
	}
	return false
}
