package biz_unit

import structure_base "hcscm/structure/system"

type Req struct {
	structure_base.ListQuery
	Ids          []uint64
	SaleUserId   uint64
	SaleSystemID uint64
	Status       int

	Id       uint64
	Category int
	Phone    string
}

type Res struct {
	Id                 uint64   `json:"id"`                   // id
	Name               string   `json:"name"`                 // 名称
	FullName           string   `json:"full_name"`            // 全称
	Code               int      `json:"-"`                    // 数字编号（自增）
	CustomCode         string   `json:"code"`                 // 编号
	MainUnitTypeId     uint64   `json:"main_unit_type_id"`    // 主要类型id
	MainUnitTypeName   string   `json:"main_unit_type_name"`  // 主要往来单位类型名称
	MainBizUnitType    int      `json:"main_biz_unit_type"`   // 主要往来单位类型类型
	UnitTypeID         []uint64 `json:"unit_type_id"`         // 类型id
	SaleSystemID       uint64   `json:"sale_system_id"`       // 主要所属营销体系id
	SaleSystemIds      []uint64 `json:"sale_system_ids"`      // 所属营销体系id
	ContactName        string   `json:"contact_name"`         // 联系人名称
	Phone              string   `json:"phone"`                // 联系电话
	Address            string   `json:"address"`              // 地址
	Email              string   `json:"email"`                // 邮箱
	Remark             string   `json:"remark"`               // 备注
	SettleType         int      `json:"settle_type"`          // 结算类型
	SettleCycle        int      `json:"settle_cycle"`         // 结算天数
	CreditLimit        int      `json:"credit_limit"`         // 信用额度
	CreditLevel        int      `json:"credit_level"`         // 信用等级
	OrderFollowerID    uint64   `json:"order_follower_id"`    // 跟单员id
	OrderFollowerName  string   `json:"order_follower_name"`  // 跟单员名称
	OrderQcUserId      uint64   `json:"order_qc_user_id"`     // 跟单QC员id
	OrderQcUserName    string   `json:"order_qc_user_name"`   // 跟单QC员名称
	SaleAreaID         uint64   `json:"sale_area_id"`         // 销售区域id
	SaleGroupID        uint64   `json:"sale_group_id"`        // 销售群体id
	CreditCode         string   `json:"credit_code"`          // 社会统一信用代码
	SaleUserId         uint64   `json:"sale_user_id"`         // 销售员id
	SaleUserName       string   `json:"sale_user_name"`       // 销售员名称
	CategoryName       string   `json:"category_name"`        // 供应商 | 客户
	UnitTypeName       string   `json:"unit_type_name"`       // 类型名称
	SaleSystemName     string   `json:"sale_system_name"`     // 主要所属营销体系名称
	SaleSystemNames    string   `json:"sale_system_names"`    // 所属营销体系名称
	Status             int      `json:"status"`               // 状态
	IsBlacklist        bool     `json:"is_blacklist"`         // 是否黑名单
	SaleGroupName      string   `json:"sale_group_name"`      // 销售群体名称
	SaleAreaName       string   `json:"sale_area_name"`       // 销售区域名称
	SettleTypeName     string   `json:"settle_type_name"`     // 结算类型名称
	CreditLevelName    string   `json:"credit_level_name"`    // 信用等级名称
	DnfChargingMethod  int      `json:"dnf_charging_method"`  // 染费收费方式 1坯布数量 2长度 3成品数量
	OrderFollowerPhone string   `json:"order_follower_phone"` // 跟单电话
	FaxNumber          string   `json:"fax_number"`           // 传真
	Category           int      `json:"category"`             // 类别 1供应商 2客户
}

type ResList []Res

func (r ResList) Pick(id uint64) (o Res) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r ResList) PickBySellerID(sellerID uint64) (o ResList) {
	list := make(ResList, 0)
	for _, t := range r {
		if t.SaleUserId == sellerID {
			list = append(list, t)
		}
	}
	o = list
	return
}

func (r ResList) PickByName(name string) (o Res) {
	for _, t := range r {
		if t.Name == name {
			return t
		}
	}
	return
}

type SaleRes struct {
	SaleGroupID   uint64 `json:"sale_group_id"`   // 销售群体id
	SaleGroupName string `json:"sale_group_name"` // 销售群体名称
	SaleAreaID    uint64 `json:"sale_area_id"`    // 销售区域id
	SaleAreaCode  string `json:"sale_area_code"`  // 销售区域编号
	SaleAreaName  string `json:"sale_area_name"`  // 销售区域名称
}

type SaleResList []SaleRes

func (r SaleResList) PickSaleGroupByName(name string) (o SaleRes) {
	if name == "" {
		return
	}
	for _, t := range r {
		if t.SaleGroupName == name {
			return t
		}
	}
	return
}

func (r SaleResList) PickSaleAreaByCodeOrName(code, name string) (o SaleRes) {
	if code != "" {
		for _, t := range r {
			if t.SaleAreaCode == code {
				return t
			}
		}
	} else {
		for _, t := range r {
			if t.SaleAreaName == name {
				return t
			}
		}
	}
	return
}
