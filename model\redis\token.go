package redis

import (
	"context"
	"strconv"
	"time"

	"hcscm/tools"
)

var adminToken = NewDuplexKVOperate(getCmd, "admin_token", 7*24*time.Hour)

// 通过令牌获取后台用户身份
func GetAdminUserByToken(ctx context.Context, token string) (userID uint64, exist bool, err error) {
	userID, exist, err = adminToken.GetUInt64Key(ctx, token)
	return
}

// 通过后台用户身份获取令牌
func GetTokenByAdminUser(ctx context.Context, userID uint64) (tokens []string, err error) {
	key := strconv.FormatUint(userID, 10)
	tokens, err = adminToken.GetValues(ctx, key)
	return
}

// 创建后台令牌
func AddAdminUserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = adminToken.AddValue(ctx, key, token)
	return
}

// 删除后台token
func DeleteAdminUserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = adminToken.DelKeyValue(ctx, key, token)
	return
}

// 清空后台token
func FlushAdminUserToken(ctx context.Context, userID uint64) (err error) {
	value := tools.UInt642String(userID)
	err = adminToken.DelKey(ctx, value)
	return
}

// 清空后台token
func FlushAdminToken(ctx context.Context, userID uint64, tokens []string) (err error) {
	key := tools.UInt642String(userID)
	for _, token := range tokens {
		err = adminToken.DelKeyValue(ctx, key, token)
		if err != nil {
			return
		}
	}
	return
}

var h5Token = NewDuplexKVOperate(getCmd, "h5_token", 7*24*time.Hour)

// 通过令牌获取后台用户身份
func GetH5UserByToken(ctx context.Context, token string) (userID uint64, exist bool, err error) {
	userID, exist, err = h5Token.GetUInt64Key(ctx, token)
	return
}

// 通过后台用户身份获取令牌
func GetTokenByH5User(ctx context.Context, userID uint64) (tokens []string, err error) {
	key := strconv.FormatUint(userID, 10)
	tokens, err = h5Token.GetValues(ctx, key)
	return
}

// 创建后台令牌
func AddH5UserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = h5Token.AddValue(ctx, key, token)
	return
}

// 删除后台token
func DeleteH5UserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = h5Token.DelKeyValue(ctx, key, token)
	return
}

// 清空后台token
func FlushH5UserToken(ctx context.Context, userID uint64) (err error) {
	value := tools.UInt642String(userID)
	err = h5Token.DelKey(ctx, value)
	return
}

// 清空后台token
func FlushH5Token(ctx context.Context, userID uint64, tokens []string) (err error) {
	key := tools.UInt642String(userID)
	for _, token := range tokens {
		err = h5Token.DelKeyValue(ctx, key, token)
		if err != nil {
			return
		}
	}
	return
}

var pdaToken = NewDuplexKVOperate(getCmd, "pda_token", 7*24*time.Hour)

// 通过令牌获取PDA用户身份
func GetPDAUserByToken(ctx context.Context, token string) (userID uint64, exist bool, err error) {
	userID, exist, err = pdaToken.GetUInt64Key(ctx, token)
	return
}

// 通过PDA用户身份获取令牌
func GetTokenByPDAUser(ctx context.Context, userID uint64) (tokens []string, err error) {
	key := strconv.FormatUint(userID, 10)
	tokens, err = pdaToken.GetValues(ctx, key)
	return
}

// 创建PDA令牌
func AddPDAUserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = pdaToken.AddValue(ctx, key, token)
	return
}

// 删除PDA token
func DeletePDAUserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = pdaToken.DelKeyValue(ctx, key, token)
	return
}

// 清空PDAtoken
func FlushPDAToken(ctx context.Context, userID uint64, tokens []string) (err error) {
	key := tools.UInt642String(userID)
	for _, token := range tokens {
		err = pdaToken.DelKeyValue(ctx, key, token)
		if err != nil {
			return
		}
	}
	return
}

// 清空PDAtoken
func FlushPDAUserToken(ctx context.Context, userID uint64) (err error) {
	value := tools.UInt642String(userID)
	err = pdaToken.DelKey(ctx, value)
	return
}

var mpToken = NewDuplexKVOperate(getCmd, "mp_token", 7*24*time.Hour)

// 通过令牌获取 MP 用户身份
func GetMPUserByToken(ctx context.Context, token string) (userID uint64, exist bool, err error) {
	userID, exist, err = mpToken.GetUInt64Key(ctx, token)
	return
}

// 通过 MP 用户身份获取令牌
func GetTokenByMPUser(ctx context.Context, userID uint64) (tokens []string, err error) {
	key := strconv.FormatUint(userID, 10)
	tokens, err = mpToken.GetValues(ctx, key)
	return
}

// 创建MP令牌
func AddMPUserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = mpToken.AddValue(ctx, key, token)
	return
}

// 删除MP token
func DeleteMPUserToken(ctx context.Context, userID uint64, token string) (err error) {
	key := tools.UInt642String(userID)
	err = mpToken.DelKeyValue(ctx, key, token)
	return
}

// 清空MP token
func FlushMPUserToken(ctx context.Context, userID uint64) (err error) {
	value := tools.UInt642String(userID)
	err = mpToken.DelKey(ctx, value)
	return
}

// 清空MP token
func FlushMPToken(ctx context.Context, userID uint64, tokens []string) (err error) {
	key := tools.UInt642String(userID)
	for _, token := range tokens {
		err = mpToken.DelKeyValue(ctx, key, token)
		if err != nil {
			return
		}
	}
	return
}
