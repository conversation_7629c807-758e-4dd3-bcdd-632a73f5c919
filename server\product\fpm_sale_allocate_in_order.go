package product

import (
	"github.com/gin-gonic/gin"
	aggs "hcscm/aggs/product"
	common_product "hcscm/common/product"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/redis"
	"hcscm/server/system"
	svc "hcscm/service/product"
	structure "hcscm/structure/product"
)

// @Tags		【销售调拨进仓单】
// @Summary	添加销售调拨进仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmSaleAllocateInOrderParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.AddFpmSaleAllocateInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/addFpmSaleAllocateInOrder [post]
func AddFpmSaleAllocateInOrder(c *gin.Context) {
	var (
		q    = &structure.AddFpmSaleAllocateInOrderParam{}
		data = structure.AddFpmSaleAllocateInOrderData{}
		svc  = svc.NewFpmSaleAllocateInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	if err = q.CheckVNumber(); err != nil {
		return
	}
	q.InOrderType = common_product.WarehouseGoodInTypeSaleAllocate
	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	更新销售调拨进仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleAllocateInOrderParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.UpdateFpmSaleAllocateInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrder [put]
func UpdateFpmSaleAllocateInOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleAllocateInOrderParam{}
		data = structure.UpdateFpmSaleAllocateInOrderData{}
		svc  = svc.NewFpmSaleAllocateInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	if err = q.CheckVNumber(); err != nil {
		return
	}
	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	更新销售调拨进仓单业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleAllocateInOrderBusinessCloseParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.UpdateFpmSaleAllocateInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderBusinessClose [put]
func UpdateFpmSaleAllocateInOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleAllocateInOrderBusinessCloseParam{}
		data = structure.UpdateFpmSaleAllocateInOrderData{}
		svc  = svc.NewFpmSaleAllocateInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	更新销售调拨进仓单状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleAllocateInOrderStatusParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.UpdateFpmSaleAllocateInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusWait [put]
func UpdateFpmSaleAllocateInOrderStatusWait(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleAllocateInOrderStatusParam{}
		data = structure.UpdateFpmSaleAllocateInOrderStatusData{}

		inOrderSvc  = svc.NewFpmSaleAllocateInOrderService()
		stockSvc    = svc.NewStockProductService()
		rLocks      = make(redis.LockForRedisList, 0)
		updateItems structure.UpdateStockProductDetailParamList
		err         error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		data, updateItems, err = inOrderSvc.UpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			err = stockSvc.FpmCostPriceWaitUpdate(ctx, tx, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	更新销售调拨进仓单状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleAllocateInOrderStatusParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.UpdateFpmSaleAllocateInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusPass [put]
func UpdateFpmSaleAllocateInOrderStatusPass(c *gin.Context) {
	var (
		q          = &structure.UpdateFpmSaleAllocateInOrderStatusParam{}
		data       = structure.UpdateFpmSaleAllocateInOrderStatusData{}
		inOrderSvc = svc.NewFpmSaleAllocateInOrderService()
		stockSvc   = svc.NewStockProductService()
		rLocks     = make(redis.LockForRedisList, 0)
		ids        map[uint64]uint64
		sumIds     map[uint64]uint64
		addItems   structure.AddStockProductDetailParamList
		err        error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {

		data, addItems, err = inOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		if len(addItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, addItems, nil)
			if err != nil {
				return
			}
			ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addItems)
			if err != nil {
				return
			}
			err = stockSvc.FpmCostPricePassUpdate(ctx, tx, addItems)
			if err != nil {
				return
			}

			// 获取进仓单和出仓单信息，用于创建销售出仓单
			order, err := inOrderSvc.Get(ctx, &structure.GetFpmSaleAllocateInOrderQuery{Id: id})
			if err != nil {
				return
			}
			err = inOrderSvc.UpdateDetailStockDetailId(ctx, tx, ids, sumIds, id, false, order)

			if err != nil {
				return
			}
			//inOrderSvc.UpdateStockParams(ctx, tx, addItems, ids, sumIds, order.SaleAllocateOutId)

			outOrder, err := aggs.NewFpmOutOrderRepo(tx).Get(ctx, &structure.GetFpmOutOrderQuery{Id: order.SaleAllocateOutId})
			if err != nil {
				return
			}

			// 在库存生成后创建销售出仓单
			err = inOrderSvc.CreateSaleOutOrderAfterInOrderPass(ctx, tx, order, outOrder, ids, sumIds)
			if err != nil {
				return
			}
		}
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	更新销售调拨进仓单状态-驳回
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleAllocateInOrderStatusParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.UpdateFpmSaleAllocateInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusReject [put]
func UpdateFpmSaleAllocateInOrderStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleAllocateInOrderStatusParam{}
		data = structure.UpdateFpmSaleAllocateInOrderStatusData{}
		svc  = svc.NewFpmSaleAllocateInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusReject(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	更新销售调拨进仓单状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleAllocateInOrderStatusParam{}	true	"创建FpmSaleAllocateInOrder"
// @Success	200		{object}	structure.UpdateFpmSaleAllocateInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/updateFpmSaleAllocateInOrderStatusCancel [put]
func UpdateFpmSaleAllocateInOrderStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleAllocateInOrderStatusParam{}
		data = structure.UpdateFpmSaleAllocateInOrderStatusData{}
		svc  = svc.NewFpmSaleAllocateInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusCancel(ctx, id)
		if err != nil {
			return
		}
	}
	return
}

// @Tags		【销售调拨进仓单】
// @Summary	获取销售调拨进仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetFpmSaleAllocateInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/getFpmSaleAllocateInOrder [get]
func GetFpmSaleAllocateInOrder(c *gin.Context) {
	var (
		q    = &structure.GetFpmSaleAllocateInOrderQuery{}
		data = structure.GetFpmSaleAllocateInOrderData{}
		svc  = svc.NewFpmSaleAllocateInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【销售调拨进仓单】
// @Summary	获取销售调拨进仓单列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		download		query		int		false	"download"
//
// @Param		order_no		query		string	false	"单号"
// @Param		process_unit_id	query		int		false	"加工单位id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"进仓开始时间"
// @Param		in_time_end		query		string	false	"进仓结束时间"
// @Param		audit_status	query		string	false	"状态"
//
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmSaleAllocateInOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmSaleAllocateInOrder/getFpmSaleAllocateInOrderList [get]
func GetFpmSaleAllocateInOrderList(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmSaleAllocateInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmSaleAllocateInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	q.InOrderType = common_product.WarehouseGoodInTypeSaleAllocate
	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}
