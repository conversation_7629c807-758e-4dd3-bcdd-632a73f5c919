package sale

import (
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/redis"
	"hcscm/server/system"
	productSvc "hcscm/service/product"
	saleSvc "hcscm/service/sale"
	svc "hcscm/service/sale"
	productStructure "hcscm/structure/product"
	saleStructure "hcscm/structure/sale"
	structure "hcscm/structure/sale"

	"github.com/gin-gonic/gin"
)

// @Tags 【成品销售单】
// @Summary 添加成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddSaleProductOrderParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.AddSaleProductOrderData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/addSaleProductOrder [post]
func AddSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.AddSaleProductOrderParam{}
		data = structure.AddSaleProductOrderData{}

		err error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data.Id, _, err = saleProductOrderSvc.Add(ctx, tx, q)
	if err != nil {
		return
	}
	return
}

// @Tags 【企微快速下单-成品销售单】
// @Summary 添加成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddSaleProductOrderParamV2{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.AddSaleProductOrderData{}
// @Router /hcscm/mp/v1/sale/saleProductOrder/addSaleProductOrder [post]
func MPAddSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.AddSaleProductOrderParamV2{}
		data = structure.AddSaleProductOrderData{}

		err error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data.Id, err = saleProductOrderSvc.MPAdd(ctx, tx, q)
	if err != nil {
		return
	}
	return
}

// @Tags 【成品销售单】
// @Summary 删除成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.DeleteSaleProductOrderParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.DeleteSaleProductOrderData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/deleteSaleProductOrder [delete]
func DeleteSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.DeleteSaleProductOrderParam{}
		data = structure.DeleteSaleProductOrderData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.Delete(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 更新成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrder [put]
func UpdateSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateSaleProductOrderParam{}
		data = structure.UpdateSaleProductOrderData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【企微快速下单-成品销售单】
// @Summary 更新成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderParamV2{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderData{}
// @Router /hcscm/mp/v1/sale/saleProductOrder/updateSaleProductOrder [put]
func MPUpdateSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateSaleProductOrderParamV2{}
		data = structure.UpdateSaleProductOrderData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.MPUpdate(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 更新成品销售单业务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderBusinessCloseParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderBusinessCloseData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderBusinessClose [put]
func UpdateSaleProductOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateSaleProductOrderBusinessCloseParam{}
		data = structure.UpdateSaleProductOrderBusinessCloseData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 更新成品销售单状态-消审
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusWait [put]
func UpdateSaleProductOrderAuditStatusWait(c *gin.Context) {
	var (
		q                       = &structure.UpdateSaleProductOrderAuditStatusParam{}
		data                    = structure.UpdateSaleProductOrderAuditStatusData{}
		shortageProductOrderSvc = svc.NewShortageProductOrderService()
		fpmArrangeOrderSvc      = productSvc.NewFpmArrangeOrderService(c)
		err                     error
		exist                   bool
		rLocks                  = make(redis.LockForRedisList, 0)
		bookItemsReq            productStructure.UpdateStockProductDetailParamList
		stockProductSvc         = productSvc.NewStockProductService()
		pmcPlanSummaryReq       = &structure.AuditUpdatePushed{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()
	// 查找关联的配布单，有则报错
	exist, err = fpmArrangeOrderSvc.ExistOrder(ctx, tx, &productStructure.ExistOrderQuery{SrcId: q.Id})
	if err != nil {
		return
	}
	if exist {
		err = middleware.ErrorLog(errors.NewError(errors.ErrCodeExistArrangeOrder))
		return
	}
	// 查找关联的欠货单，有则报错
	exist, err = shortageProductOrderSvc.ExistOrder(ctx, tx, &structure.ExistOrderQuery{SaleProductOrderId: q.Id})
	if err != nil {
		return
	}
	if exist {
		err = middleware.ErrorLog(errors.NewError(errors.ErrCodeExistShortageOrder))
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	bookItemsReq, pmcPlanSummaryReq, err = saleProductOrderSvc.UpdateStatusWait(ctx, tx, q)
	if err != nil {
		return
	}

	// 占用库存
	for _, bookItem := range bookItemsReq {
		rLocks, err = stockProductSvc.AddBookProductStock(ctx, tx, rLocks, bookItem)
		if err != nil {
			return
		}
	}
	// 反写pmc计划单已下推数据
	if pmcPlanSummaryReq != nil {
		pmcPlanSvc := saleSvc.NewPmcGreyPlanOrderService()
		err = pmcPlanSvc.AuditUpdatePushed(ctx, tx, pmcPlanSummaryReq)
		if err != nil {
			return
		}
	}
	return
}

// @Tags 【成品销售单】
// @Summary 更新成品销售单状态-审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusPass [put]
// @Tags 【企微快速下单-成品销售单】
// @Summary 更新成品销售单状态-审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusPass [put]
func UpdateSaleProductOrderAuditStatusPass(c *gin.Context) {
	var (
		q                  = &structure.UpdateSaleProductOrderAuditStatusParam{}
		data               = structure.UpdateSaleProductOrderAuditStatusData{}
		err                error
		stockProductSvc    = productSvc.NewStockProductService()
		arrangeProductSvc  = productSvc.NewFpmArrangeOrderService(c)
		shortageProductSvc = saleSvc.NewShortageProductOrderService()
		rLocks             = make(redis.LockForRedisList, 0)
		bookItemsReq       productStructure.UpdateStockProductDetailParamList
		arrangeItemsReq    productStructure.AddFpmArrangeOrderParamList
		shortageItemReq    = &saleStructure.AddShortageProductOrderParam{}
		pmcPlanSummaryReq  = &structure.AuditUpdatePushed{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()
	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	bookItemsReq, arrangeItemsReq, shortageItemReq, pmcPlanSummaryReq, err = saleProductOrderSvc.UpdateStatusPass(ctx, tx, q)
	if err != nil {
		return
	}
	// 占用库存
	for _, bookItem := range bookItemsReq {
		rLocks, err = stockProductSvc.AddBookProductStock(ctx, tx, rLocks, bookItem)
		if err != nil {
			return
		}
	}
	// 审核生成配布单(按出库仓库来划分)
	for _, arrangeItem := range arrangeItemsReq {
		if len(arrangeItem.ItemData) > 0 {
			var salePlanOrderItemIds []uint64
			var orderId uint64
			orderId, salePlanOrderItemIds, err = arrangeProductSvc.AddOrder(ctx, tx, &arrangeItem)
			if err != nil {
				return
			}
			// 更新状态
			err = saleSvc.NewSaleProductPlanOrderService(ctx, false).UpdateSituStatus(
				ctx, tx, salePlanOrderItemIds, common_system.SituStatusProductArrangeOrder, true, orderId, "配布单审核")
			if err != nil {
				return
			}
		}
	}

	// 生成欠货单
	if shortageItemReq != nil {
		_, err = shortageProductSvc.AddOrder(ctx, tx, shortageItemReq)
		if err != nil {
			return
		}
	}

	// 反写pmc计划单已下推数据
	if pmcPlanSummaryReq != nil {
		pmcPlanSvc := saleSvc.NewPmcGreyPlanOrderService()
		err = pmcPlanSvc.AuditUpdatePushed(ctx, tx, pmcPlanSummaryReq)
		if err != nil {
			return
		}
	}
	return
}

// @Tags 【成品销售单】
// @Summary 更新成品销售单状态-驳回
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusReject [put]
// @Tags 【企微快速下单-成品销售单】
// @Summary 更新成品销售单状态-驳回
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusReject [put]
func UpdateSaleProductOrderAuditStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateSaleProductOrderAuditStatusParam{}
		data = structure.UpdateSaleProductOrderAuditStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.UpdateStatusReject(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 更新成品销售单状态-作废
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusCancel [put]
// @Tags 【企微快速下单-成品销售单】
// @Summary 更新成品销售单状态-作废
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateSaleProductOrderAuditStatusParam{}  true "创建SaleProductOrder"
// @Success 200 {object}  structure.UpdateSaleProductOrderAuditStatusData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/updateSaleProductOrderAuditStatusCancel [put]
func UpdateSaleProductOrderAuditStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateSaleProductOrderAuditStatusParam{}
		data = structure.UpdateSaleProductOrderAuditStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.UpdateStatusCancel(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 获取成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetSaleProductOrderData{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/getSaleProductOrder [get]
func GetSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.GetSaleProductOrderQuery{}
		data = structure.GetSaleProductOrderData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【企微快速下单-成品销售单】
// @Summary 获取成品销售单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetSaleProductOrderDataV2{}
// @Router /hcscm/mp/v1/sale/saleProductOrder/getSaleProductOrder [get]
func MPGetSaleProductOrder(c *gin.Context) {
	var (
		q    = &structure.GetSaleProductOrderQuery{}
		data = structure.GetSaleProductOrderDataV2{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.MPGet(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 获取成品销售单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     voucher_number     query     string  false  "凭证单号"
// @Param     start_order_time     query     string  false  "订单日期"
// @Param     end_order_time     query     string  false  "订单日期"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     sale_user_id     query     int  false  "销售员id"
// @Param     process_factory_id     query     int  false  "加工厂id"
// @Param     order_no     query     string  false  "成品销售单号"
// @Param     audit_status     query     int  false  "订单状态 1待审核 2已审核 3已驳回 4已作废"
// @Param     auditor_id     query     int  false  "审核人ID"
// @Success 200 {object}  structure.GetSaleProductOrderDataList{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/getSaleProductOrderList [get]
func GetSaleProductOrderList(c *gin.Context) {
	var (
		q     = &structure.GetSaleProductOrderListQuery{}
		list  = make(structure.GetSaleProductOrderDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		// system.BuildListResponse(c, err, list, total)
		system.BuildListResponseV2(c, q, "成品销售单", err, list, nil, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	list, total, err = saleProductOrderSvc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【企微快速下单-成品销售单】
// @Summary 获取成品销售单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     voucher_number     query     string  false  "凭证单号"
// @Param     start_order_time     query     string  false  "订单日期"
// @Param     end_order_time     query     string  false  "订单日期"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     sale_user_id     query     int  false  "销售员id"
// @Param     process_factory_id     query     int  false  "加工厂id"
// @Param     order_no     query     string  false  "成品销售单号"
// @Param     audit_status     query     int  false  "订单状态 1待审核 2已审核 3已驳回 4已作废"
// @Param     auditor_id     query     int  false  "审核人ID"
// @Success 200 {object}  structure.GetSaleProductOrderDataListV2{}
// @Router /hcscm/mp/v1/sale/saleProductOrder/getSaleProductOrderList [get]
func MPGetSaleProductOrderList(c *gin.Context) {
	var (
		q     = &structure.GetSaleProductOrderListQuery{}
		list  = make(structure.GetSaleProductOrderDataListV2, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		// system.BuildListResponse(c, err, list, total)
		system.BuildListResponseV2(c, q, "成品销售单", err, list, nil, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	list, total, err = saleProductOrderSvc.MPGetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 获取成品销售单下拉列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     voucher_number     query     string  false  "凭证单号"
// @Param     start_order_time     query     string  false  "订单日期"
// @Param     end_order_time     query     string  false  "订单日期"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     sale_user_id     query     int  false  "销售员id"
// @Param     process_factory_id     query     int  false  "加工厂id"
// @Param     order_no     query     string  false  "成品销售单号"
// @Param     audit_status     query     int  false  "订单状态 1待审核 2已审核 3已驳回 4已作废"
// @Param     auditor_id     query     int  false  "审核人ID"
// @Success 200 {object}  structure.GetSaleProductOrderDataList{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/getSaleProductOrderDropdownList [get]
// @Tags 【企微快速下单-成品销售单】
// @Summary 获取成品销售单下拉列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     voucher_number     query     string  false  "凭证单号"
// @Param     start_order_time     query     string  false  "订单日期"
// @Param     end_order_time     query     string  false  "订单日期"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     sale_user_id     query     int  false  "销售员id"
// @Param     process_factory_id     query     int  false  "加工厂id"
// @Param     order_no     query     string  false  "成品销售单号"
// @Param     audit_status     query     int  false  "订单状态 1待审核 2已审核 3已驳回 4已作废"
// @Param     auditor_id     query     int  false  "审核人ID"
// @Success 200 {object}  structure.GetSaleProductOrderDataList{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/getSaleProductOrderDropdownList [get]
func GetSaleProductOrderDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetSaleProductOrderListQuery{}
		list  = make(structure.GetSaleProductOrderDropdownDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	list, total, err = saleProductOrderSvc.GetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

/*
// @Tags 【成品销售单】
// @Summary 根据id获取信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id     query     int  false  "成品销售单ID"
// @Success 200 {object}  structure.Get成品销售单ItemDataList{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/get成品销售单ItemList [get]
func Get成品销售单ItemList(c *gin.Context) {
	var (
		q     = &structure.Get成品销售单ItemQuery{}
		list  = make(structure.Get成品销售单ItemDataList, 0)
		total int
		err   error
		svc   = svc.New成品销售单Service()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetItemList(ctx, q)
	if err != nil {
		return
	}

	return
}
*/

// @Tags 【成品销售单】
// @Summary 获取上次销售价格
// @Description 根据客户ID和产品颜色ID获取最近一次销售价格
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     Platform         header   int     true  "终端ID"
// @Param     Authorization    header   string  true  "token"
// @Param     product_color_id query    int     true  "颜色id"
// @Param     customer_id      query    int     true  "所属客户id"
// @Success 200 {object} structure.GetLastSalePriceDataList{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/getLastSalePrice [get]
// @Tags 【企微快速下单-成品销售单】
// @Summary 获取上次销售价格
// @Description 根据客户ID和产品颜色ID获取最近一次销售价格
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     Platform         header   int     true  "终端ID"
// @Param     Authorization    header   string  true  "token"
// @Param     product_color_id query    int     true  "颜色id"
// @Param     customer_id      query    int     true  "所属客户id"
// @Success 200 {object} structure.GetLastSalePriceDataList{}
// @Router /hcscm/admin/v1/sale/saleProductOrder/getLastSalePrice [get]
func GetLastSalePrice(c *gin.Context) {
	var (
		q   = &structure.GetLastSalePriceQuery{}
		res = structure.GetLastSalePriceDataList{}
		err error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, res)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 获取上次价时不允许所属客户id为0，为0时返回空
	if q.CustomerId == 0 {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, true)
	res, err = saleProductOrderSvc.GetLastSalePrice(ctx, q)
	if err != nil {
		return
	}
	return
}

// @Tags 【成品销售单】
// @Summary 获取历史销售订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     order_date_begin    query     string  false  "订单开始日期"
// @Param     order_date_end      query     string  false  "订单结束日期"
// @Param     sale_system_id      query     int     false  "营销体系ID"
// @Param     product_id        query     int  false  "成品id"
// @Param     product_color_id  query     int  false  "颜色id"
// @Param     page               query     int     false  "页码"
// @Param     size               query     int     false  "每页数量"
// @Success 200 {object} structure.GetHistorySaleOrderDataList
// @Router /hcscm/admin/v1/sale/saleProductOrder/getHistorySaleOrderList [get]
// @Tags 【企微快速下单-成品销售单】
// @Summary 获取历史销售订单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     order_date_begin    query     string  false  "订单开始日期"
// @Param     order_date_end      query     string  false  "订单结束日期"
// @Param     sale_system_id      query     int     false  "营销体系ID"
// @Param     product_id        query     int  false  "成品id"
// @Param     product_color_id  query     int  false  "颜色id"
// @Param     page               query     int     false  "页码"
// @Param     size               query     int     false  "每页数量"
// @Success 200 {object} structure.GetHistorySaleOrderDataList
// @Router /hcscm/admin/v1/sale/saleProductOrder/getHistorySaleOrderList [get]
func GetHistorySaleOrderList(c *gin.Context) {
	var (
		q     = &structure.GetHistorySaleOrderListQuery{}
		list  = make(structure.GetHistorySaleOrderDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 获取上次价时不允许所属客户id为0，为0时返回为空
	if q.CustomerId == 0 {
		return
	}

	svc := svc.NewSaleProductOrderService(ctx, true)
	list, total, err = svc.GetHistorySaleOrderList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【企微快速下单-成品销售单】
// @Summary 自动识别
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     Platform         header   int     true  "终端ID"
// @Param     Authorization    header   string  true  "token"
// @Param     query               query     string     ture  "自动识别的内容"
// @Param     sale_system_id               query     int     ture  "营销体系id"
// @Param     customer_id               query     int     ture  "客户id"
// @Success 200 {object} productStructure.GetStockProductDropdownDataList
// @Router /hcscm/admin/v1/sale/saleProductOrder/autoOcr [get]
// @Tags 【企微快速下单-成品销售单】
// @Summary 自动识别
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     Platform         header   int     true  "终端ID"
// @Param     Authorization    header   string  true  "token"
// @Param     query               query     string     ture  "自动识别的内容"
// @Param     sale_system_id               query     int     ture  "营销体系id"
// @Param     customer_id               query     int     ture  "客户id"
// @Success 200 {object} productStructure.GetStockProductDropdownDataList
// @Router /hcscm/h5/v1/sale/saleProductOrder/autoOcr [get]
func AutoOcr(c *gin.Context) {
	var (
		q    = &structure.GetProductByAutoOrcParam{}
		data = make(productStructure.GetStockProductDropdownDataList, 0)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	svc := svc.NewSaleProductOrderService(ctx, false)
	data, err = svc.AutoOcr(ctx, q)
	if err != nil {
		return
	}

	return
}
func GetSaleOrderStatusNum(c *gin.Context) {
	var (
		q    = &structure.GetSaleProductOrderListQuery{}
		data = structure.GetSaleOrderStatusNumData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	svc := svc.NewSaleProductOrderService(ctx, false)
	data, err = svc.GetSaleOrderStatusNum(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品销售单】
// @Summary 获取客户上次销售信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     customer_id      query    int     true  "客户ID"
// @Success 200 {object} structure.GetCustomerLastMoneyInfoRes
// @Router /hcscm/admin/v1/sale/saleProductOrder/getCustomerLastMoneyInfo [get]
func GetCustomerLastMoneyInfo(c *gin.Context) {
	var (
		q    = &structure.GetCustomerLastMoneyInfoReq{}
		data = structure.GetCustomerLastMoneyInfoRes{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	saleProductOrderSvc := svc.NewSaleProductOrderService(ctx, false)
	data, err = saleProductOrderSvc.GetCustomerLastMoneyInfo(ctx, q)
	if err != nil {
		return
	}

	return
}
