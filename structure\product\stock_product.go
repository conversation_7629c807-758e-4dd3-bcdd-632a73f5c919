package product

import (
	common "hcscm/common/product"
	common_sale "hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type StockProduct interface {
	GetKey() string
}

// 注意：需要与msg/msg_publish/product/ModifyProductStock结构体保持同步
type ModifyProductStock struct {
	Id                 uint64 `json:"id"`                    // 汇总库存id
	WarehouseId        uint64 `json:"warehouse_id"`          // 仓库id
	CustomerId         uint64 `json:"customer_id"`           // 所属客户id
	ProductColorId     uint64 `json:"product_color_id"`      // 颜色id
	ProductLevelId     uint64 `json:"product_level_id"`      // 成品等级id
	Remark             string `json:"remark"`                // 库存备注
	Roll               int    `json:"roll"`                  // 修改匹数(增加为+减少为-)
	Length             int    `json:"length"`                // 修改长度(增加为+减少为-)
	Weight             int    `json:"weight"`                // 修改数量(增加为+减少为-)
	BookRoll           int    `json:"book_roll"`             // 预约匹数(占用为+释放为-)
	BookWeight         int    `json:"book_weight"`           // 预约数量(占用为+释放为-)
	StockProductKey    string `json:"stock_product_key"`     // 汇总库存key值(用于创建多条库存细码的时候)
	ProductId          uint64 `json:"product_id"`            // 成品id
	ProductColorKindId uint64 `json:"product_color_kind_id"` // 颜色类别id type_finished_product_kind_id
	ProductKindId      uint64 `json:"product_kind_id"`       // 成品种类id
}

type ModifyProductStockList []*ModifyProductStock

func (r ModifyProductStockList) GetKey() string {
	return ""
}

type AddStockProductParam struct {
	structure_base.Param
	Id                 uint64                      `json:"id"`                    // 汇总库存id
	StockProductId     uint64                      `json:"stock_product_id"`      // 库存id
	ProductColorId     uint64                      `json:"product_color_id"`      // 颜色id
	ProductColorCode   string                      `json:"product_color_code"`    // 颜色编号
	ProductColorName   string                      `json:"product_color_name"`    // 颜色名称
	ProductColorKindId uint64                      `json:"product_color_kind_id"` // 颜色类别id type_finished_product_kind_id
	WarehouseId        uint64                      `json:"warehouse_id"`          // 仓库id
	WarehouseName      string                      `json:"warehouse_name"`        // 仓库名称
	CustomerId         uint64                      `json:"customer_id"`           // 所属客户id
	ProductId          uint64                      `json:"product_id"`            // 成品id
	ProductCode        string                      `json:"product_code"`          // 成品编号
	ProductName        string                      `json:"product_name"`          // 成品名称
	ProductKindId      uint64                      `json:"product_kind_id"`       // 成品种类id
	ProductLevelId     uint64                      `json:"product_level_id"`      // 成品等级id
	ProductRemark      string                      `json:"product_remark"`        // 成品备注
	Weight             int                         `json:"weight"`                // 数量
	Length             int                         `json:"length"`                // 长度
	BookRoll           int                         `json:"book_roll"`             // 预约匹数
	BookWeight         int                         `json:"book_weight"`           // 预约数量
	StockRoll          int                         `json:"stock_roll"`            // 库存匹数
	MeasurementUnitId  uint64                      `json:"measurement_unit_id"`   // 计量单位id
	Remark             string                      `json:"remark"`                // 库存备注
	StockProductKey    string                      `json:"stock_product_key"`     // 汇总库存key值(用于创建多条库存细码的时候)
	IsNoNeedCheckStock bool                        `json:"-"`                     // 是否不需要校验库存是否足够（出仓消审或者进仓审核用）
	OrderType          common_system.BookOrderType `json:"order_type"`            // 预约单类型
	BookLength         int                         `json:"book_length"`           // 预约长度
	OrderId            uint64                      `json:"order_id"`              // 单id
	OrderNo            string                      `json:"order_no"`              // 单号
	BookOrderId        uint64                      `json:"book_order_id"`         // 占用单id
}

type AddStockProductParamList []AddStockProductParam

func (r AddStockProductParamList) Adjust() {

}

func (r *AddStockProductParam) ToMergeSameItem(p *AddStockProductParam) {
	r.Weight += p.Weight
	r.Length += p.Length
	r.BookRoll += p.BookRoll
	r.BookWeight += p.BookWeight
	r.StockRoll += p.StockRoll
}

type AddStockProductData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateStockProductParam struct {
	structure_base.Param
	Items AddStockProductDetailParamList `json:"items"` // 细码
}

type UpdateStockProductData struct {
	structure_base.ResponseData
}

type DeleteStockProductParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteStockProductData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type GetStockProductQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetStockProductListQuery struct {
	structure_base.ListQuery
	IsDropList         int                       `form:"is_drop_list"`                           // 回收站
	ProductColorId     uint64                    `form:"product_color_id"`                       // 颜色id
	ProductColorCode   string                    `form:"product_color_code"`                     // 颜色编号
	ProductColorName   string                    `form:"product_color_name"`                     // 颜色名称
	ProductColorKindId uint64                    `form:"product_color_kind_id"`                  // 颜色类别id type_finished_product_kind_id
	WarehouseId        uint64                    `form:"warehouse_id"`                           // 仓库id
	WarehouseName      string                    `form:"warehouse_name"`                         // 仓库名称
	CustomerId         uint64                    `form:"customer_id"`                            // 所属客户id
	SaleCustomerId     uint64                    `form:"sale_customer_id" relate:"customer_id"`  // 购买成品客户id
	ProductId          uint64                    `form:"product_id"`                             // 成品id
	ProductCode        string                    `form:"product_code"`                           // 成品编号
	ProductName        string                    `form:"product_name"`                           // 成品名称
	ProductLevelId     uint64                    `form:"product_level_id"`                       // 成品等级id
	ProductRemark      string                    `form:"product_remark"`                         // 成品备注
	Weight             int                       `form:"weight"`                                 // 数量
	Length             int                       `form:"length"`                                 // 长度
	BookRoll           int                       `form:"book_roll"`                              // 预约匹数
	StockRoll          int                       `form:"stock_roll"`                             // 库存匹数
	MeasurementUnitId  uint64                    `form:"measurement_unit_id"`                    // 计量单位id
	Remark             string                    `form:"remark"`                                 // 库存备注
	StockShowType      common.StockShowType      `form:"stock_show_type"`                        // 显示方式
	AvailableOnly      bool                      `form:"available_only"`                         // 仅显示有可用库存
	WithPrice          bool                      `form:"with_price"`                             // 是否带上价格
	ProductIds         []uint64                  `form:"-"`                                      // 成品ids
	ProductColorIds    []uint64                  `form:"-"`                                      // 成品颜色ids
	OrderTypeCustomer  bool                      `form:"order_type_customer"`                    // 客订
	OrderTypeStock     bool                      `form:"order_type_stock"`                       // 现货
	ColorReservation   int                       `form:"color_reservation"`                      // 齐色预约
	SaleSystemId       uint64                    `form:"sale_system_id" relate:"sale_system_id"` // 营销体系id
	ColorQuery         string                    `form:"color_query"`                            // 色号颜色查询
	OrderType          common_sale.SaleOrderType `form:"order_type"`                             // 订单类型
	Ids                []uint64                  `form:"-"`                                      // 库存id
}

func (r GetStockProductListQuery) Adjust() {

}

type GetStockProductData struct {
	structure_base.RecordData
	ProductColorId          uint64 `json:"product_color_id"`                       // 颜色id
	ProductColorCode        string `json:"product_color_code"`                     // 颜色编号
	ProductColorName        string `json:"product_color_name"`                     // 颜色名称
	ProductColorKindId      uint64 `json:"product_color_kind_id"`                  // 颜色类别id type_finished_product_kind_id
	ProductColorKindName    string `json:"product_color_kind_name"`                // 颜色类别名称
	WarehouseId             uint64 `json:"warehouse_id"`                           // 仓库id
	WarehouseName           string `json:"warehouse_name"`                         // 仓库名称
	CustomerId              uint64 `json:"customer_id"`                            // 所属客户id
	CustomerName            string `json:"customer_name"`                          // 所属客户名称
	ProductId               uint64 `json:"product_id"`                             // 成品id
	ProductCode             string `json:"product_code"`                           // 成品编号
	ProductName             string `json:"product_name"`                           // 成品名称
	ProductLevelId          uint64 `json:"product_level_id"`                       // 成品等级id
	ProductLevelName        string `json:"product_level_name"`                     // 成品等级名称
	ProductRemark           string `json:"product_remark"`                         // 成品备注
	Weight                  int    `json:"weight"`                                 // 数量
	Length                  int    `json:"length"`                                 // 长度
	BookRoll                int    `json:"book_roll"`                              // 预约匹数
	BookWeight              int    `json:"book_weight"`                            // 预约数量
	StockRoll               int    `json:"stock_roll"`                             // 库存匹数
	AvailableRoll           int    `json:"available_roll"`                         // 可用匹数
	AvailableWeight         int    `json:"available_weight"`                       // 可用数量
	MeasurementUnitId       uint64 `json:"measurement_unit_id"`                    // 计量单位id
	MeasurementUnitName     string `json:"measurement_unit_name"`                  // 计量单位名称
	Remark                  string `json:"remark"`                                 // 库存备注
	WeavingOrganizationId   uint64 `json:"weaving_organization_id"`                // 织造组织id
	WeavingOrganizationCode string `json:"weaving_organization_code"`              // 织造组织编号
	WeavingOrganizationName string `json:"weaving_organization_name" excel:"组织"`   // 织造组织名称
	YarnCount               string `json:"yarn_count" excel:"纱支"`                  // 纱支
	Density                 string `json:"density" excel:"密度"`                     // 密度
	FinishProductIngredient string `json:"finish_product_ingredient" excel:"成品成分"` // 成品成分
	// OrderTypeCustomer       bool   `form:"order_type_customer"`                    // 客订
	// OrderTypeStock          bool   `form:"order_type_stock"`                       // 现货
	ColorReservation int `json:"color_reservation"` // 齐色预约
}

type GetStockProductDataList []GetStockProductData

func (g GetStockProductDataList) PickById(id uint64) (data GetStockProductData) {
	for _, i := range g {
		if i.Id == id {
			data = i
			return
		}
	}
	return
}

func (g GetStockProductDataList) Adjust() {

}

type GetStockProductDropdownData struct {
	structure_base.RecordData
	StockProductId       uint64 `json:"stock_product_id"`        // 库存汇总id
	ProductColorId       uint64 `json:"product_color_id"`        // 颜色id
	ProductColorCode     string `json:"product_color_code"`      // 颜色编号
	ProductColorName     string `json:"product_color_name"`      // 颜色名称
	ProductColorKindId   uint64 `json:"product_color_kind_id"`   // 颜色类别id type_finished_product_kind_id
	ProductColorKindName string `json:"product_color_kind_name"` // 颜色类别名称
	WarehouseId          uint64 `json:"warehouse_id"`            // 仓库id
	WarehouseName        string `json:"warehouse_name"`          // 仓库名称
	CustomerId           uint64 `json:"customer_id"`             // 所属客户id
	CustomerName         string `json:"customer_name"`           // 所属客户名称
	ProductId            uint64 `json:"product_id"`              // 成品id
	ProductCode          string `json:"product_code"`            // 成品编号
	ProductName          string `json:"product_name"`            // 成品名称
	ProductLevelId       uint64 `json:"product_level_id"`        // 成品等级id
	ProductLevelName     string `json:"product_level_name"`      // 成品等级名称
	ProductRemark        string `json:"product_remark"`          // 成品备注
	Weight               int    `json:"weight"`                  // 数量
	Length               int    `json:"length"`                  // 长度
	BookRoll             int    `json:"book_roll"`               // 预约匹数
	BookWeight           int    `json:"book_weight"`             // 预约数量
	StockRoll            int    `json:"stock_roll"`              // 库存匹数
	AvailableRoll        int    `json:"available_roll"`          // 可用匹数
	AvailableWeight      int    `json:"available_weight"`        // 可用数量
	MeasurementUnitId    uint64 `json:"measurement_unit_id"`     // 计量单位id
	MeasurementUnitName  string `json:"measurement_unit_name"`   // 计量单位名称
	Remark               string `json:"remark"`                  // 库存备注
	ProductCraft         string `json:"product_craft"`           // 成品工艺
	ProductIngredient    string `json:"product_ingredient"`      // 成品成分
	StandardWeight       int    `json:"standard_weight"`         // 标准数量
	SalePrice
	AutoOcrWeight     int    `json:"auto_ocr_weight"`     // 自动识别数量
	AuxiliaryUnitId   uint64 `json:"auxiliary_unit_id"`   // 辅助单位id
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 辅助单位name
	TextureUrl        string `json:"texture_url"`         // 纹理图片
	// Roll int `json:"roll"`
	// 上次价
	// LatestSalePrice          int `json:"latest_sale_price"`
	// LatestLengthCutSalePrice int `json:"latest_length_cut_sale_price"`
}
type GetStockProductDropdownDataV2 struct {
	structure_base.RecordData
	StockProductId   uint64                              `json:"stock_product_id"`   // 库存汇总id
	WarehouseId      uint64                              `json:"warehouse_id"`       // 仓库id
	WarehouseName    string                              `json:"warehouse_name"`     // 仓库名称
	ProductId        uint64                              `json:"product_id"`         // 成品id
	ProductCode      string                              `json:"product_code"`       // 成品编号
	ProductName      string                              `json:"product_name"`       // 成品名称
	CustomerId       uint64                              `json:"customer_id"`        // 所属客户id
	CustomerName     string                              `json:"customer_name"`      // 所属客户名称
	ProductLevelId   uint64                              `json:"product_level_id"`   // 成品等级id
	ProductLevelName string                              `json:"product_level_name"` // 成品等级名称
	ProductRemark    string                              `json:"product_remark"`     // 成品备注
	ItemData         []GetStockProductDropdownDataDetail `json:"item_data"`          // 色号详情
}
type GetStockProductDropdownDataDetail struct {
	ProductColorId       uint64 `json:"product_color_id"`        // 颜色id
	ProductColorCode     string `json:"product_color_code"`      // 颜色编号
	ProductColorName     string `json:"product_color_name"`      // 颜色名称
	ProductColorKindId   uint64 `json:"product_color_kind_id"`   // 颜色类别id type_finished_product_kind_id
	ProductColorKindName string `json:"product_color_kind_name"` // 颜色类别名称
	Weight               int    `json:"weight"`                  // 数量
	Length               int    `json:"length"`                  // 长度
	BookRoll             int    `json:"book_roll"`               // 预约匹数
	BookWeight           int    `json:"book_weight"`             // 预约数量
	StockRoll            int    `json:"stock_roll"`              // 库存匹数
	AvailableRoll        int    `json:"available_roll"`          // 可用匹数
	AvailableWeight      int    `json:"available_weight"`        // 可用数量
	MeasurementUnitId    uint64 `json:"measurement_unit_id"`     // 计量单位id
	MeasurementUnitName  string `json:"measurement_unit_name"`   // 计量单位名称
	Remark               string `json:"remark"`                  // 库存备注
	ProductCraft         string `json:"product_craft"`           // 成品工艺
	ProductIngredient    string `json:"product_ingredient"`      // 成品成分
	SalePrice
}

type GetStockProductDropdownDataList []GetStockProductDropdownData
type GetStockProductDropdownDataListV2 []GetStockProductDropdownDataV2

func (g GetStockProductDropdownDataListV2) Adjust() {

}

func (g GetStockProductDropdownDataList) Adjust() {

}

type GetStockProductOutAndInDetailData struct {
	structure_base.RecordData
	Id            uint64                   `json:"id"`
	OrderTime     tools.MyTime             `json:"order_time"`      // 单据时间
	OrderNo       string                   `json:"order_no"`        // 单据编号
	OrderType     int                      `json:"order_type"`      // 单据类型
	OutRoll       int                      `json:"out_roll"`        // 匹数（出仓）
	OutWeight     int                      `json:"out_weight"`      // 数量（出仓）
	OutLength     int                      `json:"out_length"`      // 长度（出仓）
	InRoll        int                      `json:"in_roll"`         // 匹数（进仓）
	InWeight      int                      `json:"in_weight"`       // 数量（进仓）
	InLength      int                      `json:"in_length"`       // 长度（进仓）
	ResultRoll    int                      `json:"result_roll"`     // 匹数（结余）
	ResultWeight  int                      `json:"result_weight"`   // 数量（结余）
	ResultLength  int                      `json:"result_length"`   // 长度（结余）
	OrderTypeName string                   `json:"order_type_name"` // 单据名称
	DataType      common.WarehouseGoodType `json:"data_type"`       // 1进仓，2出仓
}

type GetStockProductOutAndInDetailDataList []GetStockProductOutAndInDetailData

func (g GetStockProductOutAndInDetailDataList) Adjust() {

}

type GetStockProductOutAndInDetailDataListResp struct {
	structure_base.RecordData
	List        GetStockProductOutAndInDetailDataList `json:"list"`         // 进出仓情况列表
	TotalRoll   int                                   `json:"total_roll"`   // 匹数（上期结余）
	TotalWeight int                                   `json:"total_weight"` // 数量（上期结余）
	TotalLength int                                   `json:"total_length"` // 长度（上期结余）
	Total       int                                   `json:"total"`        // 总数量
}

func (g GetStockProductOutAndInDetailDataListResp) Adjust() {

}

type GetYBStockProductListQuery struct {
	structure_base.ListQuery
	ProductId          uint64   `form:"product_id"`            // 成品id
	ProductCode        string   `form:"product_code"`          // 成品编号
	ProductName        string   `form:"product_name"`          // 成品名称
	ProductColorId     uint64   `form:"product_color_id"`      // 颜色id
	ProductColorCode   string   `form:"product_color_code"`    // 颜色编号
	ProductColorName   string   `form:"product_color_name"`    // 颜色名称
	ProductColorKindId uint64   `form:"product_color_kind_id"` // 颜色类别id type_finished_product_kind_id
	WarehouseId        uint64   `form:"warehouse_id"`          // 仓库id
	WarehouseName      string   `form:"warehouse_name"`        // 仓库名称
	ProductIds         []uint64 `form:"-"`                     // 成品ids
	ProductColorIds    []uint64 `form:"-"`                     // 成品颜色ids

	ProductCodeOrName      string `form:"product_code_or_name"`       // 成品编号或名称
	ProductColorCodeOrName string `form:"product_color_code_or_name"` // 颜色编号或名称
}

func (r GetYBStockProductListQuery) Adjust() {
}

type GetYBListStockProductData struct {
	structure_base.RecordData
	ProductColorId       uint64 `json:"product_color_id"`        // 颜色id
	ProductColorCode     string `json:"product_color_code"`      // 颜色编号
	ProductColorName     string `json:"product_color_name"`      // 颜色名称
	ProductColorKindId   uint64 `json:"product_color_kind_id"`   // 颜色类别id type_finished_product_kind_id
	ProductColorKindName string `json:"product_color_kind_name"` // 颜色类别名称
	WarehouseId          uint64 `json:"warehouse_id"`            // 仓库id
	WarehouseName        string `json:"warehouse_name"`          // 仓库名称
	CustomerId           uint64 `json:"customer_id"`             // 所属客户id
	CustomerName         string `json:"customer_name"`           // 所属客户名称
	ProductId            uint64 `json:"product_id"`              // 成品id
	ProductCode          string `json:"product_code"`            // 成品编号
	ProductName          string `json:"product_name"`            // 成品名称
	ProductLevelId       uint64 `json:"product_level_id"`        // 成品等级id
	ProductLevelName     string `json:"product_level_name"`      // 成品等级名称
	ProductRemark        string `json:"product_remark"`          // 成品备注
	Weight               int    `json:"weight"`                  // 数量
	Length               int    `json:"length"`                  // 长度
	BookRoll             int    `json:"book_roll"`               // 预约匹数
	BookWeight           int    `json:"book_weight"`             // 预约数量
	StockRoll            int    `json:"stock_roll"`              // 库存匹数
	AvailableRoll        int    `json:"available_roll"`          // 可用匹数
	AvailableWeight      int    `json:"available_weight"`        // 可用数量
	MeasurementUnitId    uint64 `json:"measurement_unit_id"`     // 计量单位id
	MeasurementUnitName  string `json:"measurement_unit_name"`   // 计量单位名称
	// Remark               string `json:"remark"`                  // 库存备注

	DyelotNumber string `json:"dyelot_number" excel:"缸号"` // 缸号
}

type GetYBStockProductDataList []GetYBListStockProductData

func (g GetYBStockProductDataList) Adjust() {
}

// 缸号流水明细
type GetDyelotDetailQuery struct {
	structure_base.ListQuery
	DyelotNumber string `form:"dyelot_number" binding:"required"` // 缸号

	ProductColorId uint64          `form:"product_color_id"` // 颜色id
	StockDetailID  uint64          `form:"stock_detail_id"`  // 详细库存id
	StockProductId uint64          `form:"stock_product_id"` // 库存id
	BeginTime      tools.QueryTime `form:"begin_time"`       // 查询开始时间
	EndTime        tools.QueryTime `form:"end_time"`         // 查询结束时间
	Ids            []uint64        // 存储ids用
	IsNoSkipEmpty  bool
}

type GetDyelotDetailData struct {
	structure_base.RecordData
	// todo 补充
	WarehouseInOrderNo  string `json:"warehouse_in_order_no"`  // 进仓单号
	WarehouseOutOrderNo string `json:"warehouse_out_order_no"` // 出仓单号
	OrderType           string `json:"order_type"`             // 单据类型（采购进仓单/销售出仓单）
	CustomerName        string `json:"customer_name"`          // 客户名称
	RollCount           int    `json:"roll_count"`             // 变化匹数
	Weight              int    `json:"weight"`                 // 变化重量
	Action              string `json:"action"`                 // （增加/减少）
}

type GetDyelotDetailDataList []GetDyelotDetailData

func (g GetDyelotDetailDataList) Adjust() {
}

// 色号详情
type GetColorDetailQuery struct {
	structure_base.ListQuery
	ProductCode string `form:"product_code"` // 成品编号
	ProductName string `form:"product_name"` // 成品名称

	StockProductId uint64 `form:"stock_product_id"` // 库存id
}

type GetTotalData struct {
	TotalStockRoll       int                   `json:"total_stock_roll"`       // 库存匹数
	TotalBookRoll        int                   `json:"total_book_roll"`        // 总预约匹数
	TotalAvailableRoll   int                   `json:"total_available_roll"`   // 总可用匹数
	TotalAvailableWeight int                   `json:"total_available_weight"` // 总可用数量
	List                 []GetStockProductData // 数据列表
}

func (g GetTotalData) Adjust() {
}

func (g GetTotalDataList) Adjust() {
}

type GetTotalDataList []GetTotalData

// 通过stock_id查询成品详细信息的请求参数
type GetStockProductDetailInfoQuery struct {
	structure_base.Query
	StockId     uint64 `form:"stock_id" binding:"required"` // 库存ID
	WarehouseId uint64 `form:"warehouse_id"`
}

// 通过stock_id查询成品详细信息的响应数据
type GetStockProductDetailInfoData struct {
	structure_base.ResponseData
	ProductId        uint64                    `json:"product_id"`         // 成品id
	ProductCode      string                    `json:"product_code"`       // 成品编号
	ProductName      string                    `json:"product_name"`       // 成品名称
	ProductColorId   uint64                    `json:"product_color_id"`   // 颜色id
	ProductColorCode string                    `json:"product_color_code"` // 颜色编号
	ProductColorName string                    `json:"product_color_name"` // 颜色名称
	CustomerId       uint64                    `json:"customer_id"`        // 客户id
	CustomerName     string                    `json:"customer_name"`      // 客户名称
	WarehouseId      uint64                    `json:"warehouse_id"`       // 仓库id
	WarehouseName    string                    `json:"warehouse_name"`     // 仓库名称
	DyelotNumber     string                    `json:"dyelot_number"`      // 缸号
	ProductLevelId   uint64                    `json:"product_level_id"`   // 成品等级id
	ProductLevelName string                    `json:"product_level_name"` // 成品等级名称
	ProductRemark    string                    `json:"product_remark"`     // 成品备注
	BookDetails      []StockProductBookLogData `json:"book_details"`       // 库存占用详情
}

// 通过stock_id查询成品详细信息的响应包装
type GetStockProductDetailInfoResp struct {
	structure_base.Response
	Data GetStockProductDetailInfoData `json:"data"`
}

// 库存占用详情数据
type StockProductBookLogData struct {
	Id             uint64 `json:"id"`               // ID
	OrderId        uint64 `json:"order_id"`         // 占用单id
	OrderNo        string `json:"order_no"`         // 占用单号
	OrderType      int    `json:"order_type"`       // 占用单类型
	ArrangeOrderId uint64 `json:"arrange_order_id"` // 配布单ID
	ArrangeOrderNo string `json:"arrange_order_no"` // 配布单号
	BookRoll       int    `json:"book_roll"`        // 预约匹数
	BookWeight     int    `json:"book_weight"`      // 预约匹数
	CreateTime     string `json:"create_time"`      // 创建时间
	SaleUserId     uint64 `json:"sale_user_id"`     // 销售员ID
	SaleUserName   string `json:"sale_user_name"`   // 销售员名称
	SaleFollowId   uint64 `json:"sale_follow_id"`   // 销售跟单ID
	SaleFollowName string `json:"sale_follow_name"` // 销售跟单号
}

// 查询单据占用库存的请求参数
type GetOrderBookStockQuery struct {
	structure_base.Query
	OrderId   uint64                      `form:"order_id" binding:"required"`   // 单据ID
	OrderType common_system.BookOrderType `form:"order_type" binding:"required"` // 单据类型
}

// 单据占用库存详情数据
type OrderBookStockData struct {
	Id               uint64 `json:"id"`                 // 占用日志ID
	StockId          uint64 `json:"stock_id"`           // 库存ID
	ProductId        uint64 `json:"product_id"`         // 成品id
	ProductCode      string `json:"product_code"`       // 成品编号
	ProductName      string `json:"product_name"`       // 成品名称
	ColorId          uint64 `json:"color_id"`           // 颜色id
	ColorCode        string `json:"color_code"`         // 颜色编号
	ColorName        string `json:"color_name"`         // 颜色名称
	WarehouseId      uint64 `json:"warehouse_id"`       // 仓库id
	WarehouseName    string `json:"warehouse_name"`     // 仓库名称
	DyelotNumber     string `json:"dyelot_number"`      // 缸号
	CustomerId       uint64 `json:"customer_id"`        // 客户id
	CustomerName     string `json:"customer_name"`      // 客户名称
	BookRoll         int    `json:"book_roll"`          // 预约匹数
	BookWeight       int    `json:"book_weight"`        // 预约重量
	BookLength       int    `json:"book_length"`        // 预约长度
	BookUserId       uint64 `json:"book_user_id"`       // 预约人员ID
	BookUserName     string `json:"book_user_name"`     // 预约人员名称
	BookTime         string `json:"book_time"`          // 预约时间
	ProductLevelId   uint64 `json:"product_level_id"`   // 成品等级id
	ProductLevelName string `json:"product_level_name"` // 成品等级名称
}

// 单据占用库存列表
type OrderBookStockDataList []OrderBookStockData

// 单据占用库存响应数据
type GetOrderBookStockData struct {
	structure_base.ResponseData
	List        OrderBookStockDataList `json:"list"`         // 占用库存列表
	TotalRoll   int                    `json:"total_roll"`   // 总匹数
	TotalWeight int                    `json:"total_weight"` // 总重量
	TotalLength int                    `json:"total_length"` // 总长度
	Total       int                    `json:"total"`        // 总条数
}

// 单据占用库存响应包装
type GetOrderBookStockResp struct {
	structure_base.Response
	Data GetOrderBookStockData `json:"data"`
}

type GetYBStockProductOutAndInDetailDataListResp struct {
	List        GetStockProductOutAndInDetailDataList `json:"list"`         // 进出仓情况列表
	TotalRoll   int                                   `json:"total_roll"`   // 匹数（上期结余）
	TotalWeight int                                   `json:"total_weight"` // 数量（上期结余）
	TotalLength int                                   `json:"total_length"` // 长度（上期结余）
	Total       int                                   `json:"total"`        // 总数量
}
