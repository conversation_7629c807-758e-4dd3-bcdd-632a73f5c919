package biz_unit

import (
	"context"
	"hcscm/common/errors"
	"hcscm/domain/biz_unit/entity"
	"hcscm/middleware"
	mysql "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/biz_unit/dao"
	"hcscm/model/mysql/mysql_base"
	mysql_system "hcscm/model/mysql/system"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
)

type BizUnitRepo struct {
	tx  *mysql_base.Tx
	dao *dao.Dao
}

func NewBizUnitRepo(tx *mysql_base.Tx) *BizUnitRepo {
	return &BizUnitRepo{
		tx:  tx,
		dao: dao.NewDao(tx),
	}
}

func (r *Repo) CheckBizUnitIsExist(ctx context.Context, do *entity.BizUnit) (bool, error) {
	var (
		po  = &mysql.BizUnit{}
		err error
	)
	if do.IsSimpleOp {
		po, err = r.dao.QueryBizUnitByUnqSimple(ctx, do.Id, do.Category, do.Name, do.Phone, do.CustomCode)
		if err != nil {
			if po != nil {
				return true, err
			}
			return false, err
		}
	} else {
		po, err = r.dao.QueryBizUnitByUnq(ctx, do.Id, do.Category, do.Name, do.Phone, do.CustomCode)
		if err != nil {
			if po != nil {
				return true, err
			}
			return false, err
		}
	}

	return po != nil, nil
}

func (r *Repo) AddBizUnit(ctx context.Context, do *entity.BizUnit) error {
	if do.SaleSystemId == 0 {
		do.SaleSystemId = metadata.GetSaleSystemId(ctx)
	}
	if len(do.SaleSystemIds) == 0 {
		do.SaleSystemIds = []uint64{do.SaleSystemId}
	}
	po := mysql.ToBizUnitPO(do)
	if err := r.dao.Create(ctx, po); err != nil {
		return err
	}

	if do.Sale != nil {
		salePO := mysql.ToBizUnitSalePO(do)
		if err := r.dao.Create(ctx, salePO); err != nil {
			return err
		}
	}

	if do.FactoryLogistics != nil {
		facPO := mysql.ToBizUnitFactoryLogisticsPOList(do)
		if err := r.dao.Create(ctx, &facPO); err != nil {
			return err
		}
	}

	if len(do.UnitTypeId) > 0 {
		unitTypePO := mysql.ToBizUnitTypeRelPO(do)
		if err := r.dao.Create(ctx, &unitTypePO); err != nil {
			return err
		}
	}
	if len(do.SaleSystemIds) > 0 {
		saleSystemPO := mysql.ToBizUnitSaleSystemRelPOList(do)
		if err := r.dao.Create(ctx, &saleSystemPO); err != nil {
			return err
		}
	}

	return nil
}

func (r *Repo) GetBizUnit(ctx context.Context, id uint64) (*entity.BizUnit, error) {
	po, err := r.dao.QueryBizUnit(ctx, id)
	if err != nil {
		return nil, err
	}

	typeRel, err := r.dao.QueryBizUnitTypeRelByUnitId(ctx, id)
	if err != nil {
		return nil, err
	}
	saleSystemRel, err := r.dao.FindBizUnitSaleSystemRelByUnitId(ctx, id)

	return mysql.ToBizUnitDO(po, typeRel, saleSystemRel)
}

func (r *Repo) GetMultiBizUnit(ctx context.Context, ids []uint64) ([]*entity.BizUnit, error) {
	po, err := r.dao.QueryMultiBizUnit(ctx, ids)
	if err != nil {
		return nil, err
	}

	typeRel, err := r.dao.QueryBizUnitTypeRelByUnitIds(ctx, ids)
	if err != nil {
		return nil, err
	}

	saleSystemRel, err := r.dao.FindBizUnitSaleSystemRelByUnitIds(ctx, ids)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64][]*mysql.BizUnitTypeRel)
	saleSystemMap := make(map[uint64]mysql.BizUnitSaleSystemRelList)
	for _, item := range typeRel {
		m[item.UnitId] = append(m[item.UnitId], item)
	}

	for _, rel := range saleSystemRel {
		saleSystemMap[rel.UnitId] = append(saleSystemMap[rel.UnitId], rel)
	}

	dos := make([]*entity.BizUnit, 0, len(po))
	for _, item := range po {
		do, err := mysql.ToBizUnitDO(item, m[item.Id], saleSystemMap[item.Id])
		if err != nil {
			return nil, err
		}
		dos = append(dos, do)
	}
	return dos, nil
}

func (r *Repo) GetMultiBizUnits(ctx context.Context) ([]*entity.BizUnit, error) {
	po, err := r.dao.QueryMultiBizUnitList(ctx)
	if err != nil {
		return nil, err
	}

	typeRel, err := r.dao.QueryBizUnitTypeRel(ctx)
	if err != nil {
		return nil, err
	}
	saleSystemRel, err := r.dao.FindBizUnitSaleSystemRel(ctx)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64][]*mysql.BizUnitTypeRel)
	saleSystemMap := make(map[uint64]mysql.BizUnitSaleSystemRelList)
	for _, item := range typeRel {
		m[item.UnitId] = append(m[item.UnitId], item)
	}

	for _, rel := range saleSystemRel {
		saleSystemMap[rel.UnitId] = append(saleSystemMap[rel.UnitId], rel)
	}

	dos := make([]*entity.BizUnit, 0, len(po))
	for _, item := range po {
		do, err := mysql.ToBizUnitDO(item, m[item.Id], saleSystemMap[item.Id])
		if err != nil {
			return nil, err
		}
		dos = append(dos, do)
	}
	return dos, nil
}

func (r *Repo) UpdateBizUnit(ctx context.Context, do *entity.BizUnit) (err error) {
	if do.SaleSystemId == 0 {
		do.SaleSystemId = metadata.GetSaleSystemId(ctx)
	}
	if len(do.SaleSystemIds) == 0 {
		do.SaleSystemIds = []uint64{do.SaleSystemId}
	}
	po := mysql.ToBizUnitPO(do)
	if err = r.dao.UpdateBizUnit(ctx, po); err != nil {
		return err
	}

	if err = r.dao.HardDeleteBizUnitSale(ctx, do.Id); err != nil {
		return err
	}
	if do.Sale != nil {
		salePO := mysql.ToBizUnitSalePO(do)
		if err = r.dao.Create(ctx, salePO); err != nil {
			return err
		}
	}

	if do.Category == entity.CategoryCustomer {
		if err = r.dao.HardDeleteBizUnitFactoryLogistics(ctx, do.Id); err != nil {
			return err
		}
		if do.FactoryLogistics != nil {
			facPO := mysql.ToBizUnitFactoryLogisticsPOList(do)
			if err = r.dao.Create(ctx, facPO); err != nil {
				return err
			}
		}
	}

	if err = r.dao.HardDeleteBizUnitTypeRel(ctx, do.Id); err != nil {
		return err
	}
	if len(do.UnitTypeId) > 0 {
		unitTypePO := mysql.ToBizUnitTypeRelPO(do)
		if err = r.dao.Create(ctx, &unitTypePO); err != nil {
			return err
		}
	}

	// 删除旧的营销体系关联
	err = r.dao.MustDeleteBizUnitSaleSystemRelByUnitId(ctx, do.Id)
	if err != nil {
		return
	}

	if len(do.SaleSystemIds) > 0 {
		saleSystemPO := mysql.ToBizUnitSaleSystemRelPOList(do)
		if err = r.dao.Create(ctx, &saleSystemPO); err != nil {
			return err
		}
	}

	return nil
}

func (r *Repo) UpdateBizUnitStatus(ctx context.Context, do []*entity.BizUnit) error {
	m := map[int][]uint64{}
	for _, item := range do {
		_, ok := m[item.Status]
		if !ok {
			m[item.Status] = []uint64{}
		}
		m[item.Status] = append(m[item.Status], item.Id)
	}
	for status, ids := range m {
		if err := r.dao.UpdateMultiBizUnitStatus(ctx, ids, status); err != nil {
			return err
		}
	}
	return nil
}

func (r *Repo) UpdateBizUnitCustomCode(ctx context.Context, do *entity.BizUnit) error {
	po := &mysql.BizUnit{
		Id:         do.Id,
		CustomCode: do.CustomCode,
	}
	return r.dao.UpdateBizUnit(ctx, po)
}

func (r *Repo) UpdateBizUnitPinYin(ctx context.Context, id uint64, py string) error {
	po := &mysql.BizUnit{
		Id:     id,
		PinYin: py,
	}
	return r.dao.UpdateBizUnit(ctx, po)
}

func (r *Repo) DeleteBizUnit(ctx context.Context, do []*entity.BizUnit) error {
	ids := make([]uint64, 0, len(do))
	for _, item := range do {
		ids = append(ids, item.Id)
	}
	return r.dao.DeleteBizUnit(ctx, ids)
}

func (r *Repo) QueryBizUnit(ctx context.Context, id uint64) (*mysql.BizUnit, error) {
	return r.dao.QueryBizUnit(ctx, id)
}

func (r *Repo) QueryBizUnitByPhone(ctx context.Context, category int, phone string) (*mysql.BizUnit, bool, error) {
	return r.dao.QueryBizUnitByPhone(ctx, category, phone)
}

func (r *Repo) QueryMultiBizUnit(ctx context.Context, ids []uint64) ([]*mysql.BizUnit, error) {
	return r.dao.QueryMultiBizUnit(ctx, ids)
}

func (r *Repo) QueryMultiBizUnitByNameLike(ctx context.Context, name string) ([]*mysql.BizUnit, error) {
	return r.dao.QueryMultiBizUnitByNameLike(ctx, name)
}

func (r *Repo) QueryMultiBizUnitByName(ctx context.Context, name []string) ([]*mysql.BizUnit, error) {
	return r.dao.QueryBizUnitByName(ctx, name)
}

func (r *Repo) QueryBizUnitList(ctx context.Context, q *structure_base.GetBizUnitListParams) (o []*mysql.BizUnit, count int, err error) {
	var (
		t           mysql.BizUnit
		bizUnitSale mysql.BizUnitSale
		cond        = mysql_base.NewCondition()
		list        []*mysql.BizUnit
		relUnitIds  = set.NewUint64Set()
	)
	mysql_system.CommonDataSeparate(ctx, &t, cond)

	if q.Category > 0 {
		cond.AddTableEqual(&t, "category", q.Category)
	}
	if q.SaleSystemID > 0 {
		saleSystemRelList, err := r.dao.FindBizUnitSaleSystemRelBySaleSystemId(ctx, q.SaleSystemID)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range saleSystemRelList {
			relUnitIds.Add(item.UnitId)
		}
		cond.AddContainMatch("biz_unit.id", relUnitIds.List())
	}
	if q.SaleSystemName != "" {
		saleSystemRelList, err := r.dao.FindBizUnitSaleSystemRelBySaleSystemIds(ctx, q.SaleSystemIDs)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range saleSystemRelList {
			relUnitIds.Add(item.UnitId)
		}
		cond.AddContainMatch("biz_unit.id", relUnitIds.List())
	}
	if q.Code != "" {
		cond.AddTableMultiFieldLikeMatchToOR(&t, []string{"custom_code", "name"}, q.Code)
	}
	if q.Name != "" {
		cond.AddTableMultiFieldLikeMatchToOR(&t, []string{"custom_code", "name", "full_name", "pin_yin"}, q.Name)
	}
	if q.Status > 0 {
		cond.AddTableEqual(&t, "status", q.Status)
	}
	if len(q.UnitTypeId) != 0 {
		unitTypeList, err := r.dao.QueryBizUnitTypeRelByUnitTypeIds(ctx, q.UnitTypeId)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range unitTypeList {
			q.Ids = append(q.Ids, item.UnitId)
		}
	}
	if q.Location != "" {
		cond.AddTableMultiFieldLikeMatch(&t, []string{"location"}, q.Location)
	}
	if q.Nop != "" {
		cond.AddTableMultiFieldLikeMatch(&t, []string{"phone", "name"}, q.Nop)
	}
	if q.Phone != "" {
		cond.AddTableMultiFieldLikeMatch(&t, []string{"phone"}, q.Phone)
	}
	if q.OrCategory > 0 && len(q.OrUnitTypeId) != 0 {
		// (category = 2  OR unit_type_id in (11))
		cond.AddEqualToOR("biz_unit.category", q.OrCategory)

		unitTypeList, err := r.dao.QueryBizUnitTypeRelByUnitTypeIds(ctx, q.OrUnitTypeId)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range unitTypeList {
			q.OrIds = append(q.OrIds, item.UnitId)
		}
		cond.AddContainMatchToOR("biz_unit.id", q.OrIds)
	}
	if len(q.Ids) > 0 {
		cond.AddContainMatch("biz_unit.id", q.Ids)
	}
	if q.SellerID > 0 || q.SaleGroupID > 0 || q.SaleAreaID > 0 || len(q.SellerIDs) > 0 || q.SellerName != "" {
		cond.AddTableLeftJoiner(&t, &bizUnitSale, "id", "unit_id")
		if q.SellerName != "" {
			cond.AddContainMatchToOR("biz_unit_sale.seller_id", q.SellerIDs)
		} else if len(q.SellerIDs) > 0 {
			cond.AddContainMatchToOR("biz_unit_sale.seller_id", q.SellerIDs)
		}
		if q.SellerID != 0 {
			cond.AddTableContainMatch(&bizUnitSale, "seller_id", q.SellerID)
		}
		if q.SaleGroupID != 0 {
			cond.AddTableEqual(&bizUnitSale, "sale_group_id", q.SaleGroupID)
		}
		if q.SaleAreaID != 0 {
			cond.AddTableEqual(&bizUnitSale, "sale_area_id", q.SaleAreaID)
		}
	}
	if len(q.Id) != 0 {
		cond.AddTableContainMatch(&t, "id", q.Id)
	}

	if q.IsFindByCodeOrName {
		cond.AddContainMatchToOR("biz_unit.custom_code", q.Codes)
		cond.AddContainMatchToOR("biz_unit.name", q.Names)
	}
	if q.Remark != "" {
		cond.AddTableFuzzyMatch(&t, "remark", q.Remark)
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	count, err = mysql_base.SearchListGroupForPaging(tx, &t, q, &list, cond, "biz_unit.id")
	if err != nil {
		return
	}
	o = list
	return
}

func (r *Repo) QuerySomeBizUnitFieldList(tx *mysql_base.Tx, q *structure_base.GetBizUnitListParams) (o []*mysql.SomeBizUnitField, count int, err error) {
	var (
		t    mysql.BizUnit
		cond = mysql_base.NewCondition()
	)

	if q.Category != 0 {
		cond.AddEqual("category", q.Category)
	}
	if q.Code != "" {
		cond.AddFuzzyMatch("custom_code", q.Code)
	}
	count, err = mysql_base.SearchListGroupForPaging(tx, &t, q, &o, cond)
	return
}

func (r *Repo) QueryBizUnitFactoryLogisticsList(ctx context.Context, tx *mysql_base.Tx, q *structure_base.GetBizUnitFactoryLogisticsListParams) (o []*mysql.BizUnitFactoryLogistics, count int, err error) {
	var (
		t    mysql.BizUnitFactoryLogistics
		cond = mysql_base.NewCondition()
		list []*mysql.BizUnitFactoryLogistics
	)
	mysql_system.CommonDataSeparate(ctx, &t, cond)

	if q.BizUnitId != 0 {
		cond.AddEqual("unit_id", q.BizUnitId)
	}
	if q.CustomerId != 0 {
		cond.AddEqual("unit_id", q.CustomerId)
	}
	if q.Name != "" {
		cond.AddFuzzyMatch("name", q.Name)
	}
	cond.AddSort("-is_default", "-create_time")

	count, err = mysql_base.SearchListGroupForPaging(tx, &t, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (r *Repo) CheckAreaIsUseByBizUnitByAreaIds(ctx context.Context, areaIds []uint64) (bool, error) {
	exit, err := r.dao.QueryBizUnitByAreaIds(ctx, areaIds)
	if err != nil {
		return false, err
	}
	return exit, nil
}

func (r *Repo) QueryEnableBizUnitIdsByCategoryAndType(ctx context.Context, ids []uint64, category uint8, unitTypeIds []uint64) ([]uint64, error) {
	unitTypeRel, err := r.dao.QueryBizUnitTypeRelByUnitTypeIds(ctx, unitTypeIds)
	if err != nil {
		return nil, err
	}
	if len(unitTypeRel) <= 0 {
		return nil, nil
	}
	unitIds := set.NewUint64Set()
	for _, rel := range unitTypeRel {
		unitIds.Add(rel.UnitId)
	}

	if len(ids) > 0 {
		// 取交集
		ids = tools.Intersection(ids, unitIds.List())
	} else {
		ids = unitIds.List()
	}

	return r.dao.QueryBizUnitIdsByCategoryAndType(ctx, ids, category, entity.StatusAvailable)
}

// UpdateBizUnitLocation 更新业务单元的地址信息
func (r *Repo) UpdateBizUnitLocation(ctx context.Context, tx *mysql_base.Tx, bizUnitId uint64, location string) error {
	// dao更新location字段
	return r.dao.UpdateBizUnitLocation(ctx, tx, bizUnitId, location)
}

func (r BizUnitRepo) UpdateSimple(ctx context.Context, p *entity.BizUnit) (err error) {
	if p.Id == 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeLackMustField, "id不能为0"))
		return
	}
	if p.SaleSystemId == 0 {
		p.SaleSystemId = metadata.GetSaleSystemId(ctx)
	}
	if len(p.SaleSystemIds) == 0 {
		p.SaleSystemIds = []uint64{p.SaleSystemId}
	}
	bizUnit, err := dao.MustFirstBizUnitByID(r.tx, p.Id)
	if err != nil {
		return
	}
	bizUnit.CustomCode = p.CustomCode
	bizUnit.Name = p.Name
	bizUnit.Phone = p.Phone
	bizUnit.SaleSystemId = p.SaleSystemId
	bizUnit, err = dao.MustUpdateBizUnit(r.tx, bizUnit)
	if err != nil {
		return
	}
	// 更新销售员
	bizUnitSale := mysql.BizUnitSale{}
	if p.Sale != nil {
		bizUnitSale, err = dao.MustFirstBizUnitSaleByUnitID(r.tx, p.Id)
		if err != nil {
			return
		}
		bizUnitSale.SellerID = p.Sale.SellerID
		if bizUnitSale.Id == 0 {
			bizUnitSale.Id = vars.Snowflake.GenerateId().UInt64()
			bizUnitSale.UnitID = bizUnit.Id
			bizUnitSale, err = dao.MustCreateBizUnitSale(r.tx, bizUnitSale)
			if err != nil {
				return
			}
		} else {
			bizUnitSale, err = dao.MustUpdateBizUnitSale(r.tx, bizUnitSale)
			if err != nil {
				return
			}
		}
	}
	// 删除旧的营销体系关联
	err = r.dao.MustDeleteBizUnitSaleSystemRelByUnitId(ctx, p.Id)
	if err != nil {
		return
	}

	if len(p.SaleSystemIds) > 0 {
		saleSystemPO := mysql.ToBizUnitSaleSystemRelPOList(p)
		if err = r.dao.Create(ctx, &saleSystemPO); err != nil {
			return err
		}
	}

	return
}

func (r *Repo) QueryBizUnitListNoDataSeparate(ctx context.Context, q *structure_base.GetBizUnitListParams) (o []*mysql.BizUnit, count int, err error) {
	var (
		t           mysql.BizUnit
		bizUnitSale mysql.BizUnitSale
		cond        = mysql_base.NewCondition()
		list        []*mysql.BizUnit
	)

	if q.Category > 0 {
		cond.AddTableEqual(&t, "category", q.Category)
	}
	if q.SaleSystemID > 0 {
		saleSystemRelList, err := r.dao.FindBizUnitSaleSystemRelBySaleSystemId(ctx, q.SaleSystemID)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range saleSystemRelList {
			q.Ids = append(q.Ids, item.UnitId)
		}
		cond.AddContainMatch("biz_unit.id", q.Ids)
	}
	if q.Code != "" {
		cond.AddTableMultiFieldLikeMatchToOR(&t, []string{"custom_code", "name"}, q.Code)
	}
	if q.Name != "" {
		cond.AddTableMultiFieldLikeMatchToOR(&t, []string{"custom_code", "name", "full_name"}, q.Name)
	}
	if q.Status > 0 {
		cond.AddTableEqual(&t, "status", q.Status)
	}
	if len(q.UnitTypeId) != 0 {
		unitTypeList, err := r.dao.QueryBizUnitTypeRelByUnitTypeIds(ctx, q.UnitTypeId)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range unitTypeList {
			q.Ids = append(q.Ids, item.UnitId)
		}
	}
	if q.Address != "" {
		cond.AddTableMultiFieldLikeMatch(&t, []string{"address"}, q.Address)
	}
	if q.Nop != "" {
		cond.AddTableMultiFieldLikeMatch(&t, []string{"phone", "name"}, q.Nop)
	}
	if q.Phone != "" {
		cond.AddTableMultiFieldLikeMatch(&t, []string{"phone"}, q.Phone)
	}
	if q.OrCategory > 0 && len(q.OrUnitTypeId) != 0 {
		// (category = 2  OR unit_type_id in (11))
		cond.AddEqualToOR("biz_unit.category", q.OrCategory)

		unitTypeList, err := r.dao.QueryBizUnitTypeRelByUnitTypeIds(ctx, q.OrUnitTypeId)
		if err != nil {
			return nil, 0, err
		}
		for _, item := range unitTypeList {
			q.OrIds = append(q.OrIds, item.UnitId)
		}
		cond.AddContainMatchToOR("biz_unit.id", q.OrIds)
	}
	if len(q.Ids) > 0 {
		cond.AddContainMatch("biz_unit.id", q.Ids)
	}
	if q.SellerID > 0 || q.SaleGroupID > 0 || q.SaleAreaID > 0 || len(q.SellerIDs) > 0 {
		cond.AddTableLeftJoiner(&t, &bizUnitSale, "id", "unit_id")
		if len(q.SellerIDs) > 0 {
			cond.AddContainMatchToOR("biz_unit_sale.seller_id", q.SellerIDs)
		}
		if q.SellerID != 0 {
			cond.AddTableContainMatch(&bizUnitSale, "seller_id", q.SellerID)
		}
		if q.SaleGroupID != 0 {
			cond.AddTableEqual(&bizUnitSale, "sale_group_id", q.SaleGroupID)
		}
		if q.SaleAreaID != 0 {
			cond.AddTableEqual(&bizUnitSale, "sale_area_id", q.SaleAreaID)
		}
	}
	if len(q.Id) != 0 {
		cond.AddTableContainMatch(&t, "id", q.Id)
	}

	if q.IsFindByCodeOrName {
		cond.AddContainMatchToOR("biz_unit.custom_code", q.Codes)
		cond.AddContainMatchToOR("biz_unit.name", q.Names)
	}
	if q.Remark != "" {
		cond.AddTableFuzzyMatch(&t, "remark", q.Remark)
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	count, err = mysql_base.SearchListGroupForPaging(tx, &t, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}
