package should_collect_order

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	common "hcscm/common/grey_fabric_manage"
	product_common "hcscm/common/product"
	common_collect "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product_base "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/warehouse"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/department"
	"hcscm/extern/pb/employee"
	"hcscm/extern/pb/product"
	"hcscm/extern/pb/sale_price"
	"hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	modelProduct "hcscm/model/mysql/product"
	arrange_mysql "hcscm/model/mysql/product/dao"
	sale_mysql "hcscm/model/mysql/sale/dao"
	model "hcscm/model/mysql/should_collect_order"
	mysql "hcscm/model/mysql/should_collect_order/dao"
	mysqlSystem "hcscm/model/mysql/system"
	product_structure "hcscm/structure/product"
	structure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"time"
)

type ProductSaleShouldCollectOrderRepo struct {
	tx *mysql_base.Tx
}

func NewProductSaleShouldCollectOrderRepo(tx *mysql_base.Tx) *ProductSaleShouldCollectOrderRepo {
	return &ProductSaleShouldCollectOrderRepo{tx: tx}
}

// 新增销售送货单
func (r *ProductSaleShouldCollectOrderRepo) Add(ctx context.Context, req *structure.AddProductSaleShouldCollectOrderParam) (id uint64, err error) {
	var (
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_system.NewSaleSystemClient()
		saleSysData        = sale_system.Res{}
	)
	productSaleShouldCollectOrder := model.NewProductSaleShouldCollectOrder(ctx, req)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_system.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleDeliveryOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleDeliveryOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "should_collect_order", productSaleShouldCollectOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	productSaleShouldCollectOrder.OrderNo = orderNo
	productSaleShouldCollectOrder.Number = int(number)

	productSaleShouldCollectOrder, err = mysql.MustCreateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
	if err != nil {
		return
	}

	return productSaleShouldCollectOrder.Id, err
}

// 新增销售送货单详情和细码信息
func (r *ProductSaleShouldCollectOrderRepo) AddProductSaleShouldCollectOrderDetailsAndFCDetails(ctx context.Context, req *structure.AddProductSaleShouldCollectOrderParam, parentId uint64) (err error) {
	var (
		itemListAfterUpdate model.ShouldCollectOrderDetailList
		stockDetails        modelProduct.StockProductDetailList
	)
	// for _, item := range req.ItemData {
	// 	productSaleShouldCollectOrderDetailItem := model.NewProductSaleShouldCollectOrderDetail(ctx, req, &item, parentId)
	//
	// 	productSaleShouldCollectOrderDetailItem, err = mysql.MustCreateShouldCollectOrderDetail(r.tx, productSaleShouldCollectOrderDetailItem)
	// 	if err != nil {
	// 		return
	// 	}
	// 	// 新增细码信息
	// 	for _, itemFc := range item.ItemFcList {
	// 		itemFc.ShouldCollectOrderId = productSaleShouldCollectOrderDetailItem.ShouldCollectOrderId
	// 		itemFc.OrderDetailId = productSaleShouldCollectOrderDetailItem.Id
	// 		itemFc.CollectType = productSaleShouldCollectOrderDetailItem.CollectType
	// 		shouldCollectOrderDetailFc := model.NewShouldCollectOrderDetailFc(ctx, &itemFc)
	//
	// 		shouldCollectOrderDetailFc, err = mysql.MustCreateShouldCollectOrderDetailFc(r.tx, shouldCollectOrderDetailFc)
	// 		if err != nil {
	// 			return
	// 		}
	// 	}
	// 	itemListAfterUpdate = append(itemListAfterUpdate, productSaleShouldCollectOrderDetailItem)
	// }
	stockDetails, err = arrange_mysql.FindStockProductDetailByIDs(r.tx, mysql_base.GetUInt64ListV2("stock_id", req))
	if err != nil {
		return

	}
	for _, item := range req.ItemData {
		dyelotNumberMap := make(map[string]structure.AddProductSaleShouldCollectOrderDetailFcParamList, 0)
		for _, itemFc := range item.ItemFcList {
			stockDetail := stockDetails.Pick(itemFc.StockId)
			dyelotNumberMap[stockDetail.DyelotNumber] = append(dyelotNumberMap[stockDetail.DyelotNumber], itemFc)
		}
		for dyelotNumber, fcList := range dyelotNumberMap {
			productSaleShouldCollectOrderDetailItem := model.NewProductSaleShouldCollectOrderDetail(ctx, req, &item, parentId, dyelotNumber, fcList)
			productSaleShouldCollectOrderDetailItem, err = mysql.MustCreateShouldCollectOrderDetail(r.tx, productSaleShouldCollectOrderDetailItem)
			if err != nil {
				return
			}
			for _, fc := range fcList {
				fc.ShouldCollectOrderId = productSaleShouldCollectOrderDetailItem.ShouldCollectOrderId
				fc.OrderDetailId = productSaleShouldCollectOrderDetailItem.Id
				fc.CollectType = productSaleShouldCollectOrderDetailItem.CollectType
				shouldCollectOrderDetailFc := model.NewShouldCollectOrderDetailFc(ctx, &fc)

				shouldCollectOrderDetailFc, err = mysql.MustCreateShouldCollectOrderDetailFc(r.tx, shouldCollectOrderDetailFc)
				if err != nil {
					return
				}
			}
			itemListAfterUpdate = append(itemListAfterUpdate, productSaleShouldCollectOrderDetailItem)
		}
	}
	if len(itemListAfterUpdate) > 0 {
		err = r.UpdateShouldCollectOrderTotalMsg(ctx, parentId, itemListAfterUpdate)
		if err != nil {
			return
		}
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) Update(ctx context.Context, req *structure.UpdateProductSaleShouldCollectOrderParam) (id uint64, err error) {
	var (
		productSaleShouldCollectOrder model.ShouldCollectOrder
	)
	productSaleShouldCollectOrder, err = mysql.MustFirstShouldCollectOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := productSaleShouldCollectOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	productSaleShouldCollectOrder.UpdateProductSaleShouldCollectOrder(ctx, req)

	productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
	if err != nil {
		return
	}

	return productSaleShouldCollectOrder.Id, err
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateProductSaleShouldCollectOrderDetails(ctx context.Context, req *structure.UpdateProductSaleShouldCollectOrderParam) (err error) {
	var (
		orders              model.ShouldCollectOrderList
		itemList            model.ShouldCollectOrderDetailList
		itemListAfterUpdate model.ShouldCollectOrderDetailList
	)
	// 找出该单下的信息
	itemList, err = mysql.FindShouldCollectOrderDetailByShouldCollectOrderDetailID(r.tx, req)
	if err != nil {
		return
	}
	orders, err = mysql.FindShouldCollectOrderByShouldCollectOrderID(r.tx, itemList)
	if err != nil {
		return
	}

	for _, item := range req.ItemData {
		productSaleShouldCollectOrderDetailItem := itemList.Pick(item.Id)
		order := orders.Pick(productSaleShouldCollectOrderDetailItem.ShouldCollectOrderId)
		if len(item.ItemFcList) != 0 {
			err = r.UpdateProductSaleShouldCollectOrderDetailFcList(ctx, &item, productSaleShouldCollectOrderDetailItem)
			if err != nil {
				return
			}
			weightError, settleWeightError := item.ItemFcList.GetTotal()
			item.WeightError = weightError
			item.SettleErrorWeight = settleWeightError
		}
		productSaleShouldCollectOrderDetailItem.UpdateProductSaleShouldCollectOrderDetail(ctx, order, &item)

		productSaleShouldCollectOrderDetailItem, err = mysql.MustUpdateShouldCollectOrderDetail(r.tx, productSaleShouldCollectOrderDetailItem)
		if err != nil {
			return
		}
		itemListAfterUpdate = append(itemListAfterUpdate, productSaleShouldCollectOrderDetailItem)
	}
	if len(itemListAfterUpdate) > 0 {
		err = r.UpdateShouldCollectOrderTotalMsg(ctx, req.Id, itemListAfterUpdate)
		if err != nil {
			return
		}
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateProductSaleShouldCollectOrderDetailFcList(
	ctx context.Context,
	req *structure.AddProductSaleShouldCollectOrderDetailParam,
	productSaleShouldCollectOrderDetailItem model.ShouldCollectOrderDetail,
) (err error) {
	var (
		shouldCollectOrderDetailFcList model.ShouldCollectOrderDetailFcList
		arrangeOrderItemFcResList      product.ArrangeOrderItemFcResList
		newIDs                         []uint64
	)
	// 找出该单下的信息，并删除
	shouldCollectOrderDetailFcList, err = mysql.FindShouldCollectOrderDetailFcByParentID(r.tx, req.Id)
	if err != nil {
		return
	}

	fpmArrangeOrderSvc := product.NewArrangeOrderClient()
	arrangeOrderItemFcResList, err = fpmArrangeOrderSvc.GetArrangeOrderWeightItemListByItemIds(ctx, product.ArrangeOrderReq{IDs: []uint64{req.SrcDetailId}})
	if err != nil {
		return
	}

	// 新增颜色信息
	for _, itemFc := range req.ItemFcList {
		if itemFc.Id == 0 {
			arrangeOrderItemFcRes := arrangeOrderItemFcResList.PickByID(itemFc.SrcDetailFcId)
			itemFc.ShouldCollectOrderId = productSaleShouldCollectOrderDetailItem.ShouldCollectOrderId
			itemFc.CollectType = productSaleShouldCollectOrderDetailItem.CollectType
			itemFc.Roll = arrangeOrderItemFcRes.Roll
			itemFc.WarehouseId = arrangeOrderItemFcRes.WarehouseId
			itemFc.WarehouseBinId = arrangeOrderItemFcRes.WarehouseBinId
			itemFc.VolumeNumber = arrangeOrderItemFcRes.VolumeNumber
			itemFc.ArrangeOrderNo = arrangeOrderItemFcRes.ArrangeOrderNo
			itemFc.StockId = arrangeOrderItemFcRes.StockId
			itemFc.SumStockId = arrangeOrderItemFcRes.SumStockId
			itemFc.BaseUnitWeight = arrangeOrderItemFcRes.BaseUnitWeight
			itemFc.PaperTubeWeight = arrangeOrderItemFcRes.PaperTubeWeight
			itemFc.UnitId = arrangeOrderItemFcRes.UnitId
			itemFc.Length = arrangeOrderItemFcRes.Length
			shouldCollectOrderDetailFc := model.NewShouldCollectOrderDetailFc(ctx, &itemFc)

			shouldCollectOrderDetailFc, err = mysql.MustCreateShouldCollectOrderDetailFc(r.tx, shouldCollectOrderDetailFc)
			if err != nil {
				return
			}
			continue
		}
		newIDs = append(newIDs, itemFc.Id)
		shouldCollectOrderDetailFc := shouldCollectOrderDetailFcList.Pick(itemFc.OrderDetailFcId)
		shouldCollectOrderDetailFc.UpdateShouldCollectOrderDetailFc(ctx, &itemFc)

		shouldCollectOrderDetailFc, err = mysql.MustUpdateShouldCollectOrderDetailFc(r.tx, shouldCollectOrderDetailFc)
		if err != nil {
			return
		}
	}

	// 有新增和更新逻辑应该不用删除吧
	// oldIDs := shouldCollectOrderDetailFcList.GetIds()
	// oldIDs, _, _, _ = tools.CheckUint64Set(oldIDs, newIDs)
	// if len(oldIDs) != 0 {
	//	err = mysql.MustDeleteShouldCollectOrderDetailFcByIds(r.tx, oldIDs)
	//	if err != nil {
	//		return
	//	}
	// }
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateShouldCollectOrderTotalMsg(ctx context.Context, id uint64, detailList model.ShouldCollectOrderDetailList) (err error) {
	var (
		shouldCollectOrder model.ShouldCollectOrder
	)
	shouldCollectOrder, err = mysql.MustFirstShouldCollectOrderByID(r.tx, id)
	if err != nil {
		return
	}
	shouldCollectOrder.Roll, shouldCollectOrder.Weight, shouldCollectOrder.TotalSettleMoney, shouldCollectOrder.OriginPrice = detailList.GetTotal()
	shouldCollectOrder.UncollectMoney = shouldCollectOrder.TotalSettleMoney
	shouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, shouldCollectOrder)
	if err != nil {
		return
	}
	return
}

// 合并单据
func (r *ProductSaleShouldCollectOrderRepo) MergeItems(ctx context.Context, req *structure.UpdateMergeProductSaleShouldCollectOrderParam) (
	id uint64,
	err error,
) {
	var (
		shouldCollectOrder model.ShouldCollectOrder
		mergeOrders        model.ShouldCollectOrderList
		mergeDetails       model.ShouldCollectOrderDetailList
	)
	shouldCollectOrder, err = mysql.MustFirstShouldCollectOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	mergeOrders, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.IDs)
	if err != nil {
		return
	}

	mergeDetails, err = mysql.FindShouldCollectOrderDetailByParentIDs(r.tx, req.IDs)
	if err != nil {
		return
	}

	for _, mergeOrder := range mergeOrders {
		shouldCollectOrder.TotalSettleMoney += mergeOrder.TotalSettleMoney
		shouldCollectOrder.OriginPrice += mergeOrder.OriginPrice
		shouldCollectOrder.Roll += mergeOrder.Roll
		shouldCollectOrder.Weight += mergeOrder.Weight

		// 作废合并的单据
		mergeOrder.AuditStatus = common_system.OrderStatusVoided
		mergeOrder.AuditorId = metadata.GetUserId(ctx)
		mergeOrder.AuditDate = time.Now()
		mergeOrder.CancelRemark = fmt.Sprintf("%v%v", "销售送货单合并，合并后销售送货单号为", shouldCollectOrder.OrderNo)

		mergeOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, mergeOrder)
		if err != nil {
			return
		}

		// 创建关系表
		shouldCollectOrderRel := model.NewShouldCollectOrderRel(ctx, shouldCollectOrder.Id, mergeOrder.Id)
		shouldCollectOrderRel, err = mysql.MustCreateShouldCollectOrderRel(r.tx, shouldCollectOrderRel)
		if err != nil {
			return
		}
	}

	shouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, shouldCollectOrder)
	if err != nil {
		return
	}

	for _, mergeDetail := range mergeDetails {
		shouldCollectOrderDetail := mergeDetail.NewMergeProductSaleShouldCollectOrderDetail(shouldCollectOrder.Id, shouldCollectOrder.IsWithTaxRate)

		shouldCollectOrderDetail, err = mysql.MustCreateShouldCollectOrderDetail(r.tx, shouldCollectOrderDetail)
		if err != nil {
			return
		}
	}
	return shouldCollectOrder.Id, nil
}

// 拆分合并单据
func (r *ProductSaleShouldCollectOrderRepo) BreakUpItems(ctx context.Context, req *structure.UpdateMergeProductSaleShouldCollectOrderParam) (
	id uint64,
	err error,
) {
	var (
		shouldCollectOrder        model.ShouldCollectOrder
		shouldCollectOrderDetails model.ShouldCollectOrderDetailList
		mergeOrders               model.ShouldCollectOrderList
		shouldCollectOrderRels    model.ShouldCollectOrderRelList
	)
	shouldCollectOrder, err = mysql.MustFirstShouldCollectOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	shouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByMergeOrderIDs(r.tx, req.IDs)
	if err != nil {
		return
	}

	mergeOrders, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.IDs)
	if err != nil {
		return
	}

	shouldCollectOrderRels, err = mysql.FindShouldCollectOrderRelByMergeOrderIds(r.tx, req.IDs)
	if err != nil {
		return
	}

	for _, mergeOrder := range mergeOrders {
		shouldCollectOrder.TotalSettleMoney -= mergeOrder.TotalSettleMoney
		shouldCollectOrder.OriginPrice -= mergeOrder.OriginPrice
		shouldCollectOrder.Roll -= mergeOrder.Roll
		shouldCollectOrder.Weight -= mergeOrder.Weight

		// 还原合并的单据
		mergeOrder.AuditStatus = common_system.OrderStatusPendingAudit
		mergeOrder.AuditorId = 0
		mergeOrder.AuditDate = time.Time{}
		mergeOrder.CancelRemark = ""

		// 如果有销售出仓单信息，添加到拆分出来的单据中
		if req.SaleOutOrderNo != "" && req.SaleOutOrderId != 0 {
			mergeOrder.SrcOrderNo = req.SaleOutOrderNo
			mergeOrder.SrcId = req.SaleOutOrderId
			mergeOrder.SrcOrderType = common_collect.SrcOrderTypeProductSaleOut
		}

		mergeOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, mergeOrder)
		if err != nil {
			return
		}
	}

	shouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, shouldCollectOrder)
	if err != nil {
		return
	}

	// 删除合并关系表
	for _, shouldCollectOrderRel := range shouldCollectOrderRels {
		err = mysql.MustDeleteShouldCollectOrderRel(r.tx, shouldCollectOrderRel)
		if err != nil {
			return
		}
	}

	// 删除拆分的详情
	for _, shouldCollectOrderDetail := range shouldCollectOrderDetails {
		err = mysql.MustDeleteShouldCollectOrderDetail(r.tx, shouldCollectOrderDetail)
		if err != nil {
			return
		}
	}
	return shouldCollectOrder.Id, nil
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateDetailsReturnItem(ctx context.Context, req structure.ModifyProductSaleShouldCollectOrder) (err error) {
	var (
		item model.ShouldCollectOrderDetail
	)
	// 找出该单下的信息
	item, err = mysql.MustFirstShouldCollectOrderDetailByID(r.tx, req.Id)
	if err != nil {
		return
	}

	item.ReturnRoll += req.ReturnRoll
	item.ReturnLength += req.ReturnLength
	item.ReturnWeight += req.ReturnWeight

	item, err = mysql.MustUpdateShouldCollectOrderDetail(r.tx, item)
	if err != nil {
		return
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateStatusPass(ctx context.Context, req *structure.UpdateProductSaleShouldCollectOrderAuditStatusParam) (
	data structure.UpdateProductSaleShouldCollectOrderAuditStatusData,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		productSaleShouldCollectOrderList    model.ShouldCollectOrderList
		productSaleShouldCollectOrderDetails model.ShouldCollectOrderDetailList
		_salePlanOrderItemIds                = make([]uint64, 0)
	)

	productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	productSaleShouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByParentIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrderList {
		// 审核
		err = productSaleShouldCollectOrder.Audit(ctx)
		if err != nil {
			return
		}
		_productSaleShouldCollectOrderDetails := productSaleShouldCollectOrderDetails.PickByShouldCollectOrderId(productSaleShouldCollectOrder.Id)
		productSaleShouldCollectOrder.Roll, productSaleShouldCollectOrder.Weight, productSaleShouldCollectOrder.TotalSettleMoney, productSaleShouldCollectOrder.OriginPrice = _productSaleShouldCollectOrderDetails.GetTotal()

		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
		// 审核通过后将配布单的业务状态改为可出仓
		// 获取配布单明细
		arrangeOrder, exist, err2 := arrange_mysql.FirstFpmArrangeOrderByID(r.tx, productSaleShouldCollectOrder.SrcId)
		if err2 != nil {
			return
		}
		if !exist || err != nil {
			continue
		}
		if arrangeOrder.PickUpGoodsInOrder == true {
			// 通过配布单src_id查询相关的其他arrangorder
			// 使用项目中现有的查询方法
			query := &product_structure.GetFpmArrangeOrderListQuery{
				SrcId:       arrangeOrder.SrcId,
				JudgeStatus: true,
				IsNotPage:   true,
			}

			// 查询与srcId相关的所有配布单
			arrangeOrderList, _, err2 := arrange_mysql.SearchFpmArrangeOrderCrossRvt(r.tx, query)
			if err2 != nil {
				return
			}

			// 如果找到相关配布单，记录原状态并更新这些配布单的业务状态为可出仓
			for _, relatedArrangeOrder := range arrangeOrderList {
				// 记录原业务状态到PreBusinessStatus字段
				relatedArrangeOrder.PreBusinessStatus = relatedArrangeOrder.BusinessStatus
				// 修改配布业务状态为可出仓
				relatedArrangeOrder.BusinessStatus = product_common.BusinessStatusArrangeOuting
				_, updateErr := arrange_mysql.MustUpdateFpmArrangeOrder(r.tx, relatedArrangeOrder)
				if updateErr != nil {
					// 记录错误但继续处理其他配布单
					fmt.Printf("更新相关配布单状态失败，Id: %d, 错误: %v\n", relatedArrangeOrder.Id, updateErr)
				}
				fmt.Println(relatedArrangeOrder)
			}
		} else {
			// 修改配布业务状态为可出仓
			arrangeOrder.BusinessStatus = product_common.BusinessStatusArrangeOuting
			_, err = arrange_mysql.MustUpdateFpmArrangeOrder(r.tx, arrangeOrder)
			if err != nil {
				continue
			}
		}
	}
	for _, detail := range productSaleShouldCollectOrderDetails {
		if detail.SalePlanOrderItemId != 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, detail.SalePlanOrderItemId)
		}
	}
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateStatusWait(ctx context.Context, req *structure.UpdateProductSaleShouldCollectOrderAuditStatusParam) (
	data structure.UpdateProductSaleShouldCollectOrderAuditStatusData,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		productSaleShouldCollectOrderList       model.ShouldCollectOrderList
		productSaleShouldCollectOrderDetailList model.ShouldCollectOrderDetailList
		productSvc                              = product.NewSaleOutOrderClient()
		_salePlanOrderItemIds                   = make([]uint64, 0)
	)

	productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrderList {

		var (
			srcDetailIds = make([]uint64, 0)
		)

		// 获取分录行
		productSaleShouldCollectOrderDetailList, err = mysql.FindShouldCollectOrderDetailByParentID(r.tx, productSaleShouldCollectOrder.Id)
		if err != nil {
			return
		}

		for _, detail := range productSaleShouldCollectOrderDetailList {
			// 判断是否存在退货关联
			if detail.ReturnRoll != 0 || detail.ReturnLength != 0 || detail.ReturnWeight != 0 {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeOrderHasReturn))
				return
			}
			if detail.SrcDetailId > 0 {
				srcDetailIds = append(srcDetailIds, detail.SrcDetailId)
			}
			if detail.SalePlanOrderItemId != 0 {
				_salePlanOrderItemIds = append(_salePlanOrderItemIds, detail.SalePlanOrderItemId)
			}
		}

		if len(srcDetailIds) > 0 {
			isNocancel, nos, err2 := productSvc.JudgeSaleOutOrderPassByArrangeOrderItemIds(ctx, srcDetailIds)
			if err2 != nil {
				err = err2
				return
			}
			if isNocancel {
				err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "存在出仓单未消审："+nos))
				return
			}
		}

		// 消审
		err = productSaleShouldCollectOrder.Wait(ctx)
		if err != nil {
			return
		}

		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}

		// 如果是配布单来源，需要处理配布单的状态恢复
		if productSaleShouldCollectOrder.SrcOrderType == common_collect.SrcOrderTypeArrange {
			// 获取配布单信息
			arrangeOrder, exist, err2 := arrange_mysql.FirstFpmArrangeOrderByID(r.tx, productSaleShouldCollectOrder.SrcId)
			if err2 != nil || !exist {
				continue
			}
			// 通过配布单src_id查询相关的其他arrangorder
			query := &product_structure.GetFpmArrangeOrderListQuery{
				SrcId:       arrangeOrder.SrcId,
				JudgeStatus: true,
				IsNotPage:   true,
			}
			// 查询与srcId相关的所有配布单
			arrangeOrderList, _, err2 := arrange_mysql.SearchFpmArrangeOrderCrossRvt(r.tx, query)
			for _, relatedArrangeOrder := range arrangeOrderList {
				if relatedArrangeOrder.PickUpGoodsInOrder == true {
					if err2 != nil {
						continue
					}
					// 恢复业务状态为审核前的状态
					if relatedArrangeOrder.PreBusinessStatus != 0 {
						relatedArrangeOrder.BusinessStatus = relatedArrangeOrder.PreBusinessStatus
						_, updateErr := arrange_mysql.MustUpdateFpmArrangeOrder(r.tx, relatedArrangeOrder)
						if updateErr != nil {
							fmt.Printf("恢复相关配布单状态失败，Id: %d, 错误: %v\n", relatedArrangeOrder.Id, updateErr)
						}
					}
				}
			}
		}
	}
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateStatusReject(ctx context.Context, req *structure.UpdateProductSaleShouldCollectOrderAuditStatusParam) (data structure.UpdateProductSaleShouldCollectOrderAuditStatusData, err error) {
	var (
		productSaleShouldCollectOrderList model.ShouldCollectOrderList
	)

	productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrderList {
		// 驳回
		err = productSaleShouldCollectOrder.Reject(ctx)
		if err != nil {
			return
		}

		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateProductSaleShouldCollectOrderAuditStatusParam) (err error) {
	var (
		productSaleShouldCollectOrderList model.ShouldCollectOrderList
	)
	if req.Id != "" {
		productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.Id.ToUint64())
		if err != nil {
			return
		}
	} else {
		productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.IDs)
		if err != nil {
			return
		}
	}

	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrderList {
		// 作废
		err = productSaleShouldCollectOrder.Cancel(ctx)
		if err != nil {
			return
		}
		productSaleShouldCollectOrder.CancelRemark = req.CancelRemark
		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateShouldCollectOrderBusinessCloseParam) (data structure.UpdateProductSaleShouldCollectOrderBusinessCloseData, err error) {
	var (
		productSaleShouldCollectOrderList model.ShouldCollectOrderList
	)
	productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrderList {

		err = productSaleShouldCollectOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) Delete(ctx context.Context, req *structure.DeleteProductSaleShouldCollectOrderParam) (data structure.DeleteProductSaleShouldCollectOrderData, err error) {
	var (
		productSaleShouldCollectOrderList model.ShouldCollectOrderList
	)

	productSaleShouldCollectOrderList, err = mysql.FindShouldCollectOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}

	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrderList {
		// 删除
		err = mysql.MustDeleteShouldCollectOrder(r.tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
		data.Id = append(data.Id, productSaleShouldCollectOrder.Id)
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetMainOrderInfoBySrcId(ctx context.Context, srcId uint64) (
	data structure.GetProductSaleShouldCollectOrderDataForTransferDetail, err error) {
	sclOrder, err := mysql.MustFirstShouldCollectOrderBySrcID(r.tx, srcId)
	if err != nil {
		return
	}
	detailList, err := mysql.FindShouldCollectOrderDetailByParentID(r.tx, sclOrder.Id)
	if err != nil {
		return
	}
	data.Id = sclOrder.Id
	data.TotalSettleMoney = sclOrder.TotalSettleMoney
	data.TotalCollectedMoney = sclOrder.CollectedMoney
	data.TotalRemoveMoney = sclOrder.OffsetPrice
	data.TotalDiscountMoney = sclOrder.DiscountPrice
	data.TotalChargebackMoney = sclOrder.ReducePrice
	data.TotalUncollectMoney = sclOrder.UncollectMoney
	data.CustomerId = sclOrder.CustomerId
	data.CollectStatus = int(sclOrder.CollectStatus)
	data.CollectStatusName = sclOrder.CollectStatus.String()
	data.WriteOffPrice = sclOrder.TotalSettleMoney - sclOrder.UncollectMoney

	data.OrderNo = sclOrder.OrderNo
	data.CollectType = sclOrder.CollectType
	data.CollectTypeName = sclOrder.CollectType.String()
	data.OrderTime = tools.MyTime(sclOrder.OrderTime)
	data.Roll = sclOrder.Roll
	data.Weight = sclOrder.Weight
	data.TotalShouldCollectMoney = sclOrder.TotalSettleMoney - sclOrder.OffsetPrice - sclOrder.DiscountPrice - sclOrder.ReducePrice
	for _, detail := range detailList {
		data.Length += detail.Length
	}

	// 统计累计欠款
	list, _, err := mysql.SearchShouldCollectOrder(r.tx, &structure.GetShouldCollectOrderListQuery{CustomerId: sclOrder.CustomerId, AuditStatus: "2"})
	if err != nil {
		return
	}
	for _, order := range list {
		data.TotalArrearsAmount += order.UncollectMoney
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) Get(ctx context.Context, req *structure.GetProductSaleShouldCollectOrderQuery) (
	data structure.GetProductSaleShouldCollectOrderData, err error) {
	var (
		productSaleShouldCollectOrder        model.ShouldCollectOrder
		productSaleShouldCollectOrderDetails model.ShouldCollectOrderDetailList
		mergeOrders                          model.ShouldCollectOrderList
		items                                = make(structure.GetProductSaleShouldCollectOrderDetailDataList, 0)
		arrangeOrderItemFcResList            product.ArrangeOrderItemFcResList
		fpmSaleOutOrder                      product.SaleOutOrderRes
		fpmSaleOutOrderItemFcResList         product.FpmSaleOutOrderItemFcResList
		fpmSaleOutOrderSvc                   = product.NewSaleOutOrderClient()
		shouldCollectOrderDetailFcList       model.ShouldCollectOrderDetailFcList
		fpmSaleOutOrders                     = make(map[uint64]product.SaleOutOrderRes)
		// 添加一个映射，记录每个配布单srcid对应的销售出仓单信息
		srcIdToSaleOutOrder = make(map[uint64]product.SaleOutOrderRes)
	)
	productSaleShouldCollectOrder, err = mysql.MustFirstShouldCollectOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	productSaleShouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByParentID(r.tx, productSaleShouldCollectOrder.Id)
	if err != nil {
		return
	}
	// fpmSaleOutOrderSvc := product.NewSaleOutOrderClient()

	// 获取全部细码
	if req.WithAllWeight {
		// 特殊流程，销售出仓单下推销售送货单，源单类型未销售出仓单
		if productSaleShouldCollectOrder.SrcOrderType == common_collect.SrcOrderTypeProductSaleOut {
			// 查询销售出仓单详情和细码
			srcDetailIds := productSaleShouldCollectOrderDetails.GetSrcDetailIds()
			fpmSaleOutOrderItemFcResList, err = fpmSaleOutOrderSvc.GetSaleOutOrderItemFcBySaleOutOrderDetailIDs(ctx, product.SaleOutOrderReq{IDs: srcDetailIds})
			if err != nil {
				return
			}
			fpmSaleOutOrder, err = fpmSaleOutOrderSvc.GetSaleOutOrder(ctx, product.SaleOutOrderReq{Id: productSaleShouldCollectOrder.SrcId})
			if err != nil {
				return
			}
		} else {
			// 正常流程获取配布单细码
			fpmArrangeOrderSvc := product.NewArrangeOrderClient()
			srcDetailId := productSaleShouldCollectOrderDetails.GetSrcDetailIds()
			arrangeOrderItemFcResList, err = fpmArrangeOrderSvc.GetArrangeOrderWeightItemListByItemIds(ctx, product.ArrangeOrderReq{IDs: srcDetailId})
			if err != nil {
				return
			}
		}
		shouldCollectOrderDetailFcList, err = mysql.FindShouldCollectOrderDetailFcByShouldCollectOrderID(r.tx, productSaleShouldCollectOrder.Id)
		if err != nil {
			return
		}
	}

	mergeOrderIds := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "merge_should_collect_order_id")
	mergeOrders, err = mysql.FindShouldCollectOrderByIDs(r.tx, mergeOrderIds)
	if err != nil {
		return
	}

	// 在获取 fpmArrangeOrders 后,获取关联的销售出仓单
	fpmArrangeOrderIds := append(mergeOrders.GetSrcIds(), productSaleShouldCollectOrder.SrcId)
	fpmArrangeOrderSvc := product.NewArrangeOrderClient()
	fpmArrangeOrders, err := fpmArrangeOrderSvc.GetArrangeOrderList(ctx, product.ArrangeOrderReq{IDs: fpmArrangeOrderIds})
	if err != nil {
		return
	}

	// 获取销售出仓单
	fpmSaleOutOrderSvc = product.NewSaleOutOrderClient()
	if productSaleShouldCollectOrder.SrcOrderType == common_collect.SrcOrderTypeProductSaleOut {
		// 直接获取销售出仓单
		fpmSaleOutOrder, err = fpmSaleOutOrderSvc.GetSaleOutOrder(ctx, product.SaleOutOrderReq{Id: productSaleShouldCollectOrder.SrcId})
	} else {
		// 通过配布单获取销售出仓单
		fpmSaleOutOrders, err = fpmSaleOutOrderSvc.GetSaleOutOrderByArrangeOrderIDs(ctx, product.SaleOutOrderReq{IDs: fpmArrangeOrderIds})

		for srcId, saleOutOrder := range fpmSaleOutOrders {
			srcIdToSaleOutOrder[srcId] = saleOutOrder
		}
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrder, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystemMap, err := saleSystemSvc.GetSaleSystemListMapByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productSaleShouldCollectOrder, fpmArrangeOrders, fpmSaleOutOrder)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.QueryBizUnitListById(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64ListV2("process_factory_id", fpmArrangeOrders, fpmSaleOutOrder, fpmSaleOutOrders)
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	departmentIds := mysql_base.GetUInt64List(productSaleShouldCollectOrder, "department_id")
	departmentSvc := department.NewDepartmentClient()
	departmentName, err := departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(productSaleShouldCollectOrder, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(productSaleShouldCollectOrder, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	saleLevelIds := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "sale_level_id")
	saleLevelSvc := sale_price.NewSaleLevelClient()
	saleLevel, err := saleLevelSvc.GetSaleLevelNameByIds(ctx, saleLevelIds)
	if err != nil {
		return
	}

	saleGroupIds := mysql_base.GetUInt64List(fpmArrangeOrders, "sale_group_id")
	saleGroup, err := bizUnitSvc.GetSaleGroupNameByIds(ctx, saleGroupIds)
	if err != nil {
		return
	}

	logisticsCompanyIds := mysql_base.GetUInt64ListV2("logistics_company_id", fpmArrangeOrders, fpmSaleOutOrder, fpmSaleOutOrders)
	logisticsCompanySvc := info_basic_data.NewInfoSaleLogisticsCompanyClient()
	logisticsCompany, err := logisticsCompanySvc.GetInfoSaleLogisticsCompanyNameByIds(ctx, logisticsCompanyIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64ListV2("warehouse_id", fpmArrangeOrders, fpmSaleOutOrder, fpmSaleOutOrders)
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(fpmArrangeOrders, "product_level_id")
	// 获取应收单详情的成品等级
	productLevelIds2 := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "product_level_id")
	// 追加
	productLevelIds = append(productLevelIds, productLevelIds2...)
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	measurementUnitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", productSaleShouldCollectOrderDetails)
	measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "product_id")
	productSvc := product_base.NewProductClient()
	productItems, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "product_color_id")
	productColorSvc := product_base.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	// sale_mysql
	transferOrder, err := sale_mysql.NewSaleTransferOrderDao(ctx, false).GetByIds(r.tx, []uint64{productSaleShouldCollectOrder.SrcId})
	if err != nil {
		return
	}

	fpmArrangeOrder := fpmArrangeOrders.PickByOrderID(productSaleShouldCollectOrder.SrcId)
	o := structure.GetProductSaleShouldCollectOrderData{}
	o.Id = productSaleShouldCollectOrder.Id
	o.CreateTime = tools.MyTime(productSaleShouldCollectOrder.CreateTime)
	o.UpdateTime = tools.MyTime(productSaleShouldCollectOrder.UpdateTime)
	o.CreatorId = productSaleShouldCollectOrder.CreatorId
	o.CreatorName = productSaleShouldCollectOrder.CreatorName
	o.UpdaterId = productSaleShouldCollectOrder.UpdaterId
	o.UpdateUserName = productSaleShouldCollectOrder.UpdaterName
	o.OrderNo = productSaleShouldCollectOrder.OrderNo
	o.CollectType = productSaleShouldCollectOrder.CollectType
	o.CollectTypeName = productSaleShouldCollectOrder.CollectType.String()
	o.SrcOrderType = productSaleShouldCollectOrder.SrcOrderType
	o.SrcOrderTypeName = productSaleShouldCollectOrder.SrcOrderType.String()
	o.SrcId = productSaleShouldCollectOrder.SrcId
	o.SrcOrderNo = productSaleShouldCollectOrder.SrcOrderNo
	o.SaleSystemId = productSaleShouldCollectOrder.SaleSystemId
	o.SaleMode = productSaleShouldCollectOrder.SaleMode
	o.SaleModeName = productSaleShouldCollectOrder.SaleMode.String()
	if saleSystem, ok := saleSystemMap[productSaleShouldCollectOrder.SaleSystemId]; ok {
		o.SaleSystemName = saleSystem.Name
		o.SaleSystemPhone = saleSystem.Phone
		o.SaleSystemFaxNumber = saleSystem.FaxNumber
		o.SaleSystemAddr = saleSystem.AddressDetail
		o.SaleSystemContacts = saleSystem.Contacts
	}
	o.CustomerId = productSaleShouldCollectOrder.CustomerId
	o.CustomerCode = bizUnit[productSaleShouldCollectOrder.CustomerId].CustomCode
	o.CustomerName = bizUnit[productSaleShouldCollectOrder.CustomerId].Name
	o.CustomerFullName = bizUnit[productSaleShouldCollectOrder.CustomerId].FullName
	o.OrderRemark = productSaleShouldCollectOrder.OrderRemark
	o.CancelRemark = productSaleShouldCollectOrder.CancelRemark
	o.AuditStatus = productSaleShouldCollectOrder.AuditStatus
	o.AuditStatusName = productSaleShouldCollectOrder.AuditStatus.String()
	o.DepartmentId = productSaleShouldCollectOrder.DepartmentId
	o.DepartmentName = departmentName[productSaleShouldCollectOrder.DepartmentId]
	o.CompanyId = productSaleShouldCollectOrder.CompanyId
	o.AuditorId = productSaleShouldCollectOrder.AuditorId
	o.AuditorName = user[productSaleShouldCollectOrder.AuditorId]
	o.AuditDate = tools.MyTime(productSaleShouldCollectOrder.AuditDate)
	o.BusinessClose = productSaleShouldCollectOrder.BusinessClose
	o.BusinessCloseName = productSaleShouldCollectOrder.BusinessClose.String()
	o.BusinessCloseUserId = productSaleShouldCollectOrder.BusinessCloseUserId
	o.BusinessCloseUserName = user[productSaleShouldCollectOrder.BusinessCloseUserId]
	o.BusinessCloseTime = tools.MyTime(productSaleShouldCollectOrder.BusinessCloseTime)
	o.OrderTime = tools.MyTime(productSaleShouldCollectOrder.OrderTime)
	o.SaleUserId = productSaleShouldCollectOrder.SaleUserId
	o.SaleUserName = employeeName[productSaleShouldCollectOrder.SaleUserId]
	o.SaleFollowerId = productSaleShouldCollectOrder.SaleFollowerId
	o.SaleFollowerName = employeeName[productSaleShouldCollectOrder.SaleFollowerId]
	o.SettleType = productSaleShouldCollectOrder.SettleType
	if o.SettleType > 0 {
		o.SettleTypeName = o.SettleType.String()
	}
	o.CustomCycle = productSaleShouldCollectOrder.CustomCycle
	o.CollectStatus = productSaleShouldCollectOrder.CollectStatus
	o.CollectStatusName = productSaleShouldCollectOrder.CollectStatus.String()
	o.VoucherNumber = productSaleShouldCollectOrder.VoucherNumber
	o.InternalRemark = fpmArrangeOrder.InternalRemark
	o.LogisticsArea = fpmArrangeOrder.LogisticsArea
	o.ProcessFactoryId = fpmArrangeOrder.ProcessFactoryId
	o.ProcessFactoryName = logisticsName[fpmArrangeOrder.ProcessFactoryId]
	o.Contacts = fpmArrangeOrder.Contacts
	o.ReceiveAddr = fpmArrangeOrder.ReceiveAddr
	o.ReceivePhone = fpmArrangeOrder.ReceivePhone
	o.ReceiveTag = fpmArrangeOrder.ReceiveTag
	o.LogisticsCompanyId = fpmArrangeOrder.LogisticsCompanyId
	o.LogisticsCompanyName = logisticsCompany[fpmArrangeOrder.LogisticsCompanyId]
	if productSaleShouldCollectOrder.SrcOrderType == common_collect.SrcOrderTypeProductSaleOut {
		o.InternalRemark = fpmSaleOutOrder.InternalRemark
		o.LogisticsArea = fpmSaleOutOrder.LogisticsCompanyArea
		o.ProcessFactoryId = fpmSaleOutOrder.ProcessFactoryId
		o.ProcessFactoryName = logisticsName[fpmSaleOutOrder.ProcessFactoryId]
		o.Contacts = fpmSaleOutOrder.ReceiveName
		o.ReceiveAddr = fpmSaleOutOrder.ReceiveAddr
		o.ReceivePhone = fpmSaleOutOrder.ReceivePhone
		o.ReceiveTag = fpmSaleOutOrder.ReceiveTag
		o.LogisticsCompanyId = fpmSaleOutOrder.LogisticsCompanyId
		o.LogisticsCompanyName = logisticsCompany[fpmSaleOutOrder.LogisticsCompanyId]
	}
	o.SendProductRemark = fpmArrangeOrder.SendProductRemark
	o.SendProductType = fpmArrangeOrder.SendProductType
	o.SendProductTypeName = fpmArrangeOrder.SendProductType.String()
	o.SaleGroupId = fpmArrangeOrder.SaleGroupId
	o.SaleGroupName = saleGroup[fpmArrangeOrder.SaleGroupId]
	o.PostageItems = fpmArrangeOrder.PostageItems
	o.PostageItemsName = fpmArrangeOrder.PostageItems.String()
	if len(transferOrder) > 0 {
		o.Contacts = transferOrder[0].ContactName
		o.ReceivePhone = transferOrder[0].Phone
		o.ReceiveAddr = transferOrder[0].Address
	}
	{
		// 查找审核通过的单
		actuallyCollectOrders := model.ActuallyCollectOrderList{}
		actuallyCollectOrderAdvItems := model.ActuallyCollectOrderItemList{}
		actuallyCollectOrders, err = mysql.SearchActuallyCollectOrderPass(r.tx)
		if err != nil {
			return
		}
		actIds := actuallyCollectOrders.GetIds()
		actuallyCollectOrderAdvItems, err = mysql.SearchActuallyCollectOrderItemCollectOrderOrder(r.tx, req.Id, actIds)
		if err != nil {
			return
		}
		for _, item := range actuallyCollectOrderAdvItems {
			o.TotalCollectedMoney += item.ActuallyCollectPrice
			o.TotalRemoveMoney += item.OffsetPrice
			o.TotalDiscountMoney += item.DiscountPrice
			o.TotalChargebackMoney += item.DeductionPrice
		}
		o.TotalUncollectMoney = productSaleShouldCollectOrder.TotalSettleMoney - o.TotalCollectedMoney - o.TotalUncollectMoney - o.TotalDiscountMoney - o.TotalChargebackMoney - o.TotalRemoveMoney
	}
	o.TotalSettleMoney = productSaleShouldCollectOrder.TotalSettleMoney
	o.TotalShouldCollectMoney = productSaleShouldCollectOrder.TotalSettleMoney - productSaleShouldCollectOrder.OffsetPrice - productSaleShouldCollectOrder.DiscountPrice - productSaleShouldCollectOrder.ReducePrice
	o.Weight = productSaleShouldCollectOrder.Weight
	o.Roll = productSaleShouldCollectOrder.Roll
	o.TotalSaleMoney = productSaleShouldCollectOrder.OriginPrice
	o.TaxRate = productSaleShouldCollectOrder.TaxRate
	o.IsWithTaxRate = productSaleShouldCollectOrder.IsWithTaxRate
	for _, productSaleShouldCollectOrderDetail := range productSaleShouldCollectOrderDetails {
		o.MeasurementUnitName = measurementUnitName[productSaleShouldCollectOrderDetail.MeasurementUnitId]
		if productSaleShouldCollectOrderDetail.MergeOrderId != 0 {
			mergeOrder := mergeOrders.Pick(productSaleShouldCollectOrderDetail.MergeOrderId)
			fpmArrangeOrder = fpmArrangeOrders.PickByOrderID(mergeOrder.SrcId)
		} else {
			fpmArrangeOrder = fpmArrangeOrders.PickByOrderID(productSaleShouldCollectOrder.SrcId)
		}
		arrangeOrderDetail := fpmArrangeOrder.ItemData.PickByID(productSaleShouldCollectOrderDetail.SrcDetailId)
		item := structure.GetProductSaleShouldCollectOrderDetailData{}
		item.SaleOrderID = fpmArrangeOrder.SrcId
		item.SaleOrderNo = fpmArrangeOrder.SrcOrderNo
		item.ArrangeOrderID = fpmArrangeOrder.Id
		item.ArrangeOrderNo = fpmArrangeOrder.OrderNo
		item.CustomerId = arrangeOrderDetail.CustomerId
		// 优先使用arrangeOrderDetail的值，如果为空则使用productSaleShouldCollectOrderDetail的值
		if arrangeOrderDetail.ProductRemark != "" {
			item.ProductRemark = arrangeOrderDetail.ProductRemark
		} else {
			item.ProductRemark = productSaleShouldCollectOrderDetail.ProductRemark
		}
		if arrangeOrderDetail.ProductCraft != "" {
			item.ProductCraft = arrangeOrderDetail.ProductCraft
		} else {
			item.ProductCraft = productSaleShouldCollectOrderDetail.ProductCraft
		}
		if arrangeOrderDetail.ProductLevelId != 0 {
			item.ProductLevelId = arrangeOrderDetail.ProductLevelId
			item.ProductLevelName = productLevel[arrangeOrderDetail.ProductLevelId]
		} else {
			item.ProductLevelId = productSaleShouldCollectOrderDetail.ProductLevelId
			item.ProductLevelName = productLevel[productSaleShouldCollectOrderDetail.ProductLevelId]
		}
		if arrangeOrderDetail.ProductIngredient != "" {
			item.ProductIngredient = arrangeOrderDetail.ProductIngredient
		} else {
			item.ProductIngredient = productSaleShouldCollectOrderDetail.ProductIngredient
		}
		if o.SrcOrderType == common_collect.SrcOrderTypeProductSaleOut {
			item.WarehouseId = fpmSaleOutOrder.WarehouseId
			item.WarehouseName = warehouseName[fpmSaleOutOrder.WarehouseId]
			item.FpmSaleOutOrderNo = fpmSaleOutOrder.OrderNo
			item.FpmSaleOutOrderID = fpmSaleOutOrder.Id
			fpmSaleOutOrderDetail := fpmSaleOutOrder.PickDetailById(productSaleShouldCollectOrderDetail.SrcDetailId)
			item.CustomerCode = bizUnit[fpmSaleOutOrderDetail.CustomerId].CustomCode
			item.CustomerName = bizUnit[fpmSaleOutOrderDetail.CustomerId].Name
			item.CustomerFullName = bizUnit[fpmSaleOutOrderDetail.CustomerId].FullName
			item.WarehouseOutRemark = fpmSaleOutOrderDetail.Remark
		} else {
			item.WarehouseId = fpmArrangeOrder.WarehouseId
			item.WarehouseName = warehouseName[fpmArrangeOrder.WarehouseId]
			item.WarehouseOutRemark = arrangeOrderDetail.Remark

			// 首先检查配布单的srcid是否已经有对应的销售出仓单信息
			if saleOutOrder, exists := srcIdToSaleOutOrder[fpmArrangeOrder.Id]; exists && saleOutOrder.OrderNo != "" && saleOutOrder.Id != 0 {
				// 如果已经有该配布单srcid对应的有效销售出仓单信息，直接使用
				item.FpmSaleOutOrderNo = saleOutOrder.OrderNo
				item.FpmSaleOutOrderID = saleOutOrder.Id
			}
			item.CustomerCode = bizUnit[arrangeOrderDetail.CustomerId].CustomCode
			item.CustomerName = bizUnit[arrangeOrderDetail.CustomerId].Name
			item.CustomerFullName = bizUnit[arrangeOrderDetail.CustomerId].FullName
		}
		item.Id = productSaleShouldCollectOrderDetail.Id
		item.CreateTime = tools.MyTime(productSaleShouldCollectOrderDetail.CreateTime)
		item.UpdateTime = tools.MyTime(productSaleShouldCollectOrderDetail.UpdateTime)
		item.CreatorId = productSaleShouldCollectOrderDetail.CreatorId
		item.CreatorName = productSaleShouldCollectOrderDetail.CreatorName
		item.UpdaterId = productSaleShouldCollectOrderDetail.UpdaterId
		item.UpdateUserName = productSaleShouldCollectOrderDetail.UpdaterName
		item.ShouldCollectOrderId = productSaleShouldCollectOrderDetail.ShouldCollectOrderId
		item.SrcDetailId = productSaleShouldCollectOrderDetail.SrcDetailId
		item.CollectType = productSaleShouldCollectOrderDetail.CollectType
		item.CollectTypeName = productSaleShouldCollectOrderDetail.CollectType.String()
		item.Remark = productSaleShouldCollectOrderDetail.Remark
		// 如果上面两种情况都没有取到销售出仓单，则是属于销调流程未完成，暂时取送货单的来源单号

		// if item.FpmSaleOutOrderNo == "" {
		//	item.FpmSaleOutOrderNo = productSaleShouldCollectOrder.SrcOrderNo
		// }
		item.ProductId = productSaleShouldCollectOrderDetail.MaterialId
		if productItem, ok := productItems[productSaleShouldCollectOrderDetail.MaterialId]; ok {
			item.ProductCode = productItem.FinishProductCode
			item.ProductName = productItem.FinishProductName
		}

		item.ProductColorId = productSaleShouldCollectOrderDetail.ProductColorId
		item.ProductColorCode = productColorItem[productSaleShouldCollectOrderDetail.ProductColorId][0]
		item.ProductColorName = productColorItem[productSaleShouldCollectOrderDetail.ProductColorId][1]
		item.DyelotNumber = productSaleShouldCollectOrderDetail.DyelotNumber
		item.MeasurementUnitId = productSaleShouldCollectOrderDetail.MeasurementUnitId
		item.MeasurementUnitName = measurementUnitName[productSaleShouldCollectOrderDetail.MeasurementUnitId]
		item.Roll = productSaleShouldCollectOrderDetail.Roll
		item.Weight = productSaleShouldCollectOrderDetail.Weight
		item.StandardWeightError = productSaleShouldCollectOrderDetail.StandardWeightError
		item.OffsetWeightError = productSaleShouldCollectOrderDetail.OffsetWeightError
		item.AdjustWeightError = productSaleShouldCollectOrderDetail.AdjustWeightError
		item.WeightError = productSaleShouldCollectOrderDetail.WeightError
		item.ActuallyWeight = productSaleShouldCollectOrderDetail.ActuallyWeight
		item.SettleErrorWeight = productSaleShouldCollectOrderDetail.SettleErrorWeight
		item.SettleWeight = productSaleShouldCollectOrderDetail.SettleWeight
		item.Length = productSaleShouldCollectOrderDetail.Length
		item.StandardSalePrice = productSaleShouldCollectOrderDetail.StandardSalePrice
		item.SaleLevelId = productSaleShouldCollectOrderDetail.SaleLevelId
		item.SaleLevelName = saleLevel[productSaleShouldCollectOrderDetail.SaleLevelId]
		item.OffsetSalePrice = productSaleShouldCollectOrderDetail.OffsetSalePrice
		item.SalePrice = productSaleShouldCollectOrderDetail.SalePrice
		item.StandardLengthCutSalePrice = productSaleShouldCollectOrderDetail.StandardLengthCutSalePrice
		item.OffsetLengthCutSalePrice = productSaleShouldCollectOrderDetail.OffsetLengthCutSalePrice
		item.LengthCutSalePrice = productSaleShouldCollectOrderDetail.LengthCutSalePrice
		item.SaleTaxRate = productSaleShouldCollectOrderDetail.SaleTaxRate
		item.OtherPrice = productSaleShouldCollectOrderDetail.OtherPrice
		item.SettlePrice = productSaleShouldCollectOrderDetail.SettlePrice
		item.ReturnRoll = productSaleShouldCollectOrderDetail.ReturnRoll
		item.ReturnLength = productSaleShouldCollectOrderDetail.ReturnLength
		item.ReturnWeight = productSaleShouldCollectOrderDetail.ReturnWeight
		item.SumStockId = productSaleShouldCollectOrderDetail.SumStockId
		item.AuxiliaryUnitId = productSaleShouldCollectOrderDetail.AuxiliaryUnitId
		if item.AuxiliaryUnitId == 0 {
			item.AuxiliaryUnitId = productSaleShouldCollectOrderDetail.MeasurementUnitId
		}
		item.AuxiliaryUnitName = measurementUnitName[item.AuxiliaryUnitId]
		if req.WithAllWeight {
			var fcDataList = make(structure.GetProductSaleArrangeOrderItemFcDataList, 0)
			// 如果应收单的是由销售出仓单下推的
			if productSaleShouldCollectOrder.SrcOrderType == common_collect.SrcOrderTypeProductSaleOut {
				_fpmSaleOutOrderItemFcResList := fpmSaleOutOrderItemFcResList.PickByParentID(productSaleShouldCollectOrderDetail.SrcDetailId)
				for _, fpmSaleOutOrderItemFcRes := range _fpmSaleOutOrderItemFcResList {
					shouldCollectOrderDetailFc := shouldCollectOrderDetailFcList.PickBySrcDetailFcId(fpmSaleOutOrderItemFcRes.Id)
					fcData := structure.GetProductSaleArrangeOrderItemFcData{}
					fcData.Id = fpmSaleOutOrderItemFcRes.Id
					fcData.Roll = fpmSaleOutOrderItemFcRes.Roll
					fcData.WarehouseBinId = fpmSaleOutOrderItemFcRes.WarehouseBinId
					fcData.VolumeNumber = fpmSaleOutOrderItemFcRes.VolumeNumber
					fcData.WarehouseOutType = int(fpmSaleOutOrderItemFcRes.WarehouseOutType)
					fcData.WarehouseOutOrderId = fpmSaleOutOrderItemFcRes.WarehouseOutOrderId
					fcData.WarehouseOutOrderNo = fpmSaleOutOrderItemFcRes.WarehouseOutOrderNo
					fcData.WarehouseInType = int(fpmSaleOutOrderItemFcRes.WarehouseInType)
					fcData.WarehouseInOrderId = fpmSaleOutOrderItemFcRes.WarehouseInOrderId
					fcData.WarehouseInOrderNo = fpmSaleOutOrderItemFcRes.WarehouseInOrderNo
					fcData.ArrangeOrderNo = fpmSaleOutOrderItemFcRes.ArrangeOrderNo
					fcData.StockId = fpmSaleOutOrderItemFcRes.StockId
					fcData.SumStockId = fpmSaleOutOrderItemFcRes.SumStockId
					fcData.BaseUnitWeight = fpmSaleOutOrderItemFcRes.BaseUnitWeight
					fcData.PaperTubeWeight = fpmSaleOutOrderItemFcRes.PaperTubeWeight
					fcData.WeightError = fpmSaleOutOrderItemFcRes.WeightError
					fcData.UnitId = fpmSaleOutOrderItemFcRes.UnitId
					fcData.Length = fpmSaleOutOrderItemFcRes.Length
					fcData.SettleWeight = fpmSaleOutOrderItemFcRes.SettleWeight
					fcData.DigitalCode = fpmSaleOutOrderItemFcRes.DigitalCode
					fcData.ShelfNo = fpmSaleOutOrderItemFcRes.ShelfNo
					fcData.ContractNumber = fpmSaleOutOrderItemFcRes.ContractNumber
					fcData.CustomerPoNum = fpmSaleOutOrderItemFcRes.CustomerPoNum
					fcData.AccountNum = fpmSaleOutOrderItemFcRes.AccountNum
					fcData.DyeFactoryColorCode = fpmSaleOutOrderItemFcRes.DyeFactoryColorCode
					fcData.DyeFactoryDyelotNumber = fpmSaleOutOrderItemFcRes.DyeFactoryDyelotNumber
					fcData.ProductWidth = fpmSaleOutOrderItemFcRes.ProductWidth
					fcData.ProductGramWeight = fpmSaleOutOrderItemFcRes.ProductGramWeight
					fcData.StockRemark = fpmSaleOutOrderItemFcRes.StockRemark
					fcData.Remark = fpmSaleOutOrderItemFcRes.Remark
					fcData.ScanUserId = fpmSaleOutOrderItemFcRes.ScanUserId
					fcData.ScanUserName = fpmSaleOutOrderItemFcRes.ScanUserName
					fcData.ScanTime = tools.MyTime(fpmSaleOutOrderItemFcRes.ScanTime)
					fcData.WarehouseId = fpmSaleOutOrderItemFcRes.WarehouseId
					fcData.ActuallyWeight = fpmSaleOutOrderItemFcRes.ActuallyWeight
					fcData.SettleErrorWeight = fpmSaleOutOrderItemFcRes.SettleErrorWeight
					fcData.ProductColorName = item.ProductColorName
					fcData.ProductColorCode = item.ProductColorCode
					// 如果应收单细码信息不为空
					if shouldCollectOrderDetailFc.Id != 0 {
						fcData.OrderDetailFcId = shouldCollectOrderDetailFc.Id                  // Id
						fcData.WeightError = shouldCollectOrderDetailFc.WeightError             // 码单空差
						fcData.SettleWeight = shouldCollectOrderDetailFc.SettleWeight           // 结算数量
						fcData.ActuallyWeight = shouldCollectOrderDetailFc.ActuallyWeight       // 码单数量
						fcData.SettleErrorWeight = shouldCollectOrderDetailFc.SettleErrorWeight // 结算空差
					}
					// 转义
					fcData.WarehouseBinName = fpmSaleOutOrderItemFcRes.WarehouseBinName

					fcData.MeasurementUnitName = fpmSaleOutOrderItemFcRes.UnitName
					fcData.UnitName = fpmSaleOutOrderItemFcRes.UnitName
					fcDataList = append(fcDataList, fcData)
				}

			} else {
				_arrangeOrderItemFcResList := arrangeOrderItemFcResList.PickByParentID(productSaleShouldCollectOrderDetail.SrcDetailId)
				for _, arrangeOrderItemFcRes := range _arrangeOrderItemFcResList {
					if arrangeOrderItemFcRes.DyeFactoryDyelotNumber != productSaleShouldCollectOrderDetail.DyelotNumber {
						continue
					}
					shouldCollectOrderDetailFc := shouldCollectOrderDetailFcList.PickBySrcDetailFcId(arrangeOrderItemFcRes.Id)
					fcData := structure.GetProductSaleArrangeOrderItemFcData{}
					fcData.Id = arrangeOrderItemFcRes.Id
					fcData.Roll = arrangeOrderItemFcRes.Roll
					fcData.WarehouseBinId = arrangeOrderItemFcRes.WarehouseBinId
					fcData.VolumeNumber = arrangeOrderItemFcRes.VolumeNumber
					fcData.WarehouseOutType = int(arrangeOrderItemFcRes.WarehouseOutType)
					fcData.WarehouseOutOrderId = arrangeOrderItemFcRes.WarehouseOutOrderId
					fcData.WarehouseOutOrderNo = arrangeOrderItemFcRes.WarehouseOutOrderNo
					fcData.WarehouseInType = int(arrangeOrderItemFcRes.WarehouseInType)
					fcData.WarehouseInOrderId = arrangeOrderItemFcRes.WarehouseInOrderId
					fcData.WarehouseInOrderNo = arrangeOrderItemFcRes.WarehouseInOrderNo
					fcData.ArrangeOrderNo = arrangeOrderItemFcRes.ArrangeOrderNo
					fcData.StockId = arrangeOrderItemFcRes.StockId
					fcData.SumStockId = arrangeOrderItemFcRes.SumStockId
					fcData.BaseUnitWeight = arrangeOrderItemFcRes.BaseUnitWeight
					fcData.PaperTubeWeight = arrangeOrderItemFcRes.PaperTubeWeight
					fcData.WeightError = arrangeOrderItemFcRes.WeightError
					fcData.UnitId = arrangeOrderItemFcRes.UnitId
					fcData.Length = arrangeOrderItemFcRes.Length
					fcData.SettleWeight = arrangeOrderItemFcRes.SettleWeight
					fcData.DigitalCode = arrangeOrderItemFcRes.DigitalCode
					fcData.ShelfNo = arrangeOrderItemFcRes.ShelfNo
					fcData.ContractNumber = arrangeOrderItemFcRes.ContractNumber
					fcData.CustomerPoNum = arrangeOrderItemFcRes.CustomerPoNum
					fcData.AccountNum = arrangeOrderItemFcRes.AccountNum
					fcData.DyeFactoryColorCode = arrangeOrderItemFcRes.DyeFactoryColorCode
					fcData.DyeFactoryDyelotNumber = arrangeOrderItemFcRes.DyeFactoryDyelotNumber
					fcData.ProductWidth = arrangeOrderItemFcRes.ProductWidth
					fcData.ProductGramWeight = arrangeOrderItemFcRes.ProductGramWeight
					fcData.StockRemark = arrangeOrderItemFcRes.StockRemark
					fcData.Remark = arrangeOrderItemFcRes.Remark
					fcData.ScanUserId = arrangeOrderItemFcRes.ScanUserId
					fcData.ScanUserName = arrangeOrderItemFcRes.ScanUserName
					fcData.ScanTime = tools.MyTime(arrangeOrderItemFcRes.ScanTime)
					fcData.WarehouseId = arrangeOrderItemFcRes.WarehouseId
					fcData.ActuallyWeight = arrangeOrderItemFcRes.ActuallyWeight
					fcData.SettleErrorWeight = arrangeOrderItemFcRes.SettleErrorWeight
					fcData.ProductColorName = item.ProductColorName
					fcData.ProductColorCode = item.ProductColorCode
					// 如果应收单细码信息不为空
					if shouldCollectOrderDetailFc.Id != 0 {
						fcData.OrderDetailFcId = shouldCollectOrderDetailFc.Id                  // Id
						fcData.WeightError = shouldCollectOrderDetailFc.WeightError             // 码单空差
						fcData.SettleWeight = shouldCollectOrderDetailFc.SettleWeight           // 结算数量
						fcData.ActuallyWeight = shouldCollectOrderDetailFc.ActuallyWeight       // 码单数量
						fcData.SettleErrorWeight = shouldCollectOrderDetailFc.SettleErrorWeight // 结算空差
					}
					// 转义
					fcData.WarehouseBinName = arrangeOrderItemFcRes.WarehouseBinName
					fcData.MeasurementUnitName = arrangeOrderItemFcRes.UnitName
					fcData.UnitName = arrangeOrderItemFcRes.UnitName
					fcDataList = append(fcDataList, fcData)
				}
			}
			item.FcDataList = fcDataList
		}
		items = append(items, item)
	}

	// 结算方式计算日期
	{
		query := structure.CalcShouldCollectOrderDateQuery{
			SettleType:          o.SettleType,
			CustomCycle:         o.CustomCycle,
			OrderTime:           o.OrderTime,
			TotalUncollectMoney: o.TotalUncollectMoney,
		}
		resp := calcDate(query)
		o.PaymentDeadline = resp.PaymentDeadline
		o.LiquidatedDay = resp.LiquidatedDay
	}

	// 第二次遍历：更新所有没有找到销售出仓单信息的item
	// 按srcId分组，如果同一个srcId下有item找到了销售出仓单信息，则更新同组内所有item
	// 创建一个映射，记录每个srcId是否有item找到了销售出仓单信息
	srcIdHasValidOrder := make(map[uint64]bool)
	srcIdOrderInfo := make(map[uint64]struct {
		OrderNo string
		OrderID uint64
	})

	// 第一次遍历：收集有效的销售出仓单信息
	for _, item := range items {
		if item.FpmSaleOutOrderNo != "" && item.FpmSaleOutOrderID != 0 {
			srcIdHasValidOrder[item.SaleOrderID] = true
			srcIdOrderInfo[item.SaleOrderID] = struct {
				OrderNo string
				OrderID uint64
			}{item.FpmSaleOutOrderNo, item.FpmSaleOutOrderID}
		}
	}

	// 第二次遍历：更新没有找到销售出仓单信息的item
	for i := range items {
		if items[i].FpmSaleOutOrderNo == "" && items[i].FpmSaleOutOrderID == 0 {
			if info, exists := srcIdOrderInfo[items[i].SaleOrderID]; exists {
				items[i].FpmSaleOutOrderNo = info.OrderNo
				items[i].FpmSaleOutOrderID = info.OrderID
			}
		}
	}

	o.ItemData = items
	// 统计累计欠款
	list, count, err := model.FindCustomerOweMoneyList(r.tx, &structure.GetCustomerOweMoneyListQuery{
		CustomerId: productSaleShouldCollectOrder.CustomerId}, false)
	if err != nil {
		return
	}
	if count != 0 {
		customerOweMoney := list.List()[0]
		o.TotalArrearsAmount = customerOweMoney.BalancePrice - customerOweMoney.BalanceAdvancePrice
	}
	data = o
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetWeightItemList(ctx context.Context, req *structure.GetProductSaleShouldCollectOrderQuery) (list structure.GetProductSaleArrangeOrderItemFcDataList, total int, err error) {
	var (
		_                                   bool
		productSaleShouldCollectOrder       model.ShouldCollectOrder
		productSaleShouldCollectOrderDetail model.ShouldCollectOrderDetail
		weightItems                         = make(structure.GetProductSaleArrangeOrderItemFcDataList, 0)
		arrangeOrderItemFcResList           product.ArrangeOrderItemFcResList
		fpmSaleOutOrderItemFcResList        product.FpmSaleOutOrderItemFcResList
		stockIds                            = set.NewUint64Set()
		shouldCollectOrderDetailFcList      model.ShouldCollectOrderDetailFcList
	)
	if req.DetailID == 0 && req.Id == 0 {
		return weightItems, 0, nil
	}

	productSaleShouldCollectOrderDetail, _, err = mysql.FirstShouldCollectOrderDetailByID(r.tx, req.DetailID)
	if err != nil {
		return
	}

	productSaleShouldCollectOrder, _, _ = mysql.FirstShouldCollectOrderByID(r.tx, productSaleShouldCollectOrderDetail.GetShouldCollectOrderId())
	// 分情况进行赋值（配布单/成品销售出仓单）
	if productSaleShouldCollectOrder.SrcOrderType == common_collect.SrcOrderTypeProductSaleOut {
		if req.WithAllWeight {
			var (
				productSaleShouldCollectOrderDetails model.ShouldCollectOrderDetailList
			)
			productSaleShouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByParentID(r.tx, req.DetailID)
			if err != nil {
				return
			}

			srcDetailIds := productSaleShouldCollectOrderDetails.GetSrcDetailIds()

			fpmSaleOutOrderSvc := product.NewSaleOutOrderClient()
			fpmSaleOutOrderItemFcResList, err = fpmSaleOutOrderSvc.GetSaleOutOrderItemFcBySaleOutOrderDetailID(ctx, product.SaleOutOrderReq{IDs: srcDetailIds})
			if err != nil {
				return
			}

			shouldCollectOrderDetailFcList, err = mysql.FindShouldCollectOrderDetailFcByShouldCollectOrderID(r.tx, req.Id)
			if err != nil {
				return
			}
		} else {
			productSaleShouldCollectOrderDetail, _, err = mysql.FirstShouldCollectOrderDetailByID(r.tx, req.DetailID)
			if err != nil {
				return
			}

			fpmSaleOutOrderSvc := product.NewSaleOutOrderClient()
			fpmSaleOutOrderItemFcResList, err = fpmSaleOutOrderSvc.GetSaleOutOrderItemFcBySaleOutOrderDetailID(ctx, product.SaleOutOrderReq{Id: productSaleShouldCollectOrderDetail.SrcDetailId})
			if err != nil {
				return
			}

			shouldCollectOrderDetailFcList, err = mysql.FindShouldCollectOrderDetailFcByParentID(r.tx, req.DetailID)
			if err != nil {
				return
			}
		}
		for _, res := range fpmSaleOutOrderItemFcResList {
			stockIds.Add(res.StockId)
		}
		stockList, _ := product.NewProductStockClient().GetStockDetailByIds(ctx, stockIds.List())

		for _, fpmSaleOutOrderItemFcRes := range fpmSaleOutOrderItemFcResList {
			if fpmSaleOutOrderItemFcRes.DyeFactoryDyelotNumber != productSaleShouldCollectOrderDetail.DyelotNumber {
				continue
			}
			shouldCollectOrderDetailFc := shouldCollectOrderDetailFcList.PickBySrcDetailFcId(fpmSaleOutOrderItemFcRes.Id)
			weightItem := structure.GetProductSaleArrangeOrderItemFcData{}
			weightItem.Id = fpmSaleOutOrderItemFcRes.Id
			weightItem.Roll = fpmSaleOutOrderItemFcRes.Roll
			weightItem.WarehouseBinId = fpmSaleOutOrderItemFcRes.WarehouseBinId
			weightItem.VolumeNumber = fpmSaleOutOrderItemFcRes.VolumeNumber
			weightItem.WarehouseOutType = int(fpmSaleOutOrderItemFcRes.WarehouseOutType)
			weightItem.WarehouseOutOrderId = fpmSaleOutOrderItemFcRes.WarehouseOutOrderId
			weightItem.WarehouseOutOrderNo = fpmSaleOutOrderItemFcRes.WarehouseOutOrderNo
			weightItem.WarehouseInType = int(fpmSaleOutOrderItemFcRes.WarehouseInType)
			weightItem.WarehouseInOrderId = fpmSaleOutOrderItemFcRes.WarehouseInOrderId
			weightItem.WarehouseInOrderNo = fpmSaleOutOrderItemFcRes.WarehouseInOrderNo
			weightItem.ArrangeOrderNo = fpmSaleOutOrderItemFcRes.ArrangeOrderNo
			weightItem.StockId = fpmSaleOutOrderItemFcRes.StockId
			weightItem.SumStockId = fpmSaleOutOrderItemFcRes.SumStockId
			weightItem.BaseUnitWeight = fpmSaleOutOrderItemFcRes.BaseUnitWeight
			weightItem.PaperTubeWeight = fpmSaleOutOrderItemFcRes.PaperTubeWeight
			weightItem.WeightError = fpmSaleOutOrderItemFcRes.WeightError
			weightItem.UnitId = fpmSaleOutOrderItemFcRes.UnitId
			weightItem.Length = fpmSaleOutOrderItemFcRes.Length
			weightItem.SettleWeight = fpmSaleOutOrderItemFcRes.SettleWeight
			weightItem.DigitalCode = fpmSaleOutOrderItemFcRes.DigitalCode
			weightItem.ShelfNo = fpmSaleOutOrderItemFcRes.ShelfNo
			weightItem.ContractNumber = fpmSaleOutOrderItemFcRes.ContractNumber
			weightItem.CustomerPoNum = fpmSaleOutOrderItemFcRes.CustomerPoNum
			weightItem.AccountNum = fpmSaleOutOrderItemFcRes.AccountNum
			weightItem.DyeFactoryColorCode = fpmSaleOutOrderItemFcRes.DyeFactoryColorCode
			weightItem.DyeFactoryDyelotNumber = fpmSaleOutOrderItemFcRes.DyeFactoryDyelotNumber
			// weightItem.ProductWidth = fpmSaleOutOrderItemFcRes.ProductWidth
			// weightItem.ProductGramWeight = fpmSaleOutOrderItemFcRes.ProductGramWeight
			weightItem.StockRemark = fpmSaleOutOrderItemFcRes.StockRemark
			weightItem.Remark = fpmSaleOutOrderItemFcRes.Remark
			weightItem.ScanUserId = fpmSaleOutOrderItemFcRes.ScanUserId
			weightItem.ScanUserName = fpmSaleOutOrderItemFcRes.ScanUserName
			weightItem.ScanTime = tools.MyTime(fpmSaleOutOrderItemFcRes.ScanTime)
			weightItem.WarehouseId = fpmSaleOutOrderItemFcRes.WarehouseId
			weightItem.ActuallyWeight = fpmSaleOutOrderItemFcRes.ActuallyWeight
			weightItem.SettleErrorWeight = fpmSaleOutOrderItemFcRes.SettleErrorWeight

			// 如果应收单细码信息不为空
			if shouldCollectOrderDetailFc.Id != 0 {
				weightItem.OrderDetailFcId = shouldCollectOrderDetailFc.Id                  // 码单空差
				weightItem.WeightError = shouldCollectOrderDetailFc.WeightError             // 码单空差
				weightItem.SettleWeight = shouldCollectOrderDetailFc.SettleWeight           // 结算数量
				weightItem.ActuallyWeight = shouldCollectOrderDetailFc.ActuallyWeight       // 码单数量
				weightItem.SettleErrorWeight = shouldCollectOrderDetailFc.SettleErrorWeight // 结算空差
			}

			if v, ok := stockList[fpmSaleOutOrderItemFcRes.StockId]; ok {
				weightItem.BarCode = v.BarCode
				weightItem.BleachName = v.BleachName
				weightItem.Density = v.Density
				weightItem.DyelotNumber = v.DyelotNumber
				weightItem.DyeFactoryDyelotNumber = v.DyelotNumber
				weightItem.FinishProductCraft = v.FinishProductCraft
				weightItem.FinishProductWidthAndWightUnit = v.FinishProductWidthAndWightUnit
				weightItem.PrintDate = tools.MyTime(time.Now())
				weightItem.ProductKindName = v.ProductKindName
				weightItem.QRCode = v.QrCode
				weightItem.WeavingOrganizationName = v.WeavingOrganizationName
				weightItem.YarnCount = v.YarnCount
				weightItem.ProductColorName = v.ProductColorName
				weightItem.ProductColorCode = v.ProductColorCode
				weightItem.ProductName = v.ProductName
				weightItem.ProductCode = v.ProductCode
			}

			// 转义
			weightItem.WarehouseBinName = fpmSaleOutOrderItemFcRes.WarehouseBinName
			weightItem.MeasurementUnitName = fpmSaleOutOrderItemFcRes.UnitName
			weightItem.UnitName = fpmSaleOutOrderItemFcRes.UnitName
			weightItems = append(weightItems, weightItem)
		}
	} else {
		if req.WithAllWeight {
			var (
				productSaleShouldCollectOrderDetails model.ShouldCollectOrderDetailList
			)
			productSaleShouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByParentID(r.tx, req.Id)
			if err != nil {
				return
			}

			srcDetailIds := productSaleShouldCollectOrderDetails.GetSrcDetailIds()

			fpmArrangeOrderSvc := product.NewArrangeOrderClient()
			arrangeOrderItemFcResList, err = fpmArrangeOrderSvc.GetArrangeOrderWeightItemListByItemIds(ctx, product.ArrangeOrderReq{IDs: srcDetailIds})
			if err != nil {
				return
			}

			shouldCollectOrderDetailFcList, err = mysql.FindShouldCollectOrderDetailFcByShouldCollectOrderID(r.tx, req.Id)
			if err != nil {
				return
			}
		} else {
			productSaleShouldCollectOrderDetail, err = mysql.MustFirstShouldCollectOrderDetailByID(r.tx, req.DetailID)
			if err != nil {
				return
			}

			fpmArrangeOrderSvc := product.NewArrangeOrderClient()
			arrangeOrderItemFcResList, err = fpmArrangeOrderSvc.GetArrangeOrderWeightItemListByItemId(ctx, product.ArrangeOrderReq{Id: productSaleShouldCollectOrderDetail.SrcDetailId})
			if err != nil {
				return
			}

			shouldCollectOrderDetailFcList, err = mysql.FindShouldCollectOrderDetailFcByParentID(r.tx, req.DetailID)
			if err != nil {
				return
			}
		}

		for _, res := range arrangeOrderItemFcResList {
			stockIds.Add(res.StockId)
		}

		stockList, _ := product.NewProductStockClient().GetStockDetailByIds(ctx, stockIds.List())

		for _, arrangeOrderItemFcRes := range arrangeOrderItemFcResList {
			if arrangeOrderItemFcRes.DyeFactoryDyelotNumber != productSaleShouldCollectOrderDetail.DyelotNumber {
				continue
			}
			shouldCollectOrderDetailFc := shouldCollectOrderDetailFcList.PickBySrcDetailFcId(arrangeOrderItemFcRes.Id)
			weightItem := structure.GetProductSaleArrangeOrderItemFcData{}
			weightItem.Id = arrangeOrderItemFcRes.Id
			weightItem.Roll = arrangeOrderItemFcRes.Roll
			weightItem.WarehouseBinId = arrangeOrderItemFcRes.WarehouseBinId
			weightItem.VolumeNumber = arrangeOrderItemFcRes.VolumeNumber
			weightItem.WarehouseOutType = int(arrangeOrderItemFcRes.WarehouseOutType)
			weightItem.WarehouseOutOrderId = arrangeOrderItemFcRes.WarehouseOutOrderId
			weightItem.WarehouseOutOrderNo = arrangeOrderItemFcRes.WarehouseOutOrderNo
			weightItem.WarehouseInType = int(arrangeOrderItemFcRes.WarehouseInType)
			weightItem.WarehouseInOrderId = arrangeOrderItemFcRes.WarehouseInOrderId
			weightItem.WarehouseInOrderNo = arrangeOrderItemFcRes.WarehouseInOrderNo
			weightItem.ArrangeOrderNo = arrangeOrderItemFcRes.ArrangeOrderNo
			weightItem.StockId = arrangeOrderItemFcRes.StockId
			weightItem.SumStockId = arrangeOrderItemFcRes.SumStockId
			weightItem.BaseUnitWeight = arrangeOrderItemFcRes.BaseUnitWeight
			weightItem.PaperTubeWeight = arrangeOrderItemFcRes.PaperTubeWeight
			weightItem.WeightError = arrangeOrderItemFcRes.WeightError
			weightItem.UnitId = arrangeOrderItemFcRes.UnitId
			weightItem.Length = arrangeOrderItemFcRes.Length
			weightItem.SettleWeight = arrangeOrderItemFcRes.SettleWeight
			weightItem.DigitalCode = arrangeOrderItemFcRes.DigitalCode
			weightItem.ShelfNo = arrangeOrderItemFcRes.ShelfNo
			weightItem.ContractNumber = arrangeOrderItemFcRes.ContractNumber
			weightItem.CustomerPoNum = arrangeOrderItemFcRes.CustomerPoNum
			weightItem.AccountNum = arrangeOrderItemFcRes.AccountNum
			weightItem.DyeFactoryColorCode = arrangeOrderItemFcRes.DyeFactoryColorCode
			weightItem.DyeFactoryDyelotNumber = arrangeOrderItemFcRes.DyeFactoryDyelotNumber
			// weightItem.ProductWidth = arrangeOrderItemFcRes.ProductWidth
			// weightItem.ProductGramWeight = arrangeOrderItemFcRes.ProductGramWeight
			weightItem.StockRemark = arrangeOrderItemFcRes.StockRemark
			weightItem.Remark = arrangeOrderItemFcRes.Remark
			weightItem.ScanUserId = arrangeOrderItemFcRes.ScanUserId
			weightItem.ScanUserName = arrangeOrderItemFcRes.ScanUserName
			weightItem.ScanTime = tools.MyTime(arrangeOrderItemFcRes.ScanTime)
			weightItem.WarehouseId = arrangeOrderItemFcRes.WarehouseId
			weightItem.ActuallyWeight = arrangeOrderItemFcRes.ActuallyWeight
			weightItem.SettleErrorWeight = arrangeOrderItemFcRes.SettleErrorWeight

			// 如果应收单细码信息不为空
			if shouldCollectOrderDetailFc.Id != 0 {
				weightItem.OrderDetailFcId = shouldCollectOrderDetailFc.Id                  // 码单空差
				weightItem.WeightError = shouldCollectOrderDetailFc.WeightError             // 码单空差
				weightItem.SettleWeight = shouldCollectOrderDetailFc.SettleWeight           // 结算数量
				weightItem.ActuallyWeight = shouldCollectOrderDetailFc.ActuallyWeight       // 码单数量
				weightItem.SettleErrorWeight = shouldCollectOrderDetailFc.SettleErrorWeight // 结算空差
			}

			if v, ok := stockList[arrangeOrderItemFcRes.StockId]; ok {
				weightItem.BarCode = v.BarCode
				weightItem.BleachName = v.BleachName
				weightItem.Density = v.Density
				weightItem.DyelotNumber = v.DyelotNumber
				weightItem.DyeFactoryDyelotNumber = v.DyelotNumber
				weightItem.FinishProductCraft = v.FinishProductCraft
				weightItem.FinishProductWidthAndWightUnit = v.FinishProductWidthAndWightUnit
				weightItem.PrintDate = tools.MyTime(time.Now())
				weightItem.ProductKindName = v.ProductKindName
				weightItem.QRCode = v.QrCode
				weightItem.WeavingOrganizationName = v.WeavingOrganizationName
				weightItem.YarnCount = v.YarnCount
				weightItem.ProductColorName = v.ProductColorName
				weightItem.ProductColorCode = v.ProductColorCode
				weightItem.ProductName = v.ProductName
				weightItem.ProductCode = v.ProductCode
			}

			// 转义
			weightItem.WarehouseBinName = arrangeOrderItemFcRes.WarehouseBinName
			weightItem.MeasurementUnitName = arrangeOrderItemFcRes.UnitName
			weightItem.UnitName = arrangeOrderItemFcRes.UnitName
			weightItems = append(weightItems, weightItem)
		}
	}
	list = weightItems
	total = len(list)
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetList(ctx context.Context, req *structure.GetProductSaleShouldCollectOrderListQuery) (list structure.GetProductSaleShouldCollectOrderListDataList, total int, err error) {
	var (
		productSaleShouldCollectOrders model.ShouldCollectOrderList
		fpmArrangeOrderSvc             = product.NewArrangeOrderClient()
	)

	if req.SaleOrderNo != "" {
		req.FpmArrangeOrderIds, err = fpmArrangeOrderSvc.GetArrangeOrderIdsBySrcOrderNo(ctx, req.SaleOrderNo)
		if err != nil {
			return
		}
	}

	productSaleShouldCollectOrders, total, err = mysql.SearchProductSaleShouldCollectOrder(r.tx, req)
	if err != nil {
		return
	}

	srcIDs := productSaleShouldCollectOrders.GetSrcIds()
	fpmArrangeOrders, err := fpmArrangeOrderSvc.GetArrangeOrderList(ctx, product.ArrangeOrderReq{IDs: srcIDs})
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64List(fpmArrangeOrders, "process_factory_id")
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrders.List() {
		fpmArrangeOrder := fpmArrangeOrders.PickByOrderID(productSaleShouldCollectOrder.SrcId)
		o := structure.GetProductSaleShouldCollectOrderListData{}
		o.Id = productSaleShouldCollectOrder.Id
		o.CreateTime = tools.MyTime(productSaleShouldCollectOrder.CreateTime)
		o.UpdateTime = tools.MyTime(productSaleShouldCollectOrder.UpdateTime)
		o.CreatorId = productSaleShouldCollectOrder.CreatorId
		o.CreatorName = productSaleShouldCollectOrder.CreatorName
		o.UpdaterId = productSaleShouldCollectOrder.UpdaterId
		o.UpdateUserName = productSaleShouldCollectOrder.UpdaterName
		o.OrderNo = productSaleShouldCollectOrder.OrderNo
		o.CollectType = productSaleShouldCollectOrder.CollectType
		o.CollectTypeName = productSaleShouldCollectOrder.CollectType.String()
		o.SrcOrderType = productSaleShouldCollectOrder.SrcOrderType
		o.SrcOrderTypeName = productSaleShouldCollectOrder.SrcOrderType.String()
		o.SrcId = productSaleShouldCollectOrder.SrcId
		o.SrcOrderNo = productSaleShouldCollectOrder.SrcOrderNo
		o.SaleSystemId = productSaleShouldCollectOrder.SaleSystemId
		o.SaleSystemName = saleSystem[productSaleShouldCollectOrder.SaleSystemId]
		o.CustomerId = productSaleShouldCollectOrder.CustomerId
		o.CustomerCode = bizUnit[productSaleShouldCollectOrder.CustomerId][0]
		o.CustomerName = bizUnit[productSaleShouldCollectOrder.CustomerId][1]
		o.InternalRemark = fpmArrangeOrder.InternalRemark
		o.SendProductRemark = fpmArrangeOrder.SendProductRemark
		o.OrderRemark = productSaleShouldCollectOrder.OrderRemark
		o.CancelRemark = productSaleShouldCollectOrder.CancelRemark
		o.AuditStatus = productSaleShouldCollectOrder.AuditStatus
		o.AuditStatusName = productSaleShouldCollectOrder.AuditStatus.String()
		o.AuditorId = productSaleShouldCollectOrder.AuditorId
		o.AuditorName = user[productSaleShouldCollectOrder.AuditorId]
		o.AuditDate = tools.MyTime(productSaleShouldCollectOrder.AuditDate)
		o.BusinessClose = productSaleShouldCollectOrder.BusinessClose
		o.BusinessCloseName = productSaleShouldCollectOrder.BusinessClose.String()
		o.BusinessCloseUserId = productSaleShouldCollectOrder.BusinessCloseUserId
		o.BusinessCloseUserName = user[productSaleShouldCollectOrder.BusinessCloseUserId]
		o.BusinessCloseTime = tools.MyTime(productSaleShouldCollectOrder.BusinessCloseTime)
		o.OrderTime = tools.MyTime(productSaleShouldCollectOrder.OrderTime)
		o.SaleUserId = productSaleShouldCollectOrder.SaleUserId
		o.SaleUserName = employeeName[productSaleShouldCollectOrder.SaleUserId]
		o.SaleFollowerId = productSaleShouldCollectOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[productSaleShouldCollectOrder.SaleFollowerId]
		o.SettleType = productSaleShouldCollectOrder.SettleType
		o.SettleTypeName = productSaleShouldCollectOrder.SettleType.String()
		o.CustomCycle = productSaleShouldCollectOrder.CustomCycle
		o.CollectStatus = productSaleShouldCollectOrder.CollectStatus
		o.CollectStatusName = productSaleShouldCollectOrder.CollectStatus.String()
		o.VoucherNumber = productSaleShouldCollectOrder.VoucherNumber
		o.WarehouseOutRemark = fpmArrangeOrder.SendProductRemark
		o.ProcessFactoryId = fpmArrangeOrder.ProcessFactoryId
		o.SaleMode = productSaleShouldCollectOrder.SaleMode
		o.SaleModeName = productSaleShouldCollectOrder.SaleMode.String()
		o.ProcessFactoryName = logisticsName[fpmArrangeOrder.ProcessFactoryId]
		o.Weight = tools.Milligram(productSaleShouldCollectOrder.Weight)
		o.Roll = tools.Hundred(productSaleShouldCollectOrder.Roll)
		o.TotalSaleMoney = tools.Cent(productSaleShouldCollectOrder.TotalSettleMoney)
		o.TotalShouldCollectMoney = tools.Cent(productSaleShouldCollectOrder.TotalSettleMoney - productSaleShouldCollectOrder.OffsetPrice - productSaleShouldCollectOrder.DiscountPrice - productSaleShouldCollectOrder.ReducePrice)
		list = append(list, o)
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetDropdownList(ctx context.Context, req *structure.GetProductSaleShouldCollectOrderListQuery) (list structure.GetProductSaleShouldCollectOrderDropdownDataList, total int, err error) {
	var (
		productSaleShouldCollectOrders model.ShouldCollectOrderList
	)
	productSaleShouldCollectOrders, total, err = mysql.SearchProductSaleShouldCollectOrderDropdown(r.tx, req)
	if err != nil {
		return
	}

	productSaleShouldCollectOrderIDs := productSaleShouldCollectOrders.GetSrcIds()
	fpmArrangeOrderSvc := product.NewArrangeOrderClient()
	fpmArrangeOrders, err := fpmArrangeOrderSvc.GetArrangeOrderList(ctx, product.ArrangeOrderReq{IDs: productSaleShouldCollectOrderIDs})
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productSaleShouldCollectOrders, fpmArrangeOrders)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64List(fpmArrangeOrders, "process_factory_id")
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrders.List() {
		fpmArrangeOrder := fpmArrangeOrders.PickByOrderID(productSaleShouldCollectOrder.SrcId)
		o := structure.GetProductSaleShouldCollectOrderDropdownData{}
		o.Id = productSaleShouldCollectOrder.Id
		o.CreateTime = tools.MyTime(productSaleShouldCollectOrder.CreateTime)
		o.UpdateTime = tools.MyTime(productSaleShouldCollectOrder.UpdateTime)
		o.CreatorId = productSaleShouldCollectOrder.CreatorId
		o.CreatorName = productSaleShouldCollectOrder.CreatorName
		o.UpdaterId = productSaleShouldCollectOrder.UpdaterId
		o.UpdateUserName = productSaleShouldCollectOrder.UpdaterName
		o.OrderNo = productSaleShouldCollectOrder.OrderNo
		o.CollectType = productSaleShouldCollectOrder.CollectType
		o.CollectTypeName = productSaleShouldCollectOrder.CollectType.String()
		o.SrcOrderType = productSaleShouldCollectOrder.SrcOrderType
		o.SrcOrderTypeName = productSaleShouldCollectOrder.SrcOrderType.String()
		o.SrcId = productSaleShouldCollectOrder.SrcId
		o.SrcOrderNo = productSaleShouldCollectOrder.SrcOrderNo
		o.SaleOrderID = fpmArrangeOrder.SrcId
		o.SaleOrderNo = fpmArrangeOrder.SrcOrderNo
		o.SaleSystemId = productSaleShouldCollectOrder.SaleSystemId
		o.SaleSystemName = saleSystem[productSaleShouldCollectOrder.SaleSystemId]
		o.CustomerId = productSaleShouldCollectOrder.CustomerId
		o.CustomerCode = bizUnit[productSaleShouldCollectOrder.CustomerId][0]
		o.CustomerName = bizUnit[productSaleShouldCollectOrder.CustomerId][1]
		o.OrderRemark = productSaleShouldCollectOrder.OrderRemark
		o.OrderTime = tools.MyTime(productSaleShouldCollectOrder.OrderTime)
		o.SaleUserId = productSaleShouldCollectOrder.SaleUserId
		o.SaleUserName = employeeName[productSaleShouldCollectOrder.SaleUserId]
		o.SaleFollowerId = productSaleShouldCollectOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[productSaleShouldCollectOrder.SaleFollowerId]
		o.SettleType = productSaleShouldCollectOrder.SettleType
		o.SettleTypeName = productSaleShouldCollectOrder.SettleType.String()
		o.CustomCycle = productSaleShouldCollectOrder.CustomCycle
		o.TotalSettleMoney = productSaleShouldCollectOrder.TotalSettleMoney
		o.CollectStatus = productSaleShouldCollectOrder.CollectStatus
		o.CollectStatusName = productSaleShouldCollectOrder.CollectStatus.String()
		o.ProcessFactoryId = fpmArrangeOrder.ProcessFactoryId
		o.ProcessFactoryName = logisticsName[fpmArrangeOrder.ProcessFactoryId]
		o.Contacts = fpmArrangeOrder.Contacts
		o.ReceiveAddr = fpmArrangeOrder.ReceiveAddr
		o.ReceivePhone = fpmArrangeOrder.ReceivePhone
		o.Weight = productSaleShouldCollectOrder.Weight
		o.Roll = productSaleShouldCollectOrder.Roll
		o.TotalSaleMoney = productSaleShouldCollectOrder.OriginPrice
		o.TotalShouldCollectMoney = productSaleShouldCollectOrder.TotalSettleMoney - productSaleShouldCollectOrder.OffsetPrice - productSaleShouldCollectOrder.DiscountPrice - productSaleShouldCollectOrder.ReducePrice

		list = append(list, o)
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetMergeOrderList(ctx context.Context, req *structure.GetProductSaleShouldCollectOrderQuery) (list structure.GetProductSaleShouldCollectOrderDropdownDataList, total int, err error) {
	var (
		productSaleShouldCollectOrders model.ShouldCollectOrderList
		shouldCollectOrderRels         model.ShouldCollectOrderRelList
	)
	shouldCollectOrderRels, err = mysql.FindShouldCollectOrderRelByShouldCollectOrderId(r.tx, req.Id)
	if err != nil {
		return
	}

	shouldCollectOrderIDs := shouldCollectOrderRels.GetMergeOrderIDs()
	productSaleShouldCollectOrders, err = mysql.FindShouldCollectOrderByIDs(r.tx, shouldCollectOrderIDs)
	if err != nil {
		return
	}

	fpmArrangeOrderSvc := product.NewArrangeOrderClient()
	fpmArrangeOrders, err := fpmArrangeOrderSvc.GetArrangeOrderList(ctx, product.ArrangeOrderReq{IDs: productSaleShouldCollectOrders.GetSrcIds()})
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productSaleShouldCollectOrders, fpmArrangeOrders)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64List(fpmArrangeOrders, "process_factory_id")
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrders.List() {
		fpmArrangeOrder := fpmArrangeOrders.PickByOrderID(productSaleShouldCollectOrder.SrcId)
		o := structure.GetProductSaleShouldCollectOrderDropdownData{}
		o.Id = productSaleShouldCollectOrder.Id
		o.CreateTime = tools.MyTime(productSaleShouldCollectOrder.CreateTime)
		o.UpdateTime = tools.MyTime(productSaleShouldCollectOrder.UpdateTime)
		o.CreatorId = productSaleShouldCollectOrder.CreatorId
		o.CreatorName = productSaleShouldCollectOrder.CreatorName
		o.UpdaterId = productSaleShouldCollectOrder.UpdaterId
		o.UpdateUserName = productSaleShouldCollectOrder.UpdaterName
		o.OrderNo = productSaleShouldCollectOrder.OrderNo
		o.CollectType = productSaleShouldCollectOrder.CollectType
		o.CollectTypeName = productSaleShouldCollectOrder.CollectType.String()
		o.SrcId = productSaleShouldCollectOrder.SrcId
		o.SrcOrderNo = productSaleShouldCollectOrder.SrcOrderNo
		o.SaleOrderID = fpmArrangeOrder.SrcId
		o.SaleOrderNo = fpmArrangeOrder.SrcOrderNo
		o.SaleSystemId = productSaleShouldCollectOrder.SaleSystemId
		o.SaleSystemName = saleSystem[productSaleShouldCollectOrder.SaleSystemId]
		o.CustomerId = productSaleShouldCollectOrder.CustomerId
		o.CustomerCode = bizUnit[productSaleShouldCollectOrder.CustomerId][0]
		o.CustomerName = bizUnit[productSaleShouldCollectOrder.CustomerId][1]
		o.OrderRemark = productSaleShouldCollectOrder.OrderRemark
		o.OrderTime = tools.MyTime(productSaleShouldCollectOrder.OrderTime)
		o.SaleUserId = productSaleShouldCollectOrder.SaleUserId
		o.SaleUserName = employeeName[productSaleShouldCollectOrder.SaleUserId]
		o.SaleFollowerId = productSaleShouldCollectOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[productSaleShouldCollectOrder.SaleFollowerId]
		o.SettleType = productSaleShouldCollectOrder.SettleType
		o.SettleTypeName = productSaleShouldCollectOrder.SettleType.String()
		o.CustomCycle = productSaleShouldCollectOrder.CustomCycle
		o.TotalSettleMoney = productSaleShouldCollectOrder.TotalSettleMoney
		o.CollectStatus = productSaleShouldCollectOrder.CollectStatus
		o.CollectStatusName = productSaleShouldCollectOrder.CollectStatus.String()
		o.ProcessFactoryId = fpmArrangeOrder.ProcessFactoryId
		o.ProcessFactoryName = logisticsName[fpmArrangeOrder.ProcessFactoryId]
		o.Contacts = fpmArrangeOrder.Contacts
		o.ReceiveAddr = fpmArrangeOrder.ReceiveAddr
		o.ReceivePhone = fpmArrangeOrder.ReceivePhone
		o.Weight = productSaleShouldCollectOrder.Weight
		o.Roll = productSaleShouldCollectOrder.Roll
		o.TotalSaleMoney = productSaleShouldCollectOrder.OriginPrice
		o.TotalShouldCollectMoney = productSaleShouldCollectOrder.TotalSettleMoney - productSaleShouldCollectOrder.OffsetPrice - productSaleShouldCollectOrder.DiscountPrice - productSaleShouldCollectOrder.ReducePrice
		list = append(list, o)
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetProductDropdownList(ctx context.Context, req *structure.GetProductSaleProductItemListQuery) (list structure.GetProductSaleProductItemDropdownDataList, total int, err error) {
	var (
		productSaleShouldCollectOrders       model.ShouldCollectOrderList
		productSaleShouldCollectOrderDetails model.ShouldCollectOrderDetailList
	)
	productSaleShouldCollectOrderDetails, total, err = mysql.SearchProductSaleProductItemDropdown(r.tx, req)
	if err != nil {
		return
	}

	productSaleShouldCollectOrders, err = mysql.FindShouldCollectOrderByShouldCollectOrderID(r.tx, productSaleShouldCollectOrderDetails)
	if err != nil {
		return
	}

	fpmArrangeOrderIDs := productSaleShouldCollectOrders.GetSrcIds()
	fpmArrangeOrderSvc := product.NewArrangeOrderClient()
	fpmArrangeOrders, err := fpmArrangeOrderSvc.GetArrangeOrderList(ctx, product.ArrangeOrderReq{IDs: fpmArrangeOrderIDs})
	if err != nil {
		return
	}

	stockMap, err := product.NewProductStockClient().GetStockByIds(ctx, productSaleShouldCollectOrderDetails.GetSumStockIds())
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	// bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productSaleShouldCollectOrders, fpmArrangeOrders)
	// bizUnitSvc := biz_unit.NewClientBizUnitService()
	// bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	// if err != nil {
	// 	return
	// }

	employeeIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(fpmArrangeOrders, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "product_id")
	productSvc := product_base.NewProductClient()
	productItems, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(productSaleShouldCollectOrderDetails, "product_color_id")
	productColorSvc := product_base.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	measurementUnitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", fpmArrangeOrders, productSaleShouldCollectOrderDetails)
	measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	for _, productSaleShouldCollectOrderDetail := range productSaleShouldCollectOrderDetails.List() {
		sumStock := stockMap[productSaleShouldCollectOrderDetail.SumStockId]
		productSaleShouldCollectOrder := productSaleShouldCollectOrders.Pick(productSaleShouldCollectOrderDetail.ShouldCollectOrderId)
		fpmArrangeOrder := fpmArrangeOrders.PickByOrderID(productSaleShouldCollectOrder.SrcId)
		arrangeOrderDetail := fpmArrangeOrder.ItemData.PickByID(productSaleShouldCollectOrderDetail.SrcDetailId)
		o := structure.GetProductSaleProductItemDropdownData{}
		o.Id = productSaleShouldCollectOrderDetail.Id
		o.CreateTime = tools.MyTime(productSaleShouldCollectOrderDetail.CreateTime)
		o.UpdateTime = tools.MyTime(productSaleShouldCollectOrderDetail.UpdateTime)
		o.CreatorId = productSaleShouldCollectOrderDetail.CreatorId
		o.CreatorName = productSaleShouldCollectOrderDetail.CreatorName
		o.UpdaterId = productSaleShouldCollectOrderDetail.UpdaterId
		o.UpdateUserName = productSaleShouldCollectOrderDetail.UpdaterName
		o.OrderId = productSaleShouldCollectOrder.Id
		o.OrderNo = productSaleShouldCollectOrder.OrderNo
		o.SrcId = productSaleShouldCollectOrder.SrcId
		o.SrcOrderNo = productSaleShouldCollectOrder.SrcOrderNo
		o.SaleOrderID = fpmArrangeOrder.SrcId
		o.SaleOrderNo = fpmArrangeOrder.SrcOrderNo
		o.SaleSystemId = productSaleShouldCollectOrder.SaleSystemId
		o.SaleSystemName = saleSystem[productSaleShouldCollectOrder.SaleSystemId]
		o.CustomerId = sumStock.CustomerId
		o.CustomerCode = sumStock.CustomerCode
		o.CustomerName = sumStock.CustomerName
		o.OrderRemark = productSaleShouldCollectOrder.OrderRemark
		o.SaleUserId = productSaleShouldCollectOrder.SaleUserId
		o.SaleUserName = employeeName[productSaleShouldCollectOrder.SaleUserId]
		o.SaleFollowerId = productSaleShouldCollectOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[productSaleShouldCollectOrder.SaleFollowerId]
		o.SettleType = productSaleShouldCollectOrder.SettleType
		o.SettleTypeName = productSaleShouldCollectOrder.SettleType.String()
		o.CustomCycle = productSaleShouldCollectOrder.CustomCycle
		o.ProductId = productSaleShouldCollectOrderDetail.MaterialId
		if productItem, ok := productItems[productSaleShouldCollectOrderDetail.MaterialId]; ok {
			o.ProductCode = productItem.FinishProductCode
			o.ProductName = productItem.FinishProductName
			o.FinishProductWidthAndWightUnit = productItem.FinishProductWidthAndWightUnit
		}
		o.ProductColorId = productSaleShouldCollectOrderDetail.ProductColorId
		o.ProductColorCode = productColorItem[productSaleShouldCollectOrderDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[productSaleShouldCollectOrderDetail.ProductColorId][1]
		o.ProductLevelId = arrangeOrderDetail.ProductLevelId
		o.ProductLevelName = productLevel[arrangeOrderDetail.ProductLevelId]
		o.DyeFactoryColorCode = productSaleShouldCollectOrderDetail.DyeFactoryColorCode
		o.DyelotNumber = productSaleShouldCollectOrderDetail.DyelotNumber
		o.Roll = productSaleShouldCollectOrderDetail.Roll - productSaleShouldCollectOrderDetail.ReturnRoll
		o.Weight = productSaleShouldCollectOrderDetail.Weight - productSaleShouldCollectOrderDetail.ReturnWeight
		o.Length = productSaleShouldCollectOrderDetail.Length - productSaleShouldCollectOrderDetail.ReturnLength
		o.Remark = productSaleShouldCollectOrderDetail.Remark
		o.WeightError = productSaleShouldCollectOrderDetail.WeightError
		o.ActuallyWeight = productSaleShouldCollectOrderDetail.ActuallyWeight
		o.PaperTubeWeight = arrangeOrderDetail.PaperTubeWeight
		o.SettleErrorWeight = productSaleShouldCollectOrderDetail.SettleErrorWeight
		o.SettleWeight = productSaleShouldCollectOrderDetail.SettleWeight
		o.SalePrice = productSaleShouldCollectOrderDetail.SalePrice
		o.LengthCutSalePrice = productSaleShouldCollectOrderDetail.LengthCutSalePrice
		o.ArrangeRoll = productSaleShouldCollectOrderDetail.Roll
		o.ArrangeLength = productSaleShouldCollectOrderDetail.Length
		o.ArrangeWeight = productSaleShouldCollectOrderDetail.Weight
		o.ProductCraft = arrangeOrderDetail.ProductCraft
		o.ProductIngredient = arrangeOrderDetail.ProductIngredient
		o.UnitId = productSaleShouldCollectOrderDetail.MeasurementUnitId
		o.UnitName = measurementUnitName[productSaleShouldCollectOrderDetail.MeasurementUnitId]
		o.AuxiliaryUnitId = productSaleShouldCollectOrderDetail.AuxiliaryUnitId
		if o.AuxiliaryUnitId == 0 {
			o.AuxiliaryUnitId = productSaleShouldCollectOrderDetail.MeasurementUnitId
		}
		o.AuxiliaryUnitName = measurementUnitName[o.AuxiliaryUnitId]
		list = append(list, o)
	}
	return
}

func (r *ProductSaleShouldCollectOrderRepo) JudgeExitByArrangeOrder(ctx context.Context, srcId uint64) (
	data structure.GetProductSaleShouldCollectOrderData, err error) {
	order, _, err := mysql.FirstShouldCollectOrderBySrcID(r.tx, srcId)
	if err != nil {
		return
	}
	data.Id = order.Id
	data.AuditStatus = order.AuditStatus

	return
}

type AIAnalysisProductData struct {
	Id   uint64 `json:"id"`
	Code string `json:"code"`
	Name string `json:"name"`
	Roll int    `json:"roll"`
}

func (r *ProductSaleShouldCollectOrderRepo) AIAnalysisProduct(ctx context.Context, req structure.AIAnalysisQuery) (data []AIAnalysisProductData, err error) {
	tx := r.tx.Table("should_collect_order_detail as d")
	tx.Select("p.id as id ,p.finish_product_code as code, p.finish_product_name as name, SUM(d.roll) as roll")
	tx.Joins("LEFT JOIN finish_product as p ON p.id = d.material_id")
	tx.Joins("LEFT JOIN should_collect_order as o ON o.id = d.should_collect_order_id")
	if req.SaleUser != "" {
		tx.Joins("LEFT JOIN employee as e ON e.id = o.sale_user_id")
		tx.Where("e.name like ?", fmt.Sprintf("%%%s%%", req.SaleUser))
	}
	if req.Customer != "" {
		tx.Joins("LEFT JOIN biz_unit as c ON c.id = o.customer_id")
		tx.Where("c.name like ?", fmt.Sprintf("%%%s%%", req.Customer))
	}
	if req.Location != "" {
		tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
		tx.Where("b.location like ?", fmt.Sprintf("%%%s%%", req.Location))
	}
	if !req.StartTime.IsYMDZero() && !req.EndTime.IsYMDZero() {
		tx.Where("o.order_time between ? and ?", req.StartTime.StringYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	tx.Where("d.delete_time = '0000-00-00 00:00:00'")
	tx.Where("d.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	tx.Where("p.finish_product_name is not null")
	tx.Group("p.id")
	tx.Order("SUM(d.roll) DESC")
	tx.Limit(10)
	err = tx.Find(&data).Error()
	return
}

type AIAnalysisSaleAreaData struct {
	Location string `json:"location"`
	Roll     int    `json:"roll"`
}

func (r *ProductSaleShouldCollectOrderRepo) AIAnalysisSaleArea(ctx context.Context, req structure.AIAnalysisQuery) (data []AIAnalysisSaleAreaData, err error) {
	tx := r.tx.Table("should_collect_order_detail as d")
	tx.Select("b.location as location, SUM(d.roll) as roll")
	tx.Joins("LEFT JOIN should_collect_order as o ON o.id = d.should_collect_order_id")
	tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
	if req.SaleUser != "" {
		tx.Joins("LEFT JOIN employee as e ON e.id = o.sale_user_id")
		tx.Where("e.name like ?", fmt.Sprintf("%%%s%%", req.SaleUser))
	}
	if req.ProductCode != "" || req.ProductName != "" {
		tx.Joins("LEFT JOIN finish_product as p ON p.id = d.material_id")
		tx.Where("p.finish_product_code like ?", fmt.Sprintf("%%%s%%", req.ProductCode))
		tx.Where("p.finish_product_name like ?", fmt.Sprintf("%%%s%%", req.ProductName))
	}
	if !req.StartTime.IsYMDZero() && !req.EndTime.IsYMDZero() {
		tx.Where("o.order_time between ? and ?", req.StartTime.StringYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	tx.Where("d.delete_time = '0000-00-00 00:00:00'")
	tx.Where("d.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	tx.Group("b.location")
	tx.Order("SUM(d.roll) DESC")
	err = tx.Find(&data).Error()
	return
}

type AIAnalysisCustomerData struct {
	Id           uint64 `json:"id"`
	CustomerName string `json:"customer_name"`
	Roll         int    `json:"roll"`
}

func (r *ProductSaleShouldCollectOrderRepo) AIAnalysisCustomer(ctx context.Context, req structure.AIAnalysisQuery) (data []AIAnalysisCustomerData, err error) {
	tx := r.tx.Table("should_collect_order_detail as d")
	tx.Select("b.id as id, b.name as customer_name, SUM(d.roll) as roll")
	tx.Joins("LEFT JOIN should_collect_order as o ON o.id = d.should_collect_order_id")
	tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
	if req.SaleUser != "" {
		tx.Joins("LEFT JOIN employee as e ON e.id = o.sale_user_id")
		tx.Where("e.name like ?", fmt.Sprintf("%%%s%%", req.SaleUser))
	}
	if req.Location != "" {
		tx.Where("b.location like ?", fmt.Sprintf("%%%s%%", req.Location))
	}
	if req.ProductCode != "" || req.ProductName != "" {
		tx.Joins("LEFT JOIN finish_product as p ON p.id = d.material_id")
		tx.Where("p.finish_product_code like ?", fmt.Sprintf("%%%s%%", req.ProductCode))
		tx.Where("p.finish_product_name like ?", fmt.Sprintf("%%%s%%", req.ProductName))
	}
	if !req.StartTime.IsYMDZero() && !req.EndTime.IsYMDZero() {
		tx.Where("o.order_time between ? and ?", req.StartTime.StringYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	tx.Where("d.delete_time = '0000-00-00 00:00:00'")
	tx.Where("d.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	tx.Where("b.name is Not NULL")
	tx.Group("o.customer_id")
	tx.Order("SUM(d.roll) DESC")
	tx.Limit(10)
	err = tx.Find(&data).Error()
	return
}

type SaleInfoData struct {
	TotalSalePrice int `json:"total_sale_price"`
	TotalSaleRoll  int `json:"total_sale_roll"`
	OrderNum       int `json:"order_num"`
}

type SaleInfoDataItem struct {
	TotalSalePrice int `json:"total_sale_price"`
	TotalSaleRoll  int `json:"total_sale_roll"`
}

func (r *ProductSaleShouldCollectOrderRepo) AIAnalysisSaleInfo(ctx context.Context, req structure.AIAnalysisQuery) (data SaleInfoData, err error) {
	items := make([]SaleInfoDataItem, 0)
	tx := r.tx.Table("should_collect_order_detail as d")
	tx.Select("SUM(d.settle_price) as total_sale_price, SUM(d.roll) as total_sale_roll")
	tx.Joins("LEFT JOIN should_collect_order as o ON o.id = d.should_collect_order_id")
	if req.SaleUser != "" {
		tx.Joins("LEFT JOIN employee as e ON e.id = o.sale_user_id")
		tx.Where("e.name like ?", fmt.Sprintf("%%%s%%", req.SaleUser))
	}
	if req.ProductCode != "" || req.ProductName != "" {
		tx.Joins("LEFT JOIN finish_product as p ON p.id = d.material_id")
		tx.Where("p.finish_product_code like ?", fmt.Sprintf("%%%s%%", req.ProductCode))
		tx.Where("p.finish_product_name like ?", fmt.Sprintf("%%%s%%", req.ProductName))
	}
	if req.Location != "" {
		tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
		tx.Where("b.location like ?", fmt.Sprintf("%%%s%%", req.Location))
	}
	if req.Customer != "" {
		tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
		tx.Where("b.name like ?", fmt.Sprintf("%%%s%%", req.Customer))
	}
	tx.Where("o.order_time between ? and ?", req.StartTime.ToTimeYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("d.delete_time = '0000-00-00 00:00:00'")
	tx.Where("d.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	tx.Group("o.id")
	err = tx.Find(&items).Error()
	if err != nil {
		return
	}
	for _, item := range items {
		data.TotalSalePrice += item.TotalSalePrice
		data.TotalSaleRoll += item.TotalSaleRoll
	}
	data.OrderNum = len(items)
	return
}

type AIAnalysisImportantKey struct {
	TotalSettlePrice int `json:"total_settle_price"`
	UncollectedPrice int `json:"uncollected_price"`
}

func (r *ProductSaleShouldCollectOrderRepo) AIAnalysisImportantKey(ctx context.Context, req structure.AIAnalysisQuery) (data AIAnalysisImportantKey, err error) {
	tx := r.tx.Table("should_collect_order as o")
	tx.Select("SUM(o.total_settle_money) as total_settle_price, SUM(o.uncollect_money) as uncollected_price")
	tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
	tx.Where("o.order_time between ? and ?", req.StartTime.ToTimeYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("o.delete_time = '0000-00-00 00:00:00'")
	tx.Where("o.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	err = tx.Find(&data).Error()
	return
}

func (r *ProductSaleShouldCollectOrderRepo) GetNewCustomerNum(ctx context.Context, req structure.AIAnalysisQuery) (num int, err error) {
	tx := r.tx.Table("should_collect_order as o")
	tx.Select("COUNT(b.id) as num")
	tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
	tx.Where("o.order_time between ? and ?", req.StartTime.ToTimeYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("b.create_time between ? and ?", req.StartTime.ToTimeYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("o.delete_time = '0000-00-00 00:00:00'")
	tx.Where("o.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	err = tx.Find(&num).Error()
	return
}

type DailySaleInfoData struct {
	Date           string `json:"date"`
	TotalSalePrice int    `json:"total_sale_price"`
	TotalSaleRoll  int    `json:"total_sale_roll"`
}

func (r *ProductSaleShouldCollectOrderRepo) GetDailySaleInfo(ctx context.Context, req structure.AIAnalysisQuery, dateFormat string) (data []DailySaleInfoData, err error) {
	tx := r.tx.Table("should_collect_order_detail as d")
	tx.Select(fmt.Sprintf("DATE_FORMAT(o.order_time, '%s') as date, SUM(d.settle_price) as total_sale_price, SUM(d.roll) as total_sale_roll", dateFormat))
	tx.Joins("LEFT JOIN should_collect_order as o ON o.id = d.should_collect_order_id")
	if req.SaleUser != "" {
		tx.Joins("LEFT JOIN employee as e ON e.id = o.sale_user_id")
		tx.Where("e.name like ?", fmt.Sprintf("%%%s%%", req.SaleUser))
	}
	if req.ProductCode != "" || req.ProductName != "" {
		tx.Joins("LEFT JOIN finish_product as p ON p.id = d.material_id")
		tx.Where("p.finish_product_code like ?", fmt.Sprintf("%%%s%%", req.ProductCode))
		tx.Where("p.finish_product_name like ?", fmt.Sprintf("%%%s%%", req.ProductName))
	}
	if req.Location != "" {
		tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
		tx.Where("b.location like ?", fmt.Sprintf("%%%s%%", req.Location))
	}
	if req.Customer != "" {
		tx.Joins("LEFT JOIN biz_unit as b ON b.id = o.customer_id")
		tx.Where("b.name like ?", fmt.Sprintf("%%%s%%", req.Customer))
	}
	tx.Where("o.order_time between ? and ?", req.StartTime.ToTimeYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("d.delete_time = '0000-00-00 00:00:00'")
	tx.Where("d.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common.AuditStatusPass)
	tx.Group(fmt.Sprintf("DATE_FORMAT(o.order_time, '%s')", dateFormat))
	err = tx.Find(&data).Error()
	return
}

type CustomerMatrixData struct {
	CustomerId     uint64 `json:"customer_id"`
	OrderNum       int    `json:"order_num"`
	TotalSalePrice int    `json:"total_sale_price"`
}

func (r *ProductSaleShouldCollectOrderRepo) GetCustomerMatrix(ctx context.Context, req structure.GetMatrixQuery) (list []CustomerMatrixData, err error) {
	tx := r.tx.Table("should_collect_order")
	tx.Select("customer_id as customer_id, COUNT(id) as order_num, SUM(total_settle_money) as total_sale_price")
	tx.Where("order_time between ? and ?", req.StartTime.StringYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("audit_status = ?", common_system.OrderStatusAudited)
	tx.Where("delete_time = '0000-00-00 00:00:00'")
	tx.Group("customer_id")
	err = tx.Find(&list).Error()
	return
}

type CustomerMatrixDetailData struct {
	CustomerId     uint64    `json:"customer_id"`
	CustomerName   string    `json:"customer_name"`
	OrderNum       int       `json:"order_num"`
	TotalSalePrice int       `json:"total_sale_price"`
	OrderTime      time.Time `json:"order_time"`
}

func (r *ProductSaleShouldCollectOrderRepo) GetCustomerMatrixDetail(ctx context.Context, req structure.GetMatrixQuery) (list []CustomerMatrixDetailData, err error) {
	tx := r.tx.Table("should_collect_order as o")
	tx.Select("MAX(o.order_time) as order_time, o.customer_id as customer_id,b.name as customer_name, COUNT(o.id) as order_num, SUM(o.total_settle_money) as total_sale_price")
	tx.Joins("LEFT JOIN biz_unit as b ON o.customer_id = b.id")
	tx.Where("o.order_time between ? and ?", req.StartTime.StringYMD(), req.EndTime.StringYMD2DayListTimeYMDHMS())
	tx.Where("o.collect_type = ?", common_collect.CollectTypeProductSale)
	tx.Where("o.audit_status = ?", common_system.OrderStatusAudited)
	tx.Where("o.delete_time = '0000-00-00 00:00:00'")
	if req.CustomerName != "" {
		tx.Where("b.name like ?", fmt.Sprintf("%%%s%%", req.CustomerName))
	}
	tx.Group("o.customer_id")
	err = tx.Find(&list).Error()
	return
}
