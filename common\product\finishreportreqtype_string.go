// Code generated by "stringer -type=FinishReportReqType --linecomment"; DO NOT EDIT.

package product

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[FinishReportReqTypeSummaryReport-1]
	_ = x[FinishReportReqTypeDetailReport-2]
	_ = x[FinishReportReqTypeDyelotNumberReport-3]
	_ = x[FinishReportReqTypeItemFcReport-4]
	_ = x[FinishReportReqTypeMonthDetail-11]
	_ = x[FinishReportReqTypeMonthDyelotNumber-12]
	_ = x[FinishReportReqTypeMonthProductColor-13]
	_ = x[FinishReportReqTypeMonthProduct-14]
}

const (
	_FinishReportReqType_name_0 = "成品布种汇总成品颜色汇总成品缸号汇总成品细码报表"
	_FinishReportReqType_name_1 = "月报表明细月报表缸号月报表色号月报表面料"
)

var (
	_FinishReportReqType_index_0 = [...]uint8{0, 18, 36, 54, 72}
	_FinishReportReqType_index_1 = [...]uint8{0, 15, 30, 45, 60}
)

func (i FinishReportReqType) String() string {
	switch {
	case 1 <= i && i <= 4:
		i -= 1
		return _FinishReportReqType_name_0[_FinishReportReqType_index_0[i]:_FinishReportReqType_index_0[i+1]]
	case 11 <= i && i <= 14:
		i -= 11
		return _FinishReportReqType_name_1[_FinishReportReqType_index_1[i]:_FinishReportReqType_index_1[i+1]]
	default:
		return ""
	}
}
