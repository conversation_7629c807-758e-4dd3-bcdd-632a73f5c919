package system

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/gin-gonic/gin"
	"hcscm/common/errors"
	"hcscm/config"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/system"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/ast"
	"hcscm/vars"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"text/template"
)

const (
	autoPath           = "base_template/"
	autocodePath       = "resource/base_template"
	packageService     = "service/%s/enter.go"
	packageServiceName = "service"
	packageRouter      = "router/%s/enter.go"
	packageRouterName  = "router"
	packageAPI         = "api/v1/%s/enter.go"
	packageAPIName     = "api/v1"
)

var (
	packageInjectionMap map[string]structure_base.AstInjectionMeta
	injectionPaths      []structure_base.InjectionMeta
	customAutocodePath  string
	customAutoPath      string
)

func InitAutocode() {
	if config.Conf.AutoCode.Path != "" {
		customAutocodePath = config.Conf.AutoCode.Path
		customAutoPath = strings.TrimPrefix(customAutocodePath, "resource/") + "/"
	} else {
		customAutocodePath = autocodePath
		customAutoPath = autoPath
	}
}

// GetDB
//
//	@Tags		【低代码】
//	@Summary	获取当前所有数据库
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		business_db	query		string						false	"business_db"
//	@Success	200			{object}	structure_base.GetDBData	"获取当前所有数据库"
//	@Router		/system/autoCode/getDB [get]
func GetDB(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var data = &structure_base.GetDBData{}
	businessDB := c.Query("business_db")
	dbs, err := mysql.GetSql.GetDB(businessDB)

	if err != nil {
		BuildCustomError(c, err)
	} else {
		data.DBs = dbs
		BuildResponse(c, err, data)
	}
}

// GetTables
//
//	@Tags		【低代码】
//	@Summary	获取当前数据库所有表
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		business_db	query		string							false	"business_db"
//	@Param		db_name		query		string							false	"db_name"
//	@Success	200			{object}	structure_base.GetTablesData	"获取当前数据库所有表"
//	@Router		/system/autoCode/getTables [get]
func GetTables(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var data = &structure_base.GetTablesData{}
	dbName := c.DefaultQuery("db_name", config.Conf.Mysql.DbName)
	businessDB := c.Query("business_db")
	tables, err := mysql.GetSql.GetTables(businessDB, dbName)
	if err != nil {
		BuildCustomError(c, err)
	} else {
		data.Tables = tables
		BuildResponse(c, err, data)
	}
}

// GetColumn
//
//	@Tags		【低代码】
//	@Summary	获取当前表所有字段
//	@Security	ApiKeyAuths
//	@accept		application/json
//	@Produce	application/json
//	@Param		business_db	query		string							false	"business_db"
//	@Param		db_name		query		string							false	"db_name"
//	@Param		table_name	query		string							false	"table_name"
//	@Success	200			{object}	structure_base.GetColumnData	"获取当前表所有字段"
//	@Router		/system/autoCode/getColumn [get]
func GetColumn(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var data = &structure_base.GetColumnData{}
	businessDB := c.Query("business_db")
	dbName := c.DefaultQuery("db_name", config.Conf.Mysql.DbName)
	tableName := c.Query("table_name")
	columns, err := mysql.GetSql.GetColumn(businessDB, tableName, dbName)
	if err != nil {
		BuildCustomError(c, err)
	} else {
		data.Columns = columns
		BuildResponse(c, err, data)
	}
}

// PreviewTemp
//
//	@Tags		【低代码】
//	@Summary	预览创建后的代码
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		body		body		structure_base.AutoCodeStructParam{}	true	"入参信息"
//	@Param		Platform	header		int										true	"终端ID"
//	@Success	200			{object}	structure_base.AutoCodeStructData{}
//	@Router		/system/autoCode/preview [post]
func PreviewTemp(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var (
		autoCode = &structure_base.AutoCodeStructParam{}
		data     = &structure_base.AutoCodeStructData{}
		err      error
	)
	err = ShouldBind(c, autoCode)
	if err != nil {
		return
	}

	autoCode.Pretreatment() // 处理go关键字
	autoCode.GetEnumList()  // 处理枚举值
	// 包名大写
	autoCode.PackageT = tools.FirstUpper(autoCode.Package)
	data.AutoCode, err = previewTemp(autoCode)
	if err != nil {
		BuildCustomError(c, err)
	} else {
		response := structure_base.Response{}
		response.SetVersion(vars.Version)
		response.Code = 0
		response.Msg = "success"
		response.Data = data
		c.JSON(http.StatusOK, response)
		c.Abort()
		setResult(c, response)
	}
}

// CreateTemp
//
//	@Tags		【低代码】
//	@Summary	自动代码模板
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		data	body		structure_base.AutoCodeStructParam	true	"创建自动代码"
//	@Success	200		{string}	string								"{"success":true,"data":{},"msg":"创建成功"}"
//	@Router		/system/autoCode/createTemp [post]
func CreateTemp(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var (
		autoCode = &structure_base.AutoCodeStructParam{}
		err      error
	)
	ctx := GetSystemInfoCtx(c)
	err = ShouldBind(c, autoCode)
	if err != nil {
		return
	}
	autoCode.Pretreatment()
	autoCode.GetEnumList() // 处理枚举值
	autoCode.PackageT = tools.FirstUpper(autoCode.Package)
	err = createTemp(ctx, autoCode)
	if err != nil {
		BuildCustomError(c, err)
	} else {
		response := structure_base.Response{}
		response.SetVersion(vars.Version)
		response.Code = 0
		response.Msg = "success"
		c.JSON(http.StatusOK, response)
		c.Abort()
		setResult(c, response)
	}
}

func createTemp(ctx context.Context, autoCode *structure_base.AutoCodeStructParam, ids ...uint) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	autoCode.StructNameList = fmt.Sprintf("%v%v", autoCode.StructName, "List")
	makeDictTypes(autoCode)
	for i := range autoCode.Fields {
		if autoCode.Fields[i].FieldType == "time.Time" {
			autoCode.HasTimer = true
		} else if autoCode.Fields[i].FieldType == "enum" {
			if autoCode.Fields[i].FieldName == "Status" {
				autoCode.HasStatus = true
			} else if autoCode.Fields[i].FieldName == "AuditStatus" {
				autoCode.HasAuditStatus = true
			} else if autoCode.Fields[i].FieldName == "BusinessClose" {
				autoCode.HasBusinessClose = true
			} else if autoCode.Fields[i].FieldName == "OrderNo" {
				autoCode.IsOrder = true
			}
		} else if autoCode.Fields[i].FieldType == "bool" {

		}
		if autoCode.Fields[i].Require {
			autoCode.NeedValid = true
		}
		if autoCode.Fields[i].Sort {
			autoCode.NeedSort = true
		}
	}
	// 增加判断: 重复创建struct
	// if autoCode.AutoMoveFile && mysql.Repeat(autoCode.BusinessDB, autoCode.StructName, autoCode.Package) {
	//	err = middleware.WarnLog(errors.NewError(errors.ErrCodeRequestDistinct))
	//	return
	// }
	dataList, _, needMkdir, err := getNeedList(autoCode)
	if err != nil {
		return err
	}
	meta, _ := json.Marshal(autoCode)
	// 写入文件前，先创建文件夹
	if err = tools.CreateDir(needMkdir...); err != nil {
		return err
	}

	// 生成文件
	for _, value := range dataList {
		f, err := os.OpenFile(value.AutoCodePath, os.O_CREATE|os.O_WRONLY, 0o755)
		if err != nil {
			return err
		}
		if err = value.Template.Execute(f, autoCode); err != nil {
			return err
		}
		_ = f.Close()
	}

	defer func() { // 移除中间文件
		if err := os.RemoveAll(customAutoPath); err != nil {
			return
		}
	}()
	bf := strings.Builder{}
	idBf := strings.Builder{}
	injectionCodeMeta := strings.Builder{}
	for _, id := range ids {
		idBf.WriteString(strconv.Itoa(int(id)))
		idBf.WriteString(";")
	}
	if autoCode.AutoMoveFile { // 判断是否需要自动转移
		// 追加代码到文件
		InitFile(autoCode.Package)
		for index := range dataList {
			addAutoMoveFile(&dataList[index])
		}
		// 判断目标文件是否都可以移动
		for _, value := range dataList {
			if tools.FileExist(value.AutoMoveFilePath) {
				return middleware.WarnLog(errors.NewError(errors.ErrCodeFileAlreadyExist))
			}
		}
		for _, value := range dataList { // 移动文件
			if err := tools.FileMove(value.AutoCodePath, value.AutoMoveFilePath); err != nil {
				return err
			}
		}

		// {
		//	// 在gorm.go 注入 自动迁移
		//	path := filepath.Join(config.Conf.AutoCode.Root,
		//		config.Conf.AutoCode.Server, "/model/mysql", "gorm.go")
		//	ast.AddRegisterTablesAst(path, "RegisterTables", autoCode.Package, autoCode.BusinessDB, autoCode.StructName)
		// }

		// {
		//	// router.go 注入 自动迁移
		//	path := filepath.Join(config.Conf.AutoCode.Root,
		//		config.Conf.AutoCode.Server, config.Conf.AutoCode.SInitialize, "init.go")
		//	ast.AddRouterCode(path, "Init", autoCode.Package, autoCode.StructName)
		// }
		// 给各个enter进行注入
		err = injectionCode(autoCode.StructName, &injectionCodeMeta)
		if err != nil {
			return
		}
		// 保存生成信息
		for _, data := range dataList {
			if len(data.AutoMoveFilePath) != 0 {
				bf.WriteString(data.AutoMoveFilePath)
				bf.WriteString(";")
			}
		}
	}
	if autoCode.AutoMoveFile {
		_, err = mysql.MustCreateAutoCodeHistories(tx, mysql.CreateAutoCodeHistory(
			string(meta),
			autoCode.StructName,
			autoCode.Description,
			bf.String(),
			injectionCodeMeta.String(),
			autoCode.Package,
		))
	}
	if err != nil {
		return err
	}
	// if autoCode.AutoMoveFile {
	//	return middleware.WarnLog(errors.NewError(666), "创建代码成功并移动文件成功")
	// }
	return nil
}

// injectionCode 封装代码注入
func injectionCode(structName string, bf *strings.Builder) error {
	for _, meta := range injectionPaths {
		code := fmt.Sprintf(meta.StructNameF, structName)
		ast.ImportForAutoEnter(meta.Path, meta.FuncName, code)
		bf.WriteString(fmt.Sprintf("%s@%s@%s;", meta.Path, meta.FuncName, code))
	}
	return nil
}

func addAutoMoveFile(data *structure_base.TplData) {
	base := filepath.Base(data.AutoCodePath)
	fileSlice := strings.Split(data.AutoCodePath, string(os.PathSeparator))
	n := len(fileSlice)
	if n <= 2 {
		return
	}
	if strings.Contains(fileSlice[1], "hcscm") {
		if strings.Contains(fileSlice[n-2], "router") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root, config.Conf.AutoCode.Server,
				fmt.Sprintf(config.Conf.AutoCode.SRouter, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "service") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SService, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "model") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SModel, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "structure") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SRequest, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "server") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SServer, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "aggs") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SAggs, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "dao") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SDao, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "pb") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SPb, data.AutoPackage), base)
		} else if strings.Contains(fileSlice[n-2], "pdiface") {
			data.AutoMoveFilePath = filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SPdiface, data.AutoPackage), fileSlice[2]+"iface.go")
		}
	}
}

func InitFile(Package string) {
	// injectionPaths = []structure_base.InjectionMeta{
	//	{
	//		Path: filepath.Join(config.Conf.AutoCode.Root,
	//			config.Conf.AutoCode.Server, fmt.Sprintf(config.Conf.AutoCode.SCommon, Package), fmt.Sprintf("%v%v", Package, ".go")),
	//		FuncName:    "RouterGroup",
	//		StructNameF: "%sRouter",
	//	},
	// }

	packageInjectionMap = map[string]structure_base.AstInjectionMeta{
		packageRouterName: {
			Path: filepath.Join(config.Conf.AutoCode.Root,
				config.Conf.AutoCode.Server, "router", "init.go"),
			ImportCodeF:  "hcscm/%s/%s",
			PackageNameF: "%s",
			GroupName:    "RouterGroup",
			StructNameF:  "%s",
		},
	}
}

func previewTemp(autoCode *structure_base.AutoCodeStructParam) (map[string]string, error) {
	autoCode.StructNameList = fmt.Sprintf("%v%v", autoCode.StructName, "List")
	makeDictTypes(autoCode)
	for i := range autoCode.Fields {
		// 数据类型
		if autoCode.Fields[i].FieldType == "time.Time" {
			autoCode.HasTimer = true
		} else if autoCode.Fields[i].FieldType == "enum" {
			if autoCode.Fields[i].FieldName == "Status" {
				autoCode.HasStatus = true
			} else if autoCode.Fields[i].FieldName == "AuditStatus" {
				autoCode.HasAuditStatus = true
			} else if autoCode.Fields[i].FieldName == "BusinessClose" {
				autoCode.HasBusinessClose = true
			} else if autoCode.Fields[i].FieldName == "OrderNo" {
				autoCode.IsOrder = true
			}
		} else if autoCode.Fields[i].FieldType == "bool" {

		}
		// 是否必填
		if autoCode.Fields[i].Require {
			autoCode.NeedValid = true
		}
		// 是否排序
		if autoCode.Fields[i].Sort {
			autoCode.NeedSort = true
		}
	}
	dataList, _, needMkdir, err := getNeedList(autoCode) // 获取全部模板，组装文件路径
	if err != nil {
		return nil, err
	}

	// 写入文件前，先创建文件夹
	if err = tools.CreateDir(needMkdir...); err != nil {
		return nil, err
	}

	// 创建map
	ret := make(map[string]string)

	// 生成map
	for _, value := range dataList {
		ext := ""
		if ext = filepath.Ext(value.AutoCodePath); ext == ".txt" {
			continue
		}
		f, err := os.OpenFile(value.AutoCodePath, os.O_CREATE|os.O_WRONLY, 0o755)
		if err != nil {
			return nil, err
		}
		if err = value.Template.Execute(f, autoCode); err != nil {
			return nil, err
		}
		_ = f.Close()
		f, err = os.OpenFile(value.AutoCodePath, os.O_CREATE|os.O_RDONLY, 0o755)
		if err != nil {
			return nil, err
		}
		builder := strings.Builder{}
		builder.WriteString("```")

		if ext != "" && strings.Contains(ext, ".") {
			builder.WriteString(strings.Replace(ext, ".", "", -1))
		}
		builder.WriteString("\n\n")
		data, err := io.ReadAll(f)
		if err != nil {
			return nil, err
		}
		builder.Write(data)
		builder.WriteString("\n\n```")

		pathArr := strings.Split(value.AutoCodePath, string(os.PathSeparator))
		ret[pathArr[1]+"-"+pathArr[3]] = builder.String()
		_ = f.Close()

	}
	defer func() { // 移除中间文件
		if err := os.RemoveAll(customAutoPath); err != nil {
			return
		}
	}()
	return ret, err
}

func makeDictTypes(autoCode *structure_base.AutoCodeStructParam) {
	DictTypeM := make(map[string]string)
	for _, v := range autoCode.Fields { // 字典
		if v.DictType != "" {
			DictTypeM[v.DictType] = ""
		}
	}

	for k := range DictTypeM {
		autoCode.DictTypes = append(autoCode.DictTypes, k)
	}
}

func getNeedList(autoCode *structure_base.AutoCodeStructParam) (dataList []structure_base.TplData, fileList []string, needMkdir []string, err error) {
	// 去除所有空格
	tools.TrimSpace(autoCode)
	for _, field := range autoCode.Fields {
		tools.TrimSpace(field)
	}
	// 获取 basePath 文件夹下所有tpl文件
	tplFileList, err := GetAllTplFile(customAutocodePath, nil)
	if err != nil {
		return nil, nil, nil, err
	}
	dataList = make([]structure_base.TplData, 0, len(tplFileList))
	fileList = make([]string, 0, len(tplFileList))
	needMkdir = make([]string, 0, len(tplFileList)) // 当文件夹下存在多个tpl文件时，改为map更合理
	// 根据文件路径生成 tplData 结构体，待填充数据
	for _, value := range tplFileList {
		dataList = append(dataList, structure_base.TplData{LocationPath: value, AutoPackage: autoCode.Package})
	}
	// 生成 *Template, 填充 template 字段
	for index, value := range dataList {
		dataList[index].Template, err = template.ParseFiles(value.LocationPath)
		if err != nil {
			return nil, nil, nil, err
		}
	}
	// 生成文件路径，填充 AutoCodePath 字段，readme.txt.tpl不符合规则，需要特殊处理
	// resource/template/web/api.js.tpl -> autoCode/web/autoCode.PackageName/api/autoCode.PackageName.js
	// resource/template/readme.txt.tpl -> autoCode/readme.txt
	for index, value := range dataList {
		trimBase := strings.TrimPrefix(value.LocationPath, customAutocodePath+"/")
		if trimBase == "readme.txt.tpl" {
			dataList[index].AutoCodePath = customAutoPath + "readme.txt"
			continue
		}

		if lastSeparator := strings.LastIndex(trimBase, "/"); lastSeparator != -1 {
			origFileName := strings.TrimSuffix(trimBase[lastSeparator+1:], ".tpl")
			firstDot := strings.Index(origFileName, ".")
			if firstDot != -1 {
				var fileName string
				if origFileName[firstDot:] != ".go" {
					fileName = autoCode.PackageName + origFileName[firstDot:]
				} else if strings.Contains(value.LocationPath, "iface") {
					fileName = autoCode.HumpPackageName + "_iface" + origFileName[firstDot:]
				} else {
					fileName = autoCode.HumpPackageName + origFileName[firstDot:]
				}
				dataList[index].AutoCodePath = filepath.Join(customAutoPath, trimBase[:lastSeparator], autoCode.PackageName,
					origFileName[:firstDot], fileName)
			}
		}

		if lastSeparator := strings.LastIndex(dataList[index].AutoCodePath, string(os.PathSeparator)); lastSeparator != -1 {
			needMkdir = append(needMkdir, dataList[index].AutoCodePath[:lastSeparator])
		}
	}
	for _, value := range dataList {
		fileList = append(fileList, value.AutoCodePath)
	}
	return dataList, fileList, needMkdir, err
}

// 获取 pathName 文件夹下所有 tpl 文件
func GetAllTplFile(pathName string, fileList []string) ([]string, error) {
	files, err := os.ReadDir(pathName)
	if err != nil {
		return nil, err
	}
	for _, fi := range files {
		if fi.IsDir() {
			fileList, err = GetAllTplFile(pathName+"/"+fi.Name(), fileList)
			if err != nil {
				return nil, err
			}
		} else {
			if strings.HasSuffix(fi.Name(), ".tpl") {
				fileList = append(fileList, pathName+"/"+fi.Name())
			}
		}
	}
	return fileList, err
}

// CreatePackage
//
//	@Tags		【低代码】
//	@Summary	获取package
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		body		body		structure_base.AddPackageParam{}	true	"入参信息"
//	@Param		Platform	header		int									true	"终端ID"
//	@Success	200			{object}	structure_base.AddPackageData{}
//	@Router		/system/autoCode/package/add [post]
func CreatePackage(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var (
		p    = &structure_base.AddPackageParam{}
		data = &structure_base.AddPackageData{}
		err  error
	)
	ctx := GetSystemInfoCtx(c)

	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	r := mysql.NewPackage(p)

	r, err = mysql.MustCreatePackage(tx, r)
	if err != nil {
		return
	}
	data.Id = r.Id
}

// GetPackageList
//
//	@Tags		【低代码】
//	@Summary	获取package列表
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		body		body		structure_base.GetPackageListQuery{}	true	"入参信息"
//	@Param		Platform	header		int										true	"终端ID"
//	@Success	200			{object}	structure_base.GetPackageDataList{}
//	@Router		/system/autoCode/package/getList [get]
func GetPackageList(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var (
		p       = &structure_base.GetPackageListQuery{}
		list    = make(structure_base.GetPackageDataList, 0)
		err     error
		total   = 0
		Package mysql.PackageList
	)
	ctx := GetSystemInfoCtx(c)

	defer func() {
		BuildListResponse(c, err, list, total)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	Package, total, err = mysql.SearchPackage(tx, p)
	if err != nil {
		return
	}
	for _, sysAutoCode := range Package {
		var r structure_base.GetPackageData
		r.Id = sysAutoCode.Id
		r.PackageName = sysAutoCode.PackageName
		r.Label = sysAutoCode.Label
		r.Desc = sysAutoCode.Desc
		list = append(list, r)
	}
	return
}

// GetPackage
//
//	@Tags		【低代码】
//	@Summary	获取package
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		body		body		structure_base.GetPackageListQuery{}	true	"入参信息"
//	@Param		Platform	header		int										true	"终端ID"
//	@Success	200			{object}	structure_base.GetPackageDataList{}
//	@Router		/system/autoCode/package/get [get]
func GetPackage(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var (
		p           = &structure_base.GetPackageQuery{}
		data        = &structure_base.GetPackageData{}
		err         error
		sysAutoCode mysql.Package
	)
	ctx := GetSystemInfoCtx(c)

	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	sysAutoCode, _, err = mysql.FirstPackageByID(tx, p.Id)
	if err != nil {
		return
	}

	data.Id = sysAutoCode.Id
	data.PackageName = sysAutoCode.PackageName
	data.Label = sysAutoCode.Label
	data.Desc = sysAutoCode.Desc
	return
}

// DelPackage
//
//	@Tags		【低代码】
//	@Summary	获取package
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		body		body		structure_base.DeletePackageParam{}	true	"入参信息"
//	@Param		Platform	header		int									true	"终端ID"
//	@Success	200			{object}	structure_base.DeletePackageData{}
//	@Router		/system/autoCode/package/del [get]
func DelPackage(c *gin.Context) {
	if vars.Env != "local" {
		err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	var (
		p    = &structure_base.DeletePackageParam{}
		data = &structure_base.DeletePackageData{}
		err  error
		r    mysql.Package
	)
	ctx := GetSystemInfoCtx(c)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	r, err = mysql.MustFirstPackageByID(tx, p.Id)
	if err != nil {
		return
	}
	err = mysql.MustDeletePackage(tx, r)
	if err != nil {
		return
	}
	data.Id = r.Id
}

func GetSnowflake(c *gin.Context) {

	var (
		data = structure_base.RecordData{}
		err  error
	)
	if vars.Env != "local" {
		err = middleware.ErrorLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
		BuildResponse(c, err, nil)
		return
	}
	defer func() {
		BuildResponse(c, err, data)
	}()
	data.Id = vars.Snowflake.GenerateId().UInt64()
}

func SyncMainDB(c *gin.Context) {
	var (
		tenantTableMap = make(map[string]bool, 0)
	)
	info, ok := GetLoginInfo(c)
	if !ok {
		return
	}
	mainTables, err := mysql.GetSql.GetTables("", config.Conf.Mysql.DbName)
	if err != nil {
		return
	}
	tenantTables, err := mysql.GetSql.GetTables("", info.GetTenantManagementDbName())
	if err != nil {
		return
	}
	for _, table := range tenantTables {
		tenantTableMap[table.TableName] = true
	}
	for _, table := range mainTables {
		// 判断账套数据库是否存在主数据的表，不存在则新增表，存在则判断表结构是否一致，不一致则更新
		if _, ok = tenantTableMap[table.TableName]; ok {
			var (
				mainTableColumns, tenantTableColumns []structure_base.Column
				tenantTableColumnMap                 = make(map[string]bool, 0)
			)
			mainTableColumns, err = mysql.GetSql.GetColumn("", table.TableName, config.Conf.Mysql.DbName)
			if err != nil {
				return
			}
			tenantTableColumns, err = mysql.GetSql.GetColumn("", table.TableName, info.GetTenantManagementDbName())
			if err != nil {
				return
			}
			for _, column := range tenantTableColumns {
				tenantTableColumnMap[column.ColumnName] = true
			}
			for _, column := range mainTableColumns {
				if _, ok = tenantTableColumnMap[column.ColumnName]; !ok {
					if column.ColumnDefault != "NULL" && column.ColumnDefault != "" {
						err = mysql.GetSql.ExecSql(fmt.Sprintf("ALTER TABLE `%s`.`%s` ADD COLUMN `%s` %s NOT NULL DEFAULT %v COMMENT '%s';",
							info.GetTenantManagementDbName(), table.TableName, column.ColumnName, column.ColumnType, column.ColumnDefault, column.ColumnComment))
					} else {
						err = mysql.GetSql.ExecSql(fmt.Sprintf("ALTER TABLE `%s`.`%s` ADD COLUMN `%s` %s NOT NULL COMMENT '%s';",
							info.GetTenantManagementDbName(), table.TableName, column.ColumnName, column.ColumnType, column.ColumnComment))
					}
				}
				if err != nil {
					return
				}
			}
		} else {
			var tableCreateSql structure_base.TableCreateSql
			tableCreateSql, err = mysql.GetSql.GetTableCreateSql(config.Conf.Mysql.DbName, table.TableName)
			if err != nil {
				return
			}
			tableCreateSql.CreateTable = strings.Replace(tableCreateSql.CreateTable, "CREATE TABLE `", fmt.Sprintf("CREATE TABLE `%s`.`", info.GetTenantManagementDbName()), 1)
			err = mysql.GetSql.ExecSql(tableCreateSql.CreateTable)
			if err != nil {
				return
			}
		}
	}
}
