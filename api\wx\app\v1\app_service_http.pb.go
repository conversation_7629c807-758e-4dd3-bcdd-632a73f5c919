// Code generated by protoc-gen-go-http. DO NOT EDIT.
// versions:
// - protoc-gen-go-http v2.7.3
// - protoc             v5.28.2
// source: api/wx/app/app_service.proto

package v1

import (
	context "context"
	http "github.com/go-kratos/kratos/v2/transport/http"
	binding "github.com/go-kratos/kratos/v2/transport/http/binding"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the kratos package it is being compiled against.
var _ = new(context.Context)
var _ = binding.EncodeURL

const _ = http.SupportPackageIsVersion1

const OperationAppSyncExternalContact = "/wx.api.wx.app.v1.App/SyncExternalContact"
const OperationAppSyncGroupChat = "/wx.api.wx.app.v1.App/SyncGroupChat"
const OperationAppSyncUserInfo = "/wx.api.wx.app.v1.App/SyncUserInfo"

type AppHTTPServer interface {
	// SyncExternalContact 同步客户（外部联系人）
	SyncExternalContact(context.Context, *SyncExternalContactRequest) (*SyncExternalContactReply, error)
	// SyncGroupChat 同步群聊信息
	SyncGroupChat(context.Context, *SyncGroupChatRequest) (*SyncGroupChatReply, error)
	// SyncUserInfo 同步用户信息
	SyncUserInfo(context.Context, *SyncUserInfoRequest) (*SyncUserInfoReply, error)
}

func RegisterAppHTTPServer(s *http.Server, srv AppHTTPServer) {
	r := s.Route("/")
	r.GET("wx/app/api/v1/sync_user_info", _App_SyncUserInfo0_HTTP_Handler(srv))
	r.GET("wx/app/api/v1/sync_external_contact", _App_SyncExternalContact0_HTTP_Handler(srv))
	r.GET("wx/app/api/v1/sync_group_chat", _App_SyncGroupChat0_HTTP_Handler(srv))
}

func _App_SyncUserInfo0_HTTP_Handler(srv AppHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncUserInfoRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppSyncUserInfo)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.SyncUserInfo(ctx, req.(*SyncUserInfoRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncUserInfoReply)
		return ctx.Result(200, reply)
	}
}

func _App_SyncExternalContact0_HTTP_Handler(srv AppHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncExternalContactRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppSyncExternalContact)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.SyncExternalContact(ctx, req.(*SyncExternalContactRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncExternalContactReply)
		return ctx.Result(200, reply)
	}
}

func _App_SyncGroupChat0_HTTP_Handler(srv AppHTTPServer) func(ctx http.Context) error {
	return func(ctx http.Context) error {
		var in SyncGroupChatRequest
		if err := ctx.BindQuery(&in); err != nil {
			return err
		}
		http.SetOperation(ctx, OperationAppSyncGroupChat)
		h := ctx.Middleware(func(ctx context.Context, req any) (any, error) {
			return srv.SyncGroupChat(ctx, req.(*SyncGroupChatRequest))
		})
		out, err := h(ctx, &in)
		if err != nil {
			return err
		}
		reply := out.(*SyncGroupChatReply)
		return ctx.Result(200, reply)
	}
}

type AppHTTPClient interface {
	SyncExternalContact(ctx context.Context, req *SyncExternalContactRequest, opts ...http.CallOption) (rsp *SyncExternalContactReply, err error)
	SyncGroupChat(ctx context.Context, req *SyncGroupChatRequest, opts ...http.CallOption) (rsp *SyncGroupChatReply, err error)
	SyncUserInfo(ctx context.Context, req *SyncUserInfoRequest, opts ...http.CallOption) (rsp *SyncUserInfoReply, err error)
}

type AppHTTPClientImpl struct {
	cc *http.Client
}

func NewAppHTTPClient(client *http.Client) AppHTTPClient {
	return &AppHTTPClientImpl{client}
}

func (c *AppHTTPClientImpl) SyncExternalContact(ctx context.Context, in *SyncExternalContactRequest, opts ...http.CallOption) (*SyncExternalContactReply, error) {
	var out SyncExternalContactReply
	pattern := "wx/app/api/v1/sync_external_contact"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppSyncExternalContact))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AppHTTPClientImpl) SyncGroupChat(ctx context.Context, in *SyncGroupChatRequest, opts ...http.CallOption) (*SyncGroupChatReply, error) {
	var out SyncGroupChatReply
	pattern := "wx/app/api/v1/sync_group_chat"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppSyncGroupChat))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}

func (c *AppHTTPClientImpl) SyncUserInfo(ctx context.Context, in *SyncUserInfoRequest, opts ...http.CallOption) (*SyncUserInfoReply, error) {
	var out SyncUserInfoReply
	pattern := "wx/app/api/v1/sync_user_info"
	path := binding.EncodeURL(pattern, in, true)
	opts = append(opts, http.Operation(OperationAppSyncUserInfo))
	opts = append(opts, http.PathTemplate(pattern))
	err := c.cc.Invoke(ctx, "GET", path, nil, &out, opts...)
	if err != nil {
		return nil, err
	}
	return &out, err
}
