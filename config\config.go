package config

import (
	"fmt"
	"os"
	"time"

	jsoniter "github.com/json-iterator/go"
)

type DBBASE interface {
	GetLogMode() string
}

func (m *Mysql) GetLogMode() string {
	return m.LogMode
}

type Environment string

var file = "config.yaml" // 配置文件名

var Conf Config

type Autocode struct {
	TransferRestart bool   `mapstructure:"transfer-restart" json:"transfer-restart" yaml:"transfer-restart"`
	Root            string `mapstructure:"root" json:"root" yaml:"root"`
	Path            string `mapstructure:"path" json:"path" yaml:"path"`
	Server          string `mapstructure:"server" json:"server" yaml:"server"`
	SPlug           string `mapstructure:"server-plug" json:"server-plug" yaml:"server-plug"`
	SInitialize     string `mapstructure:"server-initialize" json:"server-initialize" yaml:"server-initialize"`
	SModel          string `mapstructure:"server-model" json:"server-model" yaml:"server-model"`
	SRequest        string `mapstructure:"server-request" json:"server-request"  yaml:"server-request"`
	SRouter         string `mapstructure:"server-router" json:"server-router" yaml:"server-router"`
	SService        string `mapstructure:"server-service" json:"server-service" yaml:"server-service"`
	SDao            string `mapstructure:"server-dao" json:"server-dao" yaml:"server-dao"`
	SServer         string `mapstructure:"server-server" json:"server-server" yaml:"server-server"`
	SAggs           string `mapstructure:"server-aggs" json:"server-aggs" yaml:"server-aggs"`
	SPb             string `mapstructure:"server-pb" json:"server-pb" yaml:"server-pb"`
	SPdiface        string `mapstructure:"server-pdiface" json:"server-pdiface" yaml:"server-pdiface"`
	SCommon         string `mapstructure:"server-common" json:"server-common" yaml:"server-common"`
}

type Redis struct {
	Host     string `mapstructure:"host" json:"host" yaml:"host"`             // 地址
	Port     int    `mapstructure:"port" json:"port" yaml:"port"`             // 端口
	PassWord string `mapstructure:"password" json:"password" yaml:"password"` // 密码
	DB       int    `mapstructure:"db" json:"db" yaml:"db"`                   // redis的哪个数据库
	PoolSize int    `mapstructure:"poolsize" json:"poolsize" yaml:"poolsize"` // 连接池大小
}

type Mysql struct {
	Host            string `mapstructure:"host" json:"host" yaml:"host"`                                  // 地址
	Port            int    `mapstructure:"port" json:"port" yaml:"port"`                                  // 端口
	UserName        string `mapstructure:"username" json:"username" yaml:"username"`                      // 用户
	PassWord        string `mapstructure:"password" json:"password" yaml:"password"`                      // 密码
	DbName          string `mapstructure:"dbname" json:"dbname" yaml:"dbname"`                            // 数据库名
	Debug           bool   `mapstructure:"debug" json:"debug" yaml:"debug"`                               // 是否开启调试，true表示开启
	Migrate         bool   `mapstructure:"migrate" json:"migrate" yaml:"migrate"`                         // 是否开启自动建表，true表示开启
	WasherSwitch    bool   `mapstructure:"washerswitch" json:"washerswitch" yaml:"washerswitch"`          // 是否开启清洗数据开关，true表示开启
	Singular        bool   `mapstructure:"singular" json:"singular" yaml:"singular"`                      // 是否开启全局禁用复数，true表示开启
	MaxIdleConns    int    `mapstructure:"maxidleconns" json:"maxidleconns" yaml:"maxidleconns"`          // 空闲中的最大连接数
	MaxOpenConns    int    `mapstructure:"maxopenconns" json:"maxopenconns" yaml:"maxopenconns"`          // 打开到数据库的最大连接数
	LogMode         string `mapstructure:"logmode" json:"logmode" yaml:"logmode"`                         // 是否开启Gorm全局日志
	PasswordSecret1 string `mapstructure:"passwordsecret1" json:"passwordsecret1" yaml:"passwordsecret1"` // 密码加密字符串1
	PasswordSecret2 string `mapstructure:"passwordsecret2" json:"passwordsecret2" yaml:"passwordsecret2"` // 密码加密字符串2
}

type TenantManagement struct {
	TenantPhone string `mapstructure:"tenantphone" json:"tenantphone" yaml:"tenantphone"`
}

type MysqlSlave struct {
	Host     string `mapstructure:"host" json:"host" yaml:"host"`             // 地址
	Port     int    `mapstructure:"port" json:"port" yaml:"port"`             // 端口
	UserName string `mapstructure:"username" json:"username" yaml:"username"` // 用户
	PassWord string `mapstructure:"password" json:"password" yaml:"password"` // 密码
	DbName   string `mapstructure:"dbname" json:"dbname" yaml:"dbname"`       // 数据库名
}

type Mongo struct {
	Host     string `mapstructure:"host" json:"host" yaml:"host"`       // 地址
	Port     int    `mapstructure:"port" json:"port" yaml:"port"`       // 端口
	DBName   string `mapstructure:"dbname" json:"dbname" yaml:"dbname"` // 数据库名
	AuthDB   string `mapstructure:"authdb" json:"authdb" yaml:"authdb"`
	UserName string `mapstructure:"username" json:"username" yaml:"username"`
	Password string `mapstructure:"password" json:"password" yaml:"password"`
	Auth     bool   `mapstructure:"auth" json:"auth" yaml:"auth"`
}

type Server struct {
	ServiceName     string `mapstructure:"servicename" json:"servicename" yaml:"servicename"` // 服务名
	Debug           bool   `mapstructure:"debug" json:"debug" yaml:"debug"`                   // gin是否开启debug模式
	Environment     string `mapstructure:"environment" json:"environment" yaml:"environment"` // 服务部署环境  可以是 local, dev, test, release （默认release）
	AuthOn          bool   `mapstructure:"authon" json:"authon" yaml:"authon"`
	Version         string `mapstructure:"version" json:"version" yaml:"version"`
	WebhookURL      string `mapstructure:"webhookurL" json:"webhookurL" yaml:"webhookurL"`
	TenantName      string `mapstructure:"tenantname" json:"tenantname" yaml:"tenantname"`       // 主租户名称
	ApiDomain       string `mapstructure:"apiDomain" json:"apiDomain" yaml:"apiDomain"`          // 小程序退出登录所返回的主域名
	ApiDomainPreFix string `mapstructure:"apiDomainFix" json:"apiDomainFix" yaml:"apiDomainFix"` // 小程序退出登录所返回的主域名前缀
}

type Web struct {
	Port int `mapstructure:"port" json:"port" yaml:"port"` // 服务使用端口
}

type CDN struct {
	Bucket   string `mapstructure:"bucket" json:"bucket" yaml:"bucket"`
	Op       string `mapstructure:"op" json:"op" yaml:"op"`
	Password string `mapstructure:"password" json:"password" yaml:"password"`
	Env      string `mapstructure:"env" json:"env" yaml:"env"`
}

type ZZCDN struct {
	ZZBucket   string `mapstructure:"bucket" json:"bucket" yaml:"bucket"`
	ZZOp       string `mapstructure:"op" json:"op" yaml:"op"`
	ZZPassword string `mapstructure:"password" json:"password" yaml:"password"`
	Env        string `mapstructure:"env" json:"env" yaml:"env"`
}

type Html2Image struct {
	Host string `mapstructure:"host" json:"host" yaml:"host"` // 地址
	Port int    `mapstructure:"port" json:"port" yaml:"port"` // 端口
}

type RabbitMQ struct {
	User       string
	Password   string
	Host       string
	Port       string
	Dead       string
	RoutingKey string
}

type WebSocket struct {
	PingWait string
}

type CORS struct {
	Mode      string          `mapstructure:"mode" json:"mode" yaml:"mode"`
	Whitelist []CORSWhitelist `mapstructure:"whitelist" json:"whitelist" yaml:"whitelist"`
}

type CORSWhitelist struct {
	AllowOrigin      string `mapstructure:"allow-origin" json:"allow-origin" yaml:"allow-origin"`
	AllowMethods     string `mapstructure:"allow-methods" json:"allow-methods" yaml:"allow-methods"`
	AllowHeaders     string `mapstructure:"allow-headers" json:"allow-headers" yaml:"allow-headers"`
	ExposeHeaders    string `mapstructure:"expose-headers" json:"expose-headers" yaml:"expose-headers"`
	AllowCredentials bool   `mapstructure:"allow-credentials" json:"allow-credentials" yaml:"allow-credentials"`
}

type Alibaba struct {
	AccessKey               string `mapstructure:"accesskey" json:"accesskey" yaml:"accesskey"`
	AccessSecret            string `mapstructure:"accesssecret" json:"accesssecret" yaml:"accesssecret"`
	SignName                string `mapstructure:"signname" json:"signname" yaml:"signname"`
	TemplateCode            string `mapstructure:"templatecode" json:"templatecode" yaml:"templatecode"`
	SearchImageaccesskey    string `mapstructure:"searchImageaccesskey" json:"searchImageaccesskey" yaml:"searchImageaccesskey"`
	SearchImageaccesssecret string `mapstructure:"searchImageaccesssecret" json:"searchImageaccesssecret" yaml:"searchImageaccesssecret"`
	SearchImageInstanceName string `mapstructure:"searchImageInstanceName" json:"searchImageInstanceName" yaml:"searchImageInstanceName"`
	ImageSearchRateLimitMs  int    `mapstructure:"imageSearchRateLimitMs" json:"imageSearchRateLimitMs" yaml:"imageSearchRateLimitMs"` // 图片搜索API限流间隔（毫秒），默认1000ms
}

type Feie struct {
	User string `mapstructure:"user" json:"user" yaml:"user"`
	Ukey string `mapstructure:"ukey" json:"ukey" yaml:"ukey"`
	Url  string `mapstructure:"url" json:"url" yaml:"url"`
}

type Ocr struct {
	Host string `mapstructure:"host" json:"host" yaml:"host"` // 地址
	Port string `mapstructure:"port" json:"port" yaml:"port"` // 端口
	Env  string `mapstructure:"env" json:"env" yaml:"env"`    // 端口
}

// aiDataAnalysisAppId: 67d3fea81134d4725eed73e9
// aiAnalysisAppKey: fastgpt-t9ty4QMfPzBg0XLwskFykdsNWwd2m2sJ9IONqOt7x2U4QF1uggKFZhK
type FastGpt struct {
	ChatUrl               string `mapstructure:"chatUrl" json:"chatUrl" yaml:"chatUrl"`                                           // 对话接口地址
	ABuChatAppId          string `mapstructure:"aBuChatAppId" json:"aBuChatAppId" yaml:"aBuChatAppId"`                            // 阿布智能聊天助手id
	ABuChatAppKey         string `mapstructure:"aBuChatAppKey" json:"aBuChatAppKey" yaml:"aBuChatAppKey"`                         // 阿布智能聊天助手key
	AiOrderAppId          string `mapstructure:"aiOrderAppId" json:"aiOrderAppId" yaml:"aiOrderAppId"`                            // 智能下单应用id
	AiOrderAppKey         string `mapstructure:"aiOrderAppKey" json:"aiOrderAppKey" yaml:"aiOrderAppKey"`                         // 智能下单应用key
	AiAnalysisSaleAppId   string `mapstructure:"aiAnalysisSaleAppId" json:"aiAnalysisSaleAppId" yaml:"aiAnalysisSaleAppId"`       // ai分析销售报表应用id
	AiAnalysisSaleAppKey  string `mapstructure:"aiAnalysisSaleAppKey" json:"aiAnalysisSaleAppKey" yaml:"aiAnalysisSaleAppKey"`    // ai分析销售报表应用key
	AiProductQueryAppId   string `mapstructure:"aiProductQueryAppId" json:"aiProductQueryAppId" yaml:"aiProductQueryAppId"`       // 阿布智能查百科应用id
	AiProductQueryAppKey  string `mapstructure:"aiProductQueryAppKey" json:"aiProductQueryAppKey" yaml:"aiProductQueryAppKey"`    // 阿布智能查百科应用key
	ABuAnalysisSaleAppId  string `mapstructure:"aBuAnalysisSaleAppId" json:"aBuAnalysisSaleAppId" yaml:"aBuAnalysisSaleAppId"`    // 阿布智能数据分析应用id
	ABuAnalysisSaleAppKey string `mapstructure:"aBuAnalysisSaleAppKey" json:"aBuAnalysisSaleAppKey" yaml:"aBuAnalysisSaleAppKey"` // 阿布智能数据分析应用key
	AiDataAnalysisAppId   string `mapstructure:"aiDataAnalysisAppId" json:"aiDataAnalysisAppId" yaml:"aiDataAnalysisAppId"`       // ai数据分析应用id
	AiDataAnalysisAppKey  string `mapstructure:"aiDataAnalysisAppKey" json:"aiDataAnalysisAppKey" yaml:"aiDataAnalysisAppKey"`    // ai数据分析应用key
	AiGetPriceAppId       string `mapstructure:"aiGetPriceAppId" json:"aiGetPriceAppId" yaml:"aiGetPriceAppId"`                   // ai查价助手id
	AiGetPriceAppKey      string `mapstructure:"aiGetPriceAppKey" json:"aiGetPriceAppKey" yaml:"aiGetPriceAppKey"`                // ai查价助手key
	AiGetStockAppId       string `mapstructure:"aiGetStockAppId" json:"aiGetStockAppId" yaml:"aiGetStockAppId"`                   // ai库存助手id
	AiGetStockAppKey      string `mapstructure:"aiGetStockAppKey" json:"aiGetStockAppKey" yaml:"aiGetStockAppKey"`                // ai库存助手key
}

type TencentKey struct {
	MapKey string `mapstructure:"mapkey" json:"mapkey" yaml:"mapkey"` // 腾讯地图key
}

type Config struct {
	Redis      Redis      `mapstructure:"redis" json:"redis" yaml:"redis"`
	Feie       Feie       `mapstructure:"feie" json:"feie" yaml:"feie"`
	Mysql      Mysql      `mapstructure:"mysql" json:"mysql" yaml:"mysql"`
	MysqlSlave MysqlSlave `mapstructure:"mysqlslave" json:"mysqlslave" yaml:"mysqlslave"`
	Mongo      Mongo      `mapstructure:"mongo" json:"mongo" yaml:"mongo"`
	Server     Server     `mapstructure:"server" json:"server" yaml:"server"`
	Web        Web        `mapstructure:"web" json:"web" yaml:"web"`
	CDN        CDN        `mapstructure:"cdn" json:"cdn" yaml:"cdn"`
	ZZCDN      ZZCDN      `mapstructure:"zzcdn" json:"zzcdn" yaml:"zzcdn"`
	Html2Image Html2Image `mapstructure:"html2image" json:"html2image" yaml:"html2image"`
	RabbitMQ   RabbitMQ   `mapstructure:"rabbitmq" json:"rabbitmq" yaml:"rabbitmq"`
	WebSocket  WebSocket  `mapstructure:"websocket" json:"websocket" yaml:"websocket"`
	// auto
	AutoCode Autocode `mapstructure:"autocode" json:"autocode" yaml:"autocode"`
	// 跨域配置
	Cors                 CORS             `mapstructure:"cors" json:"cors" yaml:"cors"`
	TenantManagement     TenantManagement `mapstructure:"tenantmanagement" json:"tenantmanagement" yaml:"tenantmanagement"`
	Alibaba              Alibaba          `mapstructure:"alibaba" json:"alibaba" yaml:"alibaba"`
	Ocr                  Ocr              `mapstructure:"ocr" json:"ocr" yaml:"ocr"`
	QYWXTemplateEndpoint string           `mapstructure:"qywx_template_endpoint" json:"qywx_template_endpoint" yaml:"qywx_template_endpoint"`
	FastGpt              FastGpt          `mapstructure:"fastGpt" json:"fastGpt" yaml:"fastGpt"`
	TencentKey           TencentKey       `mapstructure:"tencentkey" json:"tencentkey" yaml:"tencentkey"`
}

// 加载配置文件
func loadConfig() {

	args := os.Args
	if len(args) > 1 {
		file = args[1]
	}

	Viper(file)
	ToJson := func(v interface{}) string {
		bs, err := jsoniter.MarshalIndent(v, "", "   ")
		if err != nil {
			return ""
		}
		return string(bs)
	}

	fmt.Printf("[%s] "+time.Now().Format("2006-01-02 15:04:05")+"\r\n"+"%s"+"\r\n",
		"Config",
		fmt.Sprintf("%s\n%s\n%v\n%s\n",
			"Init Load Config Success",
			"-----------------------------------",
			ToJson(Conf),
			"-----------------------------------",
		),
	)
}

func Init() {
	loadConfig()
}

func Stop() {

}
