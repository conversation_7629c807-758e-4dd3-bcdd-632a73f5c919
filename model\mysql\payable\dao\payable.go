package dao

import (
	"context"
	"gorm.io/gorm"
	common "hcscm/common/payable"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/payable"
	structure "hcscm/structure/payable2"
	"hcscm/structure/should_collect_order"
	structure_base "hcscm/structure/system"
	"time"
)

func (d *Dao) GetPayableMaxDateCode(ctx context.Context, orderType common.OrderType, createDate string) (int64, error) {
	var maxCode int64
	query := d.db.Unscoped().Model(mysql.Payable{})
	err := query.Select("IFNULL(MAX(number),0) as max_code").Where("order_type = ? and Date(create_time)=?", orderType, createDate).Scan(&maxCode).Error
	if err != nil {
		return 0, err
	}
	return maxCode, nil
}

func (d *Dao) UpdatePayable(ctx context.Context, po *mysql.Payable) error {
	return d.db.WithContext(ctx).Omit(UpdateOmitFields...).Save(po).Error
}

// UpdateMultiOrderStatus 批量更新单据审核状态
func (d *Dao) UpdateMultiPayableStatus(ctx context.Context, ids []uint64, status common_system.OrderStatus, auditorId uint64, auditorName string, auditTime time.Time, payDate time.Time) error {
	updates := map[string]interface{}{
		"audit_status": status,
		"auditor_id":   auditorId,
		"auditor_name": auditorName,
		"audit_date":   auditTime,
		"pay_date":     payDate,
	}
	return d.db.WithContext(ctx).Model(mysql.Payable{}).Where("id in (?)", ids).Updates(updates).Error

}

func (d *Dao) UpdatePayablePrice(ctx context.Context, po *mysql.Payable) error {
	updates := map[string]interface{}{
		"discount_price": po.DiscountPrice,
		"reduce_price":   po.ReducePrice,
		"paid_price":     po.PaidPrice,
		"unpaid_price":   po.UnpaidPrice,
	}
	return d.db.WithContext(ctx).Model(po).Updates(updates).Error
}

func (d *Dao) QueryPayable(ctx context.Context, id uint64) (*mysql.Payable, error) {
	var po mysql.Payable
	if err := d.db.Where("id=?", id).First(&po).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &po, nil
}

func (d *Dao) QueryPayableByIds(ctx context.Context, ids []uint64) ([]*mysql.Payable, error) {
	var po []*mysql.Payable
	if err := d.db.Where("id in (?)", ids).Find(&po).Error; err != nil {
		return nil, err
	}
	return po, nil
}

func (d *Dao) QueryPayableByTypeAndSrcOrderId(ctx context.Context, orderType common.OrderType, srcOrderId uint64) (*mysql.Payable, error) {
	var po mysql.Payable
	if err := d.db.Where("order_type = ? and src_order_id=? and audit_status!= ? ", orderType, srcOrderId, common_system.OrderStatusVoided).First(&po).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &po, nil
}

func (d *Dao) FirstById(ctx context.Context, tx *mysql_base.Tx, orderType common.OrderType, srcOrderId uint64) (o *mysql.Payable, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
		r    mysql.Payable
	)
	cond.AddTableEqual(&r, "order_type", orderType)
	cond.AddTableEqual(&r, "src_order_id", srcOrderId)
	cond.AddTableNotEqual(&r, "audit_status", common_system.OrderStatusVoided)
	exist, err = d.mysqlFn.FirstByCond(tx, &r, cond)
	o = &r
	return
}

type QueryMultiPayableParams struct {
	SaleSystemId   uint64
	OrderNo        string
	OrderType      int
	DnfType        int
	DyeFactoryId   uint64
	DyeFactoryName string
}

func (d *Dao) QueryMultiPayable(ctx context.Context, p *QueryMultiPayableParams) ([]*mysql.Payable, error) {
	var po []*mysql.Payable
	query := d.db
	if p.SaleSystemId > 0 {
		query = query.Where("sale_system_id=?", p.SaleSystemId)
	}
	if p.OrderNo != "" {
		query = query.Where("order_no like ?", "%"+p.OrderNo+"%")
	}
	if p.OrderType > 0 {
		query = query.Where("order_type=?", p.OrderType)
	}
	if p.DnfType > 0 {
		query = query.Where("dnf_type=?", p.DnfType)
	}
	if p.DyeFactoryId > 0 {
		query = query.Where("dye_factory_id=?", p.DyeFactoryId)
	}
	if p.DyeFactoryName != "" {
		query = query.Where("dye_factory_name like ?", "%"+p.DyeFactoryName+"%")
	}

	if err := query.Find(&po).Error; err != nil {
		return nil, err
	}
	return po, nil
}

func MustFirstShouldPayOrderByID(tx *mysql_base.Tx, id uint64) (r mysql.Payable, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FindShouldPayOrderByIDs(tx *mysql_base.Tx, ids []uint64) (o mysql.PayableList, err error) {
	var (
		r    mysql.Payable
		cond = mysql_base.NewCondition()
		list []mysql.Payable
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func MustCreatePayableOrder(tx *mysql_base.Tx, r mysql.Payable) (o mysql.Payable, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdatePayableOrder(tx *mysql_base.Tx, r mysql.Payable) (o mysql.Payable, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func FirstShouldPayOrderBySrcID(tx *mysql_base.Tx, srcID uint64) (r mysql.Payable, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddTableEqual(&r, "src_order_id", srcID)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	return
}

func FindShouldPayOrderBySrcID(tx *mysql_base.Tx, srcID uint64) (data []mysql.Payable, err error) {
	var (
		cond = mysql_base.NewCondition()
		r    mysql.Payable
	)
	cond.AddTableEqual(&r, "src_order_id", srcID)
	err = mysql_base.FindByCond(tx, &r, &data, cond)
	return
}

func FindShouldPayOrderBySrcIDs(tx *mysql_base.Tx, srcID []uint64) (data []mysql.Payable, err error) {
	var (
		cond = mysql_base.NewCondition()
		r    mysql.Payable
	)
	cond.AddContainMatch("src_order_id", srcID)
	err = mysql_base.FindByCond(tx, &r, &data, cond)
	return
}

func SearchWaitAuditPayableOrder(tx *mysql_base.Tx, query structure_base.ListQuery) (payableList mysql.PayableList, count int, err error) {
	var (
		payable mysql.Payable
		cond    = mysql_base.NewCondition()
	)
	// shouldCollectOrder.BuildReadCond(tx.Context, cond)
	cond.AddTableEqual(&payable, "src_order_type", common.SrcOrderTypeSaleTransferOrder)
	cond.AddTableEqual(&payable, "delete_time", 0)
	cond.AddTableEqual(&payable, "audit_status", common_system.OrderStatusPendingAudit)
	count, err = mysql_base.SearchListGroupForPaging(tx, &payable, query, &payableList, cond)
	if err != nil {
		return
	}
	return
}

func SearchPayable(tx *mysql_base.Tx, q *structure.GetPayableListParams) (o mysql.PayableList, count int, err error) {
	var (
		r           mysql.Payable
		cond        = mysql_base.NewCondition()
		list        []mysql.Payable
		groupFields []string
	)
	// r.BuildReadCond(tx.Context, cond)
	// groupFields = []string{"supplier_id"}
	if q.SupplierId > 0 {
		cond.AddEqual("supplier_id", q.SupplierId)
	}
	if q.SaleSystemId > 0 {
		cond.AddEqual("sale_system_id", q.SaleSystemId)
	}
	if !q.Status.IsNil() {
		cond.AddContainMatch("audit_status", q.Status.ToInt())
	}
	if q.IsNeedUnPay {
		cond.AddRangeGT("unpaid_price", 0)
	}
	if q.OrderType > 0 {
		cond.AddEqual("order_type", q.OrderType)
	}
	// 开启时过滤未付金额为0的数据
	if q.UnContainZero {
		cond.AddTableNotEqual(&r, "unpaid_price", 0)
	}
	cond.AddSort("pay_date")
	// cond.addgr(r, "supplier_id")
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// MP销售报表-供应商维度
func FindSaleSimpleReportGroupSupplierList(tx *mysql_base.Tx, q *should_collect_order.QuerySaleSimpleReportListParam) (list mysql.SaleSimpleReportGroupSupplierList,
	total int, err error) {
	var (
		cond        = mysql_base.NewCondition()
		order       = mysql.Payable{}
		orderItem   = mysql.PayableItem{}
		data        = []mysql.SaleSimpleReportGroupSupplier{}
		groupFields []string
	)

	cond.AddTableJoiner(&order, &orderItem, "id", "order_id")
	if !q.OrderDateStart.IsYMDZero() && !q.OrderDateEnd.IsYMDZero() {
		cond.AddTableBetween(&order, "audit_date", q.OrderDateStart.StringYMD(), q.OrderDateEnd.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddTableEqual(&order, "audit_status", common_system.OrderStatusAudited)
	cond.AddTableEqual(&order, "order_type", common.OrderTypeProductPur)
	cond.AddTableContainMatch(&order, "src_order_type", []common.SrcOrderType{common.SrcOrderTypeSaleTransferOrder, common.SrcOrderTypeProductPurInOrder})
	groupFields = []string{"supplier_id"}
	cond.AddSort("-total_price")
	total, err = mysql_base.SearchListGroupForPaging(tx, &order, q, &data, cond, groupFields...)
	if err != nil {
		return
	}
	list = data
	return
}

func MustCreatePayable(tx *mysql_base.Tx, r mysql.Payable) (o mysql.Payable, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdatePayable(tx *mysql_base.Tx, r mysql.Payable) (o mysql.Payable, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeletePayable(tx *mysql_base.Tx, r mysql.Payable) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstPayableByID(tx *mysql_base.Tx, id uint64) (r mysql.Payable, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstPayableByID(tx *mysql_base.Tx, id uint64) (r mysql.Payable, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FindPayableByIDs(tx *mysql_base.Tx, ids []uint64) (o mysql.PayableList, err error) {
	var (
		r    mysql.Payable
		cond = mysql_base.NewCondition()
		list []mysql.Payable
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}
