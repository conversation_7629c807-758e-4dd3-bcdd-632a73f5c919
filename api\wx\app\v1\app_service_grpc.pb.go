// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.28.2
// source: api/wx/app/app_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	App_SyncUserInfo_FullMethodName           = "/wx.api.wx.app.v1.App/SyncUserInfo"
	App_SyncExternalContact_FullMethodName    = "/wx.api.wx.app.v1.App/SyncExternalContact"
	App_SyncGroupChat_FullMethodName          = "/wx.api.wx.app.v1.App/SyncGroupChat"
	App_GetQYWXUsers_FullMethodName           = "/wx.api.wx.app.v1.App/GetQYWXUsers"
	App_GetUserInfo_FullMethodName            = "/wx.api.wx.app.v1.App/GetUserInfo"
	App_GetGroupChatList_FullMethodName       = "/wx.api.wx.app.v1.App/GetGroupChatList"
	App_BindCustomers_FullMethodName          = "/wx.api.wx.app.v1.App/BindCustomers"
	App_GetBindCustomer_FullMethodName        = "/wx.api.wx.app.v1.App/GetBindCustomer"
	App_GetBindCustomers_FullMethodName       = "/wx.api.wx.app.v1.App/GetBindCustomers"
	App_GetCustomers_FullMethodName           = "/wx.api.wx.app.v1.App/GetCustomers"
	App_GetCustomersWithDetail_FullMethodName = "/wx.api.wx.app.v1.App/GetCustomersWithDetail"
	App_GetSignature_FullMethodName           = "/wx.api.wx.app.v1.App/GetSignature"
)

// AppClient is the client API for App service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AppClient interface {
	// 同步用户信息
	SyncUserInfo(ctx context.Context, in *SyncUserInfoRequest, opts ...grpc.CallOption) (*SyncUserInfoReply, error)
	// 同步客户（外部联系人）
	SyncExternalContact(ctx context.Context, in *SyncExternalContactRequest, opts ...grpc.CallOption) (*SyncExternalContactReply, error)
	// 同步群聊信息
	SyncGroupChat(ctx context.Context, in *SyncGroupChatRequest, opts ...grpc.CallOption) (*SyncGroupChatReply, error)
	// 获取企微用户列表
	GetQYWXUsers(ctx context.Context, in *GetQYWXUsersRequest, opts ...grpc.CallOption) (*GetQYWXUsersReply, error)
	// 获取企微用户详情
	GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoReply, error)
	// 获取群聊列表
	GetGroupChatList(ctx context.Context, in *GetGroupChatListRequest, opts ...grpc.CallOption) (*GetGroupChatListReply, error)
	// 绑定企微客户(删除原本的重新创建新的)
	BindCustomers(ctx context.Context, in *BindCustomersRequest, opts ...grpc.CallOption) (*BindCustomersReply, error)
	// 获取绑定企微客户
	GetBindCustomer(ctx context.Context, in *GetBindCustomerRequest, opts ...grpc.CallOption) (*GetBindCustomerReply, error)
	// 获取绑定企微客户
	GetBindCustomers(ctx context.Context, in *GetBindCustomersRequest, opts ...grpc.CallOption) (*GetBindCustomersReply, error)
	// 获取客户列表
	GetCustomers(ctx context.Context, in *GetCustomersRequest, opts ...grpc.CallOption) (*GetCustomersReply, error)
	// 获取客户列表（带详情）
	GetCustomersWithDetail(ctx context.Context, in *GetCustomersRequest, opts ...grpc.CallOption) (*GetCustomersWithDetailReply, error)
	GetSignature(ctx context.Context, in *GetSignatureRequest, opts ...grpc.CallOption) (*GetSignatureReply, error)
}

type appClient struct {
	cc grpc.ClientConnInterface
}

func NewAppClient(cc grpc.ClientConnInterface) AppClient {
	return &appClient{cc}
}

func (c *appClient) SyncUserInfo(ctx context.Context, in *SyncUserInfoRequest, opts ...grpc.CallOption) (*SyncUserInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncUserInfoReply)
	err := c.cc.Invoke(ctx, App_SyncUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) SyncExternalContact(ctx context.Context, in *SyncExternalContactRequest, opts ...grpc.CallOption) (*SyncExternalContactReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncExternalContactReply)
	err := c.cc.Invoke(ctx, App_SyncExternalContact_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) SyncGroupChat(ctx context.Context, in *SyncGroupChatRequest, opts ...grpc.CallOption) (*SyncGroupChatReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(SyncGroupChatReply)
	err := c.cc.Invoke(ctx, App_SyncGroupChat_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetQYWXUsers(ctx context.Context, in *GetQYWXUsersRequest, opts ...grpc.CallOption) (*GetQYWXUsersReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetQYWXUsersReply)
	err := c.cc.Invoke(ctx, App_GetQYWXUsers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetUserInfo(ctx context.Context, in *GetUserInfoRequest, opts ...grpc.CallOption) (*GetUserInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserInfoReply)
	err := c.cc.Invoke(ctx, App_GetUserInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetGroupChatList(ctx context.Context, in *GetGroupChatListRequest, opts ...grpc.CallOption) (*GetGroupChatListReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGroupChatListReply)
	err := c.cc.Invoke(ctx, App_GetGroupChatList_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) BindCustomers(ctx context.Context, in *BindCustomersRequest, opts ...grpc.CallOption) (*BindCustomersReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(BindCustomersReply)
	err := c.cc.Invoke(ctx, App_BindCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetBindCustomer(ctx context.Context, in *GetBindCustomerRequest, opts ...grpc.CallOption) (*GetBindCustomerReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBindCustomerReply)
	err := c.cc.Invoke(ctx, App_GetBindCustomer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetBindCustomers(ctx context.Context, in *GetBindCustomersRequest, opts ...grpc.CallOption) (*GetBindCustomersReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetBindCustomersReply)
	err := c.cc.Invoke(ctx, App_GetBindCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetCustomers(ctx context.Context, in *GetCustomersRequest, opts ...grpc.CallOption) (*GetCustomersReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomersReply)
	err := c.cc.Invoke(ctx, App_GetCustomers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetCustomersWithDetail(ctx context.Context, in *GetCustomersRequest, opts ...grpc.CallOption) (*GetCustomersWithDetailReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetCustomersWithDetailReply)
	err := c.cc.Invoke(ctx, App_GetCustomersWithDetail_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *appClient) GetSignature(ctx context.Context, in *GetSignatureRequest, opts ...grpc.CallOption) (*GetSignatureReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSignatureReply)
	err := c.cc.Invoke(ctx, App_GetSignature_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AppServer is the server API for App service.
// All implementations must embed UnimplementedAppServer
// for forward compatibility.
type AppServer interface {
	// 同步用户信息
	SyncUserInfo(context.Context, *SyncUserInfoRequest) (*SyncUserInfoReply, error)
	// 同步客户（外部联系人）
	SyncExternalContact(context.Context, *SyncExternalContactRequest) (*SyncExternalContactReply, error)
	// 同步群聊信息
	SyncGroupChat(context.Context, *SyncGroupChatRequest) (*SyncGroupChatReply, error)
	// 获取企微用户列表
	GetQYWXUsers(context.Context, *GetQYWXUsersRequest) (*GetQYWXUsersReply, error)
	// 获取企微用户详情
	GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoReply, error)
	// 获取群聊列表
	GetGroupChatList(context.Context, *GetGroupChatListRequest) (*GetGroupChatListReply, error)
	// 绑定企微客户(删除原本的重新创建新的)
	BindCustomers(context.Context, *BindCustomersRequest) (*BindCustomersReply, error)
	// 获取绑定企微客户
	GetBindCustomer(context.Context, *GetBindCustomerRequest) (*GetBindCustomerReply, error)
	// 获取绑定企微客户
	GetBindCustomers(context.Context, *GetBindCustomersRequest) (*GetBindCustomersReply, error)
	// 获取客户列表
	GetCustomers(context.Context, *GetCustomersRequest) (*GetCustomersReply, error)
	// 获取客户列表（带详情）
	GetCustomersWithDetail(context.Context, *GetCustomersRequest) (*GetCustomersWithDetailReply, error)
	GetSignature(context.Context, *GetSignatureRequest) (*GetSignatureReply, error)
	mustEmbedUnimplementedAppServer()
}

// UnimplementedAppServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAppServer struct{}

func (UnimplementedAppServer) SyncUserInfo(context.Context, *SyncUserInfoRequest) (*SyncUserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncUserInfo not implemented")
}
func (UnimplementedAppServer) SyncExternalContact(context.Context, *SyncExternalContactRequest) (*SyncExternalContactReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncExternalContact not implemented")
}
func (UnimplementedAppServer) SyncGroupChat(context.Context, *SyncGroupChatRequest) (*SyncGroupChatReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method SyncGroupChat not implemented")
}
func (UnimplementedAppServer) GetQYWXUsers(context.Context, *GetQYWXUsersRequest) (*GetQYWXUsersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetQYWXUsers not implemented")
}
func (UnimplementedAppServer) GetUserInfo(context.Context, *GetUserInfoRequest) (*GetUserInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserInfo not implemented")
}
func (UnimplementedAppServer) GetGroupChatList(context.Context, *GetGroupChatListRequest) (*GetGroupChatListReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupChatList not implemented")
}
func (UnimplementedAppServer) BindCustomers(context.Context, *BindCustomersRequest) (*BindCustomersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindCustomers not implemented")
}
func (UnimplementedAppServer) GetBindCustomer(context.Context, *GetBindCustomerRequest) (*GetBindCustomerReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBindCustomer not implemented")
}
func (UnimplementedAppServer) GetBindCustomers(context.Context, *GetBindCustomersRequest) (*GetBindCustomersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetBindCustomers not implemented")
}
func (UnimplementedAppServer) GetCustomers(context.Context, *GetCustomersRequest) (*GetCustomersReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomers not implemented")
}
func (UnimplementedAppServer) GetCustomersWithDetail(context.Context, *GetCustomersRequest) (*GetCustomersWithDetailReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetCustomersWithDetail not implemented")
}
func (UnimplementedAppServer) GetSignature(context.Context, *GetSignatureRequest) (*GetSignatureReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSignature not implemented")
}
func (UnimplementedAppServer) mustEmbedUnimplementedAppServer() {}
func (UnimplementedAppServer) testEmbeddedByValue()             {}

// UnsafeAppServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AppServer will
// result in compilation errors.
type UnsafeAppServer interface {
	mustEmbedUnimplementedAppServer()
}

func RegisterAppServer(s grpc.ServiceRegistrar, srv AppServer) {
	// If the following call pancis, it indicates UnimplementedAppServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&App_ServiceDesc, srv)
}

func _App_SyncUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).SyncUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_SyncUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).SyncUserInfo(ctx, req.(*SyncUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_SyncExternalContact_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncExternalContactRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).SyncExternalContact(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_SyncExternalContact_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).SyncExternalContact(ctx, req.(*SyncExternalContactRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_SyncGroupChat_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(SyncGroupChatRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).SyncGroupChat(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_SyncGroupChat_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).SyncGroupChat(ctx, req.(*SyncGroupChatRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetQYWXUsers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetQYWXUsersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetQYWXUsers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetQYWXUsers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetQYWXUsers(ctx, req.(*GetQYWXUsersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetUserInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetUserInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetUserInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetUserInfo(ctx, req.(*GetUserInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetGroupChatList_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupChatListRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetGroupChatList(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetGroupChatList_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetGroupChatList(ctx, req.(*GetGroupChatListRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_BindCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).BindCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_BindCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).BindCustomers(ctx, req.(*BindCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetBindCustomer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindCustomerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetBindCustomer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetBindCustomer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetBindCustomer(ctx, req.(*GetBindCustomerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetBindCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetBindCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetBindCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetBindCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetBindCustomers(ctx, req.(*GetBindCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetCustomers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetCustomers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetCustomers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetCustomers(ctx, req.(*GetCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetCustomersWithDetail_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetCustomersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetCustomersWithDetail(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetCustomersWithDetail_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetCustomersWithDetail(ctx, req.(*GetCustomersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _App_GetSignature_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSignatureRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AppServer).GetSignature(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: App_GetSignature_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AppServer).GetSignature(ctx, req.(*GetSignatureRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// App_ServiceDesc is the grpc.ServiceDesc for App service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var App_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "wx.api.wx.app.v1.App",
	HandlerType: (*AppServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "SyncUserInfo",
			Handler:    _App_SyncUserInfo_Handler,
		},
		{
			MethodName: "SyncExternalContact",
			Handler:    _App_SyncExternalContact_Handler,
		},
		{
			MethodName: "SyncGroupChat",
			Handler:    _App_SyncGroupChat_Handler,
		},
		{
			MethodName: "GetQYWXUsers",
			Handler:    _App_GetQYWXUsers_Handler,
		},
		{
			MethodName: "GetUserInfo",
			Handler:    _App_GetUserInfo_Handler,
		},
		{
			MethodName: "GetGroupChatList",
			Handler:    _App_GetGroupChatList_Handler,
		},
		{
			MethodName: "BindCustomers",
			Handler:    _App_BindCustomers_Handler,
		},
		{
			MethodName: "GetBindCustomer",
			Handler:    _App_GetBindCustomer_Handler,
		},
		{
			MethodName: "GetBindCustomers",
			Handler:    _App_GetBindCustomers_Handler,
		},
		{
			MethodName: "GetCustomers",
			Handler:    _App_GetCustomers_Handler,
		},
		{
			MethodName: "GetCustomersWithDetail",
			Handler:    _App_GetCustomersWithDetail_Handler,
		},
		{
			MethodName: "GetSignature",
			Handler:    _App_GetSignature_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wx/app/app_service.proto",
}
