package product

import (
	consts "hcscm/common/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFpmProcessOutOrderItemParam struct {
	structure_base.Param
	ItemFCData             AddFpmOutOrderItemFcParamList `json:"item_fc_data"`              // 出仓细码
	ParentId               uint64                        `json:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string                        `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string                        `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64                        `json:"quote_order_item_id"`       // 引用数据单物料那条id
	ProductId              uint64                        `json:"product_id"`                // 成品id
	ProductCode            string                        `json:"product_code"`              // 成品编号
	ProductName            string                        `json:"product_name"`              // 成品名称
	CustomerId             uint64                        `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64                        `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string                        `json:"product_color_code"`        // 成品名称
	ProductColorName       string                        `json:"product_color_name"`        // 成品名称
	ProductLevelId         uint64                        `json:"product_level_id"`          // 成品等级
	ProductWidth           string                        `json:"product_width"`             // 成品幅宽
	DyeFactoryColorCode    string                        `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                        `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductGramWeight      string                        `json:"product_gram_weight"`       // 成品克重
	ProductRemark          string                        `json:"product_remark"`            // 成品备注
	ProductCraft           string                        `json:"product_craft"`             // 成品工艺
	ProductIngredient      string                        `json:"product_ingredient"`        // 成品成分
	OutRoll                int                           `json:"out_roll"`                  // 出仓件数(件)，乘100存
	SumStockId             uint64                        `json:"sum_stock_id"`              // 汇总库存id
	TotalWeight            int                           `json:"total_weight"`              // 总数量(公斤)，乘10000存
	WeightError            int                           `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	SettleWeight           int                           `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64                        `json:"unit_id"`                   // 单位id
	OutLength              int                           `json:"out_length"`                // 出仓长度，乘100存
	Remark                 string                        `json:"remark"`                    // 备注
	ReturnRoll             int                           `json:"return_roll"`               // 退货匹数，乘100存
	ReturnWeight           int                           `json:"return_weight"`             // 退货数量，乘100存
	ReturnLength           int                           `json:"return_length"`             // 退货长度，乘100存
	DyeRoll                int                           `json:"dye_roll"`                  // 已排染匹数，乘100存
	DyeWeight              int                           `json:"dye_weight"`                // 已排染数量，乘100存
	ArrangeItemId          uint64                        `json:"arrange_item_id"`           // 配布成品信息id
	SumStockRoll           int                           `json:"sum_stock_roll"`            // 汇总库存成品匹数
	SumStockWeight         int                           `json:"sum_stock_weight"`          // 汇总库存成品数量
	SumStockLength         int                           `json:"sum_stock_length"`          // 汇总库存成品长度
}

type AddFpmProcessOutOrderItemParamList []AddFpmProcessOutOrderItemParam

func (r AddFpmProcessOutOrderItemParamList) Adjust() {

}

type AddFpmProcessOutOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// item 使用
func (r AddFpmProcessOutOrderItemParam) GetTotalWPL() (
	totalWeight int, totalPrice int, totalLength int,
	weightError int, settleWeightError int, totalWeightError int,
	totalPaperTubeWeight int, totalSettleWeight int, totalActuallyWeight int) {
	// if inType == iconst.WarehouseGoodInTypePurchase {
	for _, v2 := range r.ItemFCData {
		totalWeight += v2.BaseUnitWeight
		totalLength += v2.Length
		weightError += v2.WeightError
		settleWeightError += v2.SettleErrorWeight
		totalWeightError += v2.WeightError + v2.SettleErrorWeight // 总空差=码单空差+结算空差
		totalPaperTubeWeight += v2.PaperTubeWeight
	}
	totalSettleWeight = totalWeight - totalWeightError
	totalActuallyWeight = totalWeight - weightError

	// weightPrice := tools.DecimalDiv(float64(totalSettleWeight*r.UnitPrice), vars.WeightUnitPriceMult)
	// // 2024-09-10长度修改为10000进位
	// lenPrice := tools.DecimalDiv(float64(totalLength*r.LengthUnitPrice), vars.LengthUnitPriceMult)
	// // lenPrice := tools.DecimalDiv(float64(totalLength*r.LengthUnitPrice), 10000)
	// tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
	// totalPrice = tempPrice + r.OtherPrice
	// }
	return
}

func (r *AddFpmProcessOutOrderItemParam) Swap2AddProcessItemParam(req GetFpmArrangeOrderItemData) {
	r.ParentId = req.ParentId
	r.ParentOrderNo = req.ParentOrderNo
	r.ProductId = req.ProductId
	r.ProductCode = req.ProductCode
	r.ProductName = req.ProductName
	r.CustomerId = req.CustomerId
	r.ProductColorId = req.ProductColorId
	r.ProductColorCode = req.ProductColorCode
	r.ProductColorName = req.ProductColorName
	r.ProductLevelId = req.ProductLevelId
	r.ProductWidth = req.ProductWidth
	r.DyeFactoryColorCode = req.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = req.DyeFactoryDyelotNumber
	r.ProductGramWeight = req.ProductGramWeight
	r.ProductRemark = req.ProductRemark
	r.ProductCraft = req.ProductCraft
	r.ProductIngredient = req.ProductIngredient
	r.OutRoll = req.ArrangeRoll
	r.SumStockId = req.SumStockId
	r.TotalWeight = req.ArrangeWeight
	r.WeightError = req.WeightError
	r.SettleWeight = req.SettleWeight
	r.UnitId = req.UnitId
	r.OutLength = req.ArrangeLength
	r.Remark = req.Remark
	r.ArrangeItemId = req.Id
	r.SumStockRoll = req.SumStockRoll
	r.SumStockWeight = req.SumStockWeight
	r.SumStockLength = req.SumStockLength

}

type UpdateFpmProcessOutOrderItemParam struct {
	structure_base.Param
	Id                     uint64 `json:"id"`
	ParentId               uint64 `json:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64 `json:"quote_order_item_id"`       // 引用数据单物料那条id
	ProductId              uint64 `json:"product_id"`                // 成品id
	ProductCode            string `json:"product_code"`              // 成品编号
	ProductName            string `json:"product_name"`              // 成品名称
	CustomerId             uint64 `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64 `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string `json:"product_color_code"`        // 成品名称
	ProductColorName       string `json:"product_color_name"`        // 成品名称
	ProductLevelId         uint64 `json:"product_level_id"`          // 成品等级
	ProductWidth           string `json:"product_width"`             // 成品幅宽
	DyeFactoryColorCode    string `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductGramWeight      string `json:"product_gram_weight"`       // 成品克重
	ProductRemark          string `json:"product_remark"`            // 成品备注
	ProductCraft           string `json:"product_craft"`             // 成品工艺
	ProductIngredient      string `json:"product_ingredient"`        // 成品成分
	OutRoll                int    `json:"out_roll"`                  // 出仓件数(件)，乘100存
	SumStockId             uint64 `json:"sum_stock_id"`              // 汇总库存id
	TotalWeight            int    `json:"total_weight"`              // 总数量(公斤)，乘10000存
	WeightError            int    `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	SettleWeight           int    `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64 `json:"unit_id"`                   // 单位id
	OutLength              int    `json:"out_length"`                // 出仓长度，乘100存
	Remark                 string `json:"remark"`                    // 备注
	ReturnRoll             int    `json:"return_roll"`               // 退货匹数，乘100存
	ReturnWeight           int    `json:"return_weight"`             // 退货数量，乘100存
	ReturnLength           int    `json:"return_length"`             // 退货长度，乘100存
	DyeRoll                int    `json:"dye_roll"`                  // 已排染匹数，乘100存
	DyeWeight              int    `json:"dye_weight"`                // 已排染数量，乘100存
}

type UpdateFpmProcessOutOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type DeleteFpmProcessOutOrderItemParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmProcessOutOrderItemData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type GetFpmProcessOutOrderItemQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmProcessOutOrderItemListQuery struct {
	structure_base.ListQuery
	ParentId               uint64 `form:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string `form:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string `form:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64 `form:"quote_order_item_id"`       // 引用数据单物料那条id
	ProductId              uint64 `form:"product_id"`                // 成品id
	ProductCode            string `form:"product_code"`              // 成品编号
	ProductName            string `form:"product_name"`              // 成品名称
	CustomerId             uint64 `form:"customer_id"`               // 所属客户id
	ProductColorId         uint64 `form:"product_color_id"`          // 成品颜色id
	ProductColorCode       string `form:"product_color_code"`        // 成品名称
	ProductColorName       string `form:"product_color_name"`        // 成品名称
	ProductLevelId         uint64 `form:"product_level_id"`          // 成品等级
	ProductWidth           string `form:"product_width"`             // 成品幅宽
	DyeFactoryColorCode    string `form:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `form:"dye_factory_dyelot_number"` // 染厂缸号
	ProductGramWeight      string `form:"product_gram_weight"`       // 成品克重
	ProductRemark          string `form:"product_remark"`            // 成品备注
	ProductCraft           string `form:"product_craft"`             // 成品工艺
	ProductIngredient      string `form:"product_ingredient"`        // 成品成分
	OutRoll                int    `form:"out_roll"`                  // 出仓件数(件)，乘100存
	SumStockId             uint64 `form:"sum_stock_id"`              // 汇总库存id
	TotalWeight            int    `form:"total_weight"`              // 总数量(公斤)，乘10000存
	WeightError            int    `form:"weight_error"`              // 空差数量(公斤)，乘10000存
	SettleWeight           int    `form:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64 `form:"unit_id"`                   // 单位id
	OutLength              int    `form:"out_length"`                // 出仓长度，乘100存
	Remark                 string `form:"remark"`                    // 备注
	ReturnRoll             int    `form:"return_roll"`               // 退货匹数，乘100存
	ReturnWeight           int    `form:"return_weight"`             // 退货数量，乘100存
	ReturnLength           int    `form:"return_length"`             // 退货长度，乘100存
	DyeRoll                int    `form:"dye_roll"`                  // 已排染匹数，乘100存
	DyeWeight              int    `form:"dye_weight"`                // 已排染数量，乘100存

	// 列表查询条件
	JudgeUseByDye         bool
	SaleSystemId          uint64                      `form:"sale_system_id"`           // 营销体系id，必填
	ProcessUnitId         uint64                      `form:"process_unit_id"`          // 加工单位id
	WarehouseOutTimeBegin tools.QueryTime             `form:"warehouse_out_time_begin"` // 出仓开始时间
	WarehouseOutTimeEnd   tools.QueryTime             `form:"warehouse_out_time_end"`   // 出仓结束时间
	WarehouseId           uint64                      `form:"warehouse_id"`             // 仓库id
	ProcessOrderNo        string                      `form:"process_order_no"`         // 加工出仓单号
	OutOrderType          consts.WarehouseGoodOutType `form:"out_order_type"`           // 出仓类型
	ListIDs               []uint64
}

func (r GetFpmProcessOutOrderItemListQuery) Adjust() {

}

type GetFpmProcessOutOrderItemData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ItemFCData             GetFpmOutOrderItemFcDataList `json:"item_fc_data"`              // 出仓细码
	ParentId               uint64                       `json:"parent_id"`                 // 父id（单号id）
	ParentName             string                       `json:"parent_name"`               // 父名称
	ParentOrderNo          string                       `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string                       `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64                       `json:"quote_order_item_id"`       // 引用数据单物料那条id
	QuoteOrderItemName     string                       `json:"quote_order_item_name"`     // 引用数据单物料那条名称
	ProductId              uint64                       `json:"product_id"`                // 成品id
	ProductName            string                       `json:"product_name"`              // 成品名称
	ProductCode            string                       `json:"product_code"`              // 成品编号
	CustomerId             uint64                       `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64                       `json:"product_color_id"`          // 成品颜色id
	ProductColorName       string                       `json:"product_color_name"`        // 成品颜色名称
	ProductColorCode       string                       `json:"product_color_code"`        // 成品颜色编号
	ProductLevelId         uint64                       `json:"product_level_id"`          // 成品等级
	ProductWidth           string                       `json:"product_width"`             // 成品幅宽
	DyeFactoryColorCode    string                       `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                       `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductGramWeight      string                       `json:"product_gram_weight"`       // 成品克重
	ProductRemark          string                       `json:"product_remark"`            // 成品备注
	ProductCraft           string                       `json:"product_craft"`             // 成品工艺
	ProductIngredient      string                       `json:"product_ingredient"`        // 成品成分
	OutRoll                int                          `json:"out_roll"`                  // 出仓件数(件)，乘100存
	SumStockId             uint64                       `json:"sum_stock_id"`              // 汇总库存id
	SumStockName           string                       `json:"sum_stock_name"`            // 汇总库存名称
	TotalWeight            int                          `json:"total_weight"`              // 总数量(公斤)，乘10000存
	WeightError            int                          `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	SettleWeight           int                          `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	SettleErrorWeight      int                          `json:"settle_error_weight"`       // 结算空差
	UnitId                 uint64                       `json:"unit_id"`                   // 单位id
	OutLength              int                          `json:"out_length"`                // 出仓长度，乘100存
	Remark                 string                       `json:"remark"`                    // 备注
	ReturnRoll             int                          `json:"return_roll"`               // 退货匹数，乘100存
	ReturnWeight           int                          `json:"return_weight"`             // 退货数量，乘100存
	ReturnLength           int                          `json:"return_length"`             // 退货长度，乘100存
	DyeRoll                int                          `json:"dye_roll"`                  // 已排染匹数，乘100存
	DyeWeight              int                          `json:"dye_weight"`                // 已排染数量，乘100存
	ArrangeItemId          int                          `json:"arrange_item_id"`           // 配布成品信息id

	// 转义
	UnitName         string `json:"unit_name"`          // 计量单位名称
	CustomerName     string `json:"customer_name"`      // 所属客户name
	ProductLevelName string `json:"product_level_name"` // 成品等级name
	// 库存信息
	SumStockRoll   int `json:"sum_stock_roll"`   // 汇总库存成品匹数
	SumStockWeight int `json:"sum_stock_weight"` // 汇总库存成品数量
	SumStockLength int `json:"sum_stock_length"` // 汇总库存成品长度
}

type GetFpmProcessOutOrderItemDataUseByDyeList []GetFpmProcessOutOrderItemDataUseByDye

func (g GetFpmProcessOutOrderItemDataUseByDyeList) Adjust() {

}

type GetFpmProcessOutOrderItemDataUseByDye struct {
	GetFpmProcessOutOrderItemData
	WarehouseId      uint64       `json:"warehouse_id"`       // 仓库id
	WarehouseName    string       `json:"warehouse_name"`     // 仓库name
	ProcessUnitId    uint64       `json:"process_unit_id"`    // 加工单位id
	ProcessUnitName  string       `json:"process_unit_name"`  // 加工单位名称
	WarehouseOutTime tools.MyTime `json:"warehouse_out_time"` // 出仓时间
	WaitDyeRoll      int          `json:"wait_dye_roll"`      // 未排染匹数
	WaitDyeWeight    int          `json:"wait_dye_weight"`    // 未排染数量
}

type GetFpmProcessOutOrderItemDataList []GetFpmProcessOutOrderItemData

func (g GetFpmProcessOutOrderItemDataList) Adjust() {

}

type GetFpmProcessOutOrderItemDropdownData struct {
	structure_base.RecordData
	ParentId               uint64 `json:"parent_id"`                 // 父id（单号id）
	ParentName             string `json:"parent_name"`               // 父名称
	ParentOrderNo          string `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64 `json:"quote_order_item_id"`       // 引用数据单物料那条id
	QuoteOrderItemName     string `json:"quote_order_item_name"`     // 引用数据单物料那条名称
	ProductId              uint64 `json:"product_id"`                // 成品id
	ProductName            string `json:"product_name"`              // 成品名称
	ProductCode            string `json:"product_code"`              // 成品编号
	CustomerId             uint64 `json:"customer_id"`               // 所属客户id
	CustomerName           string `json:"customer_name"`             // 所属客户名称
	ProductColorId         uint64 `json:"product_color_id"`          // 成品颜色id
	ProductColorName       string `json:"product_color_name"`        // 成品颜色名称
	ProductColorCode       string `json:"product_color_code"`        // 成品名称
	ProductLevelId         uint64 `json:"product_level_id"`          // 成品等级
	ProductLevelName       string `json:"product_level_name"`        // 成品等级名称
	ProductWidth           string `json:"product_width"`             // 成品幅宽
	DyeFactoryColorCode    string `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductGramWeight      string `json:"product_gram_weight"`       // 成品克重
	ProductRemark          string `json:"product_remark"`            // 成品备注
	ProductCraft           string `json:"product_craft"`             // 成品工艺
	ProductIngredient      string `json:"product_ingredient"`        // 成品成分
	OutRoll                int    `json:"out_roll"`                  // 出仓件数(件)，乘100存
	SumStockId             uint64 `json:"sum_stock_id"`              // 汇总库存id
	SumStockName           string `json:"sum_stock_name"`            // 汇总库存名称
	TotalWeight            int    `json:"total_weight"`              // 总数量(公斤)，乘10000存
	WeightError            int    `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	SettleWeight           int    `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64 `json:"unit_id"`                   // 单位id
	UnitName               string `json:"unit_name"`                 // 单位名称
	OutLength              int    `json:"out_length"`                // 出仓长度，乘100存
	Remark                 string `json:"remark"`                    // 备注
	ReturnRoll             int    `json:"return_roll"`               // 退货匹数，乘100存
	ReturnWeight           int    `json:"return_weight"`             // 退货数量，乘100存
	ReturnLength           int    `json:"return_length"`             // 退货长度，乘100存
	DyeRoll                int    `json:"dye_roll"`                  // 已排染匹数，乘100存
	DyeWeight              int    `json:"dye_weight"`                // 已排染数量，乘100存
	ArrangeItemId          int    `json:"arrange_item_id"`           // 配布成品信息id
}

type GetFpmProcessOutOrderItemDropdownDataList []GetFpmProcessOutOrderItemDropdownData

func (g GetFpmProcessOutOrderItemDropdownDataList) Adjust() {

}
