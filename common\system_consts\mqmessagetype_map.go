package common

func GetMqMessageTypeMap() (r map[MqMessageType]string) {
	l := []MqMessageType{MqMessageTypeSalePlanOrder, MqMessageTypeModifyProductStock, MqMessageTypeSalePriceAdjustOrder, MqMessageTypeUserAccess, MqMessageTypeSalePriceAdjustOrderWait, MqMessageTypeAliPay, MqMessageTypeTenantManagementExpire, MqMessageTypeTenantPackageExpire, MqMessageTypeTenantPackageCancel, MqMessageTypeNewTenantNotify, MqMessageTypeCustomerFeedback, MqMessageTypeOcrCodePackageExpire, MqMessageTypeSearchImageUpload}
	r = make(map[MqMessageType]string)
	for _, k := range l {
		r[k] = k.String()
	}
	return r
}
func GetMqMessageTypeReverseMap() (r map[string]MqMessageType) {
	l := []MqMessageType{MqMessageTypeSalePlanOrder, MqMessageTypeModifyProductStock, MqMessageTypeSalePriceAdjustOrder, MqMessageTypeUserAccess, MqMessageTypeSalePriceAdjustOrderWait, MqMessageTypeAliPay, MqMessageTypeTenantManagementExpire, MqMessageTypeTenantPackageExpire, MqMessageTypeTenantPackageCancel, MqMessageTypeNewTenantNotify, MqMessageTypeCustomerFeedback, MqMessageTypeOcrCodePackageExpire, MqMessageTypeSearchImageUpload}
	r = make(map[string]MqMessageType)
	for _, k := range l {
		r[k.String()] = k
	}
	return r
}
func GetMqMessageTypeReverseIntMap() (r map[string]int) {
	l := []MqMessageType{MqMessageTypeSalePlanOrder, MqMessageTypeModifyProductStock, MqMessageTypeSalePriceAdjustOrder, MqMessageTypeUserAccess, MqMessageTypeSalePriceAdjustOrderWait, MqMessageTypeAliPay, MqMessageTypeTenantManagementExpire, MqMessageTypeTenantPackageExpire, MqMessageTypeTenantPackageCancel, MqMessageTypeNewTenantNotify, MqMessageTypeCustomerFeedback, MqMessageTypeOcrCodePackageExpire, MqMessageTypeSearchImageUpload}
	r = make(map[string]int)
	for _, k := range l {
		r[k.String()] = int(k)
	}
	return r
}

func (t MqMessageType) Check() bool {
	l := []MqMessageType{MqMessageTypeSalePlanOrder, MqMessageTypeModifyProductStock, MqMessageTypeSalePriceAdjustOrder, MqMessageTypeUserAccess, MqMessageTypeSalePriceAdjustOrderWait, MqMessageTypeAliPay, MqMessageTypeTenantManagementExpire, MqMessageTypeTenantPackageExpire, MqMessageTypeTenantPackageCancel, MqMessageTypeNewTenantNotify, MqMessageTypeCustomerFeedback, MqMessageTypeOcrCodePackageExpire, MqMessageTypeSearchImageUpload}
	for i := range l {
		if l[i] == t {
			return true
		}
	}
	return false
}
