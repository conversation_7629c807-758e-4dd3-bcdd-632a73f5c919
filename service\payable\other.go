package payable

import (
	"context"
	"fmt"
	"hcscm/aggs/payable"
	"hcscm/aggs/payable_cache"
	aggsProduct "hcscm/aggs/product"
	"hcscm/common/errors"
	"hcscm/common/lock_key"
	commonProduct "hcscm/common/product"
	commonService "hcscm/domain/common/service"
	commonImpl "hcscm/domain/common/service/impl"
	"hcscm/domain/payable/payable_entity"
	"hcscm/domain/payable/payable_iface"
	saleSys "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/system"
	modelProduct "hcscm/model/mysql/product"
	mysqlProduct "hcscm/model/mysql/product/dao"
	"hcscm/model/redis"
	structure "hcscm/structure/payable2"
	structureProduct "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/lock"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"time"
)

type PayableOtherService struct {
	orderRedisLock lock.LockIFace
	isCache        bool
}

func NewPayableOtherService(ctx context.Context, isCache bool) *PayableOtherService {
	backoff := lock.NewExponentialBackoff(
		time.Duration(20)*time.Millisecond,
		time.Duration(1000)*time.Millisecond,
	)
	redisLock := lock.NewRedisLock(redis.GetClient(ctx), lock.WithBackoff(backoff))
	return &PayableOtherService{
		orderRedisLock: redisLock,
		isCache:        isCache,
	}
}

// AddOrder 添加
func (s *PayableOtherService) AddOrder(ctx context.Context, req *structure.AddPayableOtherParams) (id uint64, err error) {
	// 获取用户ID
	userId := metadata.GetUserId(ctx)

	// 创建锁 防止用户并发下单
	var orderRedisLockInstance lock.LockInstanceIFace = lock.NewLockInstance(s.orderRedisLock)
	if err = orderRedisLockInstance.MustSetRetry(ctx, lock_key.PayableOther(userId)); err != nil {
		if err == lock.ErrLockFail {
			err = errors.NewError(errors.ErrCodeOrderBusy)
			return
		}
		err = errors.NewCustomError(errors.ErrCodeSystemError, err.Error())
		return
	}
	defer func() {
		_ = orderRedisLockInstance.Release(ctx)
	}()

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	var (
		repo               payable_iface.PayableOtherRepoIFace      = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
		cacheRepo          payable_iface.PayableOtherCacheRepoIFace = payable_cache.NewPayableOtherCacheRepo(redis.GetClient(ctx))
		orderService       commonService.OrderServiceIFace          = commonImpl.NewOrderService()
		orderPrefix        mysql.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = saleSys.NewSaleSystemClient()
		saleSysData        = saleSys.Res{}
	)

	// 获取对象
	order, err := req.ToOrderDO(ctx)
	if err != nil {
		err = errors.NewCustomError(errors.ErrCodeBusinessParameter, err.Error())
		return
	}

	// TODO 查询来源单据
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysql.GetFirstOrderPrefix(tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.OtherPayableOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.OtherPayableOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}

	// 生成并设置订单号
	if err = orderService.GenAndSetOrderNum(ctx, prefix, dateFormat, numLength, order, repo, cacheRepo); err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeServerInternalError, "GenAndSetOrderNum err: "+err.Error()))
		return
	}

	// 保存
	if err = repo.AddPayableOther(ctx, order); err != nil {
		return
	}

	return order.Id, nil
}

// UpdateOrder 更新
func (s *PayableOtherService) UpdateOrder(ctx context.Context, req *structure.UpdatePayableOtherParams) error {
	var err error
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	var (
		repo payable_iface.PayableOtherRepoIFace = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
	)

	// 获取对象
	order, err := req.ToOrderDO(ctx)
	if err != nil {
		err = errors.NewCustomError(errors.ErrCodeBusinessParameter, err.Error())
		return err
	}

	// 校验
	oldOrder, err := repo.GetPayableOtherWithoutItems(ctx, order.Id)
	if err != nil {
		return err
	}
	if oldOrder == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return err
	}
	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := oldOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return err
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return err
	}

	// 更新
	err = repo.UpdatePayableOther(ctx, order)
	if err != nil {
		return err
	}

	return nil
}

// PassOrder 审核
func (s *PayableOtherService) PassOrder(ctx context.Context, id uint64) error {
	var (
		err                  error
		order                *payable_entity.PayableOther
		fpmOtherInOrderItems modelProduct.FpmInOrderItemList
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		if err == nil {
			// changeOrderStatusChangeCallback(ctx, order, srcOrder)
		}
	}()

	var (
		repo             payable_iface.PayableOtherRepoIFace = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
		fpmCostPriceRepo                                     = aggsProduct.NewFpmCostPriceRepo(ctx, tx, false)
	)

	// 获取订单
	order, err = repo.GetPayableOther(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return err
	}

	fpmOtherInOrderItemIds := make([]uint64, 0, len(order.Items))
	for _, item := range order.Items {
		fpmOtherInOrderItemIds = append(fpmOtherInOrderItemIds, item.SrcId)
	}
	fpmOtherInOrderItems, err = mysqlProduct.FindFpmInOrderItemByIDs(tx, fpmOtherInOrderItemIds)
	if err != nil {
		return err
	}
	for _, item := range order.Items {
		var (
			fpmCostPriceData *structureProduct.GetFpmCostPriceData
			exist            bool
		)
		fpmOtherInOrderItem := fpmOtherInOrderItems.Pick(item.SrcId)
		fpmCostPriceData, exist, err = fpmCostPriceRepo.GetV2(ctx, fpmOtherInOrderItem.ProductId, fpmOtherInOrderItem.ProductColorId,
			order.SrcOrderId, fpmOtherInOrderItem.DyeFactoryDyelotNumber, commonProduct.WarehouseGoodInTypeOther)
		if err != nil {
			return err
		}
		param := structureProduct.AddFpmCostPriceParam{
			CommonFpmCostPrice: structureProduct.CommonFpmCostPrice{
				BuoyantWeightPrice: item.Price * vars.WeightUnitPriceMult / (fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError),
				BuoyantWeight:      fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError,
				NetWeightPrice:     item.Price * vars.WeightUnitPriceMult / (fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError),
				NetWeight:          fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError,
			}}
		// 更新库存成本
		if !exist {
			_, err = fpmCostPriceRepo.Add(ctx, &structureProduct.AddFpmCostPriceParam{
				CommonFpmCostPrice: structureProduct.CommonFpmCostPrice{
					ProductId:           fpmOtherInOrderItem.ProductId,
					ColourId:            fpmOtherInOrderItem.ProductColorId,
					DyelotNumber:        fpmOtherInOrderItem.DyeFactoryDyelotNumber,
					WarehouseGoodInType: commonProduct.WarehouseGoodInTypeOther,
					WarehouseGoodInId:   order.SrcOrderId,
					CostCalculateDate:   tools.QueryTime(time.Now().Format(time.DateOnly)),
					BuoyantWeightPrice:  param.BuoyantWeightPrice,
					BuoyantWeight:       param.BuoyantWeight,
					NetWeightPrice:      param.NetWeightPrice,
					NetWeight:           param.NetWeight,
					Unit:                fpmOtherInOrderItem.UnitId,
				},
			})
		} else {
			err = fpmCostPriceRepo.Update(ctx, &structureProduct.UpdateFpmCostPriceParam{
				AddFpmCostPriceParam: param,
				Id:                   fpmCostPriceData.Id,
				FpmPurPass:           true,
			})
		}
		if err != nil {
			return err
		}
		// 更新应付单成本
		for _, payableItem := range order.Items {
			if fpmOtherInOrderItem.Id == payableItem.SrcId {
				payableItem.BuoyantWeightPrice = param.BuoyantWeightPrice
			}
		}
	}

	err = repo.UpdatePayableOther(ctx, order)
	if err != nil {
		return err
	}

	// TODO 校验来源单据

	// 处理下推单据直接审核时没有时间格式问题及补充
	if order.PayDate.IsZero() {
		// 默认当天
		order.PayDate = time.Now()
	}

	// 审核
	err = order.Pass(ctx)
	if err != nil {
		return err
	}

	// 更新状态
	err = repo.UpdateMultiPayableOtherStatus(ctx, []*payable_entity.PayableOther{order})
	if err != nil {
		return err
	}

	return nil
}

// RejectOrder 驳回
func (s *PayableOtherService) RejectOrder(ctx context.Context, id uint64) error {
	var err error
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	var (
		repo payable_iface.PayableOtherRepoIFace = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
	)

	// 获取订单
	order, err := repo.GetPayableOtherWithoutItems(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return err
	}

	// 驳回
	err = order.Reject(ctx)
	if err != nil {
		return err
	}

	// 更新状态
	err = repo.UpdateMultiPayableOtherStatus(ctx, []*payable_entity.PayableOther{order})
	if err != nil {
		return err
	}
	return nil
}

// CancelOrder 消审
func (s *PayableOtherService) CancelOrder(ctx context.Context, id uint64) error {
	var (
		err                  error
		fpmOtherInOrderItems modelProduct.FpmInOrderItemList
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		if err == nil {
			// changeOrderStatusChangeCallback(ctx, order)
		}
	}()

	var (
		repo             payable_iface.PayableOtherRepoIFace = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
		fpmCostPriceRepo                                     = aggsProduct.NewFpmCostPriceRepo(ctx, tx, false)
	)

	// 获取订单
	order, err := repo.GetPayableOther(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return err
	}

	// TODO逻辑

	salveTx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	err = repo.Judge2Wait(ctx, salveTx, id)
	if err != nil {
		return err
	}

	fpmOtherInOrderItemIds := make([]uint64, 0, len(order.Items))
	for _, item := range order.Items {
		fpmOtherInOrderItemIds = append(fpmOtherInOrderItemIds, item.SrcId)
	}
	fpmOtherInOrderItems, err = mysqlProduct.FindFpmInOrderItemByIDs(tx, fpmOtherInOrderItemIds)
	if err != nil {
		return err
	}
	for _, item := range order.Items {
		var (
			fpmCostPriceData *structureProduct.GetFpmCostPriceData
			exist            bool
		)
		fpmOtherInOrderItem := fpmOtherInOrderItems.Pick(item.SrcId)
		fpmCostPriceData, exist, err = fpmCostPriceRepo.GetV2(ctx, fpmOtherInOrderItem.ProductId, fpmOtherInOrderItem.ProductColorId, order.SrcOrderId,
			fpmOtherInOrderItem.DyeFactoryDyelotNumber, commonProduct.WarehouseGoodInTypeOther)
		if err != nil {
			return err
		} // 更新库存成本
		param := structureProduct.AddFpmCostPriceParam{
			CommonFpmCostPrice: structureProduct.CommonFpmCostPrice{
				BuoyantWeightPrice: item.Price * vars.WeightUnitPriceMult / (fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError),
				BuoyantWeight:      fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError,
				NetWeightPrice:     item.Price * vars.WeightUnitPriceMult / (fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError),
				NetWeight:          fpmOtherInOrderItem.SettleWeight + fpmOtherInOrderItem.WeightError,
			}}
		if exist {
			err = fpmCostPriceRepo.Update(ctx, &structureProduct.UpdateFpmCostPriceParam{
				AddFpmCostPriceParam: param,
				Id:                   fpmCostPriceData.Id,
			})
			if err != nil {
				return err
			}
		}

		// 更新应付单成本
		for _, payableItem := range order.Items {
			if fpmOtherInOrderItem.Id == payableItem.SrcId {
				payableItem.BuoyantWeightPrice = 0
			}
		}
	}

	err = repo.UpdatePayableOther(ctx, order)
	if err != nil {
		return err
	}

	// 消审
	err = order.Wait(ctx)
	if err != nil {
		return err
	}

	// 更新状态
	err = repo.UpdateMultiPayableOtherStatus(ctx, []*payable_entity.PayableOther{order})
	if err != nil {
		return err
	}

	return nil
}

// VoidOrder 作废
func (s *PayableOtherService) VoidOrder(ctx context.Context, id uint64) error {
	var err error
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	var (
		repo payable_iface.PayableOtherRepoIFace = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
	)
	// 获取订单
	order, err := repo.GetPayableOtherWithoutItems(ctx, id)
	if err != nil {
		return err
	}
	if order == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return err
	}

	// 作废
	err = order.Cancel(ctx)
	if err != nil {
		return err
	}

	// 更新状态
	err = repo.UpdateMultiPayableOtherStatus(ctx, []*payable_entity.PayableOther{order})
	if err != nil {
		return err
	}

	return nil
}

//
// func changeOrderStatusChangeCallback(ctx context.Context, order *dnf_entity.PayableOther, srcOrder *dnf_entity.DyeingAndFinishingNoticeOrder) {
// 	if order == nil {
// 		return
// 	}
//
// 	// 发送库存盘点单状态变更的消息
// 	newCtx := tools.DetachedContext(ctx)
// 	go tools.PanicHandler(func(ctx context.Context) {
// 		msg := &structure.DNFChangeOrderStatusChangeMsg{
// 			OrderId:      order.Id,
// 			SrcOrderId:   order.SrcOrderId,
// 			SrcOrderType: order.SrcOrderType,
// 			SrcDnfType:   order.SrcDnfType,
// 			ChangeTime:   time.Now(),
// 			Items:        nil,
// 		}
// 		for _, item := range order.Items {
// 			srcItem := srcOrder.GetItem(item.SrcId)
// 			if srcItem == nil {
// 				fmt.Println("changeOrderStatusChangeCallback err: ", "找不到来源item")
// 				return
// 			}
// 			msgItem := &structure.DNFChangeOrderStatusChangeMsgItem{
// 				UnitId:          srcOrder.DyeFactoryId,
// 				FinishProductId: srcItem.FinishProductId,
// 				CustomerId:      srcItem.CustomerId,
// 				ColorId:         srcItem.ColorId,
// 				PieceCount:      item.ChangePieceCount,
// 				Weight:          item.ChangeWeight,
// 				ChangeTypePC:    item.ChangeTypePC,
// 				ChangeTypeW:     item.ChangeTypeW,
// 			}
// 			msg.Items = append(msg.Items, msgItem)
// 			if srcOrder.SrcDyeFactoryId > 0 {
// 				exMsgItem := *msgItem
// 				exMsgItem.UnitId = srcOrder.SrcDyeFactoryId
// 				msg.Items = append(msg.Items, &exMsgItem)
// 			}
// 		}
//
// 		inner_event.DNFChangeOrderStatusChangeTrigger(ctx, msg)
// 	})(newCtx)
// }

// 更新价格
func (s *PayableOtherService) UpdatePrice(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdatePriceRequest, pass bool) error {
	var (
		repo payable_iface.PayableOtherRepoIFace = payable.NewPayableOtherRepo(ctx, tx, s.isCache)
	)
	// 获取订单
	order, err := repo.GetPayableOtherWithoutItems(ctx, req.Id)
	if err != nil {
		return err
	}
	if order == nil {
		err = errors.NewError(errors.ErrCodePayableNotExist)
		return err
	}

	// 更新价格
	if err = order.UpdatePrice(req.DiscountPrice, req.ReducePrice, req.PaidPrice, req.OffsetPrice, pass); err != nil {
		return err
	}

	// 保存
	err = repo.UpdatePayableOtherPrice(ctx, order)
	if err != nil {
		return err
	}

	return nil
}
