package employee

import (
	"context"
	mysqlEmployee "hcscm/aggs/employee"
	"hcscm/common/errors"
	employeeEntity "hcscm/domain/employee/entity"
	"hcscm/extern/pb/department"
	"hcscm/extern/pb/dictionary"
	"hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	mysql "hcscm/model/mysql/employee"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/system"
	"strings"
)

type EmployeeQueryService struct {
}

func NewEmployeeQueryService() *EmployeeQueryService {
	return &EmployeeQueryService{}
}

func (s *EmployeeQueryService) QueryEmployeeList(ctx context.Context, tx *mysql_base.Tx, p *structure.GetEmployeeListParams) (*structure.QueryEmployeeListDtoRsp, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var repo = mysqlEmployee.NewRepo(tx)

	// 查询营销体系
	if !p.SaleSystemId.IsNil() {
		ids, err := repo.QueryEmployeeIdsBySaleSystemIds(ctx, p.SaleSystemId.ToUint64())
		if err != nil {
			return nil, err
		}
		p.Ids = ids
	}

	// 获取员工列表
	employeeList, total, err := repo.QueryEmployeeList(ctx, tx, p)
	if err != nil {
		return nil, err
	}

	// 获取员工所属营销体系
	ids := mysql.GetEmployeeIds(employeeList)
	saleSystemRel, err := repo.QueryEmployeeSaleSystemByIds(ctx, ids)
	if err != nil {
		return nil, err
	}
	saleSystemIds := mysql.GetSaleSystemIDSet(saleSystemRel)
	saleSystemService := sale_system.NewSaleSystemClient()
	saleSystemMap, err := saleSystemService.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return nil, err
	}

	// 获取部门
	departmentMap := make(map[uint64]string)
	departmentIds := mysql.GetDepartmentIdSet(employeeList)
	departmentService := department.NewDepartmentClient()
	departmentMap, err = departmentService.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return nil, err
	}

	// 获取职责
	dutyMap := make(map[uint64][2]string)
	dutyIds := mysql.GetDutyIdSet(employeeList)
	dutyService := dictionary.NewDictionaryClient()
	dutyMap, err = dutyService.GetDictionaryNameByIds(ctx, dutyIds)
	if err != nil {
		return nil, err
	}

	rsp := &structure.QueryEmployeeListDtoRsp{
		EmployeeList:  employeeList,
		Total:         total,
		SaleSystemRel: saleSystemRel,
		SaleSystemMap: saleSystemMap,
		DepartmentMap: departmentMap,
		DutyMap:       dutyMap,
	}
	return rsp, nil
}

func (s *EmployeeQueryService) QueryEmployeeEnumList(ctx context.Context, tx *mysql_base.Tx, p *structure.GetEmployeeListParams) (*structure.QueryEmployeeListDtoRsp, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var repo = mysqlEmployee.NewRepo(tx)

	// 查询营销体系
	if !p.SaleSystemId.IsNil() {
		ids, err := repo.QueryEmployeeIdsBySaleSystemIds(ctx, p.SaleSystemId.ToUint64())
		if err != nil {
			return nil, err
		}
		p.Ids = ids
	}

	// 获取员工列表
	employeeList, total, err := repo.QueryEmployeeEnumList(ctx, tx, p)
	if err != nil {
		return nil, err
	}

	// 获取员工所属营销体系
	ids := mysql.GetEmployeeIds(employeeList)
	saleSystemRel, err := repo.QueryEmployeeSaleSystemByIds(ctx, ids)
	if err != nil {
		return nil, err
	}
	saleSystemIds := mysql.GetSaleSystemIDSet(saleSystemRel)
	saleSystemService := sale_system.NewSaleSystemClient()
	saleSystemMap, err := saleSystemService.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return nil, err
	}

	// 获取部门
	departmentMap := make(map[uint64]string)
	departmentIds := mysql.GetDepartmentIdSet(employeeList)
	departmentService := department.NewDepartmentClient()
	departmentMap, err = departmentService.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return nil, err
	}

	// 获取职责
	dutyMap := make(map[uint64][2]string)
	dutyIds := mysql.GetDutyIdSet(employeeList)
	dutyService := dictionary.NewDictionaryClient()
	dutyMap, err = dutyService.GetDictionaryNameByIds(ctx, dutyIds)
	if err != nil {
		return nil, err
	}

	rsp := &structure.QueryEmployeeListDtoRsp{
		EmployeeList:  employeeList,
		Total:         total,
		SaleSystemRel: saleSystemRel,
		SaleSystemMap: saleSystemMap,
		DepartmentMap: departmentMap,
		DutyMap:       dutyMap,
	}
	return rsp, nil
}

func (s *EmployeeQueryService) QueryEmployeeByIds(ctx context.Context, tx *mysql_base.Tx, ids []uint64) ([]*mysql.Employee, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var employeeRepo = mysqlEmployee.NewRepo(tx)
	emplList, err := employeeRepo.QueryEmployeeByIds(ctx, ids)
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeServerInternalError, "repo.QueryEmployeeByIds err: "+err.Error()))
		return nil, err
	}
	return emplList, nil
}

func (s *EmployeeQueryService) QueryEmployeeByCodeOrName(ctx context.Context, tx *mysql_base.Tx, code, name []string) ([]*mysql.Employee, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var employeeRepo = mysqlEmployee.NewRepo(tx)
	empl, err := employeeRepo.QueryEmployeeByCodeOrName(ctx, code, name)
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeServerInternalError, "repo.QueryEmployeeByIds err: "+err.Error()))
		return nil, err
	}
	return empl, nil
}

func (s *EmployeeQueryService) QueryEmployeeByLikeCodeOrName(ctx context.Context, tx *mysql_base.Tx, codeOrName string) ([]*mysql.Employee, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var employeeRepo = mysqlEmployee.NewRepo(tx)
	empl, err := employeeRepo.QueryEmployeeByLikeCodeOrName(ctx, codeOrName)
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeServerInternalError, "repo.QueryEmployeeByIds err: "+err.Error()))
		return nil, err
	}
	return empl, nil
}

func (s *EmployeeQueryService) QueryEmployeeByDuty(ctx context.Context, tx *mysql_base.Tx, duty uint64) ([]*mysql.Employee, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var employeeRepo = mysqlEmployee.NewRepo(tx)
	empl, err := employeeRepo.QueryMultiEmployeeByDuty(duty)
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeServerInternalError, "repo.QueryEmployeeByDuty err: "+err.Error()))
		return nil, err
	}
	return empl, nil
}

func (s *EmployeeQueryService) QueryEmployeeDetail(ctx context.Context, tx *mysql_base.Tx, id uint64) (*structure.GetEmployeeDetail, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var (
		repo = mysqlEmployee.NewRepo(tx)

		departmentService = department.NewDepartmentClient()
		saleSystemService = sale_system.NewSaleSystemClient()
	)

	// 获取员工
	employee, err := repo.QueryEmployee(ctx, id)
	if err != nil {
		return nil, err
	}
	if employee == nil {
		return nil, employee.GetNotExistError()
	}

	// 获取职责名
	dutyStr := make([]string, 0, len(employee.Duty))
	dutyMap := make(map[uint64][2]string)
	dutyService := dictionary.NewDictionaryClient()
	dutyMap, err = dutyService.GetDictionaryNameByIds(ctx, employee.Duty.ToInt())
	if err != nil {
		return nil, err
	}
	for _, d := range employee.Duty.ToInt() {
		dutyStr = append(dutyStr, dutyMap[d][1])
	}

	var response *structure.GetEmployeeDetail
	response = &structure.GetEmployeeDetail{
		EmployeeParams: structure.EmployeeParams{
			Param:             structure.Param{},
			Code:              employee.Code,
			Name:              employee.Name,
			DepartmentID:      employee.DepartmentID,
			Duty:              employee.Duty,
			Phone:             employee.Phone,
			Email:             employee.Email,
			IsBlacklist:       employee.IsBlacklist,
			Remark:            employee.Remark,
			Address:           employee.Address,
			Birthday:          employee.Birthday,
			IdentityNumber:    employee.IdentityNumber,
			Nation:            employee.Nation,
			MaritalStatus:     employee.MaritalStatus,
			EducationLevel:    employee.EducationLevel,
			GraduateSchool:    employee.GraduateSchool,
			GraduateDate:      employee.GraduateDate,
			ContractStartDate: employee.ContractStartDate,
			ContractEndDate:   employee.ContractEndDate,
			ResignDate:        employee.ResignDate,
			TimecardNumber:    employee.TimecardNumber,
			Status:            employee.Status,
			QYWXUserName:      employee.QYWXUserName,
			QYWXUserId:        employee.QYWXUserID,
		},
		Id:                 employee.Id,
		DutyName:           strings.Join(dutyStr, ","),
		MaritalStatusName:  employeeEntity.EducationLevelMap[employee.MaritalStatus],
		EducationLevelName: employeeEntity.EducationLevelMap[employee.EducationLevel],
	}

	// 获取员工部门
	deptRsp, err := departmentService.GetDepartmentNameByIds(ctx, []uint64{employee.DepartmentID})
	if err != nil {
		return nil, err
	}
	if deptRsp != nil {
		response.DepartmentName = deptRsp[employee.DepartmentID]
	}

	// 获取营销体系
	saleSystemIds, err := repo.QueryEmployeeSaleSystem(ctx, employee.Id)
	if err != nil {
		return nil, err
	}
	response.SaleSystemList = saleSystemIds

	// 获取营销体系名
	saleSystemName := make([]string, 0, len(saleSystemIds))
	if len(saleSystemIds) > 0 {
		saleSystemMap, err := saleSystemService.GetSaleSystemByIds(ctx, saleSystemIds)
		if err != nil {
			return nil, err
		}
		for _, sid := range saleSystemIds {
			if name := saleSystemMap[sid]; name != "" {
				saleSystemName = append(saleSystemName, saleSystemMap[sid])
			}
		}
		response.SaleSystemName = strings.Join(saleSystemName, ",")
	}

	return response, nil
}

func (s *EmployeeQueryService) QueryEmployeeByDepartmentIDs(ctx context.Context, tx *mysql_base.Tx, ids []uint64) ([]*mysql.Employee, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var employeeRepo = mysqlEmployee.NewRepo(tx)
	emplList, err := employeeRepo.QueryEmployeeByDepartmentIDs(ctx, ids)
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeServerInternalError, "repo.QueryEmployeeByIds err: "+err.Error()))
		return nil, err
	}
	return emplList, nil
}

func (s *EmployeeQueryService) QueryEmployeeSaleSystem(ctx context.Context, tx *mysql_base.Tx, id uint64) ([]uint64, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var employeeRepo = mysqlEmployee.NewRepo(tx)
	return employeeRepo.QueryEmployeeSaleSystem(ctx, id)
}

func (s *EmployeeQueryService) JudgeEmpIsChecker(ctx context.Context, tx *mysql_base.Tx, id uint64) (bool, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var repo = mysqlEmployee.NewRepo(tx)

	// 获取员工列表
	duty := mysql.GetCheckDuty()
	exit, err := repo.JudgeEmpIsChecker(ctx, nil, id, duty)
	if err != nil {
		return exit, err
	}

	return exit, nil
}
