package msg_consume

import (
	"context"
	"encoding/json"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/aliyun/image_search"
	tenant_management_svc "hcscm/service/tenant_management"
)

type searchImageUpload struct {
	TenantManagementId      uint64
	SearchImageInstanceName string
	ProductCode             string
	Url                     string
	Type                    int // 1为新增 2为更新 3为删除
}

func ConsumeSearchImageUpload(message []byte) (err error) {
	var (
		req       searchImageUpload
		isExpired bool
	)
	err = json.Unmarshal(message, &req)
	if err != nil {
		return err
	}
	isExpired, err = tenant_management_svc.NewCodeListOrcManagementLogic(nil).IsExpired(context.Background(), common_system.RechargeTypeSearchImage, req.TenantManagementId)
	if err != nil {
		return
	}
	if isExpired {
		return
	}
	// 上传图片
	if req.Type == 1 {
		err = image_search.UploadImageByUrl(context.Background(), req.SearchImageInstanceName, req.ProductCode, req.Url)
		if err != nil {
			return err
		}
	}
	// 更新图片
	if req.Type == 2 {
	}
	// 删除图片
	if req.Type == 3 {
		err = image_search.DeleteImage(context.Background(), req.SearchImageInstanceName, req.ProductCode, req.Url)
		if err != nil {
			return err
		}
	}

	return nil
}
