package product

import (
	cus_const "hcscm/common/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFpmInOrderItemFcParamList []AddFpmInOrderItemFcParam

func (r AddFpmInOrderItemFcParamList) Adjust() {

}

type AddFpmInOrderItemFcParam struct {
	structure_base.Param
	Id                            uint64                        `json:"id,omitempty"`
	WarehouseId                   uint64                        `json:"warehouse_id"`                                        // 仓库id
	ParentId                      uint64                        `json:"parent_id"`                                           // 父id（成品信息行id）
	WarehouseInType               cus_const.WarehouseGoodInType `json:"warehouse_in_type"`                                   // 来源类型
	WarehouseInOrderId            uint64                        `json:"warehouse_in_order_id"`                               // 进仓单id
	WarehouseInOrderNo            string                        `json:"warehouse_in_order_no"`                               // 进仓单号
	Roll                          int                           `json:"roll"`                                                // 条数(条)，乘100存
	WarehouseBinId                uint64                        `json:"warehouse_bin_id"`                                    // 仓位
	VolumeNumber                  int                           `json:"volume_number"`                                       // 卷号
	BaseUnitWeight                int                           `json:"base_unit_weight"`                                    // 数量(公斤)，乘10000存
	WeightError                   int                           `json:"weight_error"`                                        // 空差数量(公斤)，乘10000存
	UnitId                        uint64                        `json:"unit_id"`                                             // 单位id（kg）
	AuxiliaryUnitId               uint64                        `gorm:"column:auxiliary_unit_id" relate:"auxiliary_unit_id"` // 辅助单位id（米/用于判断计算金额时使用哪个数量）
	StockId                       uint64                        `json:"stock_id"`                                            // 库存成品id
	ReturnStockId                 uint64                        `json:"return_stock_id"`                                     // 退货库存成品id
	SumStockId                    uint64                        `json:"sum_stock_id"`                                        // 汇总库存id
	PaperTubeWeight               int                           `json:"paper_tube_weight"`                                   // 纸筒数量(公斤)，乘10000存
	ActuallyWeight                int                           `json:"actually_weight"`                                     // 码单数量
	SettleErrorWeight             int                           `json:"settle_error_weight"`                                 // 结算空差数量
	SettleWeight                  int                           `json:"settle_weight"`                                       // 结算数量
	Length                        int                           `json:"length"`                                              // 长度，乘100存
	DigitalCode                   string                        `json:"digital_code"`                                        // 数字码
	ShelfNo                       string                        `json:"shelf_no"`                                            // 货架号
	AccountNum                    string                        `json:"account_num"`                                         // 款号
	DyeFactoryColorCode           string                        `json:"dye_factory_color_code"`                              // 染厂色号
	DyeFactoryDyelotNumber        string                        `json:"dye_factory_dyelot_number"`                           // 染厂缸号
	ProductWidth                  string                        `json:"product_width"`                                       // 成品幅宽
	ProductGramWeight             string                        `json:"product_gram_weight"`                                 // 成品克重
	Remark                        string                        `json:"remark"`                                              // 备注/库存备注
	StockRemark                   string                        `json:"stock_remark"`                                        // todo:重复字段，待去除
	InternalRemark                string                        `json:"internal_remark"`                                     // 内部备注
	ContractNumber                string                        `json:"contract_number"`                                     // 合同号
	ScanUserId                    uint64                        `json:"scan_user_id"`                                        // 扫描人id
	ScanUserName                  string                        `json:"scan_user_name"`                                      // 扫描人名称
	ScanTime                      tools.QueryTime               `json:"scan_time"`                                           // 扫描时间
	IsInWarehouse                 bool                          `json:"is_in_warehouse"`                                     // 是否已经出仓；1是
	OrderTime                     tools.QueryTime               `json:"order_time"`                                          // 单据选框选择的那个时间
	FinishProductWidthUnitId      uint64                        `json:"finish_product_width_unit_id"`                        // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                        `json:"finish_product_gram_weight_unit_id"`                  // 成品克重单位id(字典)
	BarCode                       string                        `json:"bar_code"`                                            // 条码
	QrCode                        string                        `json:"qr_code"`                                             // 二维码
	SrcId                         uint64                        `json:"src_id"`                                              // 来源id
	ArrangeItemFcId               uint64                        `json:"arrange_item_fc_id"`
	BizUnitID                     uint64                        `json:"-"`
	CustomerId                    uint64                        `json:"-"`
	ProductId                     uint64                        `json:"-"`
	ProductColorId                uint64                        `json:"-"`
	ProductLevelId                uint64                        `json:"-"`
	ProductRemark                 string                        `json:"-"`
	WarehouseInTime               tools.MyTime                  `json:"-"`
}

func (r *AddFpmInOrderItemFcParam) Swap2AddItemFcParam(req GetFpmOutOrderItemFcData) {
	r.SrcId = req.Id
	r.WarehouseId = req.WarehouseId
	r.WarehouseInType = req.WarehouseInType
	r.WarehouseInOrderId = req.WarehouseInOrderId
	r.WarehouseInOrderNo = req.WarehouseInOrderNo
	r.Roll = req.Roll
	r.WarehouseBinId = req.WarehouseBinId
	r.VolumeNumber = req.VolumeNumber
	r.BaseUnitWeight = req.BaseUnitWeight
	r.WeightError = req.WeightError
	r.SettleErrorWeight = req.SettleErrorWeight
	r.UnitId = req.UnitId
	r.QrCode = req.QrCode
	r.StockId = req.StockId
	r.SumStockId = req.SumStockId
	r.PaperTubeWeight = req.PaperTubeWeight
	r.Length = req.Length
	r.Remark = req.Remark
	r.InternalRemark = req.InternalRemark
	r.DigitalCode = req.DigitalCode
	r.ShelfNo = req.ShelfNo
	r.AccountNum = req.AccountNum
	r.DyeFactoryColorCode = req.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = req.DyeFactoryDyelotNumber
	r.ProductWidth = req.ProductWidth
	r.ProductGramWeight = req.ProductGramWeight
	r.ArrangeItemFcId = req.ArrangeItemFcId
	if r.ProductWidth == "" {
		r.ProductWidth = req.FinishProductWidth
	}
	if r.ProductGramWeight == "" {
		r.ProductGramWeight = req.FinishProductGramWeight
	}
	r.FinishProductWidthUnitId = req.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = req.FinishProductGramWeightUnitId
	r.ContractNumber = req.ContractNumber
	r.ScanUserId = req.ScanUserId
	r.ScanUserName = req.ScanUserName
	r.ScanTime = tools.QueryTime(req.ScanTime.Date())
	r.SettleErrorWeight = req.SettleErrorWeight
}

type AddFpmInOrderItemFcData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type GetFpmInOrderItemFcQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmInOrderItemFcListQuery struct {
	structure_base.ListQuery
	ParentId               uint64          `form:"parent_id"`                 // 父id（成品信息行id）
	WarehouseInType        bool            `form:"warehouse_in_type"`         // 来源类型
	WarehouseInOrderId     uint64          `form:"warehouse_in_order_id"`     // 进仓单id
	WarehouseInOrderNo     string          `form:"warehouse_in_order_no"`     // 进仓单号
	Roll                   int             `form:"roll"`                      // 条数(条)，乘100存
	WarehouseBinId         uint64          `form:"warehouse_bin_id"`          // 仓位
	VolumeNumber           int             `form:"volume_number"`             // 卷号
	BaseUnitWeight         int             `form:"base_unit_weight"`          // 数量(公斤)，乘10000存
	WeightError            int             `form:"weight_error"`              // 空差数量(公斤)，乘10000存
	UnitId                 uint64          `form:"unit_id"`                   // 单位id（kg）
	StockId                uint64          `form:"stock_id"`                  // 库存成品id
	SumStockId             uint64          `form:"sum_stock_id"`              // 汇总库存id
	PaperTubeWeight        int             `form:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	Length                 int             `form:"length"`                    // 长度，乘100存
	Remark                 string          `form:"remark"`                    // 备注
	DigitalCode            string          `form:"digital_code"`              // 数字码
	ShelfNo                string          `form:"shelf_no"`                  // 货架号
	AccountNum             string          `form:"account_num"`               // 款号
	DyeFactoryColorCode    string          `form:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string          `form:"dye_factory_dyelot_number"` // 染厂缸号
	ProductWidth           string          `form:"product_width"`             // 成品幅宽
	ProductGramWeight      string          `form:"product_gram_weight"`       // 成品克重
	ContractNumber         string          `form:"contract_number"`           // 合同号
	ScanUserId             uint64          `form:"scan_user_id"`              // 扫描人id
	ScanUserName           string          `form:"scan_user_name"`            // 扫描人名称
	ScanTime               tools.QueryTime `form:"scan_time"`                 // 扫描时间
	IsInWarehouse          bool            `form:"is_in_warehouse"`           // 是否已经出仓；1是
	OrderTime              tools.QueryTime `form:"order_time"`                // 单据选框选择的那个时间
	SrcIds                 []uint64        `form:"-"`                         // 来源id
}

func (r GetFpmInOrderItemFcListQuery) Adjust() {

}

type GetFpmInOrderItemFcData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	WarehouseId            uint64                        `json:"warehouse_id"`              // 仓库id
	ParentId               uint64                        `json:"parent_id"`                 // 父id（成品信息行id）
	WarehouseInType        cus_const.WarehouseGoodInType `json:"warehouse_in_type"`         // 来源类型
	WarehouseInOrderId     uint64                        `json:"warehouse_in_order_id"`     // 进仓单id
	WarehouseInOrderNo     string                        `json:"warehouse_in_order_no"`     // 进仓单号
	Roll                   int                           `json:"roll"`                      // 条数(条)，乘100存
	WarehouseBinId         uint64                        `json:"warehouse_bin_id"`          // 仓位
	VolumeNumber           int                           `json:"volume_number"`             // 卷号
	BaseUnitWeight         int                           `json:"base_unit_weight"`          // 基本单位数量(公斤)，乘10000存
	WeightError            int                           `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int                           `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	ActuallyWeight         int                           `json:"actually_weight"`           // 码单数量
	SettleErrorWeight      int                           `json:"settle_error_weight"`       // 结算空差数量
	SettleWeight           int                           `json:"settle_weight"`             // 结算数量
	Length                 int                           `json:"length"`                    // 长度，乘100存
	UnitId                 uint64                        `json:"unit_id"`                   // 单位id（kg）
	AuxiliaryUnitId        uint64                        `json:"auxiliary_unit_id"`         // 辅助单位id(米)
	StockId                uint64                        `json:"stock_id"`                  // 库存成品id
	ReturnStockId          uint64                        `json:"return_stock_id"`           // 退货库存成品id
	SumStockId             uint64                        `json:"sum_stock_id"`              // 汇总库存id
	Remark                 string                        `json:"remark"`                    // 备注
	InternalRemark         string                        `json:"internal_remark"`           // 内部备注
	DigitalCode            string                        `json:"digital_code"`              // 数字码
	ShelfNo                string                        `json:"shelf_no"`                  // 货架号
	AccountNum             string                        `json:"account_num"`               // 款号
	DyeFactoryColorCode    string                        `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                        `json:"dye_factory_dyelot_number"` // 染厂缸号
	ContractNumber         string                        `json:"contract_number"`           // 合同号
	StockRemark            string                        `json:"stock_remark"`              // 库存备注
	ScanUserId             uint64                        `json:"scan_user_id"`              // 扫描人id
	ScanUserName           string                        `json:"scan_user_name"`            // 扫描人名称
	ScanTime               tools.MyTime                  `json:"scan_time"`                 // 扫描时间
	IsInWarehouse          bool                          `json:"is_in_warehouse"`           // 是否已经出仓；1是
	OrderTime              tools.QueryTime               `json:"order_time"`                // 单据选框选择的那个时间

	// 转义
	WarehouseBinName        string `json:"warehouse_bin_name"`        // 仓位name
	UnitName                string `json:"unit_name"`                 // 单位name
	MeasurementUnitName     string `json:"measurement_unit_name"`     // 单位name(打印用)
	AuxiliaryUnitName       string `json:"auxiliary_unit_name"`       // 辅助单位名称
	WeavingOrganizationId   uint64 `json:"weaving_organization_id"`   // 织造组织id
	WeavingOrganizationCode string `json:"weaving_organization_code"` // 织造组织编号
	WeavingOrganizationName string `json:"weaving_organization_name"` // 织造组织名称
	Density                 string `json:"density"`                   // 密度
	ProductKindId           uint64 `json:"product_kind_id"`           // 布种类型id
	ProductKindName         string `json:"product_kind_name"`         // 布种类型名称
	YarnCount               string `json:"yarn_count"`                // 纱支
	BleachId                uint64 `json:"bleach_id"`                 // 漂染性id（字典）
	BleachName              string `json:"bleach_name"`               // 漂染性名称
	FinishProductCraft      string `json:"finish_product_craft"`      // 成品工艺
	FinishProductIngredient string `json:"finish_product_ingredient"` // 成品成分
	BarCode                 string `json:"bar_code"`                  // 条码
	QrCode                  string `json:"qr_code"`                   // 二维码
	PrintDate               string `json:"print_date"`                // 打印日期
	DyelotNumber            string `json:"dyelot_number"`             // 缸号(统一字段，打印用)
	SrcId                   uint64 `json:"src_id"`                    // 来源id
	ArrangeItemFcId         uint64 `json:"arrange_item_fc_id"`
	WarehouseInTypeName     string `json:"warehouse_in_type_name"` // 单据类型
}

type GetFpmInOrderItemFcDataList []GetFpmInOrderItemFcData

func (g GetFpmInOrderItemFcDataList) Adjust() {

}

func (g GetFpmInOrderItemFcDataList) PickBySrcId(id uint64) GetFpmInOrderItemFcData {
	for _, fcData := range g {
		if fcData.SrcId == id {
			return fcData
		}
	}
	return GetFpmInOrderItemFcData{}
}

type DeleteFpmInOrderItemFcParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmInOrderItemFcData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type Swap2StockFieldParam struct {
	ItemProductRemark             string                        // 成品备注
	DyeFactoryDyelotNumber        string                        // 缸号
	ProductLevelId                uint64                        // 成品等级
	WarehouseInType               cus_const.WarehouseGoodInType // 入库类型
	WarehouseInOrderId            uint64                        // 单据id
	WarehouseInOrderNo            string                        // 单据编号
	DyeFactoryColorCode           string                        // 染厂色号
	WarehouseId                   uint64                        // 仓库id
	CustomerId                    uint64                        // 客户id
	ProductId                     uint64                        // 成品id
	ProductColorId                uint64                        // 成品颜色id
	ProductColorKindId            uint64                        // 成品颜色种类id
	MeasurementUnitId             uint64                        // 计量单位id
	MeasurementUnitName           string                        // 计量单位名称
	FinishProductWidthUnitId      uint64                        // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                        // 成品克重单位id(字典)
	SupplierId                    uint64                        // 供应商id
	OutStockProductDetailId       uint64                        // 出仓库存id（调拨和调整）
}
