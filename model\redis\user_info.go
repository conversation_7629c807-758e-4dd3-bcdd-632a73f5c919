package redis

import (
	"context"
	"fmt"
	common_system "hcscm/common/system_consts"
	"time"
)

var (
	userInfo             = NewKvOperate(getCmd, "user_info", 5*time.Minute)
	tenantManagementInfo = NewKvOperate(getCmd, "tenant_management_user_info", 5*time.Minute)
	BizUnitInfo          = NewKvOperate(getCmd, "biz_unit_user_info", 5*time.Minute)
)

/*
---------------------------------
-------------用户登录态------------
---------------------------------
*/

// 通过UserId获取UserInfo
func GetUserInfo(ctx context.Context, userId uint64, platform common_system.Platform) (info []byte, exist bool, err error) {
	var module string
	key := fmt.Sprintf("%v%v", userId, platform)
	module, exist, err = userInfo.Get(ctx, key)
	if err != nil {
		return
	}
	return ([]byte)(module), exist, err
}

// 创建UserId UserInfo
func AddUserInfo(ctx context.Context, userId uint64, platform common_system.Platform, module []byte) (err error) {
	key := fmt.Sprintf("%v%v", userId, platform)
	err = userInfo.Set(ctx, key, string(module))
	return
}

// 删除UserId
func DeleteUserInfo(ctx context.Context, userId uint64, platform common_system.Platform) (err error) {
	key := fmt.Sprintf("%v%v", userId, platform)
	err = userInfo.Del(ctx, key)
	return
}

/*
---------------------------------
-------------租户登录态------------
---------------------------------
*/

// 通过TenantManagementId获取UserInfo
func GetTenantManagementUserInfo(ctx context.Context, tenantManagementId uint64, platform common_system.Platform) (info []byte, exist bool, err error) {
	var module string
	key := fmt.Sprintf("%v%v", tenantManagementId, platform)
	module, exist, err = tenantManagementInfo.Get(ctx, key)
	if err != nil {
		return
	}
	return ([]byte)(module), exist, err
}

// TenantManagementId UserInfo
func AddTenantManagementUserInfo(ctx context.Context, tenantManagementId uint64, platform common_system.Platform, module []byte) (err error) {
	key := fmt.Sprintf("%v%v", tenantManagementId, platform)
	err = tenantManagementInfo.Set(ctx, key, string(module))
	return
}

// 删除TenantManagementId
func DeleteTenantManagementUserInfo(ctx context.Context, tenantManagementId uint64, platform common_system.Platform) (err error) {
	key := fmt.Sprintf("%v%v", tenantManagementId, platform)
	err = tenantManagementInfo.Del(ctx, key)
	return
}

/*
---------------------------------
-------------客户登录态------------
---------------------------------
*/

// 通过BizUnitId获取UserInfo
func GetBizUnitUserInfo(ctx context.Context, bizUnitId uint64, platform common_system.Platform) (info []byte, exist bool, err error) {
	var module string
	key := fmt.Sprintf("%v%v", bizUnitId, platform)
	module, exist, err = BizUnitInfo.Get(ctx, key)
	if err != nil {
		return
	}
	return ([]byte)(module), exist, err
}

// 创建BizUnitId UserInfo
func AddBizUnitUserInfo(ctx context.Context, bizUnitId uint64, platform common_system.Platform, module []byte) (err error) {
	key := fmt.Sprintf("%v%v", bizUnitId, platform)
	err = BizUnitInfo.Set(ctx, key, string(module))
	return
}

// 删除BizUnitId
func DeleteBizUnitUserInfo(ctx context.Context, bizUnitId uint64, platform common_system.Platform) (err error) {
	key := fmt.Sprintf("%v%v", bizUnitId, platform)
	err = BizUnitInfo.Del(ctx, key)
	return
}
