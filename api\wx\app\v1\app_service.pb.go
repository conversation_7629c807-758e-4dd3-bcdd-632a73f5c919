// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.28.2
// source: api/wx/app/app_service.proto

package v1

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_wx_app_app_service_proto protoreflect.FileDescriptor

var file_api_wx_app_app_service_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x61, 0x70, 0x70,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31,
	0x1a, 0x14, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x61, 0x70, 0x70,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x32, 0xb1, 0x0a, 0x0a, 0x03, 0x41, 0x70, 0x70, 0x12, 0x80, 0x01, 0x0a,
	0x0c, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x2e,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x55, 0x73, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x24, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1e, 0x12, 0x1c, 0x77, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x12,
	0x9c, 0x01, 0x0a, 0x13, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x2c, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x45,
	0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x45, 0x78, 0x74,
	0x65, 0x72, 0x6e, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x2b, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x25, 0x12, 0x23, 0x77, 0x78, 0x2f, 0x61, 0x70,
	0x70, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x65, 0x78,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x84,
	0x01, 0x0a, 0x0d, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74,
	0x12, 0x26, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x79, 0x6e, 0x63,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x25,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x1f, 0x12, 0x1d, 0x77, 0x78, 0x2f, 0x61, 0x70, 0x70, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x79, 0x6e, 0x63, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x63, 0x68, 0x61, 0x74, 0x12, 0x5c, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x51, 0x59, 0x57, 0x58,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x12, 0x25, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x51, 0x59, 0x57, 0x58,
	0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x51, 0x59, 0x57, 0x58, 0x55, 0x73, 0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x22, 0x00, 0x12, 0x59, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x24, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55,
	0x73, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x68,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x4c, 0x69,
	0x73, 0x74, 0x12, 0x29, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68,
	0x61, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x68, 0x61, 0x74, 0x4c, 0x69, 0x73,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x5f, 0x0a, 0x0d, 0x42, 0x69, 0x6e, 0x64,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x26, 0x2e, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x65, 0x0a, 0x0f, 0x47, 0x65, 0x74,
	0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x12, 0x28, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e,
	0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x68, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f,
	0x6d, 0x65, 0x72, 0x73, 0x12, 0x29, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x27, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x42, 0x69, 0x6e, 0x64, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x0c, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x12, 0x25, 0x2e, 0x77, 0x78, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x70, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74, 0x61,
	0x69, 0x6c, 0x12, 0x25, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74,
	0x43, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x73, 0x57, 0x69, 0x74, 0x68, 0x44, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x5c, 0x0a, 0x0c, 0x47, 0x65,
	0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x12, 0x25, 0x2e, 0x77, 0x78, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x23, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x69, 0x67, 0x6e, 0x61, 0x74, 0x75, 0x72,
	0x65, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x42, 0x24, 0x0a, 0x10, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x70, 0x2e, 0x76, 0x31, 0x42, 0x05, 0x41, 0x70,
	0x70, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_wx_app_app_service_proto_goTypes = []any{
	(*SyncUserInfoRequest)(nil),         // 0: wx.api.wx.app.v1.SyncUserInfoRequest
	(*SyncExternalContactRequest)(nil),  // 1: wx.api.wx.app.v1.SyncExternalContactRequest
	(*SyncGroupChatRequest)(nil),        // 2: wx.api.wx.app.v1.SyncGroupChatRequest
	(*GetQYWXUsersRequest)(nil),         // 3: wx.api.wx.app.v1.GetQYWXUsersRequest
	(*GetUserInfoRequest)(nil),          // 4: wx.api.wx.app.v1.GetUserInfoRequest
	(*GetGroupChatListRequest)(nil),     // 5: wx.api.wx.app.v1.GetGroupChatListRequest
	(*BindCustomersRequest)(nil),        // 6: wx.api.wx.app.v1.BindCustomersRequest
	(*GetBindCustomerRequest)(nil),      // 7: wx.api.wx.app.v1.GetBindCustomerRequest
	(*GetBindCustomersRequest)(nil),     // 8: wx.api.wx.app.v1.GetBindCustomersRequest
	(*GetCustomersRequest)(nil),         // 9: wx.api.wx.app.v1.GetCustomersRequest
	(*GetSignatureRequest)(nil),         // 10: wx.api.wx.app.v1.GetSignatureRequest
	(*SyncUserInfoReply)(nil),           // 11: wx.api.wx.app.v1.SyncUserInfoReply
	(*SyncExternalContactReply)(nil),    // 12: wx.api.wx.app.v1.SyncExternalContactReply
	(*SyncGroupChatReply)(nil),          // 13: wx.api.wx.app.v1.SyncGroupChatReply
	(*GetQYWXUsersReply)(nil),           // 14: wx.api.wx.app.v1.GetQYWXUsersReply
	(*GetUserInfoReply)(nil),            // 15: wx.api.wx.app.v1.GetUserInfoReply
	(*GetGroupChatListReply)(nil),       // 16: wx.api.wx.app.v1.GetGroupChatListReply
	(*BindCustomersReply)(nil),          // 17: wx.api.wx.app.v1.BindCustomersReply
	(*GetBindCustomerReply)(nil),        // 18: wx.api.wx.app.v1.GetBindCustomerReply
	(*GetBindCustomersReply)(nil),       // 19: wx.api.wx.app.v1.GetBindCustomersReply
	(*GetCustomersReply)(nil),           // 20: wx.api.wx.app.v1.GetCustomersReply
	(*GetCustomersWithDetailReply)(nil), // 21: wx.api.wx.app.v1.GetCustomersWithDetailReply
	(*GetSignatureReply)(nil),           // 22: wx.api.wx.app.v1.GetSignatureReply
}
var file_api_wx_app_app_service_proto_depIdxs = []int32{
	0,  // 0: wx.api.wx.app.v1.App.SyncUserInfo:input_type -> wx.api.wx.app.v1.SyncUserInfoRequest
	1,  // 1: wx.api.wx.app.v1.App.SyncExternalContact:input_type -> wx.api.wx.app.v1.SyncExternalContactRequest
	2,  // 2: wx.api.wx.app.v1.App.SyncGroupChat:input_type -> wx.api.wx.app.v1.SyncGroupChatRequest
	3,  // 3: wx.api.wx.app.v1.App.GetQYWXUsers:input_type -> wx.api.wx.app.v1.GetQYWXUsersRequest
	4,  // 4: wx.api.wx.app.v1.App.GetUserInfo:input_type -> wx.api.wx.app.v1.GetUserInfoRequest
	5,  // 5: wx.api.wx.app.v1.App.GetGroupChatList:input_type -> wx.api.wx.app.v1.GetGroupChatListRequest
	6,  // 6: wx.api.wx.app.v1.App.BindCustomers:input_type -> wx.api.wx.app.v1.BindCustomersRequest
	7,  // 7: wx.api.wx.app.v1.App.GetBindCustomer:input_type -> wx.api.wx.app.v1.GetBindCustomerRequest
	8,  // 8: wx.api.wx.app.v1.App.GetBindCustomers:input_type -> wx.api.wx.app.v1.GetBindCustomersRequest
	9,  // 9: wx.api.wx.app.v1.App.GetCustomers:input_type -> wx.api.wx.app.v1.GetCustomersRequest
	9,  // 10: wx.api.wx.app.v1.App.GetCustomersWithDetail:input_type -> wx.api.wx.app.v1.GetCustomersRequest
	10, // 11: wx.api.wx.app.v1.App.GetSignature:input_type -> wx.api.wx.app.v1.GetSignatureRequest
	11, // 12: wx.api.wx.app.v1.App.SyncUserInfo:output_type -> wx.api.wx.app.v1.SyncUserInfoReply
	12, // 13: wx.api.wx.app.v1.App.SyncExternalContact:output_type -> wx.api.wx.app.v1.SyncExternalContactReply
	13, // 14: wx.api.wx.app.v1.App.SyncGroupChat:output_type -> wx.api.wx.app.v1.SyncGroupChatReply
	14, // 15: wx.api.wx.app.v1.App.GetQYWXUsers:output_type -> wx.api.wx.app.v1.GetQYWXUsersReply
	15, // 16: wx.api.wx.app.v1.App.GetUserInfo:output_type -> wx.api.wx.app.v1.GetUserInfoReply
	16, // 17: wx.api.wx.app.v1.App.GetGroupChatList:output_type -> wx.api.wx.app.v1.GetGroupChatListReply
	17, // 18: wx.api.wx.app.v1.App.BindCustomers:output_type -> wx.api.wx.app.v1.BindCustomersReply
	18, // 19: wx.api.wx.app.v1.App.GetBindCustomer:output_type -> wx.api.wx.app.v1.GetBindCustomerReply
	19, // 20: wx.api.wx.app.v1.App.GetBindCustomers:output_type -> wx.api.wx.app.v1.GetBindCustomersReply
	20, // 21: wx.api.wx.app.v1.App.GetCustomers:output_type -> wx.api.wx.app.v1.GetCustomersReply
	21, // 22: wx.api.wx.app.v1.App.GetCustomersWithDetail:output_type -> wx.api.wx.app.v1.GetCustomersWithDetailReply
	22, // 23: wx.api.wx.app.v1.App.GetSignature:output_type -> wx.api.wx.app.v1.GetSignatureReply
	12, // [12:24] is the sub-list for method output_type
	0,  // [0:12] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_api_wx_app_app_service_proto_init() }
func file_api_wx_app_app_service_proto_init() {
	if File_api_wx_app_app_service_proto != nil {
		return
	}
	file_api_wx_app_app_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_app_app_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wx_app_app_service_proto_goTypes,
		DependencyIndexes: file_api_wx_app_app_service_proto_depIdxs,
	}.Build()
	File_api_wx_app_app_service_proto = out.File
	file_api_wx_app_app_service_proto_rawDesc = nil
	file_api_wx_app_app_service_proto_goTypes = nil
	file_api_wx_app_app_service_proto_depIdxs = nil
}
