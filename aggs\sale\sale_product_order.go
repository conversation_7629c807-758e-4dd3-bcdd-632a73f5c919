package sale

import (
	"context"
	"fmt"
	aggs_info_basic "hcscm/aggs/basic_data/info_basic_data"
	"hcscm/aggs/gen_order_no"
	aggs "hcscm/aggs/product"
	mysqlSystem "hcscm/model/mysql/system"

	"hcscm/common/errors"
	common_product "hcscm/common/product"
	"hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product2 "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/basic_data/warehouse"
	"hcscm/extern/pb/biz_unit"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/department"
	"hcscm/extern/pb/employee"
	product_pb "hcscm/extern/pb/product"
	"hcscm/extern/pb/product_manage"
	"hcscm/extern/pb/sale_price"
	"hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/user"
	user_pb "hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/sale"
	mysql "hcscm/model/mysql/sale/dao"

	info_basic_structure "hcscm/structure/basic_data/info_basic_data"
	productStructure "hcscm/structure/product"
	saleStructure "hcscm/structure/sale"
	structure "hcscm/structure/sale"
	"hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"regexp"

	"strconv"
	"strings"
	"time"
)

type ISaleProductOrderRepo interface {
	Add(ctx context.Context, req *structure.AddSaleProductOrderParam) (id uint64, orderNo string, err error)
	MPAdd(ctx context.Context, req *structure.AddSaleProductOrderParamV2) (id uint64, err error)
	AddSaleProductOrderDetailDetails(ctx context.Context, req *structure.AddSaleProductOrderParam, parentId uint64) (err error)
	AddMPSaleProductOrderDetailDetails(ctx context.Context, req *structure.AddSaleProductOrderParamV2, parentId uint64) (err error)
	Update(ctx context.Context, req *structure.UpdateSaleProductOrderParam) (id uint64, err error)
	MPUpdate(ctx context.Context, req *structure.UpdateSaleProductOrderParamV2) (id uint64, err error)
	UpdateSaleProductOrderDetailDetails(ctx context.Context, req *structure.UpdateSaleProductOrderParam, parentId uint64) (err error)
	MPUpdateSaleProductOrderDetailDetails(ctx context.Context, req *structure.UpdateSaleProductOrderParamV2, parentId uint64) (err error)
	UpdateStatusPass(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (
		bookItemsReq productStructure.UpdateStockProductDetailParamList,
		arrangeItemsReq productStructure.AddFpmArrangeOrderParamList,
		shortageItemReq *saleStructure.AddShortageProductOrderParam,
		pmcPlanSummaryReq *structure.AuditUpdatePushed,
		salePlanOrderItemIds []uint64,
		err error)
	UpdateStatusWait(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (
		bookItemsReq productStructure.UpdateStockProductDetailParamList,
		pmcPlanSummaryReq *structure.AuditUpdatePushed,
		salePlanOrderItemIds []uint64,
		err error)
	UpdateStatusReject(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, err error)
	UpdateStatusCancel(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, BackPlanDetail map[uint64][2]int, err error)
	UpdateBusinessClose(ctx context.Context, req *structure.UpdateSaleProductOrderBusinessCloseParam) (data structure.UpdateSaleProductOrderBusinessCloseData, err error)
	Delete(ctx context.Context, req *structure.DeleteSaleProductOrderParam) (data structure.DeleteSaleProductOrderData, err error)
	Get(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderData, err error)
	MPGet(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderDataV2, err error)
	GetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataList, total int, err error)
	MPGetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataListV2, total int, err error)
	GetDropdownList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDropdownDataList, total int, err error)
	Exist(ctx context.Context, getDetail bool, ids, summaryIds []uint64) (list structure.PushedRecordList, orderNos []string, exist bool, err error)
	ExistBySaleProductPlanOrderId(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error)
	GetLastSalePrice(ctx context.Context, req *structure.GetLastSalePriceQuery) (res structure.GetLastSalePriceDataList, err error)
	GetHistorySaleOrderList(ctx context.Context, req *structure.GetHistorySaleOrderListQuery) (list structure.GetHistorySaleOrderDataList, total int, err error)
	GetSaleOrderStatusNum(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (data structure.GetSaleOrderStatusNumData, err error)
	AutoOcr(ctx context.Context, req *structure.GetProductByAutoOrcParam) (data productStructure.GetStockProductDropdownDataList, err error)
	GetCustomerLastMoneyInfo(ctx context.Context, req *structure.GetCustomerLastMoneyInfoReq) (res structure.GetCustomerLastMoneyInfoRes, err error)
}

type SaleProductOrderRepo struct {
	tx                        *mysql_base.Tx
	isCache                   bool
	saleProductOrderDao       mysql.ISaleProductOrderDao
	saleProductOrderDetailDao mysql.ISaleProductOrderDetailDao
}

func NewSaleProductOrderRepo(ctx context.Context, tx *mysql_base.Tx, isCache bool) ISaleProductOrderRepo {
	return &SaleProductOrderRepo{
		tx:                        tx,
		isCache:                   isCache,
		saleProductOrderDao:       mysql.NewSaleProductOrderDao(ctx, isCache),
		saleProductOrderDetailDao: mysql.NewSaleProductOrderDetailDao(ctx, isCache),
	}
}
func (r *SaleProductOrderRepo) MPAdd(ctx context.Context, req *structure.AddSaleProductOrderParamV2) (id uint64, err error) {
	var (
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_system.NewSaleSystemClient()
		saleSysData        = sale_system.Res{}
	)
	saleProductOrder := model.NewMPSaleProductOrder(ctx, req)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_system.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleOutOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "sale", saleProductOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	saleProductOrder.OrderNo = orderNo
	saleProductOrder.Number = int(number)

	saleProductOrder, err = r.saleProductOrderDao.MustCreate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}
	return saleProductOrder.Id, err
}
func (r *SaleProductOrderRepo) Add(ctx context.Context, req *structure.AddSaleProductOrderParam) (id uint64, orderNo string, err error) {
	var (
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_system.NewSaleSystemClient()
		saleSysData        = sale_system.Res{}
	)
	saleProductOrder := model.NewSaleProductOrder(ctx, req)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_system.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "sale", saleProductOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	saleProductOrder.OrderNo = orderNo
	saleProductOrder.Number = int(number)

	saleProductOrder, err = r.saleProductOrderDao.MustCreate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}
	return saleProductOrder.Id, saleProductOrder.OrderNo, err
}
func (r *SaleProductOrderRepo) AddMPSaleProductOrderDetailDetails(ctx context.Context, req *structure.AddSaleProductOrderParamV2, parentId uint64) (err error) {
	for _, item := range req.ItemData {
		saleProductOrderDetailItem := model.NewMPSaleProductOrderDetail(ctx, &item, parentId)
		saleProductOrderDetailItem, err = r.saleProductOrderDetailDao.BatchCreate(ctx, r.tx, saleProductOrderDetailItem)
		if err != nil {
			return
		}
	}
	return
}

func (r *SaleProductOrderRepo) AddSaleProductOrderDetailDetails(ctx context.Context, req *structure.AddSaleProductOrderParam, parentId uint64) (err error) {

	for _, item := range req.ItemData {
		saleProductOrderDetailItem := model.NewSaleProductOrderDetail(ctx, &item, parentId)
		saleProductOrderDetailItem, err = r.saleProductOrderDetailDao.MustCreate(ctx, r.tx, saleProductOrderDetailItem)
		if err != nil {
			return
		}
	}
	return
}

func (r *SaleProductOrderRepo) Update(ctx context.Context, req *structure.UpdateSaleProductOrderParam) (id uint64, err error) {
	var (
		saleProductOrder model.SaleProductOrder
	)
	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := saleProductOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	saleProductOrder.UpdateSaleProductOrder(ctx, req)

	saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}

	return saleProductOrder.Id, err
}

func (r *SaleProductOrderRepo) UpdateSaleProductOrderDetailDetails(ctx context.Context, req *structure.UpdateSaleProductOrderParam, parentId uint64) (err error) {
	var (
		itemList model.SaleProductOrderDetailList
	)

	// 找出该单下的信息，并删除
	itemList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, parentId)
	for _, item := range itemList {
		err = r.saleProductOrderDetailDao.MustDelete(ctx, r.tx, item)
		if err != nil {
			return
		}
	}

	// 新增坯布信息
	for _, item := range req.ItemData {
		saleProductOrderDetailItem := model.NewSaleProductOrderDetail(ctx, &item, parentId)

		saleProductOrderDetailItem, err = r.saleProductOrderDetailDao.MustCreate(ctx, r.tx, saleProductOrderDetailItem)
		if err != nil {
			return
		}
	}
	return
}

func (r *SaleProductOrderRepo) MPUpdate(ctx context.Context, req *structure.UpdateSaleProductOrderParamV2) (id uint64, err error) {
	var (
		saleProductOrder model.SaleProductOrder
	)
	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	saleProductOrder.MPUpdateSaleProductOrder(ctx, req)

	saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}

	return saleProductOrder.Id, err
}
func (r *SaleProductOrderRepo) MPUpdateSaleProductOrderDetailDetails(ctx context.Context, req *structure.UpdateSaleProductOrderParamV2, parentId uint64) (err error) {
	var (
		itemList model.SaleProductOrderDetailList
	)

	// 找出该单下的信息，并删除
	itemList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, parentId)
	for _, item := range itemList {
		err = r.saleProductOrderDetailDao.MustDelete(ctx, r.tx, item)
		if err != nil {
			return
		}
	}
	// 新增坯布信息
	for _, item := range req.ItemData {
		saleProductOrderDetailItem := model.NewMPSaleProductOrderDetail(ctx, &item, parentId)
		saleProductOrderDetailItem, err = r.saleProductOrderDetailDao.BatchCreate(ctx, r.tx, saleProductOrderDetailItem)
		if err != nil {
			return
		}
	}
	return
}

func (r *SaleProductOrderRepo) UpdateStatusPass(
	ctx context.Context,
	req *structure.UpdateSaleProductOrderAuditStatusParam,
) (
	bookItemsReq productStructure.UpdateStockProductDetailParamList,
	arrangeItemsReq productStructure.AddFpmArrangeOrderParamList,
	shortageItemReq *saleStructure.AddShortageProductOrderParam,
	pmcPlanSummaryReq *structure.AuditUpdatePushed,
	salePlanOrderItemIds []uint64,
	err error,
) {
	var (
		saleProductOrder           model.SaleProductOrder
		saleProductOrderDetailList model.SaleProductOrderDetailList
		_salePlanOrderItemIds      = make([]uint64, 0)
	)

	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	saleProductOrderDetailList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	// 请至少添加一种成品
	if len(saleProductOrderDetailList) == 0 {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeAtLeastOneProduct))
		return
	}

	// 订单匹数 = 预约匹数 + 采购匹数 + 欠货匹数
	if !saleProductOrderDetailList.IsNumberEqual() {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeRollShouldEqual))
		return
	}

	// req.IsAudit值为 0为正常流程,1为不审核只获取信息,2为只审核
	if req.IsAudit == 0 || req.IsAudit == 2 {
		// 订单匹数和订单数量判断是否为0
		for _, saleProductOrderDetail := range saleProductOrderDetailList {
			if saleProductOrderDetail.Roll == 0 && saleProductOrderDetail.Weight == 0 && saleProductOrderDetail.Length == 0 {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeRollAndWeightCanNotAllZero))
				return
			}
			if saleProductOrderDetail.PlanDetailId > 0 {
				_salePlanOrderItemIds = append(_salePlanOrderItemIds, saleProductOrderDetail.PlanDetailId)
			}
		}
		err = r.auditSaleProductOrder(ctx, r.tx, saleProductOrder)
		if err != nil {
			return
		}
	}
	if req.IsAudit == 0 || req.IsAudit == 1 {
		bookItemsReq, arrangeItemsReq, shortageItemReq, pmcPlanSummaryReq, err = r.getPushNextItems(ctx, r.tx, saleProductOrder, saleProductOrderDetailList)
		if err != nil {
			return
		}
	}
	// 修改订单进度
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *SaleProductOrderRepo) getPushNextItems(
	ctx context.Context,
	tx *mysql_base.Tx,
	saleProductOrder model.SaleProductOrder,
	saleProductOrderDetailList model.SaleProductOrderDetailList,
) (
	bookItemsReq productStructure.UpdateStockProductDetailParamList,
	arrangeItemsReq productStructure.AddFpmArrangeOrderParamList,
	shortageItemReq *saleStructure.AddShortageProductOrderParam,
	pmcPlanSummaryReq *structure.AuditUpdatePushed,
	err error,
) {
	var (
		bookItems           = make(productStructure.UpdateStockProductDetailParamList, 0)
		arrangeItems        = make(productStructure.AddFpmArrangeOrderParamList, 0)
		arrangeDetailItems  = make(productStructure.AddFpmArrangeOrderItemParamList, 0)
		shortageDetailItems = make(saleStructure.AddShortageProductOrderDetailParamList, 0)
	)
	for _, detail := range saleProductOrderDetailList {
		// 根据预约匹数占用库存
		// 把预约匹数不为0的数据转换为生成配布单的结构体入参
		// 不用生成配布单的情况: 预约匹数为0/空 且(采购数量+欠货数量 = 订单数量)且 (采购长度+欠货长度 = 订单长度)20230727产品说的
		if !(detail.BookRoll == 0 && detail.PurchaseWeight+detail.ShortageWeight >= detail.Weight && detail.PurchaseLength+detail.ShortageLength >= detail.Length) {
			bookItem := detail.ToUpdateStockProductDetailParam(true)
			bookItem.OrderNo = saleProductOrder.OrderNo
			bookItems = append(bookItems, bookItem)
			arrangeDetailItems = append(arrangeDetailItems, detail.ToAddFpmArrangeOrderParam(saleProductOrder))
		}
		// if detail.BookRoll != 0 || detail.PurchaseWeight+detail.ShortageWeight < detail.Weight || detail.PurchaseLength+detail.ShortageLength < detail.Length {
		// }
		// 欠货匹数、数量、长度有一项不为0生成一张欠货单并自动审核
		if detail.ShortageRoll != 0 || detail.ShortageLength != 0 || detail.ShortageWeight != 0 {
			shortageDetailItems = append(shortageDetailItems, detail.ToAddShortageProductOrderDetailParam())
		}

		// 更新库存为最新
		if detail.DyelotNumber != "" {
			productStockSvc := product_pb.NewProductStockClient()
			rwl, _ := productStockSvc.GetStockByDyelotNumberAndColor(ctx, product_pb.GetCADRWLParam{
				ProductColorId: detail.ProductColorId,
				SumStockId:     detail.StockProductId,
				DeylotNubmer:   detail.DyelotNumber,
			})
			detail.StockRoll = rwl[0]
		}
		if detail.DyelotNumber == "" {
			// 阿华说数据不准，我现在就按照汇总库存来
			stockProductIds := tools.UInt64s2String([]uint64{detail.StockProductId})
			stockMap, _, _ := product_manage.GetStockByIds(ctx, &product_manage.GetRequest{Ids: stockProductIds})
			if stockProduct, ok := stockMap[detail.StockProductId]; ok {
				detail.StockRoll = int(stockProduct.StockRoll - stockProduct.BookRoll)
			}
		}

		detail, err = r.saleProductOrderDetailDao.MustUpdate(ctx, tx, detail)
		if err != nil {
			return
		}
	}
	// 把对应的仓库分成多张配布单
	warehouseIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "warehouse_id")
	for _, warehouseId := range warehouseIds {
		o := saleProductOrder.ToAddFpmArrangeOrderParam(warehouseId)
		// 判断下推配布单是出货还是销调
		if saleProductOrder.SendProductType == sale.SendProductTypeDelivery {
			o.OutOrderType = common_product.WarehouseGoodOutTypeSale
		} else {
			// 销调类型，同仓库为出货，不同仓库为销调
			if warehouseId == saleProductOrder.WarehouseId {
				o.OutOrderType = common_product.WarehouseGoodOutTypeSale
			} else {
				o.OutOrderType = common_product.WarehouseGoodOutTypeSaleAllocate
			}
		}
		for _, item := range arrangeDetailItems {
			if item.WarehouseId == warehouseId {
				o.ItemData = append(o.ItemData, item)
			}
		}
		arrangeItems = append(arrangeItems, o)
	}
	// 欠货单生成参数
	if len(shortageDetailItems) != 0 {
		shortageItemReq = saleProductOrder.ToAddShortageProductOrderParam()
		shortageItemReq.ItemData = shortageDetailItems
	}
	if saleProductOrder.PmcGreyPlanOrderId != 0 {
		pmcPlanSummaryReq = saleProductOrder.ToAuditUpdatePmcPlanOrderSummary(saleProductOrderDetailList)
	}

	bookItemsReq = bookItems
	arrangeItemsReq = arrangeItems
	return
}

// 审核
func (r *SaleProductOrderRepo) auditSaleProductOrder(ctx context.Context, tx *mysql_base.Tx, saleProductOrder model.SaleProductOrder) (err error) {
	// 审核
	err = saleProductOrder.Audit(ctx)
	if err != nil {
		return
	}
	saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, tx, saleProductOrder)
	if err != nil {
		return
	}
	return
}

func (r *SaleProductOrderRepo) UpdateStatusWait(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (
	bookItemsReq productStructure.UpdateStockProductDetailParamList,
	pmcPlanSummaryReq *structure.AuditUpdatePushed,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		saleProductOrder           model.SaleProductOrder
		saleProductOrderDetailList model.SaleProductOrderDetailList
		_salePlanOrderItemIds      = make([]uint64, 0)
	)

	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	saleProductOrderDetailList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, req.Id)
	if err != nil {
		return
	}
	// 消审
	err = saleProductOrder.Wait(ctx)
	if err != nil {
		return
	}

	saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}

	var (
		bookItems = make(productStructure.UpdateStockProductDetailParamList, 0)
	)
	for _, detail := range saleProductOrderDetailList {
		// 根据预约匹数占用库存
		// 把预约匹数不为0的数据转换为生成配布单的结构体入参
		if detail.BookRoll != 0 || detail.Weight != 0 {
			bookItem := detail.ToUpdateStockProductDetailParam(false)
			bookItem.OrderNo = saleProductOrder.OrderNo
			bookItems = append(bookItems, bookItem)
		}
		if detail.PlanDetailId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, detail.PlanDetailId)
		}
	}
	if saleProductOrder.PmcGreyPlanOrderId != 0 {
		pmcPlanSummaryReq = saleProductOrder.ToWaitUpdatePmcPlanOrderSummary(saleProductOrderDetailList)
	}
	bookItemsReq = bookItems
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *SaleProductOrderRepo) UpdateStatusReject(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, err error) {
	var (
		saleProductOrder model.SaleProductOrder
	)

	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}
	// 驳回
	err = saleProductOrder.Reject(ctx)
	if err != nil {
		return
	}

	saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}
	return
}

func (r *SaleProductOrderRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (
	data structure.UpdateSaleProductOrderAuditStatusData, BackPlanDetail map[uint64][2]int, err error) {
	var (
		saleProductOrder model.SaleProductOrder
		detailList       model.SaleProductOrderDetailList
		_BackPlanDetail  = make(map[uint64][2]int)
	)

	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	detailList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, req.Id)
	if err != nil {
		return
	}
	for _, detail := range detailList {
		if val, ok := _BackPlanDetail[detail.PlanDetailId]; ok {
			val[0] += detail.Roll
			val[1] += detail.Weight
			_BackPlanDetail[detail.PlanDetailId] = val
		} else {
			_BackPlanDetail[detail.PlanDetailId] = [2]int{detail.Roll, detail.Weight}
		}
	}
	BackPlanDetail = _BackPlanDetail

	// 作废
	err = saleProductOrder.Cancel(ctx)
	if err != nil {
		return
	}

	saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, r.tx, saleProductOrder)
	if err != nil {
		return
	}
	return
}

func (r *SaleProductOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateSaleProductOrderBusinessCloseParam) (data structure.UpdateSaleProductOrderBusinessCloseData, err error) {
	var (
		saleProductOrderList model.SaleProductOrderList
	)
	saleProductOrderList, err = r.saleProductOrderDao.FindByIds(ctx, r.tx, req.Id.ToUint64(), true)
	if err != nil {
		return
	}
	for _, saleProductOrder := range saleProductOrderList {

		err = saleProductOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		saleProductOrder, err = r.saleProductOrderDao.MustUpdate(ctx, r.tx, saleProductOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *SaleProductOrderRepo) Delete(ctx context.Context, req *structure.DeleteSaleProductOrderParam) (data structure.DeleteSaleProductOrderData, err error) {
	var (
		saleProductOrderList model.SaleProductOrderList
	)

	saleProductOrderList, err = r.saleProductOrderDao.FindByIds(ctx, r.tx, req.Id.ToUint64(), true)
	if err != nil {
		return
	}

	for _, saleProductOrder := range saleProductOrderList {
		// 删除
		err = r.saleProductOrderDao.MustDelete(ctx, r.tx, saleProductOrder)
		if err != nil {
			return
		}
		data.Id = append(data.Id, saleProductOrder.Id)
	}
	return
}

func (r *SaleProductOrderRepo) Get(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderData, err error) {
	var (
		saleProductOrder           model.SaleProductOrder
		saleProductOrderDetailList model.SaleProductOrderDetailList
		items                      = make(structure.GetSaleProductOrderDetailDataList, 0)
		record                     info_basic_structure.GetSaleShipmentTypeData
	)
	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	saleProductOrderDetailList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, req.Id)
	if err != nil {
		return
	}
	// 获取销售发货类型
	if saleProductOrder.SaleShipmentName != "" {
		repo := aggs_info_basic.NewInfoSaleShipmentTypeRepo(ctx, r.tx, r.isCache)
		record, err = repo.Get(ctx, &info_basic_structure.GetSaleShipmentTypeQuery{Name: saleProductOrder.SaleShipmentName})
		if err != nil {
			return
		}
	}

	var (
		sumStockIds                   = set.NewUint64Set()
		warehouseIds                  = set.NewUint64Set()
		bizUnitIds                    = set.NewUint64Set()
		productLevelIds               = set.NewUint64Set()
		productIds                    = set.NewUint64Set()
		productColorIds               = set.NewUint64Set()
		saleLevelIds                  = set.NewUint64Set()
		productColorKindIds           = set.NewUint64Set()
		productKindNameIds            = set.NewUint64Set()
		measurementUnitIds            = set.NewUint64Set()
		saleProductPlanOrderDetailIds = set.NewUint64Set()
		saleSystemSvc                 = sale_system.NewSaleSystemClient()
		employeeSvc                   = employee.NewClientEmployeeService()
		// logisticsCompanySvc           = info_basic_data.NewInfoSaleLogisticsCompanyClient()
		infoSaleTaxableItemSvc = info_basic_data.NewInfoSaleTaxableItemClient()
		departmentSvc          = department.NewDepartmentClient()
		userSvc                = user_pb.NewUserClient()
		productStockSvc        = product_pb.NewProductStockClient()
		warehouseSvc           = warehouse.NewPhysicalWarehouseClient()
		bizUnitSvc             = biz_unit.NewClientBizUnitService()
		productLevelSvc        = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		productSvc             = product2.NewProductClient()
		productColorSvc        = product2.NewProductColorClient()
		saleLevelSvc           = sale_price.NewSaleLevelClient()
		productColorKindSvc    = type_basic_data.NewTypeFinishedProductColorClient()
		productKindNameSvc     = type_basic_data.NewTypeFabricClient()
		measurementUnitNameSvc = info_basic_data.NewInfoBaseMeasurementUnitClient()
		stockProductMap        map[uint64]product_pb.StockProduct
		warehouseName          map[uint64]string
		bizUnit                map[uint64][2]string
		productLevel           map[uint64]string
		productItems           map[uint64]*product2.ProductRes
		productColorItem       product2.ProductColorResList
		saleLevel              map[uint64]string
		productColorKind       map[uint64]string
		productKindName        map[uint64]string
		measurementUnitName    map[uint64]string
		saleSystem             map[uint64]string
		saleGroup              map[uint64]string
		employeeName           map[uint64]string
		// logisticsCompany              map[uint64]string
		// logisticsName                 map[uint64]string
		infoSaleTaxableItem map[uint64]string
		departmentName      map[uint64]string
		user                map[uint64]string
	)
	for _, detail := range saleProductOrderDetailList {
		sumStockIds.Add(detail.StockProductId)
		warehouseIds.Add(detail.WarehouseId)
		bizUnitIds.Add(detail.CustomerId)
		productLevelIds.Add(detail.ProductLevelId)
		productIds.Add(detail.ProductId)
		productColorIds.Add(detail.ProductColorId)
		saleLevelIds.Add(detail.SaleLevelId)
		productColorKindIds.Add(detail.ProductColorKindId)
		productKindNameIds.Add(detail.ProductKindId)
		measurementUnitIds.Add(detail.MeasurementUnitId)
		measurementUnitIds.Add(detail.AuxiliaryUnitId)
		saleProductPlanOrderDetailIds.Add(detail.PlanDetailId)
	}
	warehouseIds.Add(saleProductOrder.WarehouseId)
	bizUnitIds.Add(saleProductOrder.CustomerId)

	err = errgroup.Finish(ctx, 0,
		func(ctx context.Context) error {
			saleSystem, _ = saleSystemSvc.GetSaleSystemByIds(ctx, []uint64{saleProductOrder.SaleSystemId})
			return nil
		},
		// func(ctx context.Context) error {
		// 	logisticsIds := mysql_base.GetUInt64List(saleProductOrder, "process_factory_id")
		// 	logisticsName, _ = bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
		// 	return nil
		// },
		// func(ctx context.Context) error {
		// 	logisticsCompanyIds := mysql_base.GetUInt64List(saleProductOrder, "logistics_company_id")
		// 	logisticsCompany, _ = logisticsCompanySvc.GetInfoSaleLogisticsCompanyNameByIds(ctx, logisticsCompanyIds)
		// 	return nil
		// },
		func(ctx context.Context) error {
			saleGroupIds := mysql_base.GetUInt64List(saleProductOrder, "sale_group_id")
			saleGroup, _ = bizUnitSvc.GetSaleGroupNameByIds(ctx, saleGroupIds)
			return nil
		},
		func(ctx context.Context) error {
			employeeIds := mysql_base.GetUInt64List(saleProductOrder, "employee_id")
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
			return nil
		},
		func(ctx context.Context) error {
			infoSaleTaxableItemIds := mysql_base.GetUInt64List(saleProductOrder, "info_sale_taxable_item_id")
			infoSaleTaxableItem, _ = infoSaleTaxableItemSvc.GetInfoSaleTaxableItemNameByIds(ctx, infoSaleTaxableItemIds)
			return nil
		},
		func(ctx context.Context) error {
			departmentIds := mysql_base.GetUInt64List(saleProductOrder, "department_id")
			departmentName, _ = departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
			return nil
		},
		func(ctx context.Context) error {
			userIds := mysql_base.GetUInt64List(saleProductOrder, "user_id")
			user, _ = userSvc.GetUserNameByIds(ctx, userIds)
			return nil
		},
		func(ctx context.Context) error {
			// 获取分录行的缸号库存条件
			stockProductMap, _ = productStockSvc.GetStockByIds(ctx, sumStockIds.List())
			return nil
		},
		func(ctx context.Context) error {
			warehouseName, _ = warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds.List())
			return nil
		},
		func(ctx context.Context) error {
			bizUnit, _ = bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds.List())
			return nil
		},
		func(ctx context.Context) error {
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds.List())
			return nil
		},
		func(ctx context.Context) error {
			productItems, _ = productSvc.GetProductMapByIds(ctx, productIds.List())
			return nil
		},
		func(ctx context.Context) error {
			productColorItem, _ = productColorSvc.GetProductColorByIds(ctx, productColorIds.List())
			return nil
		},
		func(ctx context.Context) error {
			saleLevel, _ = saleLevelSvc.GetSaleLevelNameByIds(ctx, saleLevelIds.List())
			return nil
		},
		func(ctx context.Context) error {
			productColorKind, _ = productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds.List())
			return nil
		},
		func(ctx context.Context) error {
			productKindName, _ = productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds.List())
			return nil
		},
		func(ctx context.Context) error {
			measurementUnitName, _ = measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds.List())
			return nil
		},
	)
	if err != nil {
		return
	}

	o := structure.GetSaleProductOrderData{}
	o.Id = saleProductOrder.Id
	o.CreateTime = tools.MyTime(saleProductOrder.CreateTime)
	o.UpdateTime = tools.MyTime(saleProductOrder.UpdateTime)
	o.CreatorId = saleProductOrder.CreatorId
	o.CreatorName = saleProductOrder.CreatorName
	o.UpdaterId = saleProductOrder.UpdaterId
	o.UpdateUserName = saleProductOrder.UpdaterName
	o.SaleSystemId = saleProductOrder.SaleSystemId
	o.SaleSystemName = saleSystem[saleProductOrder.SaleSystemId]
	o.VoucherNumber = saleProductOrder.VoucherNumber
	o.OrderTime = tools.MyTime(saleProductOrder.OrderTime)
	o.SendProductType = saleProductOrder.SendProductType
	o.SendProductTypeName = saleProductOrder.SendProductType.String()
	o.WarehouseId = saleProductOrder.WarehouseId
	o.WarehouseName = warehouseName[saleProductOrder.WarehouseId]
	o.CustomerId = saleProductOrder.CustomerId
	o.CustomerCode = bizUnit[saleProductOrder.CustomerId][0]
	o.CustomerName = bizUnit[saleProductOrder.CustomerId][1]
	o.SaleUserId = saleProductOrder.SaleUserId
	o.SaleUserName = employeeName[saleProductOrder.SaleUserId]
	o.SaleFollowerId = saleProductOrder.SaleFollowerId
	o.SaleFollowerName = employeeName[saleProductOrder.SaleFollowerId]
	o.SettleType = saleProductOrder.SettleType
	o.SettleTypeName = saleProductOrder.SettleType.String()
	o.ProcessFactoryId = saleProductOrder.ProcessFactoryId
	o.ProcessFactoryName = saleProductOrder.ProcessFactory
	o.Contacts = saleProductOrder.Contacts
	o.ContactPhone = saleProductOrder.ContactPhone
	o.PrintTag = saleProductOrder.PrintTag
	o.LogisticsArea = saleProductOrder.LogisticsArea
	o.ReceiptAddress = saleProductOrder.ReceiptAddress
	o.InfoSaleTaxableItemId = saleProductOrder.InfoSaleTaxableItemId
	o.InfoSaleTaxableItemName = infoSaleTaxableItem[saleProductOrder.InfoSaleTaxableItemId]
	o.SaleTaxRate = tools.Hundred(saleProductOrder.SaleTaxRate)
	o.PostageItems = saleProductOrder.PostageItems
	o.PostageItemsName = saleProductOrder.PostageItems.String()
	o.OrderNo = saleProductOrder.OrderNo
	o.Number = saleProductOrder.Number
	o.AuditStatus = saleProductOrder.AuditStatus
	o.AuditStatusName = saleProductOrder.AuditStatus.String()
	o.DepartmentId = saleProductOrder.DepartmentId
	o.DepartmentName = departmentName[saleProductOrder.DepartmentId]
	o.CompanyId = saleProductOrder.CompanyId
	o.AuditorId = saleProductOrder.AuditorId
	o.AuditorName = user[saleProductOrder.AuditorId]
	o.AuditDate = tools.MyTime(saleProductOrder.AuditDate)
	o.BusinessClose = saleProductOrder.BusinessClose
	o.BusinessCloseName = saleProductOrder.BusinessClose.String()
	o.BusinessCloseUserId = saleProductOrder.BusinessCloseUserId
	o.BusinessCloseUserName = user[saleProductOrder.BusinessCloseUserId]
	o.BusinessCloseTime = tools.MyTime(saleProductOrder.BusinessCloseTime)
	o.InternalRemark = saleProductOrder.InternalRemark
	o.SendProductRemark = saleProductOrder.SendProductRemark
	o.SaleGroupId = saleProductOrder.SaleGroupId
	o.SaleGroupName = saleGroup[saleProductOrder.SaleGroupId]
	o.IsWithTaxRate = saleProductOrder.IsWithTaxRate
	o.SaleShipmentName = saleProductOrder.SaleShipmentName
	o.ReceiptAddressDetail = saleProductOrder.ReceiptAddressDetail
	o.ProcessFactory = saleProductOrder.ProcessFactory
	o.LogisticsCompanyName = saleProductOrder.LogisticsCompany
	o.LogisticsCompany = saleProductOrder.LogisticsCompany
	o.SaleShipmentTypeCode = record.Code
	o.SaleMode = saleProductOrder.SaleMode
	o.SaleModeName = saleProductOrder.SaleMode.String()
	o.TotalItem = len(productIds.List())
	o.TotalColor = len(productColorIds.List())
	o.PickUpGoodsInOrder = saleProductOrder.PickUpGoodsInOrder
	o.SameColorSameDyeLot = saleProductOrder.SameColorSameDyeLot
	for _, saleProductOrderDetail := range saleProductOrderDetailList {
		var standardWeight int
		item := structure.GetSaleProductOrderDetailData{}
		item.Id = saleProductOrderDetail.Id
		item.StockProductId = saleProductOrderDetail.StockProductId
		item.SaleProductOrderId = saleProductOrderDetail.SaleProductOrderId
		item.ProductColorId = saleProductOrderDetail.ProductColorId
		item.ProductColorCode = productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId).ProductColorCode
		item.ProductColorName = productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId).ProductColorName
		if colorInfo := productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId); len(colorInfo.TextureURL) > 0 {
			item.TextureURL = colorInfo.TextureURL[0]
		}
		item.ProductColorKindId = saleProductOrderDetail.ProductColorKindId
		item.ProductColorKindName = productColorKind[saleProductOrderDetail.ProductColorKindId]
		item.CustomerId = saleProductOrderDetail.CustomerId
		item.CustomerCode = bizUnit[saleProductOrderDetail.CustomerId][0]
		item.CustomerName = bizUnit[saleProductOrderDetail.CustomerId][1]
		item.ProductId = saleProductOrderDetail.ProductId
		if productItem, ok := productItems[saleProductOrderDetail.ProductId]; ok {
			item.ProductCode = productItem.FinishProductCode
			item.ProductName = productItem.FinishProductName
			standardWeight = productItem.StandardWeight
		}
		item.ProductLevelId = saleProductOrderDetail.ProductLevelId
		item.ProductLevelName = productLevel[saleProductOrderDetail.ProductLevelId]
		item.ProductKindId = saleProductOrderDetail.ProductKindId
		item.ProductKindName = productKindName[saleProductOrderDetail.ProductKindId]
		item.DyelotNumber = saleProductOrderDetail.DyelotNumber
		item.ProductRemark = saleProductOrderDetail.ProductRemark
		item.MeasurementUnitId = saleProductOrderDetail.MeasurementUnitId
		item.MeasurementUnitName = measurementUnitName[saleProductOrderDetail.MeasurementUnitId]
		item.AuxiliaryUnitId = saleProductOrderDetail.AuxiliaryUnitId
		item.AuxiliaryUnitName = measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]
		if item.AuxiliaryUnitId == 0 {
			item.AuxiliaryUnitId = saleProductOrderDetail.MeasurementUnitId
			item.AuxiliaryUnitName = measurementUnitName[saleProductOrderDetail.MeasurementUnitId]
		}
		item.Weight = saleProductOrderDetail.Weight
		item.Roll = saleProductOrderDetail.Roll
		item.CustomerAccountNum = saleProductOrderDetail.CustomerAccountNum
		item.Remark = saleProductOrderDetail.Remark
		item.StandardSalePrice = saleProductOrderDetail.StandardSalePrice
		item.SaleLevelId = saleProductOrderDetail.SaleLevelId
		item.SaleLevelName = saleLevel[saleProductOrderDetail.SaleLevelId]
		item.OffsetSalePrice = saleProductOrderDetail.OffsetSalePrice
		item.SalePrice = saleProductOrderDetail.SalePrice
		item.WeightError = saleProductOrderDetail.WeightError
		item.OffsetWeightError = saleProductOrderDetail.OffsetWeightError
		item.AdjustWeightError = saleProductOrderDetail.AdjustWeightError
		item.SettleWeightError = saleProductOrderDetail.SettleWeightError
		item.Length = saleProductOrderDetail.Length
		item.StandardLengthCutSalePrice = saleProductOrderDetail.StandardLengthCutSalePrice
		item.OffsetLengthCutSalePrice = saleProductOrderDetail.OffsetLengthCutSalePrice
		item.LengthCutSalePrice = saleProductOrderDetail.LengthCutSalePrice
		item.OtherPrice = saleProductOrderDetail.OtherPrice
		item.WarehouseId = saleProductOrderDetail.WarehouseId
		item.WarehouseName = warehouseName[saleProductOrderDetail.WarehouseId]
		// 审核前读最新，审核后读记录
		if saleProductOrder.AuditStatus != common_system.OrderStatusAudited {
			// 读最新
			if item.DyelotNumber == "" {
				item.StockRoll = saleProductOrderDetail.StockRoll
				item.AvailableWeight = saleProductOrderDetail.AvailableWeight
			} else {
				rwl, _ := productStockSvc.GetStockByDyelotNumberAndColor(ctx, product_pb.GetCADRWLParam{
					ProductColorId: item.ProductColorId,
					SumStockId:     item.StockProductId,
					DeylotNubmer:   item.DyelotNumber,
				})
				item.StockRoll = rwl[3]
				item.AvailableWeight = rwl[4]
			}
		} else {
			item.StockRoll = saleProductOrderDetail.StockRoll
			item.AvailableWeight = saleProductOrderDetail.AvailableWeight
		}
		// item.StockLength = saleProductOrderDetail.StockLength
		item.BookRoll = saleProductOrderDetail.BookRoll
		item.PurchaseRoll = saleProductOrderDetail.PurchaseRoll
		item.PurchaseWeight = saleProductOrderDetail.PurchaseWeight
		item.PurchaseLength = saleProductOrderDetail.PurchaseLength
		item.ShortageRoll = saleProductOrderDetail.ShortageRoll
		item.ShortageWeight = saleProductOrderDetail.ShortageWeight
		item.ShortageLength = saleProductOrderDetail.ShortageLength
		item.PlanDetailId = saleProductOrderDetail.PlanDetailId
		item.StockRemark = stockProductMap[saleProductOrderDetail.StockProductId].Remark
		totalPrice, totalWeight, totalLength, totalRoll := calculateDetailPriceV2(&item, standardWeight)
		o.TotalPrice += totalPrice
		o.TotalWeight += totalWeight
		o.TotalLength += totalLength
		o.TotalRoll += totalRoll
		items = append(items, item)
	}
	o.ItemData = items
	data = o
	return
}

func (r *SaleProductOrderRepo) MPGet(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderDataV2, err error) {
	var (
		saleProductOrder           model.SaleProductOrder
		saleProductOrderDetailList model.SaleProductOrderDetailList
		sumStockIds                = set.NewUint64Set()
		productStockSvc            = product_pb.NewProductStockClient()
		productColorKindSvc        = type_basic_data.NewTypeFinishedProductColorClient()
		items                      = make(structure.GetSaleProductOrderDetailDataListV2, 0)
		productMap                 = make(map[uint64]structure.GetSaleProductOrderColorDetailData, 0)
		productColorKindIds        = set.NewUint64Set()
		productColorKind           map[uint64]string
		productLevel               map[uint64]string
	)
	saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	saleProductOrderDetailList, err = r.saleProductOrderDetailDao.FindByParentId(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	for _, detail := range saleProductOrderDetailList {
		sumStockIds.Add(detail.StockProductId)
	}

	saleSystemIds := mysql_base.GetUInt64List(saleProductOrder, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64ListV2("warehouse_id", saleProductOrder, saleProductOrderDetailList)
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	bizUnitIds := append(mysql_base.GetUInt64List(saleProductOrder, "biz_unit_id"), mysql_base.GetUInt64List(saleProductOrderDetailList, "biz_unit_id")...)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64List(saleProductOrder, "process_factory_id")
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(saleProductOrder, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	logisticsCompanyIds := mysql_base.GetUInt64List(saleProductOrder, "logistics_company_id")
	logisticsCompanySvc := info_basic_data.NewInfoSaleLogisticsCompanyClient()
	logisticsCompany, err := logisticsCompanySvc.GetInfoSaleLogisticsCompanyNameByIds(ctx, logisticsCompanyIds)
	if err != nil {
		return
	}

	infoSaleTaxableItemIds := mysql_base.GetUInt64List(saleProductOrder, "info_sale_taxable_item_id")
	infoSaleTaxableItemSvc := info_basic_data.NewInfoSaleTaxableItemClient()
	infoSaleTaxableItem, err := infoSaleTaxableItemSvc.GetInfoSaleTaxableItemNameByIds(ctx, infoSaleTaxableItemIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "product_id")
	productSvc := product2.NewProductClient()
	productItems, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	measurementUnitIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "measurement_unit_id")
	auxiliaryUnitIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "auxiliary_unit_id")
	measurementUnitIds = append(measurementUnitIds, auxiliaryUnitIds...)
	measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)

	productColorKind, _ = productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds.List())
	if err != nil {
		return
	}

	var (
		totalRoll,
		totalWeight,
		totalLength,
		totalColor int
		totalPrice float64
	)
	var total = make(map[string]int) // 统计面料信息
	o := structure.GetSaleProductOrderDataV2{}
	o.Id = saleProductOrder.Id
	o.CreateTime = tools.MyTime(saleProductOrder.CreateTime)
	o.UpdateTime = tools.MyTime(saleProductOrder.UpdateTime)
	o.CreatorId = saleProductOrder.CreatorId
	o.CreatorName = saleProductOrder.CreatorName
	o.UpdaterId = saleProductOrder.UpdaterId
	o.UpdateUserName = saleProductOrder.UpdaterName
	o.SaleSystemId = saleProductOrder.SaleSystemId
	o.SaleSystemName = saleSystem[saleProductOrder.SaleSystemId]
	o.VoucherNumber = saleProductOrder.VoucherNumber
	o.OrderTime = tools.MyTime(saleProductOrder.OrderTime)
	o.SendProductType = saleProductOrder.SendProductType
	o.SendProductTypeName = saleProductOrder.SendProductType.String()
	o.WarehouseId = saleProductOrder.WarehouseId
	o.WarehouseName = warehouseName[saleProductOrder.WarehouseId]
	o.SaleUserId = saleProductOrder.SaleUserId
	o.SaleUserName = employeeName[saleProductOrder.SaleUserId]
	o.SaleFollowerId = saleProductOrder.SaleFollowerId
	o.SaleFollowerName = employeeName[saleProductOrder.SaleFollowerId]
	o.ProcessFactoryId = saleProductOrder.ProcessFactoryId
	o.ProcessFactoryName = logisticsName[saleProductOrder.ProcessFactoryId]
	o.Contacts = saleProductOrder.Contacts
	o.ContactPhone = saleProductOrder.ContactPhone
	o.PrintTag = saleProductOrder.PrintTag
	o.LogisticsCompanyId = saleProductOrder.LogisticsCompanyId
	o.LogisticsCompanyName = logisticsCompany[saleProductOrder.LogisticsCompanyId]
	o.LogisticsCompany = saleProductOrder.LogisticsCompany
	o.ProcessFactory = saleProductOrder.ProcessFactory
	o.LogisticsArea = saleProductOrder.LogisticsArea
	o.ReceiptAddress = saleProductOrder.ReceiptAddress
	o.ReceiptAddressDetail = saleProductOrder.ReceiptAddressDetail
	o.OrderNo = saleProductOrder.OrderNo
	o.AuditStatus = saleProductOrder.AuditStatus
	o.AuditStatusName = saleProductOrder.AuditStatus.String()
	o.SendProductRemark = saleProductOrder.SendProductRemark
	o.SaleMode = saleProductOrder.SaleMode                       // 订单类型
	o.PickUpGoodsInOrder = saleProductOrder.PickUpGoodsInOrder   // 齐单提货
	o.SameColorSameDyeLot = saleProductOrder.SameColorSameDyeLot // 同色同缸
	o.SaleShipmentName = saleProductOrder.SaleShipmentName       // 发货类型名称
	o.CustomerId = saleProductOrder.CustomerId                   // 客户id
	o.CustomerName = bizUnit[saleProductOrder.CustomerId][1]     // 客户名称
	o.InfoSaleTaxableItemId = saleProductOrder.InfoSaleTaxableItemId
	o.InfoSaleTaxableItemName = infoSaleTaxableItem[saleProductOrder.InfoSaleTaxableItemId]
	o.SaleTaxRate = tools.Hundred(saleProductOrder.SaleTaxRate)
	o.InternalRemark = saleProductOrder.InternalRemark
	o.IsWithTaxRate = saleProductOrder.IsWithTaxRate
	o.SaleModeName = saleProductOrder.SaleMode.String()
	o.CustomerName = bizUnit[saleProductOrder.CustomerId][1]
	// o.OrderTypeName = fmt.Sprintf("%s-%s", saleProductOrder.SendProductType.String())

	// 获取配布信息
	repo := aggs.NewFpmArrangeOrderRepo(r.tx)
	o.ArrangeData, _, err = repo.GetList(ctx, &productStructure.GetFpmArrangeOrderListQuery{SrcOrderNo: o.OrderNo})
	// 获取配布成品资料
	for i, item := range o.ArrangeData {
		orderData, err := repo.Get(ctx, &productStructure.GetFpmArrangeOrderQuery{Id: item.Id})
		if err != nil {
			continue
		}
		o.OrderProgressName = item.BusinessStatusName
		o.ArrangeData[i] = orderData
		// 获取配布的源单信息
		saleProductOrder, err = r.saleProductOrderDao.MustFirstById(ctx, r.tx, item.SrcId)
		if err != nil {
			continue
		}
		o.ArrangeData[i].OrderTypeName = fmt.Sprintf("%s-%s", item.OutOrderTypeName, saleProductOrder.SendProductType.String())
	}
	// 获取成品详情
	for _, saleProductOrderDetail := range saleProductOrderDetailList {
		var (
			standardWeight int                                              // 成品资料标准数量
			item           = structure.GetSaleProductOrderDetailDataV2{}    // 成品信息
			detail         = structure.GetSaleProductOrderColorDetailData{} // 色号详情
		)

		item.Id = saleProductOrderDetail.Id
		item.SaleProductOrderId = saleProductOrderDetail.SaleProductOrderId
		item.ProductId = saleProductOrderDetail.ProductId

		detail.ProductRemark = saleProductOrderDetail.ProductRemark
		detail.CustomerId = saleProductOrderDetail.CustomerId
		detail.CustomerCode = bizUnit[saleProductOrder.CustomerId][0]
		detail.CustomerName = bizUnit[saleProductOrder.CustomerId][1]
		detail.ProductColorKindId = saleProductOrderDetail.ProductColorKindId
		detail.ProductId = saleProductOrderDetail.ProductId
		detail.SaleLevelId = saleProductOrderDetail.SaleLevelId
		detail.PurchaseRoll = saleProductOrderDetail.PurchaseRoll
		detail.StockProductId = saleProductOrderDetail.StockProductId
		detail.ProductColorId = saleProductOrderDetail.ProductColorId
		detail.ProductColorCode = productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId).ProductColorCode
		detail.ProductColorName = productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId).ProductColorName
		if colorInfo := productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId); len(colorInfo.TextureURL) > 0 {
			detail.TextureURL = colorInfo.TextureURL[0]
		}
		detail.ProductColorKindName = productColorKind[saleProductOrderDetail.ProductColorKindId]
		detail.WarehouseId = saleProductOrderDetail.WarehouseId
		detail.WarehouseName = warehouseName[saleProductOrderDetail.WarehouseId]
		detail.ProductLevelName = productLevel[saleProductOrderDetail.ProductLevelId]
		if productItem, ok := productItems[saleProductOrderDetail.ProductId]; ok {
			item.ProductCode = productItem.FinishProductCode
			item.ProductName = productItem.FinishProductName
			standardWeight = productItem.StandardWeight
		}
		detail.ProductLevelId = saleProductOrderDetail.ProductLevelId
		detail.DyelotNumber = saleProductOrderDetail.DyelotNumber
		detail.AuxiliaryUnitId = saleProductOrderDetail.AuxiliaryUnitId
		detail.MeasurementUnitId = saleProductOrderDetail.MeasurementUnitId
		detail.MeasurementUnitName = measurementUnitName[saleProductOrderDetail.MeasurementUnitId]
		detail.AuxiliaryUnitName = measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]
		detail.StandardWeight = standardWeight
		if detail.AuxiliaryUnitId == 0 {
			detail.AuxiliaryUnitId = detail.MeasurementUnitId
			detail.AuxiliaryUnitName = measurementUnitName[saleProductOrderDetail.MeasurementUnitId]
		}
		detail.Weight = saleProductOrderDetail.Weight
		detail.Roll = saleProductOrderDetail.Roll
		detail.Length = saleProductOrderDetail.Length
		detail.Remark = saleProductOrderDetail.Remark
		detail.StandardLengthCutSalePrice = saleProductOrderDetail.StandardLengthCutSalePrice // 剪板标准销售价格
		detail.LengthCutSalePrice = saleProductOrderDetail.LengthCutSalePrice                 // 剪板销售价格
		detail.StandardSalePrice = saleProductOrderDetail.StandardSalePrice                   // 大货标准销售价格
		detail.SalePrice = saleProductOrderDetail.SalePrice                                   // 大货销售价格
		detail.BookRoll = saleProductOrderDetail.BookRoll
		detail.ShortageRoll = saleProductOrderDetail.ShortageRoll
		if detail.StockRoll == 0 {
			detail.AvailableRoll = detail.ShortageRoll
		} else {
			detail.AvailableRoll = detail.StockRoll - detail.BookRoll
		}
		item.WarehouseId = saleProductOrderDetail.WarehouseId
		item.WarehouseName = warehouseName[saleProductOrderDetail.WarehouseId]
		// 计量单位
		o.AuxiliaryUnitId = detail.AuxiliaryUnitId
		o.AuxiliaryUnitName = detail.AuxiliaryUnitName
		o.MeasurementUnitId = detail.MeasurementUnitId
		o.MeasurementUnitName = detail.MeasurementUnitName

		detail.TotalPrice, detail.Weight, detail.Length, detail.Roll = calculateDetailPrice(&detail, standardWeight)

		totalPrice += detail.TotalPrice
		totalRoll += detail.Roll

		// 单位是否一致
		if saleProductOrderDetail.AuxiliaryUnitId == saleProductOrderDetail.MeasurementUnitId {
			totalWeight += detail.Weight
			total[measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]] += detail.Weight // 累加数量
		} else {
			totalLength += detail.Length
			total[measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]] += detail.Length // 累加长度的数量
		}

		// 审核前读最新，审核后读记录
		if saleProductOrder.AuditStatus != common_system.OrderStatusAudited {
			// 读最新
			if detail.DyelotNumber == "" {
				detail.StockRoll = saleProductOrderDetail.StockRoll
				detail.StockWeight = saleProductOrderDetail.AvailableWeight
			} else {
				rwl, _ := productStockSvc.GetStockByDyelotNumberAndColor(ctx, product_pb.GetCADRWLParam{
					ProductColorId: detail.ProductColorId,
					SumStockId:     detail.StockProductId,
					DeylotNubmer:   detail.DyelotNumber,
				})
				detail.StockRoll = rwl[3]
				detail.StockWeight = rwl[4]
			}
		} else {
			detail.StockRoll = saleProductOrderDetail.StockRoll
			detail.StockWeight = saleProductOrderDetail.AvailableWeight
		}
		// 判断成品是否存在，存在则将色号数据追加至成品当中
		_, ok := productMap[saleProductOrderDetail.ProductId]
		// 添加成品信息只map记录
		productMap[saleProductOrderDetail.ProductId] = detail
		// 如果存在成品信息
		if ok {
			// 将色号数据追加至对应的成品当中
			items[len(items)-1].ColorDetail = append(items[len(items)-1].ColorDetail, detail)
		} else {
			// 不存在则新增成品信息
			item.ColorDetail = append(item.ColorDetail, detail)
			items = append(items, item)
		}
	}
	totalColor = len(productColorIds)
	o.TotalItem = len(items)    // 总面料
	o.TotalWeight = totalWeight // 总重量
	o.TotalRoll = totalRoll     // 总匹数
	o.TotalColor = totalColor   // 总颜色
	o.TotalLength = totalLength // 总长度
	o.TotalPrice = totalPrice   // 预估价格
	o.ItemData = items
	// 大货类型
	if saleProductOrder.SaleMode == sale.BulkTypeOrder || saleProductOrder.SaleMode == sale.CustomerBulkTypeOrder {
		o.ItemInfo = fmt.Sprintf("%d种面料，%d个颜色，共%d匹", len(items), totalColor, o.TotalRoll/vars.Roll)
		o.ItemInfo += "("
		for s, i := range total {
			o.ItemInfo += fmt.Sprintf("%.2f%s,", float64(i)/float64(vars.Weight), s)
			o.MergeWeightUnit += fmt.Sprintf("%.2f%s", float64(i)/float64(vars.Weight), s)
		}
		o.ItemInfo = strings.TrimSuffix(o.ItemInfo, ",")
		o.ItemInfo += ")"
	} else {
		o.ItemInfo = fmt.Sprintf("%d种面料，%d个颜色，共", len(items), totalColor)
		for s, i := range total {
			o.ItemInfo += fmt.Sprintf("%.2f%s,", float64(i)/float64(vars.Weight), s)
			o.MergeWeightUnit += fmt.Sprintf("%.2f%s", float64(i)/float64(vars.Weight), s)
		}
		o.ItemInfo = strings.TrimSuffix(o.ItemInfo, ",")
	}
	data = o
	return
}

// 统计
func calculateDetailPrice(detail *structure.GetSaleProductOrderColorDetailData, standardWeight int) (TotalPrice float64,
	TotalWeight,
	TotalLength,
	TotalRoll int) {
	// 如果未设置标准重量，使用默认值
	if standardWeight == 0 {
		standardWeight = vars.StandardWeight
	}

	// 判断是否为大货(主辅单位一致)
	isBulkOrder := detail.AuxiliaryUnitId == detail.MeasurementUnitId

	// 判断是否填写了数量
	hasQuantity := detail.Weight != 0 || detail.Length != 0

	if hasQuantity {
		// 有填写数量的情况
		if isBulkOrder {
			// 大货: 数量 * 单价
			TotalPrice = float64(detail.Weight) / vars.Weight * float64(detail.SalePrice) / vars.PriceMult
			TotalWeight += detail.Weight
		} else {
			// 剪板: 长度 * 剪板单价
			TotalPrice = float64(detail.Length) / vars.Weight * float64(detail.LengthCutSalePrice) / vars.PriceMult
			TotalLength += detail.Length
		}
	} else {
		// 未填写数量的情况
		if isBulkOrder {
			// 大货: 标准数量 * 单价 * 匹数
			TotalPrice = float64(standardWeight) / vars.Weight * float64(detail.SalePrice/vars.PriceMult) * float64(detail.Roll/vars.Roll)
			TotalWeight += detail.Weight
		} else {
			// 剪板: 标准数量 * 剪板单价 * 匹数
			TotalPrice = float64(standardWeight) / vars.Weight * float64(detail.LengthCutSalePrice) / vars.PriceMult * float64(detail.Roll) / vars.Roll
			TotalLength += detail.Length
		}
	}
	// 累计匹数
	TotalRoll += detail.Roll
	return
}

func calculateDetailPriceV2(detail *structure.GetSaleProductOrderDetailData, standardWeight int) (TotalPrice,
	TotalWeight,
	TotalLength,
	TotalRoll int) {
	// 如果未设置标准重量，使用默认值
	if standardWeight == 0 {
		standardWeight = vars.StandardWeight
	}

	// 判断是否为大货(主辅单位一致)
	isBulkOrder := detail.AuxiliaryUnitId == detail.MeasurementUnitId

	// 判断是否填写了数量
	hasQuantity := detail.Weight != 0 || detail.Length != 0

	if hasQuantity {
		// 有填写数量的情况
		if isBulkOrder {
			// 大货: 数量 * 单价
			TotalPrice = (detail.Weight / vars.Weight) * (detail.SalePrice / vars.PriceMult)
			TotalWeight += detail.Weight
		} else {
			// 剪板: 长度 * 剪板单价
			TotalPrice = (detail.Length / vars.Weight) * (detail.LengthCutSalePrice / vars.PriceMult)
			TotalLength += detail.Length
		}
	} else {
		// 未填写数量的情况
		if isBulkOrder {
			// 大货: 标准数量 * 单价 * 匹数
			TotalPrice = (standardWeight / vars.Weight) * (detail.SalePrice / vars.PriceMult) * (detail.Roll / vars.Roll)
			TotalWeight += detail.Weight
		} else {
			// 剪板: 标准数量 * 剪板单价 * 匹数
			TotalPrice = (standardWeight / vars.Weight) * (detail.LengthCutSalePrice / vars.PriceMult) * (detail.Roll / vars.Roll)
			TotalLength += detail.Length
		}
	}
	// 累计匹数
	TotalRoll += detail.Roll
	return
}

func (r *SaleProductOrderRepo) GetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataList, total int, err error) {
	var (
		saleProductOrders model.SaleProductOrderList
	)
	// 获取用户登录平台
	req.Platform = metadata.GetLoginInfo(ctx).GetPlatform()
	saleProductOrders, total, err = r.saleProductOrderDao.SearchList(ctx, r.tx, req)
	if err != nil {
		return
	}

	list = buildOrders(ctx, saleProductOrders)
	return
}

func (r *SaleProductOrderRepo) MPGetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataListV2, total int, err error) {
	var (
		saleProductOrders          model.SaleProductOrderList
		saleProductOrderDetailList model.SaleProductOrderDetailList
		bizService                 = biz_pb.NewClientBizUnitService()
	)
	// 通过用户名模糊匹配客户的ids
	data, _ := bizService.GetBizUnitIdsByNameLike(ctx, req.OrderQuery)
	if len(data) != 0 {
		req.CustomerIds = append(req.CustomerIds, data...)
	}
	// 获取用户登录平台
	req.Platform = metadata.GetLoginInfo(ctx).GetPlatform()
	saleProductOrders, total, err = r.saleProductOrderDao.SearchList(ctx, r.tx, req)
	if err != nil {
		return
	}
	// 获取成品销售详情列表
	saleProductOrderDetailList, err = r.saleProductOrderDetailDao.FindByParentIds(ctx, r.tx, saleProductOrders.GetIds())
	if err != nil {
		return
	}

	list = buildMPOrders(ctx, r.tx, saleProductOrders, saleProductOrderDetailList)
	return
}

func (r *SaleProductOrderRepo) GetDropdownList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDropdownDataList, total int, err error) {
	var (
		saleProductOrders model.SaleProductOrderList
	)
	saleProductOrders, total, err = r.saleProductOrderDao.SearchList(ctx, r.tx, req)
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(saleProductOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(saleProductOrders, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(saleProductOrders, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64List(saleProductOrders, "process_factory_id")
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	saleGroupIds := mysql_base.GetUInt64List(saleProductOrders, "sale_group_id")
	saleGroup, err := bizUnitSvc.GetSaleGroupNameByIds(ctx, saleGroupIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(saleProductOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	logisticsCompanyIds := mysql_base.GetUInt64List(saleProductOrders, "logistics_company_id")
	logisticsCompanySvc := info_basic_data.NewInfoSaleLogisticsCompanyClient()
	logisticsCompany, err := logisticsCompanySvc.GetInfoSaleLogisticsCompanyNameByIds(ctx, logisticsCompanyIds)
	if err != nil {
		return
	}

	infoSaleTaxableItemIds := mysql_base.GetUInt64List(saleProductOrders, "info_sale_taxable_item_id")
	infoSaleTaxableItemSvc := info_basic_data.NewInfoSaleTaxableItemClient()
	infoSaleTaxableItem, err := infoSaleTaxableItemSvc.GetInfoSaleTaxableItemNameByIds(ctx, infoSaleTaxableItemIds)
	if err != nil {
		return
	}

	departmentIds := mysql_base.GetUInt64List(saleProductOrders, "department_id")
	departmentSvc := department.NewDepartmentClient()
	departmentName, err := departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(saleProductOrders, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	for _, saleProductOrder := range saleProductOrders.List() {
		o := structure.GetSaleProductOrderDropdownData{}
		o.Id = saleProductOrder.Id
		o.CreateTime = tools.MyTime(saleProductOrder.CreateTime)
		o.UpdateTime = tools.MyTime(saleProductOrder.UpdateTime)
		o.CreatorId = saleProductOrder.CreatorId
		o.CreatorName = saleProductOrder.CreatorName
		o.UpdaterId = saleProductOrder.UpdaterId
		o.UpdateUserName = saleProductOrder.UpdaterName
		o.SaleSystemId = saleProductOrder.SaleSystemId
		o.SaleSystemName = saleSystem[saleProductOrder.SaleSystemId]
		o.VoucherNumber = saleProductOrder.VoucherNumber
		o.OrderTime = tools.MyTime(saleProductOrder.OrderTime)
		o.SendProductType = saleProductOrder.SendProductType
		o.SendProductTypeName = saleProductOrder.SendProductType.String()
		o.WarehouseId = saleProductOrder.WarehouseId
		o.WarehouseName = warehouseName[saleProductOrder.WarehouseId]
		o.CustomerId = saleProductOrder.CustomerId
		o.CustomerCode = bizUnit[saleProductOrder.CustomerId][0]
		o.CustomerName = bizUnit[saleProductOrder.CustomerId][1]
		o.SaleUserId = saleProductOrder.SaleUserId
		o.SaleUserName = employeeName[saleProductOrder.SaleUserId]
		o.SaleFollowerId = saleProductOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[saleProductOrder.SaleFollowerId]
		o.SettleType = saleProductOrder.SettleType
		o.SettleTypeName = saleProductOrder.SettleType.String()
		o.ProcessFactoryId = saleProductOrder.ProcessFactoryId
		o.ProcessFactoryName = logisticsName[saleProductOrder.ProcessFactoryId]
		o.Contacts = saleProductOrder.Contacts
		o.ContactPhone = saleProductOrder.ContactPhone
		o.PrintTag = saleProductOrder.PrintTag
		o.LogisticsCompanyId = saleProductOrder.LogisticsCompanyId
		o.LogisticsCompanyName = logisticsCompany[saleProductOrder.LogisticsCompanyId]
		o.LogisticsArea = saleProductOrder.LogisticsArea
		o.ReceiptAddress = saleProductOrder.ReceiptAddress
		o.InfoSaleTaxableItemId = saleProductOrder.InfoSaleTaxableItemId
		o.InfoSaleTaxableItemName = infoSaleTaxableItem[saleProductOrder.InfoSaleTaxableItemId]
		o.SaleTaxRate = saleProductOrder.SaleTaxRate
		o.PostageItems = saleProductOrder.PostageItems
		o.PostageItemsName = saleProductOrder.PostageItems.String()
		o.OrderNo = saleProductOrder.OrderNo
		o.Number = saleProductOrder.Number
		o.AuditStatus = saleProductOrder.AuditStatus
		o.AuditStatusName = saleProductOrder.AuditStatus.String()
		o.DepartmentId = saleProductOrder.DepartmentId
		o.DepartmentName = departmentName[saleProductOrder.DepartmentId]
		o.CompanyId = saleProductOrder.CompanyId
		o.AuditorId = saleProductOrder.AuditorId
		o.AuditorName = user[saleProductOrder.AuditorId]
		o.AuditDate = tools.MyTime(saleProductOrder.AuditDate)
		o.BusinessClose = saleProductOrder.BusinessClose
		o.BusinessCloseName = saleProductOrder.BusinessClose.String()
		o.BusinessCloseUserId = saleProductOrder.BusinessCloseUserId
		o.BusinessCloseUserName = user[saleProductOrder.BusinessCloseUserId]
		o.BusinessCloseTime = tools.MyTime(saleProductOrder.BusinessCloseTime)
		o.InternalRemark = saleProductOrder.InternalRemark
		o.SendProductRemark = saleProductOrder.SendProductRemark
		o.SaleGroupId = saleProductOrder.SaleGroupId
		o.SaleGroupName = saleGroup[saleProductOrder.SaleGroupId]
		list = append(list, o)
	}
	return
}

func (r *SaleProductOrderRepo) Exist(ctx context.Context, getDetail bool, ids, summaryIds []uint64) (list structure.PushedRecordList, orderNos []string, exist bool, err error) {
	var (
		saleProductOrders          model.SaleProductOrderList
		saleProductOrderDetailList model.SaleProductOrderDetailList
	)
	saleProductOrders, err = r.saleProductOrderDao.FindByPmcGreyPlanOrderIds(ctx, r.tx, ids, summaryIds, false)
	if err != nil {
		return
	}

	saleProductOrderDetailList, err = r.saleProductOrderDetailDao.FindByParentIds(ctx, r.tx, saleProductOrders.GetIds())
	if err != nil {
		return
	}

	for _, saleProductOrder := range saleProductOrders {
		orderNos = append(orderNos, saleProductOrder.OrderNo)
	}
	if len(saleProductOrders) != 0 {
		exist = true
	}

	if getDetail {
		for _, saleProductOrder := range saleProductOrders {
			_saleProductOrderDetails := saleProductOrderDetailList.PickByOrderId(saleProductOrder.Id)
			data := structure.PushedRecord{}
			data.OrderId = saleProductOrder.Id
			data.OrderNo = saleProductOrder.OrderNo
			data.OrderStatus = saleProductOrder.AuditStatus
			data.OrderStatusName = saleProductOrder.AuditStatus.String()
			data.Roll = _saleProductOrderDetails.SumRoll()
			data.Weight = _saleProductOrderDetails.SumWeight()
			data.CreatorID = saleProductOrder.CreatorId
			data.CreatorName = saleProductOrder.CreatorName
			data.CreateTime = tools.MyTime(saleProductOrder.CreateTime)
			list = append(list, data)
		}
	}
	return
}

func (r *SaleProductOrderRepo) ExistBySaleProductPlanOrderId(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error) {
	var (
		saleProductOrders model.SaleProductOrderList
	)
	saleProductOrders, err = r.saleProductOrderDao.FindByIds(ctx, r.tx, ids, false)
	if err != nil {
		return
	}

	for _, saleProductOrder := range saleProductOrders {
		orderNos = append(orderNos, saleProductOrder.OrderNo)
	}
	if len(saleProductOrders) != 0 {
		exist = true
	}
	return
}

func buildOrders(
	ctx context.Context,
	saleProductOrders model.SaleProductOrderList,
) (res structure.GetSaleProductOrderDataList) {
	var list = make(structure.GetSaleProductOrderDataList, 0)
	saleSystemIds := mysql_base.GetUInt64List(saleProductOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(saleProductOrders, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(saleProductOrders, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	// logisticsIds := mysql_base.GetUInt64List(saleProductOrders, "process_factory_id")
	// logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	// if err != nil {
	//	return
	// }

	saleGroupIds := mysql_base.GetUInt64List(saleProductOrders, "sale_group_id")
	saleGroup, err := bizUnitSvc.GetSaleGroupNameByIds(ctx, saleGroupIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(saleProductOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	// logisticsCompanyIds := mysql_base.GetUInt64List(saleProductOrders, "logistics_company_id")
	// logisticsCompanySvc := info_basic_data.NewInfoSaleLogisticsCompanyClient()
	// logisticsCompany, err := logisticsCompanySvc.GetInfoSaleLogisticsCompanyNameByIds(ctx, logisticsCompanyIds)
	// if err != nil {
	//	return
	// }

	infoSaleTaxableItemIds := mysql_base.GetUInt64List(saleProductOrders, "info_sale_taxable_item_id")
	infoSaleTaxableItemSvc := info_basic_data.NewInfoSaleTaxableItemClient()
	infoSaleTaxableItem, err := infoSaleTaxableItemSvc.GetInfoSaleTaxableItemNameByIds(ctx, infoSaleTaxableItemIds)
	if err != nil {
		return
	}

	departmentIds := mysql_base.GetUInt64List(saleProductOrders, "department_id")
	departmentSvc := department.NewDepartmentClient()
	departmentName, err := departmentSvc.GetDepartmentNameByIds(ctx, departmentIds)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(saleProductOrders, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	for _, saleProductOrder := range saleProductOrders {
		o := &structure.GetSaleProductOrderData{}
		o.Id = saleProductOrder.Id
		o.CreateTime = tools.MyTime(saleProductOrder.CreateTime)
		o.UpdateTime = tools.MyTime(saleProductOrder.UpdateTime)
		o.CreatorId = saleProductOrder.CreatorId
		o.CreatorName = saleProductOrder.CreatorName
		o.UpdaterId = saleProductOrder.UpdaterId
		o.UpdateUserName = saleProductOrder.UpdaterName
		o.SaleSystemId = saleProductOrder.SaleSystemId
		o.SaleSystemName = saleSystem[saleProductOrder.SaleSystemId]
		o.VoucherNumber = saleProductOrder.VoucherNumber
		o.OrderTime = tools.MyTime(saleProductOrder.OrderTime)
		o.SendProductType = saleProductOrder.SendProductType
		o.SendProductTypeName = saleProductOrder.SendProductType.String()
		o.WarehouseId = saleProductOrder.WarehouseId
		o.WarehouseName = warehouseName[saleProductOrder.WarehouseId]
		o.CustomerId = saleProductOrder.CustomerId
		o.CustomerCode = bizUnit[saleProductOrder.CustomerId][0]
		o.CustomerName = bizUnit[saleProductOrder.CustomerId][1]
		o.SaleUserId = saleProductOrder.SaleUserId
		o.SaleUserName = employeeName[saleProductOrder.SaleUserId]
		o.SaleFollowerId = saleProductOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[saleProductOrder.SaleFollowerId]
		o.SettleType = saleProductOrder.SettleType
		o.SettleTypeName = saleProductOrder.SettleType.String()
		o.ProcessFactoryId = saleProductOrder.ProcessFactoryId
		o.ProcessFactoryName = saleProductOrder.ProcessFactory
		o.Contacts = saleProductOrder.Contacts
		o.ContactPhone = saleProductOrder.ContactPhone
		o.PrintTag = saleProductOrder.PrintTag
		o.LogisticsCompanyId = saleProductOrder.LogisticsCompanyId
		o.LogisticsCompanyName = saleProductOrder.LogisticsCompany
		o.LogisticsArea = saleProductOrder.LogisticsArea
		o.ReceiptAddress = saleProductOrder.ReceiptAddress
		o.InfoSaleTaxableItemId = saleProductOrder.InfoSaleTaxableItemId
		o.InfoSaleTaxableItemName = infoSaleTaxableItem[saleProductOrder.InfoSaleTaxableItemId]
		o.SaleTaxRate = tools.Hundred(saleProductOrder.SaleTaxRate)
		o.PostageItems = saleProductOrder.PostageItems
		o.PostageItemsName = saleProductOrder.PostageItems.String()
		o.OrderNo = saleProductOrder.OrderNo
		o.Number = saleProductOrder.Number
		o.AuditStatus = saleProductOrder.AuditStatus
		o.AuditStatusName = saleProductOrder.AuditStatus.String()
		o.DepartmentId = saleProductOrder.DepartmentId
		o.DepartmentName = departmentName[saleProductOrder.DepartmentId]
		o.CompanyId = saleProductOrder.CompanyId
		o.AuditorId = saleProductOrder.AuditorId
		o.AuditorName = user[saleProductOrder.AuditorId]
		o.AuditDate = tools.MyTime(saleProductOrder.AuditDate)
		o.BusinessClose = saleProductOrder.BusinessClose
		o.BusinessCloseName = saleProductOrder.BusinessClose.String()
		o.BusinessCloseUserId = saleProductOrder.BusinessCloseUserId
		o.BusinessCloseUserName = user[saleProductOrder.BusinessCloseUserId]
		o.BusinessCloseTime = tools.MyTime(saleProductOrder.BusinessCloseTime)
		o.InternalRemark = saleProductOrder.InternalRemark
		o.SendProductRemark = saleProductOrder.SendProductRemark
		o.SaleGroupId = saleProductOrder.SaleGroupId
		o.SaleGroupName = saleGroup[saleProductOrder.SaleGroupId]
		o.IsWithTaxRate = saleProductOrder.IsWithTaxRate
		o.SaleMode = saleProductOrder.SaleMode
		o.SaleModeName = saleProductOrder.SaleMode.String()
		list = append(list, o)
	}
	res = list
	return
}

func buildMPOrders(
	ctx context.Context,
	tx *mysql_base.Tx,
	saleProductOrders model.SaleProductOrderList,
	saleProductOrderDetailList model.SaleProductOrderDetailList, // 成品详情列表
) (res structure.GetSaleProductOrderDataListV2) {
	var list = make(structure.GetSaleProductOrderDataListV2, 0)
	saleSystemIds := mysql_base.GetUInt64List(saleProductOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(saleProductOrders, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(saleProductOrders, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	logisticsIds := mysql_base.GetUInt64List(saleProductOrders, "process_factory_id")
	logisticsName, err := bizUnitSvc.GetBizUnitFactoryLogisticsNameByIds(ctx, logisticsIds)
	if err != nil {
		return
	}

	employeeIds := mysql_base.GetUInt64List(saleProductOrders, "employee_id")
	employeeSvc := employee.NewClientEmployeeService()
	employeeName, err := employeeSvc.GetEmployeeNameByIds(ctx, employeeIds)
	if err != nil {
		return
	}

	logisticsCompanyIds := mysql_base.GetUInt64List(saleProductOrders, "logistics_company_id")
	logisticsCompanySvc := info_basic_data.NewInfoSaleLogisticsCompanyClient()
	logisticsCompany, err := logisticsCompanySvc.GetInfoSaleLogisticsCompanyNameByIds(ctx, logisticsCompanyIds)
	if err != nil {
		return
	}

	infoSaleTaxableItemIds := mysql_base.GetUInt64List(saleProductOrders, "info_sale_taxable_item_id")
	infoSaleTaxableItemSvc := info_basic_data.NewInfoSaleTaxableItemClient()
	infoSaleTaxableItem, err := infoSaleTaxableItemSvc.GetInfoSaleTaxableItemNameByIds(ctx, infoSaleTaxableItemIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "measurement_unit_id")
	auxiliaryUnitIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "auxiliary_unit_id")
	measurementUnitIds = append(measurementUnitIds, auxiliaryUnitIds...)
	measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(saleProductOrderDetailList, "product_id")
	productSvc := product2.NewProductClient()
	productItems, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}
	for _, saleProductOrder := range saleProductOrders {
		o := &structure.GetSaleProductOrderDataV2{}
		o.Id = saleProductOrder.Id
		o.CreateTime = tools.MyTime(saleProductOrder.CreateTime)
		o.UpdateTime = tools.MyTime(saleProductOrder.UpdateTime)
		o.CreatorId = saleProductOrder.CreatorId
		o.CreatorName = saleProductOrder.CreatorName
		o.UpdaterId = saleProductOrder.UpdaterId
		o.UpdateUserName = saleProductOrder.UpdaterName
		o.SaleSystemId = saleProductOrder.SaleSystemId
		o.SaleSystemName = saleSystem[saleProductOrder.SaleSystemId]
		o.VoucherNumber = saleProductOrder.VoucherNumber
		o.OrderTime = tools.MyTime(saleProductOrder.OrderTime)
		o.SendProductType = saleProductOrder.SendProductType
		o.SendProductTypeName = saleProductOrder.SendProductType.String()
		o.WarehouseId = saleProductOrder.WarehouseId
		o.WarehouseName = warehouseName[saleProductOrder.WarehouseId]
		o.CustomerId = saleProductOrder.CustomerId
		o.CustomerName = bizUnit[saleProductOrder.CustomerId][1]
		o.SaleUserId = saleProductOrder.SaleUserId
		o.SaleUserName = employeeName[saleProductOrder.SaleUserId]
		o.SaleFollowerId = saleProductOrder.SaleFollowerId
		o.SaleFollowerName = employeeName[saleProductOrder.SaleFollowerId]
		o.ProcessFactoryId = saleProductOrder.ProcessFactoryId
		o.ProcessFactoryName = logisticsName[saleProductOrder.ProcessFactoryId]
		o.Contacts = saleProductOrder.Contacts
		o.ContactPhone = saleProductOrder.ContactPhone
		o.PrintTag = saleProductOrder.PrintTag
		o.LogisticsCompanyId = saleProductOrder.LogisticsCompanyId
		o.LogisticsCompanyName = logisticsCompany[saleProductOrder.LogisticsCompanyId]
		o.LogisticsArea = saleProductOrder.LogisticsArea
		o.ReceiptAddress = saleProductOrder.ReceiptAddress
		o.InfoSaleTaxableItemId = saleProductOrder.InfoSaleTaxableItemId
		o.InfoSaleTaxableItemName = infoSaleTaxableItem[saleProductOrder.InfoSaleTaxableItemId]
		o.SaleTaxRate = tools.Hundred(saleProductOrder.SaleTaxRate)
		o.OrderNo = saleProductOrder.OrderNo
		o.AuditStatus = saleProductOrder.AuditStatus
		o.AuditStatusName = saleProductOrder.AuditStatus.String()
		o.InternalRemark = saleProductOrder.InternalRemark
		o.SendProductRemark = saleProductOrder.SendProductRemark
		o.IsWithTaxRate = saleProductOrder.IsWithTaxRate
		o.SaleMode = saleProductOrder.SaleMode                 // 订单类型
		o.SaleModeName = saleProductOrder.SaleMode.String()    // 订单类型
		o.LogisticsCompany = saleProductOrder.LogisticsCompany // 物流公司
		o.ProcessFactory = saleProductOrder.ProcessFactory     // 加工厂信息
		// 获取配布信息
		repo := aggs.NewFpmArrangeOrderRepo(tx)
		o.ArrangeData, _, err = repo.GetList(ctx, &productStructure.GetFpmArrangeOrderListQuery{SrcOrderNo: o.OrderNo})
		// 获取配布成品资料
		for i, item := range o.ArrangeData {
			orderData, err := repo.Get(ctx, &productStructure.GetFpmArrangeOrderQuery{Id: item.Id})
			if err != nil {
				continue
			}
			o.OrderProgressName = item.BusinessStatusName
			o.ArrangeData[i] = orderData
		}

		var (
			totalRoll,
			totalWeight,
			totalProduct,
			totalLength,
			totalColor int
			totalPrice      float64
			standardWeight  int
			productStockSvc = product_pb.NewProductStockClient()
			items           = make(structure.GetSaleProductOrderDetailDataListV2, 0) // 面料列表
			productMap      = make(map[uint64]structure.GetSaleProductOrderColorDetailData, 0)
			total           = make(map[string]int) // 统计面料信息

			orderDetails  = make([]model.SaleProductOrderDetail, 0)
			SumProductMap = make(map[uint64]bool) // 用于统计不重复的面料
			SumColorMap   = make(map[uint64]bool) // 用于统计不重复的颜色
		)
		// 获取成品列表
		for _, saleProductOrderDetail := range saleProductOrderDetailList {
			if saleProductOrderDetail.SaleProductOrderId == saleProductOrder.Id {
				orderDetails = append(orderDetails, saleProductOrderDetail)
				SumProductMap[saleProductOrderDetail.ProductId] = true
				SumColorMap[saleProductOrderDetail.ProductColorId] = true
				// 销售单的成品信息
				item := structure.GetSaleProductOrderDetailDataV2{}      // 成品信息
				detail := structure.GetSaleProductOrderColorDetailData{} // 色号详情
				item.Id = saleProductOrderDetail.Id

				detail.StockProductId = saleProductOrderDetail.StockProductId
				item.SaleProductOrderId = saleProductOrderDetail.SaleProductOrderId
				detail.ProductColorId = saleProductOrderDetail.ProductColorId
				detail.ProductColorCode = productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId).ProductColorCode
				detail.ProductColorName = productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId).ProductColorName
				if colorInfo := productColorItem.PickByProductColorId(saleProductOrderDetail.ProductColorId); len(colorInfo.TextureURL) > 0 {
					detail.TextureURL = colorInfo.TextureURL[0]
				}

				item.ProductId = saleProductOrderDetail.ProductId
				if productItem, ok := productItems[saleProductOrderDetail.ProductId]; ok {
					item.ProductCode = productItem.FinishProductCode
					item.ProductName = productItem.FinishProductName
				}
				item.ProductLevelId = saleProductOrderDetail.ProductLevelId
				detail.DyelotNumber = saleProductOrderDetail.DyelotNumber
				detail.AuxiliaryUnitId = saleProductOrderDetail.AuxiliaryUnitId
				detail.MeasurementUnitId = saleProductOrderDetail.MeasurementUnitId
				detail.MeasurementUnitName = measurementUnitName[saleProductOrderDetail.MeasurementUnitId]
				detail.AuxiliaryUnitName = measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]
				detail.Weight = saleProductOrderDetail.Weight
				detail.Roll = saleProductOrderDetail.Roll
				detail.Length = saleProductOrderDetail.Length
				detail.StandardLengthCutSalePrice = saleProductOrderDetail.StandardLengthCutSalePrice // 剪板标准销售价格
				detail.LengthCutSalePrice = saleProductOrderDetail.LengthCutSalePrice                 // 剪板销售价格
				detail.StandardSalePrice = saleProductOrderDetail.StandardSalePrice                   // 大货标准销售价格
				detail.SalePrice = saleProductOrderDetail.SalePrice                                   // 大货销售价格
				item.WarehouseId = saleProductOrderDetail.WarehouseId
				item.WarehouseName = warehouseName[saleProductOrderDetail.WarehouseId]
				detail.TotalPrice, detail.Weight, detail.Length, detail.Roll = calculateDetailPrice(&detail, standardWeight)

				totalPrice += detail.TotalPrice
				totalRoll += detail.Roll
				// 单位是否一致
				if saleProductOrderDetail.AuxiliaryUnitId == saleProductOrderDetail.MeasurementUnitId {
					totalWeight += detail.Weight
					total[measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]] += detail.Weight // 累加数量
				} else {
					totalLength += detail.Length
					total[measurementUnitName[saleProductOrderDetail.AuxiliaryUnitId]] += detail.Length // 累加长度的数量
				}
				// 审核前读最新，审核后读记录
				if saleProductOrder.AuditStatus != common_system.OrderStatusAudited {
					// 读最新
					if detail.DyelotNumber == "" {
						detail.StockRoll = saleProductOrderDetail.StockRoll
						detail.StockWeight = saleProductOrderDetail.AvailableWeight
					} else {
						rwl, _ := productStockSvc.GetStockByDyelotNumberAndColor(ctx, product_pb.GetCADRWLParam{
							ProductColorId: detail.ProductColorId,
							SumStockId:     detail.StockProductId,
							DeylotNubmer:   detail.DyelotNumber,
						})
						detail.StockRoll = rwl[3]
						detail.StockWeight = rwl[4]
					}
				} else {
					detail.StockRoll = saleProductOrderDetail.StockRoll
					detail.StockWeight = saleProductOrderDetail.AvailableWeight
				}
				// 判断成品是否存在，存在则将色号数据追加至成品当中
				_, ok := productMap[saleProductOrderDetail.ProductId]
				// 添加成品信息只map记录
				productMap[saleProductOrderDetail.ProductId] = detail
				if ok {
					items[len(items)-1].ColorDetail = append(items[len(items)-1].ColorDetail, detail)
				} else if len(items) <= 1 { // 列表只返回一个面料信息
					// 不存在则新增成品信息
					item.ColorDetail = append(item.ColorDetail, detail)
					items = append(items, item)
				}
			}

			totalColor = len(SumColorMap)
			totalProduct = len(SumProductMap)
			o.TotalItem = totalProduct  // 总面料
			o.TotalWeight = totalWeight // 总重量
			o.TotalRoll = totalRoll     // 总匹数
			o.TotalColor = totalColor   // 总颜色
			o.TotalLength = totalLength // 总长度
			o.TotalPrice = totalPrice   // 预估价格
			o.ItemData = items
		}
		// 大货类型
		if saleProductOrder.SaleMode == sale.BulkTypeOrder {
			o.ItemInfo = fmt.Sprintf("%d种面料，%d个颜色，共%d匹", totalProduct, totalColor, o.TotalRoll/vars.Roll)
		} else {
			o.ItemInfo = fmt.Sprintf("%d种面料，%d个颜色，共", totalProduct, totalColor)
			for s, i := range total {
				o.ItemInfo += fmt.Sprintf("%d%s", i/vars.Weight, s)
			}
		}
		list = append(list, o)
	}
	res = list
	return
}

func (u *SaleProductOrderRepo) GetLastSalePrice(ctx context.Context, req *structure.GetLastSalePriceQuery) (res structure.GetLastSalePriceDataList, err error) {
	// 判断营销体系是否勾选默认上次单价
	// saleSystems, err := sale_system_mysql.MustFirstSaleSystemByID(u.tx, req.SaleSystemId)
	// if err != nil {
	//	return
	// }
	if req.ProductColorIds == "" || req.CustomerId == 0 {
		res = structure.GetLastSalePriceDataList{
			{Price: 0,
				DefaultLatestPrice: false},
		}
		return
	}

	productColorIds := req.ProductColorIds.ToUint64()

	// 查询销售单记录
	var (
		saleProductOrderDetail model.SaleProductOrderDetail
		saleProductOrder       model.SaleProductOrder
		cond                   = mysql_base.NewCondition()
		list                   model.SaleProductOrderDetailList
	)

	// 联表sale_product_order
	cond.AddTableLeftJoiner(saleProductOrderDetail, saleProductOrder, "sale_product_order_id", "id")

	// 设置查询条件
	cond.AddTableContainMatch(saleProductOrderDetail, "product_color_id", productColorIds)
	cond.AddTableEqual(saleProductOrder, "customer_id", req.CustomerId)
	cond.AddTableEqual(saleProductOrder, "audit_status", common_system.OrderStatusAudited) // 只选择已审核状态
	cond.AddSort("-create_time")

	// 执行查询
	err = mysql_base.SearchListGroup(u.tx, &saleProductOrderDetail, &list, cond)
	if err != nil {
		return
	}

	// 如果没有记录返回0
	if len(list) == 0 {
		for i := 0; i < len(productColorIds); i++ {
			res = append(res, structure.GetLastSalePriceData{
				Id:                 0,
				Price:              0,
				DefaultLatestPrice: false,
			})
		}
		return
	}

	// 根据productColorIds的id，取list和productcolorids的id对应的最新记录
	latestRecords := make(map[uint64]model.SaleProductOrderDetail)

	// 遍历list,保存每个product_color_id对应的最新记录
	for _, detail := range list {
		if existing, ok := latestRecords[detail.ProductColorId]; !ok || detail.CreateTime.After(existing.CreateTime) {
			latestRecords[detail.ProductColorId] = detail
		}
	}

	// 清空list并重新赋值为最新记录
	list = make(model.SaleProductOrderDetailList, 0)
	for _, detail := range latestRecords {
		list = append(list, detail)
	}

	// 返回所有记录
	for _, saleProductOrderDetail := range list {
		if saleProductOrderDetail.SalePrice > 0 {
			res = append(res, structure.GetLastSalePriceData{
				Id:                 saleProductOrderDetail.ProductColorId,
				Price:              float64(saleProductOrderDetail.SalePrice) / 100,
				Type:               "W",
				DefaultLatestPrice: true,
			})
		} else {
			res = append(res, structure.GetLastSalePriceData{
				Id:                 saleProductOrderDetail.ProductColorId,
				Price:              float64(saleProductOrderDetail.LengthCutSalePrice) / 100,
				Type:               "L",
				DefaultLatestPrice: true,
			})
		}

	}
	return
}
func (r *SaleProductOrderRepo) GetSaleOrderStatusNum(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (data structure.GetSaleOrderStatusNumData, err error) {
	var (
		saleProductOrders          model.SaleProductOrderList
		bizService                 = biz_pb.NewClientBizUnitService()
		OrderStatusPendingAuditNum int // 待审核
		OrderStatusAuditedNum      int // 已审核
		OrderStatusRejectedNum     int // 已驳回
		OrderStatusVoidedNum       int // 已作废
	)
	// 获取用户平台
	req.Platform = metadata.GetLoginInfo(ctx).GetPlatform()
	// 通过用户名模糊匹配客户的ids
	datas, _ := bizService.GetBizUnitIdsByNameLike(ctx, req.OrderQuery)
	if len(datas) != 0 {
		req.CustomerIds = append(req.CustomerIds, datas...)
	}
	saleProductOrders, _, err = r.saleProductOrderDao.SearchList(ctx, r.tx, req)
	if err != nil {
		return
	}
	for _, order := range saleProductOrders {
		switch order.AuditStatus {
		case common_system.OrderStatusPendingAudit:
			OrderStatusPendingAuditNum++
		case common_system.OrderStatusAudited:
			OrderStatusAuditedNum++
		case common_system.OrderStatusRejected:
			OrderStatusRejectedNum++
		case common_system.OrderStatusVoided:
			OrderStatusVoidedNum++
		}
	}
	data.OrderStatusList = []structure.Item{
		{
			Code:  common_system.OrderStatusPendingAudit,
			Count: OrderStatusPendingAuditNum,
		},
		{
			Code:  common_system.OrderStatusAudited,
			Count: OrderStatusAuditedNum,
		},
		{
			Code:  common_system.OrderStatusRejected,
			Count: OrderStatusRejectedNum,
		},
		{
			Code:  common_system.OrderStatusVoided,
			Count: OrderStatusVoidedNum,
		},
	}

	return
}

func (r *SaleProductOrderRepo) GetHistorySaleOrderList(ctx context.Context, req *structure.GetHistorySaleOrderListQuery) (list structure.GetHistorySaleOrderDataList, total int, err error) {
	var (
		cond                   = mysql_base.NewCondition()
		cond2                  = mysql_base.NewCondition()
		saleProductOrderList   model.SaleProductOrderList
		saleProductDetaillist  model.SaleProductOrderDetailList
		spo                    model.SaleProductOrder
		spod                   model.SaleProductOrderDetail
		bizUnitSvc             = biz_unit.NewClientBizUnitService()
		productColorSvc        = product2.NewProductColorClient()
		measurementUnitNameSvc = info_basic_data.NewInfoBaseMeasurementUnitClient()
		saleProductOrderIds    []uint64
		groupFields            []string
	)
	cond.AddTableLeftJoiner(spod, spo, "sale_product_order_id", "id")
	// 添加查询条件
	if !req.OrderDateBegin.IsYMDZero() && !req.OrderDateEnd.IsYMDZero() {
		cond.AddTableBetween(spo, "order_time", req.OrderDateBegin.StringYMD(), req.OrderDateEnd.StringYMD2DayListTimeYMDHMS())
	}
	if req.SaleSystemId > 0 {
		cond.AddTableEqual(spo, "sale_system_id", req.SaleSystemId)
	}
	if req.ProductId > 0 {
		cond.AddTableEqual(spod, "product_id", req.ProductId)
	}
	if req.ProductColorId > 0 {
		cond.AddTableEqual(spod, "product_color_id", req.ProductColorId)
	}
	if req.CustomerId > 0 {
		cond.AddTableEqual(spo, "customer_id", req.CustomerId)
	}
	// 只查询已审核的订单
	cond.AddTableEqual(spo, "audit_status", common_system.OrderStatusAudited)
	cond.AddSort("-sale_product_order_detail.create_time", "-sale_product_order_detail.id")
	// 执行查询
	total, err = mysql_base.SearchListGroupForPaging(r.tx, spod, req, &saleProductDetaillist, cond, groupFields...)
	if err != nil {
		return
	}

	saleProductOrderIds = mysql_base.GetUInt64List(saleProductDetaillist, "sale_product_order_id")
	err = mysql_base.Find(r.tx, spo, saleProductOrderIds, &saleProductOrderList, cond2)
	if err != nil {
		return nil, 0, err
	}
	saleSystemIds := mysql_base.GetUInt64List(saleProductOrderList, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}
	orderMap := make(map[uint64]model.SaleProductOrder)
	for _, saleOrder := range saleProductOrderList {
		orderMap[saleOrder.Id] = saleOrder
	}

	for _, saleDetail := range saleProductDetaillist {
		saleOrder, exists := orderMap[saleDetail.SaleProductOrderId]
		if !exists {
			continue
		}

		var (
			bizUnitIds          []uint64
			bizUnit             map[uint64][2]string
			productColorItem    product2.ProductColorRes
			measurementUnitName string
		)

		// 获取客户名称
		bizUnitIds = []uint64{saleOrder.CustomerId}
		bizUnit, err = bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
		if err != nil {
			return
		}

		// 获取产品颜色信息
		productColorItem, err = productColorSvc.GetProductColorById(ctx, saleDetail.ProductColorId)
		if err != nil {
			return
		}

		// 获取计量单位名称
		measurementUnitName, err = measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameById(ctx, saleDetail.MeasurementUnitId)
		if err != nil {
			return
		}

		item := structure.GetHistorySaleOrderData{
			OrderNo:             saleOrder.OrderNo,
			OrderDate:           tools.MyTime(saleOrder.OrderTime),
			SaleSystemName:      saleSystem[saleOrder.SaleSystemId],
			CustomerName:        bizUnit[saleOrder.CustomerId][1],
			ProductCode:         productColorItem.FinishProductCode,
			ProductName:         productColorItem.FinishProductName,
			ProductColorCode:    productColorItem.ProductColorCode,
			ProductColorName:    productColorItem.ProductColorName,
			MeasurementUnitName: measurementUnitName,
			Roll:                saleDetail.Roll,
			Weight:              saleDetail.Weight,
			StandardPrice:       saleDetail.StandardSalePrice,
			UnitPrice:           saleDetail.SalePrice,
			Length:              saleDetail.Length,
			LengthUnitPrice:     saleDetail.LengthCutSalePrice,
			StandardWeightError: saleDetail.WeightError,
			SettleWeightError:   saleDetail.SettleWeightError,
		}
		list = append(list, item)
	}
	// 组装返回数据
	// for _, saleDetail := range saleProductDetaillist {
	//	for _, saleOrder := range saleProductOrderList {
	//		var (
	//			bizUnitIds          []uint64
	//			bizUnit             map[uint64][2]string
	//			productColorItem    product2.ProductColorRes
	//			measurementUnitName string
	//		)
	//		// 获取客户名称
	//		bizUnitIds = []uint64{saleOrder.CustomerId}
	//		bizUnit, err = bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	//		if err != nil {
	//			return
	//		}
	//		// 获取产品颜色信息
	//		productColorItem, err = productColorSvc.GetProductColorById(ctx, saleDetail.ProductColorId)
	//		if err != nil {
	//			return
	//		}
	//		// 获取计量单位名称
	//		measurementUnitName, err = measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameById(ctx, saleDetail.MeasurementUnitId)
	//		if err != nil {
	//			return
	//		}
	//		if saleDetail.SaleProductOrderId == saleOrder.Id {
	//			item := structure.GetHistorySaleOrderData{
	//				OrderNo:             saleOrder.OrderNo,
	//				OrderDate:           tools.MyTime(saleOrder.OrderTime),
	//				SaleSystemName:      saleSystem[saleOrder.SaleSystemId],
	//				CustomerName:        bizUnit[saleOrder.CustomerId][1],
	//				ProductCode:         productColorItem.FinishProductCode,
	//				ProductName:         productColorItem.FinishProductName,
	//				ProductColorCode:    productColorItem.ProductColorCode,
	//				ProductColorName:    productColorItem.ProductColorName,
	//				MeasurementUnitName: measurementUnitName,
	//				Roll:                saleDetail.Roll,
	//				Weight:              saleDetail.Weight,
	//				StandardPrice:       float64(saleDetail.StandardSalePrice) / 100,
	//				UnitPrice:           float64(saleDetail.SalePrice) / 100,
	//				Length:              saleDetail.Length,
	//				LengthUnitPrice:     float64(saleDetail.LengthCutSalePrice) / 100,
	//				StandardWeightError: saleDetail.WeightError,
	//				SettleWeightError:   saleDetail.SettleWeightError,
	//			}
	//			list = append(list, item)
	//		}
	//	}
	// }
	return
}

func (r *SaleProductOrderRepo) AutoOcr(ctx context.Context, req *structure.GetProductByAutoOrcParam) (data productStructure.GetStockProductDropdownDataList, err error) {
	var (
		productSvc      = product2.NewProductClient()
		colorSvc        = product2.NewProductColorClient()
		unitSvc         = info_basic_data.NewInfoBaseMeasurementUnitClient()
		list            = make(productStructure.GetStockProductDropdownDataList, 0)
		productRes      = product2.ProductRes{}
		productColorRes = product2.ProductColorRes{}
	)
	// 1. 获取所有有效的计量单位
	unitResp, err := unitSvc.GetInfoBaseMeasurementUnitList(ctx, &info_basic_data.GetInfoBaseMeasurementUnitListQuery{Status: 1})
	if err != nil {
		return data, middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, ":获取计量单位失败"))
	}

	// 构建计量单位正则表达式
	var unitNames []string
	for _, item := range unitResp {
		unitNames = append(unitNames, item.Name)
	}
	unitPattern := strings.Join(unitNames, "|")

	// 2. 按换行符和分号分割文本
	semicolonSplit := strings.Split(req.Query, "；")
	var lines []string

	// 再对每个部分按换行符分割
	for _, part := range semicolonSplit {
		newLineSplit := strings.Split(part, "\n")
		for _, line := range newLineSplit {
			if trimmed := strings.TrimSpace(line); trimmed != "" {
				lines = append(lines, trimmed)
			}
		}
	}
	colorMap := make(map[string]bool)

	for _, line := range lines {
		spaces := normalizeSpaces(line)
		// 判断格式是否正确
		split := strings.Split(spaces, " ")
		if len(split) < 3 {
			return data, middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve,
				fmt.Sprintf(":无效的格式: %s", line)))
		}
		// 3. 使用正则匹配格式
		pattern := fmt.Sprintf(`([\p{Han}A-Za-z0-9-#]+)\s+([\p{Han}A-Za-z0-9#-]+)\s+(\d+)(?:(%s))?`, unitPattern)
		reg := regexp.MustCompile(pattern)
		matches := reg.FindStringSubmatch(line)

		if len(matches) < 4 {
			return data, middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeWeightErr,
				fmt.Sprintf(":%s", split[2])))
		}
		productCode := matches[1]
		// 如果是纯数字，则补零
		if isNumeric := regexp.MustCompile(`^\d+$`).MatchString(productCode); isNumeric {
			productCode = fmt.Sprintf("%04s", productCode)
		}
		colorCode := matches[2]
		colorName := ""
		// 判断色号是否为纯数字，如果是则补零
		if isNumeric := regexp.MustCompile(`^\d+$`).MatchString(colorCode); isNumeric {
			colorCode = fmt.Sprintf("%03s", colorCode) // 补齐3位
		}
		quantity, _ := strconv.Atoi(matches[3]) // 数量
		unitName := matches[4]                  // 单位
		// 获取辅助单位id

		// 获取可用数量最多的仓库并返回
		var (
			maxAvailable     int                                          // 最大可用数量
			maxAvailableData productStructure.GetStockProductDropdownData // 最大可用数量的成品信息
			unitID           uint64                                       // 单位id
		)
		for _, unitData := range unitResp {
			if unitData.Name == unitName {
				unitID = unitData.Id
				break
			}
		}
		// 4. 检查色号数量限制
		colorMap[colorCode] = true
		if len(colorMap) > 3 {
			return data, errors.NewCustomError(errors.ErrCodeFinishProductColorNotExist, fmt.Sprintf(":%s", colorCode))
		}
		//  5. 查询成品信息
		productRes, err = productSvc.GetProductByCode(ctx, product2.ProductReq{ProductCode: productCode})
		if err != nil || productRes.Id == 0 {
			return data, errors.NewCustomError(errors.ErrCodeNotFoundProductErr, fmt.Sprintf(":%s", productCode))
		}
		// 判断是否是汉字或英文
		colorQuery := colorCode
		chineseOrEnglishPattern := `^[\p{Han}a-zA-Z]+$`
		chineseOrEnglishReg := regexp.MustCompile(chineseOrEnglishPattern)
		if chineseOrEnglishReg.MatchString(colorCode) { // 使用颜色查询
			colorName = colorCode
			colorCode = ""
		}
		// 6. 查询色号信息
		productColorRes, err = colorSvc.GetProductColorByCodeAndProductName(ctx, colorCode, colorName, productRes.FinishProductName)
		if err != nil || productColorRes.Id == 0 {
			return data, errors.NewCustomError(errors.ErrCodeNotFoundProductColorErr, fmt.Sprintf(":%s", colorQuery))
		}
		query := system.ListQuery{
			Page: 1,
			Size: -1,
		}
		// 判断数量是否正确
		quantityPattern := `^\d+$`
		quantityReg := regexp.MustCompile(quantityPattern)
		if !quantityReg.MatchString(split[2]) {
			return data, middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeWeightErr,
				fmt.Sprintf(":%s", split[2])))
		}
		// 获取成品库存信息
		tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
		repo := aggs.NewStockProductRepo(ctx, tx, nil, nil, nil)
		dropdownList, _, err := repo.MPGetDropdownList(ctx, &productStructure.GetStockProductListQuery{
			ListQuery:        query,
			ProductId:        productRes.Id,
			WithPrice:        true,
			SaleSystemId:     req.SaleSystemId,
			ProductCode:      productRes.FinishProductCode,
			ProductName:      productRes.FinishProductName,
			ProductColorCode: productColorRes.ProductColorCode,
			ColorQuery:       productColorRes.ProductColorName,
			AvailableOnly:    true,
		})
		if err != nil {
			return data, middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, ":获取库存信息失败"))
		}
		// 找到可用数量最大的仓库数据
		for _, dropdownData := range dropdownList {
			if dropdownData.AvailableWeight > maxAvailable {
				maxAvailable = dropdownData.AvailableWeight
				maxAvailableData = dropdownData
			}
		}
		// 将可用数量最大的仓库数据添加到列表中
		if maxAvailable > 0 {
			maxAvailableData.ProductCode = productRes.FinishProductCode
			maxAvailableData.ProductName = productRes.FinishProductName
			maxAvailableData.ProductColorCode = productColorRes.ProductColorCode
			maxAvailableData.ProductColorName = productColorRes.ProductColorName
			maxAvailableData.AutoOcrWeight = quantity * vars.Weight
			maxAvailableData.AuxiliaryUnitId = unitID
			maxAvailableData.AuxiliaryUnitName = unitName
			maxAvailableData.Remark = "" // 将指定缸号置空
			list = append(list, maxAvailableData)
		}
	}
	data = list
	return
}

// 处理多个空格
func normalizeSpaces(s string) string {
	// 使用正则表达式将多个空格替换为单个空格
	spaceRegex := regexp.MustCompile(`\s+`)
	return strings.TrimSpace(spaceRegex.ReplaceAllString(s, " "))
}

// GetCustomerLastMoneyInfo 获取客户上次销售信息
func (r *SaleProductOrderRepo) GetCustomerLastMoneyInfo(ctx context.Context, req *structure.GetCustomerLastMoneyInfoReq) (res structure.GetCustomerLastMoneyInfoRes, err error) {
	// var (
	//	detail                 should_collect_order_model.ShouldCollectOrderDetail
	//	list                   sale_price2.GetCustomerSalePriceAdjustDataList
	//	list2                  sale_price2.GetSalePriceColorKindDataList
	//	salePriceColorKindData sale_price2.GetSalePriceColorKindData
	// )
	// // 判断营销体系是否勾选默认上次单价
	// saleSystems, err := sale_system_mysql.MustFirstSaleSystemByID(r.tx, req.SaleSystemId)
	// if err != nil {
	//	return
	// }
	// repo := sale_price_aggs.NewSalePriceColorKindRepo(r.tx)
	// list2, _, err = repo.GetList(ctx, &sale_price2.GetSalePriceColorKindListQuery{
	//	ProductId:          req.ProductId,
	//	ProductColorKindId: req.ProductColorKindId,
	//	Status:             common_system.StatusEnable,
	// })
	// // 销售报价Item
	// if len(list2) > 0 {
	//	salePriceColorKindData = list2[0]
	// }
	// // 营销体系没开启最后一次报价
	// if saleSystems.DefaultLastSalePrice == false {
	//	detail, err = should_collect_order2.NewShouldCollectOrderClient().GetLastOrderByCustomerId(ctx, req.CustomerId, req.ProductColorId, req.SaleSystemId)
	//	if err != nil {
	//		return
	//	}
	//	res.Price = detail.SalePrice
	// } else {
	//	if slices.Contains([]sale.SaleOrderType{sale.SaleOrderType(1), sale.SaleOrderType(3)}, req.SaleMode) {
	//		var (
	//			saleLevelId uint64
	//			// 大货单价优惠
	//			discountForBulkUnitPrice tools.Cent
	//			// 大货空差减重
	//			bulkCargoVolumeWeightReduction tools.Milligram
	//		)
	//		repo := sale_price_aggs.NewCustomerSalePriceAdjustRepo(r.tx)
	//		// 客户等级定价表
	//		list, _, err = repo.GetList(ctx, &sale_price2.GetCustomerSalePriceAdjustListQuery{
	//			CustomerId:         req.CustomerId,
	//			ProductColorId:     req.ProductColorId,
	//			ProductColorKindId: req.ProductColorKindId,
	//			ProductId:          req.ProductId,
	//			SaleSystemId:       req.SaleSystemId,
	//			IsNotOpenPage:      true,
	//		})
	//		if len(list) > 0 {
	//			for _, v := range list {
	//				if v.ProductColorId != 0 {
	//					saleLevelId = v.SaleLevelId
	//					discountForBulkUnitPrice = v.OffsetSalePrice
	//					bulkCargoVolumeWeightReduction = v.OffsetWeightError
	//				}
	//			}
	//			if saleLevelId == 0 {
	//				for _, v := range list {
	//					if v.ProductColorKindId != 0 {
	//						saleLevelId = v.SaleLevelId
	//						discountForBulkUnitPrice = v.OffsetSalePrice
	//						bulkCargoVolumeWeightReduction = v.OffsetWeightError
	//					}
	//				}
	//			}
	//			if saleLevelId == 0 {
	//				for _, v := range list {
	//					if v.ProductId != 0 {
	//						saleLevelId = v.SaleLevelId
	//						discountForBulkUnitPrice = v.OffsetSalePrice
	//						bulkCargoVolumeWeightReduction = v.OffsetWeightError
	//					}
	//				}
	//			}
	//			if saleLevelId == 0 {
	//				for _, v := range list {
	//					if v.CustomerId != 0 {
	//						saleLevelId = v.SaleLevelId
	//						discountForBulkUnitPrice = v.OffsetSalePrice
	//						bulkCargoVolumeWeightReduction = v.OffsetWeightError
	//					}
	//				}
	//			}
	//			for _, v := range salePriceColorKindData.LevelItem {
	//				if v.Id == saleLevelId {
	//					res.Price = v.TargetBulkSalePrice - discountForBulkUnitPrice.ToInt()
	//					res.DiscountGap = v.WeightError + int(bulkCargoVolumeWeightReduction.ToKiloGram())
	//				}
	//			}
	//		} else {
	//			res.Price = salePriceColorKindData.SourceBulkSalePrice.ToInt()
	//			// 没有设置阶梯优惠
	//		}
	//	} else {
	//		if req.UnitId == req.AuxiliaryUnitId {
	//			res.Price = salePriceColorKindData.LengthCutSalePrice.ToInt()
	//		} else {
	//			res.Price = salePriceColorKindData.WeightCutSalePrice.ToInt()
	//		}
	//	}
	// }
	// var (
	//	saleLevel              = make(map[uint64]string)
	//	salePriceColorKindRels salePriceModel.SalePriceColorKindRelList
	//	salePriceColorKinds    salePriceModel.SalePriceColorKindList
	//	saleLevelPrices        salePriceModel.SalePriceLevelList
	// )
	// salePriceColorKindRels, err = salePriceMysql.FindSalePriceColorKindRelByProductAndColorKind(r.tx, req.ProductIds, req.ProductColorKindIds)
	//
	// salePriceColorKindRelIds := mysql_base.GetUInt64List(salePriceColorKindRels, "sale_price_color_kind_rel_id")
	// versions := mysql_base.GetIntList(salePriceColorKindRels, "version")
	// // 获取正在生效以及下一次生效的版本组合salePriceColorKindRelId查询
	// salePriceColorKinds, err = salePriceMysql.FindSalePriceColorKindByParentIdsAndVersions(r.tx, salePriceColorKindRelIds, versions)
	// if err != nil {
	//	return
	// }
	// salePriceColorKindIds := mysql_base.GetUInt64List(salePriceColorKinds, "sale_price_color_kind_id")
	// saleLevelPrices, err = salePriceMysql.FindSalePriceLevelByParentIDs(r.tx, salePriceColorKindIds)
	// if err != nil {
	//	return
	// }
	// salePriceColorKind := salePriceColorKinds.Pick(req.ProductIds, req.ProductColorKindIds)
	// saleLevelSvc := sale_price.NewSaleLevelClient()
	// saleLevel, err = saleLevelSvc.GetAllEnableSaleLevel(ctx)
	// if err != nil {
	//	return
	// }
	// sale_price2.MPCalcPurchaserLadderSalePrice(ctx, r.tx, saleLevel, saleLevelPrices)
	// // 获取最后一笔订单
	// return
	return
}
