// Code generated by "stringer -type=MqMessageType --linecomment"; DO NOT EDIT.

package common

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[MqMessageTypeSalePlanOrder-1]
	_ = x[MqMessageTypeModifyProductStock-2]
	_ = x[MqMessageTypeSalePriceAdjustOrder-3]
	_ = x[MqMessageTypeUserAccess-4]
	_ = x[MqMessageTypeSalePriceAdjustOrderWait-5]
	_ = x[MqMessageTypeAliPay-6]
	_ = x[MqMessageTypeTenantManagementExpire-7]
	_ = x[MqMessageTypeTenantPackageExpire-8]
	_ = x[MqMessageTypeTenantPackageCancel-9]
	_ = x[MqMessageTypeNewTenantNotify-10]
	_ = x[MqMessageTypeCustomerFeedback-11]
	_ = x[MqMessageTyepeOcrCodeManagementExpire-12]
	_ = x[MqMessageTypeOcrCodePackageExpire-13]
	_ = x[MqMessageTypeSearchImageUpload-14]
}

const _MqMessageType_name = "销售计划单反写修改成品库存销售调价单生效用户权限销售调价单消审支付宝支付租户管理过期账套套餐过期账套套餐作废新建账套通知客户反馈OCR码单识别过期码单识别套餐过期搜索图片上传"

var _MqMessageType_index = [...]uint8{0, 21, 39, 60, 72, 93, 108, 126, 144, 162, 180, 192, 213, 237, 255}

func (i MqMessageType) String() string {
	i -= 1
	if i < 0 || i >= MqMessageType(len(_MqMessageType_index)-1) {
		return ""
	}
	return _MqMessageType_name[_MqMessageType_index[i]:_MqMessageType_index[i+1]]
}
