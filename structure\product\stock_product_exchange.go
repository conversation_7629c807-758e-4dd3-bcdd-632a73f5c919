package product

import (
	"context"
	common_system "hcscm/common/system_consts"
	"hcscm/tools/metadata"
)

// 库存详情转换为库存汇总空参
func (p *AddStockProductDetailParam) ToAddEmptyStockProductParam(stockProductId uint64) *AddStockProductParam {
	var (
		r = &AddStockProductParam{}
	)
	if stockProductId != 0 {
		r.Id = stockProductId
	}
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductKindId = p.ProductKindId
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.MeasurementUnitId = p.MeasurementUnitId
	r.Remark = p.Remark
	r.StockProductKey = p.StockProductKey
	return r
}

// 库存详情转换为库存汇总入参
func (p *AddStockProductDetailParam) ToAddStockProductParam(stockProductId uint64) *AddStockProductParam {
	var (
		r = &AddStockProductParam{}
	)
	if stockProductId != 0 {
		r.Id = stockProductId
	}
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductKindId = p.ProductKindId
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.MeasurementUnitId = p.MeasurementUnitId
	r.Remark = p.Remark
	r.StockRoll = p.Roll
	r.Length = p.Length
	r.Weight = p.Weight
	r.StockProductKey = p.StockProductKey
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	r.IsNoNeedCheckStock = p.IsNoNeedCheckStock
	if p.WarehouseInOrderId != 0 {
		r.OrderId = p.WarehouseInOrderId
		r.OrderNo = p.WarehouseInOrderNo
		r.OrderType = 28
	}
	return r
}

// 库存详情转换为库存汇总空参
func (p *UpdateStockProductDetailParam) ToAddEmptyStockProductParam(stockProductId uint64) *AddStockProductParam {
	var (
		r = &AddStockProductParam{}
	)
	if stockProductId != 0 {
		r.Id = stockProductId
	}
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductKindId = p.ProductKindId
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.MeasurementUnitId = p.MeasurementUnitId
	r.Remark = p.Remark
	r.StockProductKey = p.StockProductKey
	return r
}

// 更新库存详情转换为库存汇总入参(出仓，退货入仓)
func (p *UpdateStockProductDetailParam) ToAddStockProductParam() *AddStockProductParam {
	var (
		r = &AddStockProductParam{}
	)
	r.Id = p.StockProductId
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductKindId = p.ProductKindId
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.MeasurementUnitId = p.MeasurementUnitId
	r.Remark = p.Remark
	r.StockRoll = p.Roll
	r.Length = p.Length
	r.Weight = p.Weight
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	r.StockProductKey = p.StockProductKey
	r.IsNoNeedCheckStock = p.IsNoNeedCheckStock

	r.OrderType = p.OrderType
	r.BookLength = p.BookLength
	r.OrderId = p.OrderId
	r.OrderNo = p.OrderNo
	r.BookOrderId = p.BookOrderId
	return r
}

// 汇总增加库存匹数占用(入参为正数)
func (p *ModifyProductStock) ToAddBookStockProductParam() *AddStockProductParam {
	var (
		r = &AddStockProductParam{}
	)
	r.Id = p.Id
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.ProductKindId = p.ProductKindId
	r.ProductLevelId = p.ProductLevelId
	r.Remark = p.Remark
	r.StockRoll = p.Roll
	r.Length = p.Length
	r.Weight = p.Weight
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	r.StockProductKey = p.StockProductKey
	return r
}

// 更新库存详情转换为库存汇总入参(出仓，退货入仓)
func (p *UpdateStockProductDetailParam) ToAddStockProductBookLog(ctx context.Context) *AddStockProductBookLogParam {
	var (
		r = &AddStockProductBookLogParam{}
	)
	r.StockType = common_system.StockTypeProduct
	r.OrderId = p.OrderId
	r.OrderNo = p.OrderNo
	r.OrderType = p.OrderType
	r.OrderTypeName = p.OrderType.String()
	r.ProductId = p.ProductId
	r.ColorId = p.ProductColorId
	r.BookUserId = metadata.GetUserId(ctx)
	r.BookUserName = metadata.GetUserName(ctx)
	r.StockId = p.StockProductId
	r.BookRoll = p.BookRoll
	r.BookWeight = p.BookWeight
	// r.BookLength = p.BookLength
	return r
}
