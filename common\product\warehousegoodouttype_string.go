// Code generated by "stringer -type=WarehouseGoodOutType --linecomment"; DO NOT EDIT.

package product

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[WarehouseGoodOutTypeInternalAllocate-1]
	_ = x[WarehouseGoodOutTypeSaleAllocate-2]
	_ = x[WarehouseGoodOutTypeSale-3]
	_ = x[WarehouseGoodOutTypePurchaseReturn-4]
	_ = x[WarehouseGoodOutTypeProcess-5]
	_ = x[WarehouseGoodOutTypeOther-6]
	_ = x[WarehouseGoodOutTypeDeduction-7]
	_ = x[WarehouseGoodOutTypeTypeRepair-8]
	_ = x[WarehouseGoodOutTypeCheck-9]
	_ = x[WarehouseGoodOutTypeAdjust-10]
}

const _WarehouseGoodOutType_name = "内部调拨出仓销售调拨出仓销售出仓采购退货出仓正常加工出仓其他出仓扣款出仓回修加工出仓盘点出仓调整出仓"

var _WarehouseGoodOutType_index = [...]uint8{0, 18, 36, 48, 66, 84, 96, 108, 126, 138, 150}

func (i WarehouseGoodOutType) String() string {
	i -= 1
	if i < 0 || i >= WarehouseGoodOutType(len(_WarehouseGoodOutType_index)-1) {
		return ""
	}
	return _WarehouseGoodOutType_name[_WarehouseGoodOutType_index[i]:_WarehouseGoodOutType_index[i+1]]
}
