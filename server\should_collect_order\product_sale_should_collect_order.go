package should_collect_order

import (
	"hcscm/common/errors"
	common "hcscm/common/should_collect_order"
	system_consts "hcscm/common/system_consts"
	product_pb "hcscm/extern/pb/product"
	salePb "hcscm/extern/pb/sale"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/should_collect_order"
	mysql "hcscm/model/mysql/should_collect_order/dao"
	"hcscm/server/system"
	product_svc "hcscm/service/product"
	svc "hcscm/service/should_collect_order"
	product_structure "hcscm/structure/product"
	structure "hcscm/structure/should_collect_order"
	"hcscm/vars"

	"github.com/gin-gonic/gin"
)

// @Tags 【销售送货单】
// @Summary 删除销售送货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.DeleteProductSaleShouldCollectOrderParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.DeleteProductSaleShouldCollectOrderData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/delete [delete]
func DeleteProductSaleShouldCollectOrder(c *gin.Context) {
	var (
		q    = &structure.DeleteProductSaleShouldCollectOrderParam{}
		data = structure.DeleteProductSaleShouldCollectOrderData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Delete(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 更新销售送货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductSaleShouldCollectOrderParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/update [put]
func UpdateProductSaleShouldCollectOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateProductSaleShouldCollectOrderParam{}
		data = structure.UpdateProductSaleShouldCollectOrderData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 合并销售送货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateMergeProductSaleShouldCollectOrderParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/mergeItems [put]
func MergeItems(c *gin.Context) {
	var (
		q    = &structure.UpdateMergeProductSaleShouldCollectOrderParam{}
		data = structure.UpdateProductSaleShouldCollectOrderData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.MergeItems(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 拆分合并销售送货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateMergeProductSaleShouldCollectOrderParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/breakUpItems [put]
func BreakUpItems(c *gin.Context) {
	var (
		q    = &structure.UpdateMergeProductSaleShouldCollectOrderParam{}
		data = structure.UpdateProductSaleShouldCollectOrderData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 获取主单信息，判断配布单是否是齐单提货
	orderInfo, err := svc.Get(ctx, &structure.GetProductSaleShouldCollectOrderQuery{Id: q.Id})
	if err != nil {
		return
	}

	// 判断是否是配布单来源
	if orderInfo.SrcOrderType == common.SrcOrderTypeArrange {
		// 获取配布单详情信息
		arrangeOrderSvc := product_svc.NewFpmArrangeOrderService(c)
		arrangeOrderDetail, err := arrangeOrderSvc.Get(ctx, &product_structure.GetFpmArrangeOrderQuery{Id: orderInfo.SrcId})
		if err != nil {
			return
		}

		// 判断是否是齐单提货
		if arrangeOrderDetail.PickUpGoodsInOrder {
			// 如果是齐单提货，获取销售出仓单信息
			saleOutOrderSvc := product_pb.NewSaleOutOrderClient()
			saleOutOrders, err := saleOutOrderSvc.GetSaleOutOrderByArrangeOrderIDs(ctx, product_pb.SaleOutOrderReq{IDs: []uint64{orderInfo.SrcId}})
			if err != nil {
				return
			}

			// 将销售出仓单信息添加到请求参数中
			for _, saleOutOrder := range saleOutOrders {
				if len(saleOutOrder.OrderNo) >= 5 && saleOutOrder.OrderNo[:5] == "FPSE-" {
					q.SaleOutOrderNo = saleOutOrder.OrderNo
					q.SaleOutOrderId = saleOutOrder.Id
					break
				}
			}
		}
	}

	data, err = svc.BreakUpItems(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 更新销售送货单业务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateShouldCollectOrderBusinessCloseParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderBusinessCloseData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/updateBusinessClose [put]
func UpdateProductSaleShouldCollectOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateShouldCollectOrderBusinessCloseParam{}
		data = structure.UpdateProductSaleShouldCollectOrderBusinessCloseData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 更新销售送货单状态-消审
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/updateAuditStatusWait [put]
func UpdateProductSaleShouldCollectOrderAuditStatusWait(c *gin.Context) {
	var (
		q                    = &structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}
		data                 = structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
		actSvc               = svc.NewActuallyCollectOrderService()
		svc                  = svc.NewProductSaleShouldCollectOrderService(c)
		salePlanOrderItemIds []uint64
		err                  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		actlist, err1 := actSvc.FindNoCancelActuallyCollectOrderByShouldCollectOrderId(ctx, id)
		if err1 != nil {
			err = err1
			return
		}
		actOrderNos := actlist.GetOrderNos()
		if len(actOrderNos) > 0 {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, "被实收单："+actOrderNos+" 引用"))
			return
		}
	}

	data, salePlanOrderItemIds, err = svc.UpdateStatusWait(ctx, q)
	if err != nil {
		return
	}
	if len(salePlanOrderItemIds) > 0 {
		err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, system_consts.SituStatusProductArrangeOrder,
			false, q.Id.ToUint64()[0], "销售送货单消审")
		if err != nil {
			return
		}
	}

	return
}

// @Tags 【销售送货单】
// @Summary 更新销售送货单状态-审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/updateAuditStatusPass [put]
func UpdateProductSaleShouldCollectOrderAuditStatusPass(c *gin.Context) {
	var (
		q                    = &structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}
		data                 = structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
		svc                  = svc.NewProductSaleShouldCollectOrderService(c)
		salePlanOrderItemIds []uint64
		err                  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	if len(q.Id.ToUint64()) > 1 {
		err = errors.NewCustomError(errors.ErrCodeMysqlUpdate, "请只传一条数据")
		return
	}

	data, salePlanOrderItemIds, err = svc.UpdateStatusPass(ctx, nil, q)
	if err != nil {
		return
	}

	if len(salePlanOrderItemIds) > 0 {
		err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, system_consts.SituStatusProductSaleDeliveryOrder,
			true, q.Id.ToUint64()[0], "销售送货单审核")
		if err != nil {
			return
		}
	}

	return
}

// @Tags 【销售送货单】
// @Summary 更新销售送货单状态-驳回
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/updateAuditStatusReject [put]
func UpdateProductSaleShouldCollectOrderAuditStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}
		data = structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateStatusReject(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 更新销售送货单状态-作废
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}  true "创建ProductSaleShouldCollectOrder"
// @Success 200 {object}  structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/updateAuditStatusCancel [put]
func UpdateProductSaleShouldCollectOrderAuditStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateProductSaleShouldCollectOrderAuditStatusParam{}
		data = structure.UpdateProductSaleShouldCollectOrderAuditStatusData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	err = svc.UpdateStatusCancel(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetProductSaleShouldCollectOrderData{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/get [get]
func GetProductSaleShouldCollectOrder(c *gin.Context) {
	var (
		q    = &structure.GetProductSaleShouldCollectOrderQuery{}
		data = structure.GetProductSaleShouldCollectOrderData{}
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单(给调货单用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     src_id  query    int  true   "来源单id"
// @Param     order_type  query    int  true   "订单类型(1销售调货单 2退货调货单)"
// @Success 200 {object}  structure.GetProductSaleShouldCollectOrderDataForTransferDetail{}
// @Router /hcscm/mp/v1/should_collect_order/productSale/getBySrcId [get]
// @Tags 【销售送货单】
// @Summary 获取销售送货单(给调货单用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     src_id  query    int  true   "来源单id"
// @Param     order_type  query    int  true   "订单类型(1销售调货单 2退货调货单)"
// @Success 200 {object}  structure.GetProductSaleShouldCollectOrderDataForTransferDetail{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/getBySrcId [get]
func GetProductSaleShouldCollectOrderBySrc(c *gin.Context) {
	var (
		q     = &structure.GetProductSaleShouldCollectOrderQuery{}
		data  = structure.GetProductSaleShouldCollectOrderDataForTransferDetail{}
		svc   = svc.NewProductSaleShouldCollectOrderService(c)
		err   error
		srcId uint64
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	if q.SrcId == 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeLackMustField, "请传入src_id"))
		return
	}

	// 退货调货单
	if q.OrderType == 2 {
		inOrder, err := product_pb.NewFpmInOrderClient().GetInOrderBySrcId(ctx, q.SrcId)
		if err != nil {
			return
		}
		srcId = inOrder.Id
	} else {
		// 销售调货单或者旧单据没有库存流水的单据
		// 先根据调货单id查询出仓单id(适配旧流程没有出仓单的情况)
		outOrder, err := product_pb.NewFpmOutOrderClient().GetOutOrderBySrcId(ctx, q.SrcId)
		if err != nil {
			return
		}
		srcId = outOrder.Id
	}
	// 进仓单据或者出仓单据存在的话更换请求的源单id
	if srcId != 0 {
		q.SrcId = srcId
	}

	// 把销售出仓单id传给参数
	data, err = svc.GetBySrcId(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     order_no     query     string  false  "应收单号"
// @Param     sale_order_no     query     string  false  "销售单号"
// @Param     src_order_no     query     string  false  "配布单号"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     customer_id     query     int  false  "所属客户id(biz_unit_id)"
// @Param     audit_status     query     int  false  "订单状态 1待审核 2已审核 3已驳回 4已作废"
// @Param     auditor_id     query     int  false  "审核人ID"
// @Param     start_audit_date     query     string  false  "审核时间"
// @Param     end_audit_date     query     string  false  "审核时间"
// @Param     start_order_time     query     string  false  "单据日期(送货日期)"
// @Param     end_order_time     query     string  false  "单据日期(送货日期)"
// @Param     start_update_time     query     string  false  "开始更新时间"
// @Param     end_update_time     query     string  false  "结束更新时间"
// @Param     start_create_time     query     string  false  "开始创建时间"
// @Param     end_create_time     query     string  false  "结束创建时间"
// @Param     voucher_number     query     string  false  "凭证单号"
// @Param     updater_id     query     int  false  "修改人id(用户id)"
// @Param     sale_user_id     query     int  false  "销售员id"
// @Param     sale_follower_id     query     int  false  "销售跟单员id"
// @Param     collect_status     query     int  false  "收款状态 1未收款 2已收部分 3已收全款"
// @Success 200 {object}  structure.GetProductSaleShouldCollectOrderListDataList{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/getList [get]
func GetProductSaleShouldCollectOrderList(c *gin.Context) {
	var (
		q     = &structure.GetProductSaleShouldCollectOrderListQuery{}
		list  = make(structure.GetProductSaleShouldCollectOrderListDataList, 0)
		total int
		err   error
		svc   = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		// system.BuildListResponse(c, err, list, total)
		system.BuildListResponseV2(c, q, "销售送货单", err, list, nil, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单细码列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id     query     int  false  "销售送货单id"
// @Success 200 {object}  structure.GetProductSaleArrangeOrderItemFcDataList{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/getWeightItemList [get]
func GetProductSaleWeightItemList(c *gin.Context) {
	var (
		q     = &structure.GetProductSaleShouldCollectOrderQuery{}
		list  = make(structure.GetProductSaleArrangeOrderItemFcDataList, 0)
		total int
		err   error
		svc   = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetWeightItemList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单下拉列表(未审核)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     order_no     query     string  false  "应收单号"
// @Param     src_order_no     query     string  false  "配布单号"
// @Param     sale_order_no     query     string  false  "销售单号"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     start_order_time     query     string  false  "单据日期(送货日期)"
// @Param     end_order_time     query     string  false  "单据日期(送货日期)"
// @Param     should_collect_order_id     query     int  false  "需要排除的应收单id"
// @Success 200 {object}  structure.GetProductSaleShouldCollectOrderDataList{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/getDropdownList [get]
func GetProductSaleShouldCollectOrderDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetProductSaleShouldCollectOrderListQuery{}
		list  = make(structure.GetProductSaleShouldCollectOrderDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单下拉列表(未审核)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id     query     int  false  "销售送货单id"
// @Success 200 {object}  structure.GetProductSaleShouldCollectOrderDataList{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/getMergeOrderList [get]
func GetMergeOrderList(c *gin.Context) {
	var (
		q     = &structure.GetProductSaleShouldCollectOrderQuery{}
		list  = make(structure.GetProductSaleShouldCollectOrderDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetMergeOrderList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【销售送货单】
// @Summary 获取销售送货单成品下拉列表（成品销售退货进仓单）
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     order_no     query     string  false  "销售送货单号"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     customer_id     query     int  false  "所属客户id"
// @Param     product_id     query     int  false  "成品id"
// @Param     product_color_id     query     int  false  "颜色id"
// @Param     dyelot_number     query     string  false  "缸号"
// @Success 200 {object}  structure.GetProductSaleProductItemDropdownDataList{}
// @Router /hcscm/admin/v1/should_collect_order/productSale/getProductDropdownList [get]
func GetProductSaleShouldCollectOrderProductDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetProductSaleProductItemListQuery{}
		list  = make(structure.GetProductSaleProductItemDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetProductDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// /hcscm/admin/v1/temporary/should_collect_order/price [get]
func WashShouldCollectOrderPrice(c *gin.Context) {
	var (
		q                         = &structure.GetShouldCollectOrderListQuery{}
		list                      = make(structure.GetProductSaleProductItemDropdownDataList, 0)
		total                     int
		err                       error
		shouldCollectOrders       model.ShouldCollectOrderList
		shouldCollectOrderDetails model.ShouldCollectOrderDetailList
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()

	shouldCollectOrders, total, err = mysql.SearchShouldCollectOrder(tx, q)
	if err != nil {
		return
	}

	shouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByParentIDs(tx, shouldCollectOrders.GetIds())
	if err != nil {
		return
	}

	// fmt.Println(productSaleShouldCollectOrderDetails)
	for _, shouldCollectOrder := range shouldCollectOrders {
		var newShouldCollectOrderDetails = make(model.ShouldCollectOrderDetailList, 0)
		_shouldCollectOrderDetails := shouldCollectOrderDetails.PickByShouldCollectOrderId(shouldCollectOrder.Id)
		for _, r := range _shouldCollectOrderDetails {
			if r.CollectType == common.CollectTypeProductSale {
				r.SettlePrice = model.SettleMoney(r.Roll, r.SettleWeight, r.Length, r.SalePrice, r.LengthCutSalePrice, r.SettleErrorWeight, r.SaleTaxRate, r.OtherPrice, false)
			}
			if r.CollectType == common.CollectTypeProductReturn {
				r.SettlePrice = model.ReturnSettleMoney(r.Weight, r.Length, r.ReturnPrice, r.LengthCutReturnPrice, r.SaleTaxRate, r.OtherPrice, false)
			}
			if r.CollectType == common.CollectTypeRawMaterial {
				r.SettlePrice = r.SettleWeight*r.SalePrice/vars.WeightUnitPriceMult + r.OtherPrice
			}
			if r.CollectType == common.CollectTypeRawMaterialReturn {
				r.SettlePrice = r.SettleWeight*r.ReturnPrice/vars.WeightUnitPriceMult + r.OtherPrice
			}
			if r.CollectType == common.CollectTypeGreyFabric {
				r.SettlePrice = r.SettleWeight*r.SalePrice/vars.WeightUnitPriceMult + r.OtherPrice
			}
			if r.CollectType == common.CollectTypeGreyFabricReturn {
				r.SettlePrice = r.SettleWeight*r.ReturnPrice/vars.WeightUnitPriceMult + r.OtherPrice
			}
			if r.CollectType == common.CollectTypeOther {
				continue
			}
			r.OriginPrice = r.SettlePrice - r.OtherPrice
			r, err = mysql.MustUpdateShouldCollectOrderDetail(tx, r)
			if err != nil {
				return
			}
			newShouldCollectOrderDetails = append(newShouldCollectOrderDetails, r)
		}
		shouldCollectOrder.Roll, shouldCollectOrder.Weight, shouldCollectOrder.TotalSettleMoney, shouldCollectOrder.OriginPrice = newShouldCollectOrderDetails.GetTotal()
		uncollectMoney := shouldCollectOrder.TotalSettleMoney - shouldCollectOrder.OffsetPrice - shouldCollectOrder.DiscountPrice - shouldCollectOrder.ReducePrice - shouldCollectOrder.CollectedMoney
		shouldCollectOrder.UncollectMoney = uncollectMoney
		shouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(tx, shouldCollectOrder)
		if err != nil {
			return
		}
	}

	return
}

func AIAnalysis(c *gin.Context) {
	var (
		q    structure.AIAnalysisQuery
		data structure.AIAnalysisData
		err  error
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.AIAnalysis(ctx, q)

	return
}

// @Summary 产品和客户矩阵
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     start_time      query     string  false  "start_time"
// @Param     end_time      query     string  false  "end_time"
// @Success 200 {object}  structure.GetMatrixData{}
// @Router /hcscm/h5/v1/should_collect_order/matrix/getMatrixData [get]
func GetMatrixData(c *gin.Context) {
	var (
		q    structure.GetMatrixQuery
		data structure.GetMatrixData
		err  error
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.GetMatrixData(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Summary 客户矩阵详情
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     start_time      query     string  false  "start_time"
// @Param     end_time      query     string  false  "end_time"
// @Success 200 {object}  structure.GetCustomerMatrixDetailData{}
// @Router /hcscm/h5/v1/should_collect_order/matrix/GetCustomerMatrixDetail [get]
func GetCustomerMatrixDetail(c *gin.Context) {
	var (
		q    structure.GetMatrixQuery
		data structure.GetCustomerMatrixDetailData
		err  error
		svc  = svc.NewProductSaleShouldCollectOrderService(c)
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.GetCustomerMatrixDetail(ctx, q)
	if err != nil {
		return
	}

	return
}
