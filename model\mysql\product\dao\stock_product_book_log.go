package dao

import (
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	. "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
)

type (
	IStockProductBookLogDao interface {
		MustCreateStockProductBookLog(r StockProductBookLog) (o StockProductBookLog, err error)
		MustUpdateStockProductBookLog(r StockProductBookLog) (o StockProductBookLog, err error)
		MustDeleteStockProductBookLog(r StockProductBookLog) (err error)
		MustFirstStockProductBookLogByID(id uint64) (r StockProductBookLog, err error)
		FirstStockProductBookLogByID(id uint64) (r StockProductBookLog, exist bool, err error)
		FindStockProductBookLogByStockProductBookLogID(objects ...interface{}) (o StockProductBookLogList, err error)
		FindStockProductBookLogByIDs(ids []uint64) (o StockProductBookLogList, err error)
		SearchStockProductBookLog(q *structure.GetStockProductBookLogListQuery) (o StockProductBookLogList, count int, err error)
		FindStockProductBookLogByParentID(pid uint64) (o StockProductBookLogList, err error)
		MustDeleteStockProductBookLogByIds(ids []uint64) (err error)
		MustDeleteStockProductBookLogByParentId(pid uint64) (err error)
	}

	StockProductBookLogDao struct {
		tx *mysql_base.Tx
	}
)

func NewStockProductBookLogDao(tx *mysql_base.Tx) IStockProductBookLogDao {
	return &StockProductBookLogDao{
		tx: tx,
	}
}

func (d *StockProductBookLogDao) MustCreateStockProductBookLog(r StockProductBookLog) (o StockProductBookLog, err error) {
	err = mysql_base.MustCreateModel(d.tx, &r)
	o = r
	return
}

func (d *StockProductBookLogDao) MustUpdateStockProductBookLog(r StockProductBookLog) (o StockProductBookLog, err error) {
	err = mysql_base.MustUpdateModel(d.tx, &r)
	o = r
	return
}

func (d *StockProductBookLogDao) MustDeleteStockProductBookLog(r StockProductBookLog) (err error) {
	err = mysql_base.MustDeleteModel(d.tx, &r)
	return
}

func (d *StockProductBookLogDao) MustFirstStockProductBookLogByID(id uint64) (r StockProductBookLog, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(d.tx, &r, id, cond)

	return
}

func (d *StockProductBookLogDao) FirstStockProductBookLogByID(id uint64) (r StockProductBookLog, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(d.tx, &r, id, cond)

	return
}

func (d *StockProductBookLogDao) FindStockProductBookLogByStockProductBookLogID(objects ...interface{}) (o StockProductBookLogList, err error) {
	ids := GetStockProductBookLogIdList(objects)
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
		list []StockProductBookLog
	)

	err = mysql_base.Find(d.tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (d *StockProductBookLogDao) FindStockProductBookLogByIDs(ids []uint64) (o StockProductBookLogList, err error) {
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
		list []StockProductBookLog
	)
	r.BuildReadCond(d.tx.Context, cond)

	err = mysql_base.Find(d.tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (d *StockProductBookLogDao) SearchStockProductBookLog(q *structure.GetStockProductBookLogListQuery) (o StockProductBookLogList, count int, err error) {
	var (
		r           StockProductBookLog
		cond        = mysql_base.NewCondition()
		list        []StockProductBookLog
		groupFields []string
	)
	r.BuildReadCond(d.tx.Context, cond)
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(d.tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据上级id获取
func (d *StockProductBookLogDao) FindStockProductBookLogByParentID(pid uint64) (o StockProductBookLogList, err error) {
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
		list []StockProductBookLog
	)
	r.BuildReadCond(d.tx.Context, cond)

	cond.AddTableEqual(r, "parent_id", pid)

	err = mysql_base.SearchListGroup(d.tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据ids删除
func (d *StockProductBookLogDao) MustDeleteStockProductBookLogByIds(ids []uint64) (err error) {
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
	)
	cond.AddContainMatchToOR("id", ids)
	err = mysql_base.MustDeleteModelByCond(d.tx, &r, cond)
	if err != nil {
		return
	}
	return
}

// 根据上级id删除
func (d *StockProductBookLogDao) MustDeleteStockProductBookLogByParentId(pid uint64) (err error) {
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
	)
	cond.AddTableEqual(r, "parent_id", pid)
	err = mysql_base.MustDeleteModelByCond(d.tx, &r, cond)
	if err != nil {
		return
	}
	return
}

// 根据库存ID查询库存占用日志
func FindStockProductBookLogByStockId(tx *mysql_base.Tx, stockId uint64) (o StockProductBookLogList, err error) {
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
		list []StockProductBookLog
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "stock_id", stockId)
	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据订单ID查询库存占用日志
func FindStockProductBookLogByOrderId(tx *mysql_base.Tx, orderId uint64, orderType common_system.BookOrderType) (o StockProductBookLog, err error) {
	var (
		r    StockProductBookLog
		cond = mysql_base.NewCondition()
	)
	//r.BuildReadCond(tx.Context, cond)
	cond.AddTableEqual(r, "order_id", orderId)
	cond.AddTableEqual(r, "order_type", orderType)

	err = mysql_base.FindByCond(tx, &r, &o, cond)
	if err != nil {
		return
	}
	return
}
