package third_party_extra

import (
	"github.com/gin-gonic/gin"
	"hcscm/common/response"
	"hcscm/service/third_party_extra"
	"hcscm/structure/third_party_extra_structure"
	"hcscm/tools/metadata"
	"strconv"
)

// CreateUserBehaviorTracking 创建用户行为跟踪记录
// @Summary 创建用户行为跟踪记录
// @Description 记录用户的行为数据
// @Tags 用户行为跟踪
// @Accept json
// @Produce json
// @Param data body third_party_extra_structure.CreateUserBehaviorTrackingRequest true "行为跟踪请求"
// @Success 200 {object} response.Response
// @Router /api/third_party_extra/user_behavior_tracking/create [post]
func CreateUserBehaviorTracking(c *gin.Context) {
	var req third_party_extra_structure.CreateUserBehaviorTrackingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	// 如果没有传用户ID，从上下文获取
	if req.UserId == 0 {
		userIdStr := metadata.GetUserIdFromIncoming(c)
		if userIdStr == "" {
			response.FailWithMessage(c, "用户ID不能为空")
			return
		}
		userId, err := strconv.ParseUint(userIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage(c, "用户ID格式错误")
			return
		}
		req.UserId = userId
	}

	// 自动获取IP和UserAgent
	if req.IpAddress == "" {
		req.IpAddress = c.ClientIP()
	}
	if req.UserAgent == "" {
		req.UserAgent = c.GetHeader("User-Agent")
	}

	service := third_party_extra.NewUserBehaviorTrackingService()
	err := service.CreateUserBehaviorTracking(req)
	if err != nil {
		response.FailWithMessage(c, "创建用户行为跟踪记录失败: "+err.Error())
		return
	}

	response.OkWithMessage(c, "记录成功")
}

// GetUserBehaviorTrackingList 获取用户行为跟踪列表
// @Summary 获取用户行为跟踪列表
// @Description 获取用户行为跟踪记录列表
// @Tags 用户行为跟踪
// @Accept json
// @Produce json
// @Param user_id query uint64 false "用户ID"
// @Param action_type query string false "行为类型"
// @Param action_target query string false "行为目标"
// @Param session_id query string false "会话ID"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} response.Response{data=third_party_extra_structure.GetUserBehaviorTrackingListResponse}
// @Router /api/third_party_extra/user_behavior_tracking/list [get]
func GetUserBehaviorTrackingList(c *gin.Context) {
	var req third_party_extra_structure.GetUserBehaviorTrackingListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	service := third_party_extra.NewUserBehaviorTrackingService()
	result, err := service.GetUserBehaviorTrackingList(req)
	if err != nil {
		response.FailWithMessage(c, "获取用户行为跟踪列表失败: "+err.Error())
		return
	}

	response.OkWithData(c, result)
}

// GetUserBehaviorStatistics 获取用户行为统计
// @Summary 获取用户行为统计
// @Description 获取用户行为统计数据
// @Tags 用户行为跟踪
// @Accept json
// @Produce json
// @Param user_id query uint64 true "用户ID"
// @Param start_time query string false "开始时间"
// @Param end_time query string false "结束时间"
// @Success 200 {object} response.Response{data=third_party_extra_structure.GetUserBehaviorStatisticsResponse}
// @Router /api/third_party_extra/user_behavior_tracking/statistics [get]
func GetUserBehaviorStatistics(c *gin.Context) {
	var req third_party_extra_structure.GetUserBehaviorStatisticsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	// 如果没有传用户ID，从上下文获取
	if req.UserId == 0 {
		userIdStr := metadata.GetUserIdFromIncoming(c)
		if userIdStr == "" {
			response.FailWithMessage(c, "用户ID不能为空")
			return
		}
		userId, err := strconv.ParseUint(userIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage(c, "用户ID格式错误")
			return
		}
		req.UserId = userId
	}

	service := third_party_extra.NewUserBehaviorTrackingService()
	result, err := service.GetUserBehaviorStatistics(req)
	if err != nil {
		response.FailWithMessage(c, "获取用户行为统计失败: "+err.Error())
		return
	}

	response.OkWithData(c, result)
}

// TrackPageView 跟踪页面访问
// @Summary 跟踪页面访问
// @Description 记录用户页面访问行为
// @Tags 用户行为跟踪
// @Accept json
// @Produce json
// @Param data body map[string]interface{} true "页面访问数据"
// @Success 200 {object} response.Response
// @Router /api/third_party_extra/user_behavior_tracking/track_page_view [post]
func TrackPageView(c *gin.Context) {
	var data map[string]interface{}
	if err := c.ShouldBindJSON(&data); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	userIdStr := metadata.GetUserIdFromIncoming(c)
	if userIdStr == "" {
		response.FailWithMessage(c, "用户ID不能为空")
		return
	}
	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		response.FailWithMessage(c, "用户ID格式错误")
		return
	}

	pageName, ok := data["page_name"].(string)
	if !ok || pageName == "" {
		response.FailWithMessage(c, "页面名称不能为空")
		return
	}

	sessionId, _ := data["session_id"].(string)

	service := third_party_extra.NewUserBehaviorTrackingService()
	err = service.TrackPageView(userId, pageName, c.ClientIP(), c.GetHeader("User-Agent"), sessionId)
	if err != nil {
		response.FailWithMessage(c, "跟踪页面访问失败: "+err.Error())
		return
	}

	response.OkWithMessage(c, "跟踪成功")
}

// TrackButtonClick 跟踪按钮点击
// @Summary 跟踪按钮点击
// @Description 记录用户按钮点击行为
// @Tags 用户行为跟踪
// @Accept json
// @Produce json
// @Param data body map[string]interface{} true "按钮点击数据"
// @Success 200 {object} response.Response
// @Router /api/third_party_extra/user_behavior_tracking/track_button_click [post]
func TrackButtonClick(c *gin.Context) {
	var data map[string]interface{}
	if err := c.ShouldBindJSON(&data); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	userIdStr := metadata.GetUserIdFromIncoming(c)
	if userIdStr == "" {
		response.FailWithMessage(c, "用户ID不能为空")
		return
	}
	userId, err := strconv.ParseUint(userIdStr, 10, 64)
	if err != nil {
		response.FailWithMessage(c, "用户ID格式错误")
		return
	}

	buttonName, ok := data["button_name"].(string)
	if !ok || buttonName == "" {
		response.FailWithMessage(c, "按钮名称不能为空")
		return
	}

	pageName, _ := data["page_name"].(string)
	sessionId, _ := data["session_id"].(string)

	service := third_party_extra.NewUserBehaviorTrackingService()
	err = service.TrackButtonClick(userId, buttonName, pageName, c.ClientIP(), c.GetHeader("User-Agent"), sessionId)
	if err != nil {
		response.FailWithMessage(c, "跟踪按钮点击失败: "+err.Error())
		return
	}

	response.OkWithMessage(c, "跟踪成功")
}
