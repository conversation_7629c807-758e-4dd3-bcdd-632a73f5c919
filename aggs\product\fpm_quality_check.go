package product

import (
	"context"
	"fmt"
	"hcscm/common/errors"
	"hcscm/common/product"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product_basic_pb "hcscm/extern/pb/basic_data/product"
	bizPB "hcscm/extern/pb/biz_unit"
	user_pb "hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strings"
	"time"
)

type (
	FpmQualityCheckRepo struct {
		tx *mysql_base.Tx
	}
	IFpmQualityCheckRepo interface {
		Add(ctx context.Context, req *structure.AddFpmQualityCheckParam) (data structure.AddFpmQualityCheckData, err error)
		Update(ctx context.Context, req *structure.UpdateFpmQualityCheckParam) (data structure.UpdateFpmQualityCheckData, err error)
		UpdateUpdateTime(ctx context.Context, id uint64) (err error)
		Delete(ctx context.Context, req *structure.DeleteFpmQualityCheckParam) (data structure.DeleteFpmQualityCheckData, err error)
		Get(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.GetFpmQualityCheckData, err error)
		GetList(ctx context.Context, req *structure.GetFpmQualityCheckListQuery) (list structure.GetFpmQualityCheckDataList, total int, err error)
		JudgeExitQC(ctx context.Context, stockID uint64, qcDate tools.QueryTime, qcerId uint64) (data model.FpmQualityCheck, exit bool, err error)
		GetForPrint(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.FpmQualityForPrint, err error)
		GetByStockId(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.FpmQualityItemForStockId, err error)
		UpdateCheckoutStatusbyIds(ctx context.Context, ids []uint64) error
	}
)

func NewFpmQualityCheckRepo(tx *mysql_base.Tx) *FpmQualityCheckRepo {
	return &FpmQualityCheckRepo{tx: tx}
}

func (r *FpmQualityCheckRepo) Add(ctx context.Context, req *structure.AddFpmQualityCheckParam) (data structure.AddFpmQualityCheckData, err error) {

	var (
		info                        = metadata.GetLoginInfo(ctx)
		fpmQualityCheckbySameDyelot model.FpmQualityCheckList
		fpmQualityIdsMap            = set.NewUint64Set()
	)

	if req.StockId == 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlCreate, "请传入库存id"))
		return
	}
	stockDetail, err := mysql.MustFirstStockProductDetailByID(r.tx, req.StockId)
	if err != nil {
		return
	}
	fpmQualityCheck := model.NewFpmQualityCheck(ctx, req)

	// 当前登录人和所在部门id
	fpmQualityCheck.QualityCheckerId = info.GetUserId()
	fpmQualityCheck.DepartmentId = info.GetDepartmentId()

	fpmQualityCheck.DyelotNumber = stockDetail.DyelotNumber
	fpmQualityCheck.SumStockId = stockDetail.StockProductId
	fpmQualityCheck.ProductColorId = stockDetail.ProductColorId
	fpmQualityCheck.VolumeNumber = stockDetail.VolumeNumber

	fpmQualityCheck, err = mysql.MustCreateFpmQualityCheck(r.tx, fpmQualityCheck)
	if err != nil {
		return
	}
	fpmQualityCheckbySameDyelot, err = mysql.FindFpmQualityCheckByDyelot(r.tx, []string{fpmQualityCheck.DyelotNumber})
	if err != nil {
		return
	}
	for _, v := range fpmQualityCheckbySameDyelot {
		fpmQualityIdsMap.Add(v.Id)
	}
	// 遍历fpmQualityCheckbySameDyelot，更新is_generate_report为0
	err = mysql.UpdateFpmQualityCheckIsGenerateReportByParentIds(r.tx, fpmQualityIdsMap.List(), product.GenerateReportStatusNotGen)
	if err != nil {
		return
	}
	data.Id = fpmQualityCheck.Id
	return
}

func (r *FpmQualityCheckRepo) Update(ctx context.Context, req *structure.UpdateFpmQualityCheckParam) (data structure.UpdateFpmQualityCheckData, err error) {
	var (
		fpmQualityCheck model.FpmQualityCheck
	)
	fpmQualityCheck, err = mysql.MustFirstFpmQualityCheckByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断是否有更新单据的权限
	err = mysql_base.CanUpdate(ctx, fpmQualityCheck.CreatorId, fpmQualityCheck.CreatorName)
	if err != nil {
		return
	}

	fpmQualityCheck.UpdateFpmQualityCheck(ctx, req)

	fpmQualityCheck, err = mysql.MustUpdateFpmQualityCheck(r.tx, fpmQualityCheck)
	if err != nil {
		return
	}

	data.Id = fpmQualityCheck.Id
	return
}

// 仅更新时间
func (r *FpmQualityCheckRepo) UpdateUpdateTime(ctx context.Context, id uint64) (err error) {
	var (
		fpmQualityCheck model.FpmQualityCheck
	)
	fpmQualityCheck, err = mysql.MustFirstFpmQualityCheckByID(r.tx, id)
	if err != nil {
		return
	}

	fpmQualityCheck, err = mysql.MustUpdateFpmQualityCheck(r.tx, fpmQualityCheck)
	if err != nil {
		return
	}

	return
}

func (r *FpmQualityCheckRepo) Delete(ctx context.Context, req *structure.DeleteFpmQualityCheckParam) (data structure.DeleteFpmQualityCheckData, err error) {
	var (
		list model.FpmQualityCheckList
	)

	list, err = mysql.FindFpmQualityCheckByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, v := range list {
		fpmQualityCheck := model.FpmQualityCheck{}
		fpmQualityCheck.Id = v.Id
		// 删除
		err = mysql.MustDeleteFpmQualityCheck(r.tx, fpmQualityCheck)
		if err != nil {
			return
		}
		data.Id = append(data.Id, fpmQualityCheck.Id)
	}
	return
}

func (r *FpmQualityCheckRepo) Get(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.GetFpmQualityCheckData, err error) {
	var (
		fpmQualityCheck  model.FpmQualityCheck
		fpmQualityChecks model.FpmQualityCheckList
	)

	fpmQualityCheck, err = mysql.MustFirstFpmQualityCheckByID(r.tx, req.Id)
	if err != nil {
		return
	}

	fpmQualityChecks = append(fpmQualityChecks, fpmQualityCheck)
	dst, err := toFpmQualityCheckData(ctx, r.tx, fpmQualityChecks)
	if err != nil {
		return
	}
	data = dst[fpmQualityCheck.Id]
	return
}

func (r *FpmQualityCheckRepo) GetList(ctx context.Context, req *structure.GetFpmQualityCheckListQuery) (list structure.GetFpmQualityCheckDataList, total int, err error) {
	var (
		fpmQualityChecks model.FpmQualityCheckList
	)
	fpmQualityChecks, total, err = mysql.SearchFpmQualityCheck(r.tx, req)
	if err != nil {
		return
	}

	dst, err := toFpmQualityCheckData(ctx, r.tx, fpmQualityChecks)
	if err != nil {
		return
	}
	for _, src := range fpmQualityChecks.List() {
		list = append(list, dst[src.Id])
	}
	return
}

func toFpmQualityCheckData(
	ctx context.Context,
	tx *mysql_base.Tx,
	fpmQualityChecks model.FpmQualityCheckList,
) (resMap map[uint64]structure.GetFpmQualityCheckData, err error) {
	var (
		detailStockList    model.StockProductDetailList
		detailStockIds     = set.NewUint64Set()
		supplierIDsMap     = set.NewUint64Set()
		detailStockMap     = make(map[uint64]model.StockProductDetail)
		userNameMap        = make(map[uint64]string)
		defectMergeStrMap  = make(map[uint64]string)
		defectNameMap      = make(map[uint64]string)
		RecordStockInfoMap = make(map[uint64]structure.RecordStockInfo)
		productColorMap    = make(map[uint64]*product_basic_pb.ProductColorRes) // // 色号，名称
		productItems       = make(map[uint64]*product_basic_pb.ProductRes)
		userPB             = user_pb.NewUserClient()
		productBasicPB     = product_basic_pb.NewProductClient()
		productColorPB     = product_basic_pb.NewProductColorClient()
		defectPB           = info_basic_data.NewInfoBasicDefectClient()
		defectList         = model.FpmQualityCheckDefectList{}
		bizService         = bizPB.NewClientBizUnitService()
		res                = make(map[uint64]structure.GetFpmQualityCheckData, 0)
	)
	for _, check := range fpmQualityChecks {
		detailStockIds.Add(check.StockId)
	}

	g := errgroup.WithCancel(ctx)
	// 详细库存信息
	g.Go(func(ctx context.Context) error {
		var err1 error
		// unitIds := mysql_base.GetUInt64List(fpmQualityChecks, "stock_id")
		detailStockList, err1 = mysql.FindStockProductDetailByIDs(tx, detailStockIds.List())
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindStockProductDetailByIDs err"+err1.Error()))
		}
		for _, v := range detailStockList {
			supplierIDsMap.Add(v.SupplierId)
			detailStockMap[v.Id] = v
		}
		return nil
	})

	// 成品信息
	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(fpmQualityChecks, "product_id")
		// productBasicMap, _, err1 = productBasicPB.GetProductByIds(ctx, unitIds)
		// if err1 != nil {
		// 	middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductByIds err"+err1.Error()))
		// }
		productItems, err1 = productBasicPB.GetProductMapByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductByIds err"+err1.Error()))
		}
		return nil
	})

	// 成品颜色
	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(fpmQualityChecks, "product_color_id")
		productColorMap, err1 = productColorPB.GetProductColorMapByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductColorItemByIds err"+err1.Error()))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		_detailList, err2 := mysql.GetWarehouseInDataByDetailIDs(tx, detailStockIds.List())
		if err2 != nil {
			return err
		}
		for _, fc := range _detailList {
			if val, ok := RecordStockInfoMap[fc.StockId]; ok {
				val.Roll += fc.Roll
				val.Weight += fc.BaseUnitWeight
			} else {
				RecordStockInfoMap[fc.StockId] = structure.RecordStockInfo{
					Id:           fc.StockId,
					Roll:         fc.Roll,
					Weight:       fc.BaseUnitWeight,
					VolumeNumber: fc.VolumeNumber,
				}
			}
		}
		if detailStockIds.Size() > 0 {
			adjustOrderWeightItemList, err2 := mysql.FindProductAdjustOrderWeightItemByDetailStockIds(tx, detailStockIds.List())
			if err2 != nil {
				return err2
			}
			for _, fc := range adjustOrderWeightItemList {
				if detailStockIds.In(fc.StockProductDetailId) {
					if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
						val.Roll += fc.AdjustRoll
						val.Weight += fc.AdjustWeight
					} else {
						RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
							Id:           fc.StockProductDetailId,
							Roll:         fc.AdjustRoll,
							Weight:       fc.AdjustWeight,
							VolumeNumber: fc.VolumeNumber,
						}
					}
				}
				if detailStockIds.In(fc.AdjustDetailStockId) {
					if val, ok := RecordStockInfoMap[fc.AdjustDetailStockId]; ok {
						val.Roll += fc.AdjustRoll
						val.Weight += fc.AdjustWeight
					} else {
						RecordStockInfoMap[fc.AdjustDetailStockId] = structure.RecordStockInfo{
							Id:           fc.StockProductDetailId,
							Roll:         fc.AdjustRoll,
							Weight:       fc.AdjustWeight,
							VolumeNumber: fc.VolumeNumber,
						}
					}
				}
			}
			checkOrderWeightItemList, err2 := mysql.FindProductCheckOrderWeightItemByDetailStockIds(tx, detailStockIds.List())
			if err2 != nil {
				return err2
			}
			for _, fc := range checkOrderWeightItemList {
				if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
					val.Roll += fc.DifferenceRoll
					val.Weight += fc.DifferenceWeight
				} else {
					RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
						Id:           fc.StockProductDetailId,
						Roll:         fc.DifferenceRoll,
						Weight:       fc.DifferenceWeight,
						VolumeNumber: fc.VolumeNumber,
					}
				}
			}
		}
		return nil
	})

	// 用户
	g.Go(func(ctx context.Context) error {
		var err1 error
		userIds := mysql_base.GetUInt64List(fpmQualityChecks, "user_id")
		userNameMap, err1 = userPB.GetUserNameByIds(ctx, userIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductColorItemByIds err"+err1.Error()))
		}
		return nil
	})

	// 查询子信息（用于统计）
	g.Go(func(ctx context.Context) error {
		var (
			err1       error
			tempIdMap  = make(map[uint64]int)    // 记录疵点信息
			tempId2Map = make(map[uint64]uint64) //
		)
		ids := fpmQualityChecks.GetIds()
		defectList, err = mysql.FindFpmQualityCheckDefectByParentIDs(tx, ids)
		if err != nil {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmQualityCheckDefectByParenTID err "+err.Error()))
			return err
		}

		for _, defect := range defectList {
			if _, ok := tempIdMap[defect.Pid+defect.DefectId]; ok {
				tempIdMap[defect.Pid+defect.DefectId] += defect.DefectCount
			} else {
				tempIdMap[defect.Pid+defect.DefectId] = defect.DefectCount
			}
			if _, ok := tempId2Map[defect.Pid+defect.DefectId]; !ok {
				tempId2Map[defect.Pid+defect.DefectId] = defect.Pid
			}
		}

		defectIds := mysql_base.GetUInt64List(defectList, "defect_id")
		defectNameMap, err1 = defectPB.GetInfoBasicDefectNameByIdsContainDel(ctx, defectIds)
		// defectNameMap, err1 = defectPB.GetInfoBasicDefectNameByIds(ctx, defectIds)
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetInfoBasicDefectNameByIds err "+err1.Error()))
		}

		// 拼字段赋值
		for k, v := range tempIdMap {
			pid := tempId2Map[k]
			defectId := k - pid
			vStr := tools.Int2String(v)
			if _, ok2 := defectMergeStrMap[pid]; ok2 {
				if defectId == 0 {
					defectMergeStrMap[pid] += "," + "其他" + "（" + vStr + "个）"
				} else {
					defectMergeStrMap[pid] += "," + defectNameMap[defectId] + "（" + vStr + "个）"
				}
			} else {
				if defectId == 0 {
					defectMergeStrMap[pid] = "其他" + "（" + vStr + "个）"
				} else {
					defectMergeStrMap[pid] = defectNameMap[defectId] + "（" + vStr + "个）"
				}
			}
		}

		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, err
	}

	// 查询来源供货商
	suppliersInfos, _ := bizService.GetBizUnitNameByIds(ctx, supplierIDsMap.List())
	for _, fpmQualityCheck := range fpmQualityChecks {
		var totalScore int
		stockDetail := detailStockMap[fpmQualityCheck.StockId]
		defects := defectList.PickByPid(fpmQualityCheck.Id)
		dst := structure.GetFpmQualityCheckData{}
		dst.Id = fpmQualityCheck.Id
		dst.CreateTime = tools.MyTime(fpmQualityCheck.CreateTime)
		dst.UpdateTime = tools.MyTime(fpmQualityCheck.UpdateTime)
		dst.CreatorId = fpmQualityCheck.CreatorId
		dst.CreatorName = fpmQualityCheck.CreatorName
		dst.UpdaterId = fpmQualityCheck.UpdaterId
		dst.UpdateUserName = fpmQualityCheck.UpdaterName
		dst.StockId = fpmQualityCheck.StockId
		dst.QualityCheckDate = tools.MyTime(fpmQualityCheck.QualityCheckDate)
		dst.WarehouseInTime = tools.MyTime(stockDetail.WarehouseInTime)
		dst.BarCode = fpmQualityCheck.BarCode
		dst.ProductId = fpmQualityCheck.ProductId
		dst.ProductColorId = fpmQualityCheck.ProductColorId
		dst.DefectWeight = fpmQualityCheck.DefectWeight
		dst.Remark = fpmQualityCheck.Remark
		dst.QualityCheckerId = fpmQualityCheck.QualityCheckerId
		dst.DepartmentId = fpmQualityCheck.DepartmentId
		dst.ActuallyWeight = fpmQualityCheck.ActuallyWeight
		dst.EdgeWidth = fpmQualityCheck.EdgeWidth
		dst.UsefulWidth = fpmQualityCheck.UsefulWidth
		dst.QcGramWeight = fpmQualityCheck.QcGramWeight
		dst.SumStockId = fpmQualityCheck.SumStockId
		dst.DyelotNumber = fpmQualityCheck.DyelotNumber

		// 转义
		dst.OrderNo = stockDetail.WarehouseInOrderNo
		dst.OrderType = stockDetail.WarehouseInType
		dst.OrderTypeName = dst.OrderType.String()
		for _, defect := range defects {
			totalScore += defect.DefectCount * defect.Score
		}
		dst.TotalScore = totalScore
		if product, ok := productItems[fpmQualityCheck.ProductId]; ok {
			dst.ProductCode = product.FinishProductCode
			dst.ProductName = product.FinishProductName
			dst.UnitName = product.MeasurementUnitName
			dst.Ingredient = product.FinishProductIngredient
			dst.YarnCount = product.YarnCount
			dst.Density = product.Density
			dst.WeavingOrganizationName = product.WeavingOrganizationName
			// 计算折算总评分(米为默认单位)，先判断计量单位为米还是码，都不是的话判断数量转长度是否有值，有值则转换，没有则按码来计算
			if strings.Contains(product.MeasurementUnitName, "米") {
				dst.ConvertTotalScore = ScoreMeterCM(dst.TotalScore, dst.ActuallyWeight, dst.UsefulWidth)
			} else if strings.Contains(product.MeasurementUnitName, "码") {
				dst.ConvertTotalScore = ScoreYardageInch(dst.TotalScore, dst.ActuallyWeight, dst.UsefulWidth)
			} else {
				if productColor, ok := productColorMap[fpmQualityCheck.ProductColorId]; ok {
					if productColor.LengthToWeightRate != 0 {
						dst.ConvertTotalScore = ScoreWeightCM(dst.TotalScore, dst.ActuallyWeight, productColor.LengthToWeightRate, dst.UsefulWidth)
					} else {
						dst.ConvertTotalScore = ScoreYardageInch(dst.TotalScore, dst.ActuallyWeight, dst.UsefulWidth)
					}
				}

			}
			// 封裝是否合格
			// 判断货物是否合格
			if dst.ConvertTotalScore <= vars.FpmQualityPassScore {
				dst.IsPass = true
			}

		}
		// dst.YarnCount = productBasicMap[fpmQualityCheck.ProductId][2]
		if productColor, ok := productColorMap[fpmQualityCheck.ProductColorId]; ok {
			dst.ProductColorCode = productColor.ProductColorCode
			dst.ProductColorName = productColor.ProductColorName
		}
		dst.DyelotNumber = fpmQualityCheck.DyelotNumber
		dst.Roll = RecordStockInfoMap[fpmQualityCheck.StockId].Roll
		dst.VolumeNumber = fpmQualityCheck.VolumeNumber
		dst.Weight = RecordStockInfoMap[fpmQualityCheck.StockId].Weight
		dst.DefectMergeStr = defectMergeStrMap[fpmQualityCheck.Id]
		dst.QualityCheckerName = userNameMap[dst.QualityCheckerId]
		dst.SupplierName = suppliersInfos[stockDetail.SupplierId]
		dst.IsGenerateReport = fpmQualityCheck.IsGenerateReport
		res[fpmQualityCheck.Id] = dst
	}

	return res, nil
}

func (r *FpmQualityCheckRepo) JudgeExitQC(ctx context.Context, stockID uint64, qcDate tools.QueryTime, qcerId uint64) (data model.FpmQualityCheck, exit bool, err error) {
	return mysql.JudgeFpmQualityCheckDefectIsExit(r.tx, stockID, qcDate, qcerId)
}

func (r *FpmQualityCheckRepo) GetForPrint(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.FpmQualityForPrint, err error) {
	var (
		supplierId                uint64
		maxTime                   time.Time
		productInfo               product_basic_pb.ProductRes
		colorInfo                 product_basic_pb.ProductColorRes
		userNameMap               map[uint64]string
		bizNameMap                map[uint64]string
		fpmQualityCheck           model.FpmQualityCheck
		fpmQualityCheckList       model.FpmQualityCheckList
		fpmQualityCheckDefectList model.FpmQualityCheckDefectList
		stockProductDetailList    model.StockProductDetailList
		stockProductDetailMap     = make(map[uint64]model.StockProductDetail)
		_data                     structure.FpmQualityForPrint
		mark                      = 'A'
		markMap                   = make(map[uint64]string)
		detailStockIds            = set.NewUint64Set()
		productLevelIds           = set.NewUint64Set()
		// unitIds                   = set.NewUint64Set()
		productIds   = set.NewUint64Set()
		productMap   map[uint64]*product_basic_pb.ProductRes
		productLevel map[uint64][2]string
		// measurementUnitName       map[uint64]string
		Ids = make([]uint64, 0)
		// stockProductDetailMap     = make(map[uint64]model.StockProductDetail)
		RecordStockInfoMap = make(map[uint64]structure.RecordStockInfo)
		itemList           = make(structure.FpmQualityItemForPrintList, 0)
		defectInfoList     = make(structure.BasicDefectInfoForPrintList, 0)
	)

	// 查数据
	fpmQualityCheck, err = mysql.MustFirstFpmQualityCheckByID(r.tx, req.Id)
	if err != nil {
		return
	}
	err = tools.Finish(
		func() error {
			fpmQualityCheckList, err = mysql.FindFpmQualityCheckBySameDyelot(
				r.tx, fpmQualityCheck.DyelotNumber, fpmQualityCheck.SumStockId, fpmQualityCheck.ProductColorId)
			if err != nil {
				return err
			}
			for _, check := range fpmQualityCheckList {
				Ids = append(Ids, check.Id)
			}
			// 找这些质检下疵点的数据
			fpmQualityCheckDefectList, err = mysql.FindFpmQualityCheckDefectByParentIDs(r.tx, Ids)
			return err
		}, func() error {
			// 找该缸号下的细码
			stockProductDetailList, err = mysql.FindStockProductByDyelotNumberAndColorList(r.tx, &structure.UnionOutAndInBaseListQuery{
				DyelotNumber:   fpmQualityCheck.DyelotNumber,
				ProductColorId: fpmQualityCheck.ProductColorId,
				StockProductId: fpmQualityCheck.SumStockId,
				IsNoSkipEmpty:  true,
			})
			for _, detail := range stockProductDetailList {
				if detail.SupplierId > 0 {
					supplierId = detail.SupplierId
				}
				detailStockIds.Add(detail.Id)
				productLevelIds.Add(detail.ProductLevelId)
				// unitIds.Add(detail.MeasurementUnitId)
				productIds.Add(detail.ProductId)
				stockProductDetailMap[detail.Id] = detail
			}
			var (
				productLevelSvc = info_basic_data.NewInfoBaseFinishedProductLevelClient()
				// measurementUnitNameSvc = info_basic_data.NewInfoBaseMeasurementUnitClient()
			)
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelMapByIds(ctx, productLevelIds.List())
			// 计量单位
			// measurementUnitName, _ = measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())

			productMap, _ = product_basic_pb.NewProductClient().GetProductMapByIds(ctx, productIds.List())

			_detailList, err2 := mysql.GetWarehouseInDataByDetailIDs(r.tx, detailStockIds.List())
			if err2 != nil {
				return err
			}
			for _, fc := range _detailList {
				if val, ok := RecordStockInfoMap[fc.StockId]; ok {
					val.Roll += fc.Roll
					val.Weight += fc.BaseUnitWeight
				} else {
					recordStockInfo := structure.RecordStockInfo{
						Id:           fc.StockId,
						Roll:         fc.Roll,
						Weight:       fc.BaseUnitWeight,
						VolumeNumber: fc.VolumeNumber,
						// MeasurementUnitId: stockProductDetailMap[fc.StockId].MeasurementUnitId,
					}
					// 库存的计量单位改为获取成品资料的
					if product, productOk := productMap[stockProductDetailMap[fc.StockId].ProductId]; productOk {
						recordStockInfo.MeasurementUnitId = product.MeasurementUnitId
					}
					RecordStockInfoMap[fc.StockId] = recordStockInfo
				}
			}
			if detailStockIds.Size() > 0 {
				adjustOrderWeightItemList, err2 := mysql.FindProductAdjustOrderWeightItemByDetailStockIds(r.tx, detailStockIds.List())
				if err2 != nil {
					return err2
				}
				for _, fc := range adjustOrderWeightItemList {
					if detailStockIds.In(fc.StockProductDetailId) {
						if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
							val.Roll += fc.AdjustRoll
							val.Weight += fc.AdjustWeight
						} else {
							RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
								Id:           fc.StockProductDetailId,
								Roll:         fc.AdjustRoll,
								Weight:       fc.AdjustWeight,
								VolumeNumber: fc.VolumeNumber,
							}
						}
					}
					if detailStockIds.In(fc.AdjustDetailStockId) {
						if val, ok := RecordStockInfoMap[fc.AdjustDetailStockId]; ok {
							val.Roll += fc.AdjustRoll
							val.Weight += fc.AdjustWeight
						} else {
							RecordStockInfoMap[fc.AdjustDetailStockId] = structure.RecordStockInfo{
								Id:           fc.StockProductDetailId,
								Roll:         fc.AdjustRoll,
								Weight:       fc.AdjustWeight,
								VolumeNumber: fc.VolumeNumber,
							}
						}
					}
				}
				checkOrderWeightItemList, err2 := mysql.FindProductCheckOrderWeightItemByDetailStockIds(r.tx, detailStockIds.List())
				if err2 != nil {
					return err2
				}
				for _, fc := range checkOrderWeightItemList {
					if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
						val.Roll += fc.DifferenceRoll
						val.Weight += fc.DifferenceWeight
					} else {
						RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
							Id:           fc.StockProductDetailId,
							Roll:         fc.DifferenceRoll,
							Weight:       fc.DifferenceWeight,
							VolumeNumber: fc.VolumeNumber,
						}
					}
				}
			}

			return err
		}, func() error {
			var (
				prevMark string
			)
			// 疵点信息
			basicDefectInfoList, _err := info_basic_data.NewInfoBasicDefectClient().GetInfoBasicDefectList(ctx)
			if _err != nil {
				return _err
			}
			for _, defectInfo := range basicDefectInfoList {
				if mark > 'Z' {
					prevMark += "A"
					mark = 'A'
				}
				markInfo := &structure.BasicDefectInfoForPrint{
					Id:   defectInfo.Id,
					Mark: fmt.Sprintf("%s%c", prevMark, mark),
					Name: defectInfo.Name,
				}
				defectInfoList = append(defectInfoList, markInfo)
				markMap[defectInfo.Id] = markInfo.Mark
				mark++
			}
			if len(basicDefectInfoList) < 26 {
				for i := mark; i <= 'Z'; i++ {
					defectInfoList = append(defectInfoList, &structure.BasicDefectInfoForPrint{
						Id:   0,
						Mark: fmt.Sprintf("%c", mark),
						Name: "",
					})
					mark++
				}
			}
			return nil
		}, func() error {
			// 成品信息
			productInfo, err = product_basic_pb.NewProductClient().GetProduct(ctx, product_basic_pb.ProductReq{Id: fpmQualityCheck.ProductId})
			return nil
		}, func() error {
			// 成品颜色
			colorInfo, err = product_basic_pb.NewProductColorClient().GetProductColorById(ctx, fpmQualityCheck.ProductColorId)
			return nil
		}, func() error {
			userNameMap, err = user_pb.NewUserClient().GetUserNameByIds(ctx, []uint64{fpmQualityCheck.QualityCheckerId})
			return err
		}, func() error {
			bizNameMap, err = bizPB.NewClientBizUnitService().GetBizUnitNameByIds(ctx, []uint64{supplierId})
			return err
		},
	)
	if err != nil {
		return
	}

	// 组装
	for _, detail := range stockProductDetailList {
		// 基本信息
		info := RecordStockInfoMap[detail.Id]
		_data.TotalRoll += info.Roll
		_data.TotalWeight += info.Weight
		// stockProductDetailMap[detail.Id] = detail
		// _data.TotalRoll += detail.Roll
		// _data.TotalWeight += detail.Weight
		_data.ProductLevelId = detail.ProductLevelId
		_data.ProductLevelName = productLevel[detail.ProductLevelId][1]

		_data.ReceiveDate = tools.MyTime(detail.WarehouseInTime)
		_data.DyelotNumber = detail.DyelotNumber
	}

	for _, check := range fpmQualityCheckList {
		detailStockInfo := RecordStockInfoMap[check.StockId]
		if maxTime.Before(check.QualityCheckDate) {
			maxTime = check.QualityCheckDate
		}
		// 检验信息
		_data.InspectTotalRoll += detailStockInfo.Roll
		_data.InspectTotalWeight += check.ActuallyWeight
		_data.InspectAvgUsefulWidth += check.UsefulWidth
		_data.InspectAvgEdgeWidth += check.EdgeWidth
		_data.InspectAvgGramWeight += check.QcGramWeight

		// 面料信息
		temFpmQualityCheckDefectList := fpmQualityCheckDefectList.PickByPid(check.Id)
		tmpFpmQualityItemForPrint := &structure.FpmQualityItemForPrint{
			Id:             check.Id,
			VolumeNumber:   detailStockInfo.VolumeNumber,
			StockWeight:    detailStockInfo.Weight,
			ActuallyWeight: check.ActuallyWeight,
			EdgeWidth:      check.EdgeWidth,
			UsefulWidth:    check.UsefulWidth,
			QcGramWeight:   check.QcGramWeight,
			IsPass:         true,
			Remark:         check.Remark,
		}
		if product, ok := productMap[check.ProductId]; ok {
			tmpFpmQualityItemForPrint.MeasurementUnitId = product.MeasurementUnitId
			tmpFpmQualityItemForPrint.MeasurementUnitName = product.MeasurementUnitName
		}
		var (
			scoreOneCountMap   = make(map[string]int)
			scoreTwoCountMap   = make(map[string]int)
			scoreThreeCountMap = make(map[string]int)
			scoreFourCountMap  = make(map[string]int)
		)
		for _, defect := range temFpmQualityCheckDefectList {
			switch defect.Score {
			case 1:
				tmpFpmQualityItemForPrint.ScoreOneCount += defect.DefectCount
				if markStr, ok := markMap[defect.DefectId]; ok {
					scoreOneCountMap[markStr] += defect.DefectCount
				}
				if defect.DefectId == 0 {
					scoreOneCountMap[defect.DefectName] += defect.DefectCount
				}
			case 2:
				tmpFpmQualityItemForPrint.ScoreTwoCount += defect.DefectCount
				if markStr, ok := markMap[defect.DefectId]; ok {
					scoreTwoCountMap[markStr] += defect.DefectCount
				}
				if defect.DefectId == 0 {
					scoreTwoCountMap[defect.DefectName] += defect.DefectCount
				}
			case 3:
				tmpFpmQualityItemForPrint.ScoreThreeCount += defect.DefectCount
				if markStr, ok := markMap[defect.DefectId]; ok {
					scoreThreeCountMap[markStr] += defect.DefectCount
				}
				if defect.DefectId == 0 {
					scoreThreeCountMap[defect.DefectName] += defect.DefectCount
				}
			case 4:
				tmpFpmQualityItemForPrint.ScoreFourCount += defect.DefectCount
				if markStr, ok := markMap[defect.DefectId]; ok {
					scoreFourCountMap[markStr] += defect.DefectCount
				}
				if defect.DefectId == 0 {
					scoreFourCountMap[defect.DefectName] += defect.DefectCount
				}
			}
			tmpFpmQualityItemForPrint.TotalScore += defect.DefectCount * defect.Score
		}
		for key := range scoreOneCountMap {
			tmpFpmQualityItemForPrint.ScoreOneCountStr += fmt.Sprintf("%v-%v ", key, scoreOneCountMap[key])
		}
		for key := range scoreTwoCountMap {
			tmpFpmQualityItemForPrint.ScoreTwoCountStr += fmt.Sprintf("%v-%v ", key, scoreTwoCountMap[key])
		}
		for key := range scoreThreeCountMap {
			tmpFpmQualityItemForPrint.ScoreThreeCountStr += fmt.Sprintf("%v-%v ", key, scoreThreeCountMap[key])
		}
		for key := range scoreFourCountMap {
			tmpFpmQualityItemForPrint.ScoreFourCountStr += fmt.Sprintf("%v-%v ", key, scoreFourCountMap[key])
		}
		// 封裝計算折算評分
		// 计算折算总评分(米为默认单位)，先判断计量单位为米还是码，都不是的话判断数量转长度是否有值，有值则转换，没有则按码来计算
		if strings.Contains(productInfo.MeasurementUnitName, "米") {
			tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreMeterCM(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, tmpFpmQualityItemForPrint.UsefulWidth)
		} else if strings.Contains(productInfo.MeasurementUnitName, "码") {
			tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreYardageInch(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, tmpFpmQualityItemForPrint.UsefulWidth)
		} else {
			if colorInfo.LengthToWeightRate != 0 {
				tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreWeightCM(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, colorInfo.LengthToWeightRate, tmpFpmQualityItemForPrint.UsefulWidth)
			} else {
				tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreYardageInch(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, tmpFpmQualityItemForPrint.UsefulWidth)
			}
		}
		// 封裝是否合格
		// 判断货物是否合格
		if tmpFpmQualityItemForPrint.ConvertTotalScore > vars.FpmQualityPassScore {
			tmpFpmQualityItemForPrint.IsPass = false
			_data.InspectNoPassRoll++
		} else {
			_data.InspectPassRoll++
		}
		_data.InspectTotalScore += tmpFpmQualityItemForPrint.TotalScore

		itemList = append(itemList, tmpFpmQualityItemForPrint)
	}

	// 基本信息
	_data.SupplierName = bizNameMap[supplierId]
	_data.QcDate = tools.MyTime(maxTime)
	_data.ProductCode = productInfo.FinishProductCode
	_data.ProductName = productInfo.FinishProductName
	_data.Ingredient = productInfo.FinishProductIngredient
	_data.ProductColorCode = colorInfo.ProductColorCode
	_data.ProductColorName = colorInfo.ProductColorName
	_data.InspectRatio = tools.IntRoundHalf2One(tools.DecimalDiv(float64(_data.InspectTotalRoll*10000), float64(_data.TotalRoll))) // 检验比例（抽/总）
	_data.ProductColorCodeAndName = fmt.Sprintf("%v/%v", colorInfo.ProductColorCode, colorInfo.ProductColorName)
	_data.QualityCheckerName = userNameMap[fpmQualityCheck.QualityCheckerId]
	if len(fpmQualityCheckList) > 0 {
		_data.InspectAvgUsefulWidth = _data.InspectAvgUsefulWidth / len(fpmQualityCheckList)
		_data.InspectAvgEdgeWidth = _data.InspectAvgEdgeWidth / len(fpmQualityCheckList)
		_data.InspectAvgGramWeight = _data.InspectAvgGramWeight / len(fpmQualityCheckList)
	}
	// 计算折算平均分(米为默认单位)，先判断计量单位为米还是码，都不是的话判断数量转长度是否有值，有值则转换，没有则按码来计算
	if strings.Contains(productInfo.MeasurementUnitName, "米") {
		_data.InspectConvertAvgScore = ScoreMeterCM(_data.InspectTotalScore, _data.InspectTotalWeight, _data.InspectAvgUsefulWidth)
	} else if strings.Contains(productInfo.MeasurementUnitName, "码") {
		_data.InspectConvertAvgScore = ScoreYardageInch(_data.InspectTotalScore, _data.InspectTotalWeight, _data.InspectAvgUsefulWidth)
	} else {
		if colorInfo.LengthToWeightRate != 0 {
			_data.InspectConvertAvgScore = ScoreWeightCM(_data.InspectTotalScore, _data.InspectTotalWeight, colorInfo.LengthToWeightRate, _data.InspectAvgUsefulWidth)
		} else {
			_data.InspectConvertAvgScore = ScoreYardageInch(_data.InspectTotalScore, _data.InspectTotalWeight, _data.InspectAvgUsefulWidth)
		}
	}
	data = _data
	data.QcItem = itemList
	data.DefectList = defectInfoList
	return
}

// 折算评分/100平方码 = (总评分 * 3600) / 检查码数 / 有效门幅宽度(cm) * 2.54
func ScoreYardageInch(totalScore, weight, usefulWidth int) (conversionScore int) {
	if totalScore == 0 || weight == 0 || usefulWidth == 0 {
		return
	}
	total := float64(totalScore) * 3600.0 * 10000.0 * 100.0 * 10000.0 / float64(weight) / float64(usefulWidth) * 2.54
	return tools.HandlerProcessing(int(total), 100)
}

// 折算评分/100平方码 = (总评分 * 3600) / 检查米数 * 0.9144 / 有效门幅宽度(cm) * 2.54
func ScoreMeterCM(totalScore, weight, usefulWidth int) (conversionScore int) {
	if totalScore == 0 || weight == 0 || usefulWidth == 0 {
		return
	}
	total := float64(totalScore) * 3600.0 * 10000.0 * 100.0 * 10000.0 / float64(weight) * 0.9144 / float64(usefulWidth) * 2.54
	return tools.HandlerProcessing(int(total), 100)
}

// 折算评分/100平方码 = (总评分 * 3600) / 检查数量 / 数量转长度 * 0.9144 / 有效门幅宽度(cm) * 2.54
func ScoreWeightCM(totalScore, weight, lengthToWeightRate, usefulWidth int) (conversionScore int) {
	if totalScore == 0 || weight == 0 || usefulWidth == 0 {
		return
	}
	total := float64(totalScore) * 3600.0 * 10000.0 * 100.0 * 100.0 * 10000.0 / float64(weight) * 0.9144 / float64(lengthToWeightRate) / float64(usefulWidth) * 2.54
	return tools.HandlerProcessing(int(total), 100)
}

// 数据清洗
func (r *FpmQualityCheckRepo) WashDataDyelotNumber(ctx context.Context) error {
	var (
		isUpdate       = false
		detailIds      = set.NewUint64Set()
		detailStockMap = make(map[uint64]model.StockProductDetail)
	)
	list, count, err := mysql.SearchFpmQualityCheck(r.tx, &structure.GetFpmQualityCheckListQuery{})
	if err != nil {
		return err
	}
	if count == 0 {
		return nil
	}
	for _, check := range list {
		detailIds.Add(check.StockId)
	}
	detailStockList, err := mysql.FindStockProductDetailByIDs(r.tx, detailIds.List())
	if err != nil {
		return err
	}
	if detailStockList == nil {
		return nil
	}
	for _, detail := range detailStockList {
		detailStockMap[detail.Id] = detail
	}
	for _, check := range list {
		detail := detailStockMap[check.StockId]
		if check.DyelotNumber == "" {
			check.DyelotNumber = detail.DyelotNumber
			isUpdate = true
		}
		if check.ProductColorId == 0 {
			check.ProductColorId = detail.ProductColorId
			isUpdate = true
		}
		if check.SumStockId == 0 {
			check.SumStockId = detail.StockProductId
			isUpdate = true
		}
		if isUpdate {
			check, err = mysql.MustUpdateFpmQualityCheck(r.tx, check)
		}
		if err != nil {
			return err
		}
	}
	return nil
}

func (r *FpmQualityCheckRepo) GetByStockId(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.FpmQualityItemForStockId, err error) {
	var (
		exit            bool
		fpmQualityCheck model.FpmQualityCheck
	)
	// 判断是否已经存在同一天,同一个人的数据
	fpmQualityCheck, exit, err = r.JudgeExitQC(ctx, req.StockId, tools.QueryTime(time.Now().Format("2006-01-02")), metadata.GetUserId(ctx))
	if !exit {
		return
	}

	data.Id = fpmQualityCheck.Id
	data.ActuallyWeight = fpmQualityCheck.ActuallyWeight
	data.EdgeWidth = fpmQualityCheck.EdgeWidth
	data.UsefulWidth = fpmQualityCheck.UsefulWidth
	data.DefectWeight = fpmQualityCheck.DefectWeight
	data.QcGramWeight = fpmQualityCheck.QcGramWeight
	data.Remark = fpmQualityCheck.Remark

	return
}

// 根据缸号查询质检数据
func (r *FpmQualityCheckRepo) GetListByDyelotNumber(ctx context.Context, req *structure.GetFpmQualityCheckDyelotNumberListQuery) (list structure.GetFpmQualityCheckDyelotNumberDataList, total int, err error) {
	var (
		fpmQualityCheckDyelotNumbers mysql.FpmQualityCheckDyelotNumberList
		detailStockList              model.StockProductDetailList
		detailStockIds               = set.NewUint64Set()
		unitIDsMap                   = set.NewUint64Set()
		dyelotNumberMap              = set.NewStringSet()
		// customerIdMaps               = set.NewUint64Set()
		detailStockMap     = make(map[uint64]model.StockProductDetail)
		RecordStockInfoMap = make(map[uint64]structure.RecordStockInfo)
		bizService         = bizPB.NewClientBizUnitService()
		// fpmQualityCheckIdsMap  = set.NewUint64Set()
		stockProductDetailList model.StockProductDetailList
		fpmQualityChecks       model.FpmQualityCheckList
		productColorMap        = make(map[uint64]*product_basic_pb.ProductColorRes) // // 色号，名称
		productMap             = make(map[uint64]*product_basic_pb.ProductRes)
		productBasicPB         = product_basic_pb.NewProductClient()
		productColorPB         = product_basic_pb.NewProductColorClient()
		unitInfos              = make(map[uint64]string)

		// customerInfos          = make(map[uint64]string)
		productIDMap      = set.NewUint64Set()
		productColorIDMap = set.NewUint64Set()
	)
	// req.StartWarehouseInTime = "2025-03-01"
	// req.EndWarehouseInTime = "2025-03-01"
	// 仅查看已质检信息
	if req.Type == 1 {
		// 根据同缸号查询质检数据
		fpmQualityCheckDyelotNumbers, total, err = mysql.SearchFpmQualityCheckBySameDyelot(r.tx, req)
		if err != nil {
			return
		}
		for _, fpmQualityCheckDyelotNumber := range fpmQualityCheckDyelotNumbers {
			dyelotNumberMap.Add(fpmQualityCheckDyelotNumber.DyelotNumber)
		}
		// 根据缸号查询质检数据
		fpmQualityChecks, err = mysql.FindFpmQualityCheckByDyelot(r.tx, dyelotNumberMap.List())
		if err != nil {
			return
		}

		// 收集库存ID
		for _, check := range fpmQualityChecks {
			detailStockIds.Add(check.StockId)
			// fpmQualityCheckIdsMap.Add(check.Id)
			productIDMap.Add(check.ProductId)
			productColorIDMap.Add(check.ProductColorId)
		}

		// 查询库存详情和入库数据
		g := errgroup.WithCancel(ctx)
		g.Go(func(ctx context.Context) error {
			detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, detailStockIds.List())
			if err != nil {
				middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindStockProductDetailByIDs err"+err.Error()))
			}
			for _, v := range detailStockList {
				if v.SupplierId != 0 {
					unitIDsMap.Add(v.SupplierId)
				} else {
					unitIDsMap.Add(v.CustomerId)
				}
				detailStockMap[v.Id] = v
			}
			// 查询往来单位
			unitInfos, _ = bizService.GetBizUnitNameByIds(ctx, unitIDsMap.List())
			return nil
		})

		// 成品信息
		g.Go(func(ctx context.Context) error {
			productMap, _ = productBasicPB.GetProductMapByIds(ctx, productIDMap.List())
			return nil
		})

		// 成品颜色
		g.Go(func(ctx context.Context) error {
			productColorMap, _ = productColorPB.GetProductColorMapByIds(ctx, productColorIDMap.List())
			return nil
		})

		// 获取进出仓数据
		g.Go(func(ctx context.Context) error {
			_detailList, err2 := mysql.GetWarehouseInDataByDetailIDs(r.tx, detailStockIds.List())
			if err2 != nil {
				return err
			}
			for _, fc := range _detailList {
				if val, ok := RecordStockInfoMap[fc.StockId]; ok {
					val.Roll += fc.Roll
					val.Weight += fc.BaseUnitWeight
				} else {
					RecordStockInfoMap[fc.StockId] = structure.RecordStockInfo{
						Id:           fc.StockId,
						Roll:         fc.Roll,
						Weight:       fc.BaseUnitWeight,
						VolumeNumber: fc.VolumeNumber,
					}
				}
			}
			if detailStockIds.Size() > 0 {
				adjustOrderWeightItemList, err2 := mysql.FindProductAdjustOrderWeightItemByDetailStockIds(r.tx, detailStockIds.List())
				if err2 != nil {
					return err2
				}
				for _, fc := range adjustOrderWeightItemList {
					if detailStockIds.In(fc.StockProductDetailId) {
						if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
							val.Roll += fc.AdjustRoll
							val.Weight += fc.AdjustWeight
						} else {
							RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
								Id:           fc.StockProductDetailId,
								Roll:         fc.AdjustRoll,
								Weight:       fc.AdjustWeight,
								VolumeNumber: fc.VolumeNumber,
							}
						}
					}
					if detailStockIds.In(fc.AdjustDetailStockId) {
						if val, ok := RecordStockInfoMap[fc.AdjustDetailStockId]; ok {
							val.Roll += fc.AdjustRoll
							val.Weight += fc.AdjustWeight
						} else {
							RecordStockInfoMap[fc.AdjustDetailStockId] = structure.RecordStockInfo{
								Id:           fc.StockProductDetailId,
								Roll:         fc.AdjustRoll,
								Weight:       fc.AdjustWeight,
								VolumeNumber: fc.VolumeNumber,
							}
						}
					}
				}
				checkOrderWeightItemList, err2 := mysql.FindProductCheckOrderWeightItemByDetailStockIds(r.tx, detailStockIds.List())
				if err2 != nil {
					return err2
				}
				for _, fc := range checkOrderWeightItemList {
					if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
						val.Roll += fc.DifferenceRoll
						val.Weight += fc.DifferenceWeight
					} else {
						RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
							Id:           fc.StockProductDetailId,
							Roll:         fc.DifferenceRoll,
							Weight:       fc.DifferenceWeight,
							VolumeNumber: fc.VolumeNumber,
						}
					}
				}
			}
			return nil
		})

		if err = g.Wait(); err != nil {
			return nil, 0, err
		}

		// 组装质检报表数据
		dst, err1 := toFpmQualityCheckData(ctx, r.tx, fpmQualityChecks)
		if err1 != nil {
			err = err1
			return
		}

		var (
			fpmQualityCheckDyelotNumberMap = make(map[string]model.FpmQualityCheckList, 0)
		)
		for _, check := range fpmQualityChecks {
			stockDetail := detailStockMap[check.StockId]
			key := fmt.Sprintf("%v%v%v", check.DyelotNumber, check.SumStockId, stockDetail.SupplierId)
			fpmQualityCheckDyelotNumber, ok := fpmQualityCheckDyelotNumberMap[key]
			if !ok {
				fpmQualityCheckDyelotNumber = make(model.FpmQualityCheckList, 0)
			}
			fpmQualityCheckDyelotNumber = append(fpmQualityCheckDyelotNumber, check)
			fpmQualityCheckDyelotNumberMap[key] = fpmQualityCheckDyelotNumber
		}
		// 转换数据
		for _, fpmQualityCheckDyelotNumber := range fpmQualityCheckDyelotNumbers {
			var (
				latestQualityCheckMap = make(map[int]structure.GetFpmQualityCheckData, 0) // 存放最新一条质检记录
				itemList              = make(structure.GetFpmQualityCheckDataList, 0)
			)

			_fpmQualityChecks := fpmQualityCheckDyelotNumberMap[fmt.Sprintf("%v%v%v", fpmQualityCheckDyelotNumber.DyelotNumber, fpmQualityCheckDyelotNumber.SumStockId, fpmQualityCheckDyelotNumber.SupplierId)]
			var (
				totalRoll, totalWeight               int
				inspectTotalRoll, inspectTotalWeight int
				// totalCount, inspectCount             int
				warehouseInOrderNo = set.NewStringSet()
			)
			stockProductDetailList, err = mysql.FindStockProductByDyelotNumberAndColorList(r.tx, &structure.UnionOutAndInBaseListQuery{
				DyelotNumber:   fpmQualityCheckDyelotNumber.DyelotNumber,
				ProductColorId: fpmQualityCheckDyelotNumber.ProductColorId,
				StockProductId: fpmQualityCheckDyelotNumber.SumStockId,
				SupplierID:     fpmQualityCheckDyelotNumber.SupplierId,
				IsNoSkipEmpty:  true,
			})
			for _, detail := range stockProductDetailList {
				// 基本信息
				// info := RecordStockInfoMap[detail.Id]
				// totalCount += 1
				// totalRoll += detail.Roll
				totalWeight += detail.Weight
			}
			for _, _fpmQualityCheck := range _fpmQualityChecks {
				detailStorkInfo := detailStockMap[_fpmQualityCheck.StockId]
				// inspectCount += 1
				// inspectTotalRoll += detailStorkInfo.Roll
				inspectTotalWeight += detailStorkInfo.Weight
				// 获取当前卷号的最新质检记录
				existingCheck, exists := latestQualityCheckMap[detailStorkInfo.VolumeNumber]

				// 如果不存在记录，或者当前记录的质检日期比map中的记录新，则更新map
				if !exists || _fpmQualityCheck.QualityCheckDate.After(time.Time(existingCheck.QualityCheckDate)) {
					latestQualityCheckMap[detailStorkInfo.VolumeNumber] = dst[_fpmQualityCheck.Id]
				}
				warehouseInOrderNo.Add(detailStorkInfo.WarehouseInOrderNo)
			}
			// 遍历map，将每个卷号最新的质检记录添加到itemList中
			for _, data := range latestQualityCheckMap {
				itemList = append(itemList, data)
			}
			totalRoll = len(stockProductDetailList) * 100
			inspectTotalRoll = len(itemList) * 100                                              // 抽查匹数
			inspectRatio := tools.DecimalDiv(float64(inspectTotalRoll)*100, float64(totalRoll)) // 抽检比例=抽检的记录的条数/库存细码记录的条数
			data := structure.GetFpmQualityCheckDyelotNumberData{
				DyelotNumber:       fpmQualityCheckDyelotNumber.DyelotNumber,
				SupplierId:         fpmQualityCheckDyelotNumber.SupplierId,
				SupplierName:       unitInfos[fpmQualityCheckDyelotNumber.SupplierId],
				TotalRoll:          totalRoll,   // 缸匹数
				TotalWeight:        totalWeight, // 缸数量
				InspectTotalRoll:   inspectTotalRoll,
				InspectTotalWeight: inspectTotalWeight,
				InspectRatio:       int(inspectRatio),
				ItemList:           itemList,
				CustomerId:         fpmQualityCheckDyelotNumber.CustomerId,
				CustomerName:       unitInfos[fpmQualityCheckDyelotNumber.CustomerId],
				DyeFactoryName:     unitInfos[fpmQualityCheckDyelotNumber.SupplierId], // todo:
				// WeaveFactoryName:   suppliersInfos[fpmQualityCheckDyelotNumber.SupplierId], // todo:""
				YarnBatch:          "",
				WarehouseInOrderNo: warehouseInOrderNo.Merge2String(","),
				WarehouseInTime:    tools.MyTime(fpmQualityCheckDyelotNumber.WarehouseInTime),
			}
			if product, ok := productMap[fpmQualityCheckDyelotNumber.ProductId]; ok {
				data.ProductId = product.Id
				data.ProductCode = product.FinishProductCode
				data.ProductName = product.FinishProductName
				data.YarnCount = product.YarnCount
				data.NeedleSize = product.NeedleSize
				data.Density = product.Density
			}
			if productColor, ok := productColorMap[fpmQualityCheckDyelotNumber.ProductColorId]; ok {
				data.ProductColorId = productColor.Id
				data.ProductColorCode = productColor.ProductColorCode
				data.ProductColorName = productColor.ProductColorName
				// 成品幅宽克重
				data.BuildFPResp(productColor.FinishProductWidth, productColor.FinishProductGramWeight, productColor.FinishProductWidthUnitName,
					productColor.FinishProductGramWeightUnitName, productColor.FinishProductWidthUnitId, productColor.FinishProductGramWeightUnitId)
			}
			// 在添加到 list 之前，检查是否所有的质检项都未生成报告
			// hasUngenerated := false
			// for _, item := range itemList {
			// 	if item.IsGenerateReport == 0 { // 0表示未生成报告
			// 		hasUngenerated = true
			// 		break
			// 	}
			// }

			// 只有当存在未生成报告的项时，才添加到列表中
			// if hasUngenerated {
			list = append(list, data)
			// }
		}
	}
	// 仅查看未质检信息
	if req.Type == 2 {
		var (
			stockDyelotNumberDetails mysql.StockDyelotNumberDetailList
			fpmSaleReturnInOrderlist model.FpmInOrderList
		)
		stockDyelotNumberDetails, total, err = mysql.GetStockProductDyelotNumberList(r.tx, &structure.GetStockProductDyelotNumberDetailListQuery{
			ListQuery:            req.ListQuery,
			DyelotNumber:         req.DyelotNumber,
			SupplierId:           req.SupplierId,
			ProductId:            req.ProductId,
			ProductColorId:       req.ProductColorId,
			StartWarehouseInTime: req.StartWarehouseInTime,
			EndWarehouseInTime:   req.EndWarehouseInTime,
			IsUseByCheckReport:   true,
			StockShowType:        req.StockShowType,
		})
		if err != nil {
			return
		}

		g := errgroup.WithCancel(ctx)
		// 成品信息
		g.Go(func(ctx context.Context) error {
			var err1 error
			unitIds := mysql_base.GetUInt64List(stockDyelotNumberDetails, "product_id")
			productMap, err1 = productBasicPB.GetProductMapByIds(ctx, unitIds)
			if err1 != nil {
				middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductByIds err"+err1.Error()))
			}
			return nil
		})

		// 成品颜色
		g.Go(func(ctx context.Context) error {
			var err1 error
			unitIds := mysql_base.GetUInt64List(stockDyelotNumberDetails, "product_color_id")
			productColorMap, err1 = productColorPB.GetProductColorMapByIds(ctx, unitIds)
			if err1 != nil {
				middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductColorItemByIds err"+err1.Error()))
			}
			return nil
		})

		if err = g.Wait(); err != nil {
			return nil, 0, err
		}

		for _, stockDyelotNumberDetail := range stockDyelotNumberDetails {
			if stockDyelotNumberDetail.SupplierId != 0 {
				unitIDsMap.Add(stockDyelotNumberDetail.SupplierId)
			} else {
				// customerIdMaps.Add(stockDyelotNumberDetail.WarehouseInOrderId)
				fpmSaleReturnInOrderlist, err = mysql.FindFpmInOrderByIDs(r.tx, []uint64{stockDyelotNumberDetail.WarehouseInOrderId})
				if len(fpmSaleReturnInOrderlist) != 0 {
					if fpmSaleReturnInOrderlist[0].BizUnitId != 0 {
						unitIDsMap.Add(fpmSaleReturnInOrderlist[0].BizUnitId)
					}
				}
			}

		}

		if err != nil {
			return
		}
		// 查询来源供货商
		unitInfos, _ = bizService.GetBizUnitNameByIds(ctx, unitIDsMap.List())

		for _, stockDyelotNumberDetail := range stockDyelotNumberDetails {
			var (
				totalRoll, totalWeight int
				warehouseInOrderNo     = set.NewStringSet()
				QualityCheckStatus     bool // 控制是否添加数据
			)
			stockProductDetailList, err = mysql.FindStockProductByDyelotNumberAndColorList(r.tx, &structure.UnionOutAndInBaseListQuery{
				DyelotNumber:   stockDyelotNumberDetail.DyelotNumber,
				ProductColorId: stockDyelotNumberDetail.ProductColorId,
				StockProductId: stockDyelotNumberDetail.StockProductId,
				SupplierID:     stockDyelotNumberDetail.SupplierId,
				IsNoSkipEmpty:  true,
			})

			for _, detail := range stockProductDetailList {
				// 基本信息
				// info := RecordStockInfoMap[detail.Id]
				// totalCount += 1
				warehouseInOrderNo.Add(detail.WarehouseInOrderNo)
				// totalRoll += detail.Roll
				totalWeight += detail.Weight
			}
			totalRoll = len(stockProductDetailList) * 100
			data := structure.GetFpmQualityCheckDyelotNumberData{
				DyelotNumber:       stockDyelotNumberDetail.DyelotNumber,
				TotalRoll:          totalRoll,   // 缸匹数
				TotalWeight:        totalWeight, // 缸数量
				InspectTotalRoll:   0,
				InspectTotalWeight: 0,
				InspectRatio:       0,
				ItemList:           make(structure.GetFpmQualityCheckDataList, 0),
				DyeFactoryName:     unitInfos[stockDyelotNumberDetail.SupplierId], // todo:
				// WeaveFactoryName:   suppliersInfos[stockDyelotNumberDetail.SupplierId], // todo:""
				YarnBatch:          "",
				WarehouseInOrderNo: warehouseInOrderNo.Merge2String(","),
				WarehouseInTime:    tools.MyTime(stockDyelotNumberDetail.WarehouseInTime),
			}
			if stockDyelotNumberDetail.SupplierId != 0 {
				data.SupplierName = unitInfos[stockDyelotNumberDetail.SupplierId]
				data.SupplierId = stockDyelotNumberDetail.SupplierId
			} else {
				fpmSaleReturnInOrderlist, err = mysql.FindFpmInOrderByIDs(r.tx, []uint64{stockDyelotNumberDetail.WarehouseInOrderId})
				if len(fpmSaleReturnInOrderlist) != 0 {
					if fpmSaleReturnInOrderlist[0].BizUnitId != 0 {
						data.CustomerName = unitInfos[fpmSaleReturnInOrderlist[0].BizUnitId]
						data.CustomerId = fpmSaleReturnInOrderlist[0].BizUnitId
					}
				}

			}
			if product, ok := productMap[stockDyelotNumberDetail.ProductId]; ok {
				data.ProductId = product.Id
				data.ProductCode = product.FinishProductCode
				data.ProductName = product.FinishProductName
				data.YarnCount = product.YarnCount
				data.NeedleSize = product.NeedleSize
				data.Density = product.Density
			}
			if productColor, ok := productColorMap[stockDyelotNumberDetail.ProductColorId]; ok {
				data.ProductColorId = productColor.Id
				data.ProductColorCode = productColor.ProductColorCode
				data.ProductColorName = productColor.ProductColorName
				// 成品幅宽克重
				data.BuildFPResp(productColor.FinishProductWidth, productColor.FinishProductGramWeight, productColor.FinishProductWidthUnitName,
					productColor.FinishProductGramWeightUnitName, productColor.FinishProductWidthUnitId, productColor.FinishProductGramWeightUnitId)
			}
			// if data.TotalRoll != 0 {
			// 	list = append(list, data)
			// }
			// 如果同缸号存在质检数据则不添加
			for _, detail := range stockProductDetailList {
				if detail.QualityCheckStatus == 2 {
					QualityCheckStatus = true
				}
			}
			if !QualityCheckStatus {
				list = append(list, data)
			}
		}
	}

	return
}

func (r *FpmQualityCheckRepo) UpdateCheckoutStatusbyIds(ctx context.Context, ids []uint64) error {
	// 更新质检单的出库状态
	err := mysql.UpdateFpmQualityCheckStatusByIds(r.tx, ids)
	if err != nil {
		return err
	}
	return nil
}
