package product

import (
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFpmArrangeOrderItemParamList []AddFpmArrangeOrderItemParam

func (r AddFpmArrangeOrderItemParamList) Adjust() {

}

func (r AddFpmArrangeOrderItemParamList) GetIds() (ids []uint64) {
	for _, i2 := range r {
		ids = append(ids, i2.Id)
	}
	return
}

// 出仓数量 码单空差 码单数量 结算空差 结算数量 长度 出仓匹数
func (r AddFpmArrangeOrderItemParam) GetCountFc() [7]int {

	var (
		n1, n2, n3, n4, n5, n6, n7 int
	)
	for _, fc := range r.ItemFCData {
		n1 += fc.BaseUnitWeight
		n2 += fc.WeightError
		n4 += fc.SettleErrorWeight
		n6 += fc.Length
		n7 += fc.Roll
	}
	n3 = n1 - n2
	n5 = n3 - n4

	return [7]int{n1, n2, n3, n4, n5, n6, n7}
}

type AddFpmArrangeOrderItemParam struct {
	structure_base.Param
	Id                     uint64                            `json:"id"`                        // id
	ItemFCData             AddFpmArrangeOrderItemFcParamList `json:"item_fc_data"`              // 细码
	ProductId              uint64                            `json:"product_id"`                // 成品id
	ProductCode            string                            `json:"product_code"`              // 成品编号
	ProductName            string                            `json:"product_name"`              // 成品名称
	CustomerId             uint64                            `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64                            `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string                            `json:"product_color_code"`        // 成品名称
	ProductColorName       string                            `json:"product_color_name"`        // 成品名称
	ProductLevelId         uint64                            `json:"product_level_id"`          // 成品等级
	DyeFactoryColorCode    string                            `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                            `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductWidth           string                            `json:"product_width"`             // 成品幅宽
	ProductGramWeight      string                            `json:"product_gram_weight"`       // 成品克重
	ProductRemark          string                            `json:"product_remark"`            // 成品备注
	ProductCraft           string                            `json:"product_craft"`             // 成品工艺
	ProductIngredient      string                            `json:"product_ingredient"`        // 成品成分
	SumStockId             uint64                            `json:"sum_stock_id"`              // 汇总库存id
	ArrangeRoll            int                               `json:"arrange_roll"`              // 配布件数(件)，乘100存
	ArrangeWeight          int                               `json:"arrange_weight"`            // 出仓总数量(公斤)，乘10000存
	ArrangeLength          int                               `json:"arrange_length"`            // 出仓长度，乘100存
	WeightError            int                               `json:"weight_error"`              // 码单空差数量(公斤)，乘10000存
	ActuallyWeight         int                               `json:"actually_weight"`           // 码单数量(公斤)，乘10000存
	PaperTubeWeight        int                               `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleErrorWeight      int                               `json:"settle_error_weight"`       // 结算空差数量(公斤)，乘10000存
	SettleWeight           int                               `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64                            `json:"unit_id"`                   // 单位id
	AuxiliaryUnitId        uint64                            `json:"auxiliary_unit_id"`         // 辅助单位
	Remark                 string                            `json:"remark"`                    // 备注
	WarehouseId            uint64                            `json:"warehouse_id"`              // 仓库id
	QuoteOrderNo           string                            `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64                            `json:"quote_order_item_id"`       // 引用数据单物料那条id
	SumStockRoll           int                               `json:"sum_stock_roll"`            // 汇总库存成品匹数
	SumStockWeight         int                               `json:"sum_stock_weight"`          // 汇总库存成品数量
	SumStockLength         int                               `json:"sum_stock_length"`          // 汇总库存成品长度
	// 销售价格
	StandardSalePrice          int    `json:"standard_sale_price"`                  // 标准销售报价(大货 散剪)
	SaleLevelId                uint64 `json:"sale_level_id" relate:"sale_level_id"` // 销售等级ID
	OffsetSalePrice            int    `json:"offset_sale_price"`                    // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                  int    `json:"sale_price"`                           // 销售单价(销售报价-优惠单价)(大货 散剪)
	StandardWeightError        int    `json:"standard_weight_error"`                // 标准空差 /0.1g
	OffsetWeightError          int    `json:"offset_weight_error"`                  // 优惠空差 /0.1g
	AdjustWeightError          int    `json:"adjust_weight_error"`                  // 调整空差 /0.1g
	StandardLengthCutSalePrice int    `json:"standard_length_cut_sale_price"`       // 剪板销售价格
	OffsetLengthCutSalePrice   int    `json:"offset_length_cut_sale_price"`         // 剪版优惠单价
	LengthCutSalePrice         int    `json:"length_cut_sale_price"`                // 剪版销售单价(剪板销售价格-剪版优惠单价)
	OtherPrice                 int    `json:"other_price"`                          // 其他金额
	SaleTaxRate                int    `json:"sale_tax_rate"`                        // 销售税率
	PushRoll                   int    `json:"push_roll"`                            // 下推匹数
	PushLength                 int    `json:"push_length"`                          // 下推长度
	PushWeight                 int    `json:"push_weight"`                          // 下推数量
	SalePlanOrderItemId        uint64 `json:"sale_plan_order_item_id"`              // 成品销售计划单子项信息id
}

type AddFpmArrangeOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateFpmArrangeOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type GetFpmArrangeOrderItemQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmArrangeOrderItemListQuery struct {
	structure_base.ListQuery
	ParentId               uint64 `form:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string `form:"parent_order_no"`           // 父单号(对应的单据号)(对应的单据号)
	ProductId              uint64 `form:"product_id"`                // 成品id
	ProductCode            string `form:"product_code"`              // 成品编号
	ProductName            string `form:"product_name"`              // 成品名称
	CustomerId             uint64 `form:"customer_id"`               // 所属客户id
	ProductColorId         uint64 `form:"product_color_id"`          // 成品颜色id
	ProductColorCode       string `form:"product_color_code"`        // 成品名称
	ProductColorName       string `form:"product_color_name"`        // 成品名称
	ProductLevelId         uint64 `form:"product_level_id"`          // 成品等级
	ProductWidth           string `form:"product_width"`             // 成品幅宽
	DyeFactoryColorCode    string `form:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `form:"dye_factory_dyelot_number"` // 染厂缸号
	ProductGramWeight      string `form:"product_gram_weight"`       // 成品克重
	ProductRemark          string `form:"product_remark"`            // 成品备注
	ProductCraft           string `form:"product_craft"`             // 成品工艺
	ProductIngredient      string `form:"product_ingredient"`        // 成品成分
	ArrangeRoll            int    `form:"arrange_roll"`              // 配布件数(件)，乘100存
	SumStockId             uint64 `form:"sum_stock_id"`              // 汇总库存id
	ArrangeWeight          int    `form:"arrange_weight"`            // 出仓总数量(公斤)，乘10000存
	WeightError            int    `form:"weight_error"`              // 码单空差数量(公斤)，乘10000存
	ActuallyWeight         int    `form:"actually_weight"`           // 码单数量(公斤)，乘10000存
	PaperTubeWeight        int    `form:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleErrorWeight      int    `form:"settle_error_weight"`       // 结算空差数量(公斤)，乘10000存
	SettleWeight           int    `form:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64 `form:"unit_id"`                   // 单位id
	ArrangeLength          int    `form:"arrange_length"`            // 出仓长度，乘100存
	Remark                 string `form:"remark"`                    // 备注
}

func (r GetFpmArrangeOrderItemListQuery) Adjust() {

}

type GetFpmArrangeOrderItemData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ItemFCData             GetFpmArrangeOrderItemFcDataList `json:"item_fc_data"`              // 细码
	ParentId               uint64                           `json:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string                           `json:"parent_order_no"`           // 父单号(对应的单据号)
	ProductId              uint64                           `json:"product_id"`                // 成品id
	ProductCode            string                           `json:"product_code"`              // 成品编号
	ProductName            string                           `json:"product_name"`              // 成品名称
	CustomerId             uint64                           `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64                           `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string                           `json:"product_color_code"`        // 成品名称
	ProductColorName       string                           `json:"product_color_name"`        // 成品名称
	ProductLevelId         uint64                           `json:"product_level_id"`          // 成品等级
	DyeFactoryColorCode    string                           `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                           `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductWidth           string                           `json:"product_width"`             // 成品幅宽
	ProductGramWeight      string                           `json:"product_gram_weight"`       // 成品克重
	ProductRemark          string                           `json:"product_remark"`            // 成品备注
	ProductCraft           string                           `json:"product_craft"`             // 成品工艺
	ProductIngredient      string                           `json:"product_ingredient"`        // 成品成分
	ArrangeRoll            int                              `json:"arrange_roll"`              // 配布件数(件)，乘100存
	SumStockId             uint64                           `json:"sum_stock_id"`              // 汇总库存id
	ArrangeWeight          int                              `json:"arrange_weight"`            // 出仓总数量(公斤)，乘10000存
	WeightError            int                              `json:"weight_error"`              // 码单空差数量(公斤)，乘10000存
	ActuallyWeight         int                              `json:"actually_weight"`           // 码单数量(公斤)，乘10000存
	PaperTubeWeight        int                              `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleErrorWeight      int                              `json:"settle_error_weight"`       // 结算空差数量(公斤)，乘10000存
	SettleWeight           int                              `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	UnitId                 uint64                           `json:"unit_id"`                   // 单位id
	AuxiliaryUnitId        uint64                           `json:"auxiliary_unit_id"`         // 辅助单位id
	LengthUnitPrice        int                              `json:"length_unit_price"`         // 长度单位价格
	ArrangeLength          int                              `json:"arrange_length"`            // 出仓长度，乘100存
	Remark                 string                           `json:"remark"`                    // 备注
	QuoteOrderNo           string                           `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64                           `json:"quote_order_item_id"`       // 引用数据单物料那条id

	// 转义
	UnitName          string `json:"unit_name"`           // 单位名称
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 下推结算单位(销售单下下推)
	CustomerName      string `json:"customer_name"`       // 所属客户name
	ProductLevelName  string `json:"product_level_name"`  // 成品等级name
	// 库存信息
	SumStockRoll   int `json:"sum_stock_roll"`   // 汇总库存成品匹数
	SumStockWeight int `json:"sum_stock_weight"` // 汇总库存成品数量
	SumStockLength int `json:"sum_stock_length"` // 汇总库存成品长度

	PushRoll            int    `json:"push_roll"`               // 下推匹数(计划配布的总数)
	PushLength          int    `json:"push_length"`             // 下推长度(计划配布的总数)
	PushWeight          int    `json:"push_weight"`             // 下推数量(计划配布的总数)
	SalePlanOrderItemId uint64 `json:"sale_plan_order_item_id"` // 成品销售计划单子项信息id
}

type GetFpmArrangeOrderItemDataList []GetFpmArrangeOrderItemData

func (g GetFpmArrangeOrderItemDataList) Adjust() {

}

type DeleteFpmArrangeOrderItemParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmArrangeOrderItemData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}
