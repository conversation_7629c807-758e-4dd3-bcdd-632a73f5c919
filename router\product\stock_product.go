package product

import (
	"google.golang.org/grpc"
	"hcscm/extern/pb/product_manage"
	web "hcscm/server/product"
	"hcscm/server/system"
)

func InitStockProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		stockProduct := product.Group("stockProduct")
		stockProduct.GET("查询库存(汇总库存)", "getStockProductList", web.GetStockProductList)
		stockProduct.GET("查询库存(细码库存)", "getStockProductDetailList", web.GetStockProductDetailList)
		stockProduct.GET("获取详情下拉列表", "getStockProductDetailDropdownList", web.GetStockProductDetailDropdownList)
		stockProduct.GET("查询库存(缸号库存)", "getStockProductDyelotNumberList", web.GetStockProductDyelotNumberList)
		stockProduct.POST("ai查询库存", "ai", web.AIGetStock)

		stockProduct.GET("汇总-查看进出仓情况", "getSumStockProductOutAndInDetailList", web.GetSumStockProductOutAndInDetailList)
		stockProduct.GET("缸号-查看进出仓情况", "getDyeNumberAndColorStockProductOutAndInDetailList", web.GetDyeNumberAndColorStockProductOutAndInDetailList)
		stockProduct.GET("详细-查看进出仓情况", "getDetailStockProductOutAndInDetailList", web.GetDetailStockProductOutAndInDetailList)

		stockProduct.POSTNeedAuth("添加单条详情", "addStockProductDetail", web.AddStockProductDetail)
		stockProduct.POSTNeedAuth("添加多条详情", "addStockProductDetails", web.AddStockProductDetails)
		stockProduct.PUTNeedAuth("更新单条详情", "updateStockProductDetail", web.UpdateStockProductDetail)
		stockProduct.GET("根据缸号和颜色id获取最大卷号", "getMaxVolumeNumber", web.GetMaxVolumeNumber)
		stockProduct.GET("获取详情", "getStockProductDetail", web.GetStockProductDetail)
		stockProduct.GET("通过stock_id查询成品详细信息", "getStockProductDetailInfo", web.GetStockProductDetailInfo)

		stockProduct.GET("获取汇总下拉列表", "getStockProductDropdownList", web.GetStockProductDropdownList)
		stockProduct.GET("获取详情缸号分组列表", "getStockProductDyelotNumberDetailList", web.GetStockProductDyelotNumberDetailList)
		stockProduct.GET("获取详情细码选择列表", "getStockProductWeightDetailList", web.GetStockProductWeightDetailList)

		stockProduct.GET("根据二维码获取库存详情", "getDetailByCond", web.GetDetailByCond)

		{
			stockProductBookLog := product.Group("stockProductBookLog")
			stockProductBookLog.POSTNeedAuth("新增", "", web.AddStockProductBookLog)
			stockProductBookLog.PUTNeedAuth("更新", "", web.UpdateStockProductBookLog)
			stockProductBookLog.DELETENeedAuth("删除", "", web.DeleteStockProductBookLog)
			stockProductBookLog.GET("获取", "", web.GetStockProductBookLog)
			stockProductBookLog.GET("获取列表", "list", web.GetStockProductBookLogList)
		}
	}
	{
		stockProductDetailSettlePoint := product.Group("stockProductDetailSettlePoint")
		stockProductDetailSettlePoint.POSTNeedAuth("新增", "", web.AddStockProductDetailSettlePoint)
		stockProductDetailSettlePoint.DELETENeedAuth("删除", "", web.DeleteStockProductDetailSettlePoint)
		stockProductDetailSettlePoint.GET("获取", "", web.GetStockProductDetailSettlePoint)
		stockProductDetailSettlePoint.GET("获取成品库存月结表列表", "list", web.GetStockProductDetailSettlePointList)
	}

	// 枚举
	{
		enum := product.Group("enum")
		enum.GET("显示方式枚举下拉", "getStockShowType", web.GetStockShowTypeEnum)
		enum.GET("库存状态枚举下拉", "getStockStatusEnum", web.GetStockStatusEnum)
	}

	// 成本
	{
		cost := product.Group("fpmCostPrice")
		cost.POST("新增成品成本", "", web.AddFpmCostPrice)
		cost.PUT("更新成品成本", "", web.UpdateFpmCostPrice)
		cost.GET("销售送货单成本清洗", "washShouldCollectOrderBuoyantWeightPrice", web.WashShouldCollectOrderBuoyantWeightPrice)
	}
}

func InitProductManage(server *grpc.Server) {
	var productManageServer web.ProductManageServer
	product_manage.RegisterProductManageServer(server, &productManageServer)
}

func InitPDAStockProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		stockProduct := product.Group("stockProduct")
		stockProduct.GET("查询库存(缸号库存)", "getStockProductDyelotNumberList", web.PDAGetStockProductDyelotNumberList)
		// 移架
		stockProduct.POST("移架", "move", web.PDAStockDetailMove)
	}
	// 枚举
	{
		enum := product.Group("enum")
		enum.GET("显示方式枚举下拉", "getStockShowType", web.GetStockShowTypeEnum)
		enum.GET("库存状态枚举下拉", "getStockStatusEnum", web.GetStockStatusEnum)
	}
}

func MPInitStockProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		stockProduct := product.Group("stockProduct")
		stockProduct.GET("查询库存(缸号库存)", "getStockProductDyelotNumberList", web.GetStockProductDyelotNumberList)
		stockProduct.GET("查询库存(缸号库存颜色汇总)", "getColorStockProductDyelotNumberList", web.GetStockProductDyelotNumberListV2)
		stockProduct.GET("查询库存", "getYBStockProductList", web.GetYBStockProductList)
		stockProduct.GET("查询缸号流水明细", "getDyelotDetail", web.GetDyelotDetail)
		stockProduct.GET("获取色号详情", "getColorDetailList", web.GetColorDetailList)
		stockProduct.GET("根据缸号和颜色id获取最大卷号", "getMaxVolumeNumber", web.GetMaxVolumeNumber)

		stockProduct.GET("获取汇总下拉列表", "getStockProductDropdownList", web.MPGetStockProductDropdownList) // 获取库存汇总列表
		stockProduct.GET("获取详情缸号分组列表", "getStockProductDyelotNumberDetailList", web.GetStockProductDyelotNumberDetailList)
		stockProduct.GET("获取详情细码选择列表", "getStockProductWeightDetailList", web.GetStockProductWeightDetailList)
	}
}

func H5InitStockProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		stockProduct := product.Group("stockProduct")
		stockProduct.GET("查询库存(缸号库存)", "getStockProductDyelotNumberList", web.GetStockProductDyelotNumberList)
		stockProduct.GET("查询库存(缸号库存颜色汇总)", "getColorStockProductDyelotNumberList", web.GetStockProductDyelotNumberListV2)
		stockProduct.GET("查询库存", "getYBStockProductList", web.GetYBStockProductList)
		stockProduct.GET("查询缸号流水明细", "getDyelotDetail", web.GetDyelotDetail)
		stockProduct.GET("获取色号详情", "getColorDetailList", web.GetColorDetailList)
		stockProduct.GET("根据缸号和颜色id获取最大卷号", "getMaxVolumeNumber", web.GetMaxVolumeNumber)

		stockProduct.GET("获取汇总下拉列表", "getStockProductDropdownList", web.MPGetStockProductDropdownList) // 获取库存汇总列表
		stockProduct.GET("获取详情缸号分组列表", "getStockProductDyelotNumberDetailList", web.GetStockProductDyelotNumberDetailList)
		stockProduct.GET("获取详情细码选择列表", "getStockProductWeightDetailList", web.GetStockProductWeightDetailList)
	}
}
