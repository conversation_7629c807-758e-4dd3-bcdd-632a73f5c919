CREATE TABLE IF NOT EXISTS `recharge_history` (
        `id` bigint(20) unsigned NOT NULL COMMENT 'id',
        `create_time` datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
        `creator_id` bigint(20) unsigned DEFAULT '0' NOT NULL COMMENT '创建人ID （关联user.id）',
        `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
        `update_time` datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
        `updater_id` bigint(20) unsigned DEFAULT '0' NOT NULL COMMENT '更新人ID （关联user.id）',
        `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
        `delete_time` datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
        `deleter_id` bigint(20) unsigned DEFAULT '0' NOT NULL COMMENT '删除人ID （关联user.id）',
        `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
        `code_list_orc_id` bigint(20) NOT NULL DEFAULT 0 COMMENT '码单id',
        `deadline` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '截止有效期',
        `remark` varchar(255) DEFAULT '' NOT NULL COMMENT '备注',
        `voucher` longtext DEFAULT '' NOT NULL COMMENT '凭证',
        `update_user_name` varchar(255) DEFAULT '' NOT NULL COMMENT '操作人',
        `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '充值类型',
        PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT ='充值记录表';