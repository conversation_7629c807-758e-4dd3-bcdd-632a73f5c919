package product

import (
	"context"
	"encoding/json"
	"fmt"
	aggs "hcscm/aggs/product"
	cus_error "hcscm/common/errors"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	fpm "hcscm/extern/pb/product"
	sale_pb "hcscm/extern/pb/sale"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"reflect"

	"github.com/gin-gonic/gin"
)

func NewFpmArrangeOrderService(ginCtx *gin.Context) *FpmArrangeOrderService {
	return &FpmArrangeOrderService{
		singleFlight: tools.Single,
		ginCtx:       ginCtx,
	}
}

type FpmArrangeOrderService struct {
	singleFlight tools.SingleFlight
	ginCtx       *gin.Context
}

// 添加配布单，
func (u FpmArrangeOrderService) Add(ctx context.Context, req *structure.AddFpmArrangeOrderParam) (data structure.AddFpmArrangeOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data.Id, _, err = repo.Add(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmArrangeOrderService) AddOrder(ctx context.Context, tx *mysql_base.Tx, req *structure.AddFpmArrangeOrderParam) (id uint64, salePlanOrderItemIds []uint64, err error) {

	repo := aggs.NewFpmArrangeOrderRepo(tx)
	id, salePlanOrderItemIds, err = repo.Add(ctx, req)
	if err != nil {
		return
	}

	return
}

func (u FpmArrangeOrderService) Update(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateFpmArrangeOrderParam) (data structure.UpdateFpmArrangeOrderData, stockIds map[uint64]bool, err error) {
	tx, commit := mysql_base.TransactionMainEx(tx, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, stockIds, err = repo.Update(ctx, req)
	if err != nil {
		return data, stockIds, err
	}
	return
}

// pda扫码啊配布使用
func (u FpmArrangeOrderService) UpdateFpmArrangeOrderFc(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateArrangeOrderFcParam, stock structure.GetStockProductDetailDropdownData) (data structure.UpdateFpmArrangeOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(tx, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, err = repo.UpdateFpmArrangeOrderFc(ctx, req, stock)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmArrangeOrderService) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmArrangeOrderBusinessCloseParam) (data structure.UpdateFpmArrangeOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, err = repo.UpdateBusinessClose(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmArrangeOrderService) UpdateStatusPass(ctx context.Context, tx *mysql_base.Tx, id uint64) (bookItemsReq structure.UpdateStockProductDetailParamList, stockIds []uint64, err error) {
	salePlanOrderItemIds := make([]uint64, 0)
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	bookItemsReq, salePlanOrderItemIds, stockIds, err = repo.UpdateStatusPass(ctx, id)
	if err != nil {
		return
	}

	if len(salePlanOrderItemIds) > 0 {
		err = sale_pb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, common_system.SituStatusProductArrangeOrder, true, id, "配布单审核")
		if err != nil {
			return
		}
	}
	return
}

func (u FpmArrangeOrderService) UpdateStatusWait(ctx context.Context, tx *mysql_base.Tx, id uint64) (
	data structure.UpdateFpmArrangeOrderStatusData, bookItemsReq structure.UpdateStockProductDetailParamList, stockIds []uint64, err error) {

	salePlanOrderItemIds := make([]uint64, 0)
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, bookItemsReq, salePlanOrderItemIds, stockIds, err = repo.UpdateStatusWait(ctx, id)
	if err != nil {
		return
	}

	if len(salePlanOrderItemIds) > 0 {
		err = sale_pb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, common_system.SituStatusProductSaleOrder, false, id, "配布单消审")
		if err != nil {
			return
		}
	}
	return
}

func (u FpmArrangeOrderService) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmArrangeOrderStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, err = repo.UpdateStatusCancel(ctx, id)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmArrangeOrderService) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmArrangeOrderStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, err = repo.UpdateStatusReject(ctx, id)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmArrangeOrderService) ExistOrder(ctx context.Context, tx *mysql_base.Tx, q *structure.ExistOrderQuery) (exist bool, err error) {
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	exist, err = repo.ExistOrder(ctx, q)
	if err != nil {
		return
	}
	return
}

func (u FpmArrangeOrderService) Get(ctx context.Context, req *structure.GetFpmArrangeOrderQuery) (data structure.GetFpmArrangeOrderData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	data, err = repo.Get(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmArrangeOrderService) GetList(ctx context.Context, req *structure.GetFpmArrangeOrderListQuery) (list structure.GetFpmArrangeOrderDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 确认出仓
// ArrangeOutStockResult 用于包装 ArrangeOutStock 的多个返回值
type ArrangeOutStockResult struct {
	Data                       structure.UpdateFpmArrangeOrderData
	AddShouldCollectOrderItems []*shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam
	StockMap                   map[uint64]int
	IsAllPass                  bool
}

func (u FpmArrangeOrderService) ArrangeOutStock(ctx context.Context, req *structure.ArrangeGoodsOutParam, tx *mysql_base.Tx) (
	data structure.UpdateFpmArrangeOrderData,
	addShouldCollectOrderItems []*shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam,
	err error, stockMap map[uint64]int, IsAllPass bool) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(req)
	if err != nil {
		return
	}

	val, err = u.singleFlight.Do(tools.GetRequestKey(u.ginCtx, paramByte), func() (res interface{}, err error) {
		res = data
		var (
			getArrangeOrder  = structure.GetFpmArrangeOrderData{}
			getArrangeOrders = structure.GetFpmArrangeOrderDataList{}
			repo             = aggs.NewFpmArrangeOrderRepo(tx)
			modelSwap        interface{}
			createId         uint64
		)
		getArrangeOrder, err = repo.Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: req.Id})
		if err != nil {
			return
		}

		// pda扫码可以多扫细码，所以后台确认出仓之前都可以修改细码信息，确认出仓时判断细码匹数正不正确
		{
			// 不是已审核状态不能确认出仓
			if getArrangeOrder.AuditStatus != common_system.OrderStatusAudited {
				err = middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，单据未审核，请审核后确认出仓"))
				return
			}
			for _, item := range getArrangeOrder.ItemData {
				var fcRoll int
				for _, fcItem := range item.ItemFCData {
					fcRoll += fcItem.Roll
				}
				if fcRoll != item.ArrangeRoll {
					err = middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，匹数不正确，无法出仓"))
					return
				}
			}
		}

		// 查询对应的销售单
		saleProductMap, _ := sale_pb.NewSaleClient().GetSaleOrderByIds(ctx, []uint64{getArrangeOrder.SrcId})
		items, err := sale_pb.NewSaleClient().GetSaleOrderDetailsByParentId(ctx, getArrangeOrder.SrcId)
		if err != nil {
			return
		}
		warehouseIds := mysql_base.GetUInt64List(items, "warehouse_id")
		if saleProductMap[getArrangeOrder.SrcId].PickUpGoodsInOrder == false || len(warehouseIds) == 1 {
			// 判断库存是否足够并占用库存
			err, stockMap = repo.JudgeStockIsEnough(ctx, getArrangeOrder)
			// 确认出仓结构体转换
			modelSwap, err = repo.ArrangeOutStockSwap(ctx, getArrangeOrder)
			if err != nil {
				return
			}
			// 确认出仓创建
			err, createId = u.ArrangeOutStockCreateOrder(getArrangeOrder.OutOrderType, tx, modelSwap)
			if err != nil {
				return
			}
			data.Id = req.Id
			data.CreateType = getArrangeOrder.OutOrderType
			data.CreateId = createId
			data.SrcId = getArrangeOrder.SrcId
			// 改变配布业务状态(待出仓)
			_, err = repo.UpdateBusinessStatus(ctx, cus_const.BusinessStatusArrangeWaitOut, req.Id)
			if err != nil {
				return
			}

			// 成品销售单
			if getArrangeOrder.SrcType == cus_const.ArrangeOrderFromSaleOrder {
				addShouldCollectOrderItem, err2 := repo.GetProductSaleShouldCollectOrderParam(ctx, req.Id)
				if err2 != nil {
					return
				}
				if saleProduct, ok := saleProductMap[getArrangeOrder.SrcId]; ok {
					addShouldCollectOrderItem.VoucherNumber = saleProduct.VoucherNumber
					addShouldCollectOrderItem.TaxRate = saleProduct.SaleTaxRate
				}
				addShouldCollectOrderItems = append(addShouldCollectOrderItems, addShouldCollectOrderItem)
			}
			// fmt.Println(modelSwap)
			// 返回包含所有需要返回值的结构体
			return ArrangeOutStockResult{
				Data:                       data,
				AddShouldCollectOrderItems: addShouldCollectOrderItems,
				StockMap:                   stockMap,
				IsAllPass:                  IsAllPass,
			}, nil
		} else {
			getArrangeOrders, _, err = repo.GetList(ctx, &structure.GetFpmArrangeOrderListQuery{SrcId: getArrangeOrder.SrcId, IsNotPage: true})
			if err != nil {
				return
			}
			// 检查是否全都已出仓
			if warehouseType, ok := u.CheckIsPass(ctx, tx, getArrangeOrders, getArrangeOrder.Id); ok {
				IsAllPass = true
				if warehouseType == 2 || warehouseType == 3 || warehouseType == 5 || warehouseType == 4 {
					var filteredList structure.GetFpmArrangeOrderDataList
					if warehouseType != 5 {
						// 过滤掉指定的arrangeId
						for _, v := range getArrangeOrders {
							if v.Id != getArrangeOrder.Id {
								filteredList = append(filteredList, v)
							}
						}
					} else {
						for _, v := range getArrangeOrders {
							if v.Id == getArrangeOrder.Id {
								filteredList = append(filteredList, v)
							}
						}
					}
					// 判断库存是否足够并占用库存
					err, stockMap = repo.JudgeStockIsEnough(ctx, getArrangeOrder)
					// 确认出仓结构体转换
					modelSwap, err = repo.ArrangeOutStockSwap(ctx, getArrangeOrder)
					if err != nil {
						return
					}
					for _, v := range filteredList {
						// 判断库存是否足够并占用库存
						err, stockMap = repo.JudgeStockIsEnough(ctx, v)
						// 确认出仓结构体转换
						siblingModel, err1 := repo.ArrangeOutStockSwap(ctx, v)
						if err1 != nil {
							return
						}
						if warehouseType == 2 {
							if mainOrder, ok := modelSwap.(structure.AddFpmSaleOutOrderParam); ok {
								if siblingOrder, ok := siblingModel.(structure.AddFpmSaleAllocateOutOrderParam); ok {
									mainOrder.ItemData = append(mainOrder.ItemData, siblingOrder.ItemData...)
									modelSwap = mainOrder
								}
							}
						}
						if warehouseType == 3 || warehouseType == 5 {
							// 确认出仓结构体转换
							modelSwap, err = repo.ArrangeOutStockSwap(ctx, getArrangeOrder)
							if err != nil {
								return
							}
							// 确认出仓创建
							err, createId = u.ArrangeOutStockCreateOrder(getArrangeOrder.OutOrderType, tx, modelSwap)
							if err != nil {
								return
							}

							data.CreateId = createId
						}
					}
					for _, v := range getArrangeOrders {
						addShouldCollectOrderItem, err2 := repo.GetProductSaleShouldCollectOrderParam(ctx, v.Id)
						if err2 != nil {
							return
						}
						if saleProduct, ok := saleProductMap[v.SrcId]; ok {
							addShouldCollectOrderItem.VoucherNumber = saleProduct.VoucherNumber
							addShouldCollectOrderItem.TaxRate = saleProduct.SaleTaxRate
							addShouldCollectOrderItem.SrcId = v.Id
						}
						data.Id = req.Id
						data.CreateType = getArrangeOrder.OutOrderType
						data.SrcId = getArrangeOrder.SrcId
						addShouldCollectOrderItems = append(addShouldCollectOrderItems, addShouldCollectOrderItem)
					}
					// 改变配布业务状态(待出仓)
					_, err = repo.UpdateBusinessStatus(ctx, cus_const.BusinessStatusArrangeWaitOut, req.Id)
					if err != nil {
						return
					}
				}
				//if warehouseType == 4 {
				//	for _, v := range getArrangeOrders {
				//		addShouldCollectOrderItem, err2 := repo.GetProductSaleShouldCollectOrderParam(ctx, v.Id)
				//		if err2 != nil {
				//			return
				//		}
				//		if saleProduct, ok := saleProductMap[v.SrcId]; ok {
				//			addShouldCollectOrderItem.VoucherNumber = saleProduct.VoucherNumber
				//			addShouldCollectOrderItem.TaxRate = saleProduct.SaleTaxRate
				//			addShouldCollectOrderItem.SrcId = v.Id
				//		}
				//		addShouldCollectOrderItems = append(addShouldCollectOrderItems, addShouldCollectOrderItem)
				//	}
				//	data.Id = req.Id
				//	data.CreateType = getArrangeOrder.OutOrderType
				//	data.SrcId = getArrangeOrder.SrcId
				//	// 改变配布业务状态(待出仓)
				//	_, err = repo.UpdateBusinessStatus(ctx, cus_const.BusinessStatusArrangeWaitOut, req.Id)
				//	if err != nil {
				//		return
				//	}
				//}
			} else {
				// 为2说明它是先走出货配布单出仓
				if getArrangeOrder.OutOrderType == cus_const.WarehouseGoodOutTypeSaleAllocate {
					// 判断库存是否足够并占用库存
					err, stockMap = repo.JudgeStockIsEnough(ctx, getArrangeOrder)
					if err != nil {
						return
					}
					// 确认出仓结构体转换
					modelSwap, err = repo.ArrangeOutStockSwap(ctx, getArrangeOrder)
					if err != nil {
						return
					}
					// 确认出仓创建
					err, createId = u.ArrangeOutStockCreateOrder(getArrangeOrder.OutOrderType, tx, modelSwap)
					if err != nil {
						return
					}
					data.Id = req.Id
					data.CreateType = getArrangeOrder.OutOrderType
					data.CreateId = createId
					data.SrcId = getArrangeOrder.SrcId
					// 改变配布业务状态(待出仓)
				}
				_, err = repo.UpdateBusinessStatus(ctx, cus_const.BusinessStatusArrangeWaitOut, req.Id)
				if err != nil {
					return
				}
			}
		}
		// 返回包含所有需要返回值的结构体
		return ArrangeOutStockResult{
			Data:                       data,
			AddShouldCollectOrderItems: addShouldCollectOrderItems,
			StockMap:                   stockMap,
			IsAllPass:                  IsAllPass,
		}, nil
	})
	// fmt.Println(modelSwap)
	if err != nil {
		return
	}

	// 从返回的结构体中提取各个返回值
	result := val.(ArrangeOutStockResult)
	return result.Data, result.AddShouldCollectOrderItems, nil, result.StockMap, result.IsAllPass
}

// 配布点击生成，自动生成对应的出仓单
func (u FpmArrangeOrderService) ArrangeOutStockCreateOrder(orderType cus_const.WarehouseGoodOutType, tx *mysql_base.Tx, swap interface{}) (error, uint64) {
	fmt.Println(reflect.TypeOf(swap))
	switch orderType {
	case cus_const.WarehouseGoodOutTypeInternalAllocate: // 内部调拨出仓
		allocateRepo := aggs.NewFpmInternalAllocateOutOrderRepo(tx)
		param := swap.(structure.AddFpmInternalAllocateOutOrderParam)
		data, err := allocateRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	case cus_const.WarehouseGoodOutTypeSaleAllocate: // 销售调拨出仓
		allocateRepo := aggs.NewFpmSaleAllocateOutOrderRepo(tx)
		param := swap.(structure.AddFpmSaleAllocateOutOrderParam)
		data, err := allocateRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	case cus_const.WarehouseGoodOutTypeSale: // 销售出仓
		outRepo := aggs.NewFpmSaleOutOrderRepo(tx)
		param := swap.(structure.AddFpmSaleOutOrderParam)
		data, err := outRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	case cus_const.WarehouseGoodOutTypePurchaseReturn: // 采购退货出仓
		outRepo := aggs.NewFpmOutOrderRepo(tx)
		param := swap.(structure.AddFpmOutOrderParam)
		data, err := outRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	case cus_const.WarehouseGoodOutTypeProcess, cus_const.WarehouseGoodOutTypeTypeRepair: // 加工出仓,回修出仓
		processOutRepo := aggs.NewFpmProcessOutOrderRepo(tx)
		param := swap.(structure.AddFpmProcessOutOrderParam)
		data, err := processOutRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	case cus_const.WarehouseGoodOutTypeOther: // 其他出仓
		outRepo := aggs.NewFpmOutOrderRepo(tx)
		param := swap.(structure.AddFpmOutOrderParam)
		data, err := outRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	case cus_const.WarehouseGoodOutTypeDeduction: // 扣款出仓
		outRepo := aggs.NewFpmOutOrderRepo(tx)
		param := swap.(structure.AddFpmOutOrderParam)
		data, err := outRepo.Add(tx.Context, &param)
		if err != nil {
			return err, 0
		}
		return nil, data.Id
	}
	return nil, 0
}

// 变更按钮（判断配布 是否被引用且状态为已审核）
func (u FpmArrangeOrderService) JudgeChangeArrangeOrder(ctx context.Context, tx *mysql_base.Tx, q *structure.ArrangeOrderChangeJudgeParam) (data structure.ArrangeOrderChangeJudgeData, err error) {
	var (
		exit      bool
		orderData = structure.GetCreateByArrangeOrderSOutOrderData{}
	)
	tx, commit := mysql_base.TransactionMainEx(tx, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	exit, orderData, err = repo.JudgeIsQuoted(q.Id)
	if err != nil {
		return data, err
	}
	if exit && orderData.AuditStatus == common_system.OrderStatusAudited {
		return data, cus_error.ErrCodeOrderIsQuoteAndPass
	}
	return
}

// 修改业务状态
func (u FpmArrangeOrderService) ChangeBusinessStatus(ctx context.Context, tx *mysql_base.Tx, id uint64) (err error) {
	repo := aggs.NewFpmArrangeOrderRepo(tx)
	err = repo.ChangeBusinessStatus(ctx, id)
	if err != nil {
		return
	}
	return
}

// CheckIsPass 检查是否全部出仓
func (u *FpmArrangeOrderService) CheckIsPass(ctx context.Context, tx *mysql_base.Tx, list structure.GetFpmArrangeOrderDataList, arrangeId uint64) (warehouseType int, isAllPass bool) {
	var (
		repo = aggs.NewFpmArrangeOrderRepo(tx)
	)
	// 判断出仓类型
	warehouseType = u.GetWarehouseIdType(list)
	// 说明只有一张配布单
	if len(list) > 1 {
		// 过滤掉指定的arrangeId
		var filteredList structure.GetFpmArrangeOrderDataList
		for _, v := range list {
			if v.Id != arrangeId {
				filteredList = append(filteredList, v)
			}
		}
		list = filteredList
	}
	// 根据不同类型进行处理
	if warehouseType == 2 {
		for _, order := range list {
			if order.OutOrderType == cus_const.WarehouseGoodOutTypeSale {
				get, err := repo.Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: order.Id})
				if err != nil {
					return
				}
				if get.BusinessStatus != cus_const.BusinessStatusArrangeWaitOut {
					return
				}

			} else {
				o, err := fpm.NewFpmInOrderClient().GetFpmInOrderByArrangeId(ctx, order.Id)
				if err != nil {
					return
				}
				if o.Id == 0 || o.AuditStatus != common_system.OrderStatusAudited {
					// 检查所有单据是否都满足条件
					allSatisfied := true
					for _, v := range list {
						// 如果有任何一个单据不是这三种状态之一，则设置allSatisfied为false
						if v.BusinessStatus != cus_const.BusinessStatusArrangeWaitOut &&
							v.BusinessStatus != cus_const.BusinessStatusArrangeSaleAlloFinish &&
							v.BusinessStatus != cus_const.BusinessStatusArrangeOuting {
							allSatisfied = false
							break
						}
					}
					// 只有当所有单据都是这三种状态之一时才返回true，否则返回false
					return 4, allSatisfied
				}

			}
		}
		// 类型二：有出仓有销调
		return warehouseType, true
	} else if warehouseType == 3 {
		for _, order := range list {
			// 类型三：只有销调
			os, err := fpm.NewFpmInOrderClient().GetFpmInOrderByArrangeId(ctx, order.Id)
			if err != nil {
				return
			}
			if os.Id == 0 || os.AuditStatus != common_system.OrderStatusAudited {
				// 检查兄弟配布单是否都已经配布等待出仓
				for _, brotherOrder := range list {
					get, err := repo.Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: brotherOrder.Id})
					if err != nil {
						return
					}
					if get.BusinessStatus != cus_const.BusinessStatusArrangeSaleAlloFinish && get.BusinessStatus != cus_const.BusinessStatusArrangeWaitOut {
						return
					}
				}
				// 所有兄弟配布单都已配布等待出仓
				return 5, true
			}
		}
	} else {
		// 类型一：所有单据都是出仓
		for _, order := range list {
			get, err := repo.Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: order.Id})
			if err != nil {
				return
			}
			if get.BusinessStatus != cus_const.BusinessStatusArrangeOuted {
				return
			}
		}
	}
	return warehouseType, true
}

// GetWarehouseIdType 判断仓库ID类型
// 返回值：
// 1 - 类型一：所有单据类型都是出仓(WarehouseGoodOutTypeSale)
// 2 - 类型二：部分单据是出仓(WarehouseGoodOutTypeSale)，部分是销调(WarehouseGoodOutTypeSaleAllocate)
// 3 - 类型三：所有单据类型都是销调(WarehouseGoodOutTypeSaleAllocate)
func (u *FpmArrangeOrderService) GetWarehouseIdType(list structure.GetFpmArrangeOrderDataList) int {
	// 初始化计数器
	saleCount := 0         // 出仓单数量
	saleAllocateCount := 0 // 销调单数量

	// 遍历单据列表，统计不同类型的单据数量
	for _, item := range list {
		if item.OutOrderType == cus_const.WarehouseGoodOutTypeSale {
			saleCount++
		} else if item.OutOrderType == cus_const.WarehouseGoodOutTypeSaleAllocate {
			saleAllocateCount++
		}
	}

	// 判断类型
	if saleCount > 0 && saleAllocateCount == 0 {
		// 类型一：所有单据都是出仓
		return 1
	} else if saleCount > 0 && saleAllocateCount > 0 {
		// 类型二：部分是出仓，部分是销调
		return 2
	} else {
		// 类型三：所有单据都是销调
		return 3
	}
}
