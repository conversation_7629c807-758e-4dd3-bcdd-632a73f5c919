package common

func GetBookOrderTypeMap() (r map[BookOrderType]string) {
	l := []BookOrderType{BookOrderTypeReservationPass, BookOrderTypeReservationWait, BookOrderTypeArrangeChangePass, BookOrderTypeArrangeChangeWait, BookOrderTypePurchaseReturnPass, BookOrderTypePurchaseReturnWait, BookOrderTypePMCPush, BookOrderTypeProductSalePass, BookOrderTypeProductSaleWait, BookOrderTypeProductSaleOutPass, BookOrderTypeProductSaleOutWait, BookOrderTypeProductIntAlloOutPass, BookOrderTypeProductIntAlloOutWait, BookOrderTypeProductPrtOutPass, BookOrderTypeProductPrtOutWait, BookOrderTypeProductOtherOutPass, BookOrderTypeProductOtherOutWait, BookOrderTypeProductDeductionOutPass, BookOrderTypeProductDeductionOutWait, BookOrderTypeProductCheckOutPass, BookOrderTypeProductCheckOutWait, BookOrderTypeProductProcessOutPass, BookOrderTypeProductProcessOutWait, BookOrderTypeProductSaleAlloOutPass, BookOrderTypeProductSaleAlloOutWait, BookOrderTypeProductAdjustOutPass, BookOrderTypeProductAdjustOutWait, BookOrderTypeProductSaleAlloInPass, BookOrderTypeProductSaleAlloInWait}
	r = make(map[BookOrderType]string)
	for _, k := range l {
		r[k] = k.String()
	}
	return r
}
func GetBookOrderTypeReverseMap() (r map[string]BookOrderType) {
	l := []BookOrderType{BookOrderTypeReservationPass, BookOrderTypeReservationWait, BookOrderTypeArrangeChangePass, BookOrderTypeArrangeChangeWait, BookOrderTypePurchaseReturnPass, BookOrderTypePurchaseReturnWait, BookOrderTypePMCPush, BookOrderTypeProductSalePass, BookOrderTypeProductSaleWait, BookOrderTypeProductSaleOutPass, BookOrderTypeProductSaleOutWait, BookOrderTypeProductIntAlloOutPass, BookOrderTypeProductIntAlloOutWait, BookOrderTypeProductPrtOutPass, BookOrderTypeProductPrtOutWait, BookOrderTypeProductOtherOutPass, BookOrderTypeProductOtherOutWait, BookOrderTypeProductDeductionOutPass, BookOrderTypeProductDeductionOutWait, BookOrderTypeProductCheckOutPass, BookOrderTypeProductCheckOutWait, BookOrderTypeProductProcessOutPass, BookOrderTypeProductProcessOutWait, BookOrderTypeProductSaleAlloOutPass, BookOrderTypeProductSaleAlloOutWait, BookOrderTypeProductAdjustOutPass, BookOrderTypeProductAdjustOutWait, BookOrderTypeProductSaleAlloInPass, BookOrderTypeProductSaleAlloInWait}
	r = make(map[string]BookOrderType)
	for _, k := range l {
		r[k.String()] = k
	}
	return r
}
func GetBookOrderTypeReverseIntMap() (r map[string]int) {
	l := []BookOrderType{BookOrderTypeReservationPass, BookOrderTypeReservationWait, BookOrderTypeArrangeChangePass, BookOrderTypeArrangeChangeWait, BookOrderTypePurchaseReturnPass, BookOrderTypePurchaseReturnWait, BookOrderTypePMCPush, BookOrderTypeProductSalePass, BookOrderTypeProductSaleWait, BookOrderTypeProductSaleOutPass, BookOrderTypeProductSaleOutWait, BookOrderTypeProductIntAlloOutPass, BookOrderTypeProductIntAlloOutWait, BookOrderTypeProductPrtOutPass, BookOrderTypeProductPrtOutWait, BookOrderTypeProductOtherOutPass, BookOrderTypeProductOtherOutWait, BookOrderTypeProductDeductionOutPass, BookOrderTypeProductDeductionOutWait, BookOrderTypeProductCheckOutPass, BookOrderTypeProductCheckOutWait, BookOrderTypeProductProcessOutPass, BookOrderTypeProductProcessOutWait, BookOrderTypeProductSaleAlloOutPass, BookOrderTypeProductSaleAlloOutWait, BookOrderTypeProductAdjustOutPass, BookOrderTypeProductAdjustOutWait, BookOrderTypeProductSaleAlloInPass, BookOrderTypeProductSaleAlloInWait}
	r = make(map[string]int)
	for _, k := range l {
		r[k.String()] = int(k)
	}
	return r
}

func (t BookOrderType) Check() bool {
	l := []BookOrderType{BookOrderTypeReservationPass, BookOrderTypeReservationWait, BookOrderTypeArrangeChangePass, BookOrderTypeArrangeChangeWait, BookOrderTypePurchaseReturnPass, BookOrderTypePurchaseReturnWait, BookOrderTypePMCPush, BookOrderTypeProductSalePass, BookOrderTypeProductSaleWait, BookOrderTypeProductSaleOutPass, BookOrderTypeProductSaleOutWait, BookOrderTypeProductIntAlloOutPass, BookOrderTypeProductIntAlloOutWait, BookOrderTypeProductPrtOutPass, BookOrderTypeProductPrtOutWait, BookOrderTypeProductOtherOutPass, BookOrderTypeProductOtherOutWait, BookOrderTypeProductDeductionOutPass, BookOrderTypeProductDeductionOutWait, BookOrderTypeProductCheckOutPass, BookOrderTypeProductCheckOutWait, BookOrderTypeProductProcessOutPass, BookOrderTypeProductProcessOutWait, BookOrderTypeProductSaleAlloOutPass, BookOrderTypeProductSaleAlloOutWait, BookOrderTypeProductAdjustOutPass, BookOrderTypeProductAdjustOutWait, BookOrderTypeProductSaleAlloInPass, BookOrderTypeProductSaleAlloInWait}
	for i := range l {
		if l[i] == t {
			return true
		}
	}
	return false
}
