package sale

import (
	"context"
	"fmt"
	common_dnf "hcscm/common/dnf"
	"hcscm/common/errors"
	cus_const "hcscm/common/raw_material_manage"
	common "hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/grey_fabric_info"
	"hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/raw_material"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dyeing_and_finishing"
	"hcscm/extern/pb/grey_fabric_manage"
	"hcscm/extern/pb/produce"
	fpm "hcscm/extern/pb/product"
	"hcscm/extern/pb/purchase"
	"hcscm/extern/pb/raw_material_dye"
	"hcscm/extern/pb/raw_matl_order"
	"hcscm/extern/pb/sale"
	system_pb "hcscm/extern/pb/system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/redis"
	dnfSvc "hcscm/service/dyeing_and_finishing"
	"hcscm/service/finish_product_order"
	gfmSvc "hcscm/service/grey_fabric_manage"
	produceSvc "hcscm/service/produce"
	productSvc "hcscm/service/product"
	purchaseSvc "hcscm/service/purchase"
	rmdSvc "hcscm/service/raw_material_dye"
	"hcscm/service/raw_material_order"
	saleSvc "hcscm/service/sale"
	structure_gf_info "hcscm/structure/basic_data/grey_fabric_info"
	structure_raw_matl "hcscm/structure/basic_data/raw_material"
	structure_dnf "hcscm/structure/dyeing_and_finishing"
	structure_gfm "hcscm/structure/grey_fabric_manage"
	structure_produce "hcscm/structure/produce"
	productStructure "hcscm/structure/product"
	structure_purchase "hcscm/structure/purchase"
	structure_rmd "hcscm/structure/raw_material_dye"
	structure "hcscm/structure/sale"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/set"
	"time"
)

func IsExistOrder(ctx context.Context, ids []uint64) (err error) {
	var (
		exist    bool
		orderNos []string
	)
	// 现货出货
	// 成品
	productSaleServiceSvc := sale.NewSalePlanClient(ctx)
	_, orderNos, exist, err = productSaleServiceSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，成品销售单：", orderNos)))
		return
	}
	// 坯布
	greyFabricSaleServiceSvc := grey_fabric_manage.NewGfmSaleDeliveryOrderClient()
	_, orderNos, exist, err = greyFabricSaleServiceSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，坯布销售单：", orderNos)))
		return
	}
	// 原料
	rawMatlSaleServiceSvc := raw_matl_order.NewClientRawMatlOrderService()
	_, orderNos, exist, err = rawMatlSaleServiceSvc.GetSaleOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，原料销售单：", orderNos)))
		return
	}
	// 成品采购(成品)
	productPurchaseSvc := purchase.NewPurchaseFinishProductClient()
	_, orderNos, exist, err = productPurchaseSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，成品采购单：", orderNos)))
		return
	}
	// 坯布染整(成品)，成品加工，成品回修
	dyeingAndFinishingSvc := dyeing_and_finishing.NewClientDNFService()
	_, orderNos, exist, err = dyeingAndFinishingSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids, 0)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，染整通知单：", orderNos)))
		return
	}
	// 坯布采购(成品，坯布)
	greyFabricPurchaseSvc := purchase.NewPurchaseGreyFabricClient()
	_, orderNos, exist, err = greyFabricPurchaseSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，坯布采购单：", orderNos)))
		return
	}
	// 坯布织造(成品，坯布)
	greyFabricProduceSvc := produce.NewProduceClient()
	_, orderNos, exist, err = greyFabricProduceSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，生产通知单：", orderNos)))
		return
	}
	// 原料加工(坯布，原料)
	rmmDyeNotifyOrderSvc := raw_material_dye.NewClientRawMatlDyeNotifyOrderService()
	_, orderNos, exist, err = rmmDyeNotifyOrderSvc.GetOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，原料染整单：", orderNos)))
		return
	}
	// 原料采购(坯布，原料)
	rawMatlPurchaseSvc := raw_matl_order.NewClientRawMatlOrderService()
	_, orderNos, exist, err = rawMatlPurchaseSvc.GetPurchaseOrderNoByPmcPlanOrderIds(ctx, false, ids)
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, fmt.Sprintf("%v%v", "，原料采购单：", orderNos)))
		return
	}
	return
}

func Get(ctx context.Context, tx *mysql_base.Tx, data *structure.AddPmcGreyPlanOrderSummary) (list structure.PushedRecordList, err error) {
	var (
		orderInfos system_pb.OrderInfoList
		exist      bool
	)
	switch data.PushType {
	case common.PushTypeSale:
		// 现货出货
		if data.PlanType == common.PlanTypeProduct {
			// 成品
			productSaleServiceSvc := sale.NewSalePlanClient(ctx)
			list, _, exist, err = productSaleServiceSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
			if err != nil {
				return
			}
			return
		}
		if data.PlanType == common.PlanTypeGreyFabric {
			// 坯布
			greyFabricSaleServiceSvc := grey_fabric_manage.NewGfmSaleDeliveryOrderClient()
			orderInfos, _, exist, err = greyFabricSaleServiceSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
			if err != nil {
				return
			}
		}
		if data.PlanType == common.PlanTypeRawMaterial {
			// 原料
			rawMatlSaleServiceSvc := raw_matl_order.NewClientRawMatlOrderService()
			orderInfos, _, exist, err = rawMatlSaleServiceSvc.GetSaleOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
			if err != nil {
				return
			}
		}
	case common.PushTypeProductPurchase:
		// 成品采购(成品)
		productPurchaseSvc := purchase.NewPurchaseFinishProductClient()
		orderInfos, _, exist, err = productPurchaseSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
		if err != nil {
			return
		}
	case common.PushTypeDNFNoticeOrder:
		// 坯布染整(成品)
		dyeingAndFinishingSvc := dyeing_and_finishing.NewClientDNFService()
		orderInfos, _, exist, err = dyeingAndFinishingSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId}, 1)
		if err != nil {
			return
		}
	case common.PushTypeProductFinishing:
		// 成品加工(成品)
		dyeingAndFinishingSvc := dyeing_and_finishing.NewClientDNFService()
		orderInfos, _, exist, err = dyeingAndFinishingSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId}, 2)
		if err != nil {
			return
		}
	case common.PushTypeProductRedye:
		// 成品回修(成品)
		dyeingAndFinishingSvc := dyeing_and_finishing.NewClientDNFService()
		orderInfos, _, exist, err = dyeingAndFinishingSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId}, 3)
		if err != nil {
			return
		}
	case common.PushTypeGreyPurchase:
		// 坯布采购(成品，坯布)
		greyFabricPurchaseSvc := purchase.NewPurchaseGreyFabricClient()
		orderInfos, _, exist, err = greyFabricPurchaseSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
		if err != nil {
			return
		}
	case common.PushTypeProductionPlanOrder:
		// 坯布织造(成品，坯布)
		greyFabricProduceSvc := produce.NewProduceClient()
		orderInfos, _, exist, err = greyFabricProduceSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
		if err != nil {
			return
		}
	case common.PushTypeRMProcess:
		// 原料加工(坯布，原料)
		rmmDyeNotifyOrderSvc := raw_material_dye.NewClientRawMatlDyeNotifyOrderService()
		orderInfos, _, exist, err = rmmDyeNotifyOrderSvc.GetOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
		if err != nil {
			return
		}
	case common.PushTypeRMPurchase:
		// 原料采购(坯布，原料)
		rawMatlPurchaseSvc := raw_matl_order.NewClientRawMatlOrderService()
		orderInfos, _, exist, err = rawMatlPurchaseSvc.GetPurchaseOrderNoByPmcPlanOrderIds(ctx, true, []uint64{data.PmcGreyPlanOrderId})
		if err != nil {
			return
		}
	}
	if !exist {
		return
	}
	for _, orderInfo := range orderInfos {
		o := structure.PushedRecord{}
		o.OrderId = orderInfo.OrderId
		o.OrderNo = orderInfo.OrderNo
		o.OrderStatus = orderInfo.OrderStatus
		o.OrderStatusName = orderInfo.OrderStatusName
		o.Roll = orderInfo.Roll
		o.WholePieceCount = orderInfo.WholePieceCount
		o.BulkPieceCount = orderInfo.BulkPieceCount
		o.Weight = orderInfo.Weight
		o.CreatorID = orderInfo.CreatorID
		o.CreatorName = orderInfo.CreatorName
		o.CreateTime = tools.MyTime(orderInfo.CreateTime)
		o.MaterialCodeAndName = orderInfo.MaterialCodeAndName
		o.MaterialColorCodeAndName = orderInfo.MaterialColorCodeAndName
		o.SituId = orderInfo.SituId
		o.SituName = orderInfo.SituName
		o.EditSituUserId = orderInfo.EditSituUserId
		o.EditSituUserName = orderInfo.EditSituUserName
		o.EditSituTime = tools.MyTime(orderInfo.EditSituTime)
		o.FinishStatusName = orderInfo.FinishStatusName
		list = append(list, o)
	}
	return
}

func ToPushNext(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, data *structure.AddPmcGreyPlanOrderSummary) (err error) {
	// 表头信息
	// for _, data := range list {
	var (
		greyFabricItems      map[uint64]*structure_gf_info.GetGreyFabricInfoData
		greyFabricRawItems   map[uint64][]grey_fabric_info.GetRawMaterialInfoData
		products             map[uint64]*product.ProductRes
		productDyeColors     map[uint64]*product.FinishProductDyeingColorItem
		bizUnits             map[uint64]biz_unit.Res
		rawMaterials         map[uint64]*structure_raw_matl.RawMaterialItem
		saleProductPlanOrder structure.GetSaleProductPlanOrderData
	)
	// 坯布信息
	greyFabricIds := mysql_base.GetUInt64ListV2("grey_fabric_id", data)
	greyFabricSvc := grey_fabric_info.NewGreyFabricInfoClient()
	greyFabricItems, err = greyFabricSvc.GetGreyFabricInfoMapList(ctx, greyFabricIds)
	if err != nil {
		return
	}
	greyFabricRawItems, err = greyFabricSvc.GetRawMaterialListMapByIds(ctx, greyFabricIds)
	if err != nil {
		return
	}
	// 往来单位信息
	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", data)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnits, err = bizUnitSvc.QueryBizUnitListById(ctx, bizUnitIds)
	if err != nil {
		return
	}
	// 获取成品信息
	productIds := mysql_base.GetUInt64ListV2("product_id", data)
	productSvcPb := product.NewProductClient()
	products, err = productSvcPb.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}
	// 获取原料信息
	rawMaterialIds := mysql_base.GetUInt64ListV2("raw_material_id", data)
	rawMaterialSvc := raw_material.NewClientRawMatlInfoService()
	rawMaterials, err = rawMaterialSvc.GetRawMatlItemByIds(ctx, rawMaterialIds)
	if err != nil {
		return
	}

	// 获取库存
	stockIds := mysql_base.GetUInt64ListV2("stock_id", data.GFDetailItems)
	stockMap, _ := fpm.NewProductStockClient().GetStockByIds(ctx, stockIds)

	// 查询销售计划单
	saleProductPlanOrder, err = saleSvc.NewSaleProductPlanOrderService(ctx, false).Get(ctx, &structure.GetSaleProductPlanOrderQuery{Id: data.SaleProductPlanOrderId})
	if err != nil {
		return
	}

	var saleShipmentTypeMap map[uint64]info_basic_data.GetInfoSaleShipmentTypeData
	saleShipmentTypeMap, err = info_basic_data.NewInfoSaleShipmentTypeClient().GetInfoSaleShipmentTypeByIds(ctx, &info_basic_data.GetInfoSaleShipmentTypeQuery{Ids: []uint64{saleProductPlanOrder.SaleShipmentId}})
	if err != nil {
		return
	}

	// 计划分配信息
	// for _, detail := range data.GFDetailItems {
	switch data.PushType {
	case common.PushTypeSale:
		// 成品现货出货
		if data.PlanType == common.PlanTypeProduct {
			o := &structure.AddSaleProductOrderParam{}
			var (
				items = make(structure.AddSaleProductOrderDetailParamList, 0)
			)
			o.SaleProductPlanOrderId = saleProductPlanOrder.Id
			o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
			o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
			o.SaleSystemId = data.SaleSystemId
			o.CustomerId = data.CustomerId
			o.SaleUserId = bizUnits[data.CustomerId].SaleUserId
			o.SaleFollowerId = bizUnits[data.CustomerId].OrderFollowerID
			o.SettleType = common.SettleType(bizUnits[data.CustomerId].SettleType)
			o.SaleGroupId = bizUnits[data.CustomerId].SaleGroupID
			o.VoucherNumber = data.VoucherNumber
			o.IsWithTaxRate = saleProductPlanOrder.IsWithTaxRate
			o.SaleTaxRate = saleProductPlanOrder.SaleTaxRate
			o.SendProductRemark = data.InternalRemark
			o.OrderTime = tools.QueryTime(time.Now().Format("2006-01-02"))
			o.SendProductType = saleShipmentTypeMap[saleProductPlanOrder.SaleShipmentId].OutOrderType
			o.WarehouseId = saleShipmentTypeMap[saleProductPlanOrder.SaleShipmentId].WareHouseInId
			// 适配旧单，默认出货类型
			if o.SendProductType == 0 {
				o.SendProductType = common.SendProductTypeDelivery
			}
			o.SaleMode = common.BulkTypeOrder
			o.ProcessFactoryId = saleProductPlanOrder.ProcessFactoryId
			o.ProcessFactory = saleProductPlanOrder.ProcessFactory
			o.Contacts = saleProductPlanOrder.ContactName
			o.ContactPhone = saleProductPlanOrder.CustomerPhone
			o.PrintTag = saleProductPlanOrder.PrintTag
			o.LogisticsCompany = saleProductPlanOrder.LogisticsCompany
			o.LogisticsArea = saleProductPlanOrder.LogisticsArea
			o.ReceiptAddress = saleProductPlanOrder.Location
			o.ReceiptAddressDetail = saleProductPlanOrder.ReceiptAddress
			// SaleShipmentId=saleProductPlanOrder.SaleShipmentId
			o.SaleShipmentName = saleProductPlanOrder.SaleShipmentName
			for _, gfDetail := range data.GFDetailItems {
				item := structure.AddSaleProductOrderDetailParam{}
				// 根据库存id获取回成品的信息和单价
				item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
				item.StockProductId = gfDetail.StockProductId
				item.ProductColorId = gfDetail.ProductColorId
				item.ProductColorKindId = gfDetail.ProductColorKindId
				item.CustomerId = gfDetail.ProductCustomerId
				item.ProductId = gfDetail.ProductId
				item.ProductLevelId = gfDetail.ProductLevelId
				item.ProductKindId = gfDetail.ProductKindId
				item.ProductRemark = gfDetail.MaterialRemark
				item.MeasurementUnitId = gfDetail.MeasurementUnitId
				item.WarehouseId = gfDetail.WarehouseId
				// 取新的显示
				// item.StockRoll = gfDetail.Roll
				item.StockRoll = stockMap[gfDetail.StockProductId].StockRoll - stockMap[gfDetail.StockProductId].BookRoll
				item.StockWeight = gfDetail.AvailableWeight
				item.DyelotNumber = gfDetail.DyelotNumber
				item.IsDisplayPrice = false

				item.SalePrice = gfDetail.UnitPrice
				item.PaperTubeWeight = gfDetail.PaperTubeWeight
				item.SettleWeightError = gfDetail.WeightError
				item.OtherPrice = gfDetail.OtherPrice
				item.BookRoll = gfDetail.EnablePushRoll
				item.Roll = gfDetail.EnablePushRoll
				item.Weight = gfDetail.EnablePushWeight
				item.CustomerAccountNum = gfDetail.AccountNum
				item.Remark = gfDetail.DetailRemark
				item.PlanDetailId = gfDetail.SalePlanOrderItemId
				items = append(items, item)
			}
			o.ItemData = items
			saleServiceSvc := saleSvc.NewSaleProductOrderService(ctx, false)
			var (
				saleOrderId        uint64
				saleOrderNo        string
				stockProductSvc    = productSvc.NewStockProductService()
				arrangeProductSvc  = productSvc.NewFpmArrangeOrderService(nil)
				shortageProductSvc = saleSvc.NewShortageProductOrderService()
				bookItemsReq       productStructure.UpdateStockProductDetailParamList
				arrangeItemsReq    productStructure.AddFpmArrangeOrderParamList
				shortageItemReq    = &structure.AddShortageProductOrderParam{}
				pmcPlanSummaryReq  = &structure.AuditUpdatePushed{}
			)
			saleOrderId, saleOrderNo, err = saleServiceSvc.Add(ctx, tx, o)
			if err != nil {
				return
			}

			// tx.SavePoint("audit_sale_order")
			// 只获取销售单审核后下推信息，不审核  0为正常流程,1为不审核只获取信息,2为只审核
			bookItemsReq, arrangeItemsReq, shortageItemReq, pmcPlanSummaryReq, err = saleServiceSvc.UpdateStatusPass(ctx, tx, &structure.UpdateSaleProductOrderAuditStatusParam{Id: saleOrderId, IsAudit: 1})
			if err != nil {
				return
			}

			if len(bookItemsReq) == 0 {
				return
			}
			// 占用库存
			for _, bookItem := range bookItemsReq {
				bookItem.OrderId = saleOrderId
				bookItem.OrderNo = saleOrderNo
				bookItem.OrderType = common_system.BookOrderTypeProductSalePass
				rLocks, err = stockProductSvc.AddBookProductStock(ctx, tx, rLocks, bookItem)
				if err != nil {
					if data.PushedRoll != 0 || data.PushedWeight != 0 {
						err = nil
						return
					}
					return
				}
			}
			// 只审核，不取信息
			_, _, _, _, err = saleServiceSvc.UpdateStatusPass(ctx, tx, &structure.UpdateSaleProductOrderAuditStatusParam{Id: saleOrderId, IsAudit: 2})
			if err != nil {
				return
			}

			// 反写pmc下推单据汇总
			if pmcPlanSummaryReq != nil {
				pmcPlanSvc := saleSvc.NewPmcGreyPlanOrderService()
				err = pmcPlanSvc.AuditUpdatePushed(ctx, tx, pmcPlanSummaryReq)
				if err != nil {
					return
				}
			}

			// 审核生成配布单(按出库仓库来划分)
			for _, arrangeItem := range arrangeItemsReq {
				if len(arrangeItem.ItemData) > 0 {
					var salePlanOrderItemIds []uint64
					var orderId uint64
					orderId, salePlanOrderItemIds, err = arrangeProductSvc.AddOrder(ctx, tx, &arrangeItem)
					if err != nil {
						return
					}
					// 更新状态
					err = saleSvc.NewSaleProductPlanOrderService(ctx, false).UpdateSituStatus(
						ctx, tx, salePlanOrderItemIds, common_system.SituStatusProductArrangeOrder, true, orderId, "配布单审核")
					if err != nil {
						return
					}
				}
			}

			if shortageItemReq != nil {
				_, err = shortageProductSvc.AddOrder(ctx, tx, shortageItemReq)
				if err != nil {
					return
				}
			}
			// tx.RollbackTo("audit_sale_order")
		}
		// 坯布销售
		if data.PlanType == common.PlanTypeGreyFabric {
			o := &structure_gfm.AddGfmSaleDeliveryOrderParam{}
			var (
				items = make(structure_gfm.AddGfmSaleDeliveryOrderItemParamList, 0)
			)
			o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
			o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
			o.DepartmentId = data.DepartmentId
			o.SaleSystemId = data.SaleSystemId
			o.DeliveryEntityId = data.ShippingUnitId
			o.DeliveryTime = tools.QueryTime(data.DeliveryTime.Format("2006-01-02"))
			o.CustomerId = data.CustomerId
			o.SalesmanId = data.SaleUserId
			o.SalesmanName = data.SaleUserName
			o.Remark = data.Remark
			o.TotalWeight = data.Roll
			o.TotalRoll = data.Weight
			for _, gfDetail := range data.GFDetailItems {
				item := structure_gfm.AddGfmSaleDeliveryOrderItemParam{}
				item.SalePlanOrderItemId = gfDetail.SalePlanOrderItemId
				item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
				item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
				item.CustomerId = gfDetail.ProductCustomerId
				item.NeedleSize = gfDetail.NeedleSize
				item.RawMaterialYarnName = gfDetail.RawMaterialYarnName
				item.RawMaterialBatchNum = gfDetail.RawMaterialBatchNum
				item.RawMaterialBatchBrand = gfDetail.RawMaterialBatchBrand
				item.WeavingProcess = gfDetail.WeavingProcess
				item.GrayFabricColorId = gfDetail.GrayFabricColorId
				item.GreyFabricLevelId = gfDetail.GrayFabricLevelId
				item.Roll = gfDetail.EnablePushRoll
				item.ReturnRoll = 0
				item.SinglePrice = gfDetail.UnitPrice
				item.OtherPrice = gfDetail.OtherPrice
				item.YarnBatch = gfDetail.YarnBatch             // 纱批
				item.MachineNumber = gfDetail.MachineNumber     // 机台号
				item.GreyFabricRemark = gfDetail.MaterialRemark // 坯布备注
				item.TotalPrice = 0
				item.TotalWeight = gfDetail.EnablePushWeight
				item.Remark = gfDetail.DetailRemark
				item.GreyFabricId = gfDetail.GreyFabricId
				item.WarehouseSumId = gfDetail.StockId
				items = append(items, item)
			}
			o.ItemData = items
			gfmServiceSvc := gfmSvc.NewGfmSaleDeliveryOrderService()
			_, err = gfmServiceSvc.Add(ctx, tx, o)
			if err != nil {
				return
			}
		}
		// 原料销售
		if data.PlanType == common.PlanTypeRawMaterial {
			o := &structure_base.AddRawMaterialSaleOrderParams{}
			var (
				items = make([]*structure_base.AddRawMaterialSaleOrderItem, 0)
			)
			o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
			o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
			o.SaleSystemId = data.SaleSystemId
			o.SaleSystemName = data.SaleSystemName
			o.CustomerId = data.CustomerId
			o.CustomerName = data.CustomerName
			o.SaleUnitId = data.ShippingUnitId
			o.SaleDate = time.Now().Format("2006-01-02")
			o.ReceiptPhone = data.CustomerPhone
			o.ReceiptAddress = data.ReceiptAddress
			o.SellerId = data.SaleUserId
			o.SellerName = data.SaleUserName
			o.Remark = data.Remark
			for _, gfDetail := range data.GFDetailItems {
				item := &structure_base.AddRawMaterialSaleOrderItem{}
				item.SalePlanOrderItemId = gfDetail.SalePlanOrderItemId
				item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
				item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
				item.StockItemId = gfDetail.StockId
				item.WholePieceCount = gfDetail.WholePieceCount
				item.BulkPieceCount = gfDetail.BulkPieceCount
				item.TotalWeight = gfDetail.EnablePushWeight
				item.PushWeight = gfDetail.EnablePushWeight
				item.OtherPrice = gfDetail.OtherPrice
				item.UnitPrice = gfDetail.UnitPrice
				item.Remark = gfDetail.DetailRemark
				items = append(items, item)
			}
			o.Items = items
			gfmServiceSvc := raw_material_order.NewRawMaterialSaleOrderService(ctx)
			_, err = gfmServiceSvc.AddOrder(ctx, o)
			if err != nil {
				return
			}
		}
		// continue
	case common.PushTypeProductPurchase:
		// 成品采购
		o := &structure_purchase.AddFinishProductPurchaseOrderParams{}
		var (
			items = make([]*structure_purchase.FabricPurInfo, 0)
		)
		// 获取成品染厂颜色信息
		productColorIds := mysql_base.GetUInt64ListV2("product_color_id", data)
		productColorSvc := product.NewProductColorClient()
		productDyeColors, err = productColorSvc.GetProductDyeColorByDyeFactorIdAndProductColorIds(ctx, data.DyeFactoryId, productColorIds)
		if err != nil {
			return
		}
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.SupplierId = data.ProductSupplierId
		if data.DyeFactoryId != 0 {
			o.ReceiptUnitId = data.DyeFactoryId
		} else {
			o.ReceiptUnitId = data.CustomerId
		}
		o.ReceiptAddress = data.ReceiptAddress
		o.ReceiptPhone = data.CustomerPhone
		o.FapiaoId = 0
		o.Remark = data.Remark
		o.PurchaseDate = time.Now().Format("2006-01-02")
		o.SaleMode = common.BulkTypeOrder
		for i, gfDetail := range data.GFDetailItems {
			if i == 0 {
				if !gfDetail.DeliveryTime.IsZero() {
					o.ReceiptDate = gfDetail.DeliveryTime.Format("2006-01-02")
				}
			}
			item := &structure_purchase.FabricPurInfo{}
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.FinishProductId = gfDetail.ProductId
			item.CustomerId = data.CustomerId
			item.ColorId = gfDetail.ProductColorId
			if productDyeColor, ok := productDyeColors[gfDetail.ProductColorId]; ok {
				item.DyeFactoryColor = productDyeColor.DyeFactoryColorName
				item.DyeFactoryColorCode = productDyeColor.DyeFactoryColorCode
				item.DyeFactoryVatCode = productDyeColor.DyelotNumber
			}
			item.SalePlanOrderItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			item.FinishProductLevelId = gfDetail.ProductLevelId
			item.FinishProductWidth = gfDetail.FinishProductWidth
			item.FinishProductGramWeight = gfDetail.FinishProductGramWeight
			item.PaperTubeWeight = gfDetail.PaperTubeWeight
			item.Remark = gfDetail.DetailRemark
			item.PieceCount = gfDetail.EnablePushRoll
			item.PieceWeight = 0
			item.TotalWeight = gfDetail.EnablePushWeight
			item.UnitId = gfDetail.MeasurementUnitId
			item.AuxiliaryUnitId = gfDetail.MeasurementUnitId
			// item.UnitPrice = gfDetail.UnitPrice
			item.FinishProductWidthUnitId = gfDetail.FinishProductWidthUnitId
			item.FinishProductGramWeightUnitId = gfDetail.FinishProductGramWeightUnitId
			item.LowerLimit = gfDetail.LowerLimit
			item.UpperLimit = gfDetail.UpperLimit
			items = append(items, item)
		}
		o.Items = items
		productPurchaseSvc := finish_product_order.NewFinishProductPurchaseOrderService(ctx)
		_, err = productPurchaseSvc.AddOrder(ctx, o)
		if err != nil {
			return
		}
		// continue
	case common.PushTypeDNFNoticeOrder:
		// 坯布染整
		o := &structure_dnf.AddDyeingAndFinishingNoticeOrderParams{}
		var (
			items = make([]*structure_dnf.DyeingAndFinishingNoticeOrderItem, 0)
		)
		// 获取成品染厂颜色信息
		productColorIds := mysql_base.GetUInt64ListV2("product_color_id", data)
		productColorSvc := product.NewProductColorClient()
		productDyeColors, err = productColorSvc.GetProductDyeColorByDyeFactorIdAndProductColorIds(ctx, data.DyeFactoryId, productColorIds)
		if err != nil {
			return
		}
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.DyeFactoryId = data.DyeFactoryId
		o.OrderFollowerId = bizUnits[data.DyeFactoryId].OrderFollowerID
		o.OrderFollowerName = bizUnits[data.DyeFactoryId].OrderFollowerName
		o.OrderFollowerPhone = bizUnits[data.DyeFactoryId].OrderFollowerPhone
		o.Remark = data.Remark
		o.ReturnAddress = ""
		o.DnfDate = time.Now().Format("2006-01-02")
		o.DnfType = common_dnf.DnfTypeGreyDnf
		for _, gfDetail := range data.GFDetailItems {
			item := &structure_dnf.DyeingAndFinishingNoticeOrderItem{}
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.FinishProductId = gfDetail.ProductId
			item.SalePlanOrderNo = data.SaleProductPlanOrderNo
			item.SalePlanItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			item.CustomerId = data.CustomerId
			item.StyleNo = gfDetail.AccountNum
			item.ProductColorId = gfDetail.ProductColorId
			// 染厂颜色
			if productDyeColor, ok := productDyeColors[gfDetail.ProductColorId]; ok {
				item.ColorId = productDyeColor.Id
				item.Dyelot = productDyeColor.DyelotNumber
				item.DnfCraft = productDyeColor.DyeCraft
				item.DnfCraftIds = productDyeColor.DyeCraftIds
				item.PaperTubeIds = productDyeColor.PaperTubeSpecsIds
				item.PlasticsBagIds = productDyeColor.TapeSpecsIds
			}
			if product, ok := products[gfDetail.ProductId]; ok {
				item.Craft = product.FinishProductCraft
				item.HandFeeling = product.TouchStyle
				item.DnfLoss = product.DyeingLoss
			}
			item.ShrinkageRate = 0
			item.LevelId = 0
			item.IncreaseWeight = 0
			item.Width = gfDetail.FinishProductWidth
			item.WidthUnitId = gfDetail.FinishProductWidthUnitId
			item.GramWeight = gfDetail.FinishProductGramWeight
			item.GramWeightUnitID = gfDetail.FinishProductGramWeightUnitId
			item.PieceCount = gfDetail.EnablePushRoll
			item.Weight = gfDetail.EnablePushWeight
			item.LevelId = gfDetail.ProductLevelId
			if !gfDetail.DeliveryTime.IsZero() {
				item.DeliveryDate = gfDetail.DeliveryTime.Format("2006-01-02")
			}
			item.Remark = gfDetail.DetailRemark
			items = append(items, item)
		}
		o.Items = items
		dyeingAndFinishingSvc := dnfSvc.NewDyeingAndFinishingNoticeOrderService(ctx)
		_, err = dyeingAndFinishingSvc.AddOrder(ctx, tx, o)
		if err != nil {
			return
		}
	// continue
	case common.PushTypeProductFinishing:
		// 成品加工
		o := &structure_dnf.AddDyeingAndFinishingNoticeOrderParams{}
		var (
			items = make([]*structure_dnf.DyeingAndFinishingNoticeOrderItem, 0)
		)
		// 获取成品染厂颜色信息
		productColorIds := mysql_base.GetUInt64ListV2("product_color_id", data)
		productColorSvc := product.NewProductColorClient()
		productDyeColors, err = productColorSvc.GetProductDyeColorByDyeFactorIdAndProductColorIds(ctx, data.DyeFactoryId, productColorIds)
		if err != nil {
			return
		}
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.DyeFactoryId = data.DyeFactoryId
		o.OrderFollowerId = bizUnits[data.DyeFactoryId].OrderFollowerID
		o.OrderFollowerName = bizUnits[data.DyeFactoryId].OrderFollowerName
		o.OrderFollowerPhone = bizUnits[data.DyeFactoryId].OrderFollowerPhone
		o.Remark = data.Remark
		o.ReturnAddress = ""
		o.DnfDate = time.Now().Format("2006-01-02")
		o.DnfType = common_dnf.DnfTypeProductProcessing
		for _, gfDetail := range data.GFDetailItems {
			item := &structure_dnf.DyeingAndFinishingNoticeOrderItem{}
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.FinishProductId = gfDetail.ProductId
			item.SalePlanOrderNo = data.SaleProductPlanOrderNo
			item.SalePlanItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			item.CustomerId = data.CustomerId
			item.StyleNo = gfDetail.AccountNum
			item.ProductColorId = gfDetail.ProductColorId
			// 染厂颜色
			if productDyeColor, ok := productDyeColors[gfDetail.ProductColorId]; ok {
				item.ColorId = productDyeColor.Id
				item.Dyelot = productDyeColor.DyelotNumber
				item.DnfCraft = productDyeColor.DyeCraft
				item.DnfCraftIds = productDyeColor.DyeCraftIds
				item.PaperTubeIds = productDyeColor.PaperTubeSpecsIds
				item.PlasticsBagIds = productDyeColor.TapeSpecsIds
			}
			if product, ok := products[gfDetail.ProductId]; ok {
				item.Craft = product.FinishProductCraft
				item.HandFeeling = product.TouchStyle
				item.DnfLoss = product.DyeingLoss
			}
			item.ShrinkageRate = 0
			item.LevelId = 0
			item.IncreaseWeight = 0
			item.Width = gfDetail.FinishProductWidth
			item.WidthUnitId = gfDetail.FinishProductWidthUnitId
			item.GramWeight = gfDetail.FinishProductGramWeight
			item.GramWeightUnitID = gfDetail.FinishProductGramWeightUnitId
			item.PieceCount = gfDetail.EnablePushRoll
			item.Weight = gfDetail.EnablePushWeight
			item.LevelId = gfDetail.ProductLevelId
			if !gfDetail.DeliveryTime.IsZero() {
				item.DeliveryDate = gfDetail.DeliveryTime.Format("2006-01-02")
			}
			item.Remark = gfDetail.DetailRemark
			items = append(items, item)
		}
		o.Items = items
		dyeingAndFinishingSvc := dnfSvc.NewDyeingAndFinishingNoticeOrderService(ctx)
		_, err = dyeingAndFinishingSvc.AddOrder(ctx, tx, o)
		if err != nil {
			return
		}
	case common.PushTypeProductRedye:
		// 成品回修
		o := &structure_dnf.AddDyeingAndFinishingNoticeOrderParams{}
		var (
			items = make([]*structure_dnf.DyeingAndFinishingNoticeOrderItem, 0)
		)
		// 获取成品染厂颜色信息
		productColorIds := mysql_base.GetUInt64ListV2("product_color_id", data)
		productColorSvc := product.NewProductColorClient()
		productDyeColors, err = productColorSvc.GetProductDyeColorByDyeFactorIdAndProductColorIds(ctx, data.DyeFactoryId, productColorIds)
		if err != nil {
			return
		}
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.DyeFactoryId = data.DyeFactoryId
		o.OrderFollowerId = bizUnits[data.DyeFactoryId].OrderFollowerID
		o.OrderFollowerName = bizUnits[data.DyeFactoryId].OrderFollowerName
		o.OrderFollowerPhone = bizUnits[data.DyeFactoryId].OrderFollowerPhone
		o.Remark = data.Remark
		o.ReturnAddress = ""
		o.DnfDate = time.Now().Format("2006-01-02")
		o.DnfType = common_dnf.DnfTypeProductRepair
		for _, gfDetail := range data.GFDetailItems {
			item := &structure_dnf.DyeingAndFinishingNoticeOrderItem{}
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.FinishProductId = gfDetail.ProductId
			item.SalePlanOrderNo = data.SaleProductPlanOrderNo
			item.SalePlanItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			item.CustomerId = data.CustomerId
			item.StyleNo = gfDetail.AccountNum
			item.ProductColorId = gfDetail.ProductColorId
			// 染厂颜色
			if productDyeColor, ok := productDyeColors[gfDetail.ProductColorId]; ok {
				item.ColorId = productDyeColor.Id
				item.Dyelot = productDyeColor.DyelotNumber
				item.DnfCraft = productDyeColor.DyeCraft
				item.DnfCraftIds = productDyeColor.DyeCraftIds
				item.PaperTubeIds = productDyeColor.PaperTubeSpecsIds
				item.PlasticsBagIds = productDyeColor.TapeSpecsIds
			}
			if product, ok := products[gfDetail.ProductId]; ok {
				item.Craft = product.FinishProductCraft
				item.HandFeeling = product.TouchStyle
				item.DnfLoss = product.DyeingLoss
			}
			item.ShrinkageRate = 0
			item.LevelId = 0
			item.IncreaseWeight = 0
			item.Width = gfDetail.FinishProductWidth
			item.WidthUnitId = gfDetail.FinishProductWidthUnitId
			item.GramWeight = gfDetail.FinishProductGramWeight
			item.GramWeightUnitID = gfDetail.FinishProductGramWeightUnitId
			item.PieceCount = gfDetail.EnablePushRoll
			item.Weight = gfDetail.EnablePushWeight
			item.LevelId = gfDetail.ProductLevelId
			if !gfDetail.DeliveryTime.IsZero() {
				item.DeliveryDate = gfDetail.DeliveryTime.Format("2006-01-02")
			}
			item.Remark = gfDetail.DetailRemark
			items = append(items, item)
		}
		o.Items = items
		dyeingAndFinishingSvc := dnfSvc.NewDyeingAndFinishingNoticeOrderService(ctx)
		_, err = dyeingAndFinishingSvc.AddOrder(ctx, tx, o)
		if err != nil {
			return
		}
	case common.PushTypeGreyPurchase:
		// 坯布采购
		o := &structure_purchase.AddPurchaseGreyFabricParam{}
		var (
			items = make([]structure_purchase.AddPurchaseGreyFabricItemParam, 0)
		)
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.SupplierId = data.GreyFabricSupplierId
		if data.DyeFactoryId != 0 {
			o.RecipienEntityId = data.DyeFactoryId
			o.RecipienEntityType = 1
		} else {
			o.RecipienEntityId = data.WeaveFactoryId
			o.RecipienEntityType = 1
		}
		o.InvoiceHeaderId = 0
		o.Remark = data.Remark
		o.PurchaseTime = tools.QueryTime(time.Now().Format("2006-01-02"))
		for i, gfDetail := range data.GFDetailItems {
			if i == 0 {
				if !gfDetail.DeliveryTime.IsZero() {
					o.ReceivingTime = tools.QueryTime(gfDetail.DeliveryTime.Format("2006-01-02"))
				}
			}
			var (
				itemAddrs = make([]structure_purchase.AddPurchaseGreyFabricItemAddrParam, 0)
			)
			item := structure_purchase.AddPurchaseGreyFabricItemParam{}
			item.WeaveFactoryNameId = gfDetail.WeaveFactoryId
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.GreyFabricId = gfDetail.GreyFabricId
			if greyFabricItem, ok := greyFabricItems[gfDetail.GreyFabricId]; ok {
				item.GreyFabricCode = greyFabricItem.Code
				item.GreyFabricName = greyFabricItem.Name
				item.GreyFabricWidth = greyFabricItem.GreyFabricWidth
				item.GreyFabricGramWeight = greyFabricItem.GreyFabricGramWeight
				item.GreyFabricWidthUnitId = greyFabricItem.GreyFabricWidthUnitId
				item.GreyFabricGramWeightUnitId = greyFabricItem.GreyFabricGramWeightUnitId
				item.NeedleSize = greyFabricItem.NeedleSize
				item.TotalNeedleSize = greyFabricItem.TotalNeedleSize
				item.WeavingProcess = greyFabricItem.WeavingProcess
			}
			item.GrayFabricColorId = gfDetail.GrayFabricColorId
			item.GreyFabricLevelId = gfDetail.GrayFabricLevelId
			item.CustomerId = data.CustomerId
			item.SalePlanOrderItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo

			item.YarnBatch = ""
			item.MachineCombinationNumber = ""
			item.WeaveFactoryNameId = gfDetail.WeaveFactoryId
			item.Remark = gfDetail.DetailRemark
			item.Number = gfDetail.EnablePushRoll
			item.AverageWeight = tools.IntRoundHalf2One(tools.DecimalDiv(float64(gfDetail.EnablePushWeight), tools.DecimalDiv(float64(gfDetail.EnablePushRoll), 100)))
			item.TotalWeight = gfDetail.EnablePushWeight
			item.SinglePrice = 0
			if gfDetail.DyeFactoryId != 0 {
				itemAddr := structure_purchase.AddPurchaseGreyFabricItemAddrParam{}
				itemAddr.Number = gfDetail.EnablePushRoll
				itemAddr.RecipientEntityType = 1
				itemAddr.RecipientEntityID = gfDetail.DyeFactoryId
				itemAddr.TotalWeight = gfDetail.Weight
				itemAddr.GreyFabricItemId = gfDetail.GreyFabricId
				itemAddr.Contacts = bizUnits[gfDetail.DyeFactoryId].ContactName
				itemAddr.ContactPhone = bizUnits[gfDetail.DyeFactoryId].Phone
				itemAddr.Addr = bizUnits[gfDetail.DyeFactoryId].Address
				itemAddrs = append(itemAddrs, itemAddr)
				item.ItemParamAddrs = itemAddrs
			}
			// item.SinglePrice = gfDetail.UnitPrice
			if gfDetail.PlanType == common.PlanTypeGreyFabric {
				item.NeedleSize = gfDetail.NeedleSize
				item.TotalNeedleSize = gfDetail.TotalNeedleSize
				item.GreyFabricWidth = gfDetail.GreyFabricWidth
				item.GreyFabricGramWeight = gfDetail.GreyFabricGramWeight
				item.GreyFabricWidthUnitId = gfDetail.GreyFabricWidthUnitId
				item.GreyFabricGramWeightUnitId = gfDetail.GreyFabricGramWeightUnitId
			}
			items = append(items, item)
		}
		o.ItemParams = items
		greyFabricPurchaseSvc := purchaseSvc.NewPurchaseGreyFabricService()
		_, err = greyFabricPurchaseSvc.Add(ctx, o)
		if err != nil {
			return
		}
		// continue
	case common.PushTypeProductionPlanOrder:
		// 坯布织造
		for _, gfDetail := range data.GFDetailItems {
			o := &structure_produce.AddProductionNotifyOrderParam{}
			var (
				greyItems = make([]structure_produce.UpdateProductionNotifyGreyFabricDetailParam, 0)
				rawItems  = make([]structure_produce.UpdateProductionNotifyMaterialRatioParam, 0)
			)
			o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
			o.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
			o.SaleSystemId = data.SaleSystemId
			o.NotifyDate = tools.QueryTime(time.Now().Format("2006-01-02"))
			o.WeavingMillId = data.WeaveFactoryId
			o.WeavingMillOrderFollowerId = bizUnits[data.WeaveFactoryId].OrderFollowerID
			o.WeavingMillOrderFollowerPhone = bizUnits[data.WeaveFactoryId].OrderFollowerPhone
			o.CustomerId = data.CustomerId
			o.CustomerOrderFollowerId = bizUnits[data.WeaveFactoryId].OrderFollowerID
			o.SaleUserId = bizUnits[data.CustomerId].SaleUserId
			o.OrderQcUserId = bizUnits[data.WeaveFactoryId].OrderQcUserId
			o.PaymentTerm = 0
			o.ReceiptGreyFabricAddress = bizUnits[data.WeaveFactoryId].Address
			o.InvoiceHeader = 0
			o.OrderRemark = data.Remark
			o.GreyFabricId = gfDetail.GreyFabricId
			if greyFabricItem, ok := greyFabricItems[gfDetail.GreyFabricId]; ok {
				o.NeedleSize = greyFabricItem.NeedleSize
				o.TotalNeedleSize = greyFabricItem.TotalNeedleSize
				o.WeightOfFabric = greyFabricItem.WeightOfFabric
				o.GreyFabricWidth = greyFabricItem.GreyFabricWidth
				o.GreyFabricGramWeight = greyFabricItem.GreyFabricGramWeight
				o.GreyFabricWidthUnitId = greyFabricItem.GreyFabricWidthUnitId
				o.GreyFabricGramWeightUnitId = greyFabricItem.GreyFabricGramWeightUnitId
				o.WeavingLoss = greyFabricItem.WeavingLoss
				o.LoomModelId = greyFabricItem.LoomModelId
				o.UpperNeedle = greyFabricItem.UpperNeedle
				o.LowerNeedle = greyFabricItem.LowerNeedle
				o.YarnArrange = greyFabricItem.YarnArrange
				o.YarnLength = greyFabricItem.YarnLength
				o.WarpDensity = greyFabricItem.WarpDensity
				o.WeftDensity = greyFabricItem.WeftDensity
				o.ReedInnerWidth = greyFabricItem.ReedInnerWidth
				o.ReedOuterWidth = greyFabricItem.ReedOuterWidth
				o.ReedTotalWidth = o.ReedInnerWidth + o.ReedOuterWidth
				o.ReedNo = greyFabricItem.ReedNo
				o.PenetrationNumber = greyFabricItem.PenetrationNumber
				o.UpperWeftDensity = greyFabricItem.UpperWeftDensity
				o.LowerWeftDensity = greyFabricItem.LowerWeftDensity
				o.GfTheoryGramWidth = greyFabricItem.GfTheoryGramWidth
				o.TotalWarpPieces = greyFabricItem.TotalWarpPieces
				o.WarpArrangement = greyFabricItem.WarpArrangement
				o.WeftArrangement = greyFabricItem.WeftArrangement
				o.WeavingSpecificationsIds = tools.QueryIntList(tools.KeyEncode(",", tools.UInt64Arr2StringArr(greyFabricItem.WeaveSpecIds)...))
				o.WeavingSpecificationsName = tools.QueryStringList(greyFabricItem.WeaveSpecStr)
				var (
					warpItems = make(structure_produce.AddProductionNotifyOrderGreyFabricWarpWeftParamList, 0)
					weftItems = make(structure_produce.AddProductionNotifyOrderGreyFabricWarpWeftParamList, 0)
				)
				for _, warpData := range greyFabricItem.WarpDatas {
					warpItem := structure_produce.AddProductionNotifyOrderGreyFabricWarpWeftParam{}
					warpItem.CommonGreyFabricInfoWarpWeft = warpData.CommonGreyFabricInfoWarpWeft
					warpItems = append(warpItems, warpItem)
					o.WarpDatas = warpItems
				}
				for _, weftData := range greyFabricItem.WeftDatas {
					weftItem := structure_produce.AddProductionNotifyOrderGreyFabricWarpWeftParam{}
					weftItem.CommonGreyFabricInfoWarpWeft = weftData.CommonGreyFabricInfoWarpWeft
					weftItems = append(weftItems, weftItem)
					o.WeftDatas = weftItems
				}
			}
			if gfDetail.PlanType == common.PlanTypeGreyFabric {
				o.NeedleSize = gfDetail.NeedleSize
				o.TotalNeedleSize = gfDetail.TotalNeedleSize
				o.WeightOfFabric = gfDetail.WeightOfFabric
				o.GreyFabricWidth = gfDetail.GreyFabricWidth
				o.GreyFabricGramWeight = gfDetail.GreyFabricGramWeight
				o.GreyFabricWidthUnitId = gfDetail.GreyFabricWidthUnitId
				o.GreyFabricGramWeightUnitId = gfDetail.GreyFabricGramWeightUnitId
			}
			o.FinishProductWidth = gfDetail.FinishProductWidth
			o.FinishProductGramWeight = gfDetail.FinishProductGramWeight
			o.FinishProductWidthUnitId = gfDetail.FinishProductWidthUnitId
			o.FinishProductGramWeightUnitId = gfDetail.FinishProductGramWeightUnitId
			o.GreyFabricColorId = gfDetail.GrayFabricColorId
			o.ProcessPrice = 0
			o.SchedulingRoll = gfDetail.EnablePushRoll
			o.SchedulingWeight = gfDetail.EnablePushWeight
			o.GreyFabricRemark = gfDetail.DetailRemark

			o.LoomBrand = ""
			o.LoomModelName = ""
			o.MachinesNum = 0
			o.YarnBatch = ""
			o.PackagingRequire = ""
			o.IsAuto = true
			if !gfDetail.DeliveryTime.IsZero() {
				o.ReceiptGreyFabricDate = tools.QueryTime(gfDetail.DeliveryTime.Format("2006-01-02"))
			}
			greyItem := structure_produce.UpdateProductionNotifyGreyFabricDetailParam{}
			var salePlanOrderGfDetails map[uint64]sale.SalePlanOrderGfDetailRes
			// 销售计划单坯布信息
			salePbSvc := sale.NewSalePlanClient(ctx)
			salePlanOrderGfDetails, err = salePbSvc.GetSalePlanOrderGfDetailItemByGreyFabricIds(ctx, data.SaleProductPlanOrderId, []uint64{gfDetail.GreyFabricId})
			if err != nil {
				return
			}
			if salePlanOrderGfDetail, ok := salePlanOrderGfDetails[gfDetail.GreyFabricId]; ok {
				greyItem.SalePlanOrderGfDetailId = salePlanOrderGfDetail.SalePlanOrderGfDetailId
				greyItem.SalePlanOrderId = salePlanOrderGfDetail.SalePlanOrderId
				greyItem.SalePlanOrderNo = salePlanOrderGfDetail.SalePlanOrderNo
				greyItem.OrderTime = tools.QueryTime(salePlanOrderGfDetail.OrderTime.Format("2006-01-02"))
				greyItem.CreateTime = tools.QueryTime(salePlanOrderGfDetail.CreateTime.Format("2006-01-02"))
				greyItem.Weight = salePlanOrderGfDetail.Weight
				greyItem.Roll = salePlanOrderGfDetail.Roll
				greyItem.PlanedRoll = salePlanOrderGfDetail.PlanedRoll
				greyItem.PlanedWeight = salePlanOrderGfDetail.PlanedWeight
				greyItem.SchedulingRoll = salePlanOrderGfDetail.SchedulingRoll
				greyItem.SchedulingWeight = salePlanOrderGfDetail.SchedulingWeight
				greyItem.ProducedRoll = salePlanOrderGfDetail.ProducedRoll
				greyItem.ProducedWeight = salePlanOrderGfDetail.ProducedWeight
				greyItem.UseStockRoll = salePlanOrderGfDetail.UseStockRoll
				greyItem.CanSchedulingRoll = salePlanOrderGfDetail.CanSchedulingRoll
			}
			greyItem.GreyFabricId = gfDetail.GreyFabricId
			greyItem.GreyFabricColorId = gfDetail.GrayFabricColorId
			greyItem.SalePlanOrderItemId = gfDetail.SalePlanOrderItemId
			greyItem.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			greyItems = append(greyItems, greyItem)
			for _, greyFabricRawItem := range greyFabricRawItems[gfDetail.GreyFabricId] {
				rawItem := structure_produce.UpdateProductionNotifyMaterialRatioParam{}
				rawItem.RawMaterialId = greyFabricRawItem.RawMaterialId
				rawItem.WeavingCategory = ""
				rawItem.RawMaterialBrand = greyFabricRawItem.Brand
				rawItem.RawMaterialBatchNumber = greyFabricRawItem.BatchNum
				rawItem.SupplierId = 0
				rawItem.MillPrivateYarn = false
				rawItem.YarnRatio = greyFabricRawItem.MaterialRatio
				rawItem.YarnLoss = greyFabricRawItem.MaterialLoss
				// 数量乘用料比例乘（100+用料损耗）用料比例10000用料损耗10000
				rawItem.UseYarnQuantity = tools.HandlerProcessing(gfDetail.Weight*greyFabricRawItem.MaterialRatio*(10000+greyFabricRawItem.MaterialLoss), 100000000)
				rawItem.SendYarnQuantity = rawItem.UseYarnQuantity
				rawItem.Remark = ""
				rawItem.ColorScheme = greyFabricRawItem.Color
				rawItems = append(rawItems, rawItem)
			}
			o.ProductionNotifyGreyFabricDetail = greyItems
			o.ProductionNotifyMaterialRatio = rawItems
			greyFabricProduceSvc := produceSvc.NewProductionNotifyOrderService()
			_, err = greyFabricProduceSvc.Add(ctx, o)
			if err != nil {
				return
			}
		}
		// continue
	case common.PushTypeRMProcess:
		// 原料加工
		o := &structure_rmd.AddRmmDyeOrderParam{}
		var (
			items                = make(structure_rmd.AddRmmDyeOrderItemParamList, 0)
			rawMaterialDyeColors map[uint64]*raw_material.ColorInfoItem
			rawColorIds          = set.NewUint64Set()
		)

		for _, item := range data.GFDetailItems {
			rawColorIds.Add(item.RawMaterialColorId)
		}
		productColorSvc := raw_material.NewRawMaterialColorClient()
		rawMaterialDyeColors, err = productColorSvc.GetFactoryColorByDyeFactorIdAndColorIds(ctx, data.DyeRawMaterialFactoryId, rawColorIds.List())
		if err != nil {
			return
		}
		// 获取染厂信息
		bizMap, _ := biz_unit.NewClientBizUnitService().QueryBizUnitListById(ctx, []uint64{data.DyeFactoryId})
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.DyeUnitId = data.DyeRawMaterialFactoryId
		o.DyeUnitFollowerId = bizUnits[data.DyeRawMaterialFactoryId].OrderFollowerID
		o.DyeUnitFollowerPhone = bizUnits[data.DyeRawMaterialFactoryId].OrderFollowerPhone
		o.Remark = data.Remark
		o.ReceiveUnitId = data.WeaveFactoryId
		o.ReceiveAddress = bizUnits[data.WeaveFactoryId].Address
		o.DyeDate = tools.QueryTime(time.Now().Format("2006-01-02"))
		o.DyeType = cus_const.DyeTypeRaw
		o.OrderType = cus_const.DyeOrderTypeDye
		if val, ok := bizMap[data.DyeFactoryId]; ok {
			fmt.Println(val)
		}
		for _, gfDetail := range data.GFDetailItems {
			item := &structure_rmd.AddRmmDyeOrderItemParam{}
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.SrcId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.SalePlanOrderId = data.SaleProductPlanOrderId
			item.SalePlanOrderNo = data.SaleProductPlanOrderNo
			item.SalePlanItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			item.RawMaterialId = gfDetail.RawMaterialId
			item.CustomerId = data.CustomerId
			item.RawMaterialColorId = gfDetail.RawMaterialColorId
			// 染厂颜色
			if rawMaterialDyeColor, ok := rawMaterialDyeColors[gfDetail.RawMaterialColorId]; ok {
				item.DyeUnitColorId = rawMaterialDyeColor.Id
				item.RawMaterialLevelId = rawMaterialDyeColor.LevelId
				item.DyeingLoss = rawMaterialDyeColor.Wastage
			}
			item.Weight = gfDetail.EnablePushWeight
			if !gfDetail.DeliveryTime.IsZero() {
				item.DeliveryDate = tools.QueryTime(gfDetail.DeliveryTime.Format("2006-01-02"))
			}
			item.Remark = gfDetail.DetailRemark
			items = append(items, item)
		}
		o.ItemData = items
		dyeingAndFinishingSvc := rmdSvc.NewRmmDyeNotifyOrderService()
		_, err = dyeingAndFinishingSvc.Add(ctx, tx, o)
		if err != nil {
			return
		}
	case common.PushTypeRMPurchase:
		// 原料采购
		o := &structure_base.AddRawMaterialPurchaseOrderParams{}
		var (
			items = make([]*structure_base.AddRawMaterialPurchaseOrderItem, 0)
		)
		o.PmcGreyPlanOrderSummaryId = data.PmcGreyPlanOrderSummaryId
		o.PmcGreyPlanOrderId = data.PmcGreyPlanOrderId
		o.SaleSystemId = data.SaleSystemId
		o.SupplierId = data.RawMaterialSupplierId
		if data.DyeRawMaterialFactoryId != 0 {
			o.ReceiptUnitId = data.DyeRawMaterialFactoryId
		} else {
			o.ReceiptUnitId = data.WeaveFactoryId
		}
		o.FapiaoTitleId = 0
		o.Remark = data.Remark
		o.PurchaseDate = time.Now().Format("2006-01-02")
		for i, gfDetail := range data.GFDetailItems {
			if i == 0 {
				if !gfDetail.DeliveryTime.IsZero() {
					o.ReceiptDate = gfDetail.DeliveryTime.Format("2006-01-02")
				}
			}
			var (
				itemAddrs = make([]*structure_base.RawMaterialPurchaseOrderItemLogistics, 0)
			)
			item := &structure_base.AddRawMaterialPurchaseOrderItem{}
			item.PmcGreyPlanOrderSummaryDetailId = gfDetail.PmcGreyPlanOrderSummaryDetailId
			item.RawMaterialId = gfDetail.RawMaterialId
			if rawMaterial, ok := rawMaterials[gfDetail.RawMaterialId]; ok {
				item.Craft = rawMaterial.Craft
				item.MeasurementUnitId = rawMaterial.UnitID
			}
			item.CustomerId = data.CustomerId
			item.Brand = ""
			item.ColorScheme = gfDetail.RawMaterialColorName
			if !gfDetail.DeliveryTime.IsZero() {
				item.ProductionDate = gfDetail.DeliveryTime.Format("2006-01-02")
			}
			item.BlankFabricId = gfDetail.GreyFabricId
			if greyFabricItem, ok := greyFabricItems[gfDetail.GreyFabricId]; ok {
				item.BlankFabricCode = greyFabricItem.Code
				item.BlankFabricName = greyFabricItem.Name
			}
			item.ProductionOrderNum = ""
			item.TaxIncluded = 0
			item.PackagePrice = 0
			item.SpinningType = ""
			item.Remark = gfDetail.DetailRemark
			item.WholePieceCount = gfDetail.WholePieceCount
			item.BulkPieceCount = gfDetail.BulkPieceCount
			item.BulkWeight = gfDetail.EnablePushWeight
			item.PushWeight = gfDetail.EnablePushWeight
			item.WholeWeight = gfDetail.EnablePushWeight
			item.TotalWeight = gfDetail.EnablePushWeight
			// item.UnitPrice = gfDetail.UnitPrice
			item.SalePlanOrderItemId = gfDetail.SalePlanOrderItemId
			item.SalePlanOrderItemNo = gfDetail.SalePlanOrderItemNo
			if o.ReceiptUnitId != 0 {
				itemAddr := &structure_base.RawMaterialPurchaseOrderItemLogistics{}
				itemAddr.WholePieceCount = gfDetail.WholePieceCount
				itemAddr.BulkPieceCount = gfDetail.BulkPieceCount
				itemAddr.BulkWeight = gfDetail.EnablePushWeight
				itemAddr.Weight = gfDetail.EnablePushWeight
				itemAddr.ReceiptUnitId = o.ReceiptUnitId
				itemAddr.ReceiptUnitName = bizUnits[o.ReceiptUnitId].Name
				itemAddr.ReceiptPerson = bizUnits[o.ReceiptUnitId].ContactName
				itemAddr.ReceiptPhone = bizUnits[o.ReceiptUnitId].Phone
				itemAddr.ReceiptAddress = bizUnits[o.ReceiptUnitId].Address
				itemAddrs = append(itemAddrs, itemAddr)
				item.Logistics = itemAddrs
			}
			items = append(items, item)
		}
		o.Items = items
		rawMaterialPurchase := raw_material_order.NewRawMaterialPurchaseOrderService(ctx)
		_, err = rawMaterialPurchase.AddOrder(ctx, o)
		if err != nil {
			return
		}
		// continue
	default:

	}
	// }
	// }
	return
}
