package product

import (
	"context"
	"fmt"
	cus_const "hcscm/common/product"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/vars"
	"time"

	"gorm.io/gorm"

	"hcscm/common/errors"
)

func GetFpmInOrderItemFcIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_in_order_item_fc_id")
}

type FpmInOrderItemFcList []FpmInOrderItemFc

func (r FpmInOrderItemFcList) List() []FpmInOrderItemFc {
	return r
}

func (r FpmInOrderItemFcList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmInOrderItemFcList) GetTotalRWL() []int {
	var (
		tr int
		tw int
		tl int
		o  = make([]int, 3)
	)
	for _, t := range r {
		tr += t.Roll
		tw += t.BaseUnitWeight
		tl += t.Length
	}
	o[0] = tr
	o[1] = tw
	o[2] = tl
	return o
}

func (r FpmInOrderItemFcList) One() FpmInOrderItemFc {
	return r[0]
}

func (r FpmInOrderItemFcList) Pick(id uint64) (o FpmInOrderItemFc) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r FpmInOrderItemFcList) PickBySrcId(id uint64) (o FpmInOrderItemFc) {
	for _, t := range r {
		if t.SrcId == id {
			return t
		}
	}
	return
}

func (r FpmInOrderItemFcList) PickFcListByParentId(pid uint64) (o FpmInOrderItemFcList) {
	list := make(FpmInOrderItemFcList, 0)
	for _, t := range r {
		if t.ParentId == pid {
			list = append(list, t)
		}
	}
	return list
}

func (r FpmInOrderItemFcList) PickByStockDetailId(pid uint64) (o FpmInOrderItemFcList) {
	list := make(FpmInOrderItemFcList, 0)
	for _, t := range r {
		if t.StockId == pid {
			list = append(list, t)
		}
	}
	return list
}

// FpmInOrderItemFc 进仓细码
type FpmInOrderItemFc struct {
	mysql_base.ModelHard
	Id                            uint64                        `gorm:"column:id;primaryKey"`
	ParentId                      uint64                        `gorm:"column:parent_id"  relate:"parent_id"`                                    // 父id（成品信息行id）
	SrcId                         uint64                        `gorm:"column:src_id"`                                                           // 来源id
	WarehouseInType               cus_const.WarehouseGoodInType `gorm:"column:warehouse_in_type"`                                                // 来源类型
	WarehouseInOrderId            uint64                        `gorm:"column:warehouse_in_order_id"`                                            // 进仓单id
	WarehouseInOrderNo            string                        `gorm:"column:warehouse_in_order_no"`                                            // 进仓单号
	Roll                          int                           `gorm:"column:roll"`                                                             // 条数(条)，乘100存
	WarehouseId                   uint64                        `gorm:"column:warehouse_id"   relate:"warehouse_id"`                             // 仓库id
	WarehouseBinId                uint64                        `gorm:"column:warehouse_bin_id"   relate:"warehouse_bin_id"`                     // 仓位
	VolumeNumber                  int                           `gorm:"column:volume_number"`                                                    // 卷号
	BaseUnitWeight                int                           `gorm:"column:base_unit_weight"`                                                 // 数量(公斤)，乘10000存
	WeightError                   int                           `gorm:"column:weight_error"`                                                     // 空差数量(公斤)，乘10000存
	UnitId                        uint64                        `gorm:"column:unit_id"  relate:"measurement_unit_id"`                            // 单位id（kg）
	StockId                       uint64                        `gorm:"column:stock_id"  relate:"stock_id"`                                      // 库存成品id
	StockRemark                   string                        `gorm:"column:stock_remark"`                                                     // 库存备注 todo:重复字段，待去除
	SumStockId                    uint64                        `gorm:"column:sum_stock_id"`                                                     // 汇总库存id
	ReturnStockId                 uint64                        `gorm:"column:return_stock_id"  relate:"stock_id"`                               // 退货库存成品id
	IsUpdateStock                 bool                          `gorm:"column:is_update_stock"`                                                  // 是否是新增库存（新增库存消审时需要把新增的库存删除）
	PaperTubeWeight               int                           `gorm:"column:paper_tube_weight"`                                                // 纸筒数量(公斤)，乘10000存
	ActuallyWeight                int                           `gorm:"column:actually_weight"`                                                  // 码单数量
	SettleErrorWeight             int                           `gorm:"column:settle_error_weight"`                                              // 结算空差数量
	SettleWeight                  int                           `gorm:"column:settle_weight"`                                                    // 结算数量
	Length                        int                           `gorm:"column:length"`                                                           // 长度，乘100存
	Remark                        string                        `gorm:"column:remark"`                                                           // 备注
	DigitalCode                   string                        `gorm:"column:digital_code"`                                                     // 数字码
	ShelfNo                       string                        `gorm:"column:shelf_no"`                                                         // 货架号
	AccountNum                    string                        `gorm:"column:account_num"`                                                      // 款号
	DyeFactoryColorCode           string                        `gorm:"column:dye_factory_color_code"`                                           // 染厂色号
	DyeFactoryDyelotNumber        string                        `gorm:"column:dye_factory_dyelot_number"`                                        // 染厂缸号
	ProductWidth                  string                        `gorm:"column:product_width"`                                                    // 成品幅宽
	ProductGramWeight             string                        `gorm:"column:product_gram_weight"`                                              // 成品克重
	ContractNumber                string                        `gorm:"column:contract_number"`                                                  // 合同号
	ScanUserId                    uint64                        `gorm:"column:scan_user_id"`                                                     // 扫描人id
	ScanUserName                  string                        `gorm:"column:scan_user_name"`                                                   // 扫描人名称
	ScanTime                      time.Time                     `gorm:"column:scan_time"`                                                        // 扫描时间
	IsInWarehouse                 bool                          `gorm:"column:is_in_warehouse"`                                                  // 是否已经入仓；1是
	OrderTime                     time.Time                     `gorm:"column:order_time"`                                                       // 单据选框选择的那个时间
	FinishProductWidthUnitId      uint64                        `gorm:"column:finish_product_width_unit_id" relate:"dictionary_detail_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                        `gorm:"column:finish_product_gram_weight_unit_id" relate:"dictionary_detail_id"` // 成品克重单位id(字典)
	BarCode                       string                        `gorm:"column:bar_code"`                                                         // 条码
	QrCode                        string                        `gorm:"column:qr_code"`                                                          // 二维码
	InternalRemark                string                        `gorm:"column:internal_remark"`                                                  // 内部备注
	ArrangeItemFcId               uint64                        `gorm:"column:arrange_item_fc"`
}

// 查询后的钩子
func (r *FpmInOrderItemFc) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmInOrderItemFc) GetId() uint64 {
	return r.Id
}

// TableName FpmInOrderItemFc 表名
func (FpmInOrderItemFc) TableName() string {
	return "fpm_in_order_item_fc"
}

func (r FpmInOrderItemFc) IsMain() bool {
	return false
}

func (r FpmInOrderItemFc) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmInOrderItemFc) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

// ErrCodeFpmInOrderItemFcAlreadyExist     ErrCode = 51XX1 // 进仓细码已存在
// ErrCodeFpmInOrderItemFcNotExist         ErrCode = 51XX2 // 进仓细码不存在
func (FpmInOrderItemFc) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmInOrderItemFcNotExist
}

func (FpmInOrderItemFc) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmInOrderItemFcAlreadyExist
}

func (r FpmInOrderItemFc) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func (w *FpmInOrderItemFc) ToAddStockProductDetailParam(ctx context.Context, swap structure.Swap2StockFieldParam) *structure.AddStockProductDetailParam {
	o := &structure.AddStockProductDetailParam{}
	o.AddWeightItemId = w.Id // 获取库存详情id用
	o.WarehouseBinId = w.WarehouseBinId
	o.WarehouseInType = swap.WarehouseInType
	o.WarehouseInOrderId = swap.WarehouseInOrderId
	o.WarehouseInOrderNo = swap.WarehouseInOrderNo
	o.DyeFactoryColorCode = w.DyeFactoryColorCode
	o.ProductColorId = swap.ProductColorId
	o.WarehouseId = swap.WarehouseId
	o.CustomerId = swap.CustomerId
	o.ProductId = swap.ProductId
	o.ProductLevelId = swap.ProductLevelId
	o.ProductColorKindId = swap.ProductColorKindId
	o.DyelotNumber = w.DyeFactoryDyelotNumber
	o.VolumeNumber = w.VolumeNumber
	o.WeightError = w.WeightError
	o.SettleErrorWeight = w.SettleErrorWeight
	o.PaperTubeWeight = w.PaperTubeWeight
	o.Weight = w.BaseUnitWeight
	o.Length = w.Length
	o.Roll = w.Roll
	o.DigitalCode = w.DigitalCode
	o.ContractNumber = w.ContractNumber
	// o.CustomerPoNum = item.
	o.CustomerAccountNum = w.AccountNum
	o.MeasurementUnitId = swap.MeasurementUnitId
	o.ProductRemark = swap.ItemProductRemark
	o.Remark = w.Remark
	o.InternalRemark = w.InternalRemark
	o.ShelfNo = w.ShelfNo
	o.IsNoNeedCheckStock = true
	o.FinishProductWidth = w.ProductWidth
	o.FinishProductGramWeight = w.ProductGramWeight
	o.FinishProductWidthUnitId = swap.FinishProductWidthUnitId
	o.FinishProductGramWeightUnitId = swap.FinishProductGramWeightUnitId
	// 正常进仓单取该值，如果是退货进仓取库存的供应商id todo:退货逻辑待补充
	o.SupplierId = swap.SupplierId
	// 汇总key
	o.StockProductKey = fmt.Sprintf("%v%v%v%v%v%v", o.WarehouseId, o.CustomerId, o.ProductColorId, o.ProductLevelId, o.ProductRemark, o.Remark)
	if swap.WarehouseInType == cus_const.WarehouseGoodInTypeProcessReturn {
		o.CustomerAccountNum = w.AccountNum
		o.DyelotNumber = w.DyeFactoryDyelotNumber
		o.FinishProductWidthUnitId = w.FinishProductWidthUnitId
		o.FinishProductGramWeightUnitId = w.FinishProductGramWeightUnitId
		o.DyeFactoryColorCode = w.DyeFactoryColorCode
		o.QrCode = w.QrCode
		o.IsMayBePassVolumeNumber = true
	}
	if swap.WarehouseInType == cus_const.WarehouseGoodInTypeAdjust {
		o.FinishProductWidthUnitId = w.FinishProductWidthUnitId
		o.FinishProductGramWeightUnitId = w.FinishProductGramWeightUnitId
	}
	if swap.WarehouseInType == cus_const.WarehouseGoodInTypeInternalAllocate ||
		swap.WarehouseInType == cus_const.WarehouseGoodInTypeSaleAllocate ||
		swap.WarehouseInType == cus_const.WarehouseGoodInTypeSaleReturn {
		o.QrCode = w.QrCode
	}
	o.SrcId = w.SrcId
	o.OutStockProductDetailId = swap.OutStockProductDetailId
	return o
}

func (w *FpmInOrderItemFc) ToUpdateStockProductDetailParamBack(ctx context.Context) *structure.UpdateStockProductDetailParam {
	o := &structure.UpdateStockProductDetailParam{}
	o.Id = w.StockId
	o.WarehouseOutOrderId = w.WarehouseInOrderId
	o.WarehouseInOrderNo = w.WarehouseInOrderNo
	o.StockProductId = w.SumStockId
	o.Weight = -w.BaseUnitWeight
	o.Length = -w.Length
	o.Roll = -w.Roll
	o.WeightError = -w.WeightError
	o.PaperTubeWeight = -w.PaperTubeWeight
	o.IsToWait = true
	return o
}

// 获取更新库存信息
func (w *FpmInOrderItemFc) ToUpdateStockProductDetailParam(ctx context.Context, swap structure.Swap2StockFieldParam, isToWait bool) *structure.UpdateStockProductDetailParam {
	o := &structure.UpdateStockProductDetailParam{}
	o.InOrderWeightItemId = w.Id // 获取库存详情id用
	o.Id = w.StockId
	o.WarehouseInOrderId = w.WarehouseInOrderId
	o.WarehouseInOrderNo = w.WarehouseInOrderNo
	o.StockProductId = w.SumStockId
	o.WarehouseId = swap.WarehouseId
	o.ProductId = swap.ProductId
	o.ProductColorId = swap.ProductColorId
	o.ProductLevelId = swap.ProductLevelId
	o.ProductRemark = swap.ItemProductRemark
	o.ProductColorKindId = swap.ProductColorKindId
	o.DyelotNumber = swap.DyeFactoryDyelotNumber
	o.CustomerId = swap.CustomerId

	o.WarehouseBinId = w.WarehouseBinId
	o.InternalRemark = w.InternalRemark
	o.DigitalCode = w.DigitalCode
	o.DyeFactoryColorCode = w.DyeFactoryColorCode
	o.CustomerAccountNum = w.AccountNum
	o.ShelfNo = w.ShelfNo
	o.FinishProductWidth = w.ProductWidth
	o.FinishProductGramWeight = w.ProductGramWeight
	o.FinishProductWidthUnitId = w.FinishProductWidthUnitId
	o.FinishProductGramWeightUnitId = w.FinishProductGramWeightUnitId
	o.VolumeNumber = w.VolumeNumber
	o.Weight = w.BaseUnitWeight
	o.Length = w.Length
	o.Roll = w.Roll
	o.WeightError = w.WeightError
	o.PaperTubeWeight = w.PaperTubeWeight
	if isToWait {
		o.Weight = -w.BaseUnitWeight
		o.Length = -w.Length
		o.Roll = -w.Roll
		o.WeightError = -w.WeightError
		o.PaperTubeWeight = -w.PaperTubeWeight
	}
	o.Remark = w.Remark
	// 汇总key
	o.StockProductKey = fmt.Sprintf("%v%v%v%v%v%v", o.WarehouseId, o.CustomerId, o.ProductColorId, o.ProductLevelId, o.ProductRemark, o.Remark)
	return o
}

func NewFpmInOrderItemFc(
	ctx context.Context,
	p *structure.AddFpmInOrderItemFcParam,
) (r FpmInOrderItemFc) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ParentId = p.ParentId
	r.WarehouseInType = p.WarehouseInType
	r.WarehouseInOrderId = p.WarehouseInOrderId
	r.WarehouseInOrderNo = p.WarehouseInOrderNo
	r.Roll = p.Roll
	r.WarehouseId = p.WarehouseId
	r.WarehouseBinId = p.WarehouseBinId
	r.VolumeNumber = p.VolumeNumber
	r.BaseUnitWeight = p.BaseUnitWeight
	r.WeightError = p.WeightError
	r.UnitId = p.UnitId
	r.StockId = p.StockId
	r.ReturnStockId = p.ReturnStockId
	r.SumStockId = p.SumStockId
	r.PaperTubeWeight = p.PaperTubeWeight
	r.Length = p.Length
	r.StockRemark = p.StockRemark // todo：重复字段，待去除
	r.Remark = p.Remark
	r.InternalRemark = p.InternalRemark
	r.DigitalCode = p.DigitalCode
	r.ShelfNo = p.ShelfNo
	r.AccountNum = p.AccountNum
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ContractNumber = p.ContractNumber
	r.ScanUserId = p.ScanUserId
	r.ScanUserName = p.ScanUserName
	r.ScanTime = p.ScanTime.ToTimeYMD()
	r.IsInWarehouse = p.IsInWarehouse
	r.OrderTime = p.OrderTime.ToTimeYMD()
	// 码单数量=总数量-码单空差
	r.ActuallyWeight = r.BaseUnitWeight - r.WeightError
	r.SettleErrorWeight = p.SettleErrorWeight
	// 结算数量=总数量-码单空差-结算空差
	r.SettleWeight = p.BaseUnitWeight - p.WeightError - p.SettleErrorWeight
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.ArrangeItemFcId = p.ArrangeItemFcId
	// 补充打码暂时使用，2024-1-11处理完去除，2024-2-11可删除
	// if r.QrCode == "" {
	// 	r.QrCode = p.QrCode
	// }
	// if r.BarCode == "" {
	// 	r.BarCode = p.BarCode
	// }
	r.SrcId = p.SrcId
	return
}

// 补充打码暂时使用，2024-1-11处理完去除，2024-2-11可删除
// func (r *FpmInOrderItemFc) GenBarQrCode(ctx context.Context, productCode, productColorCode string) {
// 	now := time.Now()
//
// 	// 生成条码
// 	barCode := tools.NewFabricPieceBarCode(
// 		001,
// 		001,
// 		now,
// 		r.VolumeNumber,
// 	)
//
// 	r.BarCode = barCode.GenerateBarCode()
//
// 	// 生成二维码
// 	qrCode := tools.NewFabricPieceCodeQrCode66(
// 		metadata.GetCompanyID(ctx),
// 		productCode,
// 		productColorCode,
// 		001,
// 		r.DyeFactoryDyelotNumber,
// 		r.VolumeNumber,
// 		r.BaseUnitWeight,
// 		r.BarCode,
// 	)
//
// 	r.QrCode = qrCode.GenerateQrCode()
// }
//
// func (r *FpmInOrderItemFc) GenBarQrCodeV2(ctx context.Context, productCode, productColorCode string, productId, colorId, oproductId, ocolorId uint64, oldFc FpmInOrderItemFc) {
// 	now := time.Now()
//
// 	if productId == oproductId &&
// 		colorId == ocolorId &&
// 		r.VolumeNumber == oldFc.VolumeNumber &&
// 		r.DyeFactoryDyelotNumber == oldFc.DyeFactoryDyelotNumber &&
// 		r.BaseUnitWeight == oldFc.BaseUnitWeight {
// 		return
// 	}
//
// 	// 生成条码
// 	barCode := tools.NewFabricPieceBarCode(
// 		001,
// 		001,
// 		now,
// 		r.VolumeNumber,
// 	)
//
// 	r.BarCode = barCode.GenerateBarCode()
//
// 	// 生成二维码
// 	qrCode := tools.NewFabricPieceCodeQrCode66(
// 		metadata.GetCompanyID(ctx),
// 		productCode,
// 		productColorCode,
// 		001,
// 		r.DyeFactoryDyelotNumber,
// 		r.VolumeNumber,
// 		r.BaseUnitWeight,
// 		r.BarCode,
// 	)
//
// 	r.QrCode = qrCode.GenerateQrCode()
// }

type FpmInOrderItemFcForReport struct {
	FpmInOrderItemFc
	SumRoll              int                `gorm:"column:sum_roll" sqlf:"sum(roll)"`
	SumWeight            int                `gorm:"column:sum_weight" sqlf:"sum(base_unit_weight)"`
	SumPaperTubeWeight   int                `gorm:"column:sum_paper_tube_weight" sqlf:"sum(paper_tube_weight)"`
	MergeStockIds        tools.QueryIntList `gorm:"column:merge_stock_ids" sqlf:"GROUP_CONCAT(distinct stock_id SEPARATOR ',')"`
	MergeWarehouseBinIds tools.QueryIntList `gorm:"column:merge_warehouse_bin_ids" sqlf:"GROUP_CONCAT(distinct warehouse_bin_id SEPARATOR ',')"`
}

type FpmInOrderItemFcForReportList []FpmInOrderItemFcForReport

type FpmInOrderItemFcForFinshProductReport struct {
	FpmInOrderItemFc
	SumRoll   int `gorm:"column:sum_roll" sqlf:"sum(roll)"`
	SumWeight int `gorm:"column:sum_weight" sqlf:"sum(base_unit_weight)"`
	// SumPaperTubeWeight int `gorm:"column:sum_paper_tube_weight" sqlf:"sum(paper_tube_weight)"`
}

type FpmInOrderItemFcForFinshProductReportList []FpmInOrderItemFcForFinshProductReport
