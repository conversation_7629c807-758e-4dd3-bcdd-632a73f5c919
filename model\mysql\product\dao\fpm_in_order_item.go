package dao

import (
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	. "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
)

func MustCreateFpmInOrderItem(tx *mysql_base.Tx, r FpmInOrderItem) (o FpmInOrderItem, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateFpmInOrderItem(tx *mysql_base.Tx, r FpmInOrderItem) (o FpmInOrderItem, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteFpmInOrderItem(tx *mysql_base.Tx, r FpmInOrderItem) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstFpmInOrderItemByID(tx *mysql_base.Tx, id uint64) (r FpmInOrderItem, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstFpmInOrderItemByID(tx *mysql_base.Tx, id uint64) (r FpmInOrderItem, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FindFpmInOrderItemByFpmInOrderItemID(tx *mysql_base.Tx, objects ...interface{}) (o FpmInOrderItemList, err error) {
	ids := GetFpmInOrderItemIdList(objects)
	var (
		r    FpmInOrderItem
		cond = mysql_base.NewCondition()
		list []FpmInOrderItem
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmInOrderItemByIDs(tx *mysql_base.Tx, ids []uint64) (o FpmInOrderItemList, err error) {
	var (
		r    FpmInOrderItem
		cond = mysql_base.NewCondition()
		list []FpmInOrderItem
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmInOrderItemByFpmArrangeItemId(tx *mysql_base.Tx, id uint64) (o FpmInOrderItem, err error) {
	var (
		r    FpmInOrderItem
		cond = mysql_base.NewCondition()
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableEqual(r, "arrange_order_item_id", id)
	err = mysql_base.FindByCond(tx, &r, &o, cond)
	if err != nil {
		return
	}
	return
}

func SearchFpmInOrderItem(tx *mysql_base.Tx, q *structure.GetFpmInOrderItemListQuery) (o FpmInOrderItemList, count int, err error) {
	var (
		r           FpmInOrderItem
		cond        = mysql_base.NewCondition()
		list        []FpmInOrderItem
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	groupFields = []string{}
	if q.InOrderType > 0 {
		cond.AddTableEqual(r, "warehouse_in_type", q.InOrderType)
	}
	if q.OrderNo != "" {
		cond.AddFuzzyMatch("parent_order_no", q.OrderNo)
	}
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.UseByEnum {
		cond.AddContainMatch("parent_id", q.OrderPassIds)
	}
	if q.CustomerId > 0 {
		cond.AddEqual("customer_id", q.CustomerId)
	}
	if q.QuoteOrderItemId > 0 {
		cond.AddEqual("quote_order_item_id", q.QuoteOrderItemId)
	}
	// cond.AddTableEqual(r, "delete_time", 0)
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据上级id获取
func FindFpmInOrderItemByParenTID(tx *mysql_base.Tx, pid uint64) (o FpmInOrderItemList, err error) {
	var (
		r    FpmInOrderItem
		cond = mysql_base.NewCondition()
		list []FpmInOrderItem
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "parent_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmInOrderItemByParenTIDs(tx *mysql_base.Tx, pid []uint64) (o FpmInOrderItemList, err error) {
	var (
		r    FpmInOrderItem
		cond = mysql_base.NewCondition()
		list []FpmInOrderItem
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableContainMatch(r, "parent_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmInOrderItemByQuoteOrderNo(tx *mysql_base.Tx, orderNo string) (o FpmInOrderItemList, err error) {
	var (
		r     FpmInOrderItem
		order FpmInOrder
		cond  = mysql_base.NewCondition()
		list  []FpmInOrderItem
	)
	cond.AddTableLeftJoiner(r, order, "parent_id", "id")
	cond.AddTableEqual(r, "quote_order_no", orderNo)
	cond.AddTableNotEqual(order, "audit_status", common_system.OrderStatusVoided)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据染整进度单ids判断是否有加工进仓单使用到染整单据
func FindFpmInOrderItemByDNFItemIds(tx *mysql_base.Tx, DNFItemIds []uint64) (o FpmInOrderList, err error) {
	var (
		order FpmInOrder
		r     FpmInOrderItem
		cond  = mysql_base.NewCondition()
		list  []FpmInOrder
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableNotEqual(order, "audit_status", common_system.OrderStatusVoided)
	cond.AddTableLeftJoiner(order, r, "id", "parent_id")
	cond.AddTableContainMatch(r, "quote_order_item_id", DNFItemIds)

	err = mysql_base.SearchListGroup(tx, &order, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}
