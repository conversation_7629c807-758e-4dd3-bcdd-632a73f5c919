package tenant_management

import (
	"context"
	"encoding/json"
	"fmt"
	aggs "hcscm/aggs/tenant_management"
	tenant_svc "hcscm/api/wx/tenant"
	tenant "hcscm/api/wx/tenant/v1"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/tenant_management"
	structure "hcscm/structure/tenant_management"
	"hcscm/tools"
	"log"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ITenantManagementLogic interface {
	Create(ctx context.Context, param structure.AddTenantManagementParam) (data structure.ResPayUrlData, err error)
	CreateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error)
	UpdateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error)
	UpdateCompanyName(ctx context.Context, param structure.UpdateTenantManagementNameParam) (data structure.ResTenantManagementIDData, err error)
	DisableTenantManagement(ctx context.Context, param structure.DisableTenantManagementParam) (err error)
	EnableTenantManagement(ctx context.Context, param structure.EnableTenantManagementParam) (err error)
	ModifyTenantLoginPassword(ctx context.Context, param structure.ModifyTenantManagementPasswordParam) (err error)
	GetDetailInfo(ctx context.Context, query structure.GetTenantManagementQuery) (data structure.GetTenantManagementData, err error)
	GetMPDetailInfo(ctx context.Context, query structure.GetTenantManagementQuery) (data structure.GetMPTenantManagementData, err error)
	SearchList(ctx context.Context, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error)
	GetExpiringSoonTenantManagementPushQueue()

	CleanTenantManagements(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (err error)
	GetUserTenantManagementList(ctx context.Context, query structure.GetTenantManagementQuery) (data structure.GetUserTenantManagementDataList, total int, err error)
	RegisterTenantManagement(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (mysql.TenantManagement, error)
	TenantManagementFeedback(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error)
	WashTenantManagementRel(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error)
	CompatibleOldTenant(tx *mysql_base.Tx, ctx context.Context, param structure.CompatibleOldTenantParam) (data structure.CompatibleOldTenantResponse, err error)
	// todo
	GetTenantManagementList(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error)
	FillDomain(ctx context.Context, param *structure.FillDomainParam) (err error)
	GetUnbindTenantManagementList(ctx context.Context, query structure.QYWXGetUnBindTenantManagementListQuery) (data structure.QYWXGetUnBindTenantManagementListData, err error)
}

func NewTenantManagementLogic(ginCtx *gin.Context) ITenantManagementLogic {
	return &tenantManagementLogic{
		tenantManagementRepo: aggs.NewTenantManagementRepo(),
		singleFlight:         tools.Single,
		ginCtx:               ginCtx,
	}
}

type tenantManagementLogic struct {
	tx                   *mysql_base.Tx
	singleFlight         tools.SingleFlight
	ginCtx               *gin.Context
	tenantManagementRepo aggs.ITenantManagementRepo
}

func (logic *tenantManagementLogic) SearchList(ctx context.Context, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	return logic.tenantManagementRepo.SearchList(ctx, tx, query)
}

func (logic *tenantManagementLogic) GetDetailInfo(ctx context.Context, query structure.GetTenantManagementQuery) (data structure.GetTenantManagementData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return logic.tenantManagementRepo.GetDetailInfo(ctx, tx, query)
}

func (logic *tenantManagementLogic) GetMPDetailInfo(ctx context.Context, query structure.GetTenantManagementQuery) (data structure.GetMPTenantManagementData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return logic.tenantManagementRepo.GetMPDetailInfo(ctx, tx, query)
}

func (logic *tenantManagementLogic) Create(ctx context.Context, param structure.AddTenantManagementParam) (data structure.ResPayUrlData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()
		data, err = logic.tenantManagementRepo.Create(ctx, tx, param)
		if err != nil {
			return
		}
		return data, nil
	})
	if err != nil {
		return
	}

	return val.(structure.ResPayUrlData), nil
}

func (logic *tenantManagementLogic) CreateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		data, err = logic.tenantManagementRepo.CreateSubTenant(ctx, param)
		if err != nil {
			return
		}
		return data, nil
	})
	if err != nil {
		return
	}

	return val.(structure.ResTenantManagementIDData), nil
}

func (logic *tenantManagementLogic) UpdateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		data, err = logic.tenantManagementRepo.UpdateSubTenant(ctx, param)
		if err != nil {
			return
		}
		return data, nil
	})
	if err != nil {
		return
	}

	return val.(structure.ResTenantManagementIDData), nil
}

func (logic *tenantManagementLogic) ModifyTenantLoginPassword(ctx context.Context, param structure.ModifyTenantManagementPasswordParam) (err error) {
	var (
		paramByte []byte
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	_, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()
		err = logic.tenantManagementRepo.ModifyTenantLoginPassword(ctx, tx, param)
		if err != nil {
			return
		}
		return nil, nil
	})
	if err != nil {
		return
	}

	return nil
}

func (logic *tenantManagementLogic) UpdateCompanyName(ctx context.Context, param structure.UpdateTenantManagementNameParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()
		return logic.tenantManagementRepo.UpdateCompanyName(ctx, tx, param)

	})
	if err != nil {
		return
	}

	return val.(structure.ResTenantManagementIDData), nil
}

func (logic *tenantManagementLogic) DisableTenantManagement(ctx context.Context, param structure.DisableTenantManagementParam) (err error) {
	var (
		paramByte []byte
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	_, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()
		return nil, logic.tenantManagementRepo.DisableTenantManagement(ctx, tx, param)

	})
	if err != nil {
		return
	}

	return
}

func (logic *tenantManagementLogic) EnableTenantManagement(ctx context.Context, param structure.EnableTenantManagementParam) (err error) {
	var (
		paramByte []byte
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	_, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		return nil, logic.tenantManagementRepo.EnableTenantManagement(ctx, param)

	})
	if err != nil {
		return
	}

	return
}

func (logic *tenantManagementLogic) GetExpiringSoonTenantManagementPushQueue() {
	var (
		err error
		ctx = context.Background()
	)
	err = logic.tenantManagementRepo.GetExpiringSoonTenantManagementPushQueue(ctx, nil)
	if err != nil {
		return
	}
	return
}

func (logic *tenantManagementLogic) GetUserTenantManagementList(ctx context.Context, query structure.GetTenantManagementQuery) (data structure.GetUserTenantManagementDataList, total int, err error) {
	data, total, err = logic.tenantManagementRepo.GetUserTenantManagementList(ctx, query)
	if err != nil {
		return
	}
	return
}

func (logic *tenantManagementLogic) CleanTenantManagements(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (err error) {
	err = logic.tenantManagementRepo.CleanTenantManagements(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}
	return
}

// 设置新用户的账套初始状态和有效期
func (logic *tenantManagementLogic) RegisterTenantManagement(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (mysql.TenantManagement, error) {
	// 设置新用户账套的有效期
	updatedTenantManagement, err := logic.tenantManagementRepo.SetNewUserOrcDeadline(ctx, tx, tenantManagementId)
	if err != nil {
		return mysql.TenantManagement{}, err
	}

	return updatedTenantManagement, nil
}

// 账套反馈
func (logic *tenantManagementLogic) TenantManagementFeedback(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error) {
	err = logic.tenantManagementRepo.TenantManagementFeedback(ctx, q)
	if err != nil {
		return
	}
	return
}

// 清洗账套关联关系(补充缺失的账套用户关系的)
func (logic *tenantManagementLogic) WashTenantManagementRel(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error) {
	err = logic.tenantManagementRepo.WashTenantManagementRel(ctx, q)
	if err != nil {
		return
	}
	return
}

// 获取所有租户管理列表
func (logic *tenantManagementLogic) GetTenantManagementList(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error) {
	tenantManagements, err = logic.tenantManagementRepo.GetTenantManagementList(ctx, tx)
	if err != nil {
		return
	}
	return
}

// 填充域名
func (logic *tenantManagementLogic) FillDomain(ctx context.Context, param *structure.FillDomainParam) (err error) {
	// 开启事务
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		if r := recover(); r != nil {
			err = commit(nil, r)
			panic(r)
		}
		err = commit(err, nil)
	}()

	// 更新所有记录的 request_domain 字段
	if err = logic.tenantManagementRepo.FillDomain(ctx, tx, param); err != nil {
		return err
	}

	return nil
}

func (logic *tenantManagementLogic) GetUnbindTenantManagementList(ctx context.Context, query structure.QYWXGetUnBindTenantManagementListQuery) (data structure.QYWXGetUnBindTenantManagementListData, err error) {
	var tenantManagements mysql.TenantManagementList
	// 开启事务
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		if r := recover(); r != nil {
			err = commit(nil, r)
			panic(r)
		}
		err = commit(err, nil)
	}()
	tenantClient := tenant_svc.GetTenantClient(ctx)
	tenantReplay, err := tenantClient.ListTenant(ctx, &tenant.ListTenantRequest{})
	if err != nil {
		return
	}
	ids := make([]string, len(tenantReplay.List))
	for i, tenant := range tenantReplay.List {
		ids[i] = strconv.FormatUint(tenant.TenantManagementId, 10)
	}
	query.BoundTenantManagementIds = tools.QueryIntList(strings.Join(ids, ","))
	tenantManagements, data.Total, err = logic.tenantManagementRepo.GetUnbindTenantManagementList(ctx, tx, query)
	if err != nil {
		return
	}
	for _, tenantManagement := range tenantManagements {
		data.List = append(data.List, structure.QYWXGetUnBindTenantManagementListDataItem{
			Id:          tenantManagement.Id,
			CompanyName: tenantManagement.TenantCompanyName,
			Phone:       tenantManagement.TenantPhoneNumber,
			Contacts:    tenantManagement.TenantContacts,
		})
	}
	return
}

// CompatibleOldTenant 兼容旧账套增加电子色卡服务
func (logic *tenantManagementLogic) CompatibleOldTenant(tx *mysql_base.Tx, ctx context.Context, param structure.CompatibleOldTenantParam) (data structure.CompatibleOldTenantResponse, err error) {
	type Tenant struct {
		ID           uint64 `gorm:"column:id"`
		Phone        string `gorm:"column:tenant_phone_number"`
		DatabaseName string `gorm:"column:database_name"`
	}
	var merchantInfoSQL string = `CREATE TABLE ` + "`merchant_info`" + ` (
  ` + "`id`" + ` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  ` + "`tenant_management_id`" + ` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户管理ID',
  ` + "`merchant_name`" + ` varchar(255) NOT NULL COMMENT '商家名称',
  ` + "`merchant_addr`" + ` varchar(255) DEFAULT NULL COMMENT '商家地址',
  ` + "`main_products`" + ` longtext DEFAULT NULL COMMENT '主营产品',
  ` + "`logo_url`" + ` varchar(255) DEFAULT NULL COMMENT 'Logo图片URL',
  ` + "`business_type`" + ` varchar(255) DEFAULT NULL COMMENT '企业类型标签',
  ` + "`service_expire`" + ` datetime DEFAULT NULL COMMENT '服务有效期至',
  ` + "`phone_contacts`" + ` varchar(255) NOT NULL COMMENT '电话号码和联系人',
  ` + "`create_time`" + ` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
  ` + "`creator_id`" + ` bigint(20) unsigned NOT NULL COMMENT '创建人ID ',
  ` + "`creator_name`" + ` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
  ` + "`update_time`" + ` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
  ` + "`updater_id`" + ` bigint(20) unsigned NOT NULL COMMENT '更新人ID ',
  ` + "`updater_name`" + ` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
  ` + "`delete_time`" + ` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
  ` + "`deleter_id`" + ` bigint(20) unsigned NOT NULL COMMENT '删除人ID ',
  ` + "`deleter_name`" + ` varchar(255) NOT NULL DEFAULT '' COMMENT '删除人名称',
  ` + "`company_url`" + ` longtext DEFAULT NULL COMMENT '企业URL链接',
  PRIMARY KEY (` + "`id`" + `)
) ENGINE=InnoDB AUTO_INCREMENT=1882138391268993 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci COMMENT='商家信息表';`
	var carouselBannersSQL string = `CREATE TABLE IF NOT EXISTS carousel_banners (
    ` + "`id`" + ` bigint(20) PRIMARY KEY AUTO_INCREMENT,
    ` + "`create_time`" + ` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    ` + "`creator_id`" + ` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人ID （关联user.id）',
    ` + "`creator_name`" + ` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
    ` + "`update_time`" + ` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    ` + "`updater_id`" + ` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新人ID （关联user.id）',
    ` + "`updater_name`" + ` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
    ` + "`delete_time`" + ` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    ` + "`deleter_id`" + ` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '删除人ID （关联user.id）',
    ` + "`deleter_name`" + ` varchar(255) NOT NULL DEFAULT '' COMMENT '删除人名称',
    ` + "`prev_view_url`" + ` varchar(128) NOT NULL COMMENT '预览图',
    ` + "`title`" + ` varchar(255) NOT NULL COMMENT '标题',
    ` + "`link`" + ` longtext NOT NULL COMMENT '链接',
    ` + "`target_id`" + ` bigint(20) NOT NULL COMMENT '目标ID',
    ` + "`jump_type`" + ` INT NOT NULL COMMENT '跳转类型',
    ` + "`sort`" + ` INT NOT NULL DEFAULT 0 COMMENT '排序',
    ` + "`remark`" + ` text COMMENT '备注',
    ` + "`status`" + ` tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态',
    ` + "`video_url`" + ` longtext NOT NULL COMMENT '视频',
    ` + "`img_url`" + ` longtext NOT NULL COMMENT '图片',
    INDEX idx_delete_time (` + "`delete_time`" + `)
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COMMENT '轮播图信息表';`

	var (
		tenantList  []Tenant
		successList []string
		failedList  []string
	)

	// 获取指定的手机号码账套
	if param.TenantPhone != "" {
		err = mysql_base.GetConn(tx).Table("tenant_management").
			Select("id, tenant_phone_number, database_name").
			Where("tenant_phone_number = ? AND delete_time = '0000-00-00 00:00:00'", param.TenantPhone).
			Scan(&tenantList).Error
		if err != nil {
			return
		}
	} else {
		// 获取所有租户数据库
		err = mysql_base.GetConn(tx).Table("tenant_management").
			Select("id, tenant_phone_number, database_name").
			Where("delete_time = '0000-00-00 00:00:00'").
			Scan(&tenantList).Error
		if err != nil {
			return
		}
	}

	// 遍历执行SQL
	for _, tenant := range tenantList {
		var (
			_db *gorm.DB
		)

		log.Printf("开始处理数据库: %s", tenant.DatabaseName)
		startTime := time.Now()

		// 连接租户数据库
		_db = mysql_base.GetDBMap(tenant.ID)
		if _db == nil {
			_db, err = mysql_base.NewDBConn(ctx, tenant.DatabaseName, tenant.ID)
			if err != nil {
				failedList = append(failedList, fmt.Sprintf("连接数据库 %s 失败: %v", tenant.DatabaseName, err))
				log.Printf("连接数据库 %s 失败: %v", tenant.DatabaseName, err)
				continue
			}
		}

		// 检查merchant_info表是否存在
		var hasTable bool
		if err = _db.Raw("SHOW TABLES LIKE 'merchant_info'").Scan(&hasTable).Error; err != nil {
			failedList = append(failedList, fmt.Sprintf("检查数据库 %s 的merchant_info表失败: %v", tenant.DatabaseName, err))
			continue
		}

		if !hasTable {
			if err = _db.Exec(merchantInfoSQL).Error; err != nil {
				failedList = append(failedList, fmt.Sprintf("在数据库 %s 中创建merchant_info表失败: %v", tenant.DatabaseName, err))
				continue
			}
		}

		// 检查carousel_banners表是否存在
		if err = _db.Raw("SHOW TABLES LIKE 'carousel_banners'").Scan(&hasTable).Error; err != nil {
			failedList = append(failedList, fmt.Sprintf("检查数据库 %s 的carousel_banners表失败: %v", tenant.DatabaseName, err))
			continue
		}

		if !hasTable {
			if err = _db.Exec(carouselBannersSQL).Error; err != nil {
				failedList = append(failedList, fmt.Sprintf("在数据库 %s 中创建carousel_banners表失败: %v", tenant.DatabaseName, err))
				continue
			}
		}

		// 检查租户数据库中tenant_management表是否存在电子色卡状态字段
		var hasStatusField, hasDeadLineField bool
		var columns []struct {
			Field string `gorm:"column:Field"`
		}

		// 检查主库tenant_management表是否存在电子色卡状态字段
		// 查询表结构
		err = _db.Raw("SHOW COLUMNS FROM tenant_management").Scan(&columns).Error
		if err != nil {
			failedList = append(failedList, fmt.Sprintf("检查租户数据库 %s 的tenant_management表结构失败: %v", tenant.DatabaseName, err))
			continue
		}

		// 检查租户数据库是否存在字段
		for _, col := range columns {
			if col.Field == "electronic_color_card_status" {
				hasStatusField = true
			}
			if col.Field == "electronic_color_card_dead_line" {
				hasDeadLineField = true
			}
		}

		// 添加电子色卡状态字段
		if !hasStatusField {
			err = _db.Exec("ALTER TABLE tenant_management ADD COLUMN electronic_color_card_status tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '电子色卡状态 1启用 2禁用 3未激活'").Error
			if err != nil {
				failedList = append(failedList, fmt.Sprintf("为租户数据库 %s 的tenant_management表添加电子色卡状态字段失败: %v", tenant.DatabaseName, err))
				continue
			}
			log.Printf("为租户数据库 %s 的tenant_management表添加electronic_color_card_status字段成功", tenant.DatabaseName)
		}

		// 添加电子色卡有效期字段
		if !hasDeadLineField {
			err = _db.Exec("ALTER TABLE tenant_management ADD COLUMN electronic_color_card_dead_line datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '电子色卡有效期'").Error
			if err != nil {
				failedList = append(failedList, fmt.Sprintf("为租户数据库 %s 的tenant_management表添加电子色卡有效期字段失败: %v", tenant.DatabaseName, err))
				continue
			}
			log.Printf("为租户数据库 %s 的tenant_management表添加electronic_color_card_dead_line字段成功", tenant.DatabaseName)
		}

		// 检查recharge_history表的结构
		var hasTypeField, hasEleCardDeadlineField bool
		var rechargeHistoryColumns []struct {
			Field string `gorm:"column:Field"`
		}

		// 查询recharge_history表结构
		err = _db.Raw("SHOW COLUMNS FROM recharge_history").Scan(&rechargeHistoryColumns).Error
		if err != nil {
			failedList = append(failedList, fmt.Sprintf("检查租户数据库 %s 的recharge_history表结构失败: %v", tenant.DatabaseName, err))
			continue
		}

		// 检查是否存在字段
		for _, col := range rechargeHistoryColumns {
			if col.Field == "type" {
				hasTypeField = true
			}
			if col.Field == "ele_card_deadline" {
				hasEleCardDeadlineField = true
			}
		}

		// 添加充值类型字段
		if !hasTypeField {
			err = _db.Exec("ALTER TABLE recharge_history ADD COLUMN `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '充值类型'").Error
			if err != nil {
				failedList = append(failedList, fmt.Sprintf("为租户数据库 %s 的recharge_history表添加type字段失败: %v", tenant.DatabaseName, err))
				continue
			}
			log.Printf("为租户数据库 %s 的recharge_history表添加type字段成功", tenant.DatabaseName)
		}

		// 添加电子色卡截止有效期字段
		if !hasEleCardDeadlineField {
			err = _db.Exec("ALTER TABLE recharge_history ADD COLUMN ele_card_deadline datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '电子色卡截止有效期'").Error
			if err != nil {
				failedList = append(failedList, fmt.Sprintf("为租户数据库 %s 的recharge_history表添加ele_card_deadline字段失败: %v", tenant.DatabaseName, err))
				continue
			}
			log.Printf("为租户数据库 %s 的recharge_history表添加ele_card_deadline字段成功", tenant.DatabaseName)
		}

		successList = append(successList, fmt.Sprintf("租户ID: %d, 数据库: %s, 电话: %s", tenant.ID, tenant.DatabaseName, tenant.Phone))
		duration := time.Since(startTime)
		log.Printf("数据库 %s 处理成功，耗时: %v", tenant.DatabaseName, duration)
	}

	return
}
