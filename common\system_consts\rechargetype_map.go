package common

func GetRechargeTypeMap() (r map[RechargeType]string) {
	l := []RechargeType{RechargeTypeOcr, RechargeTypeEleColorCard, RechargeTypeSearchImage}
	r = make(map[RechargeType]string)
	for _, k := range l {
		r[k] = k.String()
	}
	return r
}
func GetRechargeTypeReverseMap() (r map[string]RechargeType) {
	l := []RechargeType{RechargeTypeOcr, RechargeTypeEleColorCard, RechargeTypeSearchImage}
	r = make(map[string]RechargeType)
	for _, k := range l {
		r[k.String()] = k
	}
	return r
}
func GetRechargeTypeReverseIntMap() (r map[string]int) {
	l := []RechargeType{RechargeTypeOcr, RechargeTypeEleColorCard, RechargeTypeSearchImage}
	r = make(map[string]int)
	for _, k := range l {
		r[k.String()] = int(k)
	}
	return r
}

func (t RechargeType) Check() bool {
	l := []RechargeType{RechargeTypeOcr, RechargeTypeEleColorCard, RechargeTypeSearchImage}
	for i := range l {
		if l[i] == t {
			return true
		}
	}
	return false
}
