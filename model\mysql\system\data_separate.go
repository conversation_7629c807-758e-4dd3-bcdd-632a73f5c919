package mysql

import (
	"context"
	"fmt"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"strings"
)

type ISeparable interface {
	mysql_base.ITable
	GetCompanyField() string
	GetDepartmentField() string
	GetSaasCompanyField() string
	GetSaleUserField() string
	GetUserField() string
	GetSupplierField() string
	GetPurchaserField() string
	GetWarehouseField() []string
	GetSaleSystemField() string
	GetOtherFields() []string                            // 数据隔离用到的其它自定义字段，使用到的model需要重写该方法
	GetCreateUserFields() string                         // 数据隔离创建人
	GetUserFields() []string                             // 数据隔离(仅个人)相关字段
	IsHasRel() bool                                      // 是否关联表来的
	GetRelTableAndRelField() (mysql_base.ITable, string) // 关联的表名称
}

type Access struct {
	AccessScope              common.RoleAccessDataScope
	AccessScopeOtherSelect   string
	MPAccessScope            common.RoleAccessDataScope
	MPAccessScopeOtherSelect string
}

type AccessList []Access

// CommonDataSeparate 角色权限改造后，通用数据隔离统一调用方法
func CommonDataSeparate(ctx context.Context, r ISeparable, cond mysql_base.ICondition) {
	var (
		err error
	)
	operator := metadata.GetLoginInfo(ctx)
	if operator == nil {
		return
	}
	if operator.GetUserId() == vars.AdminUserID {
		return
	}
	warehouseIds := operator.GetWarehouseIds()
	if len(warehouseIds) != 0 {
		for _, field := range r.GetWarehouseField() {
			cond.AddTableContainMatchToORWithCheck(r, field, warehouseIds)
		}
	}

	saleSystemIds := operator.GetSaleSystemIds()
	// if len(saleSystemIds) != 0 {
	isRel := r.IsHasRel()
	if isRel {
		relTable, relField := r.GetRelTableAndRelField()
		cond.AddTableLeftJoiner(r, relTable, fmt.Sprintf("id and %v.deleter_id=0", relTable.TableName()), relField)
		cond.AddTableContainMatchWithCheck(relTable, r.GetSaleSystemField(), saleSystemIds)
	} else {
		cond.AddTableContainMatchWithCheck(r, r.GetSaleSystemField(), saleSystemIds)
	}
	// }

	var accessList AccessList
	data, ok := ctx.Value(metadata.DataAccessScope).([]byte)
	if !ok {
		return
	}
	err = tools.JsonUnmarshal(data, &accessList)
	if err != nil {
		return
	}
	if len(accessList) == 0 {
		return
	}

	// cond.AddTableContainMatchToORWithCheck(r, r.GetDepartmentField(), operator.GetBizUnitIds())

	// 商城用户
	/*if operator.IsMallUser() {
		cond.AddEqualWithCheck(r, r.GetUserField(), operator.GetUserId())
		return
	}*/

	// operator.GetToken()

	// 遍历角色权限列表，数据范围-取并集
	for _, access := range accessList {
		var (
			accessScope            common.RoleAccessDataScope
			accessScopeOtherSelect string
		)
		if operator.GetPlatform() == common.PlatformAdmin {
			accessScope = access.AccessScope
			accessScopeOtherSelect = access.AccessScopeOtherSelect
		}
		if operator.GetPlatform() == common.PlatformMP {
			// todo:暂时未区分后台和小程序的数据权限,统一用后台的数据权限
			accessScope = access.AccessScope
			accessScopeOtherSelect = access.AccessScopeOtherSelect
			// accessScope = access.MPAccessScope
			// accessScopeOtherSelect = access.MPAccessScopeOtherSelect
		}
		if accessScope == common.RoleAccessDataScopeCompany {
			// 整个公司
			// todo:账套隔离

		} else if accessScope == common.RoleAccessDataScopeDepartment {
			// 所在部门
			cond.AddTableEqualToORWithCheck(r, r.GetDepartmentField(), operator.GetDepartmentId())

		} else if accessScope == common.RoleAccessDataScopeDepartmentAndBelow {
			// 所在部门及以下
			subDepartmentID := operator.GetSubDepartmentId()
			subDepartmentID = append(subDepartmentID, operator.GetDepartmentId())
			cond.AddTableContainMatchToORWithCheck(r, r.GetDepartmentField(), subDepartmentID)

		} else if accessScope == common.RoleAccessDataScopePerson {
			// 仅个人
			// 自定义数据隔离字段
			for _, field := range r.GetOtherFields() {
				cond.AddTableEqualToORWithCheck(r, field, operator.GetUserId())
			}
			for _, field := range r.GetUserFields() {
				cond.AddTableEqualToORWithCheck(r, field, operator.GetUserId())
			}
		} else if accessScope == common.RoleAccessDataScopeOther {
			// 其他
			if accessScopeOtherSelect != "" {
				var (
					// 【其他】时选择的部门
					departmentIds []uint64
				)
				departmentIdStr := strings.Split(accessScopeOtherSelect, ",")
				departmentIds, err = tools.StringArr2UInt64Arr(departmentIdStr)
				if err != nil {
					return
				}
				cond.AddTableContainMatchToORWithCheck(r, r.GetDepartmentField(), departmentIds)
			}
		}
	}

}
