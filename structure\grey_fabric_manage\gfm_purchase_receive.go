package grey_fabric_manage

import (
	"github.com/shopspring/decimal"
	common_system "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/vars"
)

type AddGFMPurchaseReceiveParam struct {
	structure_base.Param
	DepartmentId      uint64                           `json:"department_id"`         // 下单人部门id
	SaleSystemId      uint64                           `json:"sale_system_id"`        // 营销体系id，必填
	RecipientEntityId uint64                           `json:"recipient_entity_id"`   // 收货单位id，必填
	SupplierId        uint64                           `json:"supplier_id"`           // 供应商id，必填
	DocumentCode      string                           `json:"document_code"`         // 单据编号
	VoucherNumber     string                           `json:"voucher_number"`        // 凭证单号
	ReceiveTime       tools.QueryTime                  `json:"receive_date"`          // 收货日期，必
	AuditStatus       common_system.OrderStatus        `json:"audit_status"`          // 审核状态
	AuditerId         uint64                           `json:"auditer_id"`            // 审核人ID （关联user.id）
	AuditerName       string                           `json:"auditer_name"`          // 审核人名称
	Remark            string                           `json:"remark"`                // 备注
	PurchaseOrderId   uint64                           `json:"purchase_order_id"`     // 坯布采购单id
	DyeUnitUseOrderNo string                           `json:"dye_unit_use_order_no"` // 染厂用坯单号
	ItemData          []AddGFMPurchaseReceiveItemParam `json:"item_data"`             // 坯布信息
}

func (r AddGFMPurchaseReceiveParam) GetTotalPWR() (totalPrice int, totalWeight int, totalRoll int) {
	for _, v := range r.ItemData {
		totalRoll += v.Roll
		aItemTotalWeight := 0
		aItemTotalPrice := 0
		for _, v2 := range v.ItemFCData {
			totalWeight += v2.Weight
			aItemTotalWeight += v2.Weight
		}
		// 安全相除计算并四舍五入
		temp1 := tools.DecimalDiv(float64(aItemTotalWeight*v.SinglePrice), vars.WeightUnitPriceMult)
		tempPrice, _ := decimal.NewFromFloat(temp1).Round(0).Float64()
		aItemTotalPrice = int(tempPrice) + v.OtherPrice
		totalPrice += aItemTotalPrice
	}
	return
}

type AddGFMPurchaseReceiveData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateGFMPurchaseReceiveParam struct {
	structure_base.Param
	Id                uint64                           `json:"id"`
	SaleSystemId      uint64                           `json:"sale_system_id"`        // 营销体系id，必填
	RecipientEntityId uint64                           `json:"recipient_entity_id"`   // 收货单位id，必填
	SupplierId        uint64                           `json:"supplier_id"`           // 供应商id，必填
	DocumentCode      string                           `json:"document_code"`         // 单据编号
	VoucherNumber     string                           `json:"voucher_number"`        // 凭证单号
	ReceiveTime       tools.QueryTime                  `json:"receive_date"`          // 收货日期，必
	AuditStatus       common_system.OrderStatus        `json:"audit_status"`          // 审核状态
	AuditerId         uint64                           `json:"auditer_id"`            // 审核人ID （关联user.id）
	AuditerName       string                           `json:"auditer_name"`          // 审核人名称
	Remark            string                           `json:"remark"`                // 备注
	DyeUnitUseOrderNo string                           `json:"dye_unit_use_order_no"` // 染厂用坯单号
	ItemData          []AddGFMPurchaseReceiveItemParam `json:"item_data"`             // 坯布信息
}

func (r UpdateGFMPurchaseReceiveParam) GetTotalPWR() (totalPrice int, totalWeight int, totalRoll int) {
	for _, v := range r.ItemData {
		totalRoll += v.Roll
		aItemTotalWeight := 0
		aItemTotalPrice := 0
		for _, v2 := range v.ItemFCData {
			totalWeight += v2.Weight
			aItemTotalWeight += v2.Weight
		}
		// 安全相除计算并四舍五入
		temp1 := tools.DecimalDiv(float64(aItemTotalWeight*v.SinglePrice), vars.WeightUnitPriceMult)
		tempPrice, _ := decimal.NewFromFloat(temp1).Round(0).Float64()
		aItemTotalPrice = int(tempPrice) + v.OtherPrice
		totalPrice += aItemTotalPrice
	}
	return
}

type UpdateGFMPurchaseReceiveData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateGFMPurchaseReceiveStatusParam struct {
	structure_base.Param
	Id          tools.QueryIntList        `json:"id"`
	AuditStatus common_system.OrderStatus `json:"audit_status"`
}

type UpdateGFMPurchaseReceiveStatusData struct {
	structure_base.ResponseData
	Id      uint64 `json:"id"`
	OrderNo string
}

type UpdateGfmPurchaseReceiveBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type GetGFMPurchaseReceiveQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetGFMPurchaseReceiveListQuery struct {
	structure_base.ListQuery
	JudgeExit         bool
	GetPass           bool
	IsDropList        uint64 `form:"is_drop_list"`        // 回收站
	SaleSystemId      uint64 `form:"sale_system_id"`      // 营销体系id，必填
	RecipientEntityId uint64 `form:"recipient_entity_id"` // 收货单位id，必填
	SupplierId        uint64 `form:"supplier_id"`         // 供应商id，必填
	DocumentCode      string `form:"document_code"`       // 单据编号
	VoucherNumber     string `form:"voucher_number"`      // 凭证单号
	// ReceiveTime       tools.QueryTime `json:"receive_date"`        // 收货日期，必
	AuditStatus            tools.QueryIntList    `form:"audit_status"` // 审核状态
	AuditStatusFormat      []int                 // 格式化状态
	AuditerId              uint64                `form:"auditer_id"`                // 审核人ID （关联user.id）
	AuditerName            string                `form:"auditer_name"`              // 审核人名称
	Remark                 string                `form:"remark"`                    // 备注
	OrderNos               tools.QueryStringList `form:"order_nos"`                 // 单号
	GreyFabricPurchaseId   uint64                `form:"grey_fabric_purchase_id"`   // 采购单id(一般接口调用使用)
	GreyFabricPurchaseCode string                `form:"grey_fabric_purchase_code"` // 采购单号(一般客户使用)
}

func (r GetGFMPurchaseReceiveListQuery) Adjust() {

}

type GetGFMPurchaseReceiveData struct {
	structure_base.RecordData
	BusinessClose         common_system.BusinessClose       `json:"business_close"`                       // 业务关闭 1.正常 2。关闭
	BusinessCloseName     string                            `json:"business_close_name"`                  // 业务关闭name 1.正常 2。关闭
	BusinessCloseUserId   uint64                            `json:"business_close_user_id"`               // 业务关闭操作人
	BusinessCloseUserName string                            `json:"business_close_user_name"`             // 业务关闭操作人名
	BusinessCloseTime     tools.MyTime                      `json:"business_close_time"`                  // 业务关闭时间
	SaleSystemId          uint64                            `json:"sale_system_id"`                       // 营销体系id，必填
	SaleSystemName        string                            `json:"sale_system_name" excel:"营销体系"`        // 营销体系name
	RecipientEntityId     uint64                            `json:"recipient_entity_id"`                  // 收货单位id，必填
	RecipientEntityName   string                            `json:"recipient_entity_name" excel:"收货单位"`   // 收货单位name
	SupplierId            uint64                            `json:"supplier_id"`                          // 供应商id，必填
	SupplierName          string                            `json:"supplier_name" excel:"供应商名称"`          // 供应商名称
	DocumentCode          string                            `json:"document_code" excel:"单据编号"`           // 单据编号
	VoucherNumber         string                            `json:"voucher_number" excel:"凭证单号"`          // 凭证单号
	ReceiveTime           tools.MyTime                      `json:"receive_date" excel:"收货日期"`            // 收货日期，必
	AuditStatus           common_system.OrderStatus         `json:"audit_status"`                         // 审核状态
	AuditStatusName       string                            `json:"audit_status_name" excel:"状态"`         // 审核状态名称
	AuditerId             uint64                            `json:"auditer_id"`                           // 审核人ID （关联user.id）
	AuditerName           string                            `json:"auditer_name" excel:"审核人"`             // 审核人名称
	AuditTime             tools.MyTime                      `json:"audit_time" excel:"审核时间"`              // 审核时间
	Remark                string                            `json:"remark" excel:"备注"`                    // 备注
	TotalRoll             tools.Hundred                     `json:"total_roll" excel:"总匹数"`               // 总匹数
	TotalWeight           tools.Milligram                   `json:"total_weight" excel:"总数量"`             // 总数量
	TotalPrice            tools.Cent                        `json:"total_price" excel:"总价格"`              // 总价格
	DyeUnitUseOrderNo     string                            `json:"dye_unit_use_order_no" excel:"染厂用坯单号"` // 染厂用坯单号
	ItemData              GetGFMPurchaseReceiveItemDataList `json:"item_data"`                            // 坯布信息
	SrcId                 uint64                            `json:"src_id"`                               // 坯布采购单id
}

type GetGFMPurchaseReceiveDataList []GetGFMPurchaseReceiveData

func (g GetGFMPurchaseReceiveDataList) PickById(id uint64) (data GetGFMPurchaseReceiveData) {
	for _, item := range g {
		if id == item.Id {
			data = item
			return
		}
	}
	return
}

func (g GetGFMPurchaseReceiveDataList) Adjust() {

}

type DeleteGFMPurchaseReceiveParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteGFMPurchaseReceiveData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}
