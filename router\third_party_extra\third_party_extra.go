package third_party_extra

import (
	"hcscm/server/system"
	"hcscm/server/third_party_extra"
)

func ThirdPartyExtraInit(routerGroup *system.RouterGroup) {
	personalCenter := routerGroup.Group("personal_center")
	{
		personalCenter.GET("/get", third_party_extra.GetPersonalCenter)
		personalCenter.PUT("/update", third_party_extra.UpdatePersonalCenter)
		personalCenter.GET("/list", third_party_extra.GetPersonalCenterList)
		personalCenter.DELETE("/delete", third_party_extra.DeletePersonalCenter)
	}
	userBehaviorTracking := routerGroup.Group("user_behavior_tracking")
	{
		userBehaviorTracking.POST("/create", third_party_extra.CreateUserBehaviorTracking)
		userBehaviorTracking.GET("/list", third_party_extra.GetUserBehaviorTrackingList)
		userBehaviorTracking.GET("/statistics", third_party_extra.GetUserBehaviorStatistics)
		userBehaviorTracking.POST("/track_page_view", third_party_extra.TrackPageView)
		userBehaviorTracking.POST("/track_button_click", third_party_extra.TrackButtonClick)
	}
}
