# HCSCM 代码风格和约定

## Go代码风格

### 命名约定
- **包名**: 小写，简短，有意义 (如 `server`, `service`, `model`)
- **文件名**: 小写，下划线分隔 (如 `user_service.go`, `order_model.go`)
- **函数名**: 驼峰命名法，公开函数首字母大写 (如 `GetUserInfo`, `createOrder`)
- **变量名**: 驼峰命名法，私有变量首字母小写 (如 `userID`, `orderList`)
- **常量名**: 全大写，下划线分隔 (如 `TIME_LAYOUT`, `MAX_RETRY_COUNT`)
- **结构体**: 驼峰命名法，首字母大写 (如 `UserInfo`, `OrderDetail`)

### 项目结构约定
- **分层架构**: server -> service -> aggs -> model
- **模块化**: 按业务功能划分目录 (如 `sale`, `purchase`, `stock`)
- **接口定义**: 在 `interfaces/` 目录中定义
- **公共代码**: 放在 `common/` 目录
- **外部集成**: 放在 `extern/` 目录

### 代码组织
- **初始化**: 统一在 `initialize/` 目录管理
- **配置**: 使用 `config.yaml` 和 Viper 管理
- **路由**: 在 `router/` 目录按模块组织
- **中间件**: 在 `middleware/` 目录统一管理

### 注释约定
- **包注释**: 每个包都应有包级别的注释
- **函数注释**: 公开函数必须有注释，说明功能、参数和返回值
- **Swagger注释**: API接口使用Swagger注释格式
- **TODO注释**: 使用 `// TODO:` 标记待完成的工作

### 错误处理
- 使用标准的Go错误处理模式
- 自定义错误类型在 `common/errors/` 目录
- 错误码统一管理

### 数据库约定
- 使用GORM作为ORM
- 模型定义在 `model/mysql/` 目录
- 数据访问对象(DAO)模式
- 支持主从数据库配置

### API设计
- RESTful API设计原则
- 统一的响应格式
- 使用Swagger生成API文档
- 版本控制通过路径管理