package product

import (
	"context"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	"hcscm/common/sale"
	should_collect_order_consts "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql_system "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/vars"
	"time"

	"gorm.io/gorm"
)

func GetFpmArrangeOrderIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_arrange_order_id")
}

type FpmArrangeOrderList []FpmArrangeOrder

func (r FpmArrangeOrderList) List() []FpmArrangeOrder {
	return r
}

func (r FpmArrangeOrderList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmArrangeOrderList) One() FpmArrangeOrder {
	return r[0]
}

func (r FpmArrangeOrderList) Pick(id uint64) (o FpmArrangeOrder) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

// ToSrcIdMap 将FpmArrangeOrderList转换为嵌套map结构
// 外层map的键是SrcId，值是另一个map
// 内层map的键是Id，值是对应的FpmArrangeOrder
// 这样可以通过SrcId和Id快速查找对应的FpmArrangeOrder
func (r FpmArrangeOrderList) ToSrcIdMap() map[uint64]map[uint64]FpmArrangeOrder {
	result := make(map[uint64]map[uint64]FpmArrangeOrder)

	for _, order := range r {
		// 如果外层map中还没有这个SrcId的键，则创建一个新的内层map
		if _, ok := result[order.SrcId]; !ok {
			result[order.SrcId] = make(map[uint64]FpmArrangeOrder)
		}

		// 将FpmArrangeOrder添加到对应的内层map中，键为Id
		result[order.SrcId][order.Id] = order
	}

	return result
}

// FpmArrangeOrder 成品管理-配布单
type FpmArrangeOrder struct {
	mysql_base.Order
	Id                   uint64                         `gorm:"column:id;primaryKey"`
	SrcType              cus_const.ArrangeOrderFrom     `gorm:"column:src_type"`                                                 // 来源类型（1预约，2销售）
	SrcId                uint64                         `gorm:"column:src_id"`                                                   // 来源id，必填
	SrcOrderNo           string                         `gorm:"column:src_order_no"`                                             // 来源单号，必填
	OutOrderType         cus_const.WarehouseGoodOutType `gorm:"column:out_order_type"`                                           // 出仓类型
	BusinessStatus       cus_const.BusinessStatus       `gorm:"column:business_status"`                                          // 业务状态
	SaleSystemId         uint64                         `gorm:"column:sale_system_id" relate:"sale_system_id"`                   // 营销体系id，必填
	ArrangeTime          time.Time                      `gorm:"column:arrange_time"`                                             // 配布日期
	ArrangeToWarehouseId uint64                         `gorm:"column:arrange_to_warehouse_id" relate:"arrange_to_warehouse_id"` // 调至仓库id
	WarehouseId          uint64                         `gorm:"column:warehouse_id" relate:"warehouse_id"`                       // 仓库id
	BizUnitId            uint64                         `gorm:"column:biz_unit_id" relate:"biz_unit_id"`                         // 往来单位id
	ProcessFactoryId     uint64                         `gorm:"column:process_factory_id" relate:"process_factory_id"`           // 加工厂id
	ProcessFactory       string                         `gorm:"column:process_factory" relate:"process_factory"`                 // 加工厂
	ReceiveAddr          string                         `gorm:"column:receive_addr"`                                             // 收货地址
	ReceivePhone         string                         `gorm:"column:receive_phone"`                                            // 收货电话
	ReceiveTag           string                         `gorm:"column:receive_tag"`                                              // 收货标签
	DriverId             string                         `gorm:"column:driver_id"`                                                // 司机id，逗号分割（关联user.id）
	SaleUserId           uint64                         `gorm:"column:sale_user_id" relate:"sale_user_id"`                       // 销售员id
	SaleFollowerId       uint64                         `gorm:"column:sale_follower_id" relate:"sale_follower_id"`               // 销售跟单员id
	StoreKeeperId        uint64                         `gorm:"column:store_keeper_id" relate:"store_keeper_id"`                 // 仓管员id（关联user.id）
	LogisticsCompanyId   uint64                         `gorm:"column:logistics_company_id" relate:"logistics_company_id"`       // 物流公司id
	LogisticsCompany     string                         `gorm:"column:logistics_company" relate:"logistics_company"`             // 物流公司
	InternalRemark       string                         `gorm:"column:internal_remark"`                                          // 内部备注
	SaleRemark           string                         `gorm:"column:sale_remark"`                                              // 销售备注
	TotalRoll            int                            `gorm:"column:total_roll"`                                               // 匹数总计
	TotalWeight          int                            `gorm:"column:total_weight"`                                             // 数量总计
	TotalLength          int                            `gorm:"column:total_length"`                                             // 长度总计
	UnitId               uint64                         `gorm:"column:unit_id"  relate:"unit_id"`                                // 单位id
	DepartmentId         uint64                         `gorm:"column:department_id"`                                            // 下单用户所属部门
	OrderNo              string                         `gorm:"column:order_no"`                                                 // 单据编号
	Number               int                            `gorm:"column:number"`                                                   // 编号流水：每日重新更新
	SaleMode             sale.SaleOrderType             `gorm:"column:sale_mode"`                                                // 订单类型 1大货 2剪板 3客订大货 4客订剪板
	// 销售单用
	Contacts            string                   `gorm:"column:contacts"`                // 联系人
	SendProductType     sale.SendProductType     `gorm:"column:send_product_type"`       // 出货类型 1出货 2销调
	SaleGroupId         uint64                   `gorm:"column:sale_group_id"`           // 销售群体id
	SettleType          sale.SettleType          `gorm:"column:settle_type"`             // 结算类型
	LogisticsArea       string                   `gorm:"column:logistics_area"`          // 物流区域
	PostageItems        sale.PostageItems        `gorm:"column:postage_items"`           // 邮费项目 1包邮 2不包邮
	SendProductRemark   string                   `gorm:"column:send_product_remark"`     // 出货备注
	IsWithTaxRate       bool                     `gorm:"column:is_with_tax_rate"`        // 单价是否含税
	TaxRate             int                      `gorm:"column:tax_rate"`                // 税率
	SameColorSameDyeLot bool                     `gorm:"column:same_color_same_dye_lot"` // 是否同色同缸
	PickUpGoodsInOrder  bool                     `gorm:"column:pick_up_goods_in_order"`  // 是否齐单提货
	PreBusinessStatus   cus_const.BusinessStatus `gorm:"column:pre_business_status"`     // （针对齐单）送货单审核前的业务状态
}

// 查询后的钩子
func (r *FpmArrangeOrder) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmArrangeOrder) GetId() uint64 {
	return r.Id
}

// TableName FpmArrangeOrder 表名
func (FpmArrangeOrder) TableName() string {
	return "fpm_arrange_order"
}

func (r FpmArrangeOrder) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmArrangeOrder) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (FpmArrangeOrder) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmArrangeOrderNotExist
}

func (FpmArrangeOrder) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmArrangeOrderAlreadyExist
}

func (r FpmArrangeOrder) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	mysql_system.CommonDataSeparate(ctx, r, cond)
}

func (r FpmArrangeOrder) GetOtherFields() []string {
	return []string{
		"creator_id", // 创建人
		"updater_id", // 更新人
	}
}

func NewFpmArrangeOrder(
	ctx context.Context,
	p *structure.AddFpmArrangeOrderParam,
) (r FpmArrangeOrder) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.SrcType = p.SrcType
	r.SrcId = p.SrcId
	r.SrcOrderNo = p.SrcOrderNo
	r.OutOrderType = p.OutOrderType
	r.SaleSystemId = p.SaleSystemId
	r.ArrangeTime = p.ArrangeTime.ToTimeYMD()
	r.ArrangeToWarehouseId = p.ArrangeToWarehouseId
	r.WarehouseId = p.WarehouseId
	r.BizUnitId = p.BizUnitId
	r.ProcessFactoryId = p.ProcessFactoryId
	r.ReceiveAddr = p.ReceiveAddr
	r.ReceivePhone = p.ReceivePhone
	r.ReceiveTag = p.ReceiveTag
	r.DriverId = p.DriverId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.StoreKeeperId = p.StoreKeeperId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.InternalRemark = p.InternalRemark
	r.SaleRemark = p.SaleRemark
	// 销售单用
	r.SendProductType = p.SendProductType
	r.Contacts = p.Contacts
	r.SaleGroupId = p.SaleGroupId
	r.SettleType = p.SettleType
	r.LogisticsArea = p.LogisticsArea
	r.PostageItems = p.PostageItems
	r.SendProductRemark = p.SendProductRemark
	r.IsWithTaxRate = p.IsWithTaxRate
	r.TaxRate = p.TaxRate
	r.ProcessFactory = p.ProcessFactory
	r.LogisticsCompany = p.LogisticsCompany
	r.SaleMode = p.SaleMode
	r.BusinessClose = common_system.BusinessCloseNo
	r.AuditStatus = common_system.OrderStatusPendingAudit
	r.SameColorSameDyeLot = p.SameColorSameDyeLot
	r.PickUpGoodsInOrder = p.PickUpGoodsInOrder
	return
}

func (r *FpmArrangeOrder) UpdateFpmArrangeOrder(
	ctx context.Context,
	p *structure.UpdateFpmArrangeOrderParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.ArrangeTime = p.ArrangeTime.ToTimeYMD()
	r.ArrangeToWarehouseId = p.ArrangeToWarehouseId
	r.WarehouseId = p.WarehouseId
	r.BizUnitId = p.BizUnitId
	r.ProcessFactoryId = p.ProcessFactoryId
	r.ReceiveAddr = p.ReceiveAddr
	r.ReceivePhone = p.ReceivePhone
	r.ReceiveTag = p.ReceiveTag
	r.DriverId = p.DriverId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.StoreKeeperId = p.StoreKeeperId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.InternalRemark = p.InternalRemark
	r.SaleRemark = p.SaleRemark
	r.SaleMode = p.SaleMode
}

func (p *FpmArrangeOrder) ToAddProductSaleShouldCollectOrderParam(fpmArrangeOrderItems FpmArrangeOrderItemList, fpmArrangeOrderItemFcList FpmArrangeOrderItemFcList) (data *shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam) {
	var (
		items = make(shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailParamList, 0)
	)
	r := &shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam{}
	r.SrcId = p.Id
	r.SrcOrderNo = p.OrderNo
	r.SrcOrderType = should_collect_order_consts.SrcOrderTypeArrange
	r.SaleSystemId = p.SaleSystemId
	r.CustomerId = p.BizUnitId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.SettleType = common_system.SettleType(p.SettleType)
	r.OrderTime = tools.QueryTime(time.Now().Format("2006-01-02"))
	r.IsWithTaxRate = p.IsWithTaxRate
	r.TaxRate = p.TaxRate
	r.SaleMode = p.SaleMode
	if vars.IsTakeMessageToSaleShouldCollectOrder {
		r.OrderRemark = p.SendProductRemark
	}
	for _, fpmArrangeOrderItem := range fpmArrangeOrderItems {
		itemFcList := fpmArrangeOrderItemFcList.PickList(fpmArrangeOrderItem.Id)
		o := shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailParam{}
		o.SrcDetailId = fpmArrangeOrderItem.Id
		o.MaterialId = fpmArrangeOrderItem.ProductId
		o.Code = fpmArrangeOrderItem.ProductCode
		o.Name = fpmArrangeOrderItem.ProductName
		o.ProductColorId = fpmArrangeOrderItem.ProductColorId
		o.ProductColorCode = fpmArrangeOrderItem.ProductColorCode
		o.ProductColorName = fpmArrangeOrderItem.ProductColorName
		o.DyelotNumber = fpmArrangeOrderItem.DyeFactoryDyelotNumber
		o.Roll = fpmArrangeOrderItem.ArrangeRoll
		o.Weight = fpmArrangeOrderItem.ArrangeWeight
		o.WeightError = fpmArrangeOrderItem.WeightError
		o.ActuallyWeight = fpmArrangeOrderItem.ActuallyWeight
		o.SettleErrorWeight = fpmArrangeOrderItem.SettleErrorWeight
		o.SettleWeight = fpmArrangeOrderItem.SettleWeight
		o.StandardSalePrice = fpmArrangeOrderItem.StandardSalePrice
		o.SaleLevelId = fpmArrangeOrderItem.SaleLevelId
		o.OffsetSalePrice = fpmArrangeOrderItem.OffsetSalePrice
		o.SalePrice = fpmArrangeOrderItem.SalePrice
		o.StandardWeightError = fpmArrangeOrderItem.StandardWeightError
		o.OffsetWeightError = fpmArrangeOrderItem.OffsetWeightError
		o.AdjustWeightError = fpmArrangeOrderItem.AdjustWeightError
		o.Length = fpmArrangeOrderItem.ArrangeLength
		o.StandardLengthCutSalePrice = fpmArrangeOrderItem.StandardLengthCutSalePrice
		o.OffsetLengthCutSalePrice = fpmArrangeOrderItem.OffsetLengthCutSalePrice
		o.LengthCutSalePrice = fpmArrangeOrderItem.LengthCutSalePrice
		o.OtherPrice = fpmArrangeOrderItem.OtherPrice
		o.Remark = fpmArrangeOrderItem.Remark
		o.SaleTaxRate = fpmArrangeOrderItem.SaleTaxRate
		o.MeasurementUnitId = fpmArrangeOrderItem.UnitId
		o.AuxiliaryUnitId = fpmArrangeOrderItem.AuxiliaryUnitId
		if o.AuxiliaryUnitId == 0 {
			o.AuxiliaryUnitId = fpmArrangeOrderItem.UnitId
		}
		o.SumStockId = fpmArrangeOrderItem.SumStockId
		o.SalePlanOrderItemId = fpmArrangeOrderItem.SalePlanOrderItemId
		for _, itemFc := range itemFcList {
			o.ItemFcList = append(o.ItemFcList, shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailFcParam{
				SrcDetailFcId:     itemFc.Id,
				SrcDetailId:       itemFc.ParentId,
				Roll:              itemFc.Roll,
				WarehouseId:       itemFc.WarehouseId,
				WarehouseBinId:    itemFc.WarehouseBinId,
				VolumeNumber:      itemFc.VolumeNumber,
				ArrangeOrderNo:    itemFc.ArrangeOrderNo,
				StockId:           itemFc.StockId,
				SumStockId:        itemFc.SumStockId,
				BaseUnitWeight:    itemFc.BaseUnitWeight,
				PaperTubeWeight:   itemFc.PaperTubeWeight,
				UnitId:            itemFc.UnitId,
				Length:            itemFc.Length,
				SettleErrorWeight: itemFc.SettleErrorWeight,
				WeightError:       itemFc.WeightError,
				ActuallyWeight:    itemFc.ActuallyWeight,
				SettleWeight:      itemFc.SettleWeight,
			})
		}
		// o.SalePlanOrderItemId = fpmArrangeOrderItem.QuoteOrderitem
		items = append(items, o)
	}
	r.ItemData = items
	data = r
	return
}
