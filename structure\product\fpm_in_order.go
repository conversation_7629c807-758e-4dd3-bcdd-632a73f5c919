package product

import (
	"fmt"
	common_basic "hcscm/common/basic_data"
	"hcscm/common/errors"
	common "hcscm/common/product"
	cus_const "hcscm/common/product"
	iconst "hcscm/common/product"
	common_sale "hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	"hcscm/middleware"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/vars"
	"time"
)

type AddFpmInOrderParamList []AddFpmInOrderParam

func (r AddFpmInOrderParamList) Adjust() {

}

type AddFpmInOrderParam struct {
	structure_base.Param
	InOrderType     iconst.WarehouseGoodInType // 入仓类型
	OrderNoPre      string                     // 单号前缀
	ItemData        AddFpmInOrderItemParamList `json:"item_data"`         // 成品信息
	SaleSystemId    uint64                     `json:"sale_system_id"`    // 营销体系id，必填
	BizUnitId       uint64                     `json:"biz_unit_id"`       // 往来单位id（供应商，客户）
	WarehouseId     uint64                     `json:"warehouse_id"`      // 仓库id
	WarehouseInTime tools.QueryTime            `json:"warehouse_in_time"` // 进仓时间
	StoreKeeperId   uint64                     `json:"store_keeper_id"`   // 仓管员id（关联employee.id）
	Remark          string                     `json:"remark"`            // 备注
	SrcId           uint64                     `json:"src_id"`            // 来源id
	SrcOrderNo      string                     `json:"src_order_no"`      // 来源单号
	OutWarehouseId  uint64                     `json:"out_warehouse_id"`  // 出仓仓库id
	VoucherNumber   string                     `json:"voucher_number"`    // 凭证号
	TextureUrl      string                     `json:"texture_url"`       // 凭证图片URL
	GenOrderNoTime  time.Time                  // 获取单据时间 todo:暂时使用，使用后删除
	SaleMode        common_sale.SaleOrderType  `json:"sale_mode"` // 订单类型 1大货 2剪板 3客订大货 4客订剪板
}

type AddFpmInOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

func checkItemFCDataVolumeNumbers(itemData AddFpmInOrderItemParamList) error {
	volumeVatMap := make(map[string]bool)
	for _, item := range itemData {
		for _, fc := range item.ItemFCData {
			if fc.VolumeNumber > 0 {
				key := fmt.Sprintf("%s:%d", item.DyeFactoryDyelotNumber, fc.VolumeNumber)
				if _, exists := volumeVatMap[key]; exists {
					return middleware.ErrorLog(errors.NewError(errors.ErrorVolumeNumberRepeat))
				}
				volumeVatMap[key] = true
			}
		}
	}

	return nil
}

func (param *AddFpmInOrderParam) CheckVNumber() error {
	return checkItemFCDataVolumeNumbers(param.ItemData)
}

func (param *UpdateFpmInOrderParam) CheckVNumber() error {
	return checkItemFCDataVolumeNumbers(param.ItemData)
}

type UpdateFpmInOrderParam struct {
	structure_base.Param
	InOrderType     iconst.WarehouseGoodInType // 入仓类型
	ItemData        AddFpmInOrderItemParamList `json:"item_data"` // 坯布信息
	Id              uint64                     `json:"id"`
	SaleSystemId    uint64                     `json:"sale_system_id"`    // 营销体系id，必填
	BizUnitId       uint64                     `json:"biz_unit_id"`       // 往来单位id（供应商，客户）
	WarehouseId     uint64                     `json:"warehouse_id"`      // 仓库id
	WarehouseInTime tools.QueryTime            `json:"warehouse_in_time"` // 进仓时间
	StoreKeeperId   uint64                     `json:"store_keeper_id"`   // 仓管员id（关联employee.id）
	Remark          string                     `json:"remark"`            // 备注
	OutWarehouseId  uint64                     `json:"out_warehouse_id"`  // 出仓仓库id
	VoucherNumber   string                     `json:"voucher_number"`    // 凭证号
	TextureUrl      string                     `json:"texture_url"`       // 凭证图片URL
	SaleMode        common_sale.SaleOrderType  `json:"sale_mode"`         // 订单类型 1大货 2剪板 3客订大货 4客订剪板
}

type UpdateFpmInOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// 列表表使用
func (r AddFpmInOrderParam) GetTotalPWR(inType iconst.WarehouseGoodInType) (totalPrice int, totalWeight int, totalRoll int, totalLength int) {
	// if inType == iconst.WarehouseGoodInTypePurchase {
	for _, v := range r.ItemData {
		tw, tl, te, tpp, tp := 0, 0, 0, 0, 0
		for _, v2 := range v.ItemFCData {
			tw += v2.BaseUnitWeight
			tl += v2.Length
			te += v2.WeightError + v2.SettleErrorWeight
			tpp += v2.PaperTubeWeight
		}

		tsw := tw - te

		weightPrice := tools.DecimalDiv(float64(tsw*v.UnitPrice), vars.WeightUnitPriceMult)
		// 2024-09-10长度修改为10000进位
		lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), vars.LengthUnitPriceMult)
		// lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), 10000)
		tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
		tp = tempPrice + v.OtherPrice
		// 安全相除计算并四舍五入
		totalPrice += tp
		totalRoll += v.InRoll
		totalLength += tl
		totalWeight += tw
	}
	// }
	return
}

func (r *AddFpmInOrderParam) Swap2ListParam(req GetFpmInternalAllocateOutOrderData) {
	r.InOrderType = cus_const.WarehouseGoodInTypeInternalAllocate
	r.OrderNoPre = "FPAW--LY"
	r.SaleSystemId = req.SaleSystemId
	r.WarehouseId = req.InWarehouseId
	r.WarehouseInTime = tools.QueryTime(time.Now().Format("2006-01-02"))
	r.StoreKeeperId = req.StoreKeeperId
	r.Remark = req.Remark
	r.SrcId = req.Id
	r.SrcOrderNo = req.OrderNo
	r.OutWarehouseId = req.OutWarehouseId
}

func (r UpdateFpmInOrderParam) GetTotalPWR(inType iconst.WarehouseGoodInType) (totalPrice int, totalWeight int, totalRoll int, totalLength int) {
	// if inType == iconst.WarehouseGoodInTypePurchase {
	for _, v := range r.ItemData {
		tw, tl, te, tpp, tp := 0, 0, 0, 0, 0
		for _, v2 := range v.ItemFCData {
			tw += v2.BaseUnitWeight
			tl += v2.Length
			te += v2.WeightError + v2.SettleErrorWeight
			tpp += v2.PaperTubeWeight
		}

		tsw := tw - te

		weightPrice := tools.DecimalDiv(float64(tsw*v.UnitPrice), vars.WeightUnitPriceMult)
		// 2024-09-10长度修改为10000进位
		lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), vars.LengthUnitPriceMult)
		// lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), 10000)
		tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
		tp = tempPrice + v.OtherPrice
		// 安全相除计算并四舍五入
		totalPrice += tp
		totalRoll += v.InRoll
		totalLength += tl
		totalWeight += tw
	}
	// }
	return
}

type UpdateFpmInOrderBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateFpmInOrderStatusParam struct {
	structure_base.Param
	Id          tools.QueryIntList        `json:"id"`
	AuditStatus common_system.OrderStatus `json:"audit_status"`
}

type UpdateFpmInOrderStatusData struct {
	structure_base.ResponseData
	WarehouseInTime   tools.MyTime             `json:"warehouse_in_time"`   // 进仓时间
	OrderNo           string                   `json:"order_no"`            // 订单号
	MainUnitTypeId    uint64                   `json:"main_unit_type_id"`   // 主要往来单位类型id
	MainUnitTypeName  string                   `json:"main_unit_type_name"` // 主要往来单位类型名称
	MainBizUnitType   common_basic.BizUnitType `json:"main_biz_unit_type"`  // 主要往来单位类型类型
	FpmInOrderDataMap map[uint64]GetFpmInOrderData
}

type GetFpmInOrderQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmInOrderListQuery struct {
	structure_base.ListQuery
	InOrderType            iconst.WarehouseGoodInType  `form:"in_order_type"`              // 入仓类型
	SaleSystemId           uint64                      `form:"sale_system_id"`             // 营销体系id，必填
	BizUnitId              uint64                      `form:"biz_unit_id"`                // 往来单位id（供应商，客户）
	WarehouseId            uint64                      `form:"warehouse_id"`               // 仓库id
	WarehouseInTime        tools.QueryTime             `form:"warehouse_in_time"`          // 进仓时间
	InTimeBegin            tools.QueryTime             `form:"in_time_begin"`              // 进仓时间
	InTimeEnd              tools.QueryTime             `form:"in_time_end"`                // 进仓时间
	StoreKeeperId          uint64                      `form:"store_keeper_id"`            // 仓管员id（关联user.id）
	Remark                 string                      `form:"remark"`                     // 备注
	ReserveInt             int                         `form:"reserve_int"`                // 预留字段int，必填
	ReserveStr             string                      `form:"reserve_str"`                // 预留字段str，(退货原因，内部调拨出仓单)
	BusinessClose          common_system.BusinessClose `form:"business_close"`             // 业务关闭
	BusinessCloseUserId    uint64                      `form:"business_close_user_id"`     // 业务关闭操作人
	BusinessCloseUserName  string                      `form:"business_close_user_name"`   // 业务关闭操作人名
	BusinessCloseTime      tools.QueryTime             `form:"business_close_time"`        // 业务关闭时间
	DepartmentId           uint64                      `form:"department_id"`              // 下单用户所属部门
	OrderNo                string                      `form:"order_no"`                   // 单据编号
	AuditStatus            tools.QueryIntList          `form:"audit_status"`               // 审核状态
	AuditorId              uint64                      `form:"auditor_id"`                 // 审核人ID （关联user.id）
	AuditorName            string                      `form:"auditor_name"`               // 审核人名称
	AuditTime              tools.QueryTime             `form:"audit_time"`                 // 审核时间
	SrcId                  uint64                      `form:"src_id"`                     // 来源id
	SrcOrderNos            []string                    `form:"src_order_nos"`              // 来源单号
	SrcOrderNo             string                      `form:"src_order_no"`               // 来源单号
	OutWarehouseId         uint64                      `form:"out_warehouse_id"`           // 出仓仓库id
	ProductCodeOrName      string                      `form:"product_code_or_name"`       // 成品编号或名称
	ProductIds             []uint64                    `form:"-"`                          // 成品ids(成品搜索用)
	ProductColorCodeOrName string                      `form:"product_color_code_or_name"` // 成品颜色编号或名称
	ProductColorIds        []uint64                    `form:"-"`                          // 成品颜色ids(成品颜色搜索用)
	DyelotNumber           string                      `form:"dyelot_number"`              // 缸号
	OrderIds               []uint64                    `form:"-"`                          // 详情内搜索使用
	OrderNos               tools.QueryStringList       `form:"-"`                          // 详情内搜索使用
	CreateStartDate        tools.QueryTime             `form:"create_start_date"`          // 创建时间开始
	CreateEndDate          tools.QueryTime             `form:"create_end_date"`            // 创建时间结束
	AuditStartDate         tools.QueryTime             `form:"audit_start_date"`           // 审核时间开始
	AuditEndDate           tools.QueryTime             `form:"audit_end_date"`             // 审核时间结束
	VoucherNumber          string                      `form:"voucher_number"`             // 凭证
	// JudgeQuoteAllocateStatus bool
	AvailableOnly bool               `form:"-"`
	CustomerId    uint64             `form:"customer_id"`     // 客户id
	QueryStr      string             `form:"query_str"`       // 小程序供应商和单号搜索
	ProcessUnitId uint64             `form:"process_unit_id"` // 加工单位id
	Ids           tools.QueryIntList `form:"-"`
	IsNotPage     bool               `form:"-"`
	WithItems     bool               `form:"-"`
}

type GetMPFpmInOrderListQuery struct {
	structure_base.ListQuery
	InOrderType            iconst.WarehouseGoodInType `form:"in_order_type"`              // 入仓类型
	SaleSystemId           uint64                     `form:"sale_system_id"`             // 营销体系id，必填
	BizUnitId              uint64                     `form:"biz_unit_id"`                // 往来单位id（供应商，客户）
	BizUnitIds             []uint64                   `form:"-"`                          // 往来单位ids用于搜索供应商名称
	OrderNo                string                     `form:"order_no"`                   // 单据编号
	AuditStatus            tools.QueryIntList         `form:"audit_status"`               // 审核状态
	AuditorId              uint64                     `form:"auditor_id"`                 // 审核人ID （关联user.id）
	AuditorName            string                     `form:"auditor_name"`               // 审核人名称
	AuditTime              tools.QueryTime            `form:"audit_time"`                 // 审核时间
	SrcId                  uint64                     `form:"src_id"`                     // 来源id
	SrcOrderNo             string                     `form:"src_order_no"`               // 来源单号
	ProductCodeOrName      string                     `form:"product_code_or_name"`       // 成品编号或名称
	ProductIds             []uint64                   `form:"-"`                          // 成品ids(成品搜索用)
	ProductColorCodeOrName string                     `form:"product_color_code_or_name"` // 成品颜色编号或名称
	ProductColorIds        []uint64                   `form:"-"`                          // 成品颜色ids(成品颜色搜索用)
	DyelotNumber           string                     `form:"dyelot_number"`              // 缸号
	OrderIds               []uint64                   `form:"-"`                          // 详情内搜索使用
	VoucherNumber          string                     `form:"voucher_number"`             // 凭证
	InTimeBegin            tools.QueryTime            `form:"in_time_begin"`              // 进仓时间
	InTimeEnd              tools.QueryTime            `form:"in_time_end"`                // 进仓时间
}

func (r GetMPFpmInOrderListQuery) Adjust() {

}

func (r GetFpmInOrderListQuery) Adjust() {

}

type GetFpmInOrderData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ItemData        GetFpmInOrderItemDataList  `json:"item_data"`         // 成品信息
	InOrderType     iconst.WarehouseGoodInType `json:"in_order_type"`     // 入仓类型
	SaleSystemId    uint64                     `json:"sale_system_id"`    // 营销体系id，必填
	BizUnitId       uint64                     `json:"biz_unit_id"`       // 往来单位id（供应商，客户）
	WarehouseId     uint64                     `json:"warehouse_id"`      // 仓库id
	WarehouseInTime tools.MyTime               `json:"warehouse_in_time"` // 进仓时间
	StoreKeeperId   uint64                     `json:"store_keeper_id"`   // 仓管员id（关联user.id）
	Remark          string                     `json:"remark"`            // 备注
	// ReserveInt            int                        `json:"reserve_int"`              // 预留字段int，必填
	// ReserveStr            string                     `json:"reserve_str"`              // 预留字段str，(退货原因，内部调拨出仓单)
	BusinessClose         common_system.BusinessClose `json:"business_close"`           // 业务关闭
	BusinessCloseUserId   uint64                      `json:"business_close_user_id"`   // 业务关闭操作人
	BusinessCloseUserName string                      `json:"business_close_user_name"` // 业务关闭操作人名
	BusinessCloseTime     tools.MyTime                `json:"business_close_time"`      // 业务关闭时间
	DepartmentId          uint64                      `json:"department_id"`            // 下单用户所属部门
	OrderNo               string                      `json:"order_no"`                 // 单据编号
	AuditStatus           common_system.OrderStatus   `json:"audit_status"`             // 审核状态
	AuditorId             uint64                      `json:"auditor_id"`               // 审核人ID （关联user.id）
	AuditorName           string                      `json:"auditor_name"`             // 审核人名称
	AuditTime             tools.MyTime                `json:"audit_time"`               // 审核时间
	TotalRoll             int                         `json:"total_roll"`               // 匹数总计
	TotalWeight           int                         `json:"total_weight"`             // 数量总计
	TotalLength           int                         `json:"total_length"`             // 长度总计
	TotalPrice            int                         `json:"total_price"`              // 单据金额
	UnitId                uint64                      `json:"unit_id"`                  // 单位id
	AuxiliaryUnitId       uint64                      `json:"auxiliary_unit_id"`        // 辅助单位id
	SrcId                 uint64                      `json:"src_id"`                   // 来源id
	SrcOrderNo            string                      `json:"src_order_no"`             // 来源单号
	OutWarehouseId        uint64                      `json:"out_warehouse_id"`         // 出仓仓库id
	SaleMode              common_sale.SaleOrderType   `json:"sale_mode"`                // 订单类型 1大货 2剪板 3客订大货 4客订剪板

	// 转义
	BusinessCloseName string `json:"business_close_name"`             // 业务关闭Name
	AuditStatusName   string `json:"audit_status_name"`               // 审核状态Name
	UnitName          string `json:"unit_name"`                       // 单位name
	AuxiliaryUnitName string `json:"auxiliary_unit_name"`             // 辅助单位名称
	InOrderTypeName   string `json:"in_order_type_name"`              // 入仓类型name
	SaleSystemName    string `json:"sale_system_name" excel:"营销体系名称"` // 营销体系name
	BizUnitName       string `json:"biz_unit_name" excel:"供应商名称"`     // 供应商name
	StoreKeeperName   string `json:"store_keeper_name"`               // 仓管员name
	WarehouseName     string `json:"warehouse_name" excel:"仓库名称"`     // 仓库name
	OutWarehouseName  string `json:"out_warehouse_name"`              // out仓库name

	VoucherNumber string `json:"voucher_number"` // 凭证号
	TextureUrl    string `json:"texture_url"`    // 凭证图片URL

	ParentOrderNo          string `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string `json:"quote_order_no"`            // 引用数据单号
	ProductCode            string `json:"product_code"`              // 成品编号
	ProductName            string `json:"product_name"`              // 成品名称
	DyeFactoryColorCode    string `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductRemark          string `json:"product_remark"`            // 成品备注
	ProductCraft           string `json:"product_craft"`             // 成品工艺
	ProductIngredient      string `json:"product_ingredient"`        // 成品成分
	QuoteRoll              int    `json:"quote_roll"`                // 引用数据匹数(件)，乘100存
	QuoteTotalWeight       int    `json:"quote_total_weight"`        // 引用数据总数量(公斤)，乘10000存
	InRoll                 int    `json:"in_roll"`                   // 进仓件数(件)，乘100存
	WeightError            int    `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int    `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleWeight           int    `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	QuoteWeight            int    `json:"quote_weight"`              // 引用数据重(公斤)，乘10000存
	UnitPrice              int    `json:"unit_price"`                // 单价(元)，乘10000存
	InLength               int    `json:"in_length"`                 // 进仓长度，乘100存
	QuoteLength            int    `json:"quote_length"`              // 引用数据长度，乘100存
	LengthUnitPrice        int    `json:"length_unit_price"`         // 长度单价，乘10000存
	OtherPrice             int    `json:"other_price"`               // 其他金额(元)，乘100存(数量单价*结算数量)+(长度单价*进仓长度)+其他金额
	ProductColorCode       string `json:"product_color_code"`        // 成品颜色编号
	ProductColorName       string `json:"product_color_name"`        // 成品颜色名称
	ActuallyWeight         int    `json:"actually_weight"`           // 码单数量
	SettleErrorWeight      int    `json:"settle_error_weight"`       // 结算空差(公斤)，乘10000存
	// 转义
	CustomerName     string `json:"customer_name"`      // 所属客户name
	ProductLevelName string `json:"product_level_name"` // 成品等级name
	// 调拨信息
	AllocateRoll   int `json:"allocate_roll"`   // 调拨匹数
	AllocateWeight int `json:"allocate_weight"` // 调拨数量
	AllocateLength int `json:"allocate_length"` // 调拨长度
	// 出仓信息
	OutRoll             int    `json:"out_roll"`                // 出仓匹数
	OutWeight           int    `json:"out_weight"`              // 出仓数量
	OutLength           int    `json:"out_length"`              // 出仓长度
	SalePlanOrderItemId uint64 `json:"sale_plan_order_item_id"` // 成品销售计划单子项信息id
	SalePlanOrderItemNo string `json:"sale_plan_order_item_no"` // 成品销售计划单子项单号
	MergeWeightInfo     string `json:"merge_weight_info"`       // 拼接所在单商品信息的数量及单位
	SaleModeName        string `json:"sale_mode_name"`          // 订单类型
	SaleAllocateOutId   uint64 `json:"sale_allocate_out_id"`    // 调拨出仓单id
	SaleUserId          uint64 `json:"sale_user_id"`
	SaleFollowerId      uint64 `json:"sale_follower_id"`
	DriverId            string `json:"driver_id"`
	LogisticsCompanyId  uint64 `json:"logistics_company_id"`
	ArrangeId           uint64 `json:"arrange_id"`
	// SumStockId             uint64 `json:"sum_stock_id"`              // 汇总库存成品id
	// ParentId               uint64 `json:"parent_id"`                 // 父id（单号id）
	QuoteOrderItemId uint64 `json:"quote_order_item_id"` // 引用数据单物料那条id
	// ProductId              uint64 `json:"product_id"`                // 成品id
	// CustomerId             uint64 `json:"customer_id"`               // 所属客户id
	// ProductColorId         uint64 `json:"product_color_id"`          // 成品颜色id
	// ProductLevelId         uint64 `json:"product_level_id"`          // 成品等级
	// ArrangeOrderItemId     uint64 `json:"arrange_order_item_id"`     // 配布单分录行id
}

type GetMPFpmInOrderData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ItemData        GetFpmInOrderItemDataList  `json:"item_data"`         // 成品信息
	InOrderType     iconst.WarehouseGoodInType `json:"in_order_type"`     // 入仓类型
	SaleSystemId    uint64                     `json:"sale_system_id"`    // 营销体系id，必填
	BizUnitId       uint64                     `json:"biz_unit_id"`       // 往来单位id（供应商，客户）
	WarehouseId     uint64                     `json:"warehouse_id"`      // 仓库id
	WarehouseInTime tools.MyTime               `json:"warehouse_in_time"` // 进仓时间
	StoreKeeperId   uint64                     `json:"store_keeper_id"`   // 仓管员id（关联user.id）
	Remark          string                     `json:"remark"`            // 备注
	// ReserveInt            int                        `json:"reserve_int"`              // 预留字段int，必填
	// ReserveStr            string                     `json:"reserve_str"`              // 预留字段str，(退货原因，内部调拨出仓单)
	BusinessClose         common_system.BusinessClose `json:"business_close"`           // 业务关闭
	BusinessCloseUserId   uint64                      `json:"business_close_user_id"`   // 业务关闭操作人
	BusinessCloseUserName string                      `json:"business_close_user_name"` // 业务关闭操作人名
	BusinessCloseTime     tools.MyTime                `json:"business_close_time"`      // 业务关闭时间
	DepartmentId          uint64                      `json:"department_id"`            // 下单用户所属部门
	OrderNo               string                      `json:"order_no"`                 // 单据编号
	AuditStatus           common_system.OrderStatus   `json:"audit_status"`             // 审核状态
	AuditorId             uint64                      `json:"auditor_id"`               // 审核人ID （关联user.id）
	AuditorName           string                      `json:"auditor_name"`             // 审核人名称
	AuditTime             tools.MyTime                `json:"audit_time"`               // 审核时间
	TotalRoll             int                         `json:"total_roll"`               // 匹数总计
	TotalWeight           int                         `json:"total_weight"`             // 数量总计
	TotalLength           int                         `json:"total_length"`             // 长度总计
	TotalPrice            int                         `json:"total_price"`              // 单据金额
	UnitId                uint64                      `json:"unit_id"`                  // 单位id
	SrcId                 uint64                      `json:"src_id"`                   // 来源id
	SrcOrderNo            string                      `json:"src_order_no"`             // 来源单号
	OutWarehouseId        uint64                      `json:"out_warehouse_id"`         // 出仓仓库id
	VoucherNumber         string                      `json:"voucher_number"`           // 凭证号
	TextureUrl            string                      `json:"texture_url"`              // 凭证图片URL
	SaleMode              common_sale.SaleOrderType   `json:"sale_mode"`                // 订单类型 1大货 2剪板 3客订大货 4客订剪板

	// 转义
	BusinessCloseName string `json:"business_close_name"`             // 业务关闭Name
	AuditStatusName   string `json:"audit_status_name"`               // 审核状态Name
	UnitName          string `json:"unit_name"`                       // 单位name
	InOrderTypeName   string `json:"in_order_type_name"`              // 入仓类型name
	SaleSystemName    string `json:"sale_system_name" excel:"营销体系名称"` // 营销体系name
	BizUnitName       string `json:"biz_unit_name" excel:"供应商名称"`     // 供应商name
	StoreKeeperName   string `json:"store_keeper_name"`               // 仓管员name
	WarehouseName     string `json:"warehouse_name" excel:"仓库名称"`     // 仓库name
	OutWarehouseName  string `json:"out_warehouse_name"`              // out仓库name

	ParentOrderNo          string `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string `json:"quote_order_no"`            // 引用数据单号
	ProductCode            string `json:"product_code"`              // 成品编号
	ProductName            string `json:"product_name"`              // 成品名称
	DyeFactoryColorCode    string `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `json:"dye_factory_dyelot_number"` // 染厂缸号

	// 转义
	CustomerName     string `json:"customer_name"`      // 所属客户name
	ProductLevelName string `json:"product_level_name"` // 成品等级name
	// 调拨信息
	AllocateRoll   int `json:"allocate_roll"`   // 调拨匹数
	AllocateWeight int `json:"allocate_weight"` // 调拨数量
	AllocateLength int `json:"allocate_length"` // 调拨长度
	// 出仓信息
	OutRoll             int    `json:"out_roll"`                // 出仓匹数
	OutWeight           int    `json:"out_weight"`              // 出仓数量
	OutLength           int    `json:"out_length"`              // 出仓长度
	SalePlanOrderItemId uint64 `json:"sale_plan_order_item_id"` // 成品销售计划单子项信息id
	SalePlanOrderItemNo string `json:"sale_plan_order_item_no"` // 成品销售计划单子项单号
	SaleModeName        string `json:"sale_mode_name"`          // 订单类型

	// SumStockId             uint64 `json:"sum_stock_id"`              // 汇总库存成品id
	// ParentId               uint64 `json:"parent_id"`                 // 父id（单号id）
	// QuoteOrderItemId       uint64 `json:"quote_order_item_id"`       // 引用数据单物料那条id
	// ProductId              uint64 `json:"product_id"`                // 成品id
	// CustomerId             uint64 `json:"customer_id"`               // 所属客户id
	// ProductColorId         uint64 `json:"product_color_id"`          // 成品颜色id
	// ProductLevelId         uint64 `json:"product_level_id"`          // 成品等级
	// ArrangeOrderItemId     uint64 `json:"arrange_order_item_id"`     // 配布单分录行id
}

type GetFpmInOrderDataList []GetFpmInOrderData

func (g GetFpmInOrderDataList) Pick(id uint64) (data GetFpmInOrderData) {
	for _, i := range g {
		if i.Id == id {
			data = i
			return
		}
	}
	return
}

func (g GetFpmInOrderDataList) Adjust() {

}

type DeleteFpmInOrderParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmInOrderData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type AddSaleProductPlanOrderData struct {
	structure_base.ResponseData
	Id                  uint64                     `json:"id"`
	OrderIds            []uint64                   `json:"order_ids"`
	WarehouseGoodInType common.WarehouseGoodInType `json:"warehouse_good_in_type"`
}

func (p PushFpmInOrderParam) Adjust() {

}
func (p PushFpmInOrderParam) Validate() error {
	return nil
}

type PushFpmInOrderParam struct {
	OrderId     uint64                      `json:"order_id"`     // 单据id
	WarehouseId uint64                      `json:"warehouse_id"` // 仓库id,出货单位id
	ItemData    PushFpmInOrderDataParamList `json:"item_data"`    // 下推详细行
}

type PushFpmInOrderDataParam struct {
	Id                uint64                   `json:"id"`                  // 采购进仓单id
	StockProductId    uint64                   `json:"stock_product_id"`    // 汇总库存id,成品销售
	PushedRoll        int                      `json:"pushed_roll"`         // 本次出货匹数
	PushedWeight      int                      `json:"pushed_weight"`       // 本次出货数量
	AdjustWeightError int                      `json:"adjust_weight_error"` // 调整空差,成品销售
	DyelotNumber      string                   `json:"dyelot_number"`       // 缸号,成品销售
	WarehouseSumId    uint64                   `json:"warehouse_sum_id"`    // 汇总库存id,坯布销售
	PushStatus        common_system.PushStatus `json:"push_status"`         // 进度状态
}

type PushFpmInOrderDataParamList []*PushFpmInOrderDataParam

type ModifyFpmInOrder struct {
	Id           uint64 `json:"id"`
	ReturnRoll   int    `json:"return_roll"`
	ReturnLength int    `json:"return_length"`
	ReturnWeight int    `json:"return_weight"`
}

type ModifyFpmInOrderList []ModifyFpmInOrder
