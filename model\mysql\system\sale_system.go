package mysql

import (
	"context"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/system"
	"hcscm/vars"
)

func GetSaleSystemIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "sale_system_id")
}

type SaleSystemList []SaleSystem

func (r SaleSystemList) List() []SaleSystem {
	return r
}

func (r SaleSystemList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r SaleSystemList) One() SaleSystem {
	return r[0]
}

func (r SaleSystemList) Pick(id uint64) (o SaleSystem) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

// 营销体系
type SaleSystem struct {
	mysql_base.Model
	Id                           uint64                `gorm:"column:id;primaryKey;comment:'Id';"`                      // Id
	Number                       int                   `gorm:"column:number"`                                           // 编号
	Code                         string                `gorm:"column:code"`                                             // 编号(Md+Id)
	Name                         string                `gorm:"column:name"`                                             // 名称(必填，唯一)
	CountryID                    uint64                `gorm:"column:country_id" relate:"district_area_id"`             // 国家ID
	CountryName                  string                `gorm:"column:country_name"`                                     // 国家名称
	ProvinceID                   uint64                `gorm:"column:province_id" relate:"district_area_id"`            // 省份ID
	ProvinceName                 string                `gorm:"column:province_name"`                                    // 省份名称
	CityID                       uint64                `gorm:"column:city_id" relate:"district_area_id"`                // 城市ID
	CityName                     string                `gorm:"column:city_name"`                                        // 城市名称
	DistrictID                   uint64                `gorm:"column:district_id" relate:"district_area_id"`            // 市区ID
	DistrictName                 string                `gorm:"column:district_name"`                                    // 市区名称
	AddressDetail                string                `gorm:"column:address_detail"`                                   // 详细地址
	Contacts                     string                `gorm:"column:contacts"`                                         // 联系人
	Phone                        string                `gorm:"column:phone"`                                            // 联系电话(必填，唯一)
	FaxNumber                    string                `gorm:"column:fax_number"`                                       // 传真号
	Status                       common.Status         `gorm:"column:status"`                                           // 状态
	Email                        string                `gorm:"column:email"`                                            // 邮箱
	Remark                       string                `gorm:"column:remark"`                                           // 备注(最多输入100个字符)
	SaleRadixPointSign           common.RadixPointSign `gorm:"column:sale_radix_point_sign"`                            // 销售金额小数位(单选)
	SaleTotalRadixPointSign      common.RadixPointSign `gorm:"column:sale_total_radix_point_sign"`                      // 销售总额小数位(单选)
	SaleTaxRateDefinition        string                `gorm:"column:sale_tax_rate_definition"`                         // 销售税率定义
	LowSaleWeightLimit           int                   `gorm:"column:low_sale_weight_limit"`                            // 最低销售数量极限/kg
	HighSaleWeightLimit          int                   `gorm:"column:high_sale_weight_limit"`                           // 最高销售数量极限/kg
	LowSalePriceLimit            int                   `gorm:"column:low_sale_price_limit"`                             // 最低销售单价极限/kg
	HighSalePriceLimit           int                   `gorm:"column:high_sale_price_limit"`                            // 最高销售单价极限/kg
	FinProPurRadixPointSign      common.RadixPointSign `gorm:"column:fin_pro_pur_radix_point_sign"`                     // 成品采购金额小数位(单选)
	FinProPurTotalRadixPointSign common.RadixPointSign `gorm:"column:fin_pro_pur_total_radix_point_sign"`               // 成品采购总额小数位(单选)
	DefaultPhysicalWarehouse     uint64                `gorm:"column:default_physical_warehouse" relate:"warehouse_id"` // 默认仓库设置(单选，仓库资料)
	DefaultCustomerId            uint64                `gorm:"column:default_customer_id" relate:"biz_unit_id"`         // 默认客户
	DefaultSupplierId            uint64                `gorm:"column:default_supplier_id" relate:"biz_unit_id"`         // 默认供应商
	LowClothSaleWeightLimit      int                   `gorm:"column:low_cloth_sale_weight_limit"`                      // 最低胚布销售数量极限/kg
	HighClothSaleWeightLimit     int                   `gorm:"column:high_cloth_sale_weight_limit"`                     // 最高胚布销售数量极限/kg
	IsSettleLimit                bool                  `gorm:"column:is_settle_limit"`                                  // 是否启用结算天数限制
	SettleType                   common.SettleType     `gorm:"column:settle_type"`                                      // 默认结算类型
	SettleCycle                  common.SettleCycle    `gorm:"column:settle_cycle"`                                     // 默认结算周期
	CustomCycle                  int                   `gorm:"column:custom_cycle"`                                     // 默认结算天数或月份
	CreditLimit                  int                   `gorm:"column:credit_limit"`                                     // 默认信用额度
	CreditLevel                  common.CreditLevel    `gorm:"column:credit_level"`                                     // 默认信用等级
	SaleOrderPrefix              string                `gorm:"column:sale_order_prefix"`                                // 销售单号前缀
	ReturnOrderPrefix            string                `gorm:"column:return_order_prefix"`                              // 退货单号前缀
	ArrangeOrderPrefix           string                `gorm:"column:arrange_order_prefix"`                             // 配布单号前缀
	PurchaseOrderPrefix          string                `gorm:"column:purchase_order_prefix"`                            // 采购单号前缀
	AllocateOrderPrefix          string                `gorm:"column:allocate_order_prefix"`                            // 调拨单号前缀
	ProduceOrderPrefix           string                `gorm:"column:produce_order_prefix"`                             // 生产单号前缀
	DyeingOrderPrefix            string                `gorm:"column:dyeing_order_prefix"`                              // 染整单号前缀
	DeliveryOrderPrefix          string                `gorm:"column:delivery_order_prefix"`                            // 销售送货单号前缀
	SpotStocks                   uint64                `gorm:"column:spot_stocks"`                                      // 现货库存
	DefaultLastSalePrice         bool                  `gorm:"column:default_last_sale_price"`                          // 默认最后一次销售报价
	DefaultLastCostPrice         bool                  `gorm:"column:default_last_cost_price"`                          // 默认最后一次成本报价
	// IsOrderRelate                bool                  `gorm:"column:is_order_relate"`                       // 是否启用订单强关联
	// RMPROrderRelateOrder         bool                  `gorm:"column:order_relate_order"`        // 原料采购单收货单必须关联原料采购单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`         // 原料采购退货单必须关联原料采购单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`         // 原料销售退货单必须关联原料销售单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`           // 其他进仓单必须关联其他出仓单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`        // 坯布采购单收货单必须关联坯布采购单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`         // 坯布采购退货单必须关联坯布采购单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`         // 坯布销售退货单必须关联坯布销售单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`           // 生产通知单必须关联生产计划单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`       // 成品销售退货进仓单必须关联成品销售单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`       // 成品调拨进仓单必须关联成品调拨出仓单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`         // 成品销售出仓单必须关联成品配布单
	// OrderRelateOrder             bool                  `gorm:"column:order_relate_order"`         // 成品采购进仓单必须关联成品采购单
}

func (r SaleSystem) GetId() uint64 {
	return r.Id
}

// TableName SaleSystem 表名
func (SaleSystem) TableName() string {
	return "sale_system"
}

func (r SaleSystem) GetSaleSystemField() string {
	return "id"
}

func (r SaleSystem) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	var (
		cond       = mysql_base.NewCondition()
		saleSystem SaleSystem
	)
	cond.AddEqualToOR("code", r.Code)
	cond.AddEqualToOR("name", r.Name)
	cond.AddNotEqual("id", r.Id)
	exist, err = mysql_base.ExistByCond(tx, &saleSystem, cond)
	if err != nil {
		return
	}
	if exist {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeSaleSystemAlreadyExist))
		return
	}
	return false, nil
}

func (SaleSystem) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (SaleSystem) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeSaleSystemNotExist
}

func (SaleSystem) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeSaleSystemAlreadyExist
}

func (r SaleSystem) buildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	CommonDataSeparate(ctx, r, cond)
}

func NewSaleSystem(
	p *structure.AddSaleSystemParam,
) (r SaleSystem) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.Code = p.Code
	r.Name = p.Name
	// todo: 地址选择
	r.AddressDetail = p.Address
	r.Contacts = p.Contacts
	r.Phone = p.Phone
	r.Status = common.StatusEnable
	r.Email = p.Email
	r.Remark = p.Remark
	r.SaleRadixPointSign = p.SaleRadixPointSign
	r.SaleTotalRadixPointSign = p.SaleTotalRadixPointSign
	r.SaleTaxRateDefinition = p.SaleTaxRateDefinition
	r.LowSaleWeightLimit = p.LowSaleWeightLimit
	r.HighSaleWeightLimit = p.HighSaleWeightLimit
	r.FinProPurRadixPointSign = p.FinProPurRadixPointSign
	r.FinProPurTotalRadixPointSign = p.FinProPurTotalRadixPointSign
	r.DefaultPhysicalWarehouse = p.DefaultPhysicalWarehouse
	r.DefaultCustomerId = p.DefaultCustomerId
	r.DefaultSupplierId = p.DefaultSupplierId
	r.LowClothSaleWeightLimit = p.LowClothSaleWeightLimit
	r.HighClothSaleWeightLimit = p.HighClothSaleWeightLimit
	r.LowSalePriceLimit = p.LowSalePriceLimit
	r.HighSalePriceLimit = p.HighSalePriceLimit
	r.IsSettleLimit = p.IsSettleLimit
	r.SettleType = p.SettleType
	r.SettleCycle = p.SettleCycle
	r.CustomCycle = p.CustomCycle
	r.CreditLevel = p.CreditLevel
	r.CreditLimit = p.CreditLimit
	r.SaleOrderPrefix = p.SaleOrderPrefix
	r.ReturnOrderPrefix = p.ReturnOrderPrefix
	r.ArrangeOrderPrefix = p.ArrangeOrderPrefix
	r.PurchaseOrderPrefix = p.PurchaseOrderPrefix
	r.AllocateOrderPrefix = p.AllocateOrderPrefix
	r.ProduceOrderPrefix = p.ProduceOrderPrefix
	r.DyeingOrderPrefix = p.DyeingOrderPrefix
	r.DeliveryOrderPrefix = p.DeliveryOrderPrefix
	r.FaxNumber = p.FaxNumber
	r.SpotStocks = p.SpotStocks
	r.DefaultLastSalePrice = p.DefaultLastSalePrice
	r.DefaultLastCostPrice = p.DefaultLastCostPrice
	return
}

func (r *SaleSystem) UpdateSaleSystem(
	p *structure.UpdateSaleSystemParam,
) {
	r.Code = p.Code
	r.Name = p.Name
	// todo: 地址选择
	r.AddressDetail = p.Address
	r.Contacts = p.Contacts
	r.Phone = p.Phone
	r.Email = p.Email
	r.Remark = p.Remark
	r.SaleRadixPointSign = p.SaleRadixPointSign
	r.SaleTotalRadixPointSign = p.SaleTotalRadixPointSign
	r.SaleTaxRateDefinition = p.SaleTaxRateDefinition
	r.LowSaleWeightLimit = p.LowSaleWeightLimit
	r.HighSaleWeightLimit = p.HighSaleWeightLimit
	r.FinProPurRadixPointSign = p.FinProPurRadixPointSign
	r.FinProPurTotalRadixPointSign = p.FinProPurTotalRadixPointSign
	r.DefaultPhysicalWarehouse = p.DefaultPhysicalWarehouse
	r.DefaultCustomerId = p.DefaultCustomerId
	r.DefaultSupplierId = p.DefaultSupplierId
	r.LowClothSaleWeightLimit = p.LowClothSaleWeightLimit
	r.HighClothSaleWeightLimit = p.HighClothSaleWeightLimit
	r.LowSalePriceLimit = p.LowSalePriceLimit
	r.HighSalePriceLimit = p.HighSalePriceLimit
	r.IsSettleLimit = p.IsSettleLimit
	r.SettleType = p.SettleType
	r.SettleCycle = p.SettleCycle
	r.CustomCycle = p.CustomCycle
	r.CreditLevel = p.CreditLevel
	r.CreditLimit = p.CreditLimit
	r.SaleOrderPrefix = p.SaleOrderPrefix
	r.ReturnOrderPrefix = p.ReturnOrderPrefix
	r.ArrangeOrderPrefix = p.ArrangeOrderPrefix
	r.PurchaseOrderPrefix = p.PurchaseOrderPrefix
	r.AllocateOrderPrefix = p.AllocateOrderPrefix
	r.ProduceOrderPrefix = p.ProduceOrderPrefix
	r.DyeingOrderPrefix = p.DyeingOrderPrefix
	r.DeliveryOrderPrefix = p.DeliveryOrderPrefix
	r.FaxNumber = p.FaxNumber
	r.SpotStocks = p.SpotStocks
	r.DefaultLastSalePrice = p.DefaultLastSalePrice
	r.DefaultLastCostPrice = p.DefaultLastCostPrice
}

// 禁用营销体系
func (r *SaleSystem) UpdateSaleSystemStatus(
	p *structure.UpdateSaleSystemStatusParam,
) {
	r.Status = p.Status
}

func MustCreateSaleSystem(tx *mysql_base.Tx, r SaleSystem) (o SaleSystem, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateSaleSystem(tx *mysql_base.Tx, r SaleSystem) (o SaleSystem, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteSaleSystem(tx *mysql_base.Tx, r SaleSystem) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstSaleSystemByID(tx *mysql_base.Tx, id uint64) (r SaleSystem, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

// 根据编号和名称查询
func FindTypeIntercourseUnitsByCodeOrName(tx *mysql_base.Tx, code, name []string) (o SaleSystemList, err error) {
	var (
		r    SaleSystem
		cond = mysql_base.NewCondition()
		list []SaleSystem
	)
	cond.AddContainMatchToOR("code", code)
	cond.AddContainMatchToOR("name", name)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	o = list
	return
}

func FirstSaleSystemByID(tx *mysql_base.Tx, id uint64) (r SaleSystem, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FindSaleSystemBySaleSystemID(tx *mysql_base.Tx, objects ...interface{}) (o SaleSystemList, err error) {
	ids := GetSaleSystemIdList(objects)
	var (
		r    SaleSystem
		cond = mysql_base.NewCondition()
		list []SaleSystem
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindSaleSystemByID(tx *mysql_base.Tx, ids []uint64) (o SaleSystemList, err error) {
	var (
		r    SaleSystem
		cond = mysql_base.NewCondition()
		list []SaleSystem
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 列表用(无数据隔离)
func SearchSaleSystem(tx *mysql_base.Tx, q *structure.GetSaleSystemListQuery) (o SaleSystemList, count int, err error) {
	var (
		r           SaleSystem
		cond        = mysql_base.NewCondition()
		list        []SaleSystem
		groupFields []string
	)
	// r.buildReadCond(tx.Context, cond)
	if q.Code != "" {
		cond.AddTableFuzzyMatch(r, "code", q.Code)
	}
	if q.Name != "" {
		cond.AddTableFuzzyMatch(r, "name", q.Name)
	}
	if q.Address != "" {
		cond.AddTableFuzzyMatch(r, "address", q.Address)
	}
	if q.Phone != "" {
		cond.AddTableFuzzyMatch(r, "phone", q.Phone)
	}
	if q.Status != 0 {
		cond.AddTableEqual(r, "status", q.Status)
	}
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 下拉列表，有数据隔离
func SearchSaleSystemDropdown(tx *mysql_base.Tx, q *structure.GetSaleSystemDropdownListQuery) (o SaleSystemList, count int, err error) {
	var (
		r           SaleSystem
		cond        = mysql_base.NewCondition()
		list        []SaleSystem
		groupFields []string
	)
	r.buildReadCond(tx.Context, cond)
	if q.Code != "" {
		cond.AddTableFuzzyMatch(r, "code", q.Code)
	}
	if q.Name != "" {
		cond.AddTableFuzzyMatch(r, "name", q.Name)
	}
	if q.Address != "" {
		cond.AddTableFuzzyMatch(r, "address", q.Address)
	}
	if q.Phone != "" {
		cond.AddTableFuzzyMatch(r, "phone", q.Phone)
	}

	cond.AddTableEqual(r, "status", common.StatusEnable)
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 下拉列表，无数据隔离
func SearchSaleSystemDropdownWithoutDS(tx *mysql_base.Tx, q *structure.GetSaleSystemDropdownListQuery) (o SaleSystemList, count int, err error) {
	var (
		r           SaleSystem
		cond        = mysql_base.NewCondition()
		list        []SaleSystem
		groupFields []string
	)
	if q.Code != "" {
		cond.AddTableFuzzyMatch(r, "code", q.Code)
	}
	if q.Name != "" {
		cond.AddTableFuzzyMatch(r, "name", q.Name)
	}
	if q.Address != "" {
		cond.AddTableFuzzyMatch(r, "address", q.Address)
	}
	if q.Phone != "" {
		cond.AddTableFuzzyMatch(r, "phone", q.Phone)
	}

	cond.AddTableEqual(r, "status", common.StatusEnable)
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 名称模糊获取
func FindSaleSystemByLikeName(tx *mysql_base.Tx, name string) (o SaleSystemList, err error) {
	var (
		r    SaleSystem
		cond = mysql_base.NewCondition()
		list []SaleSystem
	)
	cond.AddFuzzyMatch("name", name)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 查询全部营销体系
func FindAllSaleSystem(tx *mysql_base.Tx, status common.Status) (o SaleSystemList, err error) {
	var (
		r    SaleSystem
		cond = mysql_base.NewCondition()
		list []SaleSystem
	)
	if status != 0 {
		cond.AddTableEqual(r, "status", status)
	}
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}
