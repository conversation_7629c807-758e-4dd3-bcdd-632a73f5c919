package product

import (
	structure_base "hcscm/structure/system"
)

type AddProductCheckOrderItemParam struct {
	structure_base.Param
	Id                  uint64                                  `json:"id"`
	StockProductId      uint64                                  `json:"stock_product_id"`      // 库存id
	ProductId           uint64                                  `json:"product_id"`            // 成品id
	ProductColorId      uint64                                  `json:"product_color_id"`      // 颜色id
	CustomerId          uint64                                  `json:"customer_id"`           // 所属客户id
	ProductLevelId      uint64                                  `json:"product_level_id"`      // 成品等级id
	DyelotNumber        string                                  `json:"dyelot_number"`         // 缸号
	ProductRemark       string                                  `json:"product_remark"`        // 成品备注
	Remark              string                                  `json:"remark"`                // 备注
	MeasurementUnitId   uint64                                  `json:"measurement_unit_id"`   // 计量单位id
	MeasurementUnitName string                                  `json:"measurement_unit_name"` // 计量单位名称
	Roll                int                                     `json:"roll"`                  // 库存匹数
	Weight              int                                     `json:"weight"`                // 库存数量
	Length              int                                     `json:"length"`                // 库存长度
	CheckRoll           int                                     `json:"check_roll"`            // 盘点匹数
	CheckWeight         int                                     `json:"check_weight"`          // 盘点数量
	CheckLength         int                                     `json:"check_length"`          // 盘点长度
	IsStock             bool                                    `json:"is_stock"`              // 是否是库存盘点(true为库存盘点，false为盘盈新增盘点)
	ItemData            AddProductCheckOrderWeightItemParamList `json:"item_data"`             // 成品盘点单成品细码信息
}

type AddProductCheckOrderItemParamList []AddProductCheckOrderItemParam

func (r AddProductCheckOrderItemParamList) Adjust() {

}

type GetProductCheckOrderItemData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	StockProductId      uint64                                 `json:"stock_product_id"`       // 库存id
	ProductCheckOrderId uint64                                 `json:"product_check_order_id"` // 成品盘点单id
	ProductId           uint64                                 `json:"product_id"`             // 成品id
	ProductCode         string                                 `json:"product_code"`           // 成品编号
	ProductName         string                                 `json:"product_name"`           // 成品名称
	ProductCraft        string                                 `json:"product_craft"`          // 成品工艺
	ProductIngredient   string                                 `json:"product_ingredient"`     // 成品成分
	ProductColorId      uint64                                 `json:"product_color_id"`       // 颜色id
	ProductColorCode    string                                 `json:"product_color_code"`     // 颜色编号
	ProductColorName    string                                 `json:"product_color_name"`     // 颜色名称
	CustomerId          uint64                                 `json:"customer_id"`            // 所属客户id
	CustomerName        string                                 `json:"customer_name"`          // 所属客户id名称
	ProductLevelId      uint64                                 `json:"product_level_id"`       // 成品等级id
	ProductLevelName    string                                 `json:"product_level_name"`     // 成品等级id名称
	DyelotNumber        string                                 `json:"dyelot_number"`          // 缸号
	ProductRemark       string                                 `json:"product_remark"`         // 成品备注
	Remark              string                                 `json:"remark"`                 // 备注
	MeasurementUnitId   uint64                                 `json:"measurement_unit_id"`    // 计量单位id
	MeasurementUnitName string                                 `json:"measurement_unit_name"`  // 计量单位名称
	Roll                int                                    `json:"roll"`                   // 库存匹数
	Weight              int                                    `json:"weight"`                 // 库存数量
	Length              int                                    `json:"length"`                 // 库存长度
	CheckRoll           int                                    `json:"check_roll"`             // 盘点匹数
	CheckWeight         int                                    `json:"check_weight"`           // 盘点数量
	CheckLength         int                                    `json:"check_length"`           // 盘点长度
	IsStock             bool                                   `json:"is_stock"`               // 是否是库存盘点(true为库存盘点，false为盘盈新增盘点)
	ItemData            GetProductCheckOrderWeightItemDataList `json:"item_data"`              // 成品盘点单成品细码信息
}

type GetProductCheckOrderItemDataList []GetProductCheckOrderItemData

func (g GetProductCheckOrderItemDataList) Adjust() {

}
