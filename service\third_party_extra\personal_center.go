package third_party_extra

import (
	"hcscm/aggs/third_party_extra"
	"hcscm/model/mysql/mysql_base"
	"hcscm/structure/third_party_extra_structure"
)

type PersonalCenterService struct {
}

func NewPersonalCenterService() *PersonalCenterService {
	return &PersonalCenterService{}
}

// GetPersonalCenter 获取个人中心信息
func (s *PersonalCenterService) GetPersonalCenter(req third_party_extra_structure.GetPersonalCenterRequest) (response third_party_extra_structure.GetPersonalCenterResponse, err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewPersonalCenterRepo(tx)
	response, exist, err := repo.GetPersonalCenterByUserId(req.UserId)
	if err != nil {
		return
	}
	if !exist {
		// 如果不存在，返回空的响应
		response = third_party_extra_structure.GetPersonalCenterResponse{
			UserId: req.UserId,
			Status: 1,
		}
	}
	return
}

// UpdatePersonalCenter 更新个人中心信息
func (s *PersonalCenterService) UpdatePersonalCenter(req third_party_extra_structure.UpdatePersonalCenterRequest) (err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewPersonalCenterRepo(tx)
	err = repo.CreateOrUpdatePersonalCenter(req)
	if err != nil {
		return
	}

	err = tx.Commit().Error
	return
}

// GetPersonalCenterList 获取个人中心列表
func (s *PersonalCenterService) GetPersonalCenterList(req third_party_extra_structure.GetPersonalCenterListRequest) (response third_party_extra_structure.GetPersonalCenterListResponse, err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewPersonalCenterRepo(tx)
	response, err = repo.GetPersonalCenterList(req)
	return
}

// DeletePersonalCenter 删除个人中心信息
func (s *PersonalCenterService) DeletePersonalCenter(id uint64) (err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewPersonalCenterRepo(tx)
	err = repo.DeletePersonalCenter(id)
	if err != nil {
		return
	}

	err = tx.Commit().Error
	return
}
