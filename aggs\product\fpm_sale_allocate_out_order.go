package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	cus_error "hcscm/common/errors"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"time"
)

type FpmSaleAllocateOutOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmSaleAllocateOutOrderRepo(tx *mysql_base.Tx) *FpmSaleAllocateOutOrderRepo {
	return &FpmSaleAllocateOutOrderRepo{tx: tx}
}

func (r *FpmSaleAllocateOutOrderRepo) Add(ctx context.Context, req *structure.AddFpmSaleAllocateOutOrderParam) (data structure.AddFpmSaleAllocateOutOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
	)

	fpmSaleAllocateOutOrder := model.NewFpmSaleAllocateOutOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmSaleAllocateOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmSaleAllocateOutOrder.BusinessClose = common_system.BusinessCloseNo
	fpmSaleAllocateOutOrder.DepartmentId = info.GetDepartmentId()

	// 在营销体系中获取一部分前缀
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})

	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleAllocateOutOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleAllocateOutOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmSaleAllocateOutOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmSaleAllocateOutOrder.OrderNo = orderNo
	fpmSaleAllocateOutOrder.Number = int(number)

	fpmSaleAllocateOutOrder.TotalWeight, fpmSaleAllocateOutOrder.TotalRoll, fpmSaleAllocateOutOrder.TotalLength = req.GetTotalPWL()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleAllocateOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleAllocateOutOrder, err = mysql.MustCreateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		fpmOutOrderItem.ParentId = fpmSaleAllocateOutOrder.Id
		fpmOutOrderItem.ParentOrderNo = fpmSaleAllocateOutOrder.OrderNo

		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(0)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmOutOrderItem.TotalWeight = tw
		fpmOutOrderItem.TotalPrice = tp
		fpmOutOrderItem.OutLength = tl
		fpmOutOrderItem.WeightError = weightError
		fpmOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmOutOrderItem.PaperTubeWeight = tpp
		fpmOutOrderItem.SettleWeight = tsw
		fpmOutOrderItem.ActuallyWeight = taw
		fpmOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmOutOrderItem.Id
			itemFc.WarehouseId = fpmSaleAllocateOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeSaleAllocate
			itemFc.WarehouseOutOrderId = fpmSaleAllocateOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmSaleAllocateOutOrder.OrderNo
			itemFc.OrderTime = fpmSaleAllocateOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleAllocateOutOrder.Id
	return
}

func (r *FpmSaleAllocateOutOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmSaleAllocateOutOrderParam) (data structure.UpdateFpmSaleAllocateOutOrderData, err error) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
		itemModel               model.FpmOutOrderItem
		findCodeModel           model.FpmOutOrderItemFc
		itemList                model.FpmOutOrderItemList
	)
	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmSaleAllocateOutOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，当前单据状态不能更新。")
		return
	}

	fpmSaleAllocateOutOrder.UpdateFpmSaleAllocateOutOrder(ctx, req)

	fpmSaleAllocateOutOrder.TotalWeight, fpmSaleAllocateOutOrder.TotalRoll, fpmSaleAllocateOutOrder.TotalLength = req.GetTotalPWL()

	if fpmSaleAllocateOutOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmSaleAllocateOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleAllocateOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	arrangeItemIdMap := make(map[uint64]uint64) // 使用sum_stock_id作为键
	for _, item := range itemList {
		if item.SumStockId > 0 && item.ArrangeItemId > 0 {
			arrangeItemIdMap[item.SumStockId] = item.ArrangeItemId
		}
	}

	// 创建映射表，用StockId作为键存储ArrangeItemFcId
	fcArrangeItemFcMap := make(map[uint64]uint64) // key为StockId, value为ArrangeItemFcId

	if len(itemIds) > 0 {
		// 先获取fc列表，并保存ArrangeItemFcId
		fcList, _ := mysql.FindFpmOutOrderItemFcByParenTIDs(r.tx, itemIds)
		if len(fcList) > 0 {
			// 保存每个fc的ArrangeItemFcId
			for _, fc := range fcList {
				// 保存所有StockId到ArrangeItemFcId的映射，确保我们记录所有的关系
				if fc.StockId > 0 && fc.ArrangeItemFcId > 0 {
					fcArrangeItemFcMap[fc.StockId] = fc.ArrangeItemFcId
				}
			}
			// 删除fc记录
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
			if err != nil {
				return
			}
		}

		// 删除原items
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		if item.SumStockId > 0 {
			if arrangeId, exists := arrangeItemIdMap[item.SumStockId]; exists && arrangeId > 0 {
				// 如果有，则使用原来的ArrangeItemId
				fpmOutOrderItem.ArrangeItemId = arrangeId
			}
		}
		fpmOutOrderItem.ParentId = fpmSaleAllocateOutOrder.Id
		fpmOutOrderItem.ParentOrderNo = fpmSaleAllocateOutOrder.OrderNo

		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(0)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmOutOrderItem.TotalWeight = tw
		fpmOutOrderItem.TotalPrice = tp
		fpmOutOrderItem.OutLength = tl
		fpmOutOrderItem.WeightError = weightError
		fpmOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmOutOrderItem.PaperTubeWeight = tpp
		fpmOutOrderItem.SettleWeight = tsw
		fpmOutOrderItem.ActuallyWeight = taw
		fpmOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmOutOrderItem.DyeFactoryDyelotNumber
			}
			// 如果StockId在映射表中存在，则使用原来的ArrangeItemFcId
			if itemFc.StockId > 0 {
				if arrangeItemFcId, exists := fcArrangeItemFcMap[itemFc.StockId]; exists && arrangeItemFcId > 0 {
					itemFc.ArrangeItemFcId = arrangeItemFcId
				}
			}
			itemFc.ParentId = fpmOutOrderItem.Id
			itemFc.WarehouseId = fpmSaleAllocateOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeSaleAllocate
			itemFc.WarehouseOutOrderId = fpmSaleAllocateOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmSaleAllocateOutOrder.OrderNo
			itemFc.OrderTime = fpmSaleAllocateOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleAllocateOutOrder.Id
	return
}

func (r *FpmSaleAllocateOutOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmSaleAllocateOutOrderBusinessCloseParam) (data structure.UpdateFpmSaleAllocateOutOrderData, err error) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, v)
		if err != nil {
			return
		}
		// 更新业务状态
		err = fpmSaleAllocateOutOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmSaleAllocateOutOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateOutOrderStatusData, err error, updateItems structure.UpdateStockProductDetailParamList) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
		exit                    = false
	)

	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	data.ArrangeId = fpmSaleAllocateOutOrder.ArrangeOrderId

	err, updateItems = r.judgeAuditPass(id, fpmSaleAllocateOutOrder.ArrangeOrderId, fpmSaleAllocateOutOrder, ctx)
	if err != nil {
		return
	}

	_, exit, _ = mysql.JudgeExitBySaleAlloOutId(r.tx, fpmSaleAllocateOutOrder.Id)
	if exit {
		data.IsHasInOrder = true
	}

	// 审核
	err = fpmSaleAllocateOutOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmSaleAllocateOutOrder.Id, true)
	if err != nil {
		return
	}

	data.WarehouseOutTime = tools.MyTime(fpmSaleAllocateOutOrder.WarehouseOutTime)
	return
}

func (r *FpmSaleAllocateOutOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateOutOrderStatusData, err error, updateItems structure.UpdateStockProductDetailParamList) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
	)

	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	err = r.judgeIsQuoted(id, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	err, updateItems = r.judgeAuditWait(id, fpmSaleAllocateOutOrder, ctx)
	if err != nil {
		return
	}

	// 消审
	err = fpmSaleAllocateOutOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmSaleAllocateOutOrder.Id, false)
	if err != nil {
		return
	}

	data.ArrangeId = fpmSaleAllocateOutOrder.ArrangeOrderId
	data.WarehouseOutTime = tools.MyTime(fpmSaleAllocateOutOrder.WarehouseOutTime)
	return
}

func (r *FpmSaleAllocateOutOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateOutOrderStatusData, err error) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
	)

	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 拒绝/驳回
	err = fpmSaleAllocateOutOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmSaleAllocateOutOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateOutOrderStatusData, err error) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder

		fcOutList model.FpmOutOrderItemFcList
	)

	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	err = r.judgeIsQuoted(id, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	// 获取细码的库存id，用来解除占用
	fcOutList, _ = mysql.FindFpmOutOrderItemFcByOrderId(r.tx, fpmSaleAllocateOutOrder.Id)
	if len(fcOutList) > 0 {
		data.StockDetailIds = fcOutList.GetStockIDs()
	}
	data.ArrangeId = fpmSaleAllocateOutOrder.ArrangeOrderId

	// 作废
	err = fpmSaleAllocateOutOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmSaleAllocateOutOrderRepo) Get(ctx context.Context, req *structure.GetFpmSaleAllocateOutOrderQuery) (data structure.GetFpmSaleAllocateOutOrderData, err error) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
		itemDatas               model.FpmOutOrderItemList
		fineCodeList            model.FpmOutOrderItemFcList
		detailStockList         model.StockProductDetailList
		warehousePB             = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB                  = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc                  = dictionary.NewDictionaryClient()
	)
	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmSaleAllocateOutOrderData{}
	r.swapListModel2Data(fpmSaleAllocateOutOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmOutOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmSaleAllocateOutOrder.AuditStatus != common_system.OrderStatusAudited {
			stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
				StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
			for _, v := range stockList {
				// 库存信息,2023-12-20 需求1001412改为获取可用数量和匹数
				itemGetData.SumStockRoll = v.AvailableRoll
				itemGetData.SumStockWeight = v.AvailableWeight
				itemGetData.SumStockLength = v.Length
			}
		}
		// 添加细码信息
		fineCodeList, err = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, itemData.Id)
		if err != nil {
			return
		}

		mUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "measurement_unit_id")
		wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
		stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitFcIds)
		binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
		detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
		if err != nil {
			return
		}
		dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
		dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

		for _, fineCode := range fineCodeList {
			fineCodeGetData := structure.GetFpmOutOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
			fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
			fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.OrderTime = tools.MyTime(fineCode.OrderTime)
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			fineCodeGetData.IsBooked = fineCode.IsBooked
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			detailStock := detailStockList.Pick(fineCode.StockId)
			fineCodeGetData.QrCode = detailStock.QrCode

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)

			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmSaleAllocateOutOrderRepo) GetList(ctx context.Context, req *structure.GetFpmOutOrderListQuery) (list structure.GetFpmSaleAllocateOutOrderDataList, total int, err error) {
	var (
		orders      model.FpmOutOrderList
		bizPB       = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmOutOrder(r.tx, req)
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orders, "unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		dst := structure.GetFpmSaleAllocateOutOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.ArrangeOrderId = src.ArrangeOrderId
		dst.ArrangeOrderNo = src.ArrangeOrderNo
		dst.SaleSystemId = src.SaleSystemId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseInId = src.WarehouseInId
		dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
		dst.CustomerId = src.BizUnitId
		dst.StoreKeeperId = src.StoreKeeperId
		dst.SaleUserId = src.SaleUserId
		dst.SaleFollowerId = src.SaleFollowerId
		dst.DriverId = src.DriverId
		dst.LogisticsCompanyId = src.LogisticsCompanyId
		dst.LogisticsCompanyArea = src.LogisticsCompanyArea
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)

		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.CustomerName = bizNameMap[src.BizUnitId]
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.WarehouseInName = wareNameMap[src.WarehouseInId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.UnitName = unitNameMap[src.UnitId]
		list = append(list, dst)
	}
	return
}

func (r *FpmSaleAllocateOutOrderRepo) swapListModel2Data(src model.FpmOutOrder, dst *structure.GetFpmSaleAllocateOutOrderData, ctx context.Context) {
	var (
		bizService       = biz_pb.NewClientBizUnitService()
		saleSysPBSerbice = sale_sys_pb.NewSaleSystemClient()
		userName         = make(map[uint64]string)
		unitPB           = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB      = warehouse_pb.NewPhysicalWarehouseClient()
		emplPB           = empl_pb.NewClientEmployeeService()
		driverName       string
		// company          = base_info_pb.NewInfoSaleLogisticsCompanyClient()
	)

	// companyName, _ := company.GetInfoSaleLogisticsCompanyNameById(r.tx.Context, src.LogisticsCompanyId)
	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleSystemMap, err2 := saleSysPBSerbice.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	userName, _ = emplPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId, src.SaleUserId, src.SaleFollowerId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)
	warehouseInName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseInId)
	driverIds := tools.String2UintArray(src.DriverId, ",")
	if len(driverIds) != 0 {
		driverNameMap, _ := emplPB.GetEmployeeNameByIds(r.tx.Context, driverIds)
		driverName = tools.GetMapValAppend2String(driverNameMap, ",")
	}

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ArrangeOrderId = src.ArrangeOrderId
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.SaleSystemId = src.SaleSystemId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseInId = src.WarehouseInId
	dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
	dst.CustomerId = src.BizUnitId
	dst.StoreKeeperId = src.StoreKeeperId
	dst.SaleUserId = src.SaleUserId
	dst.SaleFollowerId = src.SaleFollowerId
	dst.DriverId = src.DriverId
	dst.LogisticsCompanyId = src.LogisticsCompanyId
	dst.LogisticsCompanyArea = src.LogisticsCompanyArea
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.WarehouseName = warehouseName
	dst.WarehouseInName = warehouseInName
	dst.UnitName = unitName
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.CustomerName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	dst.DriverName = driverName
	dst.SaleUserName = userName[src.SaleUserId]
	dst.SaleFollowerName = userName[src.SaleFollowerId]
	dst.LogisticsCompanyName = src.LogisticsCompanyName
	dst.SaleMode = src.SaleMode
}

func (r *FpmSaleAllocateOutOrderRepo) swapItemModel2Data(item model.FpmOutOrderItem, dst *structure.GetFpmOutOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{item.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, item.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, item.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, item.ProductColorId)

	dst.Id = item.Id
	dst.CreateTime = tools.MyTime(item.CreateTime)
	dst.UpdateTime = tools.MyTime(item.UpdateTime)
	dst.CreatorId = item.CreatorId
	dst.CreatorName = item.CreatorName
	dst.UpdaterId = item.UpdaterId
	dst.UpdateUserName = item.UpdaterName
	dst.ParentId = item.ParentId
	dst.SumStockId = item.SumStockId
	dst.ParentOrderNo = item.ParentOrderNo
	dst.QuoteOrderNo = item.QuoteOrderNo
	dst.QuoteOrderItemId = item.QuoteOrderItemId
	dst.ProductId = item.ProductId
	dst.ProductCode = item.ProductCode
	dst.ProductName = item.ProductName
	dst.CustomerId = item.CustomerId
	dst.ProductColorId = item.ProductColorId
	dst.ProductColorCode = item.ProductColorCode
	dst.ProductColorName = item.ProductColorName
	dst.ProductLevelId = item.ProductLevelId
	dst.ProductWidth = item.ProductWidth
	dst.DyeFactoryColorCode = item.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
	dst.ProductGramWeight = item.ProductGramWeight
	dst.ProductRemark = item.ProductRemark
	dst.ProductCraft = item.ProductCraft
	dst.ProductIngredient = item.ProductIngredient
	dst.OutRoll = item.OutRoll
	dst.SumStockId = item.SumStockId
	dst.TotalWeight = item.TotalWeight
	dst.WeightError = item.WeightError
	dst.ActuallyWeight = item.ActuallyWeight
	dst.PaperTubeWeight = item.PaperTubeWeight
	dst.SettleErrorWeight = item.SettleErrorWeight
	dst.SettleWeight = item.SettleWeight
	dst.UnitId = item.UnitId
	dst.OutLength = item.OutLength
	dst.Remark = item.Remark
	dst.ArrangeItemId = item.ArrangeItemId
	dst.SalePlanOrderItemId = item.SalePlanOrderItemId
	dst.FpmInOrderItemId = item.FpmInOrderItemId
	dst.SumStockRoll = item.SumStockRoll
	dst.SumStockLength = item.SumStockLength
	dst.SumStockWeight = item.SumStockWeight

	// 转义
	dst.UnitName = unitName
	dst.CustomerName = customerMap[item.CustomerId]
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
}

func (r *FpmSaleAllocateOutOrderRepo) swapFcModel2Data(src model.FpmOutOrderItemFc, dst *structure.GetFpmOutOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, src.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.Roll = src.Roll
	dst.WarehouseBinId = src.WarehouseBinId
	dst.VolumeNumber = src.VolumeNumber
	dst.WarehouseOutType = src.WarehouseOutType
	dst.WarehouseOutOrderId = src.WarehouseOutOrderId
	dst.WarehouseOutOrderNo = src.WarehouseOutOrderNo
	dst.WarehouseInType = src.WarehouseInType
	dst.WarehouseInOrderId = src.WarehouseInOrderId
	dst.WarehouseInOrderNo = src.WarehouseInOrderNo
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.StockId = src.StockId
	dst.SumStockId = src.SumStockId
	dst.BaseUnitWeight = src.BaseUnitWeight
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.WeightError = src.WeightError
	dst.UnitId = src.UnitId
	dst.Length = src.Length
	dst.SettleWeight = src.SettleWeight
	dst.DigitalCode = src.DigitalCode
	dst.ShelfNo = src.ShelfNo
	dst.ContractNumber = src.ContractNumber
	dst.CustomerPoNum = src.CustomerPoNum
	dst.AccountNum = src.AccountNum
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductWidth = src.ProductWidth
	dst.ProductGramWeight = src.ProductGramWeight
	dst.StockRemark = src.StockRemark
	dst.Remark = src.Remark
	dst.InternalRemark = src.InternalRemark
	dst.ScanUserId = src.ScanUserId
	dst.ScanUserName = src.ScanUserName
	dst.ScanTime = tools.MyTime(src.ScanTime)
	dst.WarehouseId = src.WarehouseId
	dst.ActuallyWeight = src.ActuallyWeight
	dst.SettleErrorWeight = src.SettleErrorWeight
	dst.OrderTime = tools.MyTime(src.OrderTime)
	dst.ArrangeItemFcId = src.ArrangeItemFcId
	dst.IsBooked = src.IsBooked
	// 转义
	dst.WarehouseBinName = binName
	dst.UnitName = unitName
}

func (r *FpmSaleAllocateOutOrderRepo) judgeAuditPass(
	id uint64, arrangeId uint64, order model.FpmOutOrder, ctx context.Context) (err error, updateItems structure.UpdateStockProductDetailParamList) {
	var (
		fineCodeList         = model.FpmOutOrderItemFcList{}
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		arrangeOrders        = model.FpmArrangeOrderList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
	)

	// 判断成品数量是否符合
	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)
	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)

	}
	arrangeOrders, err = mysql.FindFpmArrangeOrderByIDs(r.tx, []uint64{arrangeId})
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		totalRoll := 0
		totalLength := 0

		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]

		fineCodeList, _ = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		if item.OutRoll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(cus_error.NewError(cus_error.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(fineCodeList) == 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
			return
		}

		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll -= arrangeItem.PushRoll
			updateItemWeight.BookWeight -= arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductSaleAlloOutPass
			if len(arrangeOrders) > 0 {
				updateItemWeight.BookOrderId = arrangeOrders[0].SrcId
			}
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
		for _, fc := range fineCodeList {
			totalRoll = totalRoll + fc.Roll
			totalLength += fc.Length
			updateWeight = append(updateWeight, fc.ToUpdateStockProductDetailParamBack(ctx, swap2StockFieldParam))
		}

		if totalRoll != item.OutRoll {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.OutLength > 0 && totalLength > 0 && item.OutLength != totalLength {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}
	updateBookWeight = append(updateBookWeight, updateWeight...)
	updateItems = updateBookWeight
	return
}

func (r *FpmSaleAllocateOutOrderRepo) judgeAuditWait(id uint64, order model.FpmOutOrder, ctx context.Context) (err error, updateItems structure.UpdateStockProductDetailParamList) {
	var (
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		fineCodeList         = model.FpmOutOrderItemFcList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
	)

	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		totalRoll := 0
		totalLength := 0
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		fineCodeList, _ = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		for _, fc := range fineCodeList {
			totalRoll = totalRoll + fc.Roll
			totalLength += fc.Length
			updateWeight = append(updateWeight, fc.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam))
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll += arrangeItem.PushRoll
			updateItemWeight.BookWeight += arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductSaleAlloOutWait
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
	}
	updateWeight = append(updateWeight, updateBookWeight...)
	updateItems = updateWeight
	return
}

func (r *FpmSaleAllocateOutOrderRepo) SwapOut2InParam(req structure.GetFpmSaleAllocateOutOrderData) structure.AddFpmSaleAllocateInOrderParam {
	param := structure.AddFpmSaleAllocateInOrderParam{}
	param.Swap2ListParam(req)
	for _, item := range req.ItemData {
		itemParam := structure.AddFpmInOrderItemParam{}
		itemParam.Swap2ItemParam(item)
		itemParam.QuoteOrderItemId = item.Id
		itemParam.QuoteOrderNo = item.ParentOrderNo
		for _, itemFc := range item.ItemFCData {
			itemFcParam := structure.AddFpmInOrderItemFcParam{}
			itemFcParam.Swap2AddItemFcParam(itemFc)
			itemFcParam.WarehouseBinId = 0
			itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
		}
		param.ItemData = append(param.ItemData, itemParam)
	}
	return param
}

func (r *FpmSaleAllocateOutOrderRepo) judgeIsQuoted(id uint64, order model.FpmOutOrder) (err error) {
	var (
		exit        = false
		auditStatus common_system.OrderStatus
	)

	err, exit, auditStatus = mysql.JudgeExitBySaleAlloOutId(r.tx, order.Id)
	if err != nil {
		return
	}
	if exit && auditStatus == common_system.OrderStatusAudited {
		return cus_error.NewCustomError(cus_error.ErrCodeTheOrderIsQuoted, "操作失败,请先取消进仓单")
	}
	return
}

func (r *FpmSaleAllocateOutOrderRepo) UpdateStatusWaitUseByChangeOrder(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateOutOrderStatusData, err error) {
	var (
		fpmSaleAllocateOutOrder model.FpmOutOrder
		info                    = metadata.GetLoginInfo(ctx)
	)

	fpmSaleAllocateOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 单据为待审核/已驳回/已作废状态
	if fpmSaleAllocateOutOrder.AuditStatus == common_system.OrderStatusPendingAudit || fpmSaleAllocateOutOrder.AuditStatus == common_system.OrderStatusRejected || fpmSaleAllocateOutOrder.AuditStatus == common_system.OrderStatusVoided {
		fpmSaleAllocateOutOrder.AuditorName = info.GetUserName()
		fpmSaleAllocateOutOrder.AuditorId = info.GetUserId()
		fpmSaleAllocateOutOrder.AuditDate = time.Now()
		fpmSaleAllocateOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}
	// 单据已审核状态
	if fpmSaleAllocateOutOrder.AuditStatus == common_system.OrderStatusAudited {
		// todo: 已审核状态逻辑待补充
	}
	fpmSaleAllocateOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleAllocateOutOrder)
	if err != nil {
		return
	}

	data.ArrangeId = fpmSaleAllocateOutOrder.ArrangeOrderId
	return
}
