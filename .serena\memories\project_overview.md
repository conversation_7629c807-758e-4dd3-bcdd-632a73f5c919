# HCSCM 项目概述

## 项目简介
HCSCM是一个基于Go语言开发的ERP管理系统，专注于供应链管理。项目使用现代化的微服务架构，提供完整的企业资源规划解决方案。

## 技术栈
- **语言**: Go 1.23.0
- **Web框架**: Gin
- **数据库**: MySQL (主从配置), Redis, MongoDB
- **消息队列**: RabbitMQ
- **ORM**: GORM
- **API文档**: Swagger
- **配置管理**: Viper
- **日志**: 自定义日志系统
- **缓存**: Redis + go-cache
- **任务调度**: Cron
- **WebSocket**: Gorilla WebSocket

## 项目结构
- `main.go` - 应用程序入口点
- `config/` - 配置管理
- `server/` - HTTP服务器和路由处理
- `service/` - 业务逻辑层
- `aggs/` - 聚合层，业务聚合逻辑
- `model/` - 数据模型层
- `structure/` - 数据结构定义
- `router/` - 路由定义
- `initialize/` - 系统初始化
- `middleware/` - 中间件
- `common/` - 公共工具和常量
- `extern/` - 外部服务集成
- `docs/` - 文档

## 核心功能模块
- 基础数据管理
- 员工管理
- 销售管理
- 采购管理
- 库存管理
- 生产管理
- 财务管理
- 系统管理
- AI集成功能
- 企业微信集成