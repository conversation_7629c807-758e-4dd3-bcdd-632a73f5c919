package dao

import (
	common_system "hcscm/common/system_consts"
	bizUnitModel "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/mysql_base"
	. "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
)

func MustCreateFpmInOrder(tx *mysql_base.Tx, r FpmInOrder) (o FpmInOrder, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateFpmInOrder(tx *mysql_base.Tx, r FpmInOrder) (o FpmInOrder, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteFpmInOrder(tx *mysql_base.Tx, r FpmInOrder) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstFpmInOrderByID(tx *mysql_base.Tx, id uint64) (r FpmInOrder, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstFpmInOrderByID(tx *mysql_base.Tx, id uint64) (r FpmInOrder, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FirstFpmInOrderBySrcId(tx *mysql_base.Tx, srcId uint64) (r FpmInOrder, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddEqual("src_id", srcId)
	cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)

	return
}

func FindFpmInOrderByFpmInOrderID(tx *mysql_base.Tx, objects ...interface{}) (o FpmInOrderList, err error) {
	ids := GetFpmInOrderIdList(objects)
	var (
		r    FpmInOrder
		cond = mysql_base.NewCondition()
		list []FpmInOrder
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmInOrderByIDs(tx *mysql_base.Tx, ids []uint64) (o FpmInOrderList, err error) {
	var (
		r    FpmInOrder
		cond = mysql_base.NewCondition()
		list []FpmInOrder
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmInOrderByPurchaseOrderIDs(tx *mysql_base.Tx, ids []uint64) (o FpmInOrderList, err error) {
	var (
		r    FpmInOrder
		cond = mysql_base.NewCondition()
		list []FpmInOrder
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddContainMatch("src_id", ids)
	cond.AddNotEqual("audit_status", common_system.OrderStatusVoided)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// FindFpmInOrderByArrangeOrderIds 根据配布单IDs和审核状态查询进仓单
func FindFpmInOrderByArrangeOrderIds(tx *mysql_base.Tx, arrangeOrderIds []uint64, auditStatus common_system.OrderStatus) (o FpmInOrderList, err error) {
	var (
		r    FpmInOrder
		cond = mysql_base.NewCondition()
		list []FpmInOrder
	)
	//r.BuildReadCond(tx.Context, cond)
	// 查询arrange_order_id在指定的arrangeOrderIds列表中的记录
	cond.AddContainMatch("arrange_order_id", arrangeOrderIds)
	// 指定审核状态
	cond.AddEqual("audit_status", auditStatus)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmProcessInOrderPass(tx *mysql_base.Tx, req *structure.GetFpmInOrderItemListQuery) (o FpmInOrderList, err error) {
	var (
		r    FpmInOrder
		cond = mysql_base.NewCondition()
		list []FpmInOrder
	)

	cond.AddContainMatch("audit_status", common_system.OrderStatusAudited)
	if req.InOrderType > 0 {
		cond.AddTableEqual(r, "in_order_type", req.InOrderType)
	}
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 查询未完成的入库单(全部类型的单)
func FindUnFinishFpmInOrder(tx *mysql_base.Tx, q *structure.GetFpmInOrderListQuery) (o FpmInOrderList, err error) {
	var (
		r    FpmInOrder
		cond = mysql_base.NewCondition()
		list []FpmInOrder
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableBetween(r, "warehouse_in_time", q.InTimeBegin.StringYMD(), q.InTimeEnd.StringYMD2DayListTimeYMDHMS())
	cond.AddContainMatch("audit_status", []common_system.OrderStatus{
		common_system.OrderStatusPendingAudit,
		common_system.OrderStatusRejected,
	})
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmInOrder(tx *mysql_base.Tx, q *structure.GetFpmInOrderListQuery) (o FpmInOrderList, count int, err error) {
	var (
		r           FpmInOrder
		cond        = mysql_base.NewCondition()
		list        []FpmInOrder
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	groupFields = []string{}
	if q.InOrderType > 0 {
		cond.AddTableEqual(r, "in_order_type", q.InOrderType)
	}
	if q.OutWarehouseId > 0 {
		cond.AddTableEqual(r, "out_warehouse_id", q.OutWarehouseId)
	}
	if q.OrderNo != "" {
		cond.AddTableFuzzyMatch(r, "order_no", q.OrderNo)
	}
	if q.BizUnitId != 0 {
		cond.AddTableEqual(r, "biz_unit_id", q.BizUnitId)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if !q.InTimeBegin.IsYMDZero() && !q.InTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.InTimeBegin.StringYMD(), q.InTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.CreateStartDate.IsYMDZero() && !q.CreateEndDate.IsYMDZero() {
		cond.AddTableBetween(r, "create_time", q.CreateStartDate.StringYMD(), q.CreateEndDate.StringYMD2DayListTimeYMDHMS())
	}
	if !q.AuditStartDate.IsYMDZero() && !q.AuditEndDate.IsYMDZero() {
		cond.AddTableBetween(r, "audit_date", q.AuditStartDate.StringYMD(), q.AuditEndDate.StringYMD2DayListTimeYMDHMS())
	}
	if q.AuditStatus != "" {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToInt())
	} else {
		cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	}
	// if q.JudgeQuoteAllocateStatus == true {
	// 	cond.AddNotContainMatch("audit_status", common_system.OrderStatusVoided)
	// }
	if q.SrcOrderNo != "" {
		cond.AddTableFuzzyMatch(r, "src_order_no", q.SrcOrderNo)
	}
	if q.SrcId > 0 {
		cond.AddTableEqual(r, "src_id", q.SrcId)
	}
	if q.VoucherNumber != "" {
		cond.AddTableFuzzyMatch(r, "voucher_number", q.VoucherNumber)
	}

	// 通过QueryStr区分小程序和后台搜索
	if q.QueryStr != "" {
		cond.AddTableLeftJoiner(&r, &bizUnitModel.BizUnit{}, "biz_unit_id", "id")
		cond.AddTableEqual(&bizUnitModel.BizUnit{}, "category", 2) // 类别 1供应商 2客户
		cond.AddFuzzyMatchToOR("fpm_in_order.order_no", q.QueryStr)
		cond.AddFuzzyMatchToOR("biz_unit.name", q.QueryStr)
	}
	if q.CustomerId > 0 {
		cond.AddTableEqual(r, "biz_unit_id", q.CustomerId)
	}

	if q.ProcessUnitId > 0 {
		cond.AddTableEqual(r, "biz_unit_id", q.ProcessUnitId)
	}

	if !q.OrderNos.IsNil() {
		cond.AddTableContainMatch(r, "order_no", q.OrderNos.ToString())
	}

	cond.AddSort("-fpm_in_order.create_time")
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchMPFpmInOrder(tx *mysql_base.Tx, q *structure.GetMPFpmInOrderListQuery) (o FpmInOrderList, count int, err error) {
	var (
		r           FpmInOrder
		cond        = mysql_base.NewCondition()
		list        []FpmInOrder
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	groupFields = []string{}
	if q.InOrderType > 0 {
		cond.AddTableEqual(r, "in_order_type", q.InOrderType)
	}
	if q.OrderNo != "" {
		cond.AddFuzzyMatchToOR("order_no", q.OrderNo)
		cond.AddContainMatchToOR("biz_unit_id", q.BizUnitIds)
	}
	if q.BizUnitId != 0 {
		cond.AddEqual("biz_unit_id", q.BizUnitId)
	}

	if !q.InTimeBegin.IsYMDZero() && !q.InTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.InTimeBegin.StringYMD(), q.InTimeEnd.StringYMD2DayListTimeYMDHMS())
	}

	if !q.AuditStatus.IsNil() {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToUint64())
	} else {
		cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	}
	if q.SrcOrderNo != "" {
		cond.AddFuzzyMatch("src_order_no", q.SrcOrderNo)
	}
	if q.SrcId > 0 {
		cond.AddEqual("src_id", q.SrcId)
	}
	if q.VoucherNumber != "" {
		cond.AddFuzzyMatch("voucher_number", q.VoucherNumber)
	}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmInOrderForReport(tx *mysql_base.Tx, q *structure.GetFpmInOrderListQuery) (o FpmInOrderList, count int, err error) {
	var (
		r           FpmInOrder
		cond        = mysql_base.NewCondition()
		list        []FpmInOrder
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.InOrderType > 0 {
		cond.AddTableEqual(r, "in_order_type", q.InOrderType)
	}
	if q.WarehouseId != 0 {
		cond.AddEqual("warehouse_id", q.WarehouseId)
	}
	if !q.InTimeBegin.IsYMDZero() && !q.InTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.InTimeBegin.StringYMD(), q.InTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.CreateStartDate.IsYMDZero() && !q.CreateEndDate.IsYMDZero() {
		cond.AddTableBetween(r, "create_time", q.CreateStartDate.StringYMD(), q.CreateEndDate.StringYMD2DayListTimeYMDHMS())
	}
	if !q.AuditStartDate.IsYMDZero() && !q.AuditEndDate.IsYMDZero() {
		cond.AddTableBetween(r, "audit_date", q.AuditStartDate.StringYMD(), q.AuditEndDate.StringYMD2DayListTimeYMDHMS())
	}
	if q.AuditStatus != "" {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToInt())
	} else {
		cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	}
	if q.CustomerId != 0 {
		cond.AddTableEqual(r, "biz_unit_id", q.CustomerId)
	}
	if q.BizUnitId != 0 {
		cond.AddTableEqual(r, "biz_unit_id", q.BizUnitId)
	}
	cond.AddTableEqual(r, "delete_time", 0)
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmInOrderDetail(tx *mysql_base.Tx, q *structure.GetFpmInOrderListQuery) (o FpmInOrderItemList, count int, err error) {
	var (
		r           FpmInOrder
		detail      FpmInOrderItem
		cond        = mysql_base.NewCondition()
		list        []FpmInOrderItem
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableLeftJoiner(detail, r, "parent_id", "id")
	cond.AddTableEqual(r, "delete_time", "0000-00-00 00:00:00")
	if q.InOrderType > 0 {
		cond.AddTableEqual(r, "in_order_type", q.InOrderType)
	}
	if q.OutWarehouseId > 0 {
		cond.AddTableEqual(r, "out_warehouse_id", q.OutWarehouseId)
	}
	if q.OrderNo != "" {
		cond.AddTableFuzzyMatch(r, "order_no", q.OrderNo)
	}
	if q.BizUnitId != 0 {
		cond.AddTableEqual(r, "biz_unit_id", q.BizUnitId)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if !q.InTimeBegin.IsYMDZero() && !q.InTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.InTimeBegin.StringYMD(), q.InTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.AuditStatus.IsNil() {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToUint64())
	} else {
		cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	}
	if q.ProductCodeOrName != "" {
		cond.AddTableContainMatch(detail, "product_id", q.ProductIds)
	}
	if q.ProductColorCodeOrName != "" {
		cond.AddTableContainMatch(detail, "product_color_id", q.ProductColorIds)
	}
	if q.DyelotNumber != "" {
		cond.AddTableFuzzyMatch(detail, "dyelot_number", q.DyelotNumber)
	}
	if q.AvailableOnly {
		cond.AddSql("( fpm_in_order_item.in_roll>fpm_in_order_item.return_roll or fpm_in_order_item.settle_weight>fpm_in_order_item.return_weight or fpm_in_order_item.in_length>fpm_in_order_item.return_length)")
		// cond.AddTableMultiFieldLikeMatchRangeGE(r, []string{"stock_roll", "weight"}, 0)
	}
	if len(q.Ids.ToUint64()) != 0 {
		cond.AddTableContainMatch(r, "id", q.Ids)
	}
	if len(q.SrcOrderNos) != 0 {
		cond.AddTableContainMatch(detail, "q", q.SrcOrderNos)
	}
	if !q.OrderNos.IsNil() {
		cond.AddTableContainMatch(r, "order_no", q.OrderNos.ToString())
	}
	groupFields = []string{}

	cond.AddSort("-id")
	if q.IsNotPage {
		err = mysql_base.SearchListGroup(tx, &detail, &list, cond, groupFields...)
	} else {
		count, err = mysql_base.SearchListGroupForPaging(tx, &detail, q, &list, cond, groupFields...)
	}
	if err != nil {
		return
	}
	o = list
	return
}

// 查询同源兄弟单
func GetSameSourceFpmInOrder(tx *mysql_base.Tx, id uint64) (o FpmInOrderList, err error) {
	var (
		list []FpmInOrder
	)
	// 构建原始SQL语句，通过别名实现表的多次使用
	sql := `
	SELECT sibling_in.* 
	FROM fpm_in_order AS origin
	LEFT JOIN fpm_out_order AS origin_out ON origin.sale_allocate_out_id = origin_out.id
	LEFT JOIN fpm_arrange_order AS origin_arrange ON origin_out.arrange_order_id = origin_arrange.id
	LEFT JOIN fpm_arrange_order AS sibling_arrange ON origin_arrange.src_id = sibling_arrange.src_id
	LEFT JOIN fpm_out_order AS sibling_out ON sibling_arrange.id = sibling_out.arrange_order_id
	LEFT JOIN fpm_in_order AS sibling_in ON sibling_out.id = sibling_in.sale_allocate_out_id
	WHERE origin.id = ? AND sibling_arrange.id != origin_arrange.id AND sibling_in.id IS NOT NULL
	`
	// 使用原始SQL执行查询
	err = tx.DB.Raw(sql, id).Scan(&list).Error
	if err != nil {
		return
	}
	o = list
	return
}

func GetFpmInOrderByArrangeId(tx *mysql_base.Tx, arrangeId uint64) (o FpmInOrder, err error) {
	var (
		order FpmInOrder
	)
	// 构建原始SQL语句，通过别名实现表的多次使用
	sql := `
 SELECT 
     i.* 
 FROM 
     fpm_in_order AS i
 LEFT JOIN 
     fpm_out_order AS o 
     ON i.sale_allocate_out_id = o.id
 LEFT JOIN 
     fpm_arrange_order AS a 
     ON o.arrange_order_id = a.id
 WHERE 
     a.id = ?;`
	// 使用原始SQL执行查询
	err = tx.DB.Raw(sql, arrangeId).Scan(&order).Error
	if err != nil {
		return
	}
	o = order
	return
}
