package system

import (
	"context"
	aggs "hcscm/aggs/system"
	model "hcscm/model/mysql/system"
	structure "hcscm/structure/system"
)

type IDistrictAreaLogic interface {
	GetDistrictAreaForCityList(ctx context.Context, q *structure.GetProvinceListQuery) (list structure.GetAreaDataList, count int, err error)
	GetDistrictAreaEnumList(ctx context.Context, q *structure.GetProvinceListQuery) (list structure.GetAreaDataList, count int, err error)
}

func NewDistrictAreaLogic(ctx context.Context, isCache bool) IDistrictAreaLogic {
	return &districtAreaLogic{
		DistrictAreaRepo: aggs.NewDistrictAreaRepo(ctx, isCache),
	}
}

type districtAreaLogic struct {
	DistrictAreaRepo aggs.IDistrictAreaRepo
}

func (logic *districtAreaLogic) GetDistrictAreaForCityList(ctx context.Context, q *structure.GetProvinceListQuery) (list structure.GetAreaDataList, count int, err error) {
	var (
		districtAreas model.DistrictAreaList
	)
	districtAreas, count, err = logic.DistrictAreaRepo.Search(ctx, nil, q)
	if err != nil {
		return
	}

	for _, districtArea := range districtAreas {
		list = append(list, &structure.GetAreaData{
			ID:        districtArea.Id,
			Name:      districtArea.Name,
			AdCode:    districtArea.AdCode,
			ParentID:  districtArea.ParentID,
			Level:     districtArea.Level,
			LevelName: districtArea.Level.String(),
		})
	}
	return
}

func (logic *districtAreaLogic) GetDistrictAreaEnumList(ctx context.Context, q *structure.GetProvinceListQuery) (list structure.GetAreaDataList, count int, err error) {
	var (
		districtAreas model.DistrictAreaList
	)
	districtAreas, count, err = logic.DistrictAreaRepo.SearchEnum(ctx, nil, q)
	if err != nil {
		return
	}

	for _, districtArea := range districtAreas {
		list = append(list, &structure.GetAreaData{
			ID:        districtArea.Id,
			Name:      districtArea.Name,
			AdCode:    districtArea.AdCode,
			ParentID:  districtArea.ParentID,
			Level:     districtArea.Level,
			LevelName: districtArea.Level.String(),
		})
	}
	return
}
