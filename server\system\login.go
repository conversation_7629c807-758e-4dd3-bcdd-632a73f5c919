package system

import (
	"context"
	"fmt"
	"hcscm/aggs/tenant_management"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	common "hcscm/common/user"
	"hcscm/config"
	"hcscm/extern/aliyun/sms"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_unit_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/sale_system"
	"hcscm/log"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/system"
	tenant_management_model "hcscm/model/mysql/tenant_management"
	tenant_management_mysql "hcscm/model/mysql/tenant_management/dao"
	"hcscm/model/redis"
	qywxSvc "hcscm/service/qywx"
	svc "hcscm/service/system"
	tenant_management_svc "hcscm/service/tenant_management"
	"hcscm/structure/color_card"
	qywxStructure "hcscm/structure/qywx"
	structure "hcscm/structure/system"
	tenantManagementStructure "hcscm/structure/tenant_management"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	后台端登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/admin/v1/login [post]
func AdminLogin(c *gin.Context) {

	var (
		p      = &structure.AdminLoginParam{}
		data   = structure.AdminLoginData{}
		token  string
		_token string
		tokens []string
		info   = WashSystemUser{}
		ctx    = c.Request.Context()
		err    error
		userId uint64
		exist  bool
		user   mysql.User
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	// 登录有两种方法
	// 第一种token已经生成，那就根据token来获取要切换的租户信息，然后返回重新生成的token
	// 第二种token还没有生成，生成token，登录的租户按上一次登录或第一个租户来进行登录操作
	token = c.GetHeader("Authorization")
	if token != "" {
		userId, exist, err = redis.GetAdminUserByToken(ctx, token)
		if err != nil {
			return
		}
		if exist {
			user, err = mysql.MustFirstUser(nil, userId)
			if err != nil {
				return
			}
		}
	}
	if token == "" || !exist {
		user, err = mysql.MustFirstValidateUserByAccount(nil, p.Phone)
		if err != nil {
			return
		}

		// 登录密码不正确
		if !user.CheckPassword(p.Password) {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserPasswordIncorrect), p.Phone, p.Password)
			return
		}
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByAdminUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushAdminToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	_token, err = tools.GenerateAdminUserToken(user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, "user_name", info.GetUserName())
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(info.GetUserId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, _token)

	// 添加账号令牌绑定关系
	err = redis.AddAdminUserToken(ctx, user.Id, _token)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, userId, common_system.PlatformAdmin)

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	p.TenantManagementId, err = IsTenantLogin(ctx, true, _token, user.Id, p.TenantManagementId)
	if err != nil {
		return
	}

	data.Token = _token
	data.UserID = user.Id
	return
}

// @Tags		admin切换h5
// @Security	ApiKeyAuth
// @Summary	切换token
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/admin/v1/switchH5Token [get]
func SwitchH5Token(c *gin.Context) {

	var (
		data   = structure.AdminLoginData{}
		_token string
		tokens []string
		ctx    = c.Request.Context()
		info   = WashSystemUser{}
		err    error
		user   mysql.User
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	loginInfo, ok := GetLoginInfo(c)
	if !ok {
		return
	}
	user, err = mysql.MustFirstUser(nil, loginInfo.GetUserId())
	if err != nil {
		return
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByH5User(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushH5Token(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	_token, err = tools.GenerateH5UserToken(user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, "user_name", info.GetUserName())
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(info.GetUserId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, _token)

	// 添加账号令牌绑定关系
	err = redis.AddH5UserToken(ctx, user.Id, _token)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, user.Id, common_system.PlatformH5)

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	_, err = IsTenantLogin(ctx, true, _token, user.Id, info.GetTenantManagementId())
	if err != nil {
		return
	}

	data.Token = _token
	data.UserID = user.Id
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	h5登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/h5/v1/login [post]
func H5Login(c *gin.Context) {

	var (
		p      = &structure.AdminLoginParam{}
		data   = structure.AdminLoginData{}
		token  string
		_token string
		tokens []string
		info   = WashSystemUser{}
		ctx    = c.Request.Context()
		err    error
		userId uint64
		exist  bool
		user   mysql.User
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	// 登录有两种方法
	// 第一种token已经生成，那就根据token来获取要切换的租户信息，然后返回重新生成的token
	// 第二种token还没有生成，生成token，登录的租户按上一次登录或第一个租户来进行登录操作
	token = c.GetHeader("Authorization")
	if token != "" {
		userId, exist, err = redis.GetH5UserByToken(ctx, token)
		if err != nil {
			return
		}
		if exist {
			user, err = mysql.MustFirstUser(nil, userId)
			if err != nil {
				return
			}
		}
	}
	if token == "" || !exist {
		user, err = mysql.MustFirstValidateUserByAccount(nil, p.Phone)
		if err != nil {
			return
		}

		// 登录密码不正确
		if !user.CheckPassword(p.Password) {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserPasswordIncorrect), p.Phone, p.Password)
			return
		}
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByH5User(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushH5Token(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	_token, err = tools.GenerateH5UserToken(user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, "user_name", info.GetUserName())
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(info.GetUserId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, _token)

	// 添加账号令牌绑定关系
	err = redis.AddH5UserToken(ctx, user.Id, _token)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, userId, common_system.PlatformH5)

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	p.TenantManagementId, err = IsTenantLogin(ctx, true, _token, user.Id, p.TenantManagementId)
	if err != nil {
		return
	}

	data.Token = _token
	data.UserID = user.Id
	return
}

// @Tags		h5切换admin
// @Security	ApiKeyAuth
// @Summary	切换token
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/h5/v1/switchAdminToken [get]
func SwitchAdminToken(c *gin.Context) {

	var (
		data   = structure.AdminLoginData{}
		_token string
		tokens []string
		ctx    = c.Request.Context()
		info   = WashSystemUser{}
		err    error
		user   mysql.User
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	loginInfo, ok := GetLoginInfo(c)
	if !ok {
		return
	}
	user, err = mysql.MustFirstUser(nil, loginInfo.GetUserId())
	if err != nil {
		return
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByAdminUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushAdminToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	_token, err = tools.GenerateAdminUserToken(user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, "user_name", info.GetUserName())
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(info.GetUserId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, _token)

	// 添加账号令牌绑定关系
	err = redis.AddAdminUserToken(ctx, user.Id, _token)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, user.Id, common_system.PlatformAdmin)

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	_, err = IsTenantLogin(ctx, true, _token, user.Id, info.GetTenantManagementId())
	if err != nil {
		return
	}

	data.Token = _token
	data.UserID = user.Id
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	企微扫码登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
//
//	@Param		corp_id				body		string		true	"企业id"
//	@Param		agent_id				body		string		true	"应用id"
//	@Param		code				body		string		true	"code"
//
// @Success	200				{object}	qywxStructure.QYWXGetUserInfoResponse{}
// @Router		/hcscm/h5/v1/scanCodeLogin [post]
func H5ScanCodeLogin(c *gin.Context) {

	var (
		p                          qywxStructure.QYWXGetUserInfoRequest
		data                       qywxStructure.QYWXGetUserInfoResponse
		svc                        = qywxSvc.NewQYWXService()
		token                      string
		info                       = WashSystemUser{}
		ctx                        = c.Request.Context()
		err                        error
		userId, tenantManagementID uint64
		user                       mysql.User
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, &p)
	if err != nil {
		return
	}

	user, _, err = svc.QYWXGetUserInfo(ctx, p)
	if err != nil {
		return
	}

	// 生成token
	token, err = tools.GenerateH5UserToken(user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, "user_name", user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(tenantManagementID, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, token)

	// 添加账号令牌绑定关系
	err = redis.AddH5UserToken(ctx, user.Id, token)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, userId, common_system.PlatformH5)

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	tenantManagementID, err = IsTenantLogin(ctx, true, token, user.Id, tenantManagementID)
	if err != nil {
		return
	}

	data.Token = token
	data.UserID = user.Id
	return
}

func AdminScanCodeLogin(c *gin.Context) {

	var (
		p                          qywxStructure.QYWXGetUserInfoRequest
		data                       qywxStructure.QYWXGetUserInfoResponse
		svc                        = qywxSvc.NewQYWXService()
		token                      string
		info                       = WashSystemUser{}
		ctx                        = c.Request.Context()
		err                        error
		userId, tenantManagementID uint64
		user                       mysql.User
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, &p)
	if err != nil {
		return
	}

	user, _, err = svc.QYWXGetUserInfo(ctx, p)
	if err != nil {
		return
	}

	// 生成token
	token, err = tools.GenerateAdminUserToken(user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(ctx, "user_name", user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(tenantManagementID, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, token)

	// 添加账号令牌绑定关系
	err = redis.AddAdminUserToken(ctx, user.Id, token)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, userId, common_system.PlatformH5)

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	tenantManagementID, err = IsTenantLogin(ctx, true, token, user.Id, tenantManagementID)
	if err != nil {
		return
	}

	data.Token = token
	data.UserID = user.Id
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	pda端登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param   body   body   structure.AdminLoginParam{}  true "入参"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/pda/v1/login [post]
func PDALogin(c *gin.Context) {

	var (
		p      = &structure.AdminLoginParam{}
		data   = structure.AdminLoginData{}
		token  string
		tokens []string
		err    error

		saleSystemSvc = sale_system.NewSaleSystemClient()
		warehouseSvc  = warehouse_pb.NewPhysicalWarehouseClient()
		saleSystem    sale_system.Res
		warehouseName string
		ctx           = c.Request.Context()
		info          = WashSystemUser{}
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	user, err := mysql.MustFirstValidateUserByAccount(nil, p.Phone)
	if err != nil {
		return
	}

	// 登录密码不正确
	if !user.CheckPassword(p.Password) {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserPasswordIncorrect), p.Phone, p.Password)
		return
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByPDAUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushPDAToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	token, err = tools.GeneratePDAUserToken(user.Id)
	if err != nil {
		return
	}

	tokens, err = redis.GetTokenByPDAUser(ctx, user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(c.Request.Context(), "user_name", user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)

	// 添加账号令牌绑定关系
	err = redis.AddPDAUserToken(ctx, user.Id, token)
	if err != nil {
		return
	}

	saleSystem, err = saleSystemSvc.GetSaleSystemByQuery(context.Background(), sale_system.Req{Id: user.DefaultSaleSystemID})
	if err != nil {
		return
	}

	warehouseName, err = warehouseSvc.GetPhysicalWarehouseBinNameById(context.Background(), saleSystem.DefaultPhysicalWarehouse)
	if err != nil {
		return
	}

	data.Token = token
	data.UserID = user.Id
	data.UserName = user.EmployeeName
	data.DefaultSaleSystemID = user.DefaultSaleSystemID
	data.DefaultSaleSystemName = saleSystem.Name
	data.DefaultPhysicalWarehouseID = saleSystem.DefaultPhysicalWarehouse
	data.DefaultPhysicalWarehouseName = warehouseName
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	小程序端登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Param		body	body		structure.AdminLoginParam{}	true	"登录入参"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/mp/v1/login [post]
func MPLogin(c *gin.Context) {

	var (
		p      = &structure.AdminLoginParam{}
		data   = structure.AdminLoginData{}
		token  string
		_token string
		tokens []string
		err    error
		info   = WashSystemUser{}
		ctx    = c.Request.Context()
		exist  bool
		user   mysql.User
		userId uint64
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	// 登录有两种方法
	// 第一种token已经生成，那就根据token来获取要切换的租户信息，然后返回重新生成的token
	// 第二种token还没有生成，生成token，登录的租户按上一次登录或第一个租户来进行登录操作
	token = c.GetHeader("Authorization")
	if token != "" {
		userId, exist, err = redis.GetMPUserByToken(ctx, token)
		if err != nil {
			return
		}
		if exist {
			user, err = mysql.MustFirstUser(nil, userId)
			if err != nil {
				return
			}
		}
	}
	if token == "" || !exist {
		user, err = mysql.MustFirstValidateUserByAccount(nil, p.Phone)
		if err != nil {
			return
		}
		// 登录密码不正确
		if !user.CheckPassword(p.Password) {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserPasswordIncorrect), p.Phone, p.Password)
			return
		}

	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByMPUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushMPToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	_token, err = tools.GenerateMPUserToken(user.Id)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, userId, common_system.PlatformMP)

	ctx = metadata.SetMDToIncoming(c.Request.Context(), "user_name", user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, _token)

	// 添加账号令牌绑定关系
	err = redis.AddMPUserToken(ctx, user.Id, _token)
	if err != nil {
		return
	}
	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	p.TenantManagementId, err = IsTenantLogin(ctx, false, _token, user.Id, p.TenantManagementId)
	if err != nil {
		return
	}
	// 如果不为空，则获取域名和前缀
	if p.TenantManagementId != 0 {
		apiDomain, ApiPrefix := GetApiDomainAndPrefix(p)
		data.ApiDomain = apiDomain
		data.ApiPrefix = ApiPrefix
	}
	data.TenantManagementID = p.TenantManagementId
	data.Token = _token
	data.UserID = user.Id

	return
}

// 根据租户ID获取域名和前缀
func GetApiDomainAndPrefix(p *structure.AdminLoginParam) (apiDomain string, apiDomainPrefix string) {
	// 根据p.TenantManagementID获取域名和前缀
	tenantManagement, err := tenant_management_mysql.NewTenantManagementDao().MustFirst(context.Background(), nil, p.TenantManagementId)
	if err != nil {
		return
	}
	apiDomain = tenantManagement.RequestDomain
	apiDomainPrefix = tenantManagement.RequestDomainPrefix
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	获取用户账套信息
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/mp/v1/loginBySecret [post]
func LoginBySecret(c *gin.Context) {

	var (
		p      = &structure.AdminLoginParam{}
		data   = &structure.AdminLoginData{}
		err    error
		token  string
		tokens []string
		info   = WashSystemUser{}
		ctx    = c.Request.Context()
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	user, err := mysql.MustFirstValidateUserByAccount(nil, p.Phone)
	if err != nil {
		return
	}
	var list = tenantManagementStructure.GetUserTenantManagementDataList{}
	list, _, err = tenant_management.NewTenantManagementRepo().GetUserTenantManagementList(ctx, tenantManagementStructure.GetTenantManagementQuery{UserId: user.Id})

	secret := c.GetHeader("Secret")
	if secret == "" {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, ",秘钥不能为空"))
		return
	}
	for _, tenantManagementData := range list {
		if secret == tenantManagementData.Secret {
			p.TenantManagementId = tenantManagementData.TenantManagementID
			break
		}
	}
	if p.TenantManagementId == 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, ",秘钥错误"))
		return
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByMPUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushMPToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	token, err = tools.GenerateMPUserToken(user.Id)
	if err != nil {
		return
	}

	// 删除登录态缓存
	redis.DeleteUserInfo(ctx, user.Id, common_system.PlatformMP)

	ctx = metadata.SetMDToIncoming(c.Request.Context(), "user_name", user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)

	// 添加账号令牌绑定关系
	err = redis.AddMPUserToken(ctx, user.Id, token)
	if err != nil {
		return
	}

	// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户，提前获取对应的链接
	p.TenantManagementId, err = IsTenantLogin(ctx, false, token, user.Id, p.TenantManagementId)
	if err != nil {
		return
	}

	data.TenantManagementID = p.TenantManagementId
	data.Token = token
	data.UserID = user.Id
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	电子色卡访客登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.AdminLoginData{}
// @Router		/hcscm/third_party/v1/login [post]
func LoginByThirdParty(c *gin.Context) {
	var (
		p                = &structure.AdminLoginParam{}
		data             = &structure.AdminLoginData{}
		err              error
		token            string
		ctx              = c.Request.Context()
		tenantManagement = tenant_management_model.TenantManagement{}
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.TenantManagementId == 0 {
		var list []tenant_management_model.TenantManagement
		list, err = tenant_management.NewTenantManagementRepo().GetTenantManagementList(ctx, nil)
		if err != nil {
			return
		}
		for _, v := range list {
			if v.CheckSecret(p.EncryptedStr) {
				p.TenantManagementId = v.Id
				tenantManagement = v
				break
			}
		}

		if p.TenantManagementId == 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, ",秘钥错误"))
			return
		}
	}

	// 生成token
	token, err = tools.GenerateThirdPartyToken(tenantManagement.Id)
	if err != nil {
		return
	}
	// 添加账号令牌绑定关系
	err = redis.AddMallTenantManagementToken(context.Background(), tenantManagement.Id, token)
	if err != nil {
		return
	}

	// 判断电子色卡是否过期
	var isExpired bool
	isExpired, err = tenant_management_svc.NewCodeListOrcManagementLogic(c).IsExpired(ctx, common_system.RechargeTypeEleColorCard, tenantManagement.Id)
	if err != nil {
		return
	}
	if isExpired {
		data.IsSearchImage = false
	} else {
		// 电子色卡未过期，判断搜索图片是否过期
		isExpired, _ = tenant_management_svc.NewCodeListOrcManagementLogic(c).IsExpired(ctx, common_system.RechargeTypeSearchImage, tenantManagement.Id)
		if isExpired {
			data.IsSearchImage = false
		} else {
			data.IsSearchImage = true
		}
	}

	data.Token = token
	return
}

// 检查登录用户是否存在可用租户，存在租户则登录上一个可用的租户
func IsTenantLogin(ctx context.Context, isInit bool, token string, userID, tenantManagementID uint64) (_tenantManagementID uint64, err error) {
	// 如果没有选择要登录的租户则按上一次登录的租户来进行登录操作
	if tenantManagementID == 0 {
		var tenantManagementUserRels tenant_management_model.TenantManagementUserRelList
		tenantManagementUserRels, err = tenant_management_mysql.NewTenantManagementUserRelDao().FindByUserID(ctx, nil, userID)
		if err != nil {
			return
		}
		// 存在多个用户租户关系的时候需要判断有没有禁用,没有可用的，有禁用的则报用户不可用
		if len(tenantManagementUserRels) >= 1 {
			var isExpired bool = true
			for _, tenantManagementUserRel := range tenantManagementUserRels {
				// 存在租户用户关系为可用的，赋值登录租户ID，没有过期，跳出循环
				if tenantManagementUserRel.Status == common_system.StatusEnable {
					tenantManagementID = tenantManagementUserRel.TenantManagementID
					isExpired = false
					break
				}
			}
			// 判断是否禁用
			if isExpired {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserInvalidate))
				return
			}
		}
	}
	if tenantManagementID != 0 {
		// 缓存该用户登录选择的租户，没有选择的话默认使用最后一次登录的租户，获取登录态的时候使用
		redis.AddKeyUint64Value(ctx, token, tenantManagementID)
		var tenantManagement tenant_management_model.TenantManagement
		tenantManagement, err = tenant_management_mysql.NewTenantManagementDao().MustFirst(context.Background(), nil, tenantManagementID)
		if err != nil {
			return
		}
		if tenantManagement.TenantManagementStatus == common_system.TenantManagementStatusDisable || tenantManagement.TenantManagementStatus == common_system.TenantManagementStatusNotActivation {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeTenantManagementStatusIsDisable), tenantManagement)
			return
		}
		// 如果存在域名关系，则直接返回，不需要获取数据库链接和redis链接
		if tenantManagement.RequestDomain != "" || tenantManagement.RequestDomainPrefix != "" {
			return tenantManagementID, nil
		}
		// 获取数据库链接
		_db := mysql_base.GetDBMap(tenantManagement.Id)
		if _db == nil {
			// 如果是初始化，则同步路由
			if isInit {
				_, err = mysql_base.NewDBConn(context.Background(), tenantManagement.DatabaseName, tenantManagement.Id, syncRouterToDB)
			} else {
				_, err = mysql_base.NewDBConn(context.Background(), tenantManagement.DatabaseName, tenantManagement.Id)
			}
			if err != nil {
				return
			}
		}
		// 获取redis链接
		_redisDB := redis.GetRDBMap(tenantManagement.Id)
		if _redisDB == nil {
			_, err = redis.NewTenantClient(tenantManagement.Id)
			if err != nil {
				return
			}
		}
	}
	_tenantManagementID = tenantManagementID
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	  发送验证码注册
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Success	200				{object}	structure.PhoneCodeLoginParam{}
// @Router		/hcscm/admin/v1/sendVerificationCodeEmail [post]
func SendVerificationCodeEmail(c *gin.Context) {
	var (
		p                 = &structure.PhoneCodeLoginParam{}
		data              = structure.ResponseData{}
		err               error
		exist, cacheExist bool
		code              string
		ctx               = context.Background()
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	_, exist, err = mysql.FirstUserByPhone(nil, p.Phone)
	if err != nil {
		return
	}

	// 存在报错
	if exist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeUserAlreadyExist, ",该手机号码已被注册!"))
		return
	}

	// 用于验证手机号码是否存在直接返回
	if p.IsValid {
		return
	}

	if p.Phone == "" {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "手机号码需要传入!"))
		return
	}

	// 判断缓存中是否存在
	_, cacheExist, err = redis.GetVerificationCodeByPhone(ctx, p.Phone)
	if err != nil {
		return
	}
	if cacheExist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeSystemError, ",请两分钟后重试!"))
		return
	}

	code, err = sms.SendVerificationCode(p.Phone)
	if err != nil {
		return
	}

	// 成功后缓存
	redis.AddVerificationCodePhone(ctx, p.Phone, code)
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	手机验证码注册登录
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.PhoneCodeLoginParam{}
// @Router		/hcscm/admin/v1/phoneVerificationCodeLogin [post]
func PhoneVerificationCodeLogin(c *gin.Context) {
	var (
		p                    = &structure.PhoneCodeLoginParam{}
		data                 = structure.AdminLoginData{}
		token                string
		tokens               []string
		err                  error
		user                 mysql.User
		tenantManagementRepo = tenant_management.NewTenantManagementRepo()
		ctx                  = c.Request.Context()
		info                 = WashSystemUser{}
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	// if vars.Env == "local" {
	// 	ctx = metadata.SetMDToIncoming(c.Request.Context(), "user_name", info.GetUserName())
	// 	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(2, 10))
	// 	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	// 	mysql.LoginLogOperator(ctx, c, user, token)
	//
	// 	data.TenantManagementId, err = tenantManagementRepo.RegisterCreate(ctx, nil)
	// 	if err != nil {
	// 		return
	// 	}
	// }

	// 用户
	user, err = phoneVerificationCodeLogin(ctx, nil, user, p)
	if err != nil {
		return
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByAdminUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushAdminToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	token, err = tools.GenerateAdminUserToken(user.Id)
	if err != nil {
		return
	}

	tokens, err = redis.GetTokenByAdminUser(ctx, user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(c.Request.Context(), "user_name", user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, token)

	// 添加账号令牌绑定关系
	err = redis.AddAdminUserToken(ctx, user.Id, token)
	if err != nil {
		return
	}

	data.Token = token
	data.UserID = user.Id

	// if vars.Env != "local" {
	if vars.Env == "kdyb" || vars.Env == "test" || vars.Env == "local" {
		data.TenantManagementID, err = tenantManagementRepo.RegisterCreate(ctx)
		if err != nil {
			return
		}
	}
	// }

	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	手机验证码注册登录(内部商城)
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.PhoneCodeLoginParam{}
// @Router		/hcscm/mp/v1/phoneVerificationCodeLogin [post]
func MPPhoneVerificationCodeLogin(c *gin.Context) {
	if vars.Env != "kdyb" && vars.Env != "test" && vars.Env != "local" {
		BuildResponse(c, middleware.WarnLog(errors.NewError(errors.ErrCodeNotRegisterCreateUser)), structure.ResponseData{})
		return
	}
	var (
		p                    = &structure.PhoneCodeLoginParam{}
		data                 = structure.AdminLoginData{}
		token                string
		tokens               []string
		err                  error
		user                 mysql.User
		tenantManagementRepo = tenant_management.NewTenantManagementRepo()
		ctx                  = c.Request.Context()
		info                 = WashSystemUser{}
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	// if vars.Env == "local" {
	// 	ctx = metadata.SetMDToIncoming(ctx, "user_name", info.GetUserName())
	// 	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(2, 10))
	// 	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	//
	// 	data.TenantManagementId, err = tenantManagementRepo.RegisterCreate(ctx, tx)
	// 	if err != nil {
	// 		return
	// 	}
	// }

	user, err = phoneVerificationCodeLogin(ctx, nil, user, p)
	if err != nil {
		return
	}

	if vars.Env == "release" {
		tokens, err = redis.GetTokenByMPUser(ctx, user.Id)
		if err != nil {
			return
		}

		err = redis.FlushMPToken(ctx, user.Id, tokens)
		if err != nil {
			return
		}
	}

	// 生成token
	token, err = tools.GenerateMPUserToken(user.Id)
	if err != nil {
		return
	}

	tokens, err = redis.GetTokenByMPUser(ctx, user.Id)
	if err != nil {
		return
	}

	ctx = metadata.SetMDToIncoming(c.Request.Context(), metadata.UserName, user.EmployeeName)
	ctx = metadata.SetMDToIncoming(ctx, metadata.UserId, strconv.FormatUint(user.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(info.GetTenantManagementId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	mysql.LoginLogOperator(ctx, c, user, token)

	// 添加账号令牌绑定关系
	err = redis.AddMPUserToken(ctx, user.Id, token)
	if err != nil {
		return
	}

	data.Token = token
	data.UserID = user.Id

	// tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	// defer func() {
	// 	err = commit(err, recover())
	// }()
	// if vars.Env != "local" {
	if vars.Env == "kdyb" || vars.Env == "test" || vars.Env == "local" {
		// TODO 其他环境无法注册
		data.TenantManagementID, err = tenantManagementRepo.RegisterCreate(ctx)
		if err != nil {
			return
		}
	}
	// 缓存账套id用于获取数据库链接
	redis.AddKeyUint64Value(ctx, token, data.TenantManagementID)
	// }
	return
}

func phoneVerificationCodeLogin(ctx context.Context, tx *mysql_base.Tx, r mysql.User, p *structure.PhoneCodeLoginParam) (o mysql.User, err error) {
	var (
		cacheExist bool
		exist      bool
		code       string
	)
	// 判断缓存中是否存在
	// TODO 上线注释
	if vars.Env != "test" && p.Code != "753951" {
		if vars.Env != "local" {
			code, cacheExist, err = redis.GetVerificationCodeByPhone(ctx, p.Phone)
			if err != nil {
				return
			}
			if !cacheExist {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserVerCodeIncorrect))
				return
			}

			// 验证码不正确
			if code != p.Code {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserVerCodeIncorrect), p.Phone, p.Code)
				return
			}
		}
	}

	r, exist, err = mysql.FirstUserByPhone(nil, p.Phone)
	if err != nil {
		return
	}
	if exist {
		if !r.IsAvailable() {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserInvalidate), p.Phone)
			return
		}
	} else {
		s := vars.TenantName + p.Phone[len(p.Phone)-4:]
		r.Account = s
		r.Phone = p.Phone
		r.EmployeeName = s
		r.Status = common_system.StatusEnable
		r.Type = common.UserTypeWechatMallRegister
		r.DefaultSaleSystemID = vars.DefaultSaleSystemID
		r.DepartmentID = vars.DefaultDepartmentID
		r.SetPassword(p.RepeatPassword)
		r.AccessScope = common_system.RoleAccessDataScopeCompany
		r.AllowUpdateOrder = true
		r.AllowCancelOther = true
		r.AllowAuditSelf = true
		r, err = mysql.MustCreateUser(tx, r)
		if err != nil {
			return
		}
		// todo:科顿易布上线前需修改
		var role mysql.Role
		role, err = mysql.MustFirstRoleByName(tx, "管理者")
		userRoleRel := mysql.NewUserRoleRel(ctx, r.Id, role.Id)

		userRoleRel, err = mysql.MustCreateUserRoleRel(tx, userRoleRel)
		if err != nil {
			return
		}
	}
	o = r
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	后台端登出
// @Produce	json
// @Param		Platform	header		int	true	"终端ID"
// @Success	200			{object}	structure.ResponseData{}
// @Router		/hcscm/admin/v1/logout [post]
func Logout(c *gin.Context) {
	var (
		err       error
		data      = structure.LoginOutData{}
		svc       = svc.NewLoginService()
		token     string
		platformV int
		platform  common_system.Platform
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	token = c.GetHeader("Authorization")
	platformV, err = tools.String2Int(c.GetHeader("Platform"))
	if err != nil {
		return
	}
	platform = common_system.Platform(platformV)

	err = svc.Logout(context.Background(), token, platform)
	if err != nil {
		return
	}
	// data.ApiDomain = strings.TrimSuffix(vars.MainRequestDomain, "/hcscm")
	// data.ApiPrefix = strings.TrimPrefix(strings.TrimPrefix(vars.MainRequestDomain, data.ApiDomain), "/")
	data.ApiDomain = vars.ApiDomain
	data.ApiPrefix = vars.ApiDomainPrefix
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	  发送修改密码验证码
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Success	200				{object}	structure.SendModifyPasswordVerificationCodeParam{}
// @Router		/hcscm/admin/v1/sendModifyPasswordVerificationCode [post]
func SendModifyPasswordVerificationCode(c *gin.Context) {
	var (
		param             = structure.SendModifyPasswordVerificationCodeParam{}
		data              = structure.ResponseData{}
		err               error
		exist, cacheExist bool
		code              string
		ctx               = context.Background()
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, &param)
	if err != nil {
		return
	}

	_, exist, err = mysql.FirstUserByPhone(nil, param.Phone)
	if err != nil {
		return
	}

	if !exist {
		err = log.ErrorLog(errors.NewError(errors.ErrCodeThisPhoneIsNotRegister))
		return
	}

	// 判断缓存中是否存在
	_, cacheExist, err = redis.GetVerificationCodeByPhone(ctx, param.Phone)
	if err != nil {
		return
	}
	if cacheExist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeSystemError, ",请两分钟后重试!"))
		return
	}

	code, err = sms.SendVerificationCode(param.Phone)
	if err != nil {
		return
	}

	// 成功后缓存
	redis.AddVerificationCodePhone(ctx, param.Phone, code)
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	 修改密码
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Success	200				{object}	structure.PhoneCodeLoginParam{}
// @Router		/hcscm/mp/v1/modifyPassword [put]
func ModifyPassword(c *gin.Context) {
	var (
		param                = structure.PhoneCodeLoginParam{}
		data                 = structure.ResponseData{}
		err                  = ShouldBind(c, &param)
		code                 string
		cacheExist, exist    bool
		ctx                  = context.Background()
		_ctx                 = context.Background()
		lVal, lCacheExist    = tools.LCache.Get(fmt.Sprintf("modify_password_%s", param.Phone))
		errCount             int
		user                 mysql.User
		_tenantManagementDao = tenant_management_mysql.NewTenantManagementDao()
	)
	if err != nil {
		return
	}
	if lCacheExist {
		errCount = lVal.(int)
		if errCount >= 10 {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeFrequentOperate))
			BuildResponse(c, err, data)
			return
		}
	}
	defer func() {
		BuildResponse(c, err, data)
		if err != nil {
			errCount++
			tools.LCache.Set(fmt.Sprintf("modify_password_%s", param.Phone), errCount, time.Duration(120)*time.Second)
		}
	}()

	user, exist, err = mysql.FirstUserByPhone(nil, param.Phone)
	if err != nil {
		return
	}

	if param.Password != param.RepeatPassword {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodePasswordNotEqual))
		return
	}

	if !exist {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeThisPhoneIsNotRegister))
		return
	}

	if vars.Env != "local" {
		if param.Code != "" {
			code, cacheExist, err = redis.GetVerificationCodeByPhone(ctx, param.Phone)
			if err != nil {
				return
			}
			if !cacheExist {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserVerCodeIncorrect))
				return
			}

			// 验证码不正确
			if code != param.Code {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserVerCodeIncorrect))
				return
			}
		} else {
			// 检查旧密码
			if !user.CheckPassword(param.OldPassword) {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeSourcePasswordError))
				return
			}
		}
	}

	user.SetPassword(param.RepeatPassword)
	user, err = mysql.MustUpdateUser(nil, user)
	if err != nil {
		return
	}

	var (
		tenantManagementUserRels    tenant_management_model.TenantManagementUserRelList
		_tenantManagementUserRelDao = tenant_management_mysql.NewTenantManagementUserRelDao()
	)

	tenantManagementUserRels, err = _tenantManagementUserRelDao.FindByUserID(ctx, nil, user.Id)
	if err != nil {
		return
	}

	// 更新该用户关联的所有租户内的对应用户的密码
	for _, tenantManagementUserRel := range tenantManagementUserRels {
		var (
			_db              *gorm.DB
			tenantManagement tenant_management_model.TenantManagement
		)
		_db = mysql_base.GetDBMap(tenantManagementUserRel.TenantManagementID)
		if _db == nil {
			tenantManagement, err = _tenantManagementDao.MustFirst(context.Background(), nil, tenantManagementUserRel.TenantManagementID)
			if err != nil {
				return
			}
			_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagementUserRel.TenantManagementID)
			if err != nil {
				return
			}
		}
		// 租户内的用户全部绑定对应自己的租户id
		user.TenantManagementID = tenantManagementUserRel.TenantManagementID
		err = _db.Updates(&user).Error
		if err != nil {
			return
		}
	}

	for ind, fn := range []func(context.Context, uint64) ([]string, error){redis.GetTokenByMPUser, redis.GetTokenByAdminUser, redis.GetTokenByPDAUser} {
		var tokens []string
		tokens, err = fn(_ctx, user.Id)
		if err != nil {
			return
		}
		switch ind {
		case 0:
			err = redis.FlushMPToken(_ctx, user.Id, tokens)
			if err != nil {
				return
			}
		case 1:
			err = redis.FlushAdminToken(_ctx, user.Id, tokens)
			if err != nil {
				return
			}
		case 2:
			err = redis.FlushPDAToken(_ctx, user.Id, tokens)
			if err != nil {
				return
			}
		}
	}
}

// @Tags		返回加密的账套id
// @Security	ApiKeyAuth
// @Summary	获取用户信息
// @Produce	json
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization	header		string	true	"token"
// @Success	200				{object}	color_card.GenerateAccountSetIdData{}
// @Router		/hcscm/h5/v1/generateAccountSetId [get]
func GenerateAccountSetId(c *gin.Context) {
	var (
		data = &color_card.GenerateAccountSetIdData{}
		err  error
	)
	ctx, ok := GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		BuildResponse(c, err, data)
	}()

	tenantManagementId := metadata.GetMD(ctx, metadata.TenantManagementId)

	doubleMD5tenantManagementId := tools.StringDoubleMD5ToUpper(tenantManagementId, config.Conf.Mysql.PasswordSecret1, config.Conf.Mysql.PasswordSecret2)
	data.EncryptedAccountSetId = doubleMD5tenantManagementId
	return
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	  电子色卡发送验证码(不校验该手机号码是否已存在)
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Success	200				{object}	structure.ThirdPartyPhoneCodeLoginParam{}
// @Router		/hcscm/third_party/v1/sendVerificationCodeEmail [post]
func ThirdPartySendVerificationCodeEmail(c *gin.Context) {
	var (
		p          = &structure.ThirdPartyPhoneCodeLoginParam{}
		data       = structure.ResponseData{}
		err        error
		cacheExist bool
		code       string
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	ctx, ok := GetLoginInfoCtx(c)
	if !ok {
		return
	}

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.Phone == "" {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "手机号码需要传入!"))
		return
	}

	// 判断缓存中是否存在
	_, cacheExist, err = redis.GetVerificationCodeByPhone(ctx, p.Phone)
	if err != nil {
		return
	}
	if cacheExist {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeSystemError, ",请两分钟后重试!"))
		return
	}

	code, err = sms.SendVerificationCode(p.Phone)
	if err != nil {
		return
	}

	// 成功后缓存
	redis.AddVerificationCodePhone(ctx, p.Phone, code)
}

// @Tags		登录登出
// @Security	ApiKeyAuth
// @Summary	电子色卡手机验证码登录或者注册
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Success	200				{object}	structure.ThirdPartyPhoneCodeLoginParam{}
// @Router		/hcscm/third_party/v1/phoneVerificationCodeLogin [post]
func ThirdPartyPhoneVerificationCodeLogin(c *gin.Context) {
	var (
		p     = &structure.ThirdPartyPhoneCodeLoginParam{}
		data  = structure.ThirdPartyLoginData{}
		token string
		err   error
		// tenantManagementRepo = tenant_management.NewTenantManagementRepo()
		// info                 = WashSystemUser{}
		// tenantManagement     tenant_management_model.TenantManagement
	)
	defer func() {
		BuildResponse(c, err, data)
	}()

	ctx, ok := GetLoginInfoCtx(c)
	if !ok {
		return
	}

	err = ShouldBind(c, p)
	if err != nil {
		return
	}

	// if p.TenantManagementId == 0 {
	// 	var (
	// 		list []tenant_management_model.TenantManagement
	// 	)
	// 	// 根据加密字符串获取租户Id
	// 	list, err = tenant_management.NewTenantManagementRepo().GetTenantManagementList(ctx, nil)
	// 	if err != nil {
	// 		return
	// 	}
	// 	for _, v := range list {
	// 		if v.CheckSecret(p.EncryptedStr) {
	// 			p.TenantManagementId = v.Id
	// 			tenantManagement = v
	// 			break
	// 		}
	// 	}
	// 	if p.TenantManagementId == 0 {
	// 		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, ",秘钥错误"))
	// 		return
	// 	}
	// }
	//
	// fmt.Println(tenantManagement)

	// 校验验证码是否正确
	if vars.Env != "test" && p.Code != "753951" {
		if vars.Env != "local" {
			var (
				code       string
				cacheExist bool
			)
			code, cacheExist, err = redis.GetVerificationCodeByPhone(ctx, p.Phone)
			if err != nil {
				return
			}
			if !cacheExist {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserVerCodeIncorrect))
				return
			}

			// 验证码不正确
			if code != p.Code {
				err = middleware.WarnLog(errors.NewError(errors.ErrCodeUserVerCodeIncorrect), p.Phone, p.Code)
				return
			}
		}
	}

	tenantManagementId := metadata.GetTenantManagementId(ctx)

	var (
		user      mysql.User
		exist     bool
		bizUnit   biz_unit_pb.Res
		isBizUnit bool
		// tenantManagementUserRel tenant_management_model.TenantManagementUserRel
	)
	// 根据手机号码查询该租户下是否有该用户，如果有则返回该用户信息
	user, exist, err = mysql.FirstUserByPhone(nil, p.Phone)
	if err != nil {
		return
	}

	if exist {
		_, exist, err = tenant_management_mysql.NewTenantManagementUserRelDao().FirstByUserIDAndTenantManagementID(context.Background(), nil, user.Id, tenantManagementId)
		if err != nil {
			return
		}
		if exist {
			ctx = metadata.SetMDToIncoming(ctx, "user_name", user.EmployeeName)
			ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(user.Id, 10))
			mysql.LoginLogOperator(ctx, c, user, token)
		}

		// 生成token
		token, err = tools.GenerateThirdPartyToken(user.Id)
		if err != nil {
			return
		}

		// 添加账号令牌绑定关系
		err = redis.AddMallUserToken(context.Background(), user.Id, token)
		if err != nil {
			return
		}
	}

	// 如果没有用户信息，根据手机号码查询该租户下是否有该往来单位，如果有则返回该往来单位信息
	if !exist {
		bizUnit, err = biz_unit_pb.NewClientBizUnitService().GetBizUnitDetailByID(ctx, biz_unit_pb.Req{Category: 2, Phone: p.Phone})
		if err != nil {
			return
		}
		if bizUnit.Id == 0 {
			// 注册客户
		}
		isBizUnit = true

		// 生成token
		token, err = tools.GenerateThirdPartyToken(bizUnit.Id)
		if err != nil {
			return
		}

		// 添加账号令牌绑定关系
		err = redis.AddMallBizUnitToken(context.Background(), bizUnit.Id, token)
		if err != nil {
			return
		}
	}

	// 缓存该用户登录选择的租户，没有选择的话默认使用最后一次登录的租户，获取登录态的时候使用
	redis.AddKeyUint64Value(context.Background(), token, tenantManagementId)

	data.Token = token
	data.TenantManagementId = tenantManagementId
	data.IsBizUnit = isBizUnit

	data.UserId = user.Id
	data.UserName = user.EmployeeName
	data.BizUnitId = bizUnit.Id
	data.BizUnitName = bizUnit.Name
	return
}
