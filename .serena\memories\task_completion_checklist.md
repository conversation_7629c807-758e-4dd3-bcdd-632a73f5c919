# 任务完成检查清单

## 代码开发完成后的必要步骤

### 1. 代码质量检查
- [ ] 运行 `go fmt ./...` 格式化代码
- [ ] 运行 `go vet ./...` 进行静态分析
- [ ] 检查代码是否符合项目命名约定
- [ ] 确保所有公开函数都有适当的注释

### 2. 依赖管理
- [ ] 运行 `go mod tidy` 整理依赖
- [ ] 检查是否有未使用的导入
- [ ] 确认新增依赖的必要性和安全性

### 3. 测试
- [ ] 运行 `go test ./...` 执行所有测试
- [ ] 确保新功能有相应的单元测试
- [ ] 进行集成测试（如果适用）
- [ ] 手动测试API接口

### 4. 文档更新
- [ ] 更新API文档（运行 `swag init` 生成Swagger文档）
- [ ] 更新README.md（如果有新功能或配置变更）
- [ ] 更新相关的技术文档

### 5. 代码生成
- [ ] 如果修改了枚举，运行 `generate` 生成_map.go文件
- [ ] 如果修改了枚举，运行 `stringer` 生成_string.go文件
- [ ] 检查生成的代码是否正确

### 6. 构建验证
- [ ] 运行 `go build -o hcscm.exe ./main.go` 确保能正常构建
- [ ] 测试构建的可执行文件能正常启动
- [ ] 检查配置文件是否正确

### 7. 数据库相关
- [ ] 如果有数据库变更，确保迁移脚本正确
- [ ] 测试数据库连接和操作
- [ ] 检查主从数据库配置

### 8. 性能检查
- [ ] 检查是否有潜在的性能问题
- [ ] 如果是关键路径，进行性能测试
- [ ] 检查内存泄漏（使用pprof工具）

### 9. 安全检查
- [ ] 检查是否有安全漏洞
- [ ] 确保敏感信息不在代码中硬编码
- [ ] 验证输入参数的合法性

### 10. 版本控制
- [ ] 提交前检查 `git status`
- [ ] 编写清晰的提交信息
- [ ] 确保不提交敏感信息或临时文件
- [ ] 推送到远程仓库前进行最后检查