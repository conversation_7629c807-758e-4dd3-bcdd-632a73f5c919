package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	consts "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"strconv"
	"time"
)

type FpmOutOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmOutOrderRepo(tx *mysql_base.Tx) *FpmOutOrderRepo {
	return &FpmOutOrderRepo{tx: tx}
}

func (r *FpmOutOrderRepo) Add(ctx context.Context, req *structure.AddFpmOutOrderParam) (data structure.AddFpmOutOrderData, err error) {

	var (
		// rLock *redis.LockForRedis
		info        = metadata.GetLoginInfo(ctx)
		orderPrefix mysqlSystem.OrderPrefix
		dateFormat  string
		numLength   int
		exist       bool
	)

	fpmOutOrder := model.NewFpmOutOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmOutOrder.BusinessClose = common_system.BusinessCloseNo
	fpmOutOrder.DepartmentId = info.GetDepartmentId()

	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
	} else {
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmOutOrder, req.OrderNoPre, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmOutOrder.OrderNo = orderNo
	fpmOutOrder.Number = int(number)

	price, weight, roll, length := req.GetTotalPWR(req.OutOrderType)
	fpmOutOrder.TotalPrice = price
	fpmOutOrder.TotalWeight = weight
	fpmOutOrder.TotalRoll = roll
	fpmOutOrder.TotalLength = length

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutOrder.UnitId = item.UnitId
		}
	}
	// fpmOutOrder.OutOrderType = fpmOutOrder.OutOrderType

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmOutOrder, err = mysql.MustCreateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		fpmOutOrderItem.ParentId = fpmOutOrder.Id
		fpmOutOrderItem.ParentOrderNo = fpmOutOrder.OrderNo

		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(req.OutOrderType)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmOutOrderItem.TotalWeight = tw
		fpmOutOrderItem.TotalPrice = tp
		fpmOutOrderItem.OutLength = tl
		fpmOutOrderItem.WeightError = weightError
		fpmOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmOutOrderItem.PaperTubeWeight = tpp
		fpmOutOrderItem.SettleWeight = tsw
		fpmOutOrderItem.ActuallyWeight = taw
		fpmOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmOutOrderItem.Id
			itemFc.WarehouseId = fpmOutOrder.WarehouseId
			itemFc.WarehouseOutType = fpmOutOrder.OutOrderType
			itemFc.WarehouseOutOrderId = fpmOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmOutOrder.OrderNo
			itemFc.OrderTime = fpmOutOrder.WarehouseOutTime
			itemFc.SumStockId = item.SumStockId
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmOutOrder.Id
	return
}

func (r *FpmOutOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmOutOrderParam) (data structure.UpdateFpmOutOrderData, err error) {
	var (
		fpmOutOrder   model.FpmOutOrder
		itemModel     model.FpmOutOrderItem
		findCodeModel model.FpmOutOrderItemFc
		itemList      model.FpmOutOrderItemList
	)
	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmOutOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	fpmOutOrder.UpdateFpmOutOrder(ctx, req)

	price, weight, roll, length := req.GetTotalPWR(req.OutOrderType)
	fpmOutOrder.TotalPrice = price
	fpmOutOrder.TotalWeight = weight
	fpmOutOrder.TotalRoll = roll
	fpmOutOrder.TotalLength = length

	if fpmOutOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		fcList, _ := mysql.FindFpmOutOrderItemFcByParenTIDs(r.tx, itemIds)
		if len(fcList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
			if err != nil {
				return
			}
		}
	}

	// 新增成品信息
	for _, item := range req.ItemData {
		fpmOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		fpmOutOrderItem.ParentId = fpmOutOrder.Id
		fpmOutOrderItem.ParentOrderNo = fpmOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(req.OutOrderType)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmOutOrderItem.TotalWeight = tw
		fpmOutOrderItem.TotalPrice = tp
		fpmOutOrderItem.OutLength = tl
		fpmOutOrderItem.WeightError = weightError
		fpmOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmOutOrderItem.PaperTubeWeight = tpp
		fpmOutOrderItem.SettleWeight = tsw
		fpmOutOrderItem.ActuallyWeight = taw
		fpmOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmOutOrderItem.Id
			itemFc.WarehouseId = fpmOutOrder.WarehouseId
			itemFc.WarehouseOutType = fpmOutOrder.OutOrderType
			itemFc.WarehouseOutOrderId = fpmOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmOutOrder.OrderNo
			itemFc.OrderTime = fpmOutOrder.WarehouseOutTime
			itemFc.SumStockId = item.SumStockId
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmOutOrder.Id
	return
}

func (r *FpmOutOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmOutOrderBusinessCloseParam) (data structure.UpdateFpmOutOrderData, err error) {
	var (
		fpmOutOrder model.FpmOutOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, v)
		if err != nil {
			return
		}
		// 更新业务状态
		err = fpmOutOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmOutOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (
	data structure.UpdateFpmOutOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	modifyFpmInOrders structure.ModifyFpmInOrderList,
	err error,
) {
	var (
		fpmOutOrder model.FpmOutOrder
		items       model.FpmOutOrderItemList
	)

	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	items, err = mysql.FindFpmOutOrderItemByParentID(r.tx, id)
	if err != nil {
		return
	}

	for _, item := range items {
		if item.FpmInOrderItemId != 0 {
			modifyFpmInOrders = append(modifyFpmInOrders, structure.ModifyFpmInOrder{
				Id:           item.FpmInOrderItemId,
				ReturnRoll:   item.OutRoll,
				ReturnWeight: item.SettleWeight,
				ReturnLength: item.OutLength,
			})
		}
	}

	updateItems, err = r.judgeAuditPass(id, fpmOutOrder, ctx)
	if err != nil {
		return
	}

	// 审核
	err = fpmOutOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}
	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmOutOrder.Id, true)
	if err != nil {
		return
	}
	data.WarehouseOutTime = tools.MyTime(fpmOutOrder.WarehouseOutTime)
	data.OrderNo = fpmOutOrder.OrderNo
	data.ArrangeId = fpmOutOrder.ArrangeOrderId
	return
}

func (r *FpmOutOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (data structure.UpdateFpmOutOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, modifyFpmInOrders structure.ModifyFpmInOrderList, err error) {
	var (
		fpmOutOrder model.FpmOutOrder
		items       model.FpmOutOrderItemList
	)

	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	items, err = mysql.FindFpmOutOrderItemByParentID(r.tx, id)
	if err != nil {
		return
	}

	for _, item := range items {
		if item.FpmInOrderItemId != 0 {
			modifyFpmInOrders = append(modifyFpmInOrders, structure.ModifyFpmInOrder{
				Id:           item.FpmInOrderItemId,
				ReturnRoll:   -item.OutRoll,
				ReturnWeight: -item.SettleWeight,
				ReturnLength: -item.OutLength,
			})
		}
	}

	updateItems, err = r.judgeAuditWait(id, fpmOutOrder, ctx)
	if err != nil {
		return
	}

	// 消审
	err = fpmOutOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}
	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmOutOrder.Id, false)
	if err != nil {
		return
	}
	data.WarehouseOutTime = tools.MyTime(fpmOutOrder.WarehouseOutTime)
	data.ArrangeId = fpmOutOrder.ArrangeOrderId
	return
}

func (r *FpmOutOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmOutOrderStatusData, err error) {
	var (
		fpmOutOrder model.FpmOutOrder
	)

	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 拒绝/驳回
	err = fpmOutOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmOutOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64, arrangeOrderId uint64) (data structure.UpdateFpmOutOrderStatusData, err error) {
	var (
		fpmOutOrder model.FpmOutOrder
		fcOutList   model.FpmOutOrderItemFcList
	)

	if arrangeOrderId > 0 {
		fpmOutOrder, err = mysql.MustFirstFpmOutOrderByArrangeOrderId(r.tx, arrangeOrderId)
		if err != nil || fpmOutOrder.Id == 0 {
			return
		}
	} else {
		fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
		if err != nil {
			return
		}
	}

	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 作废
	err = fpmOutOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}

	data.Id = fpmOutOrder.Id
	data.ArrangeId = fpmOutOrder.ArrangeOrderId

	// 获取细码的库存id，用来解除占用
	if fpmOutOrder.ArrangeOrderId > 0 {
		fcOutList, _ = mysql.FindFpmOutOrderItemFcByOrderId(r.tx, fpmOutOrder.Id)
		if len(fcOutList) > 0 {
			data.StockDetailIds = fcOutList.GetStockIDs()
		}
	}

	return
}

func (r *FpmOutOrderRepo) Get(ctx context.Context, req *structure.GetFpmOutOrderQuery) (data structure.GetFpmOutOrderData, err error) {
	var (
		fpmOutOrder     model.FpmOutOrder
		itemDatas       model.FpmOutOrderItemList
		fineCodeList    model.FpmOutOrderItemFcList
		detailStockList model.StockProductDetailList
		warehousePB     = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB          = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc          = dictionary.NewDictionaryClient()
	)
	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmOutOrderData{}
	r.swapListModel2Data(fpmOutOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindFpmOutOrderItemFcByParenTIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	mUnitFcIds := mysql_base.GetUInt64ListV2("measurement_unit_id", itemDatas, fineCodeList)
	wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
	stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
	unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitFcIds)
	binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
	detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
	if err != nil {
		return
	}
	dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	var (
		countSaleWeightMap = make(map[uint64]int)
	)
	for _, _ordersItem := range itemDatas {
		if _ordersItem.AuxiliaryUnitId != 0 {
			if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
				countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
			} else {
				countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.OutLength
			}
		} else {
			countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
		}
	}
	o.MergeWeightInfo = func() (str string) {
		for k, v := range countSaleWeightMap {
			fmtRound := tools.GetRound(v, 2)
			if str == "" {
				str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
			} else {
				str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
			}
		}
		return
	}()

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmOutOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmOutOrder.AuditStatus != common_system.OrderStatusAudited {
			stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
				StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
			for _, v := range stockList {
				// 库存信息
				itemGetData.SumStockRoll = v.AvailableRoll
				itemGetData.SumStockWeight = v.AvailableWeight
				itemGetData.SumStockLength = v.Length
			}
		}
		// 添加细码信息
		fcList := fineCodeList.PickFcListByParentId(itemData.Id)
		for _, fineCode := range fcList {
			fineCodeGetData := structure.GetFpmOutOrderItemFcData{}
			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)

			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
			fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
			fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.OrderTime = tools.MyTime(fineCode.OrderTime)
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			fineCodeGetData.IsBooked = fineCode.IsBooked
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			detailStock := detailStockList.Pick(fineCode.StockId)

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)

			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmOutOrderRepo) GetList(ctx context.Context, req *structure.GetFpmOutOrderListQuery) (list structure.GetFpmOutOrderDataList, total int, err error) {
	var (
		orders      model.FpmOutOrderList
		ordersItems model.FpmOutOrderItemList
		bizPB       = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmOutOrder(r.tx, req)
	if err != nil {
		return
	}

	ordersItems, err = mysql.FindFpmOutOrderItemByParenTIDs(r.tx, orders.GetIds())
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", orders, ordersItems)
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		var (
			countSaleWeightMap = make(map[uint64]int)
		)
		_ordersItems := ordersItems.PickByParentId(src.Id)
		for _, _ordersItem := range _ordersItems {
			if _ordersItem.AuxiliaryUnitId != 0 {
				if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
				} else {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.OutLength
				}
			} else {
				countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
			}
		}
		dst := structure.GetFpmOutOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.OutOrderType = src.OutOrderType
		dst.ArrangeOrderId = src.ArrangeOrderId
		dst.ArrangeOrderNo = src.ArrangeOrderNo
		dst.SaleSystemId = src.SaleSystemId
		dst.BizUnitId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.TotalPrice = src.TotalPrice
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.SaleMode = src.SaleMode

		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.OutOrderTypeName = src.OutOrderType.String()
		dst.UnitName = unitNameMap[src.UnitId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.BizUnitName = bizNameMap[src.BizUnitId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.VoucherNumber = src.VoucherNumber
		dst.TextureUrl = src.TextureUrl
		dst.SaleModeName = src.SaleMode.String()
		dst.MergeWeightInfo = func() (str string) {
			for k, v := range countSaleWeightMap {
				fmtRound := tools.GetRound(v, 2)
				if str == "" {
					str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				} else {
					str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				}
			}
			return
		}()
		list = append(list, dst)
	}
	return
}

func (r *FpmOutOrderRepo) swapListModel2Data(src model.FpmOutOrder, dst *structure.GetFpmOutOrderData, ctx context.Context) {
	var (
		bizService  = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		userPB      = empl_pb.NewClientEmployeeService()
		userName    = make(map[uint64]string)
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleSysMap, err2 := saleSysPB.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	userName, _ = userPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.OutOrderType = src.OutOrderType
	dst.ArrangeOrderId = src.ArrangeOrderId
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.SaleSystemId = src.SaleSystemId
	dst.BizUnitId = src.BizUnitId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
	dst.StoreKeeperId = src.StoreKeeperId
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.TotalPrice = src.TotalPrice
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.SaleMode = src.SaleMode

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.WarehouseName = warehouseName
	dst.OutOrderTypeName = src.OutOrderType.String()
	dst.SaleModeName = src.SaleMode.String()
	dst.UnitName = unitName
	if val, ok := saleSysMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.BizUnitName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	dst.VoucherNumber = src.VoucherNumber
	dst.TextureUrl = src.TextureUrl
}

func (r *FpmOutOrderRepo) swapItemModel2Data(src model.FpmOutOrderItem, dst *structure.GetFpmOutOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService           = biz_pb.NewClientBizUnitService()
		pLevelPB             = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB               = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dictionaryDetailsSvc = dictionary.NewDictionaryClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, []uint64{src.UnitId, src.AuxiliaryUnitId})
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)
	dictionaryDetailIds := mysql_base.GetUInt64List(getColor, "dictionary_detail_id")
	dictionaryDetailsName, _ := dictionaryDetailsSvc.GetDictionaryNameByIds(ctx, dictionaryDetailIds)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.QuoteOrderNo = src.QuoteOrderNo
	dst.QuoteOrderItemId = src.QuoteOrderItemId
	dst.ProductCode = src.ProductCode
	dst.ProductName = src.ProductName
	dst.CustomerId = src.CustomerId
	dst.ProductColorId = src.ProductColorId
	dst.ProductColorCode = src.ProductColorCode
	dst.ProductColorName = src.ProductColorName
	dst.ProductLevelId = src.ProductLevelId
	dst.ProductWidth = src.ProductWidth
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductGramWeight = src.ProductGramWeight
	dst.ProductRemark = src.ProductRemark
	dst.ProductCraft = src.ProductCraft
	dst.ProductIngredient = src.ProductIngredient
	dst.OutRoll = src.OutRoll
	dst.SumStockId = src.SumStockId
	dst.TotalWeight = src.TotalWeight
	dst.WeightError = src.WeightError
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.SettleWeight = src.SettleWeight
	dst.UnitId = src.UnitId
	dst.AuxiliaryUnitId = src.AuxiliaryUnitId
	dst.UnitPrice = src.UnitPrice
	dst.OutLength = src.OutLength
	dst.LengthUnitPrice = src.LengthUnitPrice
	dst.OtherPrice = src.OtherPrice
	dst.TotalPrice = src.TotalPrice
	dst.Remark = src.Remark
	dst.ProductId = src.ProductId
	dst.SumStockId = src.SumStockId
	dst.ActuallyWeight = src.ActuallyWeight
	dst.SettleErrorWeight = src.SettleErrorWeight
	dst.ArrangeItemId = src.ArrangeItemId

	// 转义
	dst.UnitName = unitName[src.UnitId]
	dst.AuxiliaryUnitName = unitName[src.AuxiliaryUnitId]
	if val, ok := customerMap[src.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
	dst.SumStockRoll = src.SumStockRoll
	dst.SumStockLength = src.SumStockLength
	dst.SumStockWeight = src.SumStockWeight

	dst.BuildFPResp(getColor.FinishProductWidth, getColor.FinishProductGramWeight, dictionaryDetailsName[getColor.FinishProductWidthUnitId][1],
		dictionaryDetailsName[getColor.FinishProductGramWeightUnitId][1], getColor.FinishProductWidthUnitId, getColor.FinishProductGramWeightUnitId)
}

func (r *FpmOutOrderRepo) swapFcModel2Data(fineCode model.FpmOutOrderItemFc, fineCodeGetData *structure.GetFpmOutOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, fineCode.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, fineCode.UnitId)

	fineCodeGetData.Id = fineCode.Id
	fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
	fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
	fineCodeGetData.CreatorId = fineCode.CreatorId
	fineCodeGetData.CreatorName = fineCode.CreatorName
	fineCodeGetData.UpdaterId = fineCode.UpdaterId
	fineCodeGetData.UpdateUserName = fineCode.UpdaterName
	fineCodeGetData.ParentId = fineCode.ParentId
	fineCodeGetData.Roll = fineCode.Roll
	fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
	fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
	fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
	fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
	fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
	fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
	fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
	fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
	fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
	fineCodeGetData.StockId = fineCode.StockId
	fineCodeGetData.SumStockId = fineCode.SumStockId
	fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
	fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
	fineCodeGetData.WeightError = fineCode.WeightError
	fineCodeGetData.UnitId = fineCode.UnitId
	fineCodeGetData.Length = fineCode.Length
	fineCodeGetData.SettleWeight = fineCode.SettleWeight
	fineCodeGetData.DigitalCode = fineCode.DigitalCode
	fineCodeGetData.ShelfNo = fineCode.ShelfNo
	fineCodeGetData.ContractNumber = fineCode.ContractNumber
	fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
	fineCodeGetData.AccountNum = fineCode.AccountNum
	fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
	fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
	fineCodeGetData.ProductWidth = fineCode.ProductWidth
	fineCodeGetData.ProductGramWeight = fineCode.ProductGramWeight
	fineCodeGetData.StockRemark = fineCode.StockRemark
	fineCodeGetData.Remark = fineCode.Remark
	fineCodeGetData.InternalRemark = fineCode.InternalRemark
	fineCodeGetData.ScanUserId = fineCode.ScanUserId
	fineCodeGetData.ScanUserName = fineCode.ScanUserName
	fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
	fineCodeGetData.WarehouseId = fineCode.WarehouseId
	fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
	fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
	fineCodeGetData.OrderTime = tools.MyTime(fineCode.OrderTime)
	fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
	fineCodeGetData.IsBooked = fineCode.IsBooked
	// 转义
	fineCodeGetData.WarehouseBinName = binName
	fineCodeGetData.UnitName = unitName
}

func (r *FpmOutOrderRepo) judgeAuditPass(id uint64, order model.FpmOutOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		// arrangeItemIds       = make([]uint64, 0)
		arrangeOrder model.FpmArrangeOrder
	)

	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		totalRoll := 0
		totalLength := 0

		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		swap2StockFieldParam.WarehouseId = order.WarehouseId

		fineCodeList, _ := mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		if item.OutRoll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(errors.NewError(errors.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(fineCodeList) == 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "必须填入细码，错误行："+item.ProductName))
			return
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			// 通过arrangeItem.ParentId获取配布单
			arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, arrangeItem.ParentId)
			if err != nil {
				return
			}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll -= arrangeItem.PushRoll
			updateItemWeight.BookWeight -= arrangeItem.PushWeight
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.BookOrderId = arrangeOrder.SrcId
			updateItemWeight.OrderType = getBookOrderType(order.OutOrderType, true)
			updateItemWeight.Type = 4
			// 汇总key
			updateItemWeight.StockProductKey = fmt.Sprintf("%v", updateItemWeight.StockProductId)
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
		for _, fineCode := range fineCodeList {
			totalRoll = totalRoll + fineCode.Roll
			totalLength += fineCode.Length
			weight := fineCode.ToUpdateStockProductDetailParamBack(ctx, swap2StockFieldParam)
			// 调整单
			if order.OutOrderType == consts.WarehouseGoodOutTypeAdjust {
				weight.Type = 5
				weight.ProductRemark = item.ProductRemark
			}
			updateWeight = append(updateWeight, weight)
		}
		//
		if totalRoll != item.OutRoll {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.OutLength > 0 && totalLength > 0 && item.OutLength != totalLength {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}
	updateBookWeight = append(updateBookWeight, updateWeight...)
	updateItems = updateBookWeight
	return
}

func (r *FpmOutOrderRepo) judgeAuditWait(id uint64, fpmOutOrder model.FpmOutOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
	)

	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		swap2StockFieldParam.WarehouseId = fpmOutOrder.WarehouseId
		fineCodeList, _ := mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		for _, fineCode := range fineCodeList {
			weight := fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam)
			// 调整单
			if fpmOutOrder.OutOrderType == consts.WarehouseGoodOutTypeAdjust {
				weight.Type = 5
			}
			updateWeight = append(updateWeight, weight)
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			updateItemWeight.StockProductId = arrangeItem.SumStockId
			updateItemWeight.BookRoll += arrangeItem.PushRoll
			updateItemWeight.BookWeight += arrangeItem.PushWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = fpmOutOrder.Id
			updateItemWeight.OrderNo = fpmOutOrder.OrderNo
			updateItemWeight.OrderType = getBookOrderType(fpmOutOrder.OutOrderType, false)
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
	}
	updateWeight = append(updateWeight, updateBookWeight...)
	updateItems = updateWeight
	return
}

func getBookOrderType(outType consts.WarehouseGoodOutType, isPass bool) common_system.BookOrderType {
	switch outType {
	case consts.WarehouseGoodOutTypePurchaseReturn:
		if isPass {
			return common_system.BookOrderTypeProductPrtOutPass
		}
		return common_system.BookOrderTypeProductPrtOutWait
	case consts.WarehouseGoodOutTypeDeduction:
		if isPass {
			return common_system.BookOrderTypeProductDeductionOutPass
		}
		return common_system.BookOrderTypeProductDeductionOutWait
	case consts.WarehouseGoodOutTypeCheck:
		if isPass {
			return common_system.BookOrderTypeProductCheckOutPass
		}
		return common_system.BookOrderTypeProductCheckOutWait
	case consts.WarehouseGoodOutTypeAdjust:
		if isPass {
			return common_system.BookOrderTypeProductAdjustOutPass
		}
		return common_system.BookOrderTypeProductAdjustOutWait
	default:
		if isPass {
			return common_system.BookOrderTypeProductOtherOutPass
		}
		return common_system.BookOrderTypeProductOtherOutWait
	}
}

// 根据配布单消审出仓单
func (r *FpmOutOrderRepo) UpdateStatusWaitUseByChangeOrder(ctx context.Context, id uint64) (data structure.UpdateFpmOutOrderStatusData, err error) {
	var (
		fpmOutOrder model.FpmOutOrder
		info        = metadata.GetLoginInfo(ctx)
	)

	fpmOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 单据为待审核/已驳回/已作废状态
	if fpmOutOrder.AuditStatus == common_system.OrderStatusPendingAudit || fpmOutOrder.AuditStatus == common_system.OrderStatusRejected || fpmOutOrder.AuditStatus == common_system.OrderStatusVoided {
		fpmOutOrder.AuditorName = info.GetUserName()
		fpmOutOrder.AuditorId = info.GetUserId()
		fpmOutOrder.AuditDate = time.Now()
		fpmOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}
	// 单据已审核状态
	if fpmOutOrder.AuditStatus == common_system.OrderStatusAudited {
		// todo: 已审核状态逻辑待补充
	}
	fpmOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmOutOrder)
	if err != nil {
		return
	}

	data.ArrangeId = fpmOutOrder.ArrangeOrderId
	return
}

func (r *FpmOutOrderRepo) GetIDsBySrcID(ctx context.Context, srcIDs []uint64) (ids []uint64, err error) {
	var (
		fpmOutOrders model.FpmOutOrderList
	)
	ids = make([]uint64, 0)
	fpmOutOrders, err = mysql.FindFpmOutOrderBySrcOrderIDs(r.tx, srcIDs)
	if err != nil {
		return
	}
	for _, fpmOutOrder := range fpmOutOrders {
		ids = append(ids, fpmOutOrder.Id)
	}
	return
}

func (r *FpmOutOrderRepo) GetItemList(ctx context.Context, itemIds []uint64) (items model.FpmOutOrderItemList, err error) {
	items, err = mysql.FindFpmOutOrderItemByIDs(r.tx, itemIds)
	return
}
