package tenant_management

import (
	"context"
	"github.com/gin-gonic/gin"
	common "hcscm/common/system_consts"
	"hcscm/server/system"
	"hcscm/service/tenant_management"
	structure_base "hcscm/structure/system"
	structure "hcscm/structure/tenant_management"
)

// @Tags 【码单管理】
// @Summary 获取码单OCR套餐列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     code_list_name     query     string  false  "账套名称"
// @Param     contact_name     query     string  false  "联系人"
// @Param     phone     query     int  false  "联系电话"
// @Param     tenant_management_status     query     int  false  "状态"
// @Success 200 {object}  structure.GetTenantManagementListDataList{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/getElectronicColorCardList [get]
func GetEleColorCardManagementList(c *gin.Context) {
	GetCodeListOrcManagementList(c)
}

// @Tags 【电子色卡管理】
// @Summary 获取电子色卡套餐列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     code_list_name     query     string  false  "账套名称"
// @Param     contact_name     query     string  false  "联系人"
// @Param     phone     query     int  false  "联系电话"
// @Param     tenant_management_status     query     int  false  "状态"
// @Success 200 {object}  structure.GetTenantManagementListDataList{}
// @Router /hcscm/admin/v1/tenantManagement/codeListOrcManagement/getCodeListOrcManagementList [get]
func GetCodeListOrcManagementList(c *gin.Context) {
	var (
		query = structure.GetTenantManagementListQuery{}
		list  = make(structure.GetTenantManagementListDataList, 0)
		err   error
		total int
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, &query)
	if err != nil {
		return
	}

	list, total, err = tenant_management.NewCodeListOrcManagementLogic(c).SearchList(ctx, query)
	if err != nil {
		return
	}
	return
}

// @Tags 【码单管理】
// @Summary 更新码单OCR套餐账套名称
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateCodeListOrcManagementNameParam{}  true "更新码单管理账套名称"
// @Success 200 {object}  structure.ResTenantManagementIDData{}
// @Router /hcscm/mp/v1/tenantManagement/codeListOrcManagement/updateCodeListOrcManagementCodeListName [put]
func UpdateCodeListOrcManagementCodeListName(c *gin.Context) {
	var (
		param = structure.UpdateCodeListOrcManagementNameParam{}
		data  = structure.ResTenantManagementIDData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	data, err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateCodeListName(ctx, param)
	if err != nil {
		return
	}

	data, err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateCodeListName(context.Background(), param)
	if err != nil {
		return
	}
	return
}

// @Tags 【码单管理】
// @Summary 启用码单OCR套餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.EnableTenantManagementParam{}  true "启用码单管理"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/tenantManagement/codeListOrcManagement/updateCodeListOrcStatusEnable [put]
func UpdateCodeListOrcStatusEnable(c *gin.Context) {
	var (
		param = structure.EnableCodeListOrcManagementParam{}
		data  = structure_base.ResponseData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateStatusEnable(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【电子色卡管理】
// @Summary 启用电子色卡套餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.EnableCodeListOrcManagementParam{}  true "启用电子色卡管理"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/updateElectronicColorCardStatusEnable [put]
func UpdateStatusEnableEleColorCard(c *gin.Context) {
	var (
		param = structure.EnableCodeListOrcManagementParam{}
		data  = structure_base.ResponseData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateStatusEnableEleColorCard(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【码单管理】
// @Summary 禁用码单OCR套餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.DisableCodeListOrcManagementParam{}  true "创建租户套餐"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/tenantManagement/codeListOrcManagement/updateCodeListOrcStatusDisable [put]
func UpdateCodeListOrcStatusDisable(c *gin.Context) {
	var (
		param = structure.DisableCodeListOrcManagementParam{}
		data  = structure_base.ResponseData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateStatusDisable(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【电子色卡管理】
// @Summary 禁用电子色卡套餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.DisableCodeListOrcManagementParam{}  true "创建租户套餐"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/updateElectronicColorCardStatusDisable [put]
func UpdateStatusDisableEleColorCard(c *gin.Context) {
	var (
		param = structure.DisableCodeListOrcManagementParam{}
		data  = structure_base.ResponseData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateStatusDisableEleColorCard(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【电子色卡管理】
// @Summary 启用搜索图片功能
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.EnableCodeListOrcManagementParam{}  true "创建租户套餐"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/enable_search_image [put]
func UpdateStatusEnableSearchImage(c *gin.Context) {
	var (
		param = structure.EnableSearchImageParam{}
		data  = structure_base.ResponseData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	param.SearchImageStatus = common.ElectronicColorCardStatusEnable
	err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateStatusSearchImage(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【电子色卡管理】
// @Summary 禁用搜索图片功能
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.EnableCodeListOrcManagementParam{}  true "创建租户套餐"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/disable_search_image [put]
func UpdateStatusDisableSearchImage(c *gin.Context) {
	var (
		param = structure.EnableSearchImageParam{}
		data  = structure_base.ResponseData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	param.SearchImageStatus = common.ElectronicColorCardStatusDisable
	err = tenant_management.NewCodeListOrcManagementLogic(c).UpdateStatusSearchImage(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【码单管理】
// @Summary 充值码单OCR套餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.RechargeParam{}  true "充值"
// @Success 200 {object}  structure.RechargeData{}
// @Router /hcscm/admin/v1/tenantManagement/codeListOrcManagement/recharge [post]
func Recharge(c *gin.Context) {
	var (
		param = structure.RechargeParam{}
		data  = structure.RechargeData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	data, err = tenant_management.NewCodeListOrcManagementLogic(c).Recharge(ctx, param)
	if err != nil {
		return
	}
	return
}

// @Tags 【电子色卡管理】
// @Summary 充值电子色卡套餐
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.RechargeParam{}  true "充值"
// @Success 200 {object}  structure.RechargeData{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/rechargeEleColorCard [post]
func EleColorCardServerRecharge(c *gin.Context) {
	var (
		param = structure.RechargeParam{}
		data  = structure.RechargeData{}
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	// 调用相同的底层逻辑处理充值
	data, err = tenant_management.NewCodeListOrcManagementLogic(c).EleColorCardServerRecharge(ctx, param)
	if err != nil {
		return
	}
	return
}

// 充值记录

// @Tags 【码单管理】
// @Summary 获取码单OCR套餐充值记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id     query     string  false  "Id"
// @Param     update_name     query     string  false  "操作人"
// @Success 200 {object}  structure.GetRechargeHistoryListDataList{}
// @Router /hcscm/admin/v1/tenantManagement/codeListOrcManagement/getRechargeHistoryList [get]
func GetRechargeHistoryList(c *gin.Context) {
	var (
		query = structure.RechargeHistoryListQuery{}
		list  = make(structure.GetRechargeHistoryListDataList, 0)
		err   error
		total int
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, &query)
	if err != nil {
		return
	}

	list, total, err = tenant_management.NewCodeListOrcManagementLogic(c).GetRechargeHistorys(ctx, query)
	if err != nil {
		return
	}
}

// 充值记录

// @Tags 【电子色卡管理】
// @Summary 获取电子色卡管理套餐充值记录列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id     query     string  false  "Id"
// @Param     update_name     query     string  false  "操作人"
// @Success 200 {object}  structure.GetRechargeHistoryListDataList{}
// @Router /hcscm/admin/v1/tenantManagement/electronicColorCard/getEleColorCardRechargeHistoryList [get]
func GetEleColorCardHistoryList(c *gin.Context) {
	GetRechargeHistoryList(c)
}
