package tenant_management

import (
	"fmt"
	"github.com/mozillazg/go-pinyin"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	"hcscm/structure/tenant_management"
	"hcscm/tools"
	"hcscm/vars"
	"strconv"
	"time"
)

type TenantManagement struct {
	mysql_base.Model
	Id                     uint64                        `gorm:"column:id" relate:"tenant_management_id"` // Id
	TenantPackageID        uint64                        `gorm:"column:tenant_package_id"`                // 套餐ID
	Deadline               time.Time                     `gorm:"column:deadline"`                         // 截止日期
	PackageNumberOfPeople  int                           `gorm:"column:package_number_of_people"`         // 套餐人数
	TenantManagementStatus common.TenantManagementStatus `gorm:"column:tenant_management_status"`         // 状态
	TenantPhoneNumber      string                        `gorm:"column:tenant_phone_number"`              // 租户手机号
	TenantContacts         string                        `gorm:"column:tenant_phone_contacts"`            // 租户联系人
	TenantCompanyName      string                        `gorm:"column:tenant_company_name"`              // 租户公司名称
	DatabaseName           string                        `gorm:"column:database_name"`                    // 数据库名称(初始化后不能修改)
	ActivationTime         time.Time                     `gorm:"column:activation_time"`                  // 激活时间
	ConfigJson             string                        `gorm:"column:config_json"`                      // 配置文件json
	AssignPort             int                           `gorm:"column:assign_port"`                      // 分配端口
	Secret                 string                        `gorm:"column:secret"`                           // 租户秘钥(唯一，请勿外传)
	RequestDomain          string                        `gorm:"column:request_domain"`                   // 请求域名
	RequestDomainPrefix    string                        `gorm:"column:request_domain_prefix"`            // 请求域名前缀
	// 码单有效期
	CodeListOrcDeadLine    time.Time             `gorm:"column:code_list_orc_dead_line"`    // 码单识别截止日期
	CodeListOrcStatus      common.CodeListStatus `gorm:"column:code_list_orc_status"`       // 码单识别状态
	CodeListOrcIsRecognize bool                  `gorm:"column:code_list_orc_is_recognize"` // 是否识别过

	ElectronicColorCardStatus   common.ElectronicColorCardStatus `gorm:"column:electronic_color_card_status"`    // 电子色卡状态
	ElectronicColorCardDeadLine time.Time                        `gorm:"column:electronic_color_card_dead_line"` // 电子色卡截止日期
	SearchImageStatus           common.ElectronicColorCardStatus `gorm:"column:search_image_status"`             // 搜索图片状态(依赖电子色卡，电子色卡生效时，该功能才能生效)
	SearchImageDeadLine         time.Time                        `gorm:"column:search_image_dead_line"`          // 搜索图片截止日期
}

func (TenantManagement) OnceComplexKey() [][]string {
	return [][]string{}
}

func (r TenantManagement) GetId() uint64 {
	return r.Id
}

func (TenantManagement) TableName() string {
	return "tenant_management"
}

func (TenantManagement) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeTenantManagementNotExist
}

func (TenantManagement) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeTenantManagementAlreadyExist
}

type TenantManagementList []TenantManagement

func (l TenantManagementList) Pick(id uint64) TenantManagement {
	for _, tenantManagement := range l {
		if tenantManagement.Id == id {
			return tenantManagement
		}
	}
	return TenantManagement{}
}

func NewTenantManagement(tenantPackage TenantPackage, param tenant_management.AddTenantManagementParam, assignPort int) TenantManagement {
	return TenantManagement{
		Id:                     vars.Snowflake.GenerateId().UInt64(),
		TenantPackageID:        tenantPackage.Id,
		PackageNumberOfPeople:  tenantPackage.PackageNumberOfPeople,
		TenantManagementStatus: common.TenantManagementStatusNotActivation,
		TenantPhoneNumber:      param.Phone,
		TenantCompanyName:      param.CompanyName,
		DatabaseName:           fmt.Sprintf("%s_%s_%d", pinyin.Slug(param.CompanyName, pinyin.Args{Style: pinyin.FirstLetter}), param.Phone, time.Now().Unix()),
		AssignPort:             assignPort,
		TenantContacts:         param.TenantContacts,
		Secret:                 tools.GetRandomSecretString(192),
	}
}

func NewFirstRegistrationTenantManagement(tenantPackage TenantPackage, phone string, assignPort int, tenantContacts string) TenantManagement {
	return TenantManagement{
		Id:                     vars.Snowflake.GenerateId().UInt64(),
		TenantPackageID:        tenantPackage.Id,
		PackageNumberOfPeople:  0,
		TenantManagementStatus: common.TenantManagementStatusNormal,
		TenantPhoneNumber:      phone,
		TenantCompanyName:      vars.TenantName,
		DatabaseName:           fmt.Sprintf("%s_%s_%d", pinyin.Slug(vars.TenantName, pinyin.Args{Style: pinyin.FirstLetter}), phone, time.Now().Unix()),
		AssignPort:             assignPort,
		Deadline:               time.Now().AddDate(0, 0, tenantPackage.PeriodOfValidityDays),
		TenantContacts:         tenantContacts,
		Secret:                 tools.GetRandomSecretString(192),
	}
}

func NewSubTenantTenantManagement(tenantManagement TenantManagement, param tenant_management.AddOrUpdateSubTenantManagementParam) TenantManagement {
	return TenantManagement{
		Id:                     vars.Snowflake.GenerateId().UInt64(),
		TenantPackageID:        tenantManagement.TenantPackageID,
		PackageNumberOfPeople:  tenantManagement.PackageNumberOfPeople,
		TenantManagementStatus: tenantManagement.TenantManagementStatus,
		TenantPhoneNumber:      param.Phone,
		TenantCompanyName:      param.UserName,
		DatabaseName:           tenantManagement.DatabaseName,
		AssignPort:             tenantManagement.AssignPort,
		Deadline:               tenantManagement.Deadline,
		TenantContacts:         tenantManagement.TenantContacts,
		Secret:                 tools.GetRandomSecretString(192),
	}
}

func (r *TenantManagement) PaySuccessUpdate(tenantPackage TenantPackage, configJson string) {
	r.Deadline = time.Now().AddDate(0, 0, tenantPackage.PeriodOfValidityDays)
	r.TenantManagementStatus = common.TenantManagementStatusNormal
	r.ActivationTime = time.Now()
	r.ConfigJson = configJson
}

func (r *TenantManagement) ExpireUpdate() {
	r.TenantManagementStatus = common.TenantManagementStatusExpire
}

func (r *TenantManagement) RenewUpdate(tenantPackage TenantPackage) {
	r.PackageNumberOfPeople += tenantPackage.PackageNumberOfPeople
	if r.Deadline.Before(time.Now().AddDate(0, 0, tenantPackage.PeriodOfValidityDays)) {
		r.Deadline = time.Now().AddDate(0, 0, tenantPackage.PeriodOfValidityDays)
		r.TenantManagementStatus = common.TenantManagementStatusNormal
	}
}

// ocr码单识别续费
func (r *TenantManagement) RenewUpdateOcr(deadline time.Time) {
	if r.CodeListOrcDeadLine.Before(deadline) {
		r.CodeListOrcDeadLine = deadline
		r.CodeListOrcStatus = common.CodeListStatusEnable
	}
}

// 电子色卡续费
func (r *TenantManagement) RenewUpdateEleColorCard(deadline time.Time) {
	if r.ElectronicColorCardDeadLine.Before(deadline) {
		r.ElectronicColorCardDeadLine = deadline
		r.ElectronicColorCardStatus = common.ElectronicColorCardStatusEnable
	}
}

// 搜索图片续费
func (r *TenantManagement) RenewUpdateSearchImage(deadline time.Time) {
	if r.SearchImageDeadLine.Before(deadline) {
		r.SearchImageDeadLine = deadline
		r.SearchImageStatus = common.ElectronicColorCardStatusEnable
	}
}

func (r *TenantManagement) CancelUpdate(tenantManagementPackageRels TenantManagementPackageRelList) {
	var haveNormal bool
	r.Deadline = r.CreateTime
	for _, tenantManagementPackageRel := range tenantManagementPackageRels {
		if tenantManagementPackageRel.TenantManagementPackageStatus != common.TenantManagementPackageStatusNormal {
			continue
		}
		if r.Deadline.Before(tenantManagementPackageRel.Deadline) {
			r.Deadline = tenantManagementPackageRel.Deadline
		}
		haveNormal = true
	}
	if !haveNormal {
		r.Deadline = time.Now()
		r.TenantManagementStatus = common.TenantManagementStatusExpire
	}
}

func (r TenantManagement) CheckSecret(secret string) bool {
	return secret == tools.StringDoubleMD5ToUpper(strconv.FormatUint(r.Id, 10), mysql_base.PasswordSecret1, mysql_base.PasswordSecret2)
}
