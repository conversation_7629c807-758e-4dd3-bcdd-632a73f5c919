// Code generated by "stringer -type=GenerateReportStatus --linecomment"; DO NOT EDIT.

package product

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[GenerateReportStatusNotGen-0]
	_ = x[GenerateReportStatusIsGen-1]
}

const _GenerateReportStatus_name = "未生成已生成"

var _GenerateReportStatus_index = [...]uint8{0, 9, 18}

func (i GenerateReportStatus) String() string {
	if i < 0 || i >= GenerateReportStatus(len(_GenerateReportStatus_index)-1) {
		return ""
	}
	return _GenerateReportStatus_name[_GenerateReportStatus_index[i]:_GenerateReportStatus_index[i+1]]
}
