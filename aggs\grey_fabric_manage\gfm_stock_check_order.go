package grey_fabric_manage

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	cus_const "hcscm/common/grey_fabric_manage"
	common_system "hcscm/common/system_consts"
	grey_fabric_pb "hcscm/extern/pb/basic_data/grey_fabric_info"
	baseInfoPB "hcscm/extern/pb/basic_data/info_basic_data"
	bizPB "hcscm/extern/pb/biz_unit"
	dic_pb "hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	saleSys "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	mysql "hcscm/model/mysql/grey_fabric_manage"
	"hcscm/model/mysql/mysql_base"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/grey_fabric_manage"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"time"
)

type GfmStockCheckOrderRepo struct {
	tx *mysql_base.Tx
}

func NewGfmStockCheckOrderRepo(tx *mysql_base.Tx) *GfmStockCheckOrderRepo {
	return &GfmStockCheckOrderRepo{tx: tx}
}

func (r *GfmStockCheckOrderRepo) Add(ctx context.Context, req *structure.AddGfmStockCheckOrderParam) (data structure.AddGfmStockCheckOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = saleSys.NewSaleSystemClient()
		saleSysData        = saleSys.Res{}
	)

	gfmStockCheckOrder := mysql.NewGfmStockCheckOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	gfmStockCheckOrder.AuditStatus = common_system.OrderStatusPendingAudit
	gfmStockCheckOrder.BusinessClose = common_system.BusinessCloseNo
	gfmStockCheckOrder.DepartmentId = info.GetDepartmentId()
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.GfmStockCheckOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.GfmStockCheckOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}

	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "grey_fabric_manage", gfmStockCheckOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	gfmStockCheckOrder.OrderNo = orderNo
	gfmStockCheckOrder.Number = int(number)

	aw, ar, bw, br, rw, rr := req.GetTotalPWR()
	gfmStockCheckOrder.TotalActuallyWeight = aw
	gfmStockCheckOrder.TotalActuallyRoll = ar
	gfmStockCheckOrder.TotalBeforeWeight = bw
	gfmStockCheckOrder.TotalBeforeRoll = br
	gfmStockCheckOrder.TotalResultWeight = rw
	gfmStockCheckOrder.TotalResultRoll = rr

	gfmStockCheckOrder, err = mysql.MustCreateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
	if err != nil {
		return
	}

	// 3. 新增坯布信息
	for _, item := range req.ItemData {
		item.OrderType = req.OrderType
		gfmStockCheckOrderItem := mysql.NewGfmStockCheckOrderItem(ctx, &item)
		gfmStockCheckOrderItem.GreyFabricStockCheckId = gfmStockCheckOrder.Id
		gfmStockCheckOrderItem.OrderNo = gfmStockCheckOrder.OrderNo
		gfmStockCheckOrderItem.OrderType = gfmStockCheckOrder.OrderType
		gfmStockCheckOrderItem.ActuallyWeight, gfmStockCheckOrderItem.ResultWeight, gfmStockCheckOrderItem.ResultRoll = item.GetTotalWP()
		gfmStockCheckOrderItem, err = mysql.MustCreateGfmStockCheckOrderItem(r.tx, gfmStockCheckOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFineCode := mysql.NewGfmStockCheckOrderItemFineCode(ctx, &fineCode)
			itemFineCode.GfmStockCheckItemId = gfmStockCheckOrderItem.Id
			itemFineCode, err = mysql.MustCreateGfmStockCheckOrderItemFineCode(r.tx, itemFineCode)
			if err != nil {
				return
			}
		}
	}

	data.Id = gfmStockCheckOrder.Id
	return
}

func (r *GfmStockCheckOrderRepo) Update(ctx context.Context, req *structure.UpdateGfmStockCheckOrderParam) (data structure.UpdateGfmStockCheckOrderData, err error) {
	var (
		gfmStockCheckOrder mysql.GfmStockCheckOrder
		itemModel          mysql.GfmStockCheckOrderItem
		findCodeModel      mysql.GfmStockCheckOrderItemFineCode
		fcList             mysql.GfmStockCheckOrderItemFineCodeList
		itemList           mysql.GfmStockCheckOrderItemList
	)
	gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := gfmStockCheckOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	gfmStockCheckOrder.UpdateGfmStockCheckOrder(ctx, req)

	aw, ar, bw, br, rw, rr := req.GetTotalPWR()
	gfmStockCheckOrder.TotalActuallyWeight = aw
	gfmStockCheckOrder.TotalActuallyRoll = ar
	gfmStockCheckOrder.TotalBeforeWeight = bw
	gfmStockCheckOrder.TotalBeforeRoll = br
	gfmStockCheckOrder.TotalResultWeight = rw
	gfmStockCheckOrder.TotalResultRoll = rr

	if gfmStockCheckOrder.AuditStatus == common_system.OrderStatusRejected {
		gfmStockCheckOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	gfmStockCheckOrder, err = mysql.MustUpdateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		fcList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, itemIds)
		if err != nil {
			return
		}
		if len(fcList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "gfm_stock_check_item_id")
			if err != nil {
				return
			}
		}
	}

	// 新增坯布信息
	for _, item := range req.ItemData {
		item.OrderType = req.OrderType
		gfmStockCheckOrderItem := mysql.NewGfmStockCheckOrderItem(ctx, &item)
		gfmStockCheckOrderItem.GreyFabricStockCheckId = gfmStockCheckOrder.Id
		gfmStockCheckOrderItem.OrderNo = gfmStockCheckOrder.OrderNo
		gfmStockCheckOrderItem.OrderType = gfmStockCheckOrder.OrderType
		gfmStockCheckOrderItem.ActuallyWeight, gfmStockCheckOrderItem.ResultWeight, gfmStockCheckOrderItem.ResultRoll = item.GetTotalWP()
		gfmStockCheckOrderItem, err = mysql.MustCreateGfmStockCheckOrderItem(r.tx, gfmStockCheckOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFineCode := mysql.NewGfmStockCheckOrderItemFineCode(ctx, &fineCode)
			itemFineCode.GfmStockCheckItemId = gfmStockCheckOrderItem.Id
			itemFineCode, err = mysql.MustCreateGfmStockCheckOrderItemFineCode(r.tx, itemFineCode)
			if err != nil {
				return
			}
		}
	}

	data.Id = gfmStockCheckOrder.Id
	return
}

func (r *GfmStockCheckOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateGfmStockCheckOrderBusinessCloseParam) (data structure.UpdateGfmStockCheckOrderData, err error) {
	var (
		gfmStockCheckOrder mysql.GfmStockCheckOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, v)
		if err != nil {
			return
		}
		err = gfmStockCheckOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		gfmStockCheckOrder, err = mysql.MustUpdateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *GfmStockCheckOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (
	data structure.UpdateGfmStockCheckOrderStatusData, addDetailParams structure.AddGfmWarehouseParamList, updateDetailParams structure.UpdateGfmWarehouseParamList, getItemAndFcIds structure.GetItemAndFcIds, addRecordItemList structure.AddGfmStockRecordParamList, err error) {
	var (
		gfmStockCheckOrder mysql.GfmStockCheckOrder
	)

	gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 审核
	err = gfmStockCheckOrder.Audit(ctx)
	if err != nil {
		return
	}

	// 在染坯布库存
	if gfmStockCheckOrder.OrderType == cus_const.GfmCheckOrderTypeDyeing {
		var addGfmWarehouseSummaryRecordParams = make(structure.AddGfmWarehouseSummaryRecordParamList, 0)
		itemList, _ := mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, id)
		for _, item := range itemList {
			if item.ResultRoll != 0 || item.ResultWeight != 0 {
				addGfmWarehouseSummaryRecordParam := structure.AddGfmWarehouseSummaryRecordParam{
					WarehouseSumId: item.WarehouseSumId,
					ChangeData:     tools.QueryTime(gfmStockCheckOrder.CheckTime.Format("2006-01-02")),
					// StockType:      cus_const.StockTypeDyeing,
					CheckRoll:   item.ResultRoll,
					CheckWeight: item.ResultWeight,
				}
				addGfmWarehouseSummaryRecordParams = append(addGfmWarehouseSummaryRecordParams, addGfmWarehouseSummaryRecordParam)
			}
		}
		data.AddGfmWarehouseSummaryRecords = addGfmWarehouseSummaryRecordParams
	} else {
		// 未染坯布库存
		addDetailParams, updateDetailParams, getItemAndFcIds, addRecordItemList, err = r.judgeAuditPass(id, gfmStockCheckOrder)
		if err != nil {
			return
		}
	}

	gfmStockCheckOrder, err = mysql.MustUpdateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
	if err != nil {
		return
	}

	return
}

func (r *GfmStockCheckOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (
	data structure.UpdateGfmStockCheckOrderStatusData, updateOutDetailParams structure.UpdateGfmWarehouseParamList, updateInDetailParams structure.UpdateGfmWarehouseParamList, err error) {
	var (
		gfmStockCheckOrder mysql.GfmStockCheckOrder
	)

	gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 消审
	err = gfmStockCheckOrder.Wait(ctx)
	if err != nil {
		return
	}

	// 在染坯布库存
	if gfmStockCheckOrder.OrderType == cus_const.GfmCheckOrderTypeDyeing {
		var addGfmWarehouseSummaryRecordParams = make(structure.AddGfmWarehouseSummaryRecordParamList, 0)
		itemList, _ := mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, id)
		for _, item := range itemList {
			if item.ResultRoll != 0 || item.ResultWeight != 0 {
				addGfmWarehouseSummaryRecordParam := structure.AddGfmWarehouseSummaryRecordParam{
					WarehouseSumId: item.WarehouseSumId,
					ChangeData:     tools.QueryTime(gfmStockCheckOrder.CheckTime.Format("2006-01-02")),
					// StockType:      cus_const.StockTypeDyeing,
					CheckRoll:   -item.ResultRoll,
					CheckWeight: -item.ResultWeight,
				}
				addGfmWarehouseSummaryRecordParams = append(addGfmWarehouseSummaryRecordParams, addGfmWarehouseSummaryRecordParam)
			}
		}
		data.AddGfmWarehouseSummaryRecords = addGfmWarehouseSummaryRecordParams
	} else {
		// 未染坯布库存
		updateOutDetailParams, err = r.judgeAuditWait(id, gfmStockCheckOrder)
		if err != nil {
			return
		}
	}

	gfmStockCheckOrder, err = mysql.MustUpdateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
	if err != nil {
		return
	}

	return
}

func (r *GfmStockCheckOrderRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateGfmStockCheckOrderStatusParam) (data structure.UpdateGfmStockCheckOrderStatusData, err error) {
	var (
		gfmStockCheckOrder mysql.GfmStockCheckOrder
	)

	ids := req.Id.ToUint64()
	for _, id := range ids {
		gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, id)
		if err != nil {
			return
		}

		// 作废
		err = gfmStockCheckOrder.Cancel(ctx)
		if err != nil {
			return
		}
		gfmStockCheckOrder, err = mysql.MustUpdateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
		if err != nil {
			return
		}

	}

	return
}

func (r *GfmStockCheckOrderRepo) UpdateStatusReject(ctx context.Context, req *structure.UpdateGfmStockCheckOrderStatusParam) (data structure.UpdateGfmStockCheckOrderStatusData, err error) {
	var (
		gfmStockCheckOrder mysql.GfmStockCheckOrder
	)

	ids := req.Id.ToUint64()
	for _, id := range ids {
		gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, id)
		if err != nil {
			return
		}

		// 驳回
		err = gfmStockCheckOrder.Reject(ctx)
		if err != nil {
			return
		}
		gfmStockCheckOrder, err = mysql.MustUpdateGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
		if err != nil {
			return
		}
	}

	return
}

func (r *GfmStockCheckOrderRepo) Delete(ctx context.Context, req *structure.DeleteGfmStockCheckOrderParam) (data structure.DeleteGfmStockCheckOrderData, err error) {
	var (
		list mysql.GfmStockCheckOrderList
	)

	list, err = mysql.FindGfmStockCheckOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, v := range list {
		gfmStockCheckOrder := mysql.GfmStockCheckOrder{}
		gfmStockCheckOrder.Id = v.Id
		// 删除
		err = mysql.MustDeleteGfmStockCheckOrder(r.tx, gfmStockCheckOrder)
		if err != nil {
			return
		}
		if err != nil {
			return
		}
		data.Id = append(data.Id, gfmStockCheckOrder.Id)
	}
	return
}

func (r *GfmStockCheckOrderRepo) Get(ctx context.Context, req *structure.GetGfmStockCheckOrderQuery) (data structure.GetGfmStockCheckOrderData, err error) {
	var (
		gfmStockCheckOrder   mysql.GfmStockCheckOrder
		itemDatas            mysql.GfmStockCheckOrderItemList
		fineCodeList         mysql.GfmStockCheckOrderItemFineCodeList
		bizService           = bizPB.NewClientBizUnitService()
		saleSvc              = saleSys.NewSaleSystemClient()
		baseInfoLevelService = baseInfoPB.NewInfoBaseGreyFabricLevelClient()
		baseInfoColorService = baseInfoPB.NewInfoProductGrayFabricColorClient()

		bizIds          = set.NewUint64Set()
		dicIds          = set.NewUint64Set()
		levelIds        = set.NewUint64Set()
		colorIds        = set.NewUint64Set()
		greyFabricIds   = set.NewUint64Set()
		storeKeeperIds  = set.NewUint64Set()
		warehouseSumIds = set.NewUint64Set()
	)
	gfmStockCheckOrder, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}
	bizIds.Add(gfmStockCheckOrder.CheckUnitId)
	storeKeeperIds.Add(gfmStockCheckOrder.StoreKeeperId)

	o := structure.GetGfmStockCheckOrderData{}

	itemDatas, err = mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	for _, item := range itemDatas {
		bizIds.Add(item.CustomerId)
		bizIds.Add(item.SupplierId)
		levelIds.Add(item.GreyFabricLevelId)
		colorIds.Add(item.GrayFabricColorId)
		greyFabricIds.Add(item.GreyFabricId)
		warehouseSumIds.Add(item.WarehouseSumId)
	}
	for _, fc := range fineCodeList {
		dicIds.Add(fc.WarehouseBinId)
	}

	saleSystemMap, err := saleSvc.GetSaleSystemById(ctx, gfmStockCheckOrder.SaleSystemId)
	if err != nil {
		fmt.Println(err)
	}
	bizMap, _ := bizService.GetBizUnitNameByIds(ctx, bizIds.List())
	levelMap, _ := baseInfoLevelService.GetInfoBaseGreyFabricLevelNameByIds(ctx, levelIds.List())
	colorMap, _ := baseInfoColorService.GetInfoProductGrayFabricColorNameByIds(ctx, colorIds.List())
	greyFabricMap, _ := grey_fabric_pb.NewGreyFabricInfoClient().GetGreyFabricInfoByIds(ctx, greyFabricIds.List())
	dicNameMap, _ := dic_pb.NewDictionaryClient().GetDictionaryNameByIds(ctx, dicIds.List())
	empName, _ := empl_pb.NewClientEmployeeService().GetEmployeeNameByIds(ctx, storeKeeperIds.List())
	detailList, _ := mysql.FindGfmStockBySumStockIds(r.tx, warehouseSumIds.List())
	SumDetailMap := make(map[uint64]mysql.GfmWarehouse)
	sumDicUnitMap := make(map[uint64][2]string)
	for _, detail := range detailList {
		SumDetailMap[detail.WarehouseSumId] = detail
		sumDicUnitMap[detail.WarehouseSumId] = [2]string{dicNameMap[detail.GreyFabricWidthUnitId][1], dicNameMap[detail.GreyFabricGramWeightUnitId][1]}
	}
	r.swapListModel2Data(gfmStockCheckOrder, &o, ctx)

	o.SaleSystemName = saleSystemMap[gfmStockCheckOrder.SaleSystemId]
	o.CheckUnitName = bizMap[gfmStockCheckOrder.CheckUnitId]
	o.StoreKeeperName = empName[gfmStockCheckOrder.StoreKeeperId]

	for _, itemData := range itemDatas {
		itemGetData := structure.GetGfmStockCheckOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		itemGetData.GreyFabricWidth = SumDetailMap[itemData.WarehouseSumId].GreyFabricWidth
		itemGetData.GreyFabricGramWeight = SumDetailMap[itemData.WarehouseSumId].GreyFabricGramWeight
		itemGetData.GreyFabricGramWeightUnitName = sumDicUnitMap[itemData.WarehouseSumId][1]
		itemGetData.GreyFabricWidthUnitName = sumDicUnitMap[itemData.WarehouseSumId][0]
		itemGetData.GreyFabricCode = greyFabricMap[itemData.GreyFabricId].Code
		itemGetData.GreyFabricName = greyFabricMap[itemData.GreyFabricId].Name
		itemGetData.UnitName = greyFabricMap[itemData.GreyFabricId].UnitName
		itemGetData.CustomerName = bizMap[itemData.CustomerId]
		itemGetData.SupplierName = bizMap[itemData.SupplierId]
		itemGetData.GrayFabricColorName = colorMap[itemData.GrayFabricColorId]
		itemGetData.GreyFabricLevelName = levelMap[itemData.GreyFabricLevelId]
		// 添加细码信息
		tmpFineCodeList := fineCodeList.PickList(itemData.Id)

		for _, fineCode := range tmpFineCodeList {
			fineCodeGetData := structure.GetGfmStockCheckOrderItemFineCodeData{}
			r.swapFineCodeModel2Data(fineCode, &fineCodeGetData, ctx)
			fineCodeGetData.Position = dicNameMap[fineCode.WarehouseBinId][1]
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *GfmStockCheckOrderRepo) GetList(ctx context.Context, req *structure.GetGfmStockCheckOrderListQuery) (list structure.GetGfmStockCheckOrderDataList, total int, err error) {
	var (
		gfmStockCheckOrders mysql.GfmStockCheckOrderList
		bizService          = bizPB.NewClientBizUnitService()
		saleSvc             = saleSys.NewSaleSystemClient()
		saleSystemIds       = set.NewUint64Set()
		bizIds              = set.NewUint64Set()
		storeKeeperIds      = set.NewUint64Set()
	)
	gfmStockCheckOrders, total, err = mysql.SearchGfmStockCheckOrder(r.tx, req)
	if err != nil {
		return
	}

	for _, order := range gfmStockCheckOrders {
		saleSystemIds.Add(order.SaleSystemId)
		bizIds.Add(order.CheckUnitId)
		storeKeeperIds.Add(order.StoreKeeperId)
	}

	bizMap, err := bizService.GetBizUnitNameByIds(ctx, bizIds.List())
	if err != nil {
		return
	}
	saleSystemMap, err := saleSvc.GetSaleSystemByIds(ctx, saleSystemIds.List())
	if err != nil {
		return
	}
	empName, _ := empl_pb.NewClientEmployeeService().GetEmployeeNameByIds(ctx, storeKeeperIds.List())

	for _, gfmStockCheckOrder := range gfmStockCheckOrders.List() {
		o := structure.GetGfmStockCheckOrderData{}
		r.swapListModel2Data(gfmStockCheckOrder, &o, ctx)
		o.SaleSystemName = saleSystemMap[gfmStockCheckOrder.SaleSystemId]
		o.CheckUnitName = bizMap[gfmStockCheckOrder.CheckUnitId]
		o.StoreKeeperName = empName[gfmStockCheckOrder.StoreKeeperId]
		list = append(list, o)
	}
	return
}

func (r *GfmStockCheckOrderRepo) swapListModel2Data(source mysql.GfmStockCheckOrder, dst *structure.GetGfmStockCheckOrderData, ctx context.Context) {
	dst.Id = source.Id
	dst.CreateTime = tools.MyTime(source.CreateTime)
	dst.UpdateTime = tools.MyTime(source.UpdateTime)
	dst.CreatorId = source.CreatorId
	dst.CreatorName = source.CreatorName
	dst.UpdaterId = source.UpdaterId
	dst.UpdateUserName = source.UpdaterName
	dst.BusinessClose = source.BusinessClose
	dst.BusinessCloseUserId = source.BusinessCloseUserId
	dst.BusinessCloseUserName = source.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(source.BusinessCloseTime)
	dst.DepartmentId = source.DepartmentId
	dst.StoreKeeperId = source.StoreKeeperId
	dst.SaleSystemId = source.SaleSystemId
	dst.CheckUnitId = source.CheckUnitId
	dst.CheckTime = tools.MyTime(source.CheckTime)
	dst.OrderNo = source.OrderNo
	dst.Number = source.Number
	dst.AuditStatus = source.AuditStatus
	dst.AuditerId = source.AuditorId
	dst.AuditerName = source.AuditorName
	dst.AuditTime = tools.MyTime(source.AuditDate)
	dst.Remark = source.Remark
	dst.TotalBeforeRoll = tools.Hundred(source.TotalBeforeRoll)
	dst.TotalBeforeWeight = tools.Milligram(source.TotalBeforeWeight)
	dst.TotalActuallyRoll = tools.Hundred(source.TotalActuallyRoll)
	dst.TotalActuallyWeight = tools.Milligram(source.TotalActuallyWeight)
	dst.TotalResultRoll = tools.Hundred(source.TotalResultRoll)
	dst.TotalResultWeight = tools.Milligram(source.TotalResultWeight)
	dst.DyeUnitUseOrderNo = source.DyeUnitUseOrderNo
	dst.AuditStatusName = dst.AuditStatus.String()
	dst.BusinessCloseName = dst.BusinessClose.String()
	dst.OrderType = source.OrderType
	dst.OrderTypeName = source.OrderType.String()
}

func (r *GfmStockCheckOrderRepo) swapItemModel2Data(source mysql.GfmStockCheckOrderItem, dst *structure.GetGfmStockCheckOrderItemData, ctx context.Context) {

	dst.Id = source.Id
	dst.CreateTime = tools.MyTime(source.CreateTime)
	dst.UpdateTime = tools.MyTime(source.UpdateTime)
	dst.CreatorId = source.CreatorId
	dst.CreatorName = source.CreatorName
	dst.UpdaterId = source.UpdaterId
	dst.UpdateUserName = source.UpdaterName
	dst.OrderNo = source.OrderNo
	dst.GreyFabricStockCheckId = source.GreyFabricStockCheckId
	dst.GreyFabricCode = source.GreyFabricCode
	dst.GreyFabricName = source.GreyFabricName
	dst.CustomerId = source.CustomerId
	dst.SupplierId = source.SupplierId
	dst.GreyFabricWidth = source.GreyFabricWidth
	dst.GreyFabricGramWeight = source.GreyFabricGramWeight
	dst.NeedleSize = source.NeedleSize
	dst.RawMaterialYarnName = source.RawMaterialYarnName
	dst.RawMaterialBatchNum = source.RawMaterialBatchNum
	dst.RawMaterialBatchBrand = source.RawMaterialBatchBrand
	dst.WeavingProcess = source.WeavingProcess
	dst.YarnBatch = source.YarnBatch
	dst.GrayFabricColorId = source.GrayFabricColorId
	dst.GreyFabricLevelId = source.GreyFabricLevelId
	dst.MachineNumber = source.MachineNumber
	dst.BeforeRoll = source.BeforeRoll
	dst.BeforeWeight = source.BeforeWeight
	dst.ActuallyRoll = source.ActuallyRoll
	dst.ActuallyWeight = source.ActuallyWeight
	dst.ResultRoll = source.ResultRoll
	dst.ResultWeight = source.ResultWeight
	dst.GreyFabricRemark = source.GreyFabricRemark
	dst.Remark = source.Remark
	dst.IsStockSource = source.IsStockSource
	dst.GreyFabricId = source.GreyFabricId
	dst.WarehouseSumId = source.WarehouseSumId
	dst.WarehouseCheckSumId = source.WarehouseCheckSumId
}

func (r *GfmStockCheckOrderRepo) swapFineCodeModel2Data(src mysql.GfmStockCheckOrderItemFineCode, dst *structure.GetGfmStockCheckOrderItemFineCodeData, ctx context.Context) {
	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.GfmStockCheckItemId = src.GfmStockCheckItemId
	dst.GreyFabricStockId = src.GreyFabricStockId
	dst.IsStockSource = src.IsStockSource
	dst.VolumeNumber = src.VolumeNumber
	dst.WarehouseBinId = src.WarehouseBinId
	dst.Position = src.Position
	dst.BarCode = src.BarCode
	dst.BeforeRoll = src.BeforeRoll
	dst.BeforeWeight = src.BeforeWeight
	dst.ActuallyRoll = src.ActuallyRoll
	dst.ActuallyWeight = src.ActuallyWeight
	dst.ResultRoll = src.ResultRoll
	dst.ResultWeight = src.ResultWeight
	dst.GreyFabricCheckStockId = src.GreyFabricCheckStockId
	dst.FabricPieceCode = src.FabricPieceCode
}

func (r *GfmStockCheckOrderRepo) judgeAuditPass(id uint64, order mysql.GfmStockCheckOrder) (
	addDetailParamsV2 structure.AddGfmWarehouseParamList, updateDetailParamsV2 structure.UpdateGfmWarehouseParamList,
	getItemAndFcIdsV2 structure.GetItemAndFcIds, addRecordItemListV2 structure.AddGfmStockRecordParamList, err error) {
	var (
		fineCodeList       mysql.GfmStockCheckOrderItemFineCodeList
		itemData           mysql.GfmStockCheckOrderItem
		sumId              uint64
		categoryItem       = make(map[string][]uint64)
		categoryItemUpdate = make(map[uint64][]uint64)
		fcIdsMap           = make(map[string][]uint64)
		bizPBSvc           = bizPB.NewClientBizUnitService()
		addDetailParams    = structure.AddGfmWarehouseParamList{}
		getItemAndFcIds    = structure.GetItemAndFcIds{}
		updateDetailParams = structure.UpdateGfmWarehouseParamList{}
		addRecordItemList  = structure.AddGfmStockRecordParamList{}
		detailStockIds     = set.NewUint64Set()
		fcAndStockIdMap    = make(map[uint64]uint64)
	)
	// 判断坯布数量是否符合
	itemList, _ := mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, id)
	bizTypeMap, _ := bizPBSvc.GetBizUnitTypeById(r.tx.Context, []uint64{order.CheckUnitId})

	fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, itemList.GetIds())
	if err != nil {
		return
	}
	for _, fc := range fineCodeList {
		detailStockIds.Add(fc.GreyFabricStockId)
		fcAndStockIdMap[fc.Id] = fc.GreyFabricStockId
	}

	// 找库存
	warehouseStockList, err := mysql.FindGfmWarehouseByIDs(r.tx, detailStockIds.List())
	if err != nil {
		return
	}

	for _, item := range itemList {
		var TotalRoll int = 0

		tmpFineCodeList := fineCodeList.PickList(item.Id)
		if len(tmpFineCodeList) == 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.GreyFabricName))
			return
		}

		for _, fineCode := range tmpFineCodeList {
			TotalRoll = TotalRoll + fineCode.ActuallyRoll
			// 台账信息
			addRecordItem := r.AddRecordParam(order, item, fineCode)
			detailStock := warehouseStockList.Pick(fineCode.GreyFabricStockId)
			addRecordItem.OutOrderTime = tools.QueryTime(detailStock.SourceTime.Format("2006-01-02"))
			addRecordItem.OutOrderNo = detailStock.SourceCode
			addRecordItem.OutOrderId = detailStock.SourceId
			addRecordItem.WarehouseBinId = detailStock.WarehouseBinId
			addRecordItem.YarnBatch = detailStock.YarnBatch
			// addRecordItem.RawMaterialYarnName = detailStock.RawMaterialYarnName
			// addRecordItem.RawMaterialBatchNum = detailStock.RawMaterialBatchNum
			addRecordItem.GreyFabricColorId = detailStock.GrayFabricColorId
			addRecordItem.GreyFabricLevelId = detailStock.GreyFabricLevelId
			// addRecordItem.MachineNumber = detailStock.MachineNumber
			// addRecordItem.GreyFabricRemark = detailStock.SourceRemark
			addRecordItem.DyeFactoryRemark = order.Remark
			addRecordItem.WarehouseSumId = item.WarehouseSumId
			addRecordItem.DyeUnitUseOrderNo = detailStock.DyeUnitUseOrderNo
			addRecordItemList = append(addRecordItemList, addRecordItem)
		}
		if len(fineCodeList) != 0 && TotalRoll != item.ActuallyRoll {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.GreyFabricName))
			return
		}

		// 判断库存中数据是否足够退货
		// 判断分类是否为同一汇总信息（遍历分录行）
		sumId = item.WarehouseSumId
		if sumId == 0 {
			judgeEqual := item.SumQurey2StrForJedgeEqual(order)
			categoryItem[judgeEqual] = append(categoryItem[judgeEqual], item.Id)
		} else {
			categoryItemUpdate[sumId] = append(categoryItemUpdate[sumId], item.Id)
		}
	}

	// 1. 如果收货单位是染厂，则需要进行汇总细码（当成一条细码来存）
	getItemAndFcIds.ItemIdsMap = categoryItem

	// 如果收货单位是染厂，则需要进行汇总细码（当成一条细码来存）手填的分录行
	// if tools.ExistInList(bizTypeMap[order.CheckUnitId], uint64(common_system.BizUnitTypeNotOur)) {
	if tools.ExistInList(bizTypeMap[order.CheckUnitId], 666) {
		for k, v := range categoryItem {
			itemData, err = mysql.MustFirstGfmStockCheckOrderItemByID(r.tx, v[0])
			if err != nil {
				return
			}
			param := structure.AddGfmWarehouseParam{}
			paramFc := structure.AddWarehouseFcParam{}
			// 因为是汇总的，只去一个id赋值就够了
			param = itemData.ToAddStockGreyFabricParam(order, r.tx)
			fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, v)
			if err != nil {
				return
			}
			fcIds := fineCodeList.GetIds()
			fcIdsMap[k] = fcIds
			for _, fc := range fineCodeList {
				if fc.GreyFabricStockId > 0 {
					paramFc.Id = fc.GreyFabricStockId
				}
				acRoll := fc.ActuallyRoll - fc.BeforeRoll
				acWeight := fc.ActuallyWeight - fc.BeforeWeight
				paramFc.WarehouseBinId = fc.WarehouseBinId
				paramFc.Num += acRoll
				paramFc.Weight += acWeight
				paramFc.AddWeightItemFcId = fc.Id
				paramFc.BarCode = fc.BarCode
				paramFc.WarehouseBinName = fc.Position
				paramFc.FabricPieceCode = fc.FabricPieceCode
				paramFc.VolumeNumber = fc.VolumeNumber
				param.StockRoll += acRoll
				param.StockWeight += acWeight
				param.CheckRoll += acRoll
				param.CheckWeight += acWeight
			}
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			param.FcParam = append(param.FcParam, paramFc)
			param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
			addDetailParams = append(addDetailParams, param)
		}
		for k, v := range categoryItemUpdate {
			itemData, err = mysql.MustFirstGfmStockCheckOrderItemByID(r.tx, v[0])
			if err != nil {
				return
			}
			param := structure.AddGfmWarehouseParam{}
			uparam := structure.UpdateGfmWarehouseParam{}
			paramFc := structure.AddWarehouseFcParam{}
			uparamFc := structure.UpdateWarehouseFcParam{}
			// 因为是汇总的，只去一个id赋值就够了
			param = itemData.ToAddStockGreyFabricParam(order, r.tx)
			fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, v)
			if err != nil {
				return
			}
			// 分录行从汇总库存来，但是里面可能全部是手动输入的
			stockId, exitStockId := fineCodeList.JudgeExitStockId()
			uparam.ChangeTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
			for _, fc := range fineCodeList {
				if exitStockId {
					fc.GreyFabricStockId = stockId
					_, err = mysql.MustUpdateGfmStockCheckOrderItemFineCode(r.tx, fc)
					if err != nil {
						return
					}
					acRoll := fc.ActuallyRoll - fc.BeforeRoll
					acWeight := fc.ActuallyWeight - fc.BeforeWeight
					uparamFc.Num += acRoll
					uparamFc.Id = stockId
					uparamFc.Weight += acWeight
					uparamFc.AddWeightItemFcId = fc.Id
					uparamFc.BarCode = fc.BarCode
					uparamFc.WarehouseBinName = fc.Position
					uparamFc.VolumeNumber = fc.VolumeNumber
					uparam.WarehouseSumId = k
					uparam.StockRoll += acRoll
					uparam.StockWeight += acWeight
					uparam.CheckRoll += acRoll
					uparam.CheckWeight += acWeight
				} else {
					if fc.GreyFabricStockId > 0 {
						paramFc.Id = fc.GreyFabricStockId
					}
					acRoll := fc.ActuallyRoll - fc.BeforeRoll
					acWeight := fc.ActuallyWeight - fc.BeforeWeight
					paramFc.Num += acRoll
					paramFc.Weight += acWeight
					paramFc.AddWeightItemFcId = fc.Id
					paramFc.BarCode = fc.BarCode
					paramFc.WarehouseBinName = fc.Position
					paramFc.VolumeNumber = fc.VolumeNumber
					paramFc.FabricPieceCode = fc.FabricPieceCode
					param.StockRoll += acRoll
					param.StockWeight += acWeight
					param.CheckRoll += acRoll
					param.CheckWeight += acWeight
				}
			}
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			uparam.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			if exitStockId {
				uparam.FcParam = append(uparam.FcParam, uparamFc)
				param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
				updateDetailParams = append(updateDetailParams, uparam)
			} else {
				param.FcParam = append(param.FcParam, paramFc)
				param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
				addDetailParams = append(addDetailParams, param)
			}
		}

	} else {
		// 一条细码一条数据
		for k, v := range categoryItem {
			itemData, err = mysql.MustFirstGfmStockCheckOrderItemByID(r.tx, v[0])
			if err != nil {
				return
			}
			param := structure.AddGfmWarehouseParam{}

			param = itemData.ToAddStockGreyFabricParam(order, r.tx)
			fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, v)
			if err != nil {
				return
			}
			fcIds := fineCodeList.GetIds()
			fcIdsMap[k] = fcIds
			for _, fc := range fineCodeList {
				paramFc := structure.AddWarehouseFcParam{}
				if fc.GreyFabricStockId > 0 {
					paramFc.Id = fc.GreyFabricStockId
				}
				acRoll := fc.ActuallyRoll - fc.BeforeRoll
				acWeight := fc.ActuallyWeight - fc.BeforeWeight
				paramFc.Num = acRoll
				paramFc.Weight = acWeight
				paramFc.BarCode = fc.BarCode
				paramFc.WarehouseBinId = fc.WarehouseBinId
				paramFc.WarehouseBinName = fc.Position
				paramFc.VolumeNumber = fc.VolumeNumber
				paramFc.AddWeightItemFcId = fc.Id
				paramFc.FabricPieceCode = fc.FabricPieceCode
				param.StockRoll += acRoll
				param.StockWeight += acWeight
				param.CheckRoll += acRoll
				param.CheckWeight += acWeight
				param.FcParam = append(param.FcParam, paramFc)
			}
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
			addDetailParams = append(addDetailParams, param)
		}
		for k, v := range categoryItemUpdate {
			itemData, err = mysql.MustFirstGfmStockCheckOrderItemByID(r.tx, v[0])
			if err != nil {
				return
			}
			param := structure.AddGfmWarehouseParam{}
			uparam := structure.UpdateGfmWarehouseParam{}
			// 因为是汇总的，只去一个id赋值就够了
			param = itemData.ToAddStockGreyFabricParam(order, r.tx)
			fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, v)
			if err != nil {
				return
			}
			// 每一条进行库存进出操作
			for _, fc := range fineCodeList {
				paramFc := structure.AddWarehouseFcParam{}
				uparamFc := structure.UpdateWarehouseFcParam{}
				if fc.GreyFabricStockId > 0 {
					acRoll := fc.ActuallyRoll - fc.BeforeRoll
					acWeight := fc.ActuallyWeight - fc.BeforeWeight
					uparamFc.Num += acRoll
					uparamFc.Id = fc.GreyFabricStockId
					uparamFc.Weight += acWeight
					uparamFc.AddWeightItemFcId = fc.Id
					uparamFc.BarCode = fc.BarCode
					uparamFc.WarehouseBinName = fc.Position
					uparamFc.VolumeNumber = fc.VolumeNumber
					uparam.WarehouseSumId = k
					uparam.StockRoll += acRoll
					uparam.StockWeight += acWeight
					uparam.CheckRoll += acRoll
					uparam.CheckWeight += acWeight
					uparam.FcParam = append(uparam.FcParam, uparamFc)
				} else {
					// if fc.GreyFabricCheckStockId > 0 {
					//	paramFc.Id = fc.GreyFabricCheckStockId
					// }
					acRoll := fc.ActuallyRoll - fc.BeforeRoll
					acWeight := fc.ActuallyWeight - fc.BeforeWeight
					paramFc.Num += acRoll
					paramFc.Weight += acWeight
					paramFc.AddWeightItemFcId = fc.Id
					paramFc.BarCode = fc.BarCode
					paramFc.WarehouseBinName = fc.Position
					paramFc.VolumeNumber = fc.VolumeNumber
					paramFc.FabricPieceCode = fc.FabricPieceCode
					param.StockRoll += acRoll
					param.StockWeight += acWeight
					param.CheckRoll += acRoll
					param.CheckWeight += acWeight
					param.FcParam = append(param.FcParam, paramFc)

				}
			}
			uparam.ChangeTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
			uparam.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			updateDetailParams = append(updateDetailParams, uparam)
			param.ChangeTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
			addDetailParams = append(addDetailParams, param)
		}
	}

	getItemAndFcIds.FcIdsMap = fcIdsMap
	addDetailParamsV2 = addDetailParams
	updateDetailParamsV2 = updateDetailParams
	getItemAndFcIdsV2 = getItemAndFcIds
	addRecordItemListV2 = addRecordItemList
	return
}

func (r *GfmStockCheckOrderRepo) judgeAuditWait(id uint64, order mysql.GfmStockCheckOrder) (
	updateDetailParamsV2 structure.UpdateGfmWarehouseParamList, err error) {
	var (
		// stockID      uint64
		itemDataList mysql.GfmStockCheckOrderItemList
		fineCodeList mysql.GfmStockCheckOrderItemFineCodeList
		categoryItem = make(map[uint64][]uint64)
		// bizPBSvc     = bizPB.NewClientBizUnitService()
		sumId uint64
	)

	updateDetailParams := structure.UpdateGfmWarehouseParamList{}

	// 判断坯布数量是否符合
	itemList, _ := mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, id)

	for _, item := range itemList {
		// 判断分类是否为同一汇总信息 （遍历分录行）
		sumId = item.WarehouseSumId
		categoryItem[sumId] = append(categoryItem[sumId], item.Id)
	}

	for _, v := range categoryItem {
		itemDataList, err = mysql.FindGfmStockCheckOrderItemByIDs(r.tx, v)
		if err != nil {
			return
		}
		fineCodeList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParentIDs(r.tx, v)
		if err != nil {
			return
		}
		param := structure.UpdateGfmWarehouseParam{}

		// 因为是汇总的，只去一个id赋值就够了
		if len(itemDataList) > 0 {
			param.WarehouseSumId = itemDataList[0].WarehouseSumId
			if param.WarehouseSumId == 0 {
				param.WarehouseSumId = itemDataList[0].WarehouseCheckSumId
			}
			for _, fc := range fineCodeList {
				paramFc := structure.UpdateWarehouseFcParam{}
				acRoll := fc.ActuallyRoll - fc.BeforeRoll
				acWeight := fc.ActuallyWeight - fc.BeforeWeight
				paramFc.Id = fc.GreyFabricStockId
				if paramFc.Id == 0 {
					paramFc.Id = fc.GreyFabricCheckStockId
					paramFc.IsToWait = true
				}
				paramFc.Num -= acRoll
				paramFc.Weight -= acWeight
				paramFc.AddWeightItemFcId = fc.Id
				param.StockRoll -= acRoll
				param.StockWeight -= acWeight
				param.CheckRoll -= acRoll
				param.CheckWeight -= acWeight
				param.FcParam = append(param.FcParam, paramFc)
			}
			param.ChangeTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			updateDetailParams = append(updateDetailParams, param)
		}
	}
	// }
	updateDetailParamsV2 = updateDetailParams

	return
}

func (r *GfmStockCheckOrderRepo) UpdateStockId(
	ctx context.Context, tx *mysql_base.Tx, id uint64, idsMap map[uint64]uint64, sumIdsMap map[uint64]uint64, getItemAndFcIds structure.GetItemAndFcIds) (
	addRecordItemListV2 structure.AddGfmStockRecordParamList, err error) {
	var (
		order    mysql.GfmStockCheckOrder
		itemList mysql.GfmStockCheckOrderItemList
		fcList   mysql.GfmStockCheckOrderItemFineCodeList
		bizPBSvc = bizPB.NewClientBizUnitService()
	)

	addRecordItemList := structure.AddGfmStockRecordParamList{}
	order, err = mysql.MustFirstGfmStockCheckOrderByID(r.tx, id)
	if err != nil {
		return
	}
	bizTypeMap, _ := bizPBSvc.GetBizUnitTypeById(r.tx.Context, []uint64{order.CheckUnitId})

	// 更新分录行的汇总库存id
	for k, v := range sumIdsMap {
		for _, v2 := range getItemAndFcIds.ItemIdsMap {
			exist := tools.UInt64Contains(k, v2...)
			if exist {
				itemList, err = mysql.FindGfmStockCheckOrderItemByIDs(r.tx, v2)
				// needUpdateIds := itemList.GetNeedUpdateSumStockIdItemIds()
				// if len(needUpdateIds) > 0 {
				err = mysql.MustUpdateGfmStockCheckOrderItemCheckStockIDByIDs(r.tx, v, v2)
				if err != nil {
					return
				}
				// }
				break
			}
		}
	}

	// 如果收货单位是染厂，则需要进行汇总细码（当成一条细码来存）
	// if tools.ExistInList(bizTypeMap[order.CheckUnitId], uint64(common_system.BizUnitTypeNotOur)) {
	if tools.ExistInList(bizTypeMap[order.CheckUnitId], 666) {
		// 更新细码里的库存id
		for k, v := range idsMap {
			for _, v2 := range getItemAndFcIds.FcIdsMap {
				exist := tools.UInt64Contains(k, v2...)
				if exist {
					err = mysql.MustUpdateGfmStockCheckOrderItemFcCheckStockIDByIDs(r.tx, v, v2)
					if err != nil {
						return
					}
					break
				}
			}
		}
	} else {
		itemList, err = mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, id)
		if err != nil {
			return
		}
		for _, item := range itemList {
			fcList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParenTID(r.tx, item.Id)
			// 更新细码对应的库存id
			for _, fc := range fcList {
				if val, ok := idsMap[fc.Id]; ok {
					fc.GreyFabricCheckStockId = val
					fc, err = mysql.MustUpdateGfmStockCheckOrderItemFineCode(r.tx, fc)
					if err != nil {
						return
					}
				}
			}
		}
	}

	// 记录台账（需要前面更新完）
	itemList, err = mysql.FindGfmStockCheckOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	for _, item := range itemList {
		fcList, err = mysql.FindGfmStockCheckOrderItemFineCodeByParenTID(r.tx, item.Id)
		if err != nil {
			return
		}
		for _, fineCode := range fcList {
			addRecordItem := structure.AddGfmStockRecordParam{}
			addRecordItem = r.AddAfterRecordParam(order, item, fineCode)
			addRecordItemList = append(addRecordItemList, addRecordItem)
		}
	}
	addRecordItemListV2 = addRecordItemList
	return
}

// 扣除库存的记录
func (r *GfmStockCheckOrderRepo) AddRecordParam(order mysql.GfmStockCheckOrder, item mysql.GfmStockCheckOrderItem, fc mysql.GfmStockCheckOrderItemFineCode) structure.AddGfmStockRecordParam {
	p := structure.AddGfmStockRecordParam{}
	p.OrderTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
	p.OrderType = cus_const.SourceWarehouseTypeCheck
	p.OrderNo = order.OrderNo
	p.OrderId = order.Id
	p.DyeFactoryId = order.CheckUnitId
	p.GreyFabricId = item.GreyFabricId
	// p.SupplierId = order.SupplierId
	p.YarnBatch = item.YarnBatch
	p.RawMaterialYarnName = item.RawMaterialYarnName
	p.RawMaterialBatchNum = item.RawMaterialBatchNum
	p.GreyFabricColorId = item.GrayFabricColorId
	p.GreyFabricLevelId = item.GreyFabricLevelId
	p.MachineNumber = item.MachineNumber
	p.GreyFabricRemark = item.GreyFabricRemark
	p.DyeFactoryRemark = order.Remark
	p.InRoll = -fc.BeforeRoll
	p.InWeight = -fc.BeforeWeight

	p.WarehouseSumId = item.WarehouseSumId
	p.GreyFabricStockId = fc.GreyFabricStockId
	p.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
	return p
}

// 新进的库存记录
func (r *GfmStockCheckOrderRepo) AddAfterRecordParam(order mysql.GfmStockCheckOrder, item mysql.GfmStockCheckOrderItem, fc mysql.GfmStockCheckOrderItemFineCode) structure.AddGfmStockRecordParam {
	p := structure.AddGfmStockRecordParam{}
	p.OrderTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
	p.OrderType = cus_const.SourceWarehouseTypeCheck
	p.OrderNo = order.OrderNo
	p.OrderId = order.Id
	p.OutOrderTime = tools.QueryTime(order.CheckTime.Format("2006-01-02"))
	p.OutOrderNo = order.OrderNo
	p.OutOrderId = order.Id
	p.DyeFactoryId = order.CheckUnitId
	p.GreyFabricId = item.GreyFabricId
	p.SupplierId = item.SupplierId
	p.YarnBatch = item.YarnBatch
	p.RawMaterialYarnName = item.RawMaterialYarnName
	p.RawMaterialBatchNum = item.RawMaterialBatchNum
	p.GreyFabricColorId = item.GrayFabricColorId
	p.GreyFabricLevelId = item.GreyFabricLevelId
	p.MachineNumber = item.MachineNumber
	p.GreyFabricRemark = item.GreyFabricRemark
	p.DyeFactoryRemark = order.Remark
	if fc.ActuallyRoll-fc.BeforeRoll > 0 || fc.ActuallyWeight-fc.BeforeWeight > 0 {
		p.InRoll = fc.ActuallyRoll - fc.BeforeRoll
		p.InWeight = fc.ActuallyWeight - fc.BeforeWeight
	} else if fc.ActuallyRoll-fc.BeforeRoll < 0 || fc.ActuallyWeight-fc.BeforeWeight < 0 {
		p.OutRoll = fc.BeforeRoll - fc.ActuallyRoll
		p.OutWeight = fc.BeforeWeight - fc.ActuallyWeight
	}
	p.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
	p.WarehouseSumId = item.WarehouseSumId
	if p.WarehouseSumId == 0 {
		p.WarehouseSumId = item.WarehouseCheckSumId
	}
	p.GreyFabricStockId = fc.GreyFabricStockId
	if p.GreyFabricStockId == 0 {
		p.GreyFabricStockId = fc.GreyFabricCheckStockId
	}
	p.ReceiveUnitId = order.CheckUnitId
	p.VoucherNumber = fc.VolumeNumber
	p.FabricPieceCode = fc.FabricPieceCode
	return p
}
