CREATE TABLE IF NOT EXISTS `chat_rel` (
	`user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '用户id（关联user.id）',
	`title`  text DEFAULT '' NOT NULL COMMENT '标题（第一个问题）',
	`ChatId`  VARCHAR(255) DEFAULT '' NOT NULL COMMENT '对话id',
	`ai_app_type` int(1) unsigned DEFAULT 0 NOT NULL COMMENT '应用类型',
	`id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
	`create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
	`creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
	`creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
	`update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
	`updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
	`updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
	`delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
	`deleter_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '删除人ID （关联user.id）',
	`deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
	PRIMARY KEY (`id`),
	KEY `chat_id` (`chat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='ai对话关联表';