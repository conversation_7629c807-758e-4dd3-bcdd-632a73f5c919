// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.29.1
// source: api/wx/tenant/tenant.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateTenantRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	TenantManagementId       uint64                 `protobuf:"varint,1,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	TenantManagementName     string                 `protobuf:"bytes,2,opt,name=tenant_management_name,json=tenantManagementName,proto3" json:"tenant_management_name,omitempty"`
	TenantManagementDeadline string                 `protobuf:"bytes,3,opt,name=tenant_management_deadline,json=tenantManagementDeadline,proto3" json:"tenant_management_deadline,omitempty"`
	DatabaseName             string                 `protobuf:"bytes,4,opt,name=database_name,json=databaseName,proto3" json:"database_name,omitempty"`
	TobeDevelopedAppId       uint64                 `protobuf:"varint,5,opt,name=tobe_developed_app_id,json=tobeDevelopedAppId,proto3" json:"tobe_developed_app_id,omitempty"`
	Phone                    string                 `protobuf:"bytes,6,opt,name=phone,proto3" json:"phone,omitempty"`
	Contact                  string                 `protobuf:"bytes,7,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *CreateTenantRequest) Reset() {
	*x = CreateTenantRequest{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTenantRequest) ProtoMessage() {}

func (x *CreateTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTenantRequest.ProtoReflect.Descriptor instead.
func (*CreateTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{0}
}

func (x *CreateTenantRequest) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

func (x *CreateTenantRequest) GetTenantManagementName() string {
	if x != nil {
		return x.TenantManagementName
	}
	return ""
}

func (x *CreateTenantRequest) GetTenantManagementDeadline() string {
	if x != nil {
		return x.TenantManagementDeadline
	}
	return ""
}

func (x *CreateTenantRequest) GetDatabaseName() string {
	if x != nil {
		return x.DatabaseName
	}
	return ""
}

func (x *CreateTenantRequest) GetTobeDevelopedAppId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppId
	}
	return 0
}

func (x *CreateTenantRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *CreateTenantRequest) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

type CreateTenantReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateTenantReply) Reset() {
	*x = CreateTenantReply{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateTenantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTenantReply) ProtoMessage() {}

func (x *CreateTenantReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTenantReply.ProtoReflect.Descriptor instead.
func (*CreateTenantReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{1}
}

func (x *CreateTenantReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateTenantRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	TenantManagementId       uint64                 `protobuf:"varint,1,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	TenantManagementName     string                 `protobuf:"bytes,2,opt,name=tenant_management_name,json=tenantManagementName,proto3" json:"tenant_management_name,omitempty"`
	TenantManagementDeadline string                 `protobuf:"bytes,3,opt,name=tenant_management_deadline,json=tenantManagementDeadline,proto3" json:"tenant_management_deadline,omitempty"`
	DatabaseName             string                 `protobuf:"bytes,4,opt,name=database_name,json=databaseName,proto3" json:"database_name,omitempty"`
	TobeDevelopedAppId       uint64                 `protobuf:"varint,5,opt,name=tobe_developed_app_id,json=tobeDevelopedAppId,proto3" json:"tobe_developed_app_id,omitempty"`
	Id                       uint64                 `protobuf:"varint,6,opt,name=id,proto3" json:"id,omitempty"`
	Phone                    string                 `protobuf:"bytes,7,opt,name=phone,proto3" json:"phone,omitempty"`
	Contact                  string                 `protobuf:"bytes,8,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *UpdateTenantRequest) Reset() {
	*x = UpdateTenantRequest{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTenantRequest) ProtoMessage() {}

func (x *UpdateTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTenantRequest.ProtoReflect.Descriptor instead.
func (*UpdateTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateTenantRequest) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

func (x *UpdateTenantRequest) GetTenantManagementName() string {
	if x != nil {
		return x.TenantManagementName
	}
	return ""
}

func (x *UpdateTenantRequest) GetTenantManagementDeadline() string {
	if x != nil {
		return x.TenantManagementDeadline
	}
	return ""
}

func (x *UpdateTenantRequest) GetDatabaseName() string {
	if x != nil {
		return x.DatabaseName
	}
	return ""
}

func (x *UpdateTenantRequest) GetTobeDevelopedAppId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppId
	}
	return 0
}

func (x *UpdateTenantRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateTenantRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *UpdateTenantRequest) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

type UpdateTenantReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateTenantReply) Reset() {
	*x = UpdateTenantReply{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateTenantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTenantReply) ProtoMessage() {}

func (x *UpdateTenantReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTenantReply.ProtoReflect.Descriptor instead.
func (*UpdateTenantReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateTenantReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteTenantRequest struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	TenantManagementId uint64                 `protobuf:"varint,1,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *DeleteTenantRequest) Reset() {
	*x = DeleteTenantRequest{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTenantRequest) ProtoMessage() {}

func (x *DeleteTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTenantRequest.ProtoReflect.Descriptor instead.
func (*DeleteTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteTenantRequest) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

type DeleteTenantReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteTenantReply) Reset() {
	*x = DeleteTenantReply{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteTenantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTenantReply) ProtoMessage() {}

func (x *DeleteTenantReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTenantReply.ProtoReflect.Descriptor instead.
func (*DeleteTenantReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{5}
}

type GetTenantRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTenantRequest) Reset() {
	*x = GetTenantRequest{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantRequest) ProtoMessage() {}

func (x *GetTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantRequest.ProtoReflect.Descriptor instead.
func (*GetTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{6}
}

func (x *GetTenantRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetTenantReply struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime           string                 `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CreatorId            uint64                 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CreatorName          string                 `protobuf:"bytes,4,opt,name=creator_name,json=creatorName,proto3" json:"creator_name,omitempty"`
	UpdateTime           string                 `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UpdaterId            uint64                 `protobuf:"varint,6,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdaterName          string                 `protobuf:"bytes,7,opt,name=updater_name,json=updaterName,proto3" json:"updater_name,omitempty"`
	TenantManagementId   uint64                 `protobuf:"varint,8,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	TenantManagementName string                 `protobuf:"bytes,9,opt,name=tenant_management_name,json=tenantManagementName,proto3" json:"tenant_management_name,omitempty"`
	TobeDevelopedAppId   uint64                 `protobuf:"varint,10,opt,name=tobe_developed_app_id,json=tobeDevelopedAppId,proto3" json:"tobe_developed_app_id,omitempty"`
	Phone                string                 `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone,omitempty"`
	Contact              string                 `protobuf:"bytes,12,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *GetTenantReply) Reset() {
	*x = GetTenantReply{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTenantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTenantReply) ProtoMessage() {}

func (x *GetTenantReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTenantReply.ProtoReflect.Descriptor instead.
func (*GetTenantReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{7}
}

func (x *GetTenantReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetTenantReply) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *GetTenantReply) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *GetTenantReply) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *GetTenantReply) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *GetTenantReply) GetUpdaterId() uint64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *GetTenantReply) GetUpdaterName() string {
	if x != nil {
		return x.UpdaterName
	}
	return ""
}

func (x *GetTenantReply) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

func (x *GetTenantReply) GetTenantManagementName() string {
	if x != nil {
		return x.TenantManagementName
	}
	return ""
}

func (x *GetTenantReply) GetTobeDevelopedAppId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppId
	}
	return 0
}

func (x *GetTenantReply) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *GetTenantReply) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

type ListTenantRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Page                 uint32                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                 uint32                 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	Ids                  string                 `protobuf:"bytes,3,opt,name=ids,proto3" json:"ids,omitempty"`
	Name                 string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	Phone                string                 `protobuf:"bytes,5,opt,name=phone,proto3" json:"phone,omitempty"`
	Contact              string                 `protobuf:"bytes,6,opt,name=contact,proto3" json:"contact,omitempty"`
	TobeDevelopedAppName string                 `protobuf:"bytes,7,opt,name=tobe_developed_app_name,json=tobeDevelopedAppName,proto3" json:"tobe_developed_app_name,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ListTenantRequest) Reset() {
	*x = ListTenantRequest{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTenantRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantRequest) ProtoMessage() {}

func (x *ListTenantRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantRequest.ProtoReflect.Descriptor instead.
func (*ListTenantRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{8}
}

func (x *ListTenantRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListTenantRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListTenantRequest) GetIds() string {
	if x != nil {
		return x.Ids
	}
	return ""
}

func (x *ListTenantRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListTenantRequest) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ListTenantRequest) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

func (x *ListTenantRequest) GetTobeDevelopedAppName() string {
	if x != nil {
		return x.TobeDevelopedAppName
	}
	return ""
}

type ListTenantReply struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Total         uint32                    `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*ListTenantReply_Tenant `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListTenantReply) Reset() {
	*x = ListTenantReply{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTenantReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantReply) ProtoMessage() {}

func (x *ListTenantReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantReply.ProtoReflect.Descriptor instead.
func (*ListTenantReply) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{9}
}

func (x *ListTenantReply) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListTenantReply) GetList() []*ListTenantReply_Tenant {
	if x != nil {
		return x.List
	}
	return nil
}

type ListTenantReply_Tenant struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	Id                   uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime           string                 `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CreatorId            uint64                 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CreatorName          string                 `protobuf:"bytes,4,opt,name=creator_name,json=creatorName,proto3" json:"creator_name,omitempty"`
	UpdateTime           string                 `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UpdaterId            uint64                 `protobuf:"varint,6,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdaterName          string                 `protobuf:"bytes,7,opt,name=updater_name,json=updaterName,proto3" json:"updater_name,omitempty"`
	TenantManagementId   uint64                 `protobuf:"varint,8,opt,name=tenant_management_id,json=tenantManagementId,proto3" json:"tenant_management_id,omitempty"`
	TenantManagementName string                 `protobuf:"bytes,9,opt,name=tenant_management_name,json=tenantManagementName,proto3" json:"tenant_management_name,omitempty"`
	TobeDevelopedAppId   uint64                 `protobuf:"varint,10,opt,name=tobe_developed_app_id,json=tobeDevelopedAppId,proto3" json:"tobe_developed_app_id,omitempty"`
	Phone                string                 `protobuf:"bytes,11,opt,name=phone,proto3" json:"phone,omitempty"`
	Contact              string                 `protobuf:"bytes,12,opt,name=contact,proto3" json:"contact,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *ListTenantReply_Tenant) Reset() {
	*x = ListTenantReply_Tenant{}
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListTenantReply_Tenant) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTenantReply_Tenant) ProtoMessage() {}

func (x *ListTenantReply_Tenant) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_tenant_tenant_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTenantReply_Tenant.ProtoReflect.Descriptor instead.
func (*ListTenantReply_Tenant) Descriptor() ([]byte, []int) {
	return file_api_wx_tenant_tenant_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListTenantReply_Tenant) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListTenantReply_Tenant) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ListTenantReply_Tenant) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *ListTenantReply_Tenant) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *ListTenantReply_Tenant) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ListTenantReply_Tenant) GetUpdaterId() uint64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *ListTenantReply_Tenant) GetUpdaterName() string {
	if x != nil {
		return x.UpdaterName
	}
	return ""
}

func (x *ListTenantReply_Tenant) GetTenantManagementId() uint64 {
	if x != nil {
		return x.TenantManagementId
	}
	return 0
}

func (x *ListTenantReply_Tenant) GetTenantManagementName() string {
	if x != nil {
		return x.TenantManagementName
	}
	return ""
}

func (x *ListTenantReply_Tenant) GetTobeDevelopedAppId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppId
	}
	return 0
}

func (x *ListTenantReply_Tenant) GetPhone() string {
	if x != nil {
		return x.Phone
	}
	return ""
}

func (x *ListTenantReply_Tenant) GetContact() string {
	if x != nil {
		return x.Contact
	}
	return ""
}

var File_api_wx_tenant_tenant_proto protoreflect.FileDescriptor

var file_api_wx_tenant_tenant_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2f,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x13, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xc3, 0x02, 0x0a, 0x13, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04,
	0x52, 0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d,
	0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f,
	0x64, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x44, 0x65, 0x61, 0x64, 0x6c, 0x69, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a,
	0x15, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f,
	0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x6f,
	0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64,
	0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63,
	0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74,
	0x22, 0x23, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0xd3, 0x02, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x34, 0x0a, 0x16, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f,
	0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x64, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x44, 0x65, 0x61, 0x64, 0x6c,
	0x69, 0x6e, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x74, 0x6f, 0x62, 0x65,
	0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e,
	0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0x23, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x22, 0x47, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e,
	0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x13, 0x0a, 0x11, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x22,
	0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x22, 0xb1, 0x03, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x30, 0x0a, 0x14, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x34, 0x0a,
	0x16, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x12, 0x74, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x63,
	0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x22, 0xda, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04,
	0x70, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a,
	0x02, 0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x69, 0x64, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x70, 0x68, 0x6f, 0x6e, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f,
	0x6e, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x12, 0x35, 0x0a, 0x17,
	0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61,
	0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x74,
	0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x94, 0x04, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x12, 0x3f, 0x0a,
	0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c,
	0x79, 0x2e, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x1a, 0xa9,
	0x03, 0x0a, 0x06, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x30, 0x0a, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x34, 0x0a, 0x16, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x6d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x31, 0x0a, 0x15, 0x74, 0x6f, 0x62, 0x65, 0x5f,
	0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x04, 0x52, 0x12, 0x74, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65,
	0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68,
	0x6f, 0x6e, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x6f, 0x6e, 0x65,
	0x12, 0x18, 0x0a, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x63, 0x74, 0x42, 0x2a, 0x0a, 0x13, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x2e, 0x76,
	0x31, 0x42, 0x08, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e,
	0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wx_tenant_tenant_proto_rawDescOnce sync.Once
	file_api_wx_tenant_tenant_proto_rawDescData = file_api_wx_tenant_tenant_proto_rawDesc
)

func file_api_wx_tenant_tenant_proto_rawDescGZIP() []byte {
	file_api_wx_tenant_tenant_proto_rawDescOnce.Do(func() {
		file_api_wx_tenant_tenant_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wx_tenant_tenant_proto_rawDescData)
	})
	return file_api_wx_tenant_tenant_proto_rawDescData
}

var file_api_wx_tenant_tenant_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_wx_tenant_tenant_proto_goTypes = []any{
	(*CreateTenantRequest)(nil),    // 0: wx.api.wx.tenant.v1.CreateTenantRequest
	(*CreateTenantReply)(nil),      // 1: wx.api.wx.tenant.v1.CreateTenantReply
	(*UpdateTenantRequest)(nil),    // 2: wx.api.wx.tenant.v1.UpdateTenantRequest
	(*UpdateTenantReply)(nil),      // 3: wx.api.wx.tenant.v1.UpdateTenantReply
	(*DeleteTenantRequest)(nil),    // 4: wx.api.wx.tenant.v1.DeleteTenantRequest
	(*DeleteTenantReply)(nil),      // 5: wx.api.wx.tenant.v1.DeleteTenantReply
	(*GetTenantRequest)(nil),       // 6: wx.api.wx.tenant.v1.GetTenantRequest
	(*GetTenantReply)(nil),         // 7: wx.api.wx.tenant.v1.GetTenantReply
	(*ListTenantRequest)(nil),      // 8: wx.api.wx.tenant.v1.ListTenantRequest
	(*ListTenantReply)(nil),        // 9: wx.api.wx.tenant.v1.ListTenantReply
	(*ListTenantReply_Tenant)(nil), // 10: wx.api.wx.tenant.v1.ListTenantReply.Tenant
}
var file_api_wx_tenant_tenant_proto_depIdxs = []int32{
	10, // 0: wx.api.wx.tenant.v1.ListTenantReply.list:type_name -> wx.api.wx.tenant.v1.ListTenantReply.Tenant
	1,  // [1:1] is the sub-list for method output_type
	1,  // [1:1] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_api_wx_tenant_tenant_proto_init() }
func file_api_wx_tenant_tenant_proto_init() {
	if File_api_wx_tenant_tenant_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_tenant_tenant_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_wx_tenant_tenant_proto_goTypes,
		DependencyIndexes: file_api_wx_tenant_tenant_proto_depIdxs,
		MessageInfos:      file_api_wx_tenant_tenant_proto_msgTypes,
	}.Build()
	File_api_wx_tenant_tenant_proto = out.File
	file_api_wx_tenant_tenant_proto_rawDesc = nil
	file_api_wx_tenant_tenant_proto_goTypes = nil
	file_api_wx_tenant_tenant_proto_depIdxs = nil
}
