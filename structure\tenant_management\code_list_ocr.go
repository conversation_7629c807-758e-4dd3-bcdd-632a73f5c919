package tenant_management

import (
	common "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddCodeListOrcManagementParam struct {
	structure_base.Param
	CodeListName    string        `json:"company_name"`      // 账套名称
	Phone           string        `json:"phone"`             // 手机号
	TenantPackageID uint64        `json:"tenant_package_id"` // 套餐ID
	PayWay          common.PayWay `json:"pay_way"`           // 支付方式
	TenantContacts  string        `json:"tenant_contacts"`   // 租户联系人
	RemainderNumber int           `json:"remainder_number"`  // 剩余次数

}

type ResCodeListOrcManagementIDData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type ResCoderListOrcPayUrlData struct {
	structure_base.ResponseData
	PayUrl string `json:"pay_url"` // 支付url
	Price  int    `json:"price"`   // 支付金额
}

type GetCodeListOrcManagementListQuery struct {
	structure_base.ListQuery
	CodeListName           string                        `form:"code_list_name"`           // 账套名称
	ContactName            string                        `form:"contact_name"`             // 联系人名称
	Phone                  string                        `form:"phone"`                    // 联系电话
	TenantManagementStatus common.TenantManagementStatus `form:"tenant_management_status"` // 状态
}

type GetCodeListOrcManagementListData struct {
	structure_base.ResponseData
	Id                         uint64                `json:"id"`                            // Id
	CodeListName               string                `json:"code_list_name"`                // 账套名称
	ContactName                string                `json:"contact_name"`                  // 联系人名称
	Phone                      string                `json:"phone"`                         // 联系电话
	RemainderNumber            int                   `json:"remainder_number"`              // 剩余次数
	ActivationTime             tools.MyTime          `json:"activation_time"`               // 激活时间
	Deadline                   tools.MyTime          `json:"deadline"`                      // 截止日期
	CreateTime                 tools.MyTime          `json:"create_time"`                   // 创建时间
	CreatorName                string                `json:"creator_name"`                  // 创建人
	UpdateUserName             string                `json:"update_user_name"`              // 更新人
	UpdateTime                 tools.MyTime          `json:"update_time"`                   // 更新时间
	TenantManagementStatus     common.CodeListStatus `json:"tenant_management_status"`      // 状态
	TenantManagementStatusName string                `json:"tenant_management_status_name"` // 状态名称
}

type GetCodeListOrcManagementListDataList []GetCodeListOrcManagementListData

func (l GetCodeListOrcManagementListDataList) Adjust() {}

type GetCodeListOrcManagementQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // Id
}

type UpdateCodeListOrcManagementNameParam struct {
	structure_base.Param
	Id           uint64 `json:"id"`             // Id
	UserID       uint64 `json:"user_id"`        // 用户id
	CodeListName string `json:"code_list_name"` // 账套名称
}

type DisableCodeListOrcManagementParam struct {
	structure_base.Param
	// IDs []uint64 `json:"ids" relate:"code_list_orc_id"` // IDs
	Id uint64 `json:"id"` // IDs
}

type EnableCodeListOrcManagementParam struct {
	structure_base.Param
	Id                        uint64                           `json:"id" relate:"code_list_orc_id"` // Id
	ElectronicColorCardStatus common.ElectronicColorCardStatus `json:"electronic_color_card_status"` // 状态
}

type GetCodeListOrcManagementData struct {
	structure_base.ResponseData
	Id                         uint64                        `json:"id"`                            // Id
	CodeListName               string                        `json:"code_list_name"`                // 账套名称
	Phone                      string                        `json:"phone"`                         // 手机号
	Contacts                   string                        `json:"contacts"`                      // 联系人
	CreateTime                 tools.MyTime                  `json:"create_time"`                   // 创建时间
	CreatorName                string                        `json:"creator_name"`                  // 创建人
	UpdateTime                 tools.MyTime                  `json:"update_time"`                   // 更新时间
	UpdateUserName             string                        `json:"update_user_name"`              // 更新人
	ActivationTime             tools.MyTime                  `json:"activation_time"`               // 激活时间
	Deadline                   tools.MyTime                  `json:"deadline"`                      // 截止日期
	TenantManagementStatus     common.TenantManagementStatus `json:"tenant_management_status"`      // 状态
	TenantManagementStatusName string                        `json:"tenant_management_status_name"` // 状态名称
	RemainderNumber            int                           `json:"remainder_number"`              // 剩余次数

	CodeListOrcDeadLine tools.MyTime          `json:"code_list_orc_dead_line"` // 码单有效日期
	CodeListOrcStatus   common.CodeListStatus `json:"code_list_orc_status"`    // 码单状态
	// PayRecordDataList          []PayRecordData               `json:"pay_record_data_list"`          // 支付记录
}

type GetMPCodeListOrcManagementData struct {
	structure_base.ResponseData
	Id                          uint64                        `json:"id"`                              // Id
	CodeListName                string                        `json:"code_list_name"`                  // 账套名称
	Phone                       string                        `json:"phone"`                           // 手机号
	Contacts                    string                        `json:"contacts"`                        // 联系人
	CreateTime                  tools.MyTime                  `json:"create_time"`                     // 创建时间
	CreatorName                 string                        `json:"creator_name"`                    // 创建人
	UpdateTime                  tools.MyTime                  `json:"update_time"`                     // 更新时间
	UpdateUserName              string                        `json:"update_user_name"`                // 更新人
	Deadline                    tools.MyTime                  `json:"deadline"`                        // 截止日期
	TenantManagementStatus      common.TenantManagementStatus `json:"tenant_management_status"`        // 状态
	TenantManagementStatusName  string                        `json:"tenant_management_status_name"`   // 状态名称
	AdminName                   string                        `json:"admin_name"`                      // 管理员
	SubTenantManagementDataList []SubTenantManagementData     `json:"sub_tenant_management_data_list"` // 用户账号列表

	CodeListOrcDeadLine tools.MyTime          `json:"code_list_orc_dead_line"` // 码单到期时间
	CodeListOrcStatus   common.CodeListStatus `json:"code_list_orc_status"`    // 码单状态
}

// 充值
type RechargeParam struct {
	structure_base.Param
	Id           uint64              `json:"id"`            // Id
	Deadline     tools.MyTime        `json:"deadline"`      // 截止有效期
	Remark       string              `json:"remark"`        // 备注
	Voucher      string              `json:"voucher"`       // 凭证
	RechargeType common.RechargeType `json:"recharge_type"` // 充值类型
}

type RechargeData struct {
	structure_base.ResponseData
	Id                          uint64       `json:"id"`                              // 充值ID
	Status                      int          `json:"status"`                          // 充值状态：1-成功，0-失败
	Message                     string       `json:"message"`                         // 充值消息：成功/失败的原因
	Deadline                    tools.MyTime `json:"deadline"`                        // 截止有效期
	ElectronicColorCardDeadLine tools.MyTime `json:"electronic_color_card_dead_line"` // 电子色卡截止有效期
	SearchImageDeadLine         tools.MyTime `json:"search_image_dead_line"`          // 搜索图片截止有效期
	Remark                      string       `json:"remark"`                          // 备注
	Voucher                     string       `json:"voucher"`                         // 凭证
}

type RechargeDataList []RechargeData

func (l RechargeDataList) Adjust() {}

// 充值记录
type RechargeHistoryListQuery struct {
	structure_base.ListQuery
	Id   uint64              `form:"id"`   // Id
	Type common.RechargeType `form:"type"` // Type
}

type RechargeHistoryListData struct {
	structure_base.RecordData
	// SerialNumber   int          `json:"serial_number" gorm:"column:serial_number"`       // 序号
	// RechargeNumber int `json:"recharge_number"gorm:"column:recharge_number"` // 充值次数
	Deadline        tools.MyTime        `json:"deadline" gorm:"column:deadline"`                   // 截止有效期
	EleCardDeadline tools.MyTime        `json:"ele_card_deadline" gorm:"column:ele_card_deadline"` // 电子色卡截止有效期（冗余字段，使用Deadline）
	Remark          string              `json:"remark" gorm:"column:remark"`                       // 备注
	Voucher         string              `json:"voucher" gorm:"column:voucher"`                     // 凭证
	UpdateUserName  string              `json:"update_user_name" gorm:"column:update_user_name"`   // 操作人
	UpdateTime      tools.MyTime        `json:"update_time" gorm:"column:update_time"`             // 操作时间
	Type            common.RechargeType `json:"type"`                                              // 充值类型
	TypeName        string              `json:"type_name"`                                         // 充值类型名称
}

type GetRechargeHistoryListDataList []RechargeHistoryListData

func (l GetRechargeHistoryListDataList) Adjust() {}

type EnableSearchImageParam struct {
	structure_base.Param
	Id                uint64                           `json:"id" relate:"code_list_orc_id"` // Id
	SearchImageStatus common.ElectronicColorCardStatus `json:"search_image_status"`          // 状态
}
