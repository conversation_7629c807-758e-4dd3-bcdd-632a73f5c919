package dyeing_and_finishing

import (
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	dnf_repo "hcscm/aggs/dyeing_and_finishing"
	common "hcscm/common/dnf"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/domain/dyeing_and_finishing/dnf_entity"
	info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/biz_unit"
	pb_dnf "hcscm/extern/pb/dyeing_and_finishing"
	"hcscm/extern/pb/employee"
	salePb "hcscm/extern/pb/sale"
	"hcscm/extern/pb/sale_system"
	mysql "hcscm/model/mysql/dyeing_and_finishing"
	"hcscm/model/mysql/mysql_base"
	"hcscm/server/system"
	"hcscm/service/dyeing_and_finishing"
	structure_dnf "hcscm/structure/dyeing_and_finishing"
	structure "hcscm/structure/system"
	"hcscm/tools"
)

// AddDyeingAndFinishingNoticeOrder 新建染整通知单
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	新建染整通知单
//	@Produce	json
//	@Param		body			body		structure_dnf.AddDyeingAndFinishingNoticeOrderParams{}	true	"新建染整通知单-请求参数"
//	@Param		Platform		header		int														true	"终端ID"
//	@Param		Authorization	header		string													true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order [post]
func AddDyeingAndFinishingNoticeOrder(c *gin.Context) {
	var (
		p    = &structure_dnf.AddDyeingAndFinishingNoticeOrderParams{}
		data = &structure.AddAndUpdateResponse{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	data.Id, err = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderService(ctx).AddOrder(ctx, tx, p)
	if err != nil {
		return
	}

	return
}

// UpdateDyeingAndFinishingNoticeOrder 更新染整通知单
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	更新染整通知单
//	@Produce	json
//	@Param		body			body		structure_dnf.UpdateDyeingAndFinishingNoticeOrderParams{}	true	"更新染整通知单-请求参数"
//	@Param		Platform		header		int															true	"终端ID"
//	@Param		Authorization	header		string														true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order [put]
func UpdateDyeingAndFinishingNoticeOrder(c *gin.Context) {
	var (
		p    = &structure_dnf.UpdateDyeingAndFinishingNoticeOrderParams{}
		data = &structure.AddAndUpdateResponse{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	err = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderService(ctx).UpdateOrder(ctx, p)
	if err != nil {
		return
	}

	data.Id = p.Id
	return
}

// PassDyeingAndFinishingNoticeOrder 审核染整通知单
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	审核染整通知单
//	@Produce	json
//	@Param		body			body		structure_dnf.PassDyeingAndFinishingNoticeOrderParams{}	true	"审核染整通知单-请求参数"
//	@Param		Platform		header		int														true	"终端ID"
//	@Param		Authorization	header		string													true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/pass [put]
func PassDyeingAndFinishingNoticeOrder(c *gin.Context) {
	var (
		p                    = &structure_dnf.PassDyeingAndFinishingNoticeOrderParams{}
		data                 = &structure.ResponseData{}
		err                  error
		order                = &dnf_entity.DyeingAndFinishingNoticeOrder{}
		salePlanOrderItemIds = make([]uint64, 0)
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	order, err = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderService(ctx).PassOrder(ctx, p.Id)
	if err != nil {
		return
	}

	if order != nil {
		// 反写pmc下推单据数据
		pmcPlanSummaryReq := &salePb.AuditUpdatePushed{}
		pmcPlanSummaryReq = ToAuditUpdatePmcPlanOrderSummary(order, true)
		// 反写pmc计划单已下推数据
		if pmcPlanSummaryReq != nil {
			pmcPlanSvc := salePb.NewPmcPlanClient()
			err = pmcPlanSvc.AuditUpdatePushedPb(ctx, pmcPlanSummaryReq)
			if err != nil {
				return
			}
		}
		for _, item := range order.Items {
			if item.SalePlanItemId > 0 {
				salePlanOrderItemIds = append(salePlanOrderItemIds, item.SalePlanItemId)
			}
		}
	}

	// 更新销售计划单的进度状态为染整中
	if len(salePlanOrderItemIds) > 0 {
		err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, common_system.SituStatusDNFOrder, true, order.Id, "染整通知单审核")
		if err != nil {
			return
		}
	}

	return
}

// 审核消审反写
func ToAuditUpdatePmcPlanOrderSummary(p *dnf_entity.DyeingAndFinishingNoticeOrder, isAudit bool) *salePb.AuditUpdatePushed {
	if p.PmcGreyPlanOrderSummaryId == 0 {
		return nil
	}
	var (
		items = make([]salePb.AuditUpdatePushedDetail, 0)
	)
	r := &salePb.AuditUpdatePushed{}
	r.Id = p.PmcGreyPlanOrderSummaryId
	for _, itemData := range p.Items {
		if itemData.PmcGreyPlanOrderSummaryDetailId == 0 {
			continue
		}
		item := salePb.AuditUpdatePushedDetail{}
		item.Id = itemData.PmcGreyPlanOrderSummaryDetailId
		if isAudit {
			item.Roll = itemData.Data.PieceCount
			item.Weight = itemData.Data.Weight
		} else {
			item.Roll = -itemData.Data.PieceCount
			item.Weight = -itemData.Data.Weight
		}
		items = append(items, item)
	}
	r.Items = items
	return r
}

// RejectDyeingAndFinishingNoticeOrder 驳回染整通知单
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	驳回染整通知单
//	@Produce	json
//	@Param		body			body		structure_dnf.RejectDyeingAndFinishingNoticeOrderParams{}	true	"驳回染整通知单-请求参数"
//	@Param		Platform		header		int															true	"终端ID"
//	@Param		Authorization	header		string														true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/reject [put]
func RejectDyeingAndFinishingNoticeOrder(c *gin.Context) {
	var (
		p    = &structure_dnf.RejectDyeingAndFinishingNoticeOrderParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	err = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderService(ctx).RejectOrder(ctx, p.Id)
	if err != nil {
		return
	}

	return
}

// CancelDyeingAndFinishingNoticeOrder 消审染整通知单
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	消审染整通知单
//	@Produce	json
//	@Param		body			body		structure_dnf.CancelDyeingAndFinishingNoticeOrderParams{}	true	"消审染整通知单-请求参数"
//	@Param		Platform		header		int															true	"终端ID"
//	@Param		Authorization	header		string														true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/cancel [put]
func CancelDyeingAndFinishingNoticeOrder(c *gin.Context) {
	var (
		p                    = &structure_dnf.CancelDyeingAndFinishingNoticeOrderParams{}
		data                 = &structure.ResponseData{}
		err                  error
		order                = &dnf_entity.DyeingAndFinishingNoticeOrder{}
		salePlanOrderItemIds = make([]uint64, 0)
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	order, err = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderService(ctx).CancelOrder(ctx, p.Id)
	if err != nil {
		return
	}

	if order != nil {
		// 反写pmc下推单据数据
		pmcPlanSummaryReq := &salePb.AuditUpdatePushed{}
		pmcPlanSummaryReq = ToAuditUpdatePmcPlanOrderSummary(order, false)
		// 反写pmc计划单已下推数据
		if pmcPlanSummaryReq != nil {
			pmcPlanSvc := salePb.NewPmcPlanClient()
			err = pmcPlanSvc.AuditUpdatePushedPb(ctx, pmcPlanSummaryReq)
			if err != nil {
				return
			}
		}

		for _, item := range order.Items {
			if item.SalePlanItemId > 0 {
				salePlanOrderItemIds = append(salePlanOrderItemIds, item.SalePlanItemId)
			}
		}
	}

	// 更新销售计划单的进度状态为分配-计划
	if len(salePlanOrderItemIds) > 0 {
		err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, common_system.SituStatusPmcOrder,
			false, order.Id, "染整通知单消审")
		if err != nil {
			return
		}
	}
	return
}

// VoidDyeingAndFinishingNoticeOrder 作废染整通知单
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	作废染整通知单
//	@Produce	json
//	@Param		body			body		structure_dnf.VoidDyeingAndFinishingNoticeOrderParams{}	true	"作废染整通知单-请求参数"
//	@Param		Platform		header		int														true	"终端ID"
//	@Param		Authorization	header		string													true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/void [put]
func VoidDyeingAndFinishingNoticeOrder(c *gin.Context) {
	var (
		p    = &structure_dnf.VoidDyeingAndFinishingNoticeOrderParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	err = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderService(ctx).VoidOrder(ctx, p.Id)
	if err != nil {
		return
	}

	return
}

// GetDyeingAndFinishingNoticeOrderList 染整通知单列表
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	染整通知单列表
//	@Param		page				query		int		false	"page"
//	@Param		size				query		int		false	"size"
//	@Param		offset				query		int		false	"offset"
//	@Param		limit				query		int		false	"limit"
//	@Param		order_no			query		string	false	"订单号"
//	@Param		dnf_type			query		string	false	"染整类型：1坯布染整 2成品加工 3成品回修"
//	@Param		sale_system_id		query		int		false	"营销体系id"
//	@Param		dye_factory_id		query		int		false	"染厂ID"
//	@Param		dnf_start_date		query		string	false	"染整开始时间"
//	@Param		dnf_end_date		query		string	false	"染整结束时间"
//	@Param		status				query		string	false	"状态"
//	@Param		creator_id			query		int		false	"创建人id"
//	@Param		create_start_time	query		string	false	"创建开始时间"
//	@Param		create_end_time		query		string	false	"创建结束时间"
//	@Param		auditor_id			query		int		false	"审核人ID"
//	@Param		audit_start_time	query		string	false	"审核开始时间"
//	@Param		audit_end_time		query		string	false	"审核结束时间"
//	@Param		download			query		int		false	"是否导出excel"
//	@Param		Platform			header		int		true	"终端ID"
//	@Param		Authorization		header		string	true	"token"
//	@Success	200					{object}	structure_dnf.GetDyeingAndFinishingNoticeOrderListResponse{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/list [get]
func GetDyeingAndFinishingNoticeOrderList(c *gin.Context) {
	var (
		p        = &structure_dnf.GetDyeingAndFinishingNoticeOrderListParams{}
		data     = &structure_dnf.GetDyeingAndFinishingNoticeOrderListResponse{}
		err      error
		total    int
		querySvc = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderQueryService()
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		// system.BuildListResponse(c, err, data, total)
		system.BuildListResponseV2(c, p, "染整通知单", err, *data, nil, total)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	dtoRsp, err := querySvc.QueryDyeingAndFinishingNoticeOrderList(ctx, tx, p)
	if err != nil {
		return
	}
	total = dtoRsp.Total

	data = toGetDyeingAndFinishingNoticeOrderListResponse(dtoRsp)
	return
}

func toGetDyeingAndFinishingNoticeOrderListResponse(dtoRsp *structure_dnf.QueryDyeingAndFinishingNoticeOrderListDTOResponse) *structure_dnf.GetDyeingAndFinishingNoticeOrderListResponse {
	var data structure_dnf.GetDyeingAndFinishingNoticeOrderListResponse = make([]*structure_dnf.GetDyeingAndFinishingNoticeOrderListItem, 0, len(dtoRsp.OrderList))
	for _, item := range dtoRsp.OrderList {
		dataItem := &structure_dnf.GetDyeingAndFinishingNoticeOrderListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			OrderNo:             item.OrderNo,
			DnfType:             item.DnfType,
			DnfTypeName:         item.DnfType.String(),
			SaleSystemId:        item.SaleSystemId,
			SaleSystemName:      dtoRsp.SaleSystemMap[item.SaleSystemId],
			DyeFactoryId:        item.DyeFactoryId,
			DyeFactoryName:      dtoRsp.BizUnitMap[item.DyeFactoryId],
			OrderFollowerId:     item.OrderFollowerId,
			OrderFollowerName:   dtoRsp.EmployeeMap[item.OrderFollowerId],
			OrderFollowerPhone:  item.OrderFollowerPhone,
			DnfDate:             item.DnfDate,
			TenantReceiveAddrID: item.TenantReceiveAddrID,
			ReturnAddress:       item.ReturnAddress,
			Remark:              item.Remark,
			PieceCount:          tools.Hundred(item.PieceCount),
			Weight:              tools.Milligram(item.Weight),
			ChangePieceCount:    tools.Hundred(item.ChangePieceCount),
			ChangeWeight:        tools.Milligram(item.ChangeWeight),
			RtnPieceCount:       tools.Hundred(item.RtnPieceCount),
			RtnWeight:           tools.Milligram(item.RtnWeight),
			Status:              item.AuditStatus,
			StatusName:          item.AuditStatus.String(),
			AuditTime:           tools.MyTime(item.AuditDate),
			AuditorId:           item.AuditorId,
			AuditorName:         item.AuditorName,
		}
		data = append(data, dataItem)
	}
	return &data
}

// GetDyeingAndFinishingNoticeOrderDetail 染整通知单详情
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	染整通知单详情
//	@Param		id				query		int		true	"Id"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure_dnf.GetDyeingAndFinishingNoticeOrderDetailResponse{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/detail [get]
func GetDyeingAndFinishingNoticeOrderDetail(c *gin.Context) {
	var (
		p    = &structure_dnf.GetDyeingAndFinishingNoticeOrderDetailParams{}
		data = &structure_dnf.GetDyeingAndFinishingNoticeOrderDetailResponse{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.Id <= 0 {
		err = errors.NewError(errors.ErrCodeBusinessParameter)
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	data, err = getDyeingAndFinishingNoticeOrderDetail(ctx, tx, p.Id)

	return
}

//	 染整通知单-item用坯信息
//
//		@Tags		染整管理-染整通知单
//		@Security	ApiKeyAuth
//		@Summary	染整通知单-item用坯信息
//		@Param		order_id				query		int		true	"order_id"
//		@Param		item_id				query		int		true	"item_id"
//		@Param		Platform		header		int		true	"终端ID"
//		@Param		Authorization	header		string	true	"token"
//		@Success	200				{object}	structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChildList{}
//		@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/item/use_gf [get]
func GetDNFNoticeOrderChildByItemId(c *gin.Context) {
	var (
		p     = &structure_dnf.GetNoticeOrderItemUseGF{}
		data  = &structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChildList{}
		_data []*structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChild
		err   error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.OrderId <= 0 {
		err = errors.NewCustomError(errors.ErrCodeLackMustInfo, "请传入染整单id")
		return
	}

	if p.ItemId <= 0 {
		err = errors.NewCustomError(errors.ErrCodeLackMustInfo, "请传入item_id")
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	order, err := getDyeingAndFinishingNoticeOrderDetail(ctx, tx, p.OrderId)
	if err != nil {
		return
	}
	for _, item := range order.Items {
		if item.Id == p.ItemId {
			_data = item.UseFabric
			break
		}
	}
	*data = _data
	return
}

func getDyeingAndFinishingNoticeOrderDetail(ctx context.Context, tx *mysql_base.Tx, id uint64) (
	*structure_dnf.GetDyeingAndFinishingNoticeOrderDetailResponse, error) {
	queryService := dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderQueryService()
	dtoRsp, err := queryService.QueryDyeingAndFinishingNoticeOrderDetail(ctx, tx, id)
	if err != nil {
		return nil, err
	}

	orderItems := make([]*structure_dnf.DyeingAndFinishingNoticeOrderDetailItem, 0, len(dtoRsp.Items))
	for _, item := range dtoRsp.Items {
		orderItem := buildDNFOrderItemRsp(ctx, item, dtoRsp)
		orderItems = append(orderItems, orderItem)
	}

	s := dnf_entity.SpecialRequirement{}
	_ = json.Unmarshal([]byte(dtoRsp.Order.SpecialRequirement), &s)
	data := &structure_dnf.GetDyeingAndFinishingNoticeOrderDetailResponse{
		Items:              orderItems,
		OrderNo:            dtoRsp.Order.OrderNo,
		DnfType:            dtoRsp.Order.DnfType,
		DnfTypeName:        dtoRsp.Order.DnfType.String(),
		Status:             dtoRsp.Order.AuditStatus,
		StatusName:         dtoRsp.Order.AuditStatus.String(),
		DNFOrderCommonInfo: buildDNFOrderCommonInfo(ctx, dtoRsp.Order),
	}
	data.CraftRequirement, data.CraftRequirementData, err = getCraftRequirementV2(ctx, data.CraftRequirement, data.Status)
	// if err != nil {
	//	return data, err
	// }
	// fmt.Println(cr)
	// data.CraftRequirement = cr
	return data, nil
}

// 获取上一单染整通知单信息
// @Tags		染整管理-染整通知单
// @Security	ApiKeyAuth
// @Summary	获取上一单染整通知单信息
// @Param		order_id				query		int		true	"order_id"
// @Param		item_id				query		int		true	"item_id"
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization	header		string	true	"token"
// @Success	200				{object}	structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChildList{}
// @Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/last_order [get]
func GetLatDNFNoticeOrder(c *gin.Context) {
	var (
		p        = &structure_dnf.GetDyeingAndFinishingNoticeOrderListParams{}
		data     = &structure_dnf.GetDyeingAndFinishingNoticeOrderDetailResponse{}
		err      error
		querySvc = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderQueryService()
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.DyeFactoryId <= 0 {
		err = errors.NewCustomError(errors.ErrCodeLackMustInfo, "请选择染厂")
		return
	}

	if p.SaleSystemId <= 0 {
		err = errors.NewCustomError(errors.ErrCodeLackMustInfo, "请选择营销体系")
		return
	}
	p.OrderType = common.OrderTypeDNF
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	data, err = querySvc.GetLastDyeingAndFinishingNoticeOrder(ctx, tx, p)
	if err != nil {
		return
	}

	return
}

// UpdateDyeingAndFinishingNoticeOrderBusinessClose 更新BusinessClose字段
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	更新BusinessClose字段
//	@Produce	json
//	@Param		body			body		structure.UpdateBusinessCloseParams{}	true	"更新BusinessClose字段-请求参数"
//	@Param		Platform		header		int										true	"终端ID"
//	@Param		Authorization	header		string									true	"token"
//	@Success	200				{object}	structure.ResponseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/notice_order/business_close [put]
func UpdateDyeingAndFinishingNoticeOrderBusinessClose(c *gin.Context) {
	var (
		p    = &structure.UpdateBusinessCloseParams{}
		data = &structure.ResponseData{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.Id < 0 {
		err = errors.ErrCodeBusinessParameter
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	err = dnf_repo.NewDyeingAndFinishingNoticeOrderRepo(ctx, tx).UpdateBusinessClose(ctx, p.Id, p.BusinessClose)
	return
}

// GetDyeingAndFinishingNoticeOrderItemsDetail 染整单项详情
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	染整单项详情
//	@Param		id				query		int		true	"Id"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure_dnf.GetDyeingAndFinishingNoticeOrderItemsDetailResponse{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/order/items_detail [get]
func GetDyeingAndFinishingNoticeOrderItemsDetail(c *gin.Context) {
	var (
		p            = &structure_dnf.GetDyeingAndFinishingNoticeOrderItemsDetailParams{}
		data         = &structure_dnf.GetDyeingAndFinishingNoticeOrderItemsDetailResponse{}
		err          error
		queryService = dyeing_and_finishing.NewDyeingAndFinishingNoticeOrderQueryService()
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if p.Id == "" {
		err = errors.NewError(errors.ErrCodeBusinessParameter)
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	dtoRsp, err := queryService.QueryDNFNoticeOrderItemWithChild(ctx, tx, 0, p.Id.ToUint64())
	if err != nil {
		return
	}

	orderIds := mysql.GetDyeingAndFinishingNoticeOrderIdsByItems(dtoRsp.Items)
	orderMap, err := queryService.QueryDNFNoticeOrderMapByIds(ctx, tx, orderIds)
	if err != nil {
		return
	}

	orderItems := make([]*structure_dnf.DyeingAndFinishingNoticeOrderDetailItem, 0, len(dtoRsp.Items))
	for _, item := range dtoRsp.Items {
		orderItem := buildDNFOrderItemRsp(ctx, item, dtoRsp)
		// 单据号
		if order := orderMap[item.OrderId]; order != nil {
			orderItem.OrderNo = order.OrderNo
			orderItem.DnfDate = order.DnfDate
		}
		orderItems = append(orderItems, orderItem)
	}

	data = &structure_dnf.GetDyeingAndFinishingNoticeOrderItemsDetailResponse{
		Items: orderItems,
	}
	return
}

func buildDNFOrderItemRsp(ctx context.Context, item *mysql.DyeingAndFinishingNoticeOrderItem, dtoRsp *structure_dnf.QueryDNFNoticeOrderDetailDTOResponse) *structure_dnf.DyeingAndFinishingNoticeOrderDetailItem {

	childList := dtoRsp.ItemChildMap[item.Id]
	useFabric := make([]*structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChild, 0, len(childList))
	for _, child := range childList {
		// 用布信息
		var status common_system.OrderStatus
		if dtoRsp.Order != nil {
			status = dtoRsp.Order.AuditStatus
		}
		tmpChild := buildDyeingAndFinishingNoticeOrderDetailItemChild(child, dtoRsp.GFStockMap, dtoRsp.FOutMap, status)
		useFabric = append(useFabric, tmpChild)
	}

	orderItem := buildDyeingAndFinishingNoticeOrderDetailItem(ctx, item, useFabric, dtoRsp)

	return orderItem
}

func buildDNFOrderCommonInfo(ctx context.Context, order *mysql.DyeingAndFinishingNoticeOrder) structure_dnf.DNFOrderCommonInfo {
	saleSystemData, _ := sale_system.NewSaleSystemClient().GetSaleSystemDetailById(ctx, order.SaleSystemId)
	bizUnit, _ := biz_unit.NewClientBizUnitService().GetBizUnitDetailByID(ctx, biz_unit.Req{Id: order.DyeFactoryId})
	employeeMap, _ := employee.NewClientEmployeeService().GetEmployeeNameByIds(ctx, []uint64{order.OrderFollowerId})
	settleTypeName, _ := info_pb.NewInfoSaleSettlementMethodClient().GetInfoSaleSettlementMethodNameByIds(ctx, []uint64{order.SettleTypeID})

	s := dnf_entity.SpecialRequirement{}
	_ = json.Unmarshal([]byte(order.SpecialRequirement), &s)
	info := structure_dnf.DNFOrderCommonInfo{
		SaleSystemId:        order.SaleSystemId,
		SaleSystemName:      saleSystemData.Name,
		SaleSystemPhone:     saleSystemData.Phone,
		SaleSystemAddr:      saleSystemData.AddressDetail,
		SaleSystemContacts:  saleSystemData.Contacts,
		SaleSystemFaxNumber: saleSystemData.FaxNumber,
		DyeFactoryId:        order.DyeFactoryId,
		DyeFactoryName:      bizUnit.Name,
		DyeFactoryContact:   bizUnit.ContactName,
		DyeFactoryPhone:     bizUnit.Phone,
		DyeFactoryAddr:      bizUnit.Address,
		DyeFactoryFaxNumber: bizUnit.FaxNumber,
		OrderFollowerId:     order.OrderFollowerId,
		OrderFollowerName:   employeeMap[order.OrderFollowerId],
		OrderFollowerPhone:  order.OrderFollowerPhone,
		DnfDate:             order.DnfDate,
		TenantReceiveAddrID: order.TenantReceiveAddrID,
		ReturnAddress:       order.ReturnAddress,
		SettleTypeID:        order.SettleTypeID,
		SettleTypeName:      settleTypeName[order.SettleTypeID],
		Remark:              order.Remark,
		LightRequirement:    order.LightRequirement,
		PackRequirement:     order.PackRequirement,
		DnfRequirement:      order.DnfRequirement,
		CraftRequirement:    order.CraftRequirement,
		IsLengthCut:         s.IsLengthCut,

		SpecialRequirementRemark: order.SpecialRequirementRemark,
	}
	return info
}

func buildDyeingAndFinishingNoticeOrderDetailItem(ctx context.Context, item *mysql.DyeingAndFinishingNoticeOrderItem, useFabric []*structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChild, dtoRsp *structure_dnf.QueryDNFNoticeOrderDetailDTOResponse) *structure_dnf.DyeingAndFinishingNoticeOrderDetailItem {
	quoteService := pb_dnf.NewClientDNFQuoteService()
	// TODO
	dnfCraftName, _ := quoteService.GetFinishingQuoteOrderItemProjectNameString(ctx, tools.FormatStringToIntList(item.DnfCraftIds))
	paperTubeName, _ := quoteService.GetFinishingQuoteOrderItemProjectNameString(ctx, tools.FormatStringToIntList(item.PaperTubeIds))
	plasticsBagName, _ := quoteService.GetFinishingQuoteOrderItemProjectNameString(ctx, tools.FormatStringToIntList(item.PlasticsBagIds))
	orderItem := &structure_dnf.DyeingAndFinishingNoticeOrderDetailItem{
		Id: item.Id,
		DyeingAndFinishingNoticeOrderItemInfo: structure_dnf.DyeingAndFinishingNoticeOrderItemInfo{
			FinishProductId:     item.FinishProductId,
			SalePlanOrderNo:     item.SalePlanOrderNo,
			SalePlanItemId:      item.SalePlanItemId,
			SalePlanOrderItemNo: item.SalePlanOrderItemNo,
			CustomerId:          item.CustomerId,
			ProductColorId:      item.ProductColorId,
			ColorId:             item.ColorId,
			Dyelot:              item.Dyelot,
			LevelId:             item.LevelId,
			Craft:               item.Craft,
			DnfCraft:            dnfCraftName,
			DnfCraftIds:         tools.FormatStringToIntList(item.DnfCraftIds),
			PaperTubeIds:        tools.FormatStringToIntList(item.PaperTubeIds),
			PlasticsBagIds:      tools.FormatStringToIntList(item.PlasticsBagIds),
			HandFeeling:         item.HandFeeling,
			IncreaseWeight:      item.IncreaseWeight,
			StyleNo:             item.StyleNo,
			Width:               item.Width,
			GramWeight:          item.GramWeight,
			DnfLoss:             item.DnfLoss,
			ShrinkageRate:       item.ShrinkageRate,
			PieceCount:          item.PieceCount,
			Weight:              item.Weight,
			DeliveryDate:        item.DeliveryDate,
			Remark:              item.Remark,
			ColorMatchingData:   item.ColorMatchingData,
			GramWeightUnitID:    item.GramWeightUnitID,
			WidthUnitId:         item.WidthUnitId,
			Status:              item.Status,
			StatusChangeUserId:  item.StatusChangeUserId,
			StatusChangeTime:    tools.MyTime(item.StatusChangeTime),
			DyeUnitUseOrderNo:   item.DyeUnitUseOrderNo,
		},
		DyeingAndFinishingNoticeOrderData: structure_dnf.DyeingAndFinishingNoticeOrderData{
			ChangePieceCount:    item.ChangePieceCount,
			ChangeWeight:        item.ChangeWeight,
			RtnPieceCount:       item.RtnPieceCount,
			RtnWeight:           item.RtnWeight,
			RedyePieceCount:     item.RedyePieceCount,
			RedyeWeight:         item.RedyeWeight,
			FinishingPieceCount: item.FinishingPieceCount,
			FinishingWeight:     item.FinishingWeight,
			NotRtnPieceCount:    item.GetNotRtnPieceCount(),
			NotRtnWeight:        item.GetNotRtnWeight(),
		},
		UseFabric:      useFabric,
		FinalDnfType:   item.FinalDnfType,
		PaperTubeSpecs: paperTubeName,
		TapeSpecs:      plasticsBagName,
	}

	if dtoRsp != nil {
		orderItem.CustomerName = dtoRsp.BizUnitMap[item.CustomerId]
		orderItem.Level = dtoRsp.LvMap[item.LevelId][0]
		orderItem.LevelCode = dtoRsp.LvMap[item.LevelId][1]
		// 成品资料信息
		if product := dtoRsp.ProductMap[item.FinishProductId]; product != nil {
			orderItem.Code = product.FinishProductCode
			orderItem.Name = product.FinishProductName
			orderItem.Unit = product.MeasurementUnitName
			orderItem.FinishProductIngredient = product.FinishProductIngredient
			orderItem.DyeingLoss = product.DyeingLoss
			orderItem.Density = product.Density
			orderItem.YarnCount = product.YarnCount
			orderItem.YarnCountAndDensity = product.YarnCount + " " + product.Density
			orderItem.NeedleSize = product.NeedleSize
			orderItem.GreyFabricId = product.GreyFabricId
			orderItem.GreyFabricCode = product.GreyFabricCode
			orderItem.GreyFabricName = product.GreyFabricName
			// orderItem.UnitId = product.MeasurementUnitId
		}
		// 颜色信息
		if colorInfo := dtoRsp.DyeColorMap[item.ColorId]; colorInfo != nil {
			orderItem.ColorNo = colorInfo.ProductColorCode
			orderItem.ColorName = colorInfo.ProductColorName
			orderItem.DfColorNo = colorInfo.DyeFactoryColorCode
			orderItem.DfColorName = colorInfo.DyeFactoryColorName
		} else {
			orderItem.ColorNo = dtoRsp.ProductColorMap[item.ProductColorId][0]
			orderItem.ColorName = dtoRsp.ProductColorMap[item.ProductColorId][1]
		}
		orderItem.WidthUnitName = dtoRsp.DicMap[orderItem.WidthUnitId][1]
		orderItem.GramWeightUnitName = dtoRsp.DicMap[orderItem.GramWeightUnitID][1]
		orderItem.FinishProductWidthAndUnitName = orderItem.Width + " " + orderItem.WidthUnitName
		orderItem.FinishProductGramWeightAndUnitName = orderItem.GramWeight + " " + orderItem.GramWeightUnitName
	}

	// new：可以去掉下面，用上面的FinalDnfType判断
	// 最终来源是根据是否有库存信息或加工出仓单信息判断, 给前端判断用
	// 现在一条成品信息来源，要么都是从坯布库存来，要么都是从加工出仓单来
	// 所以选一个判断就行了，如果不存在默认坯布库存
	orderItem.FinalSrcType = common.SrcTypeGreyFabric
	if len(orderItem.UseFabric) > 0 && orderItem.UseFabric[0].FOutInfo != nil {
		orderItem.FinalSrcType = common.SrcTypeFabricOut
	}
	return orderItem
}

func buildDyeingAndFinishingNoticeOrderDetailItemChild(
	child *mysql.DyeingAndFinishingNoticeOrderItemChild,
	gFStockMap map[uint64]*structure_dnf.GreyFabricStockInfo,
	fOutMap map[uint64]*structure_dnf.FabricOutInfo,
	status common_system.OrderStatus,
) *structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChild {
	tmpChild := &structure_dnf.DyeingAndFinishingNoticeOrderDetailItemChild{
		DyeingAndFinishingNoticeOrderItemChild: structure_dnf.DyeingAndFinishingNoticeOrderItemChild{
			SrcId:      child.SrcId,
			PieceCount: child.PieceCount,
			Weight:     child.Weight,
			Remark:     child.Remark,
		},
		DyeingAndFinishingNoticeOrderData: structure_dnf.DyeingAndFinishingNoticeOrderData{
			ChangePieceCount:    child.ChangePieceCount,
			ChangeWeight:        child.ChangeWeight,
			RtnPieceCount:       child.RtnPieceCount,
			RtnWeight:           child.RtnWeight,
			RedyePieceCount:     child.RedyePieceCount,
			RedyeWeight:         child.RedyeWeight,
			FinishingPieceCount: child.FinishingPieceCount,
			FinishingWeight:     child.FinishingWeight,
			NotRtnPieceCount:    child.GetNotRtnPieceCount(),
			NotRtnWeight:        child.GetNotRtnWeight(),
		},
		Id: child.Id,
	}
	if child.SrcType == common.SrcTypeGreyFabric {
		// 已审核取记录
		if status == common_system.OrderStatusAudited && gFStockMap[child.SrcId] != nil {
			tmpChild.StockRoll = gFStockMap[child.SrcId].Num
			tmpChild.StockWeight = gFStockMap[child.SrcId].Weight
			gFStockMap[child.SrcId].Num = child.BeforePieceCount
			gFStockMap[child.SrcId].Weight = child.BeforeWeight
		}
		// 拆缸需要追踪回原始的src_id
		var srcId uint64
		srcId = child.SrcId
		if child.FinalSrcId != 0 {
			srcId = child.FinalSrcId
		}
		if tmpChild.GFStockInfo = gFStockMap[srcId]; tmpChild.GFStockInfo == nil {
			tmpChild.GFStockInfo = &structure_dnf.GreyFabricStockInfo{}
		}
	} else if child.SrcType == common.SrcTypeFabricOut {
		if tmpChild.FOutInfo = fOutMap[child.SrcId]; tmpChild.FOutInfo == nil {
			tmpChild.FOutInfo = &structure_dnf.FabricOutInfo{}
		}
		if child.FinalSrcId > 0 && fOutMap[child.FinalSrcId] != nil {
			tmpChild.FOutInfo = fOutMap[child.FinalSrcId]
		}
	} else {
		// 如果不是染整单，则取最终来源
		if child.FinalSrcType == common.SrcTypeGreyFabric {
			if status == common_system.OrderStatusAudited && gFStockMap[child.FinalSrcId] != nil {
				gFStockMap[child.FinalSrcId].Num = child.BeforePieceCount
				gFStockMap[child.FinalSrcId].Weight = child.BeforeWeight
			}
			if tmpChild.GFStockInfo = gFStockMap[child.FinalSrcId]; tmpChild.GFStockInfo == nil {
				tmpChild.GFStockInfo = &structure_dnf.GreyFabricStockInfo{}
			}
		} else if child.FinalSrcType == common.SrcTypeFabricOut {
			if tmpChild.FOutInfo = fOutMap[child.FinalSrcId]; tmpChild.FOutInfo == nil {
				tmpChild.FOutInfo = &structure_dnf.FabricOutInfo{}
			}
		}
	}
	return tmpChild
}

// GetDnfTypeEnum 染整类型枚举
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	染整类型枚举
//	@accept		application/json
//	@Produce	application/json
//	@Param		Platform	header		int	true	"终端ID"
//	@Success	200			{object}	system.BaseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/dnf_type_enum [get]
func GetDnfTypeEnum(c *gin.Context) {
	m := common.GetDnfTypeReverseIntMap()
	system.BuildMapResponse(c, m)
}

// GetOrderTypeEnum 单据类型枚举
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	单据类型枚举
//	@accept		application/json
//	@Produce	application/json
//	@Param		Platform	header		int	true	"终端ID"
//	@Success	200			{object}	system.BaseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/order_type_enum [get]
func GetOrderTypeEnum(c *gin.Context) {
	m := common.GetOrderTypeReverseIntMap()
	system.BuildMapResponse(c, m)
}

// GetSrcTypeEnum 来源类型枚举
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	来源类型枚举
//	@accept		application/json
//	@Produce	application/json
//	@Param		Platform	header		int	true	"终端ID"
//	@Success	200			{object}	system.BaseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/src_type_enum [get]
func GetSrcTypeEnum(c *gin.Context) {
	m := common.GetSrcTypeReverseIntMap()
	system.BuildMapResponse(c, m)
}

// 染整进度 状态枚举
//
//	@Tags		染整管理-染整通知单
//	@Security	ApiKeyAuth
//	@Summary	状态枚举
//	@accept		application/json
//	@Produce	application/json
//	@Param		Platform	header		int	true	"终端ID"
//	@Success	200			{object}	system.BaseData{}
//	@Router		/hcscm/admin/v1/dyeing_and_finishing/getSituStatusReverseIntMap [get]
func GetSituStatusReverseIntMap(c *gin.Context) {
	m := common.GetSituStatusReverseIntMap()
	system.BuildMapResponse(c, m)
}
