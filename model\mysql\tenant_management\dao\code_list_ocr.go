package tenant_management

import (
	"context"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/tenant_management"
	structure "hcscm/structure/tenant_management"
	"time"
)

type ICodeListOrcManagementDao interface {
	MustCreate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.CodeListOrcManagement) (_tenantManagement mysql.CodeListOrcManagement, err error)
	MustCreateRechargeHistory(ctx context.Context, tx *mysql_base.Tx, rechargeHistory mysql.RechargeHistory) (_rechargeHistory mysql.RechargeHistory, err error)
	MustUpdate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.TenantManagement) (_tenantManagement mysql.TenantManagement, err error)
	MustFirst(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, err error)
	FirstByID(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, isExist bool, err error)
	FirstByAssignPort(ctx context.Context, tx *mysql_base.Tx, assignPort int) (tenantManagement mysql.CodeListOrcManagement, isExist bool, err error)
	FirstBySecret(ctx context.Context, tx *mysql_base.Tx, secret string) (tenantManagement mysql.CodeListOrcManagement, isExist bool, err error)
	FindByIDs(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (tenantManagements mysql.CodeListOrcManagementList, err error)
	FindByIDsOfReflect(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (tenantManagements mysql.CodeListOrcManagementList, err error)
	SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (tenantManagements mysql.TenantManagementList, total int, err error)
	FindAll(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.CodeListOrcManagementList, err error)
	GetExpiringSoonList(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error)

	GetRechargeHistorys(ctx context.Context, tx *mysql_base.Tx, query structure.RechargeHistoryListQuery) (o mysql.RechargeHistoryList, total int, err error)
}

func NewCodeListOrcManagementDao() ICodeListOrcManagementDao {
	return &codeListOrcManagementDao{}
}

type codeListOrcManagementDao struct{}

func (dao *codeListOrcManagementDao) MustCreate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.CodeListOrcManagement) (_tenantManagement mysql.CodeListOrcManagement, err error) {
	err = mysql_base.MustCreateModel(tx, &tenantManagement)
	if err != nil {
		return
	}
	_tenantManagement = tenantManagement
	return
}

func (dao *codeListOrcManagementDao) MustCreateRechargeHistory(ctx context.Context, tx *mysql_base.Tx, rechargeHistory mysql.RechargeHistory) (_rechargeHistory mysql.RechargeHistory, err error) {
	err = mysql_base.MustCreateModel(tx, &rechargeHistory)
	if err != nil {
		return
	}
	_rechargeHistory = rechargeHistory
	return
}

func (dao *codeListOrcManagementDao) MustUpdate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.TenantManagement) (_tenantManagement mysql.TenantManagement, err error) {
	err = mysql_base.MustUpdateModel(tx, &tenantManagement)
	if err != nil {
		return
	}
	_tenantManagement = tenantManagement
	return
}

func (dao *codeListOrcManagementDao) MustFirst(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, err error) {
	var cond = mysql_base.NewCondition()
	err = mysql_base.MustFirst(tx, &tenantManagement, tenantManagementID, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) FirstByID(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("id", tenantManagementID)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) FirstByAssignPort(ctx context.Context, tx *mysql_base.Tx, assignPort int) (tenantManagement mysql.CodeListOrcManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("assign_port", assignPort)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) FirstBySecret(ctx context.Context, tx *mysql_base.Tx, secret string) (tenantManagement mysql.CodeListOrcManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("secret", secret)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) FindByIDs(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (tenantManagements mysql.CodeListOrcManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.CodeListOrcManagement
	)
	cond.AddContainMatch("id", ids)
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) FindByIDsOfReflect(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (tenantManagements mysql.CodeListOrcManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.CodeListOrcManagement
	)
	cond.AddContainMatch("id", mysql_base.GetUInt64ListV2("tenant_management_id", objects))
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (tenantManagements mysql.TenantManagementList, total int, err error) {
	var (
		cond = mysql_base.NewCondition()
		// tenantManagement mysql.CodeListOrcManagement
		tenantManagement mysql.TenantManagement
	)
	if !query.CreateStartTime.IsYMDZero() && !query.CreateEndTime.IsYMDZero() {
		cond.Between("create_time", query.CreateStartTime.StringYMD(), query.CreateEndTime.StringYMD2DayListTimeYMDHMS())
	}
	// if query.CodeListName != "" {
	// 	cond.AddMultiFieldLikeMatch([]string{"id", "code_list_name"}, query.CodeListName)
	// }
	if query.IDOrName != "" {
		cond.AddMultiFieldLikeMatch([]string{"id", "tenant_company_name"}, query.IDOrName)
	}

	// if query.TenantManagementStatus != 0 {
	// 	cond.AddEqual("tenant_management_status", query.TenantManagementStatus)
	// }
	if query.TenantContacts != "" {
		cond.AddFuzzyMatch("tenant_phone_contacts", query.TenantContacts)
	}

	if query.Phone != "" {
		cond.AddFuzzyMatch("tenant_phone_number", query.Phone)
	}

	total, err = mysql_base.SearchListGroupForPaging(tx, &tenantManagement, query, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) FindAll(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.CodeListOrcManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.CodeListOrcManagement
	)
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *codeListOrcManagementDao) GetExpiringSoonList(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.TenantManagement
		// initTime, _      = time.Parse("2006-01-02 15:04:05", "2023-11-01 00:00:00")
		todayMidnight    = time.Now().Truncate(24 * time.Hour) // 当天午夜
		tomorrowMidnight = todayMidnight.AddDate(0, 0, 1)      // 明天午夜
	)
	cond.AddContainMatch("code_list_orc_status", []common.CodeListStatus{common.CodeListStatusEnable, common.CodeListStatusDisable})
	cond.Between("code_list_orc_dead_line", todayMidnight, tomorrowMidnight)
	// cond.Between("code_list_orc_dead_line", initTime, time.Now().AddDate(0, 0, 1))
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

// 获取状态

// 查询充值记录
func (dao *codeListOrcManagementDao) GetRechargeHistorys(ctx context.Context, tx *mysql_base.Tx, query structure.RechargeHistoryListQuery) (o mysql.RechargeHistoryList, total int, err error) {
	var (
		cond            = mysql_base.NewCondition()
		rechargeHistory mysql.RechargeHistory
		list            mysql.RechargeHistoryList
	)

	if query.Id != 0 {
		cond.AddEqual("code_list_orc_id", query.Id)
	}
	if query.Type == common.RechargeTypeEleColorCard {
		cond.AddContainMatch("type", []common.RechargeType{common.RechargeTypeEleColorCard, common.RechargeTypeSearchImage})
	} else {
		cond.AddEqual("type", query.Type)
	}

	total, err = mysql_base.SearchListGroupForPaging(tx, &rechargeHistory, query, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}
