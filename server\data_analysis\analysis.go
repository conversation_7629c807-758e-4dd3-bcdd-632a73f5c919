package data_analysis

import (
	"hcscm/model/mysql/mysql_base"
	"hcscm/server/system"
	"hcscm/service/data_analysis"
	structure "hcscm/structure/data_analysis"

	"github.com/gin-gonic/gin"
)

// GetWorkbenchData 获取工作台数据
//
//	@Tags		【数据分析】
//	@Summary	获取工作台数据
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		Platform		header		int			true	"终端ID"
//	@Param		Authorization	header		string		true	"token"
//	@Param		start_time		query		string		true	"开始时间"
//	@Param		end_time		query		string		true	"结束时间"
//	@Success	200				{object}	structure.GetWorkbenchDataRes{}
//	@Router		/hcscm/h5/v1/data_analysis/analysis/getWorkbenchData [get]
func GetWorkbenchData(c *gin.Context) {
	var (
		req  = &structure.GetWorkbenchDataReq{}
		data structure.GetWorkbenchDataRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetWorkbenchData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetAnalysisHomeData 获取产品分析首页数据
//
// @Tags         【数据分析】
// @Summary      获取产品分析首页数据
// @Description  获取产品分析首页数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetAnalysisHomeRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/productAnalysis/home [get]
func GetAnalysisHomeData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetAnalysisHomeRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetAnalysisHomeData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetProAnalysisTrendData 获取产品分析趋势数据
//
// @Tags         【数据分析】
// @Summary      获取产品分析趋势数据
// @Description  获取产品分析趋势数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetProAnalysisTrendRes{}
// @Router       /hcscm/h5/v1/analysis/productAnalysis/trend [get]
func GetProAnalysisTrendData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetProAnalysisTrendRes
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, data)
	}()
	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetProAnalysisProductData 获取产品维度分析数据
//
// @Tags         【数据分析】
// @Summary      获取产品维度分析数据
// @Description  获取产品维度分析数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetProAnalysisProductRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/productAnalysis/product [get]
func GetProAnalysisProductData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetProAnalysisProductRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetProAnalysisProductData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetProAnalysisCustomerData 获取产品客户维度分析数据
//
// @Tags         【数据分析】
// @Summary      获取产品客户维度分析数据
// @Description  获取产品客户维度分析数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetProAnalysisCustomerRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/productAnalysis/customer [get]
func GetProAnalysisCustomerData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetProAnalysisCustomerRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetProAnalysisCustomerData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetCusAnalysisCustomerData 获取客户维度分析数据
//
// @Tags         【数据分析】
// @Summary      获取客户维度分析数据
// @Description  获取客户维度分析数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetCusAnalysisCustomerRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/customerAnalysis/customer [get]
func GetCusAnalysisCustomerData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetCusAnalysisCustomerRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetCusAnalysisCustomerData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetCusAnalysisProductData 获取客户产品维度分析数据
// @Summary 获取客户产品维度分析数据
// @Description 获取客户产品维度分析数据
// @Tags 数据分析
// @Accept json
// @Produce json
// @Param platform_id header string true "平台ID"
// @Param Authorization header string true "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success 200 {object} system.Response{data=structure.GetCusAnalysisProductRes}
// @Router /hcscm/h5/v1/data_analysis/analysis/customerAnalysis/product [get]
func GetCusAnalysisProductData(c *gin.Context) {
	var (
		req  structure.GetAnalysisHomeReq
		data structure.GetCusAnalysisProductRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	if err = system.ShouldBind(c, &req); err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetCusAnalysisProductData(ctx, &req)
	if err != nil {
		return
	}

	return
}

// GetTopProductData 获取前十产品数据
//
// @Tags         数据分析
// @Summary      获取前十产品销售排名数据
// @Description  根据指定时间范围获取销量/销售额前十的产品数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端平台ID"
// @Param        Authorization   header  string  true  "Bearer 用户令牌"
// @Param   body   body   structure.GetAnalysisHomeReq{}  true "请求体"
// @Router       /hcscm/h5/v1/data_analysis/analysis/getTopProductData [get]
func GetTopProductData(c *gin.Context) {
	var (
		req = &structure.GetAnalysisHomeReq{}
		res structure.GetTopDataRes
		err error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, &res)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	res, err = svc.GetTopProductData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetTopProductData 获取前十产品数据
//
// @Tags         数据分析
// @Summary      获取前十产品颜色排名数据
// @Description  根据指定时间范围获取销量/销售额前十的产品数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端平台ID"
// @Param        Authorization   header  string  true  "Bearer 用户令牌"
// @Param   body   body   structure.GetAnalysisHomeReq{}  true "请求体"
// @Success      200  {object}   structure.GetTopDataRes
// @Router       /hcscm/h5/v1/data_analysis/analysis/getTopColorData [get]
func GetTopColorData(c *gin.Context) {
	var (
		req = &structure.GetAnalysisHomeReq{}
		res structure.GetTopDataRes
		err error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, &res)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	res, err = svc.GetTopColorData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetTopProductData 获取前十产品数据
//
// @Tags         数据分析
// @Summary      获取前十产品销售排名数据
// @Description  根据指定时间范围获取销量/销售额前十的产品数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端平台ID"
// @Param        Authorization   header  string  true  "Bearer 用户令牌"
// @Param   body   body   structure.GetAnalysisHomeReq{}  true "请求体"
// @Success      200  {object}   structure.GetTopDataRes
// @Router       /hcscm/h5/v1/data_analysis/analysis/getTopCustomerData [get]
func GetTopCustomerData(c *gin.Context) {
	var (
		req = &structure.GetAnalysisHomeReq{}
		res structure.GetTopDataRes
		err error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, &res)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	res, err = svc.GetTopCustomerData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetUnifiedHomeAnalysisData 获取统一分析数据
//
// @Tags         【数据分析】
// @Summary      获取统一分析数据
// @Description  获取统一分析数据，包括首页数据、趋势数据和Top产品数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetUnifiedAnalysisRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/getUnifiedHomeAnalysis [get]
func GetUnifiedHomeAnalysisData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetUnifiedAnalysisRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetUnifiedHomeAnalysisData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetUnifiedProductDetailAnalysisData 获取统一产品详情分析数据
//
// @Tags         【数据分析】
// @Summary      获取统一产品详情分析数据
// @Description  获取统一产品详情分析数据，包括产品维度分析数据、Top颜色数据和Top客户数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetUnifiedProductDetailAnalysisRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/productAnalysis/getUnifiedProductDetailAnalysis [get]
func GetUnifiedProductDetailAnalysisData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetUnifiedProductDetailAnalysisRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetUnifiedProductDetailAnalysisData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetUnifiedCustomerDetailAnalysisData 获取统一客户详情分析数据
//
// @Tags         【数据分析】
// @Summary      获取统一客户详情分析数据
// @Description  获取统一客户详情分析数据，包括客户维度分析数据和Top客户数据
// @Security     ApiKeyAuth
// @Accept       application/json
// @Produce      application/json
// @Param        Platform        header  int     true  "终端ID"
// @Param        Authorization   header  string  true  "token"
// @Param        body            body    structure.GetAnalysisHomeReq{}  true  "请求参数"
// @Success      200  {object}   structure.GetUnifiedCustomerDetailAnalysisRes{}
// @Router       /hcscm/h5/v1/data_analysis/analysis/productAnalysis/getUnifiedCustomerDetailAnalysis [get]
func GetUnifiedCustomerDetailAnalysisData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetUnifiedCustomerDetailAnalysisRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetUnifiedCustomerDetailAnalysisData(ctx, req)
	if err != nil {
		return
	}

	return
}

func GetUnifiedCustomerDetailCustomerAnalysisData(c *gin.Context) {
	var (
		req  = &structure.GetAnalysisHomeReq{}
		data structure.GetUnifiedCustomerDetailCustomerAnalysisRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetUnifiedCustomerDetailCustomerAnalysisData(ctx, req)
	if err != nil {
		return
	}

	return
}

// GetReceivableOrderList 获取指定客户的指定时间段的应收单记录
//
//	@Tags		【数据分析】
//	@Summary	获取指定客户的指定时间段的应收单记录
//	@Security	ApiKeyAuth
//	@accept		application/json
//	@Produce	application/json
//	@Param		Platform		header		int			true	"终端ID"
//	@Param		Authorization	header		string		true	"token"
//	@Param		customer_id		query		int			true	"客户ID"
//	@Param		start_time		query		string		false	"开始时间"
//	@Param		end_time		query		string		false	"结束时间"
//	@Param		page			query		int			false	"页码"
//	@Param		size			query		int			false	"每页数量"
//	@Success	200				{object}	structure.GetReceivableOrderListRes{}
//	@Router		/hcscm/h5/v1/analysis/customerAnalysis/getReceivableOrderList [get]
func GetReceivableOrderList(c *gin.Context) {
	var (
		req  = &structure.GetReceivableOrderListReq{}
		data structure.GetReceivableOrderListRes
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, req)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	svc := data_analysis.NewDataAnalysisService(tx)
	data, err = svc.GetReceivableOrderList(ctx, req)
	return
}
