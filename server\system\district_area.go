package system

import (
	"context"
	"github.com/gin-gonic/gin"
	model "hcscm/model/mysql/system"
	svc "hcscm/service/system"
	structure "hcscm/structure/system"

	"hcscm/extern/district"
	mysql "hcscm/model/mysql/system/dao"
)

func SyncDistrictArea() (count int, err error) {

	var (
		page   = 1
		offset = 100
		rsp    district.GetDistrictResponse
	)

	rsp, err = district.GetDistrict(page, offset)
	if err != nil {
		return
	}

	count, err = insertDistrictResponseToMysql(rsp.Districts, "")
	return
}

func insertDistrictResponseToMysql(districts []district.District, parentName string) (count int, err error) {
	var (
		parent   model.DistrictArea
		parentID uint64
		rCount   int
		ok       bool
	)

	if parentName != "" {
		parent, err = mysql.MustFirstDistrictAreaByName(nil, parentName)
		if err != nil {
			return
		}
		parentID = parent.Id
	}

	for _, val := range districts {
		area := model.NewDistrictArea(val.Level, model.WrapLevel(val.Level), val.Adcode, val.Center, val.Name, parentID)
		area, ok, err = mysql.ShouldCreateDistrictArea(nil, area)
		if err != nil {
			return
		} else if !ok {
			continue
		} else {
			count += 1
		}

		rCount, err = insertDistrictResponseToMysql(val.Districts, val.Name)
		if err != nil {
			return
		}
		count += rCount
	}

	return
}

// @Tags      【行政地区】
// @Security  ApiKeyAuth
// @Summary   获取对应级别的地区列表
// @Produce   json
// @Param     Platform  header  int  true  "终端ID"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     parent_id  query  int  false  "父级行政地区ID"
// @Param     name  query  int  false  "行政地区名称"
// @Param     district_level  query  int  false  "行政地区级别"
// @Success   200        {object}  structure.GetAreaDataList{}
// @Router	/hcscm/admin/v1/district/list [get]
func GetDistrictList(c *gin.Context) {
	var (
		query = &structure.GetProvinceListQuery{}
		err   error
		list  = make(structure.GetAreaDataList, 0)
		total int
	)

	defer func() {
		BuildListResponse(c, err, list, total)
	}()

	err = ShouldBind(c, query)
	if err != nil {
		return
	}

	list, total, err = svc.NewDistrictAreaLogic(context.Background(), false).GetDistrictAreaForCityList(context.Background(), query)
	if err != nil {
		return
	}
}

// @Tags      【行政地区】
// @Security  ApiKeyAuth
// @Summary   获取全部级别地区列表
// @Produce   json
// @Param     Platform  header  int  true  "终端ID"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     parent_id  query  int  false  "父级行政地区ID"
// @Param     name  query  int  false  "行政地区名称"
// @Param     district_level  query  int  false  "行政地区级别"
// @Success   200        {object}  structure.GetAreaDataList{}
// @Router	/hcscm/admin/v1/district/enum_list [get]
func GetDistrictEnumList(c *gin.Context) {
	var (
		query = &structure.GetProvinceListQuery{}
		err   error
		list  = make(structure.GetAreaDataList, 0)
		total int
	)

	defer func() {
		BuildListResponse(c, err, list, total)
	}()

	err = ShouldBind(c, query)
	if err != nil {
		return
	}

	list, total, err = svc.NewDistrictAreaLogic(context.Background(), false).GetDistrictAreaEnumList(context.Background(), query)
	if err != nil {
		return
	}
}
