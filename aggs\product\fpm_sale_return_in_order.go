package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	salePriceAggs "hcscm/aggs/sale_price"
	cus_error "hcscm/common/errors"
	cus_const "hcscm/common/product"
	should_collect_order_consts "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	base_product_pb "hcscm/extern/pb/basic_data/product"
	product2 "hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	"hcscm/extern/pb/sale_price"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/should_collect_order"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	salePriceModel "hcscm/model/mysql/sale_price"
	salePriceMysql "hcscm/model/mysql/sale_price/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strconv"
	"time"
)

type FpmSaleReturnInOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmSaleReturnInOrderRepo(tx *mysql_base.Tx) *FpmSaleReturnInOrderRepo {
	return &FpmSaleReturnInOrderRepo{tx: tx}
}

func (r *FpmSaleReturnInOrderRepo) Add(ctx context.Context, req *structure.AddFpmSaleReturnInOrderParam) (data structure.AddFpmSaleReturnInOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
	)

	fpmSaleReturnInOrder := model.NewFpmSaleReturnInOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmSaleReturnInOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmSaleReturnInOrder.BusinessClose = common_system.BusinessCloseNo
	fpmSaleReturnInOrder.DepartmentId = info.GetDepartmentId()
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleReturnInOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleReturnInOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmSaleReturnInOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmSaleReturnInOrder.OrderNo = orderNo
	fpmSaleReturnInOrder.Number = int(number)

	fpmSaleReturnInOrder.TotalWeight, fpmSaleReturnInOrder.TotalRoll, fpmSaleReturnInOrder.TotalLength = req.MPGetTotalPWR()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleReturnInOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleReturnInOrder, err = mysql.MustCreateFpmInOrder(r.tx, fpmSaleReturnInOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmSaleReturnInOrderItem := model.NewFpmSaleReturnInOrderItem(ctx, &item)
		fpmSaleReturnInOrderItem.WarehouseInType = cus_const.WarehouseGoodInTypeSaleReturn
		fpmSaleReturnInOrderItem.ParentId = fpmSaleReturnInOrder.Id
		fpmSaleReturnInOrderItem.ParentOrderNo = fpmSaleReturnInOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL()
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmSaleReturnInOrderItem.InWeight = tw
		fpmSaleReturnInOrderItem.SettlePrice = tp
		fpmSaleReturnInOrderItem.InLength = tl
		fpmSaleReturnInOrderItem.WeightError = weightError
		fpmSaleReturnInOrderItem.SettleErrorWeight = settleErrorWeight
		fpmSaleReturnInOrderItem.PaperTubeWeight = tpp
		fpmSaleReturnInOrderItem.SettleWeight = tsw
		fpmSaleReturnInOrderItem.ActuallyWeight = taw
		fpmSaleReturnInOrderItem, err = mysql.MustCreateFpmInOrderItem(r.tx, fpmSaleReturnInOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			// fineCode.DyeFactoryColorCode = fpmSaleReturnInOrderItem.DyeFactoryColorCode
			// fineCode.DyeFactoryDyelotNumber = fpmSaleReturnInOrderItem.DyeFactoryDyelotNumber
			itemFc := model.NewFpmInOrderItemFc(ctx, &fineCode)
			itemFc.ParentId = fpmSaleReturnInOrderItem.Id
			itemFc.WarehouseInType = cus_const.WarehouseGoodInTypeSaleReturn
			itemFc.WarehouseInOrderId = fpmSaleReturnInOrder.Id
			itemFc.WarehouseInOrderNo = fpmSaleReturnInOrder.OrderNo
			itemFc.OrderTime = fpmSaleReturnInOrder.WarehouseInTime
			itemFc.WarehouseId = fpmSaleReturnInOrder.WarehouseId
			itemFc.SumStockId = item.SumStockId
			if itemFc.ProductGramWeight == "" && itemFc.ProductWidth == "" && itemFc.FinishProductGramWeightUnitId == 0 && itemFc.FinishProductWidthUnitId == 0 {
				itemFc.ProductWidth = item.ProductWidth
				itemFc.ProductGramWeight = item.ProductGramWeight
				itemFc.FinishProductWidthUnitId = item.FinishProductWidthUnitId
				itemFc.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
			}
			// itemFc.GenBarQrCode(ctx, item.ProductCode, item.ProductColorCode)
			itemFc, err = mysql.MustCreateFpmInOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleReturnInOrder.Id
	return
}

func (r *FpmSaleReturnInOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmSaleReturnInOrderParam) (data structure.UpdateFpmSaleReturnInOrderData, err error) {
	var (
		fpmSaleReturnInOrder model.FpmInOrder
		itemModel            model.FpmInOrderItem
		findCodeModel        model.FpmInOrderItemFc
		itemList             model.FpmInOrderItemList
	)
	fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmSaleReturnInOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，当前单据状态不能更新。")
		return
	}

	fpmSaleReturnInOrder.UpdateFpmSaleReturnInOrder(ctx, req)

	fpmSaleReturnInOrder.TotalWeight, fpmSaleReturnInOrder.TotalRoll, fpmSaleReturnInOrder.TotalLength = req.GetTotalPWR()

	if fpmSaleReturnInOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmSaleReturnInOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}
	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleReturnInOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleReturnInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleReturnInOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	// fcList, _ := mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemIds)
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
		if err != nil {
			return
		}
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmSaleReturnInOrderItem := model.NewFpmSaleReturnInOrderItem(ctx, &item)
		fpmSaleReturnInOrderItem.WarehouseInType = cus_const.WarehouseGoodInTypeSaleReturn
		fpmSaleReturnInOrderItem.ParentId = fpmSaleReturnInOrder.Id
		fpmSaleReturnInOrderItem.ParentOrderNo = fpmSaleReturnInOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL()
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmSaleReturnInOrderItem.InWeight = tw
		fpmSaleReturnInOrderItem.SettlePrice = tp
		fpmSaleReturnInOrderItem.InLength = tl
		fpmSaleReturnInOrderItem.WeightError = weightError
		fpmSaleReturnInOrderItem.SettleErrorWeight = settleErrorWeight
		fpmSaleReturnInOrderItem.PaperTubeWeight = tpp
		fpmSaleReturnInOrderItem.SettleWeight = tsw
		fpmSaleReturnInOrderItem.ActuallyWeight = taw
		fpmSaleReturnInOrderItem, err = mysql.MustCreateFpmInOrderItem(r.tx, fpmSaleReturnInOrderItem)
		if err != nil {
			return
		}
		// oldItem := itemList.Pick(item.Id)
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			// oldFc := fcList.Pick(fineCode.Id)
			// fineCode.DyeFactoryColorCode = fpmSaleReturnInOrderItem.DyeFactoryColorCode
			// fineCode.DyeFactoryDyelotNumber = fpmSaleReturnInOrderItem.DyeFactoryDyelotNumber
			itemFc := model.NewFpmInOrderItemFc(ctx, &fineCode)
			itemFc.ParentId = fpmSaleReturnInOrderItem.Id
			itemFc.WarehouseInType = cus_const.WarehouseGoodInTypeSaleReturn
			itemFc.WarehouseInOrderId = fpmSaleReturnInOrder.Id
			itemFc.WarehouseInOrderNo = fpmSaleReturnInOrder.OrderNo
			itemFc.OrderTime = fpmSaleReturnInOrder.WarehouseInTime
			itemFc.WarehouseId = fpmSaleReturnInOrder.WarehouseId
			itemFc.SumStockId = item.SumStockId
			itemFc.UnitId = fineCode.UnitId
			if itemFc.ProductGramWeight == "" && itemFc.ProductWidth == "" && itemFc.FinishProductGramWeightUnitId == 0 && itemFc.FinishProductWidthUnitId == 0 {
				itemFc.ProductWidth = item.ProductWidth
				itemFc.ProductGramWeight = item.ProductGramWeight
				itemFc.FinishProductWidthUnitId = item.FinishProductWidthUnitId
				itemFc.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
			}
			// itemFc.GenBarQrCodeV2(ctx, item.ProductCode, item.ProductColorCode, item.ProductId, item.ProductColorId, oldItem.ProductId, oldItem.ProductColorId, oldFc)
			itemFc, err = mysql.MustCreateFpmInOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleReturnInOrder.Id
	return
}

func (r *FpmSaleReturnInOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmSaleReturnInOrderBusinessCloseParam) (data structure.UpdateFpmSaleReturnInOrderData, err error) {
	var (
		fpmSaleReturnInOrder model.FpmInOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, v)
		if err != nil {
			return
		}
		// 更新业务状态
		err = fpmSaleReturnInOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmSaleReturnInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleReturnInOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmSaleReturnInOrderRepo) UpdateStatusPass(
	ctx context.Context,
	id uint64,
	productSaleBackInList map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
) (
	data structure.UpdateFpmSaleReturnInOrderStatusData,
	addItems structure.AddStockProductDetailParamList,
	updateItems structure.UpdateStockProductDetailParamList,
	addShouldCollectOrderItem *shouldCollectOrderStructure.AddProductReturnShouldCollectOrderParam,
	productSaleBackOutList map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
	err error) {
	var (
		fpmSaleReturnInOrder        model.FpmInOrder
		fpmSaleReturnInOrderItems   model.FpmInOrderItemList
		fpmSaleReturnInOrderItemFcs model.FpmInOrderItemFcList
		salePriceColorKindRels      salePriceModel.SalePriceColorKindRelList
		salePriceColorKinds         salePriceModel.SalePriceColorKindList
		customerSalePriceAdjusts    salePriceModel.CustomerSalePriceAdjustList
		saleLevelPrices             salePriceModel.SalePriceLevelList
		productColorItems           product2.ProductColorResList
		saleLevel                   map[uint64]string
	)
	fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	fpmSaleReturnInOrderItems, err = mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}

	fpmSaleReturnInOrderItemFcs, err = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, fpmSaleReturnInOrderItems.GetIds())
	if err != nil {
		return
	}

	err, addItems, updateItems = r.judgeAuditPass(id, fpmSaleReturnInOrder, ctx)
	if err != nil {
		return
	}
	// 审核
	err = fpmSaleReturnInOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmSaleReturnInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleReturnInOrder)
	if err != nil {
		return
	}
	// 更新已经入仓库
	err = mysql.MustUpdateIsInWarehouseByOrderId(r.tx, fpmSaleReturnInOrder.Id, true)
	if err != nil {
		return
	}

	// 生成销售退货单参数,查找详情
	productSaleIDs := fpmSaleReturnInOrderItems.GetQuoteOrderItemIds()
	productSaleSvc := should_collect_order.NewProductSaleClient()
	productSales, err := productSaleSvc.GetProductSaleItemByIds(ctx, should_collect_order.ProductSaleReq{IDs: productSaleIDs})
	if err != nil {
		return
	}

	productSaleItems := productSales.ToProductSaleItemData()

	// 反写销售送货单结构体
	productSaleBackOutList, err = fpmSaleReturnInOrderItems.ToModifyProductSaleShouldCollectOrderAudit(productSaleBackInList, productSaleItems)
	if err != nil {
		return
	}

	if len(fpmSaleReturnInOrderItems) != len(productSaleItems) {
		productColorIds := mysql_base.GetUInt64List(fpmSaleReturnInOrderItems, "product_color_id")
		productColorSvc := product2.NewProductColorClient()
		productColorItems, err = productColorSvc.GetProductColorByIds(ctx, productColorIds)
		if err != nil {
			return
		}
		productColorKindIds := mysql_base.GetUInt64ListV2("product_color_kind_id", productColorItems)
		productIds := mysql_base.GetUInt64ListV2("product_id", fpmSaleReturnInOrderItems)
		salePriceColorKindRels, err = salePriceMysql.FindSalePriceColorKindRelByProductAndColorKind(r.tx, productIds, productColorKindIds)
		if err != nil {
			return
		}

		salePriceColorKindRelIds := mysql_base.GetUInt64List(salePriceColorKindRels, "sale_price_color_kind_rel_id")
		versions := mysql_base.GetIntList(salePriceColorKindRels, "version")

		// 获取正在生效以及下一次生效的版本组合salePriceColorKindRelId查询
		salePriceColorKinds, err = salePriceMysql.FindSalePriceColorKindByParentIdsAndVersions(r.tx, salePriceColorKindRelIds, versions)
		if err != nil {
			return
		}

		salePriceColorKindIds := mysql_base.GetUInt64List(salePriceColorKinds, "sale_price_color_kind_id")
		saleLevelPrices, err = salePriceMysql.FindSalePriceLevelByParentIDs(r.tx, salePriceColorKindIds)
		if err != nil {
			return
		}

		customerSalePriceAdjusts, err = salePriceMysql.FindCustomerSalePriceAdjustByCustomerId(r.tx, fpmSaleReturnInOrder.BizUnitId)
		if err != nil {
			return
		}

		saleLevelSvc := sale_price.NewSaleLevelClient()
		saleLevel, err = saleLevelSvc.GetAllEnableSaleLevel(ctx)
		if err != nil {
			return
		}
	}

	addRes := r.ToAddProductReturnShouldCollectOrderParam(
		ctx,
		r.tx,
		saleLevel,
		salePriceColorKinds,
		customerSalePriceAdjusts,
		saleLevelPrices,
		productColorItems,
		fpmSaleReturnInOrder,
		fpmSaleReturnInOrderItems,
		productSaleItems,
		fpmSaleReturnInOrderItemFcs,
	)
	addShouldCollectOrderItem = addRes

	data.WarehouseInTime = tools.MyTime(fpmSaleReturnInOrder.WarehouseInTime)
	return
}

func (r *FpmSaleReturnInOrderRepo) ToAddProductReturnShouldCollectOrderParam(
	ctx context.Context,
	tx *mysql_base.Tx,
	saleLevel map[uint64]string,
	salePriceColorKinds salePriceModel.SalePriceColorKindList,
	customerSalePriceAdjusts salePriceModel.CustomerSalePriceAdjustList,
	saleLevelPrices salePriceModel.SalePriceLevelList,
	productColorItems product2.ProductColorResList,
	fpmSaleReturnInOrder model.FpmInOrder,
	fpmSaleReturnInOrderItems model.FpmInOrderItemList,
	productSaleShouldCollectOrderItems structure.ProductSaleItemDataList,
	fpmSaleReturnInOrderItemFcs model.FpmInOrderItemFcList,
) (data *shouldCollectOrderStructure.AddProductReturnShouldCollectOrderParam) {
	var (
		items = make(shouldCollectOrderStructure.AddProductReturnShouldCollectOrderDetailParamList, 0)
	)
	o := &shouldCollectOrderStructure.AddProductReturnShouldCollectOrderParam{}
	o.SrcId = fpmSaleReturnInOrder.Id
	o.SrcOrderNo = fpmSaleReturnInOrder.OrderNo
	o.SaleSystemId = fpmSaleReturnInOrder.SaleSystemId
	o.CustomerId = fpmSaleReturnInOrder.BizUnitId
	o.SrcOrderType = should_collect_order_consts.SrcOrderTypeSelf
	o.OrderTime = tools.QueryTime(fpmSaleReturnInOrder.AuditDate.Format(time.DateOnly))
	o.SaleMode = fpmSaleReturnInOrder.SaleMode // 下推订单类型
	for _, fpmSaleReturnInOrderItem := range fpmSaleReturnInOrderItems {
		detail := shouldCollectOrderStructure.AddProductReturnShouldCollectOrderDetailParam{}
		productSaleShouldCollectOrderItem := productSaleShouldCollectOrderItems.Pick(fpmSaleReturnInOrderItem.QuoteOrderItemId)
		o.TotalSettleMoney += fpmSaleReturnInOrderItem.SettlePrice
		o.TotalShouldCollectMoney += fpmSaleReturnInOrderItem.SettlePrice
		o.TotalUnCollectMoney += fpmSaleReturnInOrderItem.SettlePrice
		// o.CollectedMoney += fpmSaleReturnInOrderItem.SettlePrice
		detail.SrcDetailId = fpmSaleReturnInOrderItem.Id
		detail.MaterialId = fpmSaleReturnInOrderItem.ProductId
		detail.Code = fpmSaleReturnInOrderItem.ProductCode
		detail.Name = fpmSaleReturnInOrderItem.ProductName
		detail.ProductColorId = fpmSaleReturnInOrderItem.ProductColorId
		detail.ProductColorCode = fpmSaleReturnInOrderItem.ProductColorCode
		detail.ProductColorName = fpmSaleReturnInOrderItem.ProductColorName
		detail.DyelotNumber = fpmSaleReturnInOrderItem.DyeFactoryDyelotNumber
		detail.Roll = -fpmSaleReturnInOrderItem.InRoll
		detail.Weight = -fpmSaleReturnInOrderItem.InWeight
		detail.WeightError = -fpmSaleReturnInOrderItem.WeightError
		detail.ActuallyWeight = -fpmSaleReturnInOrderItem.ActuallyWeight
		detail.SettleErrorWeight = -fpmSaleReturnInOrderItem.SettleErrorWeight
		detail.SettleWeight = -fpmSaleReturnInOrderItem.SettleWeight
		detail.Length = -fpmSaleReturnInOrderItem.InLength
		detail.Remark = fpmSaleReturnInOrderItem.Remark
		detail.MeasurementUnitId = fpmSaleReturnInOrderItem.UnitId
		detail.AuxiliaryUnitId = fpmSaleReturnInOrderItem.AuxiliaryUnitId
		if detail.AuxiliaryUnitId == 0 {
			detail.AuxiliaryUnitId = detail.MeasurementUnitId
		}

		if fpmSaleReturnInOrderItem.QuoteOrderItemId != 0 {
			detail.StandardSalePrice = productSaleShouldCollectOrderItem.StandardSalePrice
			detail.SaleLevelId = productSaleShouldCollectOrderItem.SaleLevelId
			detail.OffsetSalePrice = productSaleShouldCollectOrderItem.OffsetSalePrice
			detail.SalePrice = productSaleShouldCollectOrderItem.SalePrice
			detail.ReturnPrice = productSaleShouldCollectOrderItem.SalePrice
			detail.StandardWeightError = productSaleShouldCollectOrderItem.StandardWeightError
			detail.OffsetWeightError = productSaleShouldCollectOrderItem.OffsetWeightError
			detail.AdjustWeightError = productSaleShouldCollectOrderItem.AdjustWeightError
			// detail.SettleErrorWeight = productSaleShouldCollectOrderItem.SettleErrorWeight
			detail.StandardLengthCutSalePrice = productSaleShouldCollectOrderItem.StandardLengthCutSalePrice
			detail.OffsetLengthCutSalePrice = productSaleShouldCollectOrderItem.OffsetLengthCutSalePrice
			detail.LengthCutSalePrice = productSaleShouldCollectOrderItem.LengthCutSalePrice
			detail.LengthCutReturnPrice = productSaleShouldCollectOrderItem.LengthCutSalePrice
			detail.OtherPrice = productSaleShouldCollectOrderItem.OtherPrice
			detail.SaleTaxRate = productSaleShouldCollectOrderItem.SaleTaxRate
		} else {
			productColor := productColorItems.PickByProductColorId(fpmSaleReturnInOrderItem.ProductColorId)
			salePriceColorKind := salePriceColorKinds.Pick(fpmSaleReturnInOrderItem.ProductId, productColor.TypeFinishedProductKindId)
			salePrice := salePriceAggs.CalcPurchaserLadderSalePrice(
				ctx,
				tx,
				saleLevel,
				salePriceColorKind,
				customerSalePriceAdjusts,
				saleLevelPrices,
				fpmSaleReturnInOrderItem.ProductId,
				productColor.TypeFinishedProductKindId,
				productColor.Id,
			)
			detail.SaleLevelId = salePrice.SaleLevelId
			detail.StandardSalePrice = salePrice.StandardSalePrice
			detail.OffsetSalePrice = salePrice.OffsetSalePrice
			detail.SalePrice = salePrice.SalePrice
			detail.ReturnPrice = salePrice.SalePrice
			if salePrice.SalePrice == 0 {
				detail.SalePrice = fpmSaleReturnInOrderItem.ReturnPrice
				detail.StandardLengthCutSalePrice = fpmSaleReturnInOrderItem.LengthCutReturnPrice
				detail.ReturnPrice = fpmSaleReturnInOrderItem.ReturnPrice
			}
			detail.StandardWeightError = salePrice.WeightError
			detail.OffsetWeightError = salePrice.OffsetWeightError
			detail.AdjustWeightError = 0
			// detail.SettleErrorWeight = detail.OffsetWeightError - detail.AdjustWeightError
			detail.StandardLengthCutSalePrice = salePrice.StandardLengthCutSalePrice
			detail.OffsetLengthCutSalePrice = salePrice.OffsetLengthCutSalePrice
			detail.LengthCutSalePrice = salePrice.LengthCutSalePrice
			detail.LengthCutReturnPrice = salePrice.LengthCutSalePrice
			detail.OtherPrice = 0
			detail.SaleTaxRate = 0
		}
		if o.SrcOrderType == should_collect_order_consts.SrcOrderTypeSelf {
			detail.ReturnPrice = fpmSaleReturnInOrderItem.ReturnPrice
			detail.SettlePrice = fpmSaleReturnInOrderItem.SettlePrice
			detail.LengthCutReturnPrice = fpmSaleReturnInOrderItem.LengthCutReturnPrice
			detail.SaleLevelId = fpmSaleReturnInOrderItem.ProductLevelId
			// detail.SettleErrorWeight = fpmSaleReturnInOrderItem.SettleErrorWeight
			detail.StandardLengthCutSalePrice = fpmSaleReturnInOrderItem.LengthCutReturnPrice
			// detail.LengthCutSalePrice = fpmSaleReturnInOrderItem.LengthCutReturnPrice
		}
		for _, fcItem := range fpmSaleReturnInOrderItemFcs.PickFcListByParentId(fpmSaleReturnInOrderItem.Id) {
			detail.FcDataList = append(detail.FcDataList, shouldCollectOrderStructure.AddProductSaleReturnShouldCollectOrderDetailFcParam{
				SrcDetailId:       fpmSaleReturnInOrderItem.Id,
				SrcDetailFcId:     fcItem.Id,
				WeightError:       -fcItem.WeightError,
				SettleErrorWeight: -fcItem.SettleErrorWeight,
				Roll:              -fcItem.Roll,
				WarehouseId:       fcItem.WarehouseId,
				WarehouseBinId:    fcItem.WarehouseBinId,
				VolumeNumber:      fcItem.VolumeNumber,
				StockId:           fcItem.StockId,
				SumStockId:        fcItem.SumStockId,
				BaseUnitWeight:    -fcItem.BaseUnitWeight,
				PaperTubeWeight:   -fcItem.PaperTubeWeight,
				UnitId:            fcItem.UnitId,
				Length:            -fcItem.Length,
			})
		}
		o.Roll += detail.Roll
		o.Weight += detail.Weight
		items = append(items, detail)
	}
	o.SrcOrderType = should_collect_order_consts.SrcOrderTypeProductSaleReturn
	o.ItemData = items
	data = o
	return
}

func (r *FpmSaleReturnInOrderRepo) UpdateStatusWait(
	ctx context.Context,
	id uint64,
	productSaleBackInList map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
) (
	data structure.UpdateFpmSaleReturnInOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	productSaleBackOutList map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
	err error,
) {
	var (
		fpmSaleReturnInOrder      model.FpmInOrder
		fpmSaleReturnInOrderItems model.FpmInOrderItemList
	)

	fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	fpmSaleReturnInOrderItems, err = mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}

	updateItems, err = r.judgeAuditWait(id, fpmSaleReturnInOrder, ctx)
	if err != nil {
		return
	}

	// 消审
	err = fpmSaleReturnInOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmSaleReturnInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleReturnInOrder)
	if err != nil {
		return
	}

	// 更新已经入仓库
	err = mysql.MustUpdateIsInWarehouseByOrderId(r.tx, fpmSaleReturnInOrder.Id, false)
	if err != nil {
		return
	}

	// 反写销售送货单结构体
	productSaleBackOutList, err = fpmSaleReturnInOrderItems.ToModifyProductSaleShouldCollectOrderWait(productSaleBackInList)
	if err != nil {
		return
	}

	data.WarehouseInTime = tools.MyTime(fpmSaleReturnInOrder.WarehouseInTime)
	return
}

func (r *FpmSaleReturnInOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmSaleReturnInOrderStatusData, err error) {
	var (
		fpmSaleReturnInOrder model.FpmInOrder
	)

	fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 拒绝/驳回
	err = fpmSaleReturnInOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmSaleReturnInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleReturnInOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmSaleReturnInOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmSaleReturnInOrderStatusData, err error) {
	var (
		fpmSaleReturnInOrder model.FpmInOrder
	)

	fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 作废
	err = fpmSaleReturnInOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmSaleReturnInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleReturnInOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmSaleReturnInOrderRepo) Get(ctx context.Context, req *structure.GetFpmSaleReturnInOrderQuery) (data structure.GetFpmSaleReturnInOrderData, err error) {
	var (
		fpmSaleReturnInOrder  model.FpmInOrder
		itemDatas             model.FpmInOrderItemList
		fineCodeList          model.FpmInOrderItemFcList
		bizService            = biz_pb.NewClientBizUnitService()
		pLevelPB              = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB                = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB           = warehouse_pb.NewPhysicalWarehouseClient()
		baseProductPB         = base_product_pb.NewProductColorClient()
		dicSvc                = dictionary.NewDictionaryClient()
		shouldCollectOrderSvc = should_collect_order.NewProductSaleClient()
	)
	fpmSaleReturnInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmSaleReturnInOrderData{}
	r.swapListModel2Data(fpmSaleReturnInOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}
	fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	var (
		quoteOrderItemIds = set.NewUint64Set()
	)
	for _, itemData := range itemDatas {
		quoteOrderItemIds.Add(itemData.QuoteOrderItemId)
	}

	bizIds := mysql_base.GetUInt64List(itemDatas, "biz_unit_id")
	levelIds := mysql_base.GetUInt64List(itemDatas, "product_level_id")
	mUnitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", itemDatas, fineCodeList)
	mAuxiliaryUnitIds := mysql_base.GetUInt64ListV2("auxiliary_unit_id", itemDatas)
	colorIds := mysql_base.GetUInt64List(itemDatas, "product_color_id")
	dicIds := mysql_base.GetUInt64ListV2("dictionary_detail_id", itemDatas, fineCodeList)
	wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, bizIds)
	levelNameMap, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds)
	unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, tools.MergeSlicesUint64(mUnitIds, mAuxiliaryUnitIds))
	ColorMap, _ := baseProductPB.GetProductColorItemByIds(ctx, colorIds)
	binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)
	shouldCollectOrderDetails, _ := shouldCollectOrderSvc.GetProductSaleItemByIds(ctx, should_collect_order.ProductSaleReq{IDs: quoteOrderItemIds.List()})

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmSaleReturnInOrderItemData{}
		itemGetData.Id = itemData.Id
		itemGetData.CreateTime = tools.MyTime(itemData.CreateTime)
		itemGetData.UpdateTime = tools.MyTime(itemData.UpdateTime)
		itemGetData.CreatorId = itemData.CreatorId
		itemGetData.CreatorName = itemData.CreatorName
		itemGetData.UpdaterId = itemData.UpdaterId
		itemGetData.UpdateUserName = itemData.UpdaterName
		itemGetData.ParentId = itemData.ParentId
		itemGetData.SumStockId = itemData.SumStockId
		itemGetData.ParentOrderNo = itemData.ParentOrderNo
		itemGetData.QuoteOrderNo = itemData.QuoteOrderNo
		itemGetData.QuoteOrderItemId = itemData.QuoteOrderItemId
		itemGetData.ProductId = itemData.ProductId
		itemGetData.ProductCode = itemData.ProductCode
		itemGetData.ProductName = itemData.ProductName
		itemGetData.CustomerId = itemData.CustomerId
		itemGetData.ProductColorId = itemData.ProductColorId
		itemGetData.ProductColorCode = itemData.ProductColorCode
		itemGetData.ProductColorName = itemData.ProductColorName
		itemGetData.DyeFactoryColorCode = itemData.DyeFactoryColorCode
		itemGetData.DyeFactoryDyelotNumber = itemData.DyeFactoryDyelotNumber
		itemGetData.ProductLevelId = itemData.ProductLevelId
		itemGetData.ProductRemark = itemData.ProductRemark
		itemGetData.ProductCraft = itemData.ProductCraft
		itemGetData.ProductIngredient = itemData.ProductIngredient
		itemGetData.InRoll = itemData.InRoll
		itemGetData.InWeight = itemData.InWeight
		itemGetData.WeightError = itemData.WeightError
		itemGetData.PaperTubeWeight = itemData.PaperTubeWeight
		itemGetData.SettleWeight = itemData.SettleWeight
		itemGetData.ActuallyWeight = itemData.ActuallyWeight
		itemGetData.SettleErrorWeight = itemData.SettleErrorWeight
		itemGetData.UnitId = itemData.UnitId
		itemGetData.AuxiliaryUnitId = itemData.AuxiliaryUnitId
		if itemData.AuxiliaryUnitId == 0 {
			itemGetData.AuxiliaryUnitId = itemData.UnitId
		}
		itemGetData.InLength = itemData.InLength
		itemGetData.Remark = itemData.Remark
		itemGetData.ReturnRoll = itemData.ReturnRoll
		itemGetData.ReturnWeight = itemData.ReturnWeight
		itemGetData.ReturnLength = itemData.ReturnLength
		itemGetData.ReturnPrice = itemData.ReturnPrice
		itemGetData.LengthCutReturnPrice = itemData.LengthCutReturnPrice

		itemGetData.SettlePrice = itemData.SettlePrice
		shouldCollectOrderDetail := shouldCollectOrderDetails.ToProductSaleItemData().Pick(itemData.QuoteOrderItemId)
		itemGetData.NoReturnRoll = shouldCollectOrderDetail.NoReturnRoll
		itemGetData.NoReturnLength = shouldCollectOrderDetail.NoReturnLength
		itemGetData.NoReturnWeight = shouldCollectOrderDetail.NoReturnWeight
		o.TotalSettlePrice += itemGetData.SettlePrice

		// 转义
		itemGetData.UnitName = unitNameMap[itemData.UnitId]
		itemGetData.AuxiliaryUnitName = unitNameMap[itemData.AuxiliaryUnitId]
		if val, ok := customerMap[itemData.CustomerId]; ok {
			itemGetData.CustomerName = val
		}
		itemGetData.ProductLevelName = levelNameMap[itemData.ProductLevelId]
		itemGetData.ProductColorName = ColorMap[itemData.ProductColorId][1]
		itemGetData.OutLength = itemData.InLength // 暂时使用，需要前端同步字段为in_length
		// r.swapItemModel2Data(itemData, &itemGetData, ctx)
		// 添加细码信息
		fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTID(r.tx, itemData.Id)
		if err != nil {
			return
		}

		itemGetData.BuildFPResp(itemData.ProductWidth, itemData.ProductGramWeight, dicNameMap[itemData.FinishProductWidthUnitId][1],
			dicNameMap[itemData.FinishProductGramWeightUnitId][1], itemData.FinishProductWidthUnitId, itemData.FinishProductGramWeightUnitId)

		fcList := fineCodeList.PickFcListByParentId(itemData.Id)

		for _, fineCode := range fcList {
			fineCodeGetData := structure.GetFpmInOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.SrcId = fineCode.SrcId
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.UnitId = fineCode.UnitId

			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.ReturnStockId = fineCode.ReturnStockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.DyelotNumber = fineCode.DyeFactoryDyelotNumber
			// 增加旧单判断，致盛旧单问题，如果重新更新才能去掉
			if fineCode.DyeFactoryDyelotNumber != itemData.DyeFactoryDyelotNumber && itemData.DyeFactoryDyelotNumber != "" {
				fineCodeGetData.DyeFactoryDyelotNumber = itemData.DyeFactoryDyelotNumber
				fineCodeGetData.DyelotNumber = itemData.DyeFactoryDyelotNumber
			}
			if fineCode.DyeFactoryColorCode != itemData.DyeFactoryColorCode {
				fineCodeGetData.DyeFactoryColorCode = itemData.DyeFactoryColorCode
			}
			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[fineCode.FinishProductWidthUnitId][1],
				dicNameMap[fineCode.FinishProductGramWeightUnitId][1], fineCode.FinishProductWidthUnitId, fineCode.FinishProductGramWeightUnitId)

			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmSaleReturnInOrderRepo) GetList(ctx context.Context, req *structure.GetFpmInOrderListQuery) (list structure.GetFpmSaleReturnInOrderDataList, total int, err error) {
	var (
		orders      model.FpmInOrderList
		ordersItems model.FpmInOrderItemList
		bizPB       = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmInOrder(r.tx, req)
	if err != nil {
		return
	}

	ordersItems, err = mysql.FindFpmInOrderItemByParenTIDs(r.tx, orders.GetIds())
	if err != nil {
		return
	}
	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", orders, ordersItems)
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		var (
			countSaleWeightMap = make(map[uint64]int)
		)
		_ordersItems := ordersItems.PickByParentId(src.Id)
		for _, _ordersItem := range _ordersItems {
			if _ordersItem.AuxiliaryUnitId != 0 {
				if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
				} else {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.InLength
				}
			} else {
				countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
			}
		}
		dst := structure.GetFpmSaleReturnInOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.SaleSystemId = src.SaleSystemId
		dst.CustomerId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.ReturnRemark = src.ReturnRemark
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.MergeWeightInfo = func() (str string) {
			for k, v := range countSaleWeightMap {
				fmtRound := tools.GetRound(v, 2)
				if str == "" {
					str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				} else {
					str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				}
			}
			return
		}()
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.VoucherNumber = src.VoucherNumber
		dst.TextureUrl = src.TextureUrl
		dst.SaleMode = src.SaleMode
		dst.SaleModeName = src.SaleMode.String()
		for _, itemData := range _ordersItems {
			dst.TotalSettlePrice += itemData.SettlePrice
		}
		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.CustomerName = bizNameMap[src.BizUnitId]
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.UnitName = unitNameMap[src.UnitId]
		list = append(list, dst)
	}
	return
}

func (r *FpmSaleReturnInOrderRepo) swapListModel2Data(src model.FpmInOrder, dst *structure.GetFpmSaleReturnInOrderData, ctx context.Context) {
	var (
		bizService        = biz_pb.NewClientBizUnitService()
		saleSystemService = sale_sys_pb.NewSaleSystemClient()
		userPB            = empl_pb.NewClientEmployeeService()
		userName          = make(map[uint64]string)
		unitPB            = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB       = warehouse_pb.NewPhysicalWarehouseClient()
	)
	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}

	saleSystemMap, err2 := saleSystemService.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}

	userName, _ = userPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)

	warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.SaleSystemId = src.SaleSystemId
	dst.CustomerId = src.BizUnitId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
	dst.StoreKeeperId = src.StoreKeeperId
	dst.ReturnRemark = src.ReturnRemark
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.VoucherNumber = src.VoucherNumber
	dst.TextureUrl = src.TextureUrl
	dst.SaleMode = src.SaleMode

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.SaleModeName = src.SaleMode.String()
	dst.WarehouseName = warehouseName
	dst.UnitName = unitName
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.CustomerName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	dst.WarehouseName = warehouseName

}

func (r *FpmSaleReturnInOrderRepo) swapItemModel2Data(item model.FpmInOrderItem, itemData *structure.GetFpmSaleReturnInOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{item.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, item.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, item.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, item.ProductColorId)

	itemData.Id = item.Id
	itemData.CreateTime = tools.MyTime(item.CreateTime)
	itemData.UpdateTime = tools.MyTime(item.UpdateTime)
	itemData.CreatorId = item.CreatorId
	itemData.CreatorName = item.CreatorName
	itemData.UpdaterId = item.UpdaterId
	itemData.UpdateUserName = item.UpdaterName
	itemData.ParentId = item.ParentId
	itemData.SumStockId = item.SumStockId
	itemData.ParentOrderNo = item.ParentOrderNo
	itemData.QuoteOrderNo = item.QuoteOrderNo
	itemData.QuoteOrderItemId = item.QuoteOrderItemId
	itemData.ProductId = item.ProductId
	itemData.ProductCode = item.ProductCode
	itemData.ProductName = item.ProductName
	itemData.CustomerId = item.CustomerId
	itemData.ProductColorId = item.ProductColorId
	itemData.ProductColorCode = item.ProductColorCode
	itemData.ProductColorName = item.ProductColorName
	itemData.DyeFactoryColorCode = item.DyeFactoryColorCode
	itemData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
	itemData.ProductWidth = item.ProductWidth
	itemData.ProductGramWeight = item.ProductGramWeight
	itemData.ProductLevelId = item.ProductLevelId
	itemData.ProductRemark = item.ProductRemark
	itemData.ProductCraft = item.ProductCraft
	itemData.ProductIngredient = item.ProductIngredient
	itemData.InRoll = item.InRoll
	itemData.InWeight = item.InWeight
	itemData.WeightError = item.WeightError
	itemData.PaperTubeWeight = item.PaperTubeWeight
	itemData.SettleWeight = item.SettleWeight
	itemData.ActuallyWeight = item.ActuallyWeight
	itemData.SettleErrorWeight = item.SettleErrorWeight
	itemData.UnitId = item.UnitId
	itemData.InLength = item.InLength
	itemData.Remark = item.Remark
	itemData.ReturnRoll = item.ReturnRoll
	itemData.ReturnWeight = item.ReturnWeight
	itemData.ReturnLength = item.ReturnLength

	// 转义
	itemData.UnitName = unitName
	if val, ok := customerMap[item.CustomerId]; ok {
		itemData.CustomerName = val
	}
	itemData.ProductLevelName = levelName
	itemData.ProductColorName = getColor.ProductColorName
	// 出仓信息
	// processOutItemData, _ := mysql.MustFirstFpmProcessOutOrderItemByID(r.tx, src.QuoteOrderItemId)
	// dst.OutRoll = processOutItemData.OutRoll
	// dst.OutLength = processOutItemData.OutLength
	// dst.OutWeight = processOutItemData.TotalWeight
	// 未退信息
	// dst.NoReturnRoll = processOutItemData.OutRoll
	// dst.NoReturnLength = processOutItemData.OutLength
	// dst.NoReturnWeight = processOutItemData.TotalWeight
}

func (r *FpmSaleReturnInOrderRepo) swapFcModel2Data(fineCode model.FpmInOrderItemFc, fineCodeGetData *structure.GetFpmInOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, fineCode.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, fineCode.UnitId)

	fineCodeGetData.Id = fineCode.Id
	fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
	fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
	fineCodeGetData.CreatorId = fineCode.CreatorId
	fineCodeGetData.CreatorName = fineCode.CreatorName
	fineCodeGetData.UpdaterId = fineCode.UpdaterId
	fineCodeGetData.UpdateUserName = fineCode.UpdaterName
	fineCodeGetData.ParentId = fineCode.ParentId
	fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
	fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
	fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
	fineCodeGetData.Roll = fineCode.Roll
	fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
	fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
	fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
	fineCodeGetData.WeightError = fineCode.WeightError
	fineCodeGetData.UnitId = fineCode.UnitId
	fineCodeGetData.StockId = fineCode.StockId
	fineCodeGetData.SumStockId = fineCode.SumStockId
	fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
	fineCodeGetData.Length = fineCode.Length
	fineCodeGetData.Remark = fineCode.Remark
	fineCodeGetData.InternalRemark = fineCode.InternalRemark
	fineCodeGetData.DigitalCode = fineCode.DigitalCode
	fineCodeGetData.ShelfNo = fineCode.ShelfNo
	fineCodeGetData.AccountNum = fineCode.AccountNum
	fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
	fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
	fineCodeGetData.ProductWidth = fineCode.ProductWidth
	fineCodeGetData.ProductGramWeight = fineCode.ProductGramWeight
	fineCodeGetData.ContractNumber = fineCode.ContractNumber
	fineCodeGetData.ScanUserId = fineCode.ScanUserId
	fineCodeGetData.ScanUserName = fineCode.ScanUserName
	fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
	fineCodeGetData.StockRemark = fineCode.StockRemark
	fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
	fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
	fineCodeGetData.SettleWeight = fineCode.SettleWeight

	// 转义
	fineCodeGetData.WarehouseBinName = binName
	fineCodeGetData.UnitName = unitName
	fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
}

func (r *FpmSaleReturnInOrderRepo) judgeAuditPass(id uint64, order model.FpmInOrder, ctx context.Context) (
	err error, addItems structure.AddStockProductDetailParamList,
	updateItems structure.UpdateStockProductDetailParamList) {
	var (
		addWeight            = make([]*structure.AddStockProductDetailParam, 0)
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		itemList             model.FpmInOrderItemList
		fineCodeList         model.FpmInOrderItemFcList
	)
	// 判断成品数量是否符合

	swap2StockFieldParam.WarehouseInType = cus_const.WarehouseGoodInTypeSaleReturn
	swap2StockFieldParam.WarehouseInOrderNo = order.OrderNo
	swap2StockFieldParam.WarehouseInOrderId = order.Id
	swap2StockFieldParam.WarehouseId = order.WarehouseId

	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemList.GetIds())
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	for _, item := range itemList {
		_fineCodeList := fineCodeList.PickFcListByParentId(item.Id)
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		totalRoll := 0
		totalLength := 0

		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		swap2StockFieldParam.FinishProductWidthUnitId = item.FinishProductWidthUnitId
		swap2StockFieldParam.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId

		if item.InRoll == 0 && item.InWeight == 0 {
			err = middleware.ErrorLog(cus_error.NewError(cus_error.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(_fineCodeList) == 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
			return
		}

		for _, fineCode := range _fineCodeList {
			totalRoll = totalRoll + fineCode.Roll
			totalLength += fineCode.Length
			// 把销售的退回仓库,汇总重新绑定
			if fineCode.ReturnStockId != 0 {
				_updateWeight := fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam, false)
				_updateWeight.Id = fineCode.ReturnStockId
				_updateWeight.StockProductId = 0
				_updateWeight.Type = 6
				updateWeight = append(updateWeight, _updateWeight)
				continue
			}
			addWeight = append(addWeight, fineCode.ToAddStockProductDetailParam(ctx, swap2StockFieldParam))
		}

		if totalRoll != item.InRoll {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.InLength > 0 && totalLength > 0 && item.InLength != totalLength {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}
	addItems = addWeight
	updateItems = updateWeight
	return
}

func (r *FpmSaleReturnInOrderRepo) judgeAuditWait(id uint64, fpmSaleReturnInOrder model.FpmInOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		updateWeight = make([]*structure.UpdateStockProductDetailParam, 0)
		itemList     model.FpmInOrderItemList
		fineCodeList model.FpmInOrderItemFcList
	)
	// 判断成品数量是否符合
	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemList.GetIds())
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	for _, item := range itemList {
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam := structure.Swap2StockFieldParam{}
		swap2StockFieldParam.WarehouseInType = cus_const.WarehouseGoodInTypeSaleReturn
		swap2StockFieldParam.WarehouseInOrderNo = fpmSaleReturnInOrder.OrderNo
		swap2StockFieldParam.WarehouseInOrderId = fpmSaleReturnInOrder.Id
		swap2StockFieldParam.WarehouseId = fpmSaleReturnInOrder.WarehouseId
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.FinishProductWidthUnitId = item.FinishProductWidthUnitId
		swap2StockFieldParam.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
		for _, fineCode := range fineCodeList.PickFcListByParentId(item.Id) {
			_updateWeight := fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam, true)
			_updateWeight.Type = 6
			if fineCode.ReturnStockId != 0 {
				_updateWeight.Id = fineCode.ReturnStockId
				updateWeight = append(updateWeight, _updateWeight)
				continue
			}
			// 需要修改细码信息，所以IsToWait为false
			// 新增的细码，直接把记录删除掉
			if fineCode.IsUpdateStock {
				_updateWeight.IsToWait = false
			} else {
				_updateWeight.IsToWait = true
			}
			updateWeight = append(updateWeight, _updateWeight)
			// updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParamBack(ctx))
		}
	}
	updateItems = updateWeight
	return
}

// 进仓成品信息枚举列表
func (r *FpmSaleReturnInOrderRepo) ItemEnumList(
	ctx context.Context, req *structure.GetFpmInOrderItemListQuery) (
	itemList structure.GetFpmSaleReturnInOrderItemDataList, count int, err error) {
	var (
		itemModelList model.FpmInOrderItemList
		itemDatas     model.FpmInOrderItemList
		bizService    = biz_pb.NewClientBizUnitService()
		pLevelPB      = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB        = base_info_pb.NewInfoBaseMeasurementUnitClient()
		baseProductPB = base_product_pb.NewProductColorClient()
		dicSvc        = dictionary.NewDictionaryClient()
	)
	itemDatas, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.ParentId)
	if err != nil {
		return
	}

	// 获取已审核的销售进仓单
	orderList, _, err := mysql.FindPassFpmSaleReturnInOrder(r.tx, req)
	if err != nil {
		return
	}
	req.OrderPassIds = orderList.GetIds()
	req.UseByEnum = true
	itemModelList, count, err = mysql.SearchFpmInOrderItem(r.tx, req)
	if err != nil {
		return
	}

	var (
		quoteOrderItemIds = set.NewUint64Set()
	)
	for _, itemData := range itemDatas {
		quoteOrderItemIds.Add(itemData.QuoteOrderItemId)
	}

	bizIds := mysql_base.GetUInt64List(itemModelList, "biz_unit_id")
	levelIds := mysql_base.GetUInt64List(itemModelList, "product_level_id")
	mUnitIds := mysql_base.GetUInt64List(itemModelList, "measurement_unit_id")
	colorIds := mysql_base.GetUInt64List(itemModelList, "product_color_id")
	dicIds := mysql_base.GetUInt64List(itemModelList, "dictionary_detail_id")
	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, bizIds)
	levelNameMap, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds)
	unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitIds)
	ColorMap, _ := baseProductPB.GetProductColorItemByIds(ctx, colorIds)
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	for _, item := range itemModelList {
		itemData := structure.GetFpmSaleReturnInOrderItemData{}
		itemData.Id = item.Id
		itemData.CreateTime = tools.MyTime(item.CreateTime)
		itemData.UpdateTime = tools.MyTime(item.UpdateTime)
		itemData.CreatorId = item.CreatorId
		itemData.CreatorName = item.CreatorName
		itemData.UpdaterId = item.UpdaterId
		itemData.UpdateUserName = item.UpdaterName
		itemData.ParentId = item.ParentId
		itemData.SumStockId = item.SumStockId
		itemData.ParentOrderNo = item.ParentOrderNo
		itemData.QuoteOrderNo = item.QuoteOrderNo
		itemData.QuoteOrderItemId = item.QuoteOrderItemId
		itemData.ProductId = item.ProductId
		itemData.ProductCode = item.ProductCode
		itemData.ProductName = item.ProductName
		itemData.CustomerId = item.CustomerId
		itemData.ProductColorId = item.ProductColorId
		itemData.ProductColorCode = item.ProductColorCode
		itemData.ProductColorName = item.ProductColorName
		itemData.DyeFactoryColorCode = item.DyeFactoryColorCode
		itemData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		itemData.ProductLevelId = item.ProductLevelId
		itemData.ProductRemark = item.ProductRemark
		itemData.ProductCraft = item.ProductCraft
		itemData.ProductIngredient = item.ProductIngredient
		itemData.InRoll = item.InRoll
		itemData.InWeight = item.InWeight
		itemData.WeightError = item.WeightError
		itemData.PaperTubeWeight = item.PaperTubeWeight
		itemData.SettleWeight = item.SettleWeight
		itemData.ActuallyWeight = item.ActuallyWeight
		itemData.SettleErrorWeight = item.SettleErrorWeight
		itemData.InLength = item.InLength
		itemData.Remark = item.Remark
		itemData.ReturnRoll = item.ReturnRoll
		itemData.ReturnWeight = item.ReturnWeight
		itemData.ReturnLength = item.ReturnLength
		itemData.ReturnPrice = item.ReturnPrice
		itemData.LengthCutReturnPrice = item.LengthCutReturnPrice
		itemData.UnitId = item.UnitId
		itemData.AuxiliaryUnitId = item.AuxiliaryUnitId

		itemData.SettlePrice = item.SettlePrice
		// 转义
		itemData.UnitName = unitNameMap[item.UnitId]
		if val, ok := customerMap[item.CustomerId]; ok {
			itemData.CustomerName = val
		}
		itemData.AuxiliaryUnitName = unitNameMap[item.AuxiliaryUnitId]
		if val, ok := customerMap[item.CustomerId]; ok {
			itemData.CustomerName = val
		}
		itemData.ProductLevelName = levelNameMap[item.ProductLevelId]
		itemData.ProductColorName = ColorMap[item.ProductColorId][1]
		itemData.BuildFPResp(item.ProductWidth, item.ProductGramWeight, dicNameMap[item.FinishProductWidthUnitId][1],
			dicNameMap[item.FinishProductGramWeightUnitId][1], item.FinishProductWidthUnitId, item.FinishProductGramWeightUnitId)

		// r.swapItemModel2Data(item, &itemData, ctx)
		itemList = append(itemList, itemData)
	}
	return
}

func (r *FpmSaleReturnInOrderRepo) GetIDsBySrcIDs(ctx context.Context, srcIDs []uint64) (ids []uint64, err error) {
	var (
		orders model.FpmInOrderList
	)
	ids = make([]uint64, 0)
	orders, err = mysql.FindFpmSaleReturnInOrderBySrcOrderIDs(r.tx, srcIDs)
	if err != nil {
		return
	}

	for _, order := range orders {
		ids = append(ids, order.Id)
	}
	return
}
