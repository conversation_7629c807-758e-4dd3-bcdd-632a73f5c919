package product

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	common "hcscm/common/payable"
	cus_const "hcscm/common/product"
	system_consts "hcscm/common/system_consts"
	"hcscm/extern/pb/payable"
	purchase_pb "hcscm/extern/pb/purchase"
	salePb "hcscm/extern/pb/sale"
	saleSys "hcscm/extern/pb/sale_system"
	"hcscm/model/mysql/mysql_base"
	mysqlSystem "hcscm/model/mysql/system"
	"hcscm/model/redis"
	"hcscm/server/system"
	svc "hcscm/service/product"
	sys_structure "hcscm/structure/payable2"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/vars"
	"log"
	"time"
)

// @Tags		【成品采购进仓】
// @Summary	添加成品采购进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.AddFpmInOrderData{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/addFpmPrcInOrder [post]
// @Tags		【成品采购进仓】
// @Summary	添加成品采购进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.AddFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/addFpmPrcInOrder [post]
func AddFpmPrcInOrder(c *gin.Context) {
	var (
		q            = &structure.AddFpmInOrderParam{}
		data         = structure.AddFpmInOrderData{}
		svc          = svc.NewFpmInOrderService()
		err          error
		orderPrefix  mysqlSystem.OrderPrefix
		exist        bool
		sale_sys_svc = saleSys.NewSaleSystemClient()
		saleSysData  = saleSys.Res{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	if err = q.CheckVNumber(); err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypePurchase
	// product receive in
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	// product receive in
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: q.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(tx)
	if err != nil {
		return
	}
	if !exist {
		q.OrderNoPre = vars.FpmPurchaseInOrderPrefix
		if vars.UseSaleSystem {
			q.OrderNoPre = fmt.Sprintf("%s-%s-", q.OrderNoPre, saleSysData.Code)
		}
	} else {
		q.OrderNoPre = orderPrefix.FpmPurchaseInOrder
		if orderPrefix.UseSaleSystem {
			q.OrderNoPre = fmt.Sprintf("%s-%s-", q.OrderNoPre, saleSysData.Code)
		}
	}
	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/updateFpmPrcInOrder [put]
// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrder [put]
func UpdateFpmPrcInOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderParam{}
		data = structure.UpdateFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	if err = q.CheckVNumber(); err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypePurchase
	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderBusinessCloseParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderBusinessClose [put]
func UpdateFpmPrcInOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderBusinessCloseParam{}
		data = structure.UpdateFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusWait [put]
// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusWait [put]
func UpdateFpmPrcInOrderStatusWait(c *gin.Context) {
	var (
		q                    = &structure.UpdateFpmInOrderStatusParam{}
		data                 = structure.UpdateFpmInOrderStatusData{}
		inOrderSvc           = svc.NewFpmInOrderService()
		stockSvc             = svc.NewStockProductService()
		err                  error
		rLocks               = make(redis.LockForRedisList, 0)
		updateItems          structure.UpdateStockProductDetailParamList
		salePlanOrderItemIds []uint64

		// paymentSvc = svc.NewPaymentService() // 支付服务
		// usageSvc   = svc.NewUsageService()   // 库存流水使用情况服务
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	// 作废采购应付单 流程应该从底层的单据开始操作
	err = voidPayableProductPur(ctx, tx, q.Id.ToUint64())
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		var (
			backWriteList = make([]purchase_pb.BackWriteItem, 0)
		)
		data, updateItems, salePlanOrderItemIds, err = inOrderSvc.UpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}

		// // 校验是否已付款给供应商
		// hasPaid, err = paymentSvc.HasPaid(ctx, id)
		// if err != nil {
		// 	err = errors.New("检查付款状态失败")
		// 	return
		// }
		// if hasPaid {
		// 	err = errors.New("已付款给供应商，请先取消付款订单")
		// 	return
		// }

		// // 校验库存流水是否被使用过
		// isUsed, err := usageSvc.IsStockUsed(ctx, id)
		// if err != nil {
		// 	err = errors.New("检查库存流水使用情况失败")
		// 	return
		// }
		// if isUsed {
		// 	err = errors.New("库存流水明细已被使用，请撤回对应的单据后再操作。")
		// 	return
		// }

		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}

		orderData := structure.GetFpmInOrderData{}
		orderData, err = inOrderSvc.GetByTx(ctx, tx, id)
		if err != nil {
			return
		}
		for i := range orderData.ItemData {
			backWriteList = append(backWriteList, purchase_pb.BackWriteItem{
				Id:       orderData.ItemData[i].QuoteOrderItemId,
				IsAdd:    false,
				InRoll:   orderData.ItemData[i].InRoll,
				InWeight: orderData.ItemData[i].TotalWeight,
				InLength: orderData.ItemData[i].InLength,
			})
		}
		err = purchase_pb.NewPurchaseProductOrderClient().BackItems(ctx, backWriteList)
		if err != nil {
			return
		}
		if len(salePlanOrderItemIds) > 0 {
			err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, system_consts.SituStatusProductPurchase,
				false, id, "成品采购进仓单消审")
			if err != nil {
				return
			}
		}
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusPass [put]
func MPUpdateFpmPrcInOrderStatusPass(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	data, err = ServiceUpdateFpmPrcInOrderStatusPass(ctx, q.Id.ToUint64(), tx)
	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusPass [put]
func UpdateFpmPrcInOrderStatusPass(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	data, err = ServiceUpdateFpmPrcInOrderStatusPass(ctx, q.Id.ToUint64(), tx)
	return
}

func ServiceUpdateFpmPrcInOrderStatusPass(ctx context.Context, fpmPrcInOrderIDs []uint64, tx *mysql_base.Tx) (data structure.UpdateFpmInOrderStatusData, err error) {
	var (
		inOrderSvc           = svc.NewFpmInOrderService()
		stockSvc             = svc.NewStockProductService()
		ids                  map[uint64]uint64
		sumIds               map[uint64]uint64
		addItems             structure.AddStockProductDetailParamList
		orderNo              = make([]string, 0)
		salePlanOrderItemIds []uint64
		rLocks               = make(redis.LockForRedisList, 0)
	)

	defer func() {
		rLocks.Unlock()
	}()

	for _, id := range fpmPrcInOrderIDs {
		var (
			backWriteList = make([]purchase_pb.BackWriteItem, 0)
		)
		data, addItems, _, salePlanOrderItemIds, err = inOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		orderNo = append(orderNo, data.OrderNo)
		if len(addItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, addItems, nil)
			if err != nil {
				return
			}
			ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addItems)
			if err != nil {
				return
			}

			err = inOrderSvc.UpdateDetailStockDetailId(ctx, tx, ids, sumIds, id, false)
			if err != nil {
				return
			}
		}
		orderData := structure.GetFpmInOrderData{}
		orderData, err = inOrderSvc.GetByTx(ctx, tx, id)
		if err != nil {
			return
		}
		for i := range orderData.ItemData {
			backWriteList = append(backWriteList, purchase_pb.BackWriteItem{
				Id:       orderData.ItemData[i].QuoteOrderItemId,
				IsAdd:    true,
				InRoll:   orderData.ItemData[i].InRoll,
				InWeight: orderData.ItemData[i].TotalWeight,
				InLength: orderData.ItemData[i].InLength,
			})
		}
		// 反写到成品采购单
		err = purchase_pb.NewPurchaseProductOrderClient().BackItems(ctx, backWriteList)
		if err == nil {
			addPayableProductPurOrderByIn(ctx, tx, orderData, vars.IsFPPurPayOrderPushAutoPass)
		}
		// 更新销售计划单的进度状态
		if len(salePlanOrderItemIds) > 0 {
			err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, system_consts.SituStatusProductProcessOrPurchaseReceive,
				true, id, "成品采购进仓单审核")
			if err != nil {
				return
			}
		}
	}
	return
}

// 审核采购进仓单（调货销售单用）
func ServiceUpdateFpmPrcInOrderStatusPassV2(ctx context.Context, rLocks redis.LockForRedisList, fpmPrcInOrderIDs []uint64, tx *mysql_base.Tx, stockSvc *svc.StockProductService) (
	data structure.UpdateFpmInOrderStatusData, locks redis.LockForRedisList, err error) {
	var (
		inOrderSvc        = svc.NewFpmInOrderService()
		orderNo           = make([]string, 0)
		fpmInOrderDataMap = make(map[uint64]structure.GetFpmInOrderData)
	)
	locks = rLocks
	for _, id := range fpmPrcInOrderIDs {
		// 重设
		stockSvc.ResetReq()
		var (
			addItems structure.AddStockProductDetailParamList
			ids      map[uint64]uint64
			sumIds   map[uint64]uint64
		)
		data, addItems, _, _, err = inOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		orderNo = append(orderNo, data.OrderNo)
		// 更新库存
		if len(addItems) != 0 {
			locks, err = stockSvc.StockProductLock(ctx, tx, locks, addItems, nil)
			if err != nil {
				return
			}
			ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addItems)
			if err != nil {
				return
			}

			err = inOrderSvc.UpdateDetailStockDetailId(ctx, tx, ids, sumIds, id, false)
			if err != nil {
				return
			}
		}
		// 重新获取最新的进仓单数据
		orderData := structure.GetFpmInOrderData{}
		orderData, err = inOrderSvc.GetByTx(ctx, tx, id)
		if err != nil {
			return
		}

		fpmInOrderDataMap[id] = orderData

		addPayableProductPurOrderByIn(ctx, tx, orderData, vars.IsFPPurPayOrderPushAutoPass)
	}
	data.FpmInOrderDataMap = fpmInOrderDataMap

	return
}

// 创建成品采购应付单
func addPayableProductPurOrderByIn(ctx context.Context, tx *mysql_base.Tx, order structure.GetFpmInOrderData, isAudit bool) {
	payableService := payable.NewClientPayableService()

	req := &sys_structure.AddPayableProductPurParams{
		PayableBaseInfo: sys_structure.PayableBaseInfo{
			SrcOrderType: common.SrcOrderTypeProductPurInOrder,
			SrcOrderId:   order.Id,
			SrcOrderNo:   order.OrderNo,
			SaleSystemId: order.SaleSystemId,
			SupplierId:   order.BizUnitId,
			PayDate:      tools.QueryTime(time.Now().Format("2006-01-02")),
			Remark:       order.Remark,
			VoucherNum:   order.VoucherNumber,
			SaleMode:     order.SaleMode, // 下推订单类型
		},
		Items: nil,
	}
	for _, item := range order.ItemData {
		if item.AuxiliaryUnitId == 0 {
			item.AuxiliaryUnitId = item.UnitId
		}
		payableFcDataList := make(sys_structure.AddPayableItemFcDataList, 0)
		for _, fc := range item.ItemFCData {
			payableFcDataList = append(payableFcDataList, sys_structure.AddPayableItemFcData{
				WarehouseId:       fc.WarehouseId,
				SrcDetailId:       fc.ParentId,
				SrcDetailFcId:     fc.Id,
				Roll:              fc.Roll,
				WarehouseBinId:    fc.WarehouseBinId,
				VolumeNumber:      fc.VolumeNumber,
				BaseUnitWeight:    fc.BaseUnitWeight,
				WeightError:       fc.WeightError,
				PaperTubeWeight:   fc.PaperTubeWeight,
				ActuallyWeight:    fc.BaseUnitWeight - fc.WeightError,
				SettleErrorWeight: fc.SettleErrorWeight,
				SettleWeight:      fc.BaseUnitWeight - fc.WeightError - fc.SettleErrorWeight,
				Length:            fc.Length,
				UnitId:            fc.UnitId,
				StockId:           fc.StockId,
				SumStockId:        fc.SumStockId,
			})
		}
		req.Items = append(req.Items, &sys_structure.PayableProductPurItem{
			SrcId:                  item.Id,
			Roll:                   item.InRoll,
			Weight:                 item.SettleWeight,
			WeightUnitPrice:        item.UnitPrice,
			Length:                 item.InLength,
			LengthUnitPrice:        item.LengthUnitPrice,
			OtherPrice:             item.OtherPrice,
			SumStockId:             item.SumStockId,
			MaterialId:             item.ProductId,
			ColorId:                item.ProductColorId,
			DyeFactoryColorCode:    item.DyeFactoryColorCode,
			DyelotNumber:           item.DyeFactoryDyelotNumber,
			MeasurementUnitId:      item.UnitId,
			AuxiliaryUnitId:        item.AuxiliaryUnitId,
			Width:                  item.ProductWidth,
			GramWeight:             item.ProductGramWeight,
			WidthUnitId:            item.FinishProductWidthUnitId,
			GramWeightUnitId:       item.FinishProductGramWeightUnitId,
			FcDataList:             payableFcDataList,
			ReceiveWeight:          item.ReceiveWeight,
			PTWeightAndWeightError: item.PTWeightAndWeightError,
			ShouldPayWeight:        item.ShouldPayWeight,
		})
	}

	id, err := payableService.AddPayableProductPur(ctx, tx, req)
	if err != nil {
		log.Default().Println(ctx, "addPayableProductPurOrder err: ", err)
		// 失败继续执行
	}
	// 是否自动审核
	if isAudit {
		tx.SavePoint("auto_audit_order")
		auditReq := &sys_structure.GetPayableOtherRequest{
			Id: id,
		}
		_, _err := payableService.AuditPayableProductPur(ctx, tx, auditReq)
		if _err != nil {
			tx.RollbackTo("auto_audit_order")
		}
	}

}

// 作废应付单
func voidPayableProductPur(ctx context.Context, tx *mysql_base.Tx, orderIds []uint64) (err error) {
	for _, id := range orderIds {
		err = payable.NewClientPayableService().VoidPayableProductPurBySrcOrderId(ctx, tx, id)
		if err != nil {
			return
		}
	}
	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-驳回
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusReject [put]
func UpdateFpmPrcInOrderStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusReject(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusCancel [put]
// @Tags		【成品采购进仓】
// @Summary	更新成品采购进仓状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/updateFpmPrcInOrderStatusCancel [put]
func UpdateFpmPrcInOrderStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusCancel(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	获取成品采购进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization	header		string	true	"token"
// @Success	200	{object}	structure.GetFpmInOrderData{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/getFpmPrcInOrder [get]
// @Tags		【成品采购进仓】
// @Summary	获取成品采购进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/getFpmPrcInOrder [get]
func GetFpmPrcInOrder(c *gin.Context) {
	var (
		q    = &structure.GetFpmInOrderQuery{}
		data = structure.GetFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	获取成品采购进仓列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/getFpmPrcInOrderList [get]
func GetFpmPrcInOrderList(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypePurchase
	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	获取成品采购进仓列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		biz_unit_name	query		string	false	"供方name"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization	header		string	true	"token"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/getMPFpmPrcInOrderList [get]
func GetMPFpmPrcInOrderList(c *gin.Context) {
	var (
		q     = &structure.GetMPFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypePurchase
	list, total, err = svc.GetMPList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	获取成品采购进仓列表(及详情)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		product_code_or_name	query		string	false	"成品编号或名称"
// @Param		product_color_code_or_name	query		string	false	"成品颜色编号或名称"
// @Param		dyelot_number	query		string	false	"缸号"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmPrcInOrder/getFpmPrcInOrderListAndDetails [get]
func GetFpmPrcInOrderListAndDetails(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypePurchase
	list, total, err = svc.GetListAndDetail(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品采购进仓】
// @Summary	获取成品下拉列表(包含细码)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		product_code_or_name	query		string	false	"成品编号或名称"
// @Param		product_color_code_or_name	query		string	false	"成品颜色编号或名称"
// @Param		dyelot_number	query		string	false	"缸号"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization   	header	    string	true	"token"
// @Success	200				{object}	structure.GetFpmInOrderItemDropdownDataList{}
// @Router		/hcscm/mp/v1/product/fpmPrcInOrder/getFpmPrcInOrderDetails [get]
func GetFpmPrcInOrderDetails(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderItemDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypePurchase
	q.AvailableOnly = true
	list, total, err = svc.GetFpmPrcInOrderDetails(ctx, q)
	if err != nil {
		return
	}

	return
}
