package should_collect_order

import (
	common "hcscm/common/should_collect_order"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddShouldCollectOrderDetailParamList []AddShouldCollectOrderDetailParam

// （）内为其他应收账的新增字段
type AddShouldCollectOrderDetailParam struct {
	structure_base.Param
	Id                   uint64          `json:"id"`                      // 应收单详情id
	ShouldCollectOrderId uint64          `json:"should_collect_order_id"` // 应收单id
	VoucherNumber        string          `json:"voucher_number"`          // 凭证单号
	OrderTime            tools.QueryTime `json:"order_time"`              // 单据日期（日期）
	Code                 string          `json:"code"`                    // 物料编号（项目编号）
	Name                 string          `json:"name"`                    // 物料名称（项目名称）
	ProductColorCode     string          `json:"product_color_code"`      // 颜色编号
	ProductColorName     string          `json:"product_color_name"`      // 颜色名称
	DyelotNumber         string          `json:"dyelot_number"`           // 染厂缸号
	Roll                 int             `json:"roll"`                    // 匹数
	Weight               int             `json:"weight"`                  // 数量
	SettleErrorWeight    int             `json:"settle_error_weight"`     // 空差
	SettleWeight         int             `json:"settle_weight"`           // 结算数量（数量）
	SalePrice            int             `json:"sale_price"`              // 单价
	OriginPrice          int             `json:"origin_price"`            // 原金额
	OtherPrice           int             `json:"other_price"`             // 折扣金额（其他应收）
	SettlePrice          int             `json:"settle_price"`            // 应收金额(必填)
	Remark               string          `json:"remark"`                  // 备注
	OrderType            string          `json:"order_type"`              // 单据类型
}

type GetShouldCollectOrderDetailData struct {
	structure_base.RecordData
	ShouldCollectOrderId uint64             `json:"should_collect_order_id"` // 应收单id
	CollectType          common.CollectType `json:"collect_type"`            // 应收单类型
	CollectTypeName      string             `json:"collect_type_name"`       // 应收单类型名称
	VoucherNumber        string             `json:"voucher_number"`          // 凭证单号
	OrderTime            tools.MyTime       `json:"order_time"`              // 单据日期
	Code                 string             `json:"code"`                    // 物料编号
	Name                 string             `json:"name"`                    // 物料名称
	DyelotNumber         string             `json:"dyelot_number"`           // 染厂缸号
	ProductColorCode     string             `json:"product_color_code"`      // 颜色编号
	ProductColorName     string             `json:"product_color_name"`      // 颜色名称
	Roll                 int                `json:"roll"`                    // 匹数
	Weight               int                `json:"weight"`                  // 数量
	SettleErrorWeight    int                `json:"settle_error_weight"`     // 空差
	SettleWeight         int                `json:"settle_weight"`           // 结算数量
	SalePrice            int                `json:"sale_price"`              // 单价
	OriginPrice          int                `json:"origin_price"`            // 原金额
	OtherPrice           int                `json:"other_price"`             // 折扣金额
	SettlePrice          int                `json:"settle_price"`            // 应收金额
	Remark               string             `json:"remark"`                  // 备注
	ActuallyWeight       int                `json:"actually_weight"`         // 码单数量（小程序产品信息里的数量）
	OrderType            string             `json:"order_type"`              // 单据类型
}

type GetShouldCollectOrderDetailDataList []GetShouldCollectOrderDetailData

func (g GetShouldCollectOrderDetailDataList) Adjust() {

}

// GetShouldCollectOrderDetailListQuery 查询应收单详情列表的查询条件
type GetShouldCollectOrderDetailListQuery struct {
	structure_base.ListQuery
	OrderNo        string             `form:"order_no" json:"order_no"`                 // 应收单号
	CustomerId     uint64             `form:"customer_id" json:"customer_id"`           // 客户ID
	SaleSystemId   uint64             `form:"sale_system_id" json:"sale_system_id"`     // 营销体系ID
	SaleUserId     uint64             `form:"sale_user_id" json:"sale_user_id"`         // 销售员ID
	StartOrderTime tools.QueryTime    `form:"start_order_time" json:"start_order_time"` // 开始时间
	EndOrderTime   tools.QueryTime    `form:"end_order_time" json:"end_order_time"`     // 结束时间
	SaleMode       tools.QueryIntList `form:"sale_mode" json:"sale_mode"`               // 销售模式列表
	CollectType    tools.QueryIntList `form:"collect_type" json:"collect_type"`         // 单据类型列表
	AuditStatus    tools.QueryIntList `form:"audit_status" json:"audit_status"`         // 审核状态
}
