// Code generated by "stringer -type=StockShowType --linecomment"; DO NOT EDIT.

package product

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[StockShowTypeAll-1]
	_ = x[StockShowTypeRoll-2]
	_ = x[StockShowTypeWeight-3]
	_ = x[StockShowTypeZero-4]
}

const _StockShowType_name = "全部匹数和数量库存不为0匹数为0数量不为0匹数为0数量为0"

var _StockShowType_index = [...]uint8{0, 6, 34, 57, 77}

func (i StockShowType) String() string {
	i -= 1
	if i < 0 || i >= StockShowType(len(_StockShowType_index)-1) {
		return ""
	}
	return _StockShowType_name[_StockShowType_index[i]:_StockShowType_index[i+1]]
}
