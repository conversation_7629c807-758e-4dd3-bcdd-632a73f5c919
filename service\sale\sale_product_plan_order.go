package sale

import (
	"context"
	aggs "hcscm/aggs/sale"
	common "hcscm/common/sale"
	system_const "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/sale/dao"
	"hcscm/model/redis"
	gfmSvc "hcscm/service/grey_fabric_manage"
	structure_gfm "hcscm/structure/grey_fabric_manage"
	structure "hcscm/structure/sale"
	"hcscm/tools/set"
)

type ISaleProductPlanOrderService interface {
	Add(ctx context.Context, req *structure.AddSaleProductPlanOrderParam) (data structure.AddSaleProductPlanOrderData, err error)
	Update(ctx context.Context, req *structure.UpdateSaleProductPlanOrderParam) (data structure.UpdateSaleProductPlanOrderData, err error)
	AuditUpdateSaleProductPlanOrderGfDetailDetails(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req structure.ProductionPlanOrderModifySalPlanOrderList) (err error)
	AuditUpdateSaleProductPlanOrderProductDetails(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req structure.UpdateSaleProductPlanOrderProductDetailParamList) (err error)
	UpdateBusinessClose(ctx context.Context, req *structure.UpdateSaleProductPlanOrderBusinessCloseParam) (data structure.UpdateSaleProductPlanOrderBusinessCloseData, err error)
	UpdateStatusWait(ctx context.Context, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (data structure.UpdateSaleProductPlanOrderAuditStatusData, err error)
	UpdateStatusCancel(ctx context.Context, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (data structure.UpdateSaleProductPlanOrderAuditStatusData, err error)
	UpdateStatusReject(ctx context.Context, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (data structure.UpdateSaleProductPlanOrderAuditStatusData, err error)
	Delete(ctx context.Context, req *structure.DeleteSaleProductPlanOrderParam) (data structure.DeleteSaleProductPlanOrderData, err error)
	Get(ctx context.Context, req *structure.GetSaleProductPlanOrderQuery) (data structure.GetSaleProductPlanOrderData, err error)
	GetList(ctx context.Context, req *structure.GetSaleProductPlanOrderListQuery) (list structure.GetSaleProductPlanOrderDataList, total int, err error)
	MPGetList(ctx context.Context, req *structure.GetSaleProductPlanOrderListQuery) (list structure.MPGetSaleProductPlanOrderDataList, total int, err error)
	GetListAndDetail(ctx context.Context, req *structure.GetSaleProductPlanOrderProductDetailListQuery) (list structure.GetSaleProductPlanOrderProductDetailDataV2List, total int, err error)
	GetGfDropdownList(ctx context.Context, req *structure.GetSaleProductPlanOrderGfDetailListQuery) (list structure.GetSaleProductPlanOrderGfDetailDropdownDataList, total int, err error)
	GetProductDropdownList(ctx context.Context, req *structure.GetSaleProductPlanOrderProductDetailListQuery) (list structure.GetSaleProductPlanOrderProductDetailDropdownDataList, total int, err error)
	UpdateStatusPass(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (
		data structure.UpdateSaleProductPlanOrderAuditStatusData, addItem structure.AddPmcGreyPlanOrderParamList, err error)
	Push(ctx context.Context, tx *mysql_base.Tx, req *structure.PushSalePlanOrderParam) (planType common.PlanType, orderIds []uint64, ok bool, err error)
	UpdatePushStatus(ctx context.Context, req *structure.UdpatePushStatusParam) (err error)
	UpdateVocherUrl(ctx context.Context, req *structure.UpdateVocherUrlParam) (err error)
	UpdateSituStatus(ctx context.Context, tx *mysql_base.Tx, ids []uint64, status system_const.SituStatus, isPass bool, orderId uint64, reason string) (err error)
	UpdatePlanPush(ctx context.Context, tx *mysql_base.Tx, outMap map[uint64][2]int, isPass bool) (err error)
}

func NewSaleProductPlanOrderService(ctx context.Context, isCache bool) ISaleProductPlanOrderService {
	return &SaleProductPlanOrderService{
		isCache: isCache,
	}
}

type SaleProductPlanOrderService struct {
	isCache bool
}

func (u SaleProductPlanOrderService) Add(ctx context.Context, req *structure.AddSaleProductPlanOrderParam) (data structure.AddSaleProductPlanOrderData, err error) {
	var (
		id      uint64
		itemIds []uint64
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	id, err = repo.Add(ctx, req)
	if err != nil {
		return data, err
	}
	itemIds, err = repo.AddSaleProductPlanOrderProductDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	err = repo.AddSaleProductPlanOrderGfDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	err = u.UpdateSituStatus(ctx, tx, itemIds, system_const.SituStatusPlaning, true, id, "销售计划单创建")
	if err != nil {
		return
	}
	data.Id = id
	return
}

func (u SaleProductPlanOrderService) Update(ctx context.Context, req *structure.UpdateSaleProductPlanOrderParam) (data structure.UpdateSaleProductPlanOrderData, err error) {
	var (
		id      uint64
		itemIds []uint64
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	id, err = repo.Update(ctx, req)
	if err != nil {
		return data, err
	}
	itemIds, err = repo.UpdateSaleProductPlanOrderProductDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	err = repo.UpdateSaleProductPlanOrderGfDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	err = u.UpdateSituStatus(ctx, tx, itemIds, system_const.SituStatusPlaning, true, id, "销售计划单创建")
	if err != nil {
		return
	}
	data.Id = id
	return
}

// 坯布信息被反写逻辑
func (u SaleProductPlanOrderService) AuditUpdateSaleProductPlanOrderGfDetailDetails(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req structure.ProductionPlanOrderModifySalPlanOrderList) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	err = repo.AuditUpdateSaleProductPlanOrderGfDetailDetails(ctx, rLocks, req)
	if err != nil {
		return
	}
	return
}

// 成品信息已排染匹数数量被反写逻辑
func (u SaleProductPlanOrderService) AuditUpdateSaleProductPlanOrderProductDetails(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req structure.UpdateSaleProductPlanOrderProductDetailParamList) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	err = repo.AuditUpdateSaleProductPlanOrderProductDetails(ctx, rLocks, req)
	if err != nil {
		return
	}
	return
}

func (u SaleProductPlanOrderService) UpdateBusinessClose(ctx context.Context, req *structure.UpdateSaleProductPlanOrderBusinessCloseParam) (data structure.UpdateSaleProductPlanOrderBusinessCloseData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, err = repo.UpdateBusinessClose(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductPlanOrderService) UpdateStatusPass(
	ctx context.Context,
	tx *mysql_base.Tx,
	req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (
	data structure.UpdateSaleProductPlanOrderAuditStatusData,
	addItem structure.AddPmcGreyPlanOrderParamList,
	err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, addItem, err = repo.UpdateStatusPass(ctx, req)
	if err != nil {
		return
	}
	return
}

func (u SaleProductPlanOrderService) UpdateStatusWait(ctx context.Context, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (data structure.UpdateSaleProductPlanOrderAuditStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, err = repo.UpdateStatusWait(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductPlanOrderService) UpdateStatusCancel(ctx context.Context, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (data structure.UpdateSaleProductPlanOrderAuditStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, err = repo.UpdateStatusCancel(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductPlanOrderService) UpdateStatusReject(ctx context.Context, req *structure.UpdateSaleProductPlanOrderAuditStatusParam) (data structure.UpdateSaleProductPlanOrderAuditStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, err = repo.UpdateStatusReject(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductPlanOrderService) Delete(ctx context.Context, req *structure.DeleteSaleProductPlanOrderParam) (data structure.DeleteSaleProductPlanOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, err = repo.Delete(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductPlanOrderService) Get(ctx context.Context, req *structure.GetSaleProductPlanOrderQuery) (data structure.GetSaleProductPlanOrderData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	data, err = repo.Get(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductPlanOrderService) GetList(ctx context.Context, req *structure.GetSaleProductPlanOrderListQuery) (list structure.GetSaleProductPlanOrderDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u SaleProductPlanOrderService) MPGetList(ctx context.Context, req *structure.GetSaleProductPlanOrderListQuery) (list structure.MPGetSaleProductPlanOrderDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.MPGetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u SaleProductPlanOrderService) GetListAndDetail(ctx context.Context, req *structure.GetSaleProductPlanOrderProductDetailListQuery) (list structure.GetSaleProductPlanOrderProductDetailDataV2List, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetListAndDetail(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u SaleProductPlanOrderService) GetGfDropdownList(ctx context.Context, req *structure.GetSaleProductPlanOrderGfDetailListQuery) (list structure.GetSaleProductPlanOrderGfDetailDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetGfDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}
func (u SaleProductPlanOrderService) GetProductDropdownList(ctx context.Context, req *structure.GetSaleProductPlanOrderProductDetailListQuery) (list structure.GetSaleProductPlanOrderProductDetailDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetProductDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u SaleProductPlanOrderService) Push(ctx context.Context, tx *mysql_base.Tx, req *structure.PushSalePlanOrderParam) (planType common.PlanType, orderIds []uint64, ok bool, err error) {

	var (
		repo      = aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
		saleRepo  = aggs.NewSaleProductOrderRepo(ctx, tx, false)
		_orderIds = make([]uint64, 0)
		data      structure.GetSaleProductPlanOrderData
	)
	data, err = repo.Get(ctx, &structure.GetSaleProductPlanOrderQuery{Id: req.OrderId})
	if err != nil {
		return
	}
	var orderId uint64
	if data.PlanType == common.PlanTypeProduct {
		var saleOrder = structure.AddSaleProductOrderParam{}
		// 下推转换 更新可下推
		saleOrder, ok, err = repo.Push(ctx, req)
		if err != nil {
			return
		}

		// 调新增 // 下推成品销售单
		if len(saleOrder.ItemData) > 0 {
			orderId, _, err = saleRepo.Add(ctx, &saleOrder)
			if err != nil {
				return
			}
			err = saleRepo.AddSaleProductOrderDetailDetails(ctx, &saleOrder, orderId)
			if err != nil {
				return
			}
			_orderIds = append(_orderIds, orderId)
		}
	}
	if data.PlanType == common.PlanTypeGreyFabric {
		var (
			saleDeliveryOrderSvc = gfmSvc.NewGfmSaleDeliveryOrderService()
			gfmSaleDeliveryOrder = &structure_gfm.AddGfmSaleDeliveryOrderParam{}
		)
		gfmSaleDeliveryOrder, ok, err = repo.PushGfmSaleDeliveryOrder(ctx, req)
		if err != nil {
			return
		}
		orderId, err = saleDeliveryOrderSvc.Add(ctx, tx, gfmSaleDeliveryOrder)
		_orderIds = append(_orderIds, orderId)
	}
	// todo:原料销售下推逻辑
	if data.PlanType == common.PlanTypeRawMaterial {

	}

	planType = data.PlanType
	orderIds = _orderIds
	return
}

func (u SaleProductPlanOrderService) UpdatePushStatus(ctx context.Context, req *structure.UdpatePushStatusParam) (
	err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		repo = aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	)

	// 下推转换 更新可下推
	err = repo.UpdatePushStatus(ctx, req)
	if err != nil {
		return
	}
	return
}

func (u SaleProductPlanOrderService) UpdateVocherUrl(ctx context.Context, req *structure.UpdateVocherUrlParam) (
	err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		repo = aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	)

	// 下推转换 更新可下推
	err = repo.UpdateVocherUrl(ctx, req)
	if err != nil {
		return
	}
	return
}

func (u SaleProductPlanOrderService) UpdateSituStatus(
	ctx context.Context, tx *mysql_base.Tx, ids []uint64, status system_const.SituStatus, isPass bool, orderId uint64, reason string) (
	err error) {
	var (
		req = &structure.UpdateSaleProductPlanOrderProductDetailSituStatusParam{}
	)
	tx, commit := mysql_base.TransactionMainEx(tx, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		repo = aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	)

	req.Ids = ids
	req.SituStatus = status
	req.IsPass = isPass
	req.OrderId = orderId
	req.Reason = reason
	// 下推转换 更新可下推
	err = repo.UpdateSituStatus(ctx, req)
	if err != nil {
		return
	}
	return
}

func (u SaleProductPlanOrderService) UpdatePlanPush(ctx context.Context, tx *mysql_base.Tx, outMap map[uint64][2]int, isPass bool) (err error) {
	var (
		planItemIds = set.NewUint64Set()
		pushMap     = make(map[uint64]structure.PushSalePlanOrderDetailParam)
		dao         = mysql.NewSaleProductPlanOrderProductDetailDao(ctx, false)
	)
	for k := range outMap {
		pushData := structure.PushSalePlanOrderDetailParam{}
		planItemIds.Add(k)
		if val, ok := pushMap[k]; ok {
			pushData = val
		}
		pushData.PushedRoll += outMap[k][0]
		pushData.PushedWeight += outMap[k][1]
		pushMap[k] = pushData
	}

	// 销售计划
	if planItemIds.Size() > 0 {
		planItemList, _err := dao.FindByIDs(tx, planItemIds.List())
		if _err != nil {
			err = _err
			return
		}

		for _, detail := range planItemList {
			if isPass {
				detail.AddPush(ctx, pushMap[detail.Id])
			}
			if !isPass {
				detail.SubPush(ctx, pushMap[detail.Id])
			}
			detail, err = dao.MustUpdate(tx, detail)
			if _err != nil {
				return
			}
		}
	}
	return
}

//
// // 审核
// func (u SaleProductPlanOrderService) Audit(
// 	ctx context.Context,
// 	tx *mysql_base.Tx,
// 	req *structure.UpdateSaleProductPlanChangeOrderAuditStatusParam,
// 	// req *structure.UpdateProductionNotifyOrderStatusParam,
// ) (
// 	updateItems Sal
// 	updatePmcPushReq []*salePb.AuditUpdatePushed,
// 	salePlanOrderItemIds []uint64,
// 	err error,
// ) {
// 	repo := aggs.NewProductionNotifyOrderRepo(tx)
// 	updateItems, updatePmcPushReq, salePlanOrderItemIds, err = repo.Audit(ctx, req)
// 	if err != nil {
// 		return
// 	}
// 	return
// }
