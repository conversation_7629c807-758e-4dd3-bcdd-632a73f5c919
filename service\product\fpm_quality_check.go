package product

import (
	"context"
	aggs "hcscm/aggs/product"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"time"
)

func NewFpmQualityCheckService() *FpmQualityCheckService {
	return &FpmQualityCheckService{}
}

type FpmQualityCheckService struct {
}

func (u FpmQualityCheckService) Add(tx *mysql_base.Tx, ctx context.Context, req *structure.AddFpmQualityCheckParam) (data structure.AddFpmQualityCheckData, err error) {
	var (
		fpmQualityCheck       model.FpmQualityCheck
		beforeFpmQualityCheck structure.GetFpmQualityCheckData
		exist                 bool
		info                  = metadata.GetLoginInfo(ctx)
	)
	repo := aggs.NewFpmQualityCheckRepo(tx)
	defectRepo := aggs.NewFpmQualityCheckDefectRepo(tx)
	if req.QualityCheckDate.IsYMDZero() {
		req.QualityCheckDate = tools.QueryTime(time.Now().Format("2006-01-02"))
	}
	// 判断是否已经存在同一天,同一个人的数据
	fpmQualityCheck, exist, err = repo.JudgeExitQC(ctx, req.StockId, req.QualityCheckDate, info.GetUserId())
	if err != nil {
		return
	}
	// 不是当天修改则创建一条新的质检记录，其他信息保留，只修改疵点信息
	if !exist {
		if len(req.DefectItem) != 0 && req.DefectItem[0].Pid != 0 {
			beforeFpmQualityCheck, err = repo.Get(ctx, &structure.GetFpmQualityCheckQuery{Id: req.DefectItem[0].Pid})
			if err != nil {
				return
			}
			fpmQualityCheck.Id = beforeFpmQualityCheck.Id
			fpmQualityCheck.BarCode = beforeFpmQualityCheck.BarCode
			data.IsActAdd = true

		} else {
			// 不存在且疵点信息为空时新建，传入基础信息
			data, err = repo.Add(ctx, req)
			if err != nil {
				return data, err
			}
			fpmQualityCheck.Id = data.Id
			fpmQualityCheck.BarCode = req.BarCode
		}
		// data.Id = fpmQualityCheck.Id
	}
	data.Id = fpmQualityCheck.Id

	// 当天修改判断是否修改成品质检信息,修改疵点信息时没有以下信息，修改布匹质检信息时需要全部带入
	if exist && req.NeedUpdate() {
		_, err = repo.Update(ctx, &structure.UpdateFpmQualityCheckParam{
			Id:             fpmQualityCheck.Id,
			DefectWeight:   req.DefectWeight,
			Remark:         req.Remark,
			ActuallyWeight: req.ActuallyWeight,
			EdgeWidth:      req.EdgeWidth,
			UsefulWidth:    req.UsefulWidth,
			QcGramWeight:   req.QcGramWeight,
		})
	}
	// 只更新布匹质检信息
	if len(req.DefectItem) == 0 {
		return
	}
	//
	for _, item := range req.DefectItem {
		if item.Pid != 0 {
			r := structure.UpdateFpmQualityCheckDefectParam{}
			r.Id = item.Id
			r.DefectName = item.DefectName
			r.MeasurementUnitId = item.MeasurementUnitId
			r.DefectPosition = item.DefectPosition
			r.DefectCount = item.DefectCount
			r.Score = item.Score
			_, err = defectRepo.Update(ctx, &r)
			if err != nil {
				return
			}
			continue
		}
		itemData := structure.AddFpmQualityCheckDefectData{}
		item.Pid = fpmQualityCheck.Id
		item.BarCode = fpmQualityCheck.BarCode
		itemData, err = defectRepo.Add(ctx, &item)
		if err != nil {
			return
		}
		err = repo.UpdateUpdateTime(ctx, itemData.Pid)
		if err != nil {
			return
		}
	}
	return
}

func (u FpmQualityCheckService) Update(ctx context.Context, req *structure.UpdateFpmQualityCheckParam) (data structure.UpdateFpmQualityCheckData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmQualityCheckRepo(tx)
	data, err = repo.Update(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmQualityCheckService) Delete(ctx context.Context, req *structure.DeleteFpmQualityCheckParam) (data structure.DeleteFpmQualityCheckData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmQualityCheckRepo(tx)
	data, err = repo.Delete(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FpmQualityCheckService) Get(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.GetFpmQualityCheckData, err error) {
	var (
		detailList structure.GetFpmQualityCheckDefectDataList
		tx         = mysql_base.TransactionSlaveEx(nil, ctx, true)
		repo       = aggs.NewFpmQualityCheckRepo(tx)
		repoDetail = aggs.NewFpmQualityCheckDefectRepo(tx)
	)
	data, err = repo.Get(ctx, req)
	if err != nil {
		return
	}
	detailList, _, err = repoDetail.GetByPid(ctx, &structure.GetFpmQualityCheckDefectListQuery{Pid: req.Id, IsBackPrintField: true})
	if err != nil {
		return
	}
	data.DetailList = detailList
	return
}

func (u FpmQualityCheckService) GetList(ctx context.Context, req *structure.GetFpmQualityCheckListQuery) (list structure.GetFpmQualityCheckDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFpmQualityCheckRepo(tx)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u FpmQualityCheckService) GetForPrint(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.FpmQualityForPrint, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, context.Background(), true)
	repo := aggs.NewFpmQualityCheckRepo(tx)
	return repo.GetForPrint(ctx, req)
}

func (u FpmQualityCheckService) WashDataDyelotNumber(ctx context.Context) error {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	var err error
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFpmQualityCheckRepo(tx)
	return repo.WashDataDyelotNumber(ctx)
}

func (u FpmQualityCheckService) GetByStockId(ctx context.Context, req *structure.GetFpmQualityCheckQuery) (data structure.FpmQualityItemForStockId, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, context.Background(), true)
	repo := aggs.NewFpmQualityCheckRepo(tx)
	return repo.GetByStockId(ctx, req)
}

func (u FpmQualityCheckService) GetListByDyelotNumber(ctx context.Context, req *structure.GetFpmQualityCheckDyelotNumberListQuery) (list structure.GetFpmQualityCheckDyelotNumberDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFpmQualityCheckRepo(tx)
	return repo.GetListByDyelotNumber(ctx, req)
}
