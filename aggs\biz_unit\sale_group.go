package biz_unit

import (
	"context"
	"hcscm/domain/biz_unit/entity"
	"hcscm/domain/biz_unit/iface"
	mysql "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/biz_unit/dao"
	"hcscm/model/mysql/mysql_base"
	dao2 "hcscm/model/mysql/should_collect_order/dao"
	mysql_system "hcscm/model/mysql/system"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

func (r *Repo) CheckSaleGroupNameIsExist(ctx context.Context, do *entity.SaleGroup) (bool, error) {
	po, err := r.dao.QuerySaleGroup(ctx, &dao.QuerySaleGroupParams{
		Name:      do.Name,
		UnequalID: do.Id,
	})
	if err != nil {
		return false, err
	}
	return po != nil, nil
}

func (r *Repo) AddSaleGroup(ctx context.Context, do *entity.SaleGroup) error {
	po := mysql.ToSaleGroupPO(do)
	return r.dao.CreateSaleGroup(ctx, po)
}

func (r *Repo) GetSaleGroup(ctx context.Context, id uint64) (*entity.SaleGroup, error) {
	po, err := r.dao.QuerySaleGroup(ctx, &dao.QuerySaleGroupParams{Id: id})
	if err != nil {
		return nil, err
	}
	return mysql.ToSaleGroupDO(po)
}

func (r *Repo) GetMultiSaleGroup(ctx context.Context, ids []uint64) ([]*entity.SaleGroup, error) {
	p := &dao.QueryMultiSaleGroupParams{IDs: ids}
	po, err := r.dao.QueryMultiSaleGroup(ctx, p)
	if err != nil {
		return nil, err
	}
	return mysql.ToSaleGroupDOs(po)
}

func (r *Repo) UpdateSaleGroup(ctx context.Context, do *entity.SaleGroup) error {
	po := mysql.ToSaleGroupPO(do)
	return r.dao.UpdateSaleGroup(ctx, po)
}

func (r *Repo) UpdateSaleGroupStatus(ctx context.Context, do []*entity.SaleGroup) error {
	m := map[int][]uint64{}
	for _, item := range do {
		_, ok := m[item.Status]
		if !ok {
			m[item.Status] = []uint64{}
		}
		m[item.Status] = append(m[item.Status], item.Id)
	}
	for status, ids := range m {
		if err := r.dao.UpdateSaleGroupStatus(ctx, ids, status); err != nil {
			return err
		}
	}
	return nil
}

func (r *Repo) DeleteSaleGroup(ctx context.Context, do []*entity.SaleGroup) error {
	ids := make([]uint64, 0, len(do))
	for _, item := range do {
		ids = append(ids, item.Id)
	}
	return r.dao.DeleteSaleGroup(ctx, ids)
}

func (r *Repo) QuerySaleGroupList(ctx context.Context, tx *mysql_base.Tx, q *structure_base.GetSaleGroupListParams) (o []*mysql.SaleGroup, count int, err error) {
	var (
		t    mysql.SaleGroup
		cond = mysql_base.NewCondition()
		list []*mysql.SaleGroup
	)
	mysql_system.CommonDataSeparate(ctx, &t, cond)

	if q.Name != "" {
		cond.AddMultiFieldLikeMatch([]string{"name"}, q.Name)
	}
	if q.GetIntCode() > 0 {
		cond.AddEqual("code", q.GetIntCode())
	}
	//cond.AddEqual("status", common.StatusEnable)

	count, err = mysql_base.SearchListGroupForPaging(tx, &t, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (r *Repo) QuerySaleGroup(ctx context.Context, id uint64) (*mysql.SaleGroup, error) {
	return r.dao.QuerySaleGroup(ctx, &dao.QuerySaleGroupParams{Id: id})
}

func (r *Repo) QueryMultiSaleGroup(ctx context.Context, ids []uint64) ([]*mysql.SaleGroup, error) {
	return r.dao.QueryMultiSaleGroup(ctx, &dao.QueryMultiSaleGroupParams{IDs: ids})
}

func (r *Repo) QueryMultiSaleGroupByCodeOrName(ctx context.Context, name []string) ([]*mysql.SaleGroup, error) {
	return r.dao.QueryMultiSaleGroupByCodeOrName(ctx, name)
}

// BizUnitAddSaleGroup 添加客户到销售群体
func (r *Repo) BizUnitAddSaleGroup(ctx context.Context, tx *mysql_base.Tx) (res structure_base.BizUnitAddSaleGroupRes, err error) {
	var (
		result structure_base.BizUnitAddSaleGroupRes
	)
	var repo iface.BizUnitRepoIface = NewRepo(tx)
	// 获取客户列表
	bizUnits, err := repo.GetMultiBizUnits(ctx)
	if err != nil {
		return
	}
	// 获取客户销售表目前已有的客户
	bizUnitSales, err := dao2.SearchBizUnitSales(tx)
	if err != nil {
		return
	}
	bizUnitSaleUnitIds := mysql_base.GetUInt64List(bizUnitSales, "unit_id")
	// 更新客户的销售群体ID
	for _, bizUnit := range bizUnits {
		if tools.ContainsUint64(bizUnitSaleUnitIds, bizUnit.Id) {
			continue
		}
		bizUnit.Sale = &entity.BizUnitSale{
			UnitID: bizUnit.Id,
		}
		bizUnitSale := mysql.ToBizUnitSalePO(bizUnit)
		if err = r.dao.Create(ctx, bizUnitSale); err != nil {
			return
		}
	}
	return result, nil
}
