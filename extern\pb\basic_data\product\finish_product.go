package product

import (
	common_system "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
)

type ProductReq struct {
	Id  uint64
	Ids []uint64

	TypeGreyFabricId   uint64
	TypeGreyFabricCode string // 布种类编号
	TypeGreyFabricName string // 布种类型名
	ProductCode        string // 成品编号
	ProductColorCode   string // 成品颜色编号
	ProductName        string // 成品名
}

type ProductRes struct {
	structure_base.FinishProductWidthAndWightUnit
	Id                        uint64 `json:"product_id" relate:"product_id"`
	FinishProductCode         string // 成品编号
	FinishProductName         string // 成品名称
	FinishProductFullName     string // 成品全称
	TypeGreyFabricId          uint64 `json:"type_grey_fabric_id" relate:"product_kind_id"` // 布种类型id
	TypeGreyFabricCode        string // 布种类编号
	TypeGreyFabricName        string // 布种类型名
	FinishProductIngredient   string // 成品成分
	MeasurementUnitId         uint64 // 计量单位id
	MeasurementUnitName       string // 计量单位名称
	GreyFabricId              uint64 `json:"grey_fabric_id" relate:"grey_fabric_id"` // 坯布信息ID
	GreyFabricCode            string // 坯布编号
	GreyFabricName            string // 坯布名称
	IsColorCard               bool   // 是否启用色卡编号
	WarehouseId               uint64 // 仓库id
	WarehouseName             string // 仓库名称
	StorageArea               string // 存放区域
	DyeFactoryOrderFollowerId uint64 // 染厂跟单用户ID
	Remark                    string // 备注
	// FinishProductWidth        string               // 成品幅宽
	// FinishProductGramWeight   string               // 成品克重
	LengthToWeightRate     int                  // 长度转数量(公斤/米)
	StandardWeight         int                  // 标准数量(公斤)
	PaperTubeWeight        int                  // 纸筒数量(公斤)
	WeightError            int                  // 空差数量(公斤)
	FinishProductCraft     string               // 成品工艺
	Status                 common_system.Status // 状态
	DyeingCraft            string               // 染整工艺
	FinishProductLevelId   uint64               // 成品等级id
	FinishProductLevelCode string               // 成品等级编号
	FinishProductLevelName string               // 成品等级名称
	TouchStyle             string               // 手感风格
	DyeingLoss             int                  // 染损(两位小数)

	FinishProductWidthUnitId        uint64   // 成品幅宽单位id(字典)
	FinishProductWidthUnitName      string   // 成品幅宽单位名称
	FinishProductGramWeightUnitId   uint64   // 成品克重单位id(字典)
	FinishProductGramWeightUnitName string   // 成品克重单位名称
	WeavingOrganizationId           uint64   // 织造组织id
	WeavingOrganizationCode         string   // 织造组织编号
	WeavingOrganizationName         string   // 织造组织名称
	YarnCount                       string   // 纱支
	Density                         string   // 密度
	ShrinkageWarp                   string   // 缩率
	BleachId                        uint64   // 漂染性id
	BleachName                      string   // 漂染性名称
	Center                          string   // 经纬度
	Size                            string   // 尺寸
	TextureURL                      []string // 纹理图片U
	CoverTextureURL                 string   // 封面纹理图
	SupplierId                      []uint64 // 供应商id
	NeedleSize                      string   // 针寸数
}

type ProductResList []ProductRes

func (i ProductResList) PickByProductId(productId uint64) ProductRes {
	for _, product := range i {
		if product.Id == productId {
			return product
		}
	}
	return ProductRes{}
}

func (i ProductResList) PickByProductKindId(productKindId uint64) ProductRes {
	for _, product := range i {
		if product.TypeGreyFabricId == productKindId {
			return product
		}
	}
	return ProductRes{}
}

func (i ProductResList) PickOneByGreyFabricId(greyFabricId uint64) ProductRes {
	for _, product := range i {
		if product.GreyFabricId == greyFabricId {
			return product
		}
	}
	return ProductRes{}
}

func (i ProductResList) PickByCodeOrName(code, name string) ProductRes {
	if code != "" {
		for _, t := range i {
			if t.FinishProductCode == code /*&& t.FinishProductName == name*/ {
				return t
			}
		}
	} else {
		for _, t := range i {
			if t.FinishProductName == name {
				return t
			}
		}
	}
	return ProductRes{}
}

func (i ProductResList) PickByGreyFabricId(greyFabricId uint64) ProductResList {
	var (
		o = make(ProductResList, 0)
	)
	for _, product := range i {
		if product.GreyFabricId == greyFabricId {
			o = append(o, product)
		}
	}
	return o
}
