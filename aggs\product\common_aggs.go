package product

import (
	"context"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	"hcscm/tools"
	"strconv"
	"time"
)

type FpmOrderFcRepo struct {
	tx *mysql_base.Tx
}

func NewFpmOrderFcRepo(tx *mysql_base.Tx) *FpmOrderFcRepo {
	return &FpmOrderFcRepo{tx: tx}
}

func getUnionCode(numberRes int, prefix string) (orderCode string, number int) {
	var (
		appendZero    int
		appendZeroStr string
	)
	date := tools.Time2Date(time.Now())

	number = numberRes
	toStr := strconv.Itoa(number + 1)
	appendZero = 4 - len(toStr)
	if appendZero > 0 {
		for i := 0; i < appendZero; i++ {
			appendZeroStr += "0"
		}
		orderCode = prefix + date + appendZeroStr + toStr
	} else {
		orderCode = prefix + date + toStr
	}
	number = number + 1
	return
}

func (r *FpmOrderFcRepo) UpdateDetailStockDetailId2OrderItemFc(ctx context.Context, ids map[uint64]uint64, sumIds map[uint64]uint64, ppid uint64, isUpdateStock bool) (err error) {
	var (
		fcList   = model.FpmInOrderItemFcList{}
		itemList = model.FpmInOrderItemList{}
	)

	fcList, err = mysql.FindFpmInOrderItemFcByGrandParenTID(r.tx, ppid)
	if err != nil {
		return
	}

	// 存储需要更新的父项ID以及对应的SumStockId
	parentUpdates := make(map[uint64]uint64)

	for _, fc := range fcList {
		if stockDetailId, ok := ids[fc.Id]; ok {
			fc.StockId = stockDetailId
			fc.SumStockId = sumIds[fc.Id]
			fc.IsUpdateStock = isUpdateStock
			fc, err = mysql.MustUpdateFpmInOrderItemFc(r.tx, fc)
			if err != nil {
				return
			}

			// 记录父ID和对应的SumStockId，以便后续更新父项
			if fc.SumStockId > 0 {
				parentUpdates[fc.ParentId] = fc.SumStockId
			}
		}
	}

	// 如果有需要更新的父项
	if len(parentUpdates) > 0 {
		// 获取所有需要更新的父项
		parentIds := make([]uint64, 0, len(parentUpdates))
		for parentId := range parentUpdates {
			parentIds = append(parentIds, parentId)
		}

		// 查询所有需要更新的父项
		itemList, err = mysql.FindFpmInOrderItemByIDs(r.tx, parentIds)
		if err != nil {
			return
		}

		// 更新每个父项的SumStockId
		for _, item := range itemList {
			if sumStockId, ok := parentUpdates[item.Id]; ok {
				item.SumStockId = sumStockId
				_, err = mysql.MustUpdateFpmInOrderItem(r.tx, item)
				if err != nil {
					return
				}
			}
		}
	}

	return
}
