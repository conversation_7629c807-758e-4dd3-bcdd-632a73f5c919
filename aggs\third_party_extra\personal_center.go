package third_party_extra

import (
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/mysql/third_party_extra"
	"hcscm/model/mysql/third_party_extra/dao"
	"hcscm/structure/third_party_extra_structure"
	"time"
)

type PersonalCenterRepo struct {
	tx *mysql_base.Tx
}

func NewPersonalCenterRepo(tx *mysql_base.Tx) *PersonalCenterRepo {
	return &PersonalCenterRepo{
		tx: tx,
	}
}

// GetPersonalCenterByUserId 根据用户ID获取个人中心信息
func (r *PersonalCenterRepo) GetPersonalCenterByUserId(userId uint64) (response third_party_extra_structure.GetPersonalCenterResponse, exist bool, err error) {
	personalCenter, exist, err := dao.GetPersonalCenterByUserId(r.tx, userId)
	if err != nil || !exist {
		return
	}

	response = third_party_extra_structure.GetPersonalCenterResponse{
		Id:          personalCenter.Id,
		UserId:      personalCenter.UserId,
		Nickname:    personalCenter.Nickname,
		Avatar:      personalCenter.Avatar,
		Phone:       personalCenter.Phone,
		Email:       personalCenter.Email,
		Gender:      personalCenter.Gender,
		Birthday:    personalCenter.Birthday,
		Address:     personalCenter.Address,
		Description: personalCenter.Description,
		Status:      personalCenter.Status,
		CreateTime:  personalCenter.CreateTime,
		UpdateTime:  personalCenter.UpdateTime,
	}
	return
}

// CreateOrUpdatePersonalCenter 创建或更新个人中心信息
func (r *PersonalCenterRepo) CreateOrUpdatePersonalCenter(req third_party_extra_structure.UpdatePersonalCenterRequest) (err error) {
	personalCenter, exist, err := dao.GetPersonalCenterByUserId(r.tx, req.UserId)
	if err != nil {
		return
	}

	if exist {
		// 更新
		personalCenter.Nickname = req.Nickname
		personalCenter.Avatar = req.Avatar
		personalCenter.Phone = req.Phone
		personalCenter.Email = req.Email
		personalCenter.Gender = req.Gender
		personalCenter.Birthday = req.Birthday
		personalCenter.Address = req.Address
		personalCenter.Description = req.Description
		personalCenter.UpdateTime = time.Now()
		err = dao.UpdatePersonalCenter(r.tx, &personalCenter)
	} else {
		// 创建
		newPersonalCenter := &third_party_extra.PersonalCenter{
			UserId:      req.UserId,
			Nickname:    req.Nickname,
			Avatar:      req.Avatar,
			Phone:       req.Phone,
			Email:       req.Email,
			Gender:      req.Gender,
			Birthday:    req.Birthday,
			Address:     req.Address,
			Description: req.Description,
			Status:      1, // 默认正常状态
		}
		newPersonalCenter.CreateTime = time.Now()
		newPersonalCenter.UpdateTime = time.Now()
		err = dao.CreatePersonalCenter(r.tx, newPersonalCenter)
	}
	return
}

// GetPersonalCenterList 获取个人中心列表
func (r *PersonalCenterRepo) GetPersonalCenterList(req third_party_extra_structure.GetPersonalCenterListRequest) (response third_party_extra_structure.GetPersonalCenterListResponse, err error) {
	list, total, err := dao.GetPersonalCenterList(r.tx, req)
	if err != nil {
		return
	}

	var responseList []third_party_extra_structure.GetPersonalCenterResponse
	for _, item := range list {
		responseList = append(responseList, third_party_extra_structure.GetPersonalCenterResponse{
			Id:          item.Id,
			UserId:      item.UserId,
			Nickname:    item.Nickname,
			Avatar:      item.Avatar,
			Phone:       item.Phone,
			Email:       item.Email,
			Gender:      item.Gender,
			Birthday:    item.Birthday,
			Address:     item.Address,
			Description: item.Description,
			Status:      item.Status,
			CreateTime:  item.CreateTime,
			UpdateTime:  item.UpdateTime,
		})
	}

	response = third_party_extra_structure.GetPersonalCenterListResponse{
		PageResponse: req.PageRequest.BuildPageResponse(total),
		List:         responseList,
	}
	return
}

// DeletePersonalCenter 删除个人中心信息
func (r *PersonalCenterRepo) DeletePersonalCenter(id uint64) (err error) {
	err = dao.DeletePersonalCenter(r.tx, id)
	return
}
