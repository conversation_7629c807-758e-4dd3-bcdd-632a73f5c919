package third_party_extra

import (
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/mysql/third_party_extra"
	"hcscm/model/mysql/third_party_extra/dao"
	"hcscm/structure/third_party_extra_structure"
	"time"
)

type UserBehaviorTrackingRepo struct {
	tx *mysql_base.Tx
}

func NewUserBehaviorTrackingRepo(tx *mysql_base.Tx) *UserBehaviorTrackingRepo {
	return &UserBehaviorTrackingRepo{
		tx: tx,
	}
}

// CreateUserBehaviorTracking 创建用户行为跟踪记录
func (r *UserBehaviorTrackingRepo) CreateUserBehaviorTracking(req third_party_extra_structure.CreateUserBehaviorTrackingRequest) (err error) {
	tracking := &third_party_extra.UserBehaviorTracking{
		UserId:       req.UserId,
		ActionType:   req.ActionType,
		ActionTarget: req.ActionTarget,
		ActionData:   req.ActionData,
		IpAddress:    req.IpAddress,
		UserAgent:    req.UserAgent,
		SessionId:    req.SessionId,
		Duration:     req.Duration,
	}
	tracking.CreateTime = time.Now()
	tracking.UpdateTime = time.Now()

	err = dao.CreateUserBehaviorTracking(r.tx, tracking)
	return
}

// GetUserBehaviorTrackingList 获取用户行为跟踪列表
func (r *UserBehaviorTrackingRepo) GetUserBehaviorTrackingList(req third_party_extra_structure.GetUserBehaviorTrackingListRequest) (response third_party_extra_structure.GetUserBehaviorTrackingListResponse, err error) {
	list, total, err := dao.GetUserBehaviorTrackingList(r.tx, req)
	if err != nil {
		return
	}

	var responseList []third_party_extra_structure.UserBehaviorTrackingResponse
	for _, item := range list {
		responseList = append(responseList, third_party_extra_structure.UserBehaviorTrackingResponse{
			Id:           item.Id,
			UserId:       item.UserId,
			ActionType:   item.ActionType,
			ActionTarget: item.ActionTarget,
			ActionData:   item.ActionData,
			IpAddress:    item.IpAddress,
			UserAgent:    item.UserAgent,
			SessionId:    item.SessionId,
			Duration:     item.Duration,
			ActionTime:   item.ActionTime,
			CreateTime:   item.CreateTime,
		})
	}

	response = third_party_extra_structure.GetUserBehaviorTrackingListResponse{
		PageResponse: req.PageRequest.BuildPageResponse(total),
		List:         responseList,
	}
	return
}

// GetUserBehaviorStatistics 获取用户行为统计
func (r *UserBehaviorTrackingRepo) GetUserBehaviorStatistics(req third_party_extra_structure.GetUserBehaviorStatisticsRequest) (response third_party_extra_structure.GetUserBehaviorStatisticsResponse, err error) {
	statistics, err := dao.GetUserBehaviorStatistics(r.tx, req.UserId, req.StartTime, req.EndTime)
	if err != nil {
		return
	}

	response = third_party_extra_structure.GetUserBehaviorStatisticsResponse{
		Statistics: statistics,
	}
	return
}

// CleanupOldBehaviorData 清理旧的行为数据
func (r *UserBehaviorTrackingRepo) CleanupOldBehaviorData(beforeTime time.Time) (err error) {
	err = dao.DeleteUserBehaviorTracking(r.tx, beforeTime)
	return
}

// TrackUserLogin 跟踪用户登录行为
func (r *UserBehaviorTrackingRepo) TrackUserLogin(userId uint64, ipAddress, userAgent, sessionId string) (err error) {
	req := third_party_extra_structure.CreateUserBehaviorTrackingRequest{
		UserId:       userId,
		ActionType:   "login",
		ActionTarget: "system",
		ActionData:   "{\"login_time\":\"" + time.Now().Format(time.RFC3339) + "\"}",
		IpAddress:    ipAddress,
		UserAgent:    userAgent,
		SessionId:    sessionId,
	}
	return r.CreateUserBehaviorTracking(req)
}

// TrackUserLogout 跟踪用户登出行为
func (r *UserBehaviorTrackingRepo) TrackUserLogout(userId uint64, ipAddress, userAgent, sessionId string, duration int64) (err error) {
	req := third_party_extra_structure.CreateUserBehaviorTrackingRequest{
		UserId:       userId,
		ActionType:   "logout",
		ActionTarget: "system",
		ActionData:   "{\"logout_time\":\"" + time.Now().Format(time.RFC3339) + "\"}",
		IpAddress:    ipAddress,
		UserAgent:    userAgent,
		SessionId:    sessionId,
		Duration:     duration,
	}
	return r.CreateUserBehaviorTracking(req)
}
