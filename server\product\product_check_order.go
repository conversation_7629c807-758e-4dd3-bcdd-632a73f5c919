package product

import (
	"github.com/gin-gonic/gin"
	commonProduct "hcscm/common/product"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/redis"
	"hcscm/server/system"
	svc "hcscm/service/product"
	structure "hcscm/structure/product"
)

// @Tags 【成品库存盘点单】
// @Summary 添加成品库存盘点单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddProductCheckOrderParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.AddProductCheckOrderData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/addProductCheckOrder [post]
func AddProductCheckOrder(c *gin.Context) {
	var (
		q    = &structure.AddProductCheckOrderParam{}
		data = structure.AddProductCheckOrderData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	if err = q.CheckVNumber(); err != nil {
		return
	}
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, err = productCheckOrderSvc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 更新成品库存盘点单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/updateProductCheckOrder [put]
func UpdateProductCheckOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateProductCheckOrderParam{}
		data = structure.UpdateProductCheckOrderData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	if err = q.CheckVNumber(); err != nil {
		return
	}
	data, err = productCheckOrderSvc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 更新成品库存盘点单状态-消审
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderAuditStatusParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderAuditStatusData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusWait [put]
func UpdateProductCheckOrderAuditStatusWait(c *gin.Context) {
	var (
		q           = &structure.UpdateProductCheckOrderAuditStatusParam{}
		data        = structure.UpdateProductCheckOrderAuditStatusData{}
		stockSvc    = svc.NewStockProductService()
		err         error
		rLocks      = make(redis.LockForRedisList, 0)
		updateItems structure.UpdateStockProductDetailParamList
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, updateItems, err = productCheckOrderSvc.UpdateStatusWait(ctx, q)
	if err != nil {
		return
	}

	// 修改的相关库存信息(有库存细码id的就更新,没有的就新增)
	if len(updateItems) != 0 {
		rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
		if err != nil {
			return
		}
		err = stockSvc.FpmCostPriceWaitUpdate(ctx, tx, updateItems)
		if err != nil {
			return
		}
		// 更新相关库存信息的盘点信息
		_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
		if err != nil {
			return
		}
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 更新成品库存盘点单状态-审核
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderAuditStatusParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderAuditStatusData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusPass [put]
func UpdateProductCheckOrderAuditStatusPass(c *gin.Context) {
	var (
		q           = &structure.UpdateProductCheckOrderAuditStatusParam{}
		data        = structure.UpdateProductCheckOrderAuditStatusData{}
		stockSvc    = svc.NewStockProductService()
		err         error
		rLocks      = make(redis.LockForRedisList, 0)
		ids         map[uint64]uint64
		sumIds      map[uint64]uint64
		addItems    structure.AddStockProductDetailParamList
		updateItems structure.UpdateStockProductDetailParamList
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, addItems, updateItems, err = productCheckOrderSvc.UpdateStatusPass(ctx, q)
	if err != nil {
		return
	}

	rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, addItems, updateItems)
	if err != nil {
		return
	}

	// 新增修改的相关库存信息(有库存细码id的就更新,没有的就新增)
	if len(addItems) != 0 {
		ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addItems)
		if err != nil {
			return
		}
		err = stockSvc.FpmCostPricePassUpdate(ctx, tx, addItems)
		if err != nil {
			return
		}

		// 更新库存细码id到单据信息里面
		err = productCheckOrderSvc.UpdateDetailStockDetailId(ctx, ids, sumIds, q.Id)
		if err != nil {
			return
		}
	}
	if len(updateItems) != 0 {
		newAddItems := make(structure.AddStockProductDetailParamList, 0)
		for _, item := range updateItems {
			newAddItems = append(newAddItems, &structure.AddStockProductDetailParam{
				OutStockProductDetailId: item.Id,
				WarehouseInOrderId:      q.Id,
				ProductId:               item.ProductId,
				ProductColorId:          item.ProductColorId,
				WarehouseInType:         commonProduct.WarehouseGoodInTypeCheck,
				DyelotNumber:            item.DyelotNumber,
			})
		}
		err = stockSvc.FpmCostPricePassUpdate(ctx, tx, newAddItems)
		if err != nil {
			return
		}
		// 更新相关库存信息的盘点信息
		_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
		if err != nil {
			return
		}
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary pda审核成品库存盘点单
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderAuditStatusParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderAuditStatusData{}
// @Router /hcscm/pda/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusPass [put]
func PDAUpdateProductCheckOrderAuditStatusPass(c *gin.Context) {
	UpdateProductCheckOrderAuditStatusPass(c)
}

// @Tags 【成品库存盘点单】
// @Summary 更新成品库存盘点单状态-驳回
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderAuditStatusParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderAuditStatusData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusReject [put]
func UpdateProductCheckOrderAuditStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateProductCheckOrderAuditStatusParam{}
		data = structure.UpdateProductCheckOrderAuditStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, err = productCheckOrderSvc.UpdateStatusReject(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 更新成品库存盘点单状态-作废
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderAuditStatusParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderAuditStatusData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/updateProductCheckOrderAuditStatusCancel [put]
func UpdateProductCheckOrderAuditStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateProductCheckOrderAuditStatusParam{}
		data = structure.UpdateProductCheckOrderAuditStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, err = productCheckOrderSvc.UpdateStatusCancel(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 更新成品库存盘点单业务状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateProductCheckOrderBusinessCloseParam{}  true "创建ProductCheckOrder"
// @Success 200 {object}  structure.UpdateProductCheckOrderBusinessCloseData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/updateProductCheckOrderBusinessClose [put]
func UpdateProductCheckOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateProductCheckOrderBusinessCloseParam{}
		data = structure.UpdateProductCheckOrderBusinessCloseData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, err = productCheckOrderSvc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 获取成品库存盘点单详情(有成品信息和细码信息)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetProductCheckOrderDetailData{}
// @Router /hcscm/admin/v1/product/productCheckOrder/getProductCheckOrder [get]
func GetProductCheckOrder(c *gin.Context) {
	var (
		q    = &structure.GetProductCheckOrderQuery{}
		data = structure.GetProductCheckOrderDetailData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	data, err = productCheckOrderSvc.Get(ctx, q.Id)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 获取成品库存盘点单列表(无成品信息和细码信息)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     warehouse_bin_id     query     int  false  "仓位id"
// @Param     start_check_time     query     string  false  "盘点日期"
// @Param     end_check_time     query     string  false  "盘点日期"
// @Param     warehouse_manager_id     query     int  false  "仓管员ID"
// @Param     order_no     query     string  false  "生产变更单号"
// @Param     audit_status     query     string  false  "订单状态(多选，逗号拼接)"
// @Param     department_id     query     int  false  "下单用户所属部门"
// @Param     start_audit_date     query     int  false  "开始审核时间"
// @Param     end_audit_date     query     int  false  "结束审核时间"
// @Success 200 {object}  structure.GetProductCheckOrderDataList{}
// @Router /hcscm/admin/v1/product/productCheckOrder/getProductCheckOrderList [get]
func GetProductCheckOrderList(c *gin.Context) {
	var (
		q     = &structure.GetProductCheckOrderListQuery{}
		list  = make(structure.GetProductCheckOrderDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)

	list, total, err = productCheckOrderSvc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary pda获取成品库存盘点单列表(无成品信息和细码信息)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     sale_system_id     query     int  false  "营销体系ID"
// @Param     warehouse_id     query     int  false  "仓库id"
// @Param     warehouse_bin_id     query     int  false  "仓位id"
// @Param     start_check_time     query     string  false  "盘点日期"
// @Param     end_check_time     query     string  false  "盘点日期"
// @Param     warehouse_manager_id     query     int  false  "仓管员ID"
// @Param     order_no     query     string  false  "生产变更单号"
// @Param     audit_status     query     string  false  "订单状态(多选，逗号拼接)"
// @Param     department_id     query     int  false  "下单用户所属部门"
// @Success 200 {object}  structure.GetProductCheckOrderDataList{}
// @Router /hcscm/pda/v1/product/productCheckOrder/getProductCheckOrderList [get]
func PDAGetProductCheckOrderList(c *gin.Context) {
	GetProductCheckOrderList(c)
}

// @Tags 【成品库存盘点单】
// @Summary 根据盘点单id获取成品信息和细码信息列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     product_check_order_id     query     int  false  "成品盘点单ID"
// @Success 200 {object}  structure.GetProductCheckOrderItemDataList{}
// @Router /hcscm/admin/v1/product/productCheckOrder/getProductCheckOrderDetailList [get]
func GetProductCheckOrderDetailList(c *gin.Context) {
	var (
		q     = &structure.GetProductCheckOrderListQuery{}
		list  = make(structure.GetProductCheckOrderItemDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	var (
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)

	list, total, err = productCheckOrderSvc.GetDetailList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品库存盘点单】
// @Summary 拆详细库存生成盘点单-并且审核通过
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.SplitStockDetailParam{}  true "拆详细库存"
// @Success 200 {object}  structure.GetProductCheckOrderItemDataList{}
// @Router /hcscm/admin/v1/product/productCheckOrder/stock_detail/split [post]
func AddAndPassCheckProductOrderByStockDetailSplit(c *gin.Context) {
	var (
		err                                error
		productCheckOrderId                uint64
		ids                                map[uint64]uint64
		sumIds                             map[uint64]uint64
		swap2AddProductCheckOrderParamList structure.AddProductCheckOrderParamList
		q                                  = &structure.SplitStockDetailParam{}
		data                               = structure.AddProductCheckOrderData{}
		addStockProductDetailParamList     = structure.AddStockProductDetailParamList{}
		updateStockProductDetailParamList  = structure.UpdateStockProductDetailParamList{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		stockSvc             = svc.NewStockProductService()
		productCheckOrderSvc = svc.NewProductCheckOrderService(c, tx)
	)
	// 转换
	swap2AddProductCheckOrderParamList, err = productCheckOrderSvc.AddCheckProductOrderByStockDetailSplit(ctx, q)
	if err != nil {
		return
	}
	for _, swap2AddProductCheckOrderParam := range swap2AddProductCheckOrderParamList {
		rLocks := make(redis.LockForRedisList, 0)
		// 2 新增
		data, err = productCheckOrderSvc.Add(ctx, &swap2AddProductCheckOrderParam)
		productCheckOrderId = data.Id
		if err != nil {
			return
		}
		// 3 审核通过
		_, addStockProductDetailParamList, updateStockProductDetailParamList, err = productCheckOrderSvc.UpdateStatusPass(ctx, &structure.UpdateProductCheckOrderAuditStatusParam{
			Id: productCheckOrderId,
		})
		if err != nil {
			return
		}

		rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, addStockProductDetailParamList, updateStockProductDetailParamList)
		if err != nil {
			return
		}
		// 新增修改的相关库存信息(有库存细码id的就更新,没有的就新增)
		if len(addStockProductDetailParamList) != 0 {
			ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addStockProductDetailParamList)
			if err != nil {
				return
			}

			// 更新库存细码id到单据信息里面
			err = productCheckOrderSvc.UpdateDetailStockDetailId(ctx, ids, sumIds, productCheckOrderId)
			if err != nil {
				return
			}
		}
		if len(updateStockProductDetailParamList) != 0 {
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateStockProductDetailParamList)
			if err != nil {
				return
			}
		}
		rLocks.Unlock()
	}

	return
}
