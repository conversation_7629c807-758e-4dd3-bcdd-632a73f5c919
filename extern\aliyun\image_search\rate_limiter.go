package image_search

import (
	"context"
	"hcscm/extern/aliyun"
	"sync"
	"time"
)

// RateLimiter 限流器结构体
type RateLimiter struct {
	mutex    sync.Mutex
	interval time.Duration
	lastTime time.Time
	queue    chan func()
}

// 全局限流器实例
var globalRateLimiter *RateLimiter
var once sync.Once

// GetRateLimiter 获取全局限流器实例
func GetRateLimiter() *RateLimiter {
	once.Do(func() {
		// 使用aliyun包中配置的限流间隔
		globalRateLimiter = NewRateLimiter(aliyun.ImageSearchRateLimitInterval)
		go globalRateLimiter.processQueue()
	})
	return globalRateLimiter
}

// NewRateLimiter 创建一个新的限流器
func NewRateLimiter(interval time.Duration) *RateLimiter {
	return &RateLimiter{
		interval: interval,
		lastTime: time.Now().Add(-interval), // 初始化为可以立即执行
		queue:    make(chan func(), 100),    // 队列容量设置为100，可根据实际需求调整
	}
}

// SetInterval 设置限流间隔
func (r *RateLimiter) SetInterval(interval time.Duration) {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	r.interval = interval
}

// GetInterval 获取当前限流间隔
func (r *RateLimiter) GetInterval() time.Duration {
	r.mutex.Lock()
	defer r.mutex.Unlock()
	return r.interval
}

// processQueue 处理队列中的任务
func (r *RateLimiter) processQueue() {
	for fn := range r.queue {
		r.mutex.Lock()
		now := time.Now()
		diff := now.Sub(r.lastTime)
		if diff < r.interval {
			// 计算需要等待的时间
			waitTime := r.interval - diff
			r.mutex.Unlock()
			time.Sleep(waitTime)
		} else {
			r.mutex.Unlock()
		}

		// 执行任务
		fn()

		// 更新最后执行时间
		r.mutex.Lock()
		r.lastTime = time.Now()
		r.mutex.Unlock()
	}
}

// Execute 执行任务，确保任务按照限流规则执行
func (r *RateLimiter) Execute(fn func()) {
	r.queue <- fn
}

// ExecuteWithResult 执行有返回值的任务
func (r *RateLimiter) ExecuteWithResult(ctx context.Context, fn func() (interface{}, error)) (interface{}, error) {
	resultCh := make(chan struct {
		result interface{}
		err    error
	})

	r.queue <- func() {
		result, err := fn()
		resultCh <- struct {
			result interface{}
			err    error
		}{result, err}
	}

	// 等待结果或上下文取消
	select {
	case res := <-resultCh:
		return res.result, res.err
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}

// 初始化函数
func init() {
	// 初始化已经在aliyun包的Init函数中完成
	// 这里不需要额外的初始化代码
}

// SetGlobalRateLimitInterval 设置全局限流间隔
func SetGlobalRateLimitInterval(interval time.Duration) {
	GetRateLimiter().SetInterval(interval)
}
