package product

import (
	"github.com/gin-gonic/gin"
	"hcscm/common/errors"
	"hcscm/extern/pb/dyeing_and_finishing"
	"hcscm/middleware"
	"hcscm/server/system"
	svc "hcscm/service/basic_data"
	structure "hcscm/structure/product"
	structure_base "hcscm/structure/system"
	"strings"
)

// @Tags 【成品颜色】
// @Summary 添加成品颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddFinishProductColorParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure.AddFinishProductColorData{}
// @Router /hcscm/admin/v1/product/finishProductColor/addFinishProductColor [post]
func AddFinishProductColor(c *gin.Context) {
	var (
		q    = &structure.AddFinishProductColorParam{}
		data = structure.AddFinishProductColorData{}
		svc  = svc.NewFinishProductColorService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 快捷添加成品颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.QuickAddFinishProductColorParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure.AddFinishProductColorData{}
// @Router /hcscm/mp/v1/product/finishProductColor/addQuickProductColor [post]
func AddQuickFinishProductColor(c *gin.Context) {
	var (
		q          = &structure.QuickAddFinishProductColorParam{}
		data       = structure.AddFinishProductColorData{}
		productSvc = svc.NewFinishProductService()
		svc        = svc.NewFinishProductColorService()
		err        error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	if q.ProductId == 0 {
		err = errors.NewCustomError(errors.ErrCodeBusinessParameter, "，需要确定面料信息")
		return
	}

	s := strings.SplitAfterN(q.ProductColorName, "#", 2)
	if len(s) != 2 {
		s = strings.SplitN(q.ProductColorName, " ", 2)
	}
	for i, s2 := range s {
		if i == 0 {
			q.ProductColorCode = s2
		}
		if i == 1 {
			q.ProductColorName = s2
		}
	}

	// 查询成品信息补全颜色资料
	var productData structure.GetFinishProductData
	productData, err = productSvc.Get(ctx, &structure.GetFinishProductQuery{Id: q.ProductId})
	if err != nil {
		return
	}

	req := &structure.AddFinishProductColorParam{
		FinishProductId:               q.ProductId,
		ProductColorCode:              q.ProductColorCode,
		ProductColorName:              q.ProductColorName,
		ProductColorFullName:          q.ProductColorFullName,
		FinishProductCraft:            productData.FinishProductCraft,
		TypeGreyFabricId:              productData.TypeGreyFabricId,
		GreyFabricId:                  productData.GreyFabricId,
		LengthToWeightRate:            productData.LengthToWeightRate,
		YarnCount:                     productData.YarnCount,
		Density:                       productData.Density,
		FinishProductWidth:            productData.FinishProductWidth,
		FinishProductGramWeight:       productData.FinishProductGramWeight,
		FinishProductWidthUnitId:      productData.FinishProductWidthUnitId,
		FinishProductGramWeightUnitId: productData.FinishProductGramWeightUnitId,
		WeavingOrganizationId:         productData.WeavingOrganizationId,
		BleachId:                      productData.BleachId,
		ShrinkageWarp:                 productData.ShrinkageWarp,
	}
	data, err = svc.Add(ctx, req)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 快捷添加染厂颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddProductDyeColorParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/mp/v1/product/finishProductColor/addQuickProductDyeColor [post]
// @Tags 【成品颜色】
// @Summary 快捷添加染厂颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.AddProductDyeColorParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure_base.ResponseData{}
// @Router /hcscm/admin/v1/product/finishProductColor/addQuickProductDyeColor [post]
func AddQuickProductDyeColor(c *gin.Context) {
	var (
		q    = &structure.AddProductDyeColorParam{}
		data = structure_base.ResponseData{}
		svc  = svc.NewFinishProductColorService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	err = svc.AddQuickDyeingColor(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 删除成品颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.DeleteFinishProductColorParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure.DeleteFinishProductColorData{}
// @Router /hcscm/admin/v1/product/finishProductColor/deleteFinishProductColor [delete]
func DeleteFinishProductColor(c *gin.Context) {
	var (
		q    = &structure.DeleteFinishProductColorParam{}
		data = structure.DeleteFinishProductColorData{}
		svc  = svc.NewFinishProductColorService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Delete(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 更新成品颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateFinishProductColorParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure.UpdateFinishProductColorData{}
// @Router /hcscm/admin/v1/product/finishProductColor/updateFinishProductColor [put]
func UpdateFinishProductColor(c *gin.Context) {
	var (
		q    = &structure.UpdateFinishProductColorParam{}
		data = structure.UpdateFinishProductColorData{}
		svc  = svc.NewFinishProductColorService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 更新成品颜色状态
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param   body   body   structure.UpdateFinishProductColorStatusParam{}  true "创建FinishProductColor"
// @Success 200 {object}  structure.UpdateFinishProductColorStatusData{}
// @Router /hcscm/admin/v1/product/finishProductColor/updateFinishProductColorStatus [put]
func UpdateFinishProductColorStatus(c *gin.Context) {
	var (
		q    = &structure.UpdateFinishProductColorStatusParam{}
		data = structure.UpdateFinishProductColorStatusData{}
		svc  = svc.NewFinishProductColorService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateStatus(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 获取成品颜色
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     id  query    int  true   "id"
// @Success 200 {object}  structure.GetFinishProductColorData{}
// @Router /hcscm/admin/v1/product/finishProductColor/getFinishProductColor [get]
func GetFinishProductColor(c *gin.Context) {
	var (
		q    = &structure.GetFinishProductColorQuery{}
		data = structure.GetFinishProductColorData{}
		svc  = svc.NewFinishProductColorService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 获取成品颜色列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     finish_product_id  query     int  false  "成品id"
// @Param     product_id  query     string  false  "成品id,逗号拼接"
// @Param     type_grey_fabric_id  query     int  false  "布种类型id"
// @Param     type_finished_product_kind_id  query     int  false  "颜色类别id"
// @Param     product_color_id  query     int  false  "颜色id"
// @Param     product_color_code  query     string  false  "颜色编号"
// @Param     product_color_name  query     string  false  "颜色名称"
// @Param     product_color_code_or_name  query     string  false  "颜色编号或名称"
// @Param		field_search			query		string	false	"字段搜索"
// @Param     status  query     int  false  "状态"
// @Success 200 {object}  structure.GetFinishProductColorDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getFinishProductColorList [get]
func GetFinishProductColorList(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductColorListQuery{}
		list  = make(structure.GetFinishProductColorDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.Adjust()
	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 获取成品颜色下拉列表(可用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     finish_product_id  query     int  false  "成品id"
// @Param     type_grey_fabric_id  query     int  false  "布种类型id"
// @Param     type_finished_product_kind_id  query     int  false  "颜色类别id"
// @Param     product_color_id  query     int  false  "颜色id"
// @Param     product_color_code  query     string  false  "颜色编号"
// @Param     product_color_name  query     string  false  "颜色名称"
// @Param     product_color_code_or_name  query     string  false  "颜色编号或名称"
// @Param     status  query     int  false  "状态"
// @Success 200 {object}  structure.GetFinishProductColorDropdownDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getFinishProductColorDropdownList [get]
func GetFinishProductColorDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductColorListQuery{}
		list  = make(structure.GetFinishProductColorDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 获取成品颜色下拉列表(可用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     finish_product_id  query     int  false  "成品id"
// @Param     type_grey_fabric_id  query     int  false  "布种类型id"
// @Param     type_finished_product_kind_id  query     int  false  "颜色类别id"
// @Param     product_color_id  query     int  false  "颜色id"
// @Param     product_color_code  query     string  false  "颜色编号"
// @Param     product_color_name  query     string  false  "颜色名称"
// @Param     product_color_code_or_name  query     string  false  "颜色编号或名称"
// @Param     status  query     int  false  "状态"
// @Success 200 {object}  structure.GetFinishProductColorDropdownDataList{}
// @Router /hcscm/mp/v1/product/finishProductColor/getFinishProductColorDropdownList [get]
func MPGetFinishProductColorDropdownList(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductColorListQuery{}
		list  = make(structure.GetFinishProductColorDropdownDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDropdownList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 获取成品颜色下拉列表(填编号的时候用)
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param		Platform			header		int										true	"终端ID"
// @Param		Authorization		header		string									true	"token"
// @Param     	product_color_code  query     	string  false  "颜色编号"
// @Param     	finish_product_id  	query     	int  	false  "成品id"
// @Success 200 {object}  structure.GetSomeFinishProductColorFieldDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/searchSomeFinishProductColorField [get]
func SearchSomeFinishProductColorField(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductColorListQuery{}
		list  = make(structure.GetSomeFinishProductColorFieldDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	if q.ProductColorCode == "" {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, "请输入编号"))
		return
	}
	if q.FinishProductId == 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, "请输入物料id"))
		return
	}

	if q.Page == 0 && q.Size == 0 {
		q.Page = 1
		q.Size = 50
	}

	list, err = svc.SearchSomeFinishProductColorField(ctx, q)
	if err != nil {
		return
	}

	total = len(list)

	return
}

// @Tags 【成品颜色】
// @Summary 根据成品id获取颜色类别列表
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param     product_id  query     int  false  "成品id"
// @Success 200 {object}  structure.GetTypeFinishedProductColorDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getProductColorKindByProductId [get]
func GetProductColorKindByProductId(c *gin.Context) {
	var (
		q     = &structure.GetTypeFinishedProductColorListQuery{}
		list  = make(structure.GetTypeFinishedProductColorDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetProductColorKindByProductId(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags 【成品颜色】
// @Summary 根据成品颜色id获取复合布信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id  query     int  false  "成品颜色id"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Success 200 {object}  structure.GetFinishProductColorDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getProductCompositeDetails [get]
func GetProductCompositeDetails(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductColorQuery{}
		list  = make(structure.GetFinishProductCompositeColorDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, err = svc.GetProductCompositeDetails(ctx, q)
	if err != nil {
		return
	}
	total = len(list)
	return
}

// @Tags 【成品颜色】
// @Summary 根据成品颜色id获取染厂颜色信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     id  query     int  false  "成品颜色id"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Success 200 {object}  structure.GetFinishProductColorDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getProductDyeingColorDetails [get]
func GetFinishProductDyeingColorDetails(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductColorQuery{}
		list  = make(structure.GetFinishProductDyeingColorDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, err = svc.GetProductDyeingColorDetails(ctx, q)
	if err != nil {
		return
	}
	total = len(list)
	return
}

// @Tags 【成品颜色】
// @Summary 根据成品id获取染厂颜色信息
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_id  query     int  false  "成品id"
// @Param     product_color_code  query     string  false  "颜色编号"
// @Param     product_color_name  query     string  false  "颜色名称"
// @Param     product_color_code_or_name  query     string  false  "颜色编号或名称"
// @Param     dye_factory_color_code  query     string  false  "染厂色号"
// @Param     dye_factory_color_name  query     string  false  "染厂颜色名称"
// @Param     dye_factory_id  query     int  false  "染厂id"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Success 200 {object}  structure.GetFinishProductDyeingColorDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getDyeingColorDetailsByProduct [get]
func GetDyeingColorDetailsByProduct(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductDyeingColorListQuery{}
		list  = make(structure.GetFinishProductDyeingColorDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetDyeingColorDetailsByProduct(ctx, q)
	if err != nil {
		return
	}
	total = len(list)
	return
}

// @Tags 【成品颜色】
// @Summary 染厂颜色枚举，染厂色号枚举
// @Security ApiKeyAuth
// @accept application/json
// @Produce application/json
// @Param     page      query     int  false  "page"
// @Param     size      query     int  false  "size"
// @Param     offset    query     int  false  "offset"
// @Param     limit     query     int  false  "limit"
// @Param     download  query     int  false  "download"
// @Param     finish_product_id  query     int  false  "成品id"
// @Param     product_color_code  query     string  false  "颜色编号"
// @Param     product_color_name  query     string  false  "颜色名称"
// @Param     product_color_code_or_name  query     string  false  "颜色编号或名称"
// @Param     dye_factory_color_code  query     string  false  "染厂色号"
// @Param     dye_factory_color_name  query     string  false  "染厂颜色名称"
// @Param     is_show_only_wait_use  query     bool  false  "是否只展示当前未有生效单价的色号"
// @Param     dye_factory_id  query     int  false  "染厂id"
// @Param		Platform		header		int										true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Success 200 {object}  structure.GetFinishProductDyeingColorDataList{}
// @Router /hcscm/admin/v1/product/finishProductColor/getDyeingColorDetailsByProductListEnum [get]
func GetDyeingColorDetailsByProductListEnum(c *gin.Context) {
	var (
		q     = &structure.GetFinishProductDyeingColorListQuery{}
		list  = make(structure.GetFinishProductDyeingColorDataList, 0)
		total int
		err   error
		svc   = svc.NewFinishProductColorService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.IsUseEnum = true
	if q.IsShowOnlyWaitUse {
		dnfQuoteService := dyeing_and_finishing.NewClientDNFQuoteService()
		q.UseIds, err = dnfQuoteService.GetEffectdyeingColorIds(ctx, q.DyeFactoryId)
		if err != nil {
			return
		}
	}
	list, total, err = svc.GetDyeingColorDetailsByProductListEnum(ctx, q)
	if err != nil {
		return
	}
	// total = len(list)
	return
}
