package product

import (
	"context"
	"fmt"
	salePriceAggs "hcscm/aggs/sale_price"
	"hcscm/common/errors"
	common_product "hcscm/common/product"
	common "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product2 "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/basic_data/warehouse"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	"hcscm/extern/pb/sale_price"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	salePriceModel "hcscm/model/mysql/sale_price"
	salePriceMysql "hcscm/model/mysql/sale_price/dao"
	redisF "hcscm/model/redis"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"strings"
	"time"
)

// 新增单条库存详情(已创建汇总库存)
func (u *StockProductRepo) AddDetail(ctx context.Context, req *structure.AddStockProductDetailParam, stockProductId uint64) (detailId uint64, err error) {
	var (
		exist              bool
		productColorItem   product2.ProductColorRes
		productItem        product2.ProductRes
		customer           biz_unit.Res
		stockDetail        model.StockProductDetail
		fabricPieceCode    model.FabricPieceCode
		noNumber           int
		customerId         uint64
		customerNumberCode int = 0001
	)
	stockProductDetail := model.NewStockProductDetail(ctx, req, stockProductId)

	// 补充颜色类别id，补充成品种类id
	err = errgroup.Finish(ctx, 0,
		func(ctx context.Context) error {
			productColorItem, _ = product2.NewProductColorClient().GetProductColorById(ctx, stockProductDetail.ProductColorId)
			return nil
		},
		func(ctx context.Context) error {
			productItem, _ = product2.NewProductClient().GetProduct(ctx, product2.ProductReq{Id: stockProductDetail.ProductId})
			return nil
		},
		func(ctx context.Context) error {
			if stockProductDetail.CustomerId == 0 {
				return nil
			}
			customer, _ = biz_unit.NewClientBizUnitService().GetBizUnitDetailByID(ctx, biz_unit.Req{Id: stockProductDetail.CustomerId})
			return nil
		},
	)
	if err != nil {
		return
	}
	if productItem.FinishProductCode == "" {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, fmt.Sprintf("成品%v资料编号不能为空", productItem.FinishProductCode)))
		return
	}
	if productColorItem.ProductColorCode == "" {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, fmt.Sprintf("成品%v成品颜色%v资料编号不能为空", fmt.Sprintf("%v%v", productItem.FinishProductCode, productItem.FinishProductName), productColorItem.ProductColorName)))
		return
	}
	if stockProductDetail.CustomerId != 0 {
		customerId = customer.Id
		customerNumberCode = customer.Code
	}
	stockProductDetail.ProductColorKindId = productColorItem.TypeFinishedProductKindId
	stockProductDetail.ProductKindId = productItem.TypeGreyFabricId
	noNumber, err = u.GetFabricPieceCodeNumber(ctx, 0001, customerNumberCode) // todo:待补充往来单位的编号
	if err != nil {
		return
	}

	fabricPieceCode, err = model.NewFabricPieceCode(ctx, &structure.AddFabricPieceCodeParam{
		SupplierId:          0, // todo:待补充往来单位的id
		SupplierNumberCode:  0001,
		DyeingId:            customerId,         // 这里使用客户id
		DyeingNumberCode:    customerNumberCode, // 这里使用客户自增编号
		DyeFactoryColorCode: stockProductDetail.DyeFactoryColorCode,
		WarehouseId:         stockProductDetail.WarehouseId,
		DyelotNumber:        stockProductDetail.DyelotNumber,
		BarCode:             stockProductDetail.BarCode,
		QrCode:              stockProductDetail.QrCode,
		ProductId:           stockProductDetail.ProductId,
		ProductCode:         productItem.FinishProductCode,
		ProductColorId:      stockProductDetail.ProductColorId,
		ProductColorCode:    productColorItem.ProductColorCode,
		ProductColorKindId:  stockProductDetail.ProductColorKindId,
		ProductLevelId:      stockProductDetail.ProductLevelId,
		ProductKindId:       stockProductDetail.ProductKindId,
		MeasurementUnitId:   productItem.MeasurementUnitId,
		Width:               stockProductDetail.FinishProductWidth,
		WidthUnitId:         stockProductDetail.FinishProductWidthUnitId,
		GramWeight:          stockProductDetail.FinishProductGramWeight,
		GramWeightUnitId:    stockProductDetail.FinishProductGramWeightUnitId,
		Tube:                stockProductDetail.PaperTubeWeight,
		WeightError:         stockProductDetail.WeightError,
		Weight:              stockProductDetail.Weight,
		Remark:              stockProductDetail.Remark,
		VolumeNumber:        stockProductDetail.VolumeNumber,
		NoNumber:            noNumber,
		SourceOrderNo:       stockProductDetail.WarehouseInOrderNo,
	})
	if err != nil {
		return
	}
	if fabricPieceCode.QrCode == "" || fabricPieceCode.BarCode == "" {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "二维码或条码生成失败"))
		return
	}
	fabricPieceCode, err = mysql.MustCreateFabricPieceCode(u.tx, fabricPieceCode)
	if err != nil {
		return
	}

	stockProductDetail.FabricPieceCodeId = fabricPieceCode.Id
	stockProductDetail.BarCode = fabricPieceCode.BarCode
	stockProductDetail.QrCode = fabricPieceCode.QrCode
	// stockProductDetail.GenBarQrCode(ctx, noNumber, productItem.FinishProductCode, productColorItem.ProductColorCode)

	stockDetail, exist, err = mysql.FirstStockProductDetailByColorDyeVolume(u.tx, stockProductDetail.WarehouseId, stockProductDetail.DyelotNumber, stockProductDetail.ProductColorId, stockProductDetail.VolumeNumber)
	if err != nil {
		return
	}
	if exist {
		if req.IsMayBePassVolumeNumber {
			// 如果都是0那么则执行更新，否则报错
			if stockDetail.Roll == 0 && stockDetail.Weight == 0 {
				noUpdate := stockDetail.IsEqualAndUpdate(ctx, req)
				if noUpdate {
					err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, fmt.Sprintf("库存卷号重复:%v-%v,缸号%v卷号%v", productItem.FinishProductCode, productColorItem.ProductColorCode, stockProductDetail.DyelotNumber, stockProductDetail.VolumeNumber)))
					return
				} else {
					// 更新
					stockProductDetail = stockDetail // 覆盖掉原来的
					stockDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockDetail)
					if err != nil {
						return
					}
				}
			}
		}
		if !req.IsMayBePassVolumeNumber {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, fmt.Sprintf("库存卷号重复:%v-%v,缸号%v卷号%v", productItem.FinishProductCode, productColorItem.ProductColorCode, stockProductDetail.DyelotNumber, stockProductDetail.VolumeNumber)))
			return
		}
	}

	if !exist {
		stockProductDetail, err = mysql.MustCreateStockProductDetail(u.tx, stockProductDetail)
		if err != nil {
			return
		}
	}

	u.stockProductDetail[stockProductDetail.Id] = stockProductDetail
	return stockProductDetail.Id, err
}

// 更新库存详情信息(出仓，盘点)
func (u *StockProductRepo) UpdateDetail(ctx context.Context, req *structure.UpdateStockProductDetailParam) (id uint64, err error) {
	var (
		stockProductDetail model.StockProductDetail
		ok                 bool
	)
	stockProductDetail, ok = u.stockProductDetail[req.Id]
	if !ok {
		stockProductDetail, err = mysql.MustFirstStockProductDetailByID(u.tx, req.Id)
		if err != nil {
			return
		}
	}

	// 消审时判断细码是否已出仓或已配布,有则报错不能消审。
	if req.IsToWait {
		if stockProductDetail.Status != common.StockStatusWarehouseIn {
			err = middleware.WarnLog(errors.NewError(errors.ErrCodeWasArrangedOrWarehouseOut),
				"缸号:",
				stockProductDetail.DyelotNumber,
				"卷号:",
				stockProductDetail.VolumeNumber,
			)
			return
		}
	}

	// 判断库存是否充足
	if stockProductDetail.Roll+req.Roll < 0 || stockProductDetail.Weight+req.Weight < 0 {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeNotEnoughStock),
			"缸号:",
			stockProductDetail.DyelotNumber,
			"卷号:",
			stockProductDetail.VolumeNumber,
		)
		return
	}

	// 消审直接删除
	if req.IsToWait {
		err = mysql.MustDeleteStockProductDetail(u.tx, stockProductDetail)
		if err != nil {
			return
		}
		// err = u.DelVolumeNumber(ctx, stockProductDetail.DyelotNumber, stockProductDetail.ProductColorId)
		// if err != nil {
		// 	return
		// }
		if stockProductDetail.FabricPieceCodeId != 0 {
			err = mysql.MustDeleteFabricPieceCodeById(u.tx, stockProductDetail.FabricPieceCodeId)
			if err != nil {
				return
			}
		}
	} else {
		// 调整单需要重新进行汇总库存绑定
		if req.Type == 5 || req.Type == 6 {
			req.StockProductId = req.AdjustStockProductId
		}
		stockProductDetail.UpdateStockProductDetail(ctx, req)
		stockProductDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockProductDetail)
		if err != nil {
			return
		}
	}

	u.stockProductDetail[req.Id] = stockProductDetail
	return stockProductDetail.Id, err
}

// 占用库存
func (u *StockProductRepo) UpdateDetailStatusArrange(ctx context.Context, detailIds []uint64) (stockProductIds []uint64, err error) {
	var (
		stockProductDetails model.StockProductDetailList
	)

	stockProductDetails, err = mysql.FindStockProductDetailByIDs(u.tx, detailIds)
	if err != nil {
		return
	}

	for _, stockProductDetail := range stockProductDetails {
		if lastStockProductDetail, ok := u.stockProductDetail[stockProductDetail.Id]; ok {
			stockProductDetail = lastStockProductDetail
		}
		if stockProductDetail.Status != common.StockStatusWarehouseIn {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeWasArrangedOrWarehouseOut,
				fmt.Sprintf("缸号:%v,卷号:%v", stockProductDetail.DyelotNumber, stockProductDetail.VolumeNumber),
			))
			return
		}
		stockProductDetail.UpdateStockProductDetailStatusArrange(ctx)

		stockProductDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockProductDetail)
		if err != nil {
			return
		}
		u.stockProductDetail[stockProductDetail.Id] = stockProductDetail
	}

	stockProductIds = mysql_base.GetUInt64List(stockProductDetails, "stock_product_id")

	return
}

// 释放库存
func (u *StockProductRepo) UpdateDetailStatusRelease(ctx context.Context, detailIds []uint64) (stockProductIds []uint64, err error) {
	var (
		stockProductDetails model.StockProductDetailList
	)

	stockProductDetails, err = mysql.FindStockProductDetailByIDs(u.tx, detailIds)
	if err != nil {
		return
	}

	for _, stockProductDetail := range stockProductDetails {
		if lastStockProductDetail, ok := u.stockProductDetail[stockProductDetail.Id]; ok {
			stockProductDetail = lastStockProductDetail
		}
		if stockProductDetail.Status != common.StockStatusArrange {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessStatusFalse,
				fmt.Sprintf("缸号:%v,卷号:%v", stockProductDetail.DyelotNumber, stockProductDetail.VolumeNumber),
			))
			return
		}
		stockProductDetail.UpdateStockProductDetailStatusRelease(ctx)

		stockProductDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockProductDetail)
		if err != nil {
			return
		}
		u.stockProductDetail[stockProductDetail.Id] = stockProductDetail
	}

	stockProductIds = mysql_base.GetUInt64List(stockProductDetails, "stock_product_id")

	return
}

func (u *StockProductRepo) GetMaxVolumeNumber(ctx context.Context, dyelotNumber string, productColorId uint64, volumeNumberReq int) (volumeNumber int, err error) {
	var (
		// v             string
		total int
		// _volumeNumber int64
	)
	// gen := redisF.NewGenVolumeNumber(ctx, u.redisClient, "stock_product_detail", dyelotNumber, tools.UInt642String(productColorId))
	// v, err = gen.GetVolumeNumber(ctx)
	// if err != nil {
	// 	return
	// }
	// if v == "" {
	total, _, err = mysql.GetMaxVolumeNumber(u.tx, dyelotNumber, productColorId, volumeNumberReq)
	if err != nil {
		return
	}

	// _, err = gen.SetVolumeNumber(ctx, int64(total))
	// if err != nil {
	// 	return
	// }
	return total, nil
	// }

	// todo:直接读取数据库数据,如果没有进仓审核则直接获取缓存，有则删除
	// _volumeNumber, err = tools.String2Int(v)
	// if err != nil {
	//	return
	// }

	// _volumeNumber, err = gen.GenVolumeNumber(ctx)
	// if err != nil {
	// 	return
	// }
	// volumeNumber = int(_volumeNumber)
	// return
}

func (u *StockProductRepo) GetFabricPieceCodeNumber(ctx context.Context, supplierNumberCode, customerNumberCode int) (volumeNumber int, err error) {
	var (
		// v             string
		total int
		// _volumeNumber int64
	)
	// gen := redisF.NewGenVolumeNumber(ctx, u.redisClient, "fabric_piece_code", fmt.Sprintf("%v:%v", supplierNumberCode, customerNumberCode))
	// v, err = gen.GetVolumeNumber(ctx)
	// if err != nil {
	//	return
	// }
	// if v == "" {
	total, _, err = mysql.GenerateFabricPieceCodeBarCodeNoNumber(u.tx, supplierNumberCode, customerNumberCode)
	if err != nil {
		return
	}

	// _, err = gen.SetVolumeNumber(ctx, int64(total))
	// if err != nil {
	//	return
	// }
	return total, nil
	// }

	// _volumeNumber, err = gen.GenVolumeNumber(ctx)
	// if err != nil {
	//	return
	// }
	// volumeNumber = int(_volumeNumber)
	// return
}

// 入库和消审出库则删除缓存
func (u *StockProductRepo) DelVolumeNumber(ctx context.Context, dyelotNumber string, productColorId uint64) (err error) {
	gen := redisF.NewGenVolumeNumber(ctx, u.redisClient, "stock_product_detail", fmt.Sprintf("%s:%s", dyelotNumber, tools.UInt642String(productColorId)))
	err = gen.DelVolumeNumber(ctx)
	if err != nil {
		return
	}
	return
}

// 根据id获取库存详情
func (u *StockProductRepo) GetDetail(ctx context.Context, id uint64, qrCode string) (data structure.GetStockProductDetailData, err error) {
	var (
		stockProductDetail model.StockProductDetail
	)
	if id != 0 {
		stockProductDetail, err = mysql.MustFirstStockProductDetailByID(u.tx, id)
		if err != nil {
			return
		}
	} else {
		stockProductDetail, err = mysql.MustFirstStockProductDetailByQrCode(u.tx, 0, qrCode)
		if err != nil {
			return
		}
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetail, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetail, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetail, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetail, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetail, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetail, "product_id")
	productSvc := product2.NewProductClient()
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetail, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	dicIds := mysql_base.GetUInt64List(stockProductDetail, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(stockProductDetail, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetail, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	o := structure.GetStockProductDetailData{}
	o.Id = stockProductDetail.Id
	o.CreateTime = tools.MyTime(stockProductDetail.CreateTime)
	o.UpdateTime = tools.MyTime(stockProductDetail.UpdateTime)
	o.CreatorId = stockProductDetail.CreatorId
	o.CreatorName = stockProductDetail.CreatorName
	o.UpdaterId = stockProductDetail.UpdaterId
	o.UpdateUserName = stockProductDetail.UpdaterName
	o.StockProductId = stockProductDetail.StockProductId
	o.WarehouseBinId = stockProductDetail.WarehouseBinId
	o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
	o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
	o.WarehouseInType = stockProductDetail.WarehouseInType
	o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
	o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
	o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
	o.CheckTime = tools.MyTime(stockProductDetail.CheckTime)
	o.CheckUserId = stockProductDetail.CheckUserId
	o.CheckUserName = user[stockProductDetail.CheckUserId]
	o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
	o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
	o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
	o.ProductColorId = stockProductDetail.ProductColorId
	o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
	o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
	o.ProductColorKindId = stockProductDetail.ProductColorKindId
	o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
	o.WarehouseId = stockProductDetail.WarehouseId
	o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
	o.CustomerId = stockProductDetail.CustomerId
	o.CustomerName = bizUnit[stockProductDetail.CustomerId]
	o.ProductId = stockProductDetail.ProductId
	o.ProductLevelId = stockProductDetail.ProductLevelId
	o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
	o.DyelotNumber = stockProductDetail.DyelotNumber
	o.VolumeNumber = stockProductDetail.VolumeNumber
	o.ProductRemark = stockProductDetail.ProductRemark
	o.WeightError = stockProductDetail.WeightError
	o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
	o.Weight = stockProductDetail.Weight
	o.ShelfNo = stockProductDetail.ShelfNo
	o.Length = stockProductDetail.Length
	o.Roll = stockProductDetail.Roll
	o.StockRoll = stockProductDetail.Roll
	o.AvailableRoll = stockProductDetail.Roll
	o.AvailableWeight = stockProductDetail.Weight
	o.DigitalCode = stockProductDetail.DigitalCode
	o.ContractNumber = stockProductDetail.ContractNumber
	o.CustomerPoNum = stockProductDetail.CustomerPoNum
	o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
	// 库存计量单位取基础资料的
	if product, ok := productMap[stockProductDetail.ProductId]; ok {
		o.ProductCode = product.FinishProductCode
		o.ProductName = product.FinishProductName
		o.FinishProductCraft = product.FinishProductCraft
		o.FinishProductIngredient = product.FinishProductIngredient
		o.MeasurementUnitId = product.MeasurementUnitId
		o.MeasurementUnitName = product.MeasurementUnitName
	}
	// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
	// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
	o.Remark = stockProductDetail.Remark
	o.InternalRemark = stockProductDetail.InternalRemark
	o.BarCode = stockProductDetail.BarCode
	o.QrCode = tools.String2Utf8(stockProductDetail.QrCode)

	o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
		dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)
	data = o
	return
}

func (u *StockProductRepo) GetDetailList(ctx context.Context, req *structure.GetStockProductDetailListQuery) (list structure.GetStockProductDetailDataList, total int, err error) {
	var (
		stockProductDetails model.StockProductDetailList
		productSvc          = product2.NewProductClient()
		productColorSvc     = product2.NewProductColorClient()
		warehouseSvc        = warehouse.NewPhysicalWarehouseClient()
		fpmCostPriceList    structure.GetFpmCostPriceDataList
	)

	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}
	if req.WarehouseBinName != "" {
		req.WarehouseBinIds, err = warehouseSvc.GetWarehouseBinIdsByCodeOrName(ctx, nil, []string{req.WarehouseBinName})
		if err != nil {
			return
		}
	}
	stockProductDetails, total, err = mysql.SearchStockProductDetail(u.tx, req)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetails, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetails, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_id")
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetails, "product_id")
	// productSvc := product2.NewProductClient()
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	productKindNameIds := mysql_base.GetUInt64List(stockProductDetails, "product_kind_id")
	productKindNameSvc := type_basic_data.NewTypeFabricClient()
	productKindName, err := productKindNameSvc.GetTypeFabricNameByIds(ctx, productKindNameIds)
	if err != nil {
		return
	}

	dicIds := mysql_base.GetUInt64List(stockProductDetails, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	dyelotNumbers := make([]string, 0, len(stockProductDetails))
	warehouseGoodInIds := make([]uint64, 0, len(stockProductDetails))
	warehouseGoodInTypes := make([]common_product.WarehouseGoodInType, 0, len(stockProductDetails))
	warehouseGoodInTypesInt := make([]int, 0, len(stockProductDetails))
	for _, stockProductDetail := range stockProductDetails {
		dyelotNumbers = append(dyelotNumbers, stockProductDetail.DyelotNumber)
		warehouseGoodInTypes = append(warehouseGoodInTypes, stockProductDetail.WarehouseInType)
		warehouseGoodInTypesInt = append(warehouseGoodInTypesInt, int(stockProductDetail.WarehouseInType))
		if stockProductDetail.WarehouseInType != common_product.WarehouseGoodInTypeProcess {
			warehouseGoodInIds = append(warehouseGoodInIds, stockProductDetail.WarehouseInOrderId)
		} else {
			warehouseGoodInIds = append(warehouseGoodInIds, 0)
		}
	}
	tools.RemoveRepeatedElementV2(warehouseGoodInIds)
	tools.RemoveRepeatedElementV3(dyelotNumbers)
	tools.RemoveRepeatedElement(warehouseGoodInTypesInt)
	costPriceRepo := NewFpmCostPriceRepo(ctx, u.tx, false)
	fpmCostPriceList, _, err = costPriceRepo.GetList(ctx, &structure.GetFpmCostPriceListQuery{
		ProductIds:           productIds,
		ColourIds:            productColorIds,
		DyelotNumbers:        dyelotNumbers,
		WarehouseGoodInIds:   warehouseGoodInIds,
		WarehouseGoodInTypes: warehouseGoodInTypes,
	})
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetails, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	for _, stockProductDetail := range stockProductDetails.List() {
		o := structure.GetStockProductDetailData{}
		warehouseGoodInId := stockProductDetail.WarehouseInOrderId
		if stockProductDetail.WarehouseInType == common_product.WarehouseGoodInTypeProcess {
			warehouseGoodInId = 0
		}
		fpmCostPrice := fpmCostPriceList.Pick(stockProductDetail.ProductId, stockProductDetail.ProductColorId,
			warehouseGoodInId, stockProductDetail.WarehouseInType, stockProductDetail.DyelotNumber)
		o.BuoyantWeightPrice = fpmCostPrice.BuoyantWeightPrice
		o.NetWeightPrice = fpmCostPrice.NetWeightPrice
		o.FpmCostPriceId = fpmCostPrice.Id
		o.Id = stockProductDetail.Id
		o.CreateTime = tools.MyTime(stockProductDetail.CreateTime)
		o.UpdateTime = tools.MyTime(stockProductDetail.UpdateTime)
		o.CreatorId = stockProductDetail.CreatorId
		o.CreatorName = stockProductDetail.CreatorName
		o.UpdaterId = stockProductDetail.UpdaterId
		o.UpdateUserName = stockProductDetail.UpdaterName
		o.StockProductId = stockProductDetail.StockProductId
		o.WarehouseBinId = stockProductDetail.WarehouseBinId
		o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
		o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		o.WarehouseInType = stockProductDetail.WarehouseInType
		o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
		o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
		o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
		o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
		o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
		o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		o.ProductColorId = stockProductDetail.ProductColorId
		o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
		o.ProductColorKindId = stockProductDetail.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
		o.WarehouseId = stockProductDetail.WarehouseId
		o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
		o.CustomerId = stockProductDetail.CustomerId
		o.CustomerName = bizUnit[stockProductDetail.CustomerId]
		o.ProductId = stockProductDetail.ProductId
		o.ProductLevelId = stockProductDetail.ProductLevelId
		o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
		o.DyelotNumber = stockProductDetail.DyelotNumber
		o.VolumeNumber = stockProductDetail.VolumeNumber
		o.ProductRemark = stockProductDetail.ProductRemark
		o.WeightError = stockProductDetail.WeightError
		o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		o.Weight = stockProductDetail.Weight
		o.Length = stockProductDetail.Length
		o.ShelfNo = stockProductDetail.ShelfNo
		o.Roll = stockProductDetail.Roll
		o.StockRoll = stockProductDetail.Roll
		o.AvailableRoll = stockProductDetail.Roll
		o.AvailableWeight = stockProductDetail.Weight
		o.DigitalCode = stockProductDetail.DigitalCode
		o.ContractNumber = stockProductDetail.ContractNumber
		o.CustomerPoNum = stockProductDetail.CustomerPoNum
		o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
		o.Remark = stockProductDetail.Remark
		o.InternalRemark = stockProductDetail.InternalRemark
		o.BarCode = stockProductDetail.BarCode
		o.QrCode = tools.String2Utf8(stockProductDetail.QrCode)
		o.StockStatus = stockProductDetail.Status
		o.StockStatusName = stockProductDetail.Status.String()
		o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)

		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.WeavingOrganizationId = product.WeavingOrganizationId
			o.WeavingOrganizationCode = product.WeavingOrganizationCode
			o.WeavingOrganizationName = product.WeavingOrganizationName
			o.Density = product.Density
			o.YarnCount = product.YarnCount
			o.BleachId = product.BleachId
			o.BleachName = product.BleachName
			o.PrintDate = time.Now().Format("2006-01-02")
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
		}
		// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
		o.ProductKindId = stockProductDetail.ProductKindId
		o.ProductKindName = productKindName[stockProductDetail.ProductKindId]
		list = append(list, o)
	}
	return
}

// 获取成品缸号库存分组列表
func (u *StockProductRepo) GetStockProductDyelotNumberList(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error) {
	var (
		stockProductDetails mysql.StockDyelotNumberDetailList
		productSvc          = product2.NewProductClient()
		productColorSvc     = product2.NewProductColorClient()
		dicSvc              = dictionary.NewDictionaryClient()
	)
	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}

	if req.WarehouseId != 0 {
		req.WarehouseIds, err = productColorSvc.GetWarehouseId(ctx, req.WarehouseId)
	}

	if req.ProductColorId != 0 {
		req.ProductColorIds = mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	}

	stockProductDetails, total, err = mysql.GetStockProductDyelotNumberList(u.tx, req)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetails, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetails, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetails, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetails, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	dicIds := mysql_base.GetUInt64List(stockProductDetails, "dictionary_detail_id")
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	for _, stockProductDetail := range stockProductDetails {
		o := structure.GetStockProductDetailDyelotNumberData{}
		o.Id = stockProductDetail.StockProductId
		o.StockProductId = stockProductDetail.StockProductId
		o.WarehouseBinId = stockProductDetail.WarehouseBinId
		o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
		o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		o.WarehouseInType = stockProductDetail.WarehouseInType
		o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
		o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
		o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
		o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
		o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
		o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		o.ProductColorId = stockProductDetail.ProductColorId
		o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
		o.ProductColorKindId = stockProductDetail.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
		o.WarehouseId = stockProductDetail.WarehouseId
		o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
		o.CustomerId = stockProductDetail.CustomerId
		o.CustomerName = bizUnit[stockProductDetail.CustomerId]
		o.ProductId = stockProductDetail.ProductId
		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.WeavingOrganizationId = product.WeavingOrganizationId
			o.WeavingOrganizationCode = product.WeavingOrganizationCode
			o.WeavingOrganizationName = product.WeavingOrganizationName
			o.Density = product.Density
			o.YarnCount = product.YarnCount
			// 库存计量单位取基础资料的
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
		}
		// 处理幅宽克重
		o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)

		o.ProductLevelId = stockProductDetail.ProductLevelId
		o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
		o.DyelotNumber = stockProductDetail.DyelotNumber
		o.VolumeNumber = stockProductDetail.VolumeNumber
		o.ProductRemark = stockProductDetail.ProductRemark
		o.WeightError = stockProductDetail.WeightError
		o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		o.Weight = stockProductDetail.Weight
		o.Length = stockProductDetail.Length
		o.Roll = stockProductDetail.Roll
		o.StockRoll = stockProductDetail.Roll
		o.BookRoll = stockProductDetail.Roll - stockProductDetail.AvailableRoll
		o.AvailableRoll = stockProductDetail.AvailableRoll
		o.AvailableWeight = stockProductDetail.AvailableWeight
		o.DigitalCode = stockProductDetail.DigitalCode
		o.ContractNumber = stockProductDetail.ContractNumber
		o.CustomerPoNum = stockProductDetail.CustomerPoNum
		o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
		o.Remark = stockProductDetail.Remark
		// o.BookRoll = stockProductDetail.BookRoll
		list = append(list, o)
	}
	return
}

// 获取成品缸号库存分组列表
func (u *StockProductRepo) GetStockProductDyelotNumberListV2(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error) {
	var (
		stockProductDetails mysql.StockDyelotNumberDetailList
		productSvc          = product2.NewProductClient()
		productColorSvc     = product2.NewProductColorClient()
	)

	if req.ProductColorId != 0 {
		req.ProductColorIds = mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	}

	stockProductDetails, total, err = mysql.GetStockProductDyelotNumberListV2(u.tx, req)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetails, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetails, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetails, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetails, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }
	dicIds := mysql_base.GetUInt64List(stockProductDetails, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	for _, stockProductDetail := range stockProductDetails {
		o := structure.GetStockProductDetailDyelotNumberData{}
		o.Id = stockProductDetail.StockProductId
		o.StockProductId = stockProductDetail.StockProductId
		o.WarehouseBinId = stockProductDetail.WarehouseBinId
		o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
		o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		o.WarehouseInType = stockProductDetail.WarehouseInType
		o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
		o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
		o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
		o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
		o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
		o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		o.ProductColorId = stockProductDetail.ProductColorId
		o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
		o.ProductColorKindId = stockProductDetail.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
		o.WarehouseId = stockProductDetail.WarehouseId
		o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
		o.CustomerId = stockProductDetail.CustomerId
		o.CustomerName = bizUnit[stockProductDetail.CustomerId]
		o.ProductId = stockProductDetail.ProductId
		// 处理幅宽克重
		o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)

		o.ProductLevelId = stockProductDetail.ProductLevelId
		o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
		o.DyelotNumber = stockProductDetail.DyelotNumber
		o.VolumeNumber = stockProductDetail.VolumeNumber
		o.ProductRemark = stockProductDetail.ProductRemark
		o.WeightError = stockProductDetail.WeightError
		o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		o.Weight = stockProductDetail.Weight
		o.Length = stockProductDetail.Length
		o.Roll = stockProductDetail.Roll
		o.StockRoll = stockProductDetail.Roll
		o.BookRoll = stockProductDetail.Roll - stockProductDetail.AvailableRoll
		o.AvailableRoll = stockProductDetail.AvailableRoll
		o.AvailableWeight = stockProductDetail.AvailableWeight
		o.DigitalCode = stockProductDetail.DigitalCode
		o.ContractNumber = stockProductDetail.ContractNumber
		o.CustomerPoNum = stockProductDetail.CustomerPoNum
		o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
		}
		// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
		o.Remark = stockProductDetail.Remark
		// o.BookRoll = stockProductDetail.BookRoll
		list = append(list, o)
	}
	return
}

// 获取详情缸号分组列表
func (u *StockProductRepo) GetDyelotNumberDetailList(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error) {
	var (
		stockProductDetails      mysql.StockDyelotNumberDetailList
		salePriceColorKindRels   salePriceModel.SalePriceColorKindRelList
		salePriceColorKinds      salePriceModel.SalePriceColorKindList
		customerSalePriceAdjusts salePriceModel.CustomerSalePriceAdjustList
		saleLevelPrices          salePriceModel.SalePriceLevelList
		saleLevel                map[uint64]string
		productSvc               = product2.NewProductClient()
		productColorSvc          = product2.NewProductColorClient()
		saleSystem               sale_sys_pb.Res
	)
	saleSystem, _ = sale_sys_pb.NewSaleSystemClient().GetSaleSystemDetailById(ctx, req.SaleSystemId)

	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}

	stockProductDetails, total, err = mysql.SearchStockProductDyelotNumberDetail(u.tx, req)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetails, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetails, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetails, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	dicIds := mysql_base.GetUInt64List(stockProductDetails, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetails, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	if req.WithPrice {
		salePriceColorKindRels, err = salePriceMysql.FindSalePriceColorKindRelByProductAndColorKind(u.tx, productIds, productColorKindIds)
		if err != nil {
			return
		}

		salePriceColorKindRelIds := mysql_base.GetUInt64List(salePriceColorKindRels, "sale_price_color_kind_rel_id")
		versions := mysql_base.GetIntList(salePriceColorKindRels, "version")

		// 获取正在生效以及下一次生效的版本组合salePriceColorKindRelId查询
		salePriceColorKinds, err = salePriceMysql.FindSalePriceColorKindByParentIdsAndVersions(u.tx, salePriceColorKindRelIds, versions)
		if err != nil {
			return
		}

		salePriceColorKindIds := mysql_base.GetUInt64List(salePriceColorKinds, "sale_price_color_kind_id")
		saleLevelPrices, err = salePriceMysql.FindSalePriceLevelByParentIDs(u.tx, salePriceColorKindIds)
		if err != nil {
			return
		}

		// customerIds := mysql_base.GetUInt64ListV2("customer_id", req)
		customerSalePriceAdjusts, err = salePriceMysql.FindCustomerSalePriceAdjustByCustomerIds(u.tx, []uint64{req.SaleCustomerId})
		if err != nil {
			return
		}

		saleLevelSvc := sale_price.NewSaleLevelClient()
		saleLevel, err = saleLevelSvc.GetAllEnableSaleLevel(ctx)
		if err != nil {
			return
		}
	}

	for _, stockProductDetail := range stockProductDetails {
		o := structure.GetStockProductDetailDyelotNumberData{}
		o.Id = stockProductDetail.StockProductId
		o.StockProductId = stockProductDetail.StockProductId
		o.WarehouseBinId = stockProductDetail.WarehouseBinId
		o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
		o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		o.WarehouseInType = stockProductDetail.WarehouseInType
		o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
		o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
		o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
		o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
		o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
		o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		o.ProductColorId = stockProductDetail.ProductColorId
		o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
		o.ProductColorKindId = stockProductDetail.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
		o.WarehouseId = stockProductDetail.WarehouseId
		o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
		o.CustomerId = stockProductDetail.CustomerId
		o.CustomerName = bizUnit[stockProductDetail.CustomerId]
		o.ProductId = stockProductDetail.ProductId
		o.ProductLevelId = stockProductDetail.ProductLevelId
		o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
		o.DyelotNumber = stockProductDetail.DyelotNumber
		o.VolumeNumber = stockProductDetail.VolumeNumber
		o.ProductRemark = stockProductDetail.ProductRemark
		o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		o.Weight = stockProductDetail.Weight
		o.Length = stockProductDetail.Length
		o.Roll = stockProductDetail.Roll
		o.StockRoll = stockProductDetail.Roll
		o.AvailableRoll = stockProductDetail.AvailableRoll
		o.AvailableWeight = stockProductDetail.AvailableWeight
		o.DigitalCode = stockProductDetail.DigitalCode
		o.ContractNumber = stockProductDetail.ContractNumber
		o.CustomerPoNum = stockProductDetail.CustomerPoNum
		o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
		o.BookRoll = o.StockRoll - o.AvailableRoll
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
			o.StandardWeight = product.StandardWeight
		}
		// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
		o.Remark = stockProductDetail.Remark
		// o.BookRoll = stockProductDetail.BookRoll
		// 是否带上价格
		if req.WithPrice {
			salePriceColorKind := salePriceColorKinds.Pick(stockProductDetail.ProductId, stockProductDetail.ProductColorKindId)
			salePrice := salePriceAggs.MPCalcPurchaserLadderSalePrice(
				ctx,
				u.tx,
				saleLevel,
				salePriceColorKind,
				customerSalePriceAdjusts,
				saleLevelPrices,
				req.SaleCustomerId,
				stockProductDetail.ProductId,
				stockProductDetail.ProductColorKindId,
				stockProductDetail.ProductColorId,
				saleSystem,
			)
			if product, ok := productMap[stockProductDetail.ProductId]; ok {
				salePrice.WeightError = product.WeightError
			}
			o.SalePrice = salePrice
		}
		o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)
		list = append(list, o)
	}
	return
}

// 获取详情细码选择列表
func (u *StockProductRepo) GetWeightDetailList(ctx context.Context, req *structure.GetStockProductWeightDetailListQuery) (list structure.GetStockProductDetailWeightDataList, total int, err error) {
	var (
		stockProductDetails      mysql.StockDetailList
		salePriceColorKindRels   salePriceModel.SalePriceColorKindRelList
		salePriceColorKinds      salePriceModel.SalePriceColorKindList
		customerSalePriceAdjusts salePriceModel.CustomerSalePriceAdjustList
		saleLevelPrices          salePriceModel.SalePriceLevelList
		saleLevel                map[uint64]string
	)
	stockProductDetails, total, err = mysql.SearchStockProductWeightDetail(u.tx, req)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetails, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetails, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetails, "product_id")
	productSvc := product2.NewProductClient()
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	dicIds := mysql_base.GetUInt64List(stockProductDetails, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetails, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	if req.WithPrice {
		salePriceColorKindRels, err = salePriceMysql.FindSalePriceColorKindRelByProductAndColorKind(u.tx, productIds, productColorKindIds)
		if err != nil {
			return
		}

		salePriceColorKindRelIds := mysql_base.GetUInt64List(salePriceColorKindRels, "sale_price_color_kind_rel_id")
		versions := mysql_base.GetIntList(salePriceColorKindRels, "version")

		// 获取正在生效以及下一次生效的版本组合salePriceColorKindRelId查询
		salePriceColorKinds, err = salePriceMysql.FindSalePriceColorKindByParentIdsAndVersions(u.tx, salePriceColorKindRelIds, versions)
		if err != nil {
			return
		}

		salePriceColorKindIds := mysql_base.GetUInt64List(salePriceColorKinds, "sale_price_color_kind_id")
		saleLevelPrices, err = salePriceMysql.FindSalePriceLevelByParentIDs(u.tx, salePriceColorKindIds)
		if err != nil {
			return
		}

		customerIds := mysql_base.GetUInt64List(req, "customer_id")
		customerSalePriceAdjusts, err = salePriceMysql.FindCustomerSalePriceAdjustByCustomerIds(u.tx, customerIds)
		if err != nil {
			return
		}

		saleLevelSvc := sale_price.NewSaleLevelClient()
		saleLevel, err = saleLevelSvc.GetAllEnableSaleLevel(ctx)
		if err != nil {
			return
		}
	}

	for _, stockProductDetail := range stockProductDetails {
		o := structure.GetStockProductDetailWeightData{}
		o.Id = stockProductDetail.Id
		o.StockProductId = stockProductDetail.StockProductId
		o.WarehouseBinId = stockProductDetail.WarehouseBinId
		o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
		o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		o.WarehouseInType = stockProductDetail.WarehouseInType
		o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
		o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
		o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
		o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
		o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
		o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		o.ProductColorId = stockProductDetail.ProductColorId
		o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
		o.ProductColorKindId = stockProductDetail.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
		o.WarehouseId = stockProductDetail.WarehouseId
		o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
		o.CustomerId = stockProductDetail.CustomerId
		o.CustomerName = bizUnit[stockProductDetail.CustomerId]
		o.ProductId = stockProductDetail.ProductId
		o.ProductLevelId = stockProductDetail.ProductLevelId
		o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
		o.DyelotNumber = stockProductDetail.DyelotNumber
		o.VolumeNumber = stockProductDetail.VolumeNumber
		o.ProductRemark = stockProductDetail.ProductRemark
		o.WeightError = stockProductDetail.WeightError
		o.SettleErrorWeight = stockProductDetail.SettleErrorWeight
		o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		o.Weight = stockProductDetail.Weight
		o.Length = stockProductDetail.Length
		o.Roll = stockProductDetail.Roll
		o.StockRoll = stockProductDetail.Roll
		o.AvailableRoll = stockProductDetail.AvailableRoll
		o.AvailableWeight = stockProductDetail.AvailableWeight
		o.DigitalCode = stockProductDetail.DigitalCode
		o.ContractNumber = stockProductDetail.ContractNumber
		o.CustomerPoNum = stockProductDetail.CustomerPoNum
		o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
		o.ShelfNo = stockProductDetail.ShelfNo
		o.Barcode = stockProductDetail.BarCode
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
		}
		// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
		o.Remark = stockProductDetail.Remark
		o.InternalRemark = stockProductDetail.InternalRemark
		// 是否带上价格
		if req.WithPrice {
			salePriceColorKind := salePriceColorKinds.Pick(stockProductDetail.ProductId, stockProductDetail.ProductColorKindId)
			salePrice := salePriceAggs.CalcPurchaserLadderSalePrice(
				ctx,
				u.tx,
				saleLevel,
				salePriceColorKind,
				customerSalePriceAdjusts,
				saleLevelPrices,
				stockProductDetail.ProductId,
				stockProductDetail.ProductColorKindId,
				stockProductDetail.ProductColorId,
			)
			o.SalePrice = salePrice
		}
		o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)
		list = append(list, o)
	}
	return
}

func (u *StockProductRepo) GetDetailDropdownList(ctx context.Context, req *structure.GetStockProductDetailListQuery) (list structure.GetStockProductDetailDropdownDataList, total int, err error) {
	var (
		stockProductDetails model.StockProductDetailList
		productSvc          = product2.NewProductClient()
		productColorSvc     = product2.NewProductColorClient()
	)
	if req.ProductCode != "" || req.ProductName != "" {
		req.ProductIds, err = productSvc.GetProductIds(ctx, req.ProductCode, req.ProductName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCode != "" || req.ProductColorName != "" {
		req.ProductColorIds, err = productColorSvc.GetProductColorIds(ctx, req.ProductColorCode, req.ProductColorName, "")
		if err != nil {
			return
		}
	}
	stockProductDetails, total, err = mysql.SearchStockProductDetail(u.tx, req)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetails, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetails, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetails, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetails, "product_id")
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetails, "product_color_id")
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	dicIds := mysql_base.GetUInt64List(stockProductDetails, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetails, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	for _, stockProductDetail := range stockProductDetails.List() {
		o := structure.GetStockProductDetailDropdownData{}
		o.Id = stockProductDetail.Id
		o.CreateTime = tools.MyTime(stockProductDetail.CreateTime)
		o.UpdateTime = tools.MyTime(stockProductDetail.UpdateTime)
		o.CreatorId = stockProductDetail.CreatorId
		o.CreatorName = stockProductDetail.CreatorName
		o.UpdaterId = stockProductDetail.UpdaterId
		o.UpdateUserName = stockProductDetail.UpdaterName
		o.StockProductId = stockProductDetail.StockProductId
		o.WarehouseBinId = stockProductDetail.WarehouseBinId
		o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
		o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		o.WarehouseInType = stockProductDetail.WarehouseInType
		o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
		o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
		o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
		o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
		o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
		o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		o.ProductColorId = stockProductDetail.ProductColorId
		o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
		o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
		o.ProductColorKindId = stockProductDetail.ProductColorKindId
		o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
		o.WarehouseId = stockProductDetail.WarehouseId
		o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
		o.CustomerId = stockProductDetail.CustomerId
		o.CustomerName = bizUnit[stockProductDetail.CustomerId]
		o.ProductId = stockProductDetail.ProductId
		o.ProductLevelId = stockProductDetail.ProductLevelId
		o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
		o.DyelotNumber = stockProductDetail.DyelotNumber
		o.VolumeNumber = stockProductDetail.VolumeNumber
		o.ProductRemark = stockProductDetail.ProductRemark
		o.WeightError = stockProductDetail.WeightError
		o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		o.Weight = stockProductDetail.Weight
		o.Length = stockProductDetail.Length
		o.Roll = stockProductDetail.Roll
		o.StockRoll = stockProductDetail.Roll
		o.DigitalCode = stockProductDetail.DigitalCode
		o.ContractNumber = stockProductDetail.ContractNumber
		o.CustomerPoNum = stockProductDetail.CustomerPoNum
		o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
		// 库存计量单位取基础资料的
		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.FinishProductCraft = product.FinishProductCraft
			o.FinishProductIngredient = product.FinishProductIngredient
			o.MeasurementUnitId = product.MeasurementUnitId
			o.MeasurementUnitName = product.MeasurementUnitName
		}
		// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
		// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
		o.Remark = stockProductDetail.Remark
		o.InternalRemark = stockProductDetail.InternalRemark
		o.ShelfNo = stockProductDetail.ShelfNo
		o.QualityCheckStatus = stockProductDetail.QualityCheckStatus
		o.QualityCheckStatusName = stockProductDetail.QualityCheckStatus.String()
		o.BarCode = stockProductDetail.BarCode
		o.QrCode = tools.String2Utf8(stockProductDetail.QrCode)

		o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
			dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)
		list = append(list, o)
	}
	return
}

// 返回库存（配布单取消了出货单）
func (u *StockProductRepo) UpdateDetailStatusArrangeStatusWarehouseIn(ctx context.Context, ids []uint64) (stockProductIds []uint64, err error) {
	var (
		stockProductDetails model.StockProductDetailList
	)

	stockProductDetails, err = mysql.FindStockProductDetailByIDs(u.tx, ids)
	if err != nil {
		return
	}

	for _, stockProductDetail := range stockProductDetails {
		if stockProductDetail.Status != common.StockStatusArrange {
			err = middleware.WarnLog(
				errors.NewCustomError(errors.ErrCodeWasArrangedOrWarehouseOut,
					fmt.Sprintf("%v%v%v%v",
						"缸号:",
						stockProductDetail.DyelotNumber,
						"卷号:",
						stockProductDetail.VolumeNumber,
					),
				),
			)
			return
		}
		stockProductDetail.UpdateStockProductDetailStatusIn(ctx)

		stockProductDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockProductDetail)
		if err != nil {
			return
		}
	}

	stockProductIds = mysql_base.GetUInt64List(stockProductDetails, "stock_product_id")

	return
}

// 缸号库存-进出情况
func (u *StockProductRepo) GetDyeNumberAndColorStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (data structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {

	var (
		// outItemFcQuery  structure.GetFpmOutOrderItemFcListQuery
		list = make(structure.GetStockProductOutAndInDetailDataList, 0)
		// o               model.StockProductOutAndInDetailList
		o               mysql.UnionOutAndInBaseList
		inModelList     model.FpmInOrderItemFcList
		outModelList    model.FpmOutOrderItemFcList
		totalRWL        = make([]int, 3)
		detailStockList model.StockProductDetailList
		detailStockIds  = make([]uint64, 0)
	)

	// if q.BeginTime == "" && q.EndTime == "" {
	//	q.BeginTime = tools.QueryTime(time.Now().AddDate(0, -1, 0).Format("2006-01-02 15:04:05"))
	//	q.EndTime = tools.QueryTime(time.Now().Format("2006-01-02 15:04:05"))
	// }
	detailStockList, err = mysql.FindStockProductByDyelotNumberAndColorList(u.tx, q)
	if err != nil {
		return
	}
	// 详细库存ids
	detailStockIds = detailStockList.GetIds()

	// 汇总日期前的入库数据 下标：0 roll ; 1 weight ; 2 length
	inModelList, err = mysql.DetailWarehouseInDataRWLByDetailIDs(u.tx, q, detailStockIds)
	if err != nil {
		return
	}
	inTotalRWL := inModelList.GetTotalRWL()
	// 汇总日期前的出库数据
	outModelList, err = mysql.DetailWarehouseOutDataRWLByDetailIDs(u.tx, q, detailStockIds)
	if err != nil {
		return
	}
	outTotalRWL := outModelList.GetTotalRWL()
	// 获取盘点数据
	checkWeightList, err := mysql.DetailWarehouseCheckDataRWLByDetailIDs(u.tx, q, detailStockIds)
	if err != nil {
		return
	}
	checkTotalRWL := checkWeightList.GetTotalRWL()

	// 上期结余
	totalRWL[0] = inTotalRWL[0] - outTotalRWL[0] + checkTotalRWL[0]
	totalRWL[1] = inTotalRWL[1] - outTotalRWL[1] + checkTotalRWL[1]
	totalRWL[2] = inTotalRWL[2] - outTotalRWL[2] + checkTotalRWL[2]
	data.TotalRoll = totalRWL[0]
	data.TotalWeight = totalRWL[1]
	data.TotalLength = totalRWL[2]

	// 获取日期范围数据
	q.Ids = detailStockIds
	o, count, err = mysql.FindWarehouseOutAndInSumList(u.tx, q)
	if err != nil {
		return
	}
	for _, src := range o {
		dst := structure.GetStockProductOutAndInDetailData{}
		totalRWL[0] = totalRWL[0] + src.InRoll - src.OutRoll + src.DifferenceRoll
		totalRWL[1] = totalRWL[1] + src.InWeight - src.OutWeight + src.DifferenceWeight
		totalRWL[2] = totalRWL[2] + src.InLength - src.OutLength + src.DifferenceLength
		u.swapStockProductOutAndInDetail2GetData(src, &dst, totalRWL)
		list = append(list, dst)
	}

	data.List = list
	return

}

// 详细库存-进出情况
func (u *StockProductRepo) GetDetailStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (data structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {
	var (
		// outItemFcQuery  structure.GetFpmOutOrderItemFcListQuery
		list         = make(structure.GetStockProductOutAndInDetailDataList, 0)
		o            mysql.UnionOutAndInBaseList
		inModelList  model.FpmInOrderItemFcList
		outModelList model.FpmOutOrderItemFcList
		totalRWL     = make([]int, 3)
	)

	// if q.BeginTime == "" && q.EndTime == "" {
	//	q.BeginTime = tools.QueryTime(time.Now().AddDate(0, -1, 0).Format("2006-01-02 15:04:05"))
	//	q.EndTime = tools.QueryTime(time.Now().Format("2006-01-02 15:04:05"))
	// }

	// 汇总日期前的入库数据 下标：0 roll ; 1 weight ; 2 length
	inModelList, err = mysql.DetailWarehouseInDataRWLByDetailID(u.tx, q)
	if err != nil {
		return
	}
	inTotalRWL := inModelList.GetTotalRWL()
	// 汇总日期前的出库数据
	outModelList, err = mysql.DetailWarehouseOutDataRWLByDetailID(u.tx, q)
	if err != nil {
		return
	}
	outTotalRWL := outModelList.GetTotalRWL()
	// 获取盘点数据
	checkWeightList, err := mysql.DetailWarehouseDataRWLByDetailID(u.tx, q)
	if err != nil {
		return
	}
	checkTotalRWL := checkWeightList.GetTotalRWL()

	// 上期结余
	totalRWL[0] = inTotalRWL[0] - outTotalRWL[0] + checkTotalRWL[0]
	totalRWL[1] = inTotalRWL[1] - outTotalRWL[1] + checkTotalRWL[1]
	totalRWL[2] = inTotalRWL[2] - outTotalRWL[2] + checkTotalRWL[2]
	data.TotalRoll = totalRWL[0]
	data.TotalWeight = totalRWL[1]
	data.TotalLength = totalRWL[2]

	// 获取日期范围数据
	o, count, err = mysql.FindWarehouseOutAndInList(u.tx, q)
	if err != nil {
		return
	}
	for _, src := range o {
		dst := structure.GetStockProductOutAndInDetailData{}
		totalRWL[0] = totalRWL[0] + src.InRoll - src.OutRoll + src.DifferenceRoll
		totalRWL[1] = totalRWL[1] + src.InWeight - src.OutWeight + src.DifferenceWeight
		totalRWL[2] = totalRWL[2] + src.InLength - src.OutLength + src.DifferenceLength
		u.swapStockProductOutAndInDetail2GetData(src, &dst, totalRWL)
		list = append(list, dst)
	}

	data.List = list

	return
}

// QC quality check 更新质检状态
func (u *StockProductRepo) UpdateDetailQCStatus(ctx context.Context, id uint64, qcStatus common_product.QualityCheckStatus) (err error) {
	var (
		stockProductDetail model.StockProductDetail
	)

	stockProductDetail, err = mysql.MustFirstStockProductDetailByID(u.tx, id)
	if err != nil {
		return
	}
	if stockProductDetail.QualityCheckStatus == qcStatus {
		return
	}
	stockProductDetail.QualityCheckStatus = qcStatus
	// if stockProductDetail.QualityCheckStatus != qcStatus {
	stockProductDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockProductDetail)
	if err != nil {
		return
	}
	// }
	return
}

// 根据条码获取库存详情
func (u *StockProductRepo) GetDetailByCond(ctx context.Context, req *structure.GetStockProductDetailListQuery) (data structure.GetStockProductDetailDropdownData, err error) {
	var (
		stockProductDetail model.StockProductDetail
	)
	stockProductDetail, err = mysql.MustFirstStockProductDetailByCond(u.tx, req)

	bizUnitIds := mysql_base.GetUInt64List(stockProductDetail, "biz_unit_id")
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	productLevelIds := mysql_base.GetUInt64List(stockProductDetail, "product_level_id")
	productLevelSvc := info_basic_data.NewInfoBaseFinishedProductLevelClient()
	productLevel, err := productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds)
	if err != nil {
		return
	}

	productColorKindIds := mysql_base.GetUInt64List(stockProductDetail, "product_color_kind_id")
	productColorKindSvc := type_basic_data.NewTypeFinishedProductColorClient()
	productColorKind, err := productColorKindSvc.GetTypeFinishedProductColorNameByIds(ctx, productColorKindIds)
	if err != nil {
		return
	}

	warehouseIds := mysql_base.GetUInt64List(stockProductDetail, "warehouse_id")
	warehouseSvc := warehouse.NewPhysicalWarehouseClient()
	warehouseName, err := warehouseSvc.GetPhysicalWarehouseByIds(ctx, warehouseIds)
	if err != nil {
		return
	}

	warehouseBinIds := mysql_base.GetUInt64List(stockProductDetail, "warehouse_bin_id")
	warehouseBin, err := warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, warehouseBinIds)
	if err != nil {
		return
	}

	productIds := mysql_base.GetUInt64List(stockProductDetail, "product_id")
	productSvc := product2.NewProductClient()
	productMap, err := productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(stockProductDetail, "product_color_id")
	productColorSvc := product2.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorItemByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	userIds := mysql_base.GetUInt64List(stockProductDetail, "user_id")
	userSvc := user.NewUserClient()
	user, err := userSvc.GetUserNameByIds(ctx, userIds)
	if err != nil {
		return
	}

	dicIds := mysql_base.GetUInt64List(stockProductDetail, "dictionary_detail_id")
	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)
	if err != nil {
		return
	}

	// measurementUnitIds := mysql_base.GetUInt64List(stockProductDetail, "measurement_unit_id")
	// measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	// measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	// if err != nil {
	// 	return
	// }

	o := structure.GetStockProductDetailDropdownData{}
	o.Id = stockProductDetail.Id
	o.CreateTime = tools.MyTime(stockProductDetail.CreateTime)
	o.UpdateTime = tools.MyTime(stockProductDetail.UpdateTime)
	o.CreatorId = stockProductDetail.CreatorId
	o.CreatorName = stockProductDetail.CreatorName
	o.UpdaterId = stockProductDetail.UpdaterId
	o.UpdateUserName = stockProductDetail.UpdaterName
	o.StockProductId = stockProductDetail.StockProductId
	o.WarehouseBinId = stockProductDetail.WarehouseBinId
	o.WarehouseBinName = warehouseBin[stockProductDetail.WarehouseBinId]
	o.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
	o.WarehouseInType = stockProductDetail.WarehouseInType
	o.WarehouseInTypeName = stockProductDetail.WarehouseInType.String()
	o.WarehouseInOrderId = stockProductDetail.WarehouseInOrderId
	o.WarehouseInOrderNo = stockProductDetail.WarehouseInOrderNo
	o.CheckTime = tools.MyTime(stockProductDetail.CheckTime)
	o.CheckUserId = stockProductDetail.CheckUserId
	o.CheckUserName = user[stockProductDetail.CheckUserId]
	o.WarehouseOutOrderId = stockProductDetail.WarehouseOutOrderId
	o.WarehouseOutOrderNo = stockProductDetail.WarehouseOutOrderNo
	o.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
	o.ProductColorId = stockProductDetail.ProductColorId
	o.ProductColorCode = productColorItem[stockProductDetail.ProductColorId][0]
	o.ProductColorName = productColorItem[stockProductDetail.ProductColorId][1]
	o.ProductColorKindId = stockProductDetail.ProductColorKindId
	o.ProductColorKindName = productColorKind[stockProductDetail.ProductColorKindId]
	o.WarehouseId = stockProductDetail.WarehouseId
	o.WarehouseName = warehouseName[stockProductDetail.WarehouseId]
	o.CustomerId = stockProductDetail.CustomerId
	o.CustomerName = bizUnit[stockProductDetail.CustomerId]
	o.ProductId = stockProductDetail.ProductId
	// 处理幅宽克重
	o.BuildFPResp(stockProductDetail.FinishProductWidth, stockProductDetail.FinishProductGramWeight, dicNameMap[stockProductDetail.FinishProductWidthUnitId][1],
		dicNameMap[stockProductDetail.FinishProductGramWeightUnitId][1], stockProductDetail.FinishProductWidthUnitId, stockProductDetail.FinishProductGramWeightUnitId)

	o.ProductLevelId = stockProductDetail.ProductLevelId
	o.ProductLevelName = productLevel[stockProductDetail.ProductLevelId]
	o.DyelotNumber = stockProductDetail.DyelotNumber
	o.VolumeNumber = stockProductDetail.VolumeNumber
	o.ProductRemark = stockProductDetail.ProductRemark
	o.WeightError = stockProductDetail.WeightError
	o.PaperTubeWeight = stockProductDetail.PaperTubeWeight
	o.Weight = stockProductDetail.Weight
	o.Length = stockProductDetail.Length
	o.Roll = stockProductDetail.Roll
	o.StockRoll = stockProductDetail.Roll
	o.DigitalCode = stockProductDetail.DigitalCode
	o.ContractNumber = stockProductDetail.ContractNumber
	o.CustomerPoNum = stockProductDetail.CustomerPoNum
	o.CustomerAccountNum = stockProductDetail.CustomerAccountNum
	// 库存计量单位取基础资料的
	if product, ok := productMap[stockProductDetail.ProductId]; ok {
		o.ProductCode = product.FinishProductCode
		o.ProductName = product.FinishProductName
		o.FinishProductCraft = product.FinishProductCraft
		o.FinishProductIngredient = product.FinishProductIngredient
		o.MeasurementUnitId = product.MeasurementUnitId
		o.MeasurementUnitName = product.MeasurementUnitName
	}
	// o.MeasurementUnitId = stockProductDetail.MeasurementUnitId
	// o.MeasurementUnitName = measurementUnitName[stockProductDetail.MeasurementUnitId]
	o.Remark = stockProductDetail.Remark
	o.InternalRemark = stockProductDetail.InternalRemark
	o.BarCode = stockProductDetail.BarCode
	o.QrCode = tools.String2Utf8(stockProductDetail.QrCode)
	data = o
	return
}

// 根据QrCode查询对应仓库中的 库存和条码 是否存在，存在的话，则执行更新操作
func (u StockProductRepo) CheckStockProductDetailAndUpdate(ctx context.Context, req *structure.AddStockProductDetailParam) (id uint64, exist bool, err error) {
	var (
		fabricPieceCode model.FabricPieceCode
	)
	fabricPieceCode, exist, err = mysql.FirstFabricPieceCodeByQrCode(u.tx, req.WarehouseId, req.QrCode)
	if err != nil {
		return
	}
	if !exist {
		return
	}
	var (
		stockProductDetail model.StockProductDetail
	)
	stockProductDetail, exist, err = mysql.FirstStockProductDetailByQrCode(u.tx, req.WarehouseId, fabricPieceCode.QrCode)
	if err != nil {
		return
	}
	if !exist {
		return
	}
	if _stockProductDetail, ok := u.stockProductDetail[stockProductDetail.Id]; ok {
		stockProductDetail = _stockProductDetail
	}

	// 判断库存是否充足
	if stockProductDetail.Roll+req.Roll < 0 || stockProductDetail.Weight+req.Weight < 0 {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeNotEnoughStock),
			"缸号:",
			stockProductDetail.DyelotNumber,
			"卷号:",
			stockProductDetail.VolumeNumber,
		)
		return
	}

	stockProductDetail.UpdateStockProductDetail(ctx, &structure.UpdateStockProductDetailParam{
		Type:            6,
		Weight:          req.Weight,
		Length:          req.Length,
		Roll:            req.Roll,
		WarehouseBinId:  req.WarehouseBinId,
		WeightError:     req.WeightError,
		PaperTubeWeight: req.PaperTubeWeight,
	})
	stockProductDetail, err = mysql.MustUpdateStockProductDetail(u.tx, stockProductDetail)
	if err != nil {
		return
	}

	u.stockProductDetail[stockProductDetail.Id] = stockProductDetail
	id = stockProductDetail.Id
	return
}

type AIGetStockData struct {
	ProductCode      string `json:"product_code"`       // 面料编号
	ProductName      string `json:"product_name"`       // 面料名称
	ProductColorCode string `json:"product_color_code"` // 颜色编号
	ProductColorName string `json:"product_color_name"` // 颜色名称
	DyelotNumber     string `json:"dyelot_number"`      // 缸号
	StockRoll        int    `json:"stock_roll"`         // 库存匹数
	StockWeight      int    `json:"stock_weight"`       // 库存数量
	AvailableRoll    int    `json:"available_roll"`     // 可用匹数
	AvailableWeight  int    `json:"available_weight"`   // 可用数量
	Length           int    `json:"length"`             // 长度
	Warehouse        string `json:"warehouse"`          // 仓库
}

func (u StockProductRepo) AIGetStock(ctx context.Context, param structure.AIGetStockParam) (data structure.AIGetStockData, err error) {
	var (
		stockProductDetail model.StockProductDetail
		stockDetails       = make([]AIGetStockData, 0)
		conditionStrs      = make([]string, 0)
	)
	tx := u.tx.Model(&stockProductDetail)
	tx.Select("finish_product.finish_product_code as product_code, finish_product.finish_product_name as product_name," +
		" finish_product_color.product_color_code, finish_product_color.product_color_name, stock_product_detail.dyelot_number," +
		" SUM(IF(stock_product_detail.status in (1,2), stock_product_detail.roll, 0)) AS stock_roll," +
		" SUM(IF(stock_product_detail.status in (1,2), stock_product_detail.weight, 0)) AS stock_weight," +
		" SUM(IF(stock_product_detail.status = 1,stock_product_detail.roll, 0)) AS available_roll," +
		" SUM(IF(stock_product_detail.status = 1,stock_product_detail.weight, 0)) AS available_weight," +
		" SUM(stock_product_detail.length) as length,  physical_warehouse.name as warehouse")
	tx.Joins("LEFT JOIN finish_product ON finish_product.id = stock_product_detail.product_id")
	tx.Joins("LEFT JOIN finish_product_color ON finish_product_color.id = stock_product_detail.product_color_id")
	tx.Joins("LEFT JOIN physical_warehouse ON physical_warehouse.id = stock_product_detail.warehouse_id")
	if param.ProductCode != "" {
		conditionStrs = append(conditionStrs, fmt.Sprintf("面料编号：%s", param.ProductCode))
		tx.Where("finish_product.finish_product_code like ?", fmt.Sprintf("%%%s%%", param.ProductCode))
	}
	if param.ProductName != "" {
		conditionStrs = append(conditionStrs, fmt.Sprintf("面料名称：%s", param.ProductName))
		tx.Where("finish_product.finish_product_name like ?", fmt.Sprintf("%%%s%%", param.ProductName))
	}
	if param.ColorCode != "" {
		conditionStrs = append(conditionStrs, fmt.Sprintf("颜色编号：%s", param.ColorCode))
		tx.Where("finish_product_color.product_color_code like ?", fmt.Sprintf("%%%s%%", param.ColorCode))
	}
	if param.ColorName != "" {
		conditionStrs = append(conditionStrs, fmt.Sprintf("颜色名称：%s", param.ColorName))
		tx.Where("finish_product_color.product_color_name like ?", fmt.Sprintf("%%%s%%", param.ColorName))
	}
	if param.DyelotNumber != "" {
		conditionStrs = append(conditionStrs, fmt.Sprintf("缸号：%s", param.DyelotNumber))
		tx.Where("stock_product_detail.dyelot_number like ?", fmt.Sprintf("%%%s%%", param.DyelotNumber))
	}
	if param.Warehouse != "" {
		conditionStrs = append(conditionStrs, fmt.Sprintf("仓库：%s", param.Warehouse))
		tx.Where("physical_warehouse.name like ?", fmt.Sprintf("%%%s%%", param.Warehouse))
	}
	tx.Where("stock_product_detail.delete_time = '0000-00-00 00:00:00'")
	tx.Group("stock_product_detail.dyelot_number,stock_product_detail.product_id," +
		"stock_product_detail.product_color_id,stock_product_detail.warehouse_id")
	err = tx.Find(&stockDetails).Error()
	if err != nil {
		return
	}
	for _, detail := range stockDetails {
		data.List = append(data.List, structure.AIGetStockDataItem{
			Product:         tools.ProductOrColorJoin(detail.ProductCode, detail.ProductName),
			Color:           tools.ProductOrColorJoin(detail.ProductColorCode, detail.ProductColorName),
			DyelotNumber:    detail.DyelotNumber,
			StockRoll:       tools.Roll(detail.StockRoll).ToRoll(),
			StockWeight:     tools.Milligram(detail.StockWeight).ToKiloGram(),
			AvailableRoll:   tools.Roll(detail.AvailableRoll).ToRoll(),
			AvailableWeight: tools.Milligram(detail.AvailableWeight).ToKiloGram(),
			Length:          tools.Milligram(detail.Length).ToKiloGram(), // 长度也是放大10000倍
			Warehouse:       detail.Warehouse,
		})
	}
	data.Condition = strings.Join(conditionStrs, "，")
	return
}

func (u *StockProductRepo) GetDetailsByIds(ctx context.Context, ids []uint64) (list model.StockProductDetailList, err error) {
	list, err = mysql.FindStockProductDetailByIDs(u.tx, ids)
	return
}
