package qywx

import (
	"hcscm/structure/system"
	"hcscm/tools"
	"time"
)

type GetQYWXBoundListRequest struct {
	system.ListQuery
	TenantName           string `form:"tenant_name"`             // 账套名称
	Phone                string `form:"phone"`                   // 联系电话
	ContactPerson        string `form:"contact_person"`          // 联系人
	TobeDevelopedAppName string `form:"tobe_developed_app_name"` // 应用名称
}

type GetQYWXBoundListResponse struct {
	system.ResponseData
	List  []GetQYWXBoundListResponseItem `json:"list"`
	Total int                            `json:"total"`
}

type GetQYWXBoundListResponseItem struct {
	TenantID             uint64       `json:"tenant_id"`
	TenantName           string       `json:"tenant_name"`
	Phone                string       `json:"phone"`
	ContactPerson        string       `json:"contact_person"`
	TobeDevelopedAppID   uint64       `json:"tobe_developed_app_id" relate:"tobe_developed_app_id"`
	AgentID              int64        `json:"agent_id"`
	TobeDevelopedAppName string       `json:"tobe_developed_app_name"`
	RobotCode            string       `json:"robot_code"`
	RobotEffectTime      tools.MyTime `json:"robot_effect_time"`
	Updater              string       `json:"updater"`
	UpdateTime           tools.MyTime `json:"update_time"`
	Token                string       `json:"token"`
	EncodingAesKey       string       `json:"encoding_aes_key"`
}

type GetQYWXRobotRequest struct {
	system.ListQuery
	TobeDevelopedAppID uint64 `form:"tobe_developed_app_id"`
}

type GetQYWXRobotResponse struct {
	system.ResponseData
	List  []QYWXRobot `json:"list"`
	Total int         `json:"total"`
}

type QYWXRobot struct {
	ID   uint64 `json:"id"`
	Name string `json:"name"`
	Url  string `json:"url"`
}

type CreateAuthCorpRequest struct {
	system.Param
	CorpID            string    `json:"corp_id"`              // 企业id
	CorpName          string    `json:"corp_name"`            // 名称
	CorpFullName      string    `json:"corp_full_name"`       // 全称
	VerifiedEndTime   time.Time `json:"verified_end_time"`    // 认证到期时间
	CorpType          string    `json:"corp_type"`            // 授权方企业类型，认证号：verified, 注册号：unverified
	CorpRoundLogoUrl  string    `json:"corp_round_logo_url"`  // 圆形logo
	CorpSquareLogoUrl string    `json:"corp_square_logo_url"` // 正方形logo
	CorpUserMax       int       `json:"corp_user_max"`        // 授权方企业用户规模
	CorpWXQrCode      string    `json:"corp_wxqrcode"`        // 授权企业在微信插件（原企业号）的二维码，可用于关注微信插件，二维码有效期为7天
	SubjectType       int       `json:"subject_type"`         // 企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织, 4.团队号
	CorpScale         string    `json:"corp_scale"`           // 企业规模
	CorpIndustry      string    `json:"corp_industry"`        // 企业所属行业
	CorpSubIndustry   string    `json:"corp_sub_industry"`    // 企业所属子行业
	Location          string    `json:"location"`
}

type CreateTobeDevelopedAppRequest struct {
	system.Param
	AuthCorpInfo  AuthCorpInfo          `json:"auth_corp_info"`
	CorpID        string                `json:"corp_id"`         // 授权企业id
	AgentID       int64                 `json:"agent_id"`        // 应用id
	Name          string                `json:"name"`            // 名称
	Remark        string                `json:"remark"`          // 备注
	SquareLogoUrl string                `json:"square_logo_url"` // 方形头像
	Level         int                   `json:"level"`           // 权限等级
	AllowParty    tools.QueryIntList    `json:"allow_party"`     // 应用可见范围（部门）
	AllowUser     tools.QueryStringList `json:"allow_user"`      // 应用可见范围（标签）
	AllowTag      tools.QueryIntList    `json:"allow_tag"`       // 应用可见范围（成员）
	ExtraParty    tools.QueryIntList    `json:"extra_party"`     // 额外通讯录（部门）
	ExtraUser     tools.QueryStringList `json:"extra_user"`      // 额外通讯录（成员）
	ExtraTag      tools.QueryIntList    `json:"extra_tag"`       // 额外通讯录（标签）
	SuiteID       string                `json:"suite_id"`        // 代开发模版id
}

type AuthCorpInfo struct {
	CorpID            string          `json:"corp_id"`              // 企业id
	CorpName          string          `json:"corp_name"`            // 名称
	CorpFullName      string          `json:"corp_full_name"`       // 全称
	VerifiedEndTime   tools.QueryTime `json:"verified_end_time"`    // 认证到期时间
	CorpType          string          `json:"corp_type"`            // 授权方企业类型，认证号：verified, 注册号：unverified
	CorpRoundLogoUrl  string          `json:"corp_round_logo_url"`  // 圆形logo
	CorpSquareLogoUrl string          `json:"corp_square_logo_url"` // 正方形logo
	CorpUserMax       int             `json:"corp_user_max"`        // 授权方企业用户规模
	CorpWXQrCode      string          `json:"corp_wxqrcode"`        // 授权企业在微信插件（原企业号）的二维码，可用于关注微信插件，二维码有效期为7天
	SubjectType       int             `json:"subject_type"`         // 企业类型，1. 企业; 2. 政府以及事业单位; 3. 其他组织, 4.团队号
	CorpScale         string          `json:"corp_scale"`           // 企业规模
	CorpIndustry      string          `json:"corp_industry"`        // 企业所属行业
	CorpSubIndustry   string          `json:"corp_sub_industry"`    // 企业所属子行业
	Location          string          `json:"location"`
}

type TobeDevelopedAppResponse struct {
	system.ResponseData
	ID uint64 `json:"id"`
}

type TobeDevelopedAppBindTenantRequest struct {
	system.Param
	TobeDevelopedAppID  uint64                `json:"tobe_developed_app_id"` // 应用id
	TenantManagementIDs tools.QueryStringList `json:"tenant_management_id"`  // 账套id
	Token               string                `json:"token"`
	EncodingAesKey      string                `json:"encoding_aes_key"`
}

type TobeDevelopedAppCancelBindTenantRequest struct {
	system.Param
	TobeDevelopedAppID uint64 `json:"tobe_developed_app_id"` // 应用id
	TenantManagementID uint64 `json:"tenant_management_id"`  // 账套id
}

type TenantManagementResponse struct {
	system.ResponseData
	ID uint64 `json:"id"`
}

type UpdateTobeDevelopedAppRequest struct {
	system.Param
	TobeDevelopedAppID uint64          `json:"tobe_developed_app_id"` // 应用id
	RobotCode          string          `json:"robot_code"`            // 机器人编码
	RobotEffectTime    tools.QueryTime `json:"robot_effect_time"`     // 机器人有效时间
}

type GetTobeDevelopedAppListRequest struct {
	system.ListQuery
	QueryStr string `form:"query_str"`
}

type GetTobeDevelopedAppListResponse struct {
	system.ResponseData
	List  []GetTobeDevelopedAppListResponseItem `json:"list"`
	Total int                                   `json:"total"`
}

type GetTobeDevelopedAppListResponseItem struct {
	CorpName           string `json:"corp_name"`             // 企微名称
	CorpID             string `json:"corp_id"`               // 企微ID
	AppName            string `json:"app_name"`              // 应用名称
	AgentID            int64  `json:"agent_id"`              // 应用id
	TobeDevelopedAppID uint64 `json:"tobe_developed_app_id"` // 代开发应用id
}

type CreateQYWXRobotRequest struct {
	system.Param
	TobeDevelopedAppID uint64 `json:"tobe_developed_app_id"` // 应用id
	Name               string `json:"name"`                  // 机器人名称
	Url                string `json:"url"`                   // 机器人地址
}

type DeleteQYWXRobotRequest struct {
	system.Param
	ID uint64 `json:"id"`
}

type QYWXRobotResponse struct {
	system.ResponseData
	ID uint64 `json:"id"`
}

type GetQYWXUsersRequest struct {
	system.ListQuery
	Name string `form:"name"`
}

type GetQYWXUsersResponse struct {
	system.ResponseData
	Total int64                      `json:"total"`
	List  []GetQYWXUsersResponseItem `json:"list"`
}

type GetQYWXUsersResponseItem struct {
	Name   string `json:"name"`    // 名称
	UserID string `json:"user_id"` // 用户id
	Bound  bool   `json:"bound"`   // 是否绑定
}

type QYWXGetUserInfoRequest struct {
	system.Param
	CorpID  string `json:"corp_id"`  // 企业id
	AgentID string `json:"agent_id"` // 应用id
	Code    string `json:"code"`
}

type QYWXGetUserInfoResponse struct {
	system.ResponseData
	Token  string `json:"token"`   // 令牌
	UserID uint64 `json:"user_id"` // 用户ID
}

type QYWXGetCustomersRequest struct {
	system.ListQuery
	Name         string `form:"name"`          // 名称
	AllCustomers bool   `form:"all_customers"` // 是否查找全部
}

type QYWXGetCustomersResponse struct {
	system.ResponseData
	Total int64          `json:"total"`
	List  []QYWXCustomer `json:"list"`
}

type QYWXCustomer struct {
	ID           string `json:"id"`             // ID
	Name         string `json:"name"`           // 名称
	Avatar       string `json:"avatar"`         // 头像
	Type         string `json:"type"`           // 类型
	CorpName     string `json:"corp_name"`      // 公司名称
	CorpFullName string `json:"corp_full_name"` // 公司全称
	Gender       string `json:"gender"`         // 性别
	Bound        bool   `json:"bound"`          // 是否绑定
}

type QYWXGetGroupChatListRequest struct {
	system.ListQuery
	Name string `form:"name"`
}

type QYWXGetGroupChatListResponse struct {
	system.ResponseData
	Total int64           `json:"total"`
	List  []QYWXGroupChat `json:"list"`
}

type QYWXGroupChat struct {
	ID         string          `json:"id"`          // 群聊id
	Name       string          `json:"name"`        // 群聊名称
	Owner      string          `json:"owner"`       // 群主
	CreateTime tools.QueryTime `json:"create_time"` // 创建时间
	Bound      bool            `json:"bound"`       // 是否绑定
}

type QYWXAddGroupChatRequest struct {
	system.Param
	Name string `json:"name"`
}

type BotSendMsgRequest struct {
	system.Param
	ChatName string `json:"chat_name"`
	FileUrl  string `json:"file_url"`
	Content  string `json:"content"`
}

type QYWXCustomerBindRelRequest struct {
	system.Param
	QYWXCustomerID  string `form:"qywx_customer_id"`   // 企业微信客户ID
	QYWXGroupChatID string `form:"qywx_group_chat_id"` // 企业微信群聊ID
}

type QYWXCustomerBindRelResponse struct {
	system.Param
	CustomerID   uint64 `json:"customer_id"`
	CustomerName string `json:"customer_name"`
}

type QYWXSignatureRequest struct {
	system.Query
	NonceStr  string `form:"nonceStr"`  // 随机字符串
	Timestamp int64  `form:"timestamp"` // 时间戳
	Url       string `form:"url"`       // url
}

type QYWXSignatureResponse struct {
	system.ResponseData
	CorpSignature string `json:"corp_signature"` // 签名
	AppSignature  string `json:"app_signature"`  // 签名
}

// 企业客户标签
type GetCorpTagGroupListQuery struct {
	system.ListQuery
	ExternalUserID string `form:"external_user_id"` // 企业微信客户ID
	FollowUserID   string `form:"follow_user_id"`   // 企业微信员工ID
}

type GetCorpTagGroupData struct {
	system.ResponseData
	ID       uint64 `json:"id"`        // ID
	Name     string `json:"name"`      // 名称
	GroupID  string `json:"group_id"`  // 标签组ID
	CorpTags []Tag  `json:"corp_tags"` // 标签组内的标签列表
}

type Tag struct {
	ID       uint64 `json:"id"`        // ID
	TagID    string `json:"tag_id"`    // 标签id
	TagName  string `json:"tag_name"`  // 标签名称
	IsSelect bool   `json:"is_select"` // 是否选中
}

type GetCorpTagGroupDataList []GetCorpTagGroupData

func (g GetCorpTagGroupDataList) Adjust() {

}
