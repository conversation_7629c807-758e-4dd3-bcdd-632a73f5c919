// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wx/tobe_developed_app_info/tobe_developed_app_info.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on UpdateTobeDevelopedAppInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *UpdateTobeDevelopedAppInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTobeDevelopedAppInfoRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateTobeDevelopedAppInfoRequestMultiError, or nil if none found.
func (m *UpdateTobeDevelopedAppInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTobeDevelopedAppInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Token

	// no validation rules for EncodingAesKey

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateTobeDevelopedAppInfoRequestMultiError(errors)
	}

	return nil
}

// UpdateTobeDevelopedAppInfoRequestMultiError is an error wrapping multiple
// validation errors returned by
// UpdateTobeDevelopedAppInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTobeDevelopedAppInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTobeDevelopedAppInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTobeDevelopedAppInfoRequestMultiError) AllErrors() []error { return m }

// UpdateTobeDevelopedAppInfoRequestValidationError is the validation error
// returned by UpdateTobeDevelopedAppInfoRequest.Validate if the designated
// constraints aren't met.
type UpdateTobeDevelopedAppInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTobeDevelopedAppInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTobeDevelopedAppInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTobeDevelopedAppInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTobeDevelopedAppInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTobeDevelopedAppInfoRequestValidationError) ErrorName() string {
	return "UpdateTobeDevelopedAppInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTobeDevelopedAppInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTobeDevelopedAppInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTobeDevelopedAppInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTobeDevelopedAppInfoRequestValidationError{}

// Validate checks the field values on UpdateTobeDevelopedAppInfoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTobeDevelopedAppInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTobeDevelopedAppInfoReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// UpdateTobeDevelopedAppInfoReplyMultiError, or nil if none found.
func (m *UpdateTobeDevelopedAppInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTobeDevelopedAppInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateTobeDevelopedAppInfoReplyMultiError(errors)
	}

	return nil
}

// UpdateTobeDevelopedAppInfoReplyMultiError is an error wrapping multiple
// validation errors returned by UpdateTobeDevelopedAppInfoReply.ValidateAll()
// if the designated constraints aren't met.
type UpdateTobeDevelopedAppInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTobeDevelopedAppInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTobeDevelopedAppInfoReplyMultiError) AllErrors() []error { return m }

// UpdateTobeDevelopedAppInfoReplyValidationError is the validation error
// returned by UpdateTobeDevelopedAppInfoReply.Validate if the designated
// constraints aren't met.
type UpdateTobeDevelopedAppInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTobeDevelopedAppInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTobeDevelopedAppInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTobeDevelopedAppInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTobeDevelopedAppInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTobeDevelopedAppInfoReplyValidationError) ErrorName() string {
	return "UpdateTobeDevelopedAppInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTobeDevelopedAppInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTobeDevelopedAppInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTobeDevelopedAppInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTobeDevelopedAppInfoReplyValidationError{}

// Validate checks the field values on UpdateTobeDevelopedAppInfoRobotRequest
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateTobeDevelopedAppInfoRobotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// UpdateTobeDevelopedAppInfoRobotRequest with the rules defined in the proto
// definition for this message. If any rules are violated, the result is a
// list of violation errors wrapped in
// UpdateTobeDevelopedAppInfoRobotRequestMultiError, or nil if none found.
func (m *UpdateTobeDevelopedAppInfoRobotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTobeDevelopedAppInfoRobotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RobotCode

	// no validation rules for RobotEffectTime

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateTobeDevelopedAppInfoRobotRequestMultiError(errors)
	}

	return nil
}

// UpdateTobeDevelopedAppInfoRobotRequestMultiError is an error wrapping
// multiple validation errors returned by
// UpdateTobeDevelopedAppInfoRobotRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTobeDevelopedAppInfoRobotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTobeDevelopedAppInfoRobotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTobeDevelopedAppInfoRobotRequestMultiError) AllErrors() []error { return m }

// UpdateTobeDevelopedAppInfoRobotRequestValidationError is the validation
// error returned by UpdateTobeDevelopedAppInfoRobotRequest.Validate if the
// designated constraints aren't met.
type UpdateTobeDevelopedAppInfoRobotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTobeDevelopedAppInfoRobotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTobeDevelopedAppInfoRobotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTobeDevelopedAppInfoRobotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTobeDevelopedAppInfoRobotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTobeDevelopedAppInfoRobotRequestValidationError) ErrorName() string {
	return "UpdateTobeDevelopedAppInfoRobotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTobeDevelopedAppInfoRobotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTobeDevelopedAppInfoRobotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTobeDevelopedAppInfoRobotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTobeDevelopedAppInfoRobotRequestValidationError{}

// Validate checks the field values on UpdateTobeDevelopedAppInfoRobotReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the first error encountered is returned, or nil if
// there are no violations.
func (m *UpdateTobeDevelopedAppInfoRobotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTobeDevelopedAppInfoRobotReply
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// UpdateTobeDevelopedAppInfoRobotReplyMultiError, or nil if none found.
func (m *UpdateTobeDevelopedAppInfoRobotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTobeDevelopedAppInfoRobotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateTobeDevelopedAppInfoRobotReplyMultiError(errors)
	}

	return nil
}

// UpdateTobeDevelopedAppInfoRobotReplyMultiError is an error wrapping multiple
// validation errors returned by
// UpdateTobeDevelopedAppInfoRobotReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateTobeDevelopedAppInfoRobotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTobeDevelopedAppInfoRobotReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTobeDevelopedAppInfoRobotReplyMultiError) AllErrors() []error { return m }

// UpdateTobeDevelopedAppInfoRobotReplyValidationError is the validation error
// returned by UpdateTobeDevelopedAppInfoRobotReply.Validate if the designated
// constraints aren't met.
type UpdateTobeDevelopedAppInfoRobotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTobeDevelopedAppInfoRobotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTobeDevelopedAppInfoRobotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTobeDevelopedAppInfoRobotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTobeDevelopedAppInfoRobotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTobeDevelopedAppInfoRobotReplyValidationError) ErrorName() string {
	return "UpdateTobeDevelopedAppInfoRobotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTobeDevelopedAppInfoRobotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTobeDevelopedAppInfoRobotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTobeDevelopedAppInfoRobotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTobeDevelopedAppInfoRobotReplyValidationError{}

// Validate checks the field values on GetTobeDevelopedAppInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTobeDevelopedAppInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTobeDevelopedAppInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetTobeDevelopedAppInfoRequestMultiError, or nil if none found.
func (m *GetTobeDevelopedAppInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTobeDevelopedAppInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetTobeDevelopedAppInfoRequestMultiError(errors)
	}

	return nil
}

// GetTobeDevelopedAppInfoRequestMultiError is an error wrapping multiple
// validation errors returned by GetTobeDevelopedAppInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type GetTobeDevelopedAppInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTobeDevelopedAppInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTobeDevelopedAppInfoRequestMultiError) AllErrors() []error { return m }

// GetTobeDevelopedAppInfoRequestValidationError is the validation error
// returned by GetTobeDevelopedAppInfoRequest.Validate if the designated
// constraints aren't met.
type GetTobeDevelopedAppInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTobeDevelopedAppInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTobeDevelopedAppInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTobeDevelopedAppInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTobeDevelopedAppInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTobeDevelopedAppInfoRequestValidationError) ErrorName() string {
	return "GetTobeDevelopedAppInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTobeDevelopedAppInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTobeDevelopedAppInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTobeDevelopedAppInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTobeDevelopedAppInfoRequestValidationError{}

// Validate checks the field values on GetTobeDevelopedAppInfoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTobeDevelopedAppInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTobeDevelopedAppInfoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTobeDevelopedAppInfoReplyMultiError, or nil if none found.
func (m *GetTobeDevelopedAppInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTobeDevelopedAppInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreateTime

	// no validation rules for CreatorId

	// no validation rules for CreatorName

	// no validation rules for UpdateTime

	// no validation rules for UpdaterId

	// no validation rules for UpdaterName

	// no validation rules for AgentId

	// no validation rules for Name

	// no validation rules for Token

	// no validation rules for EncodingAesKey

	// no validation rules for RobotCode

	// no validation rules for RobotEffectTime

	// no validation rules for CorpId

	// no validation rules for CorpName

	if len(errors) > 0 {
		return GetTobeDevelopedAppInfoReplyMultiError(errors)
	}

	return nil
}

// GetTobeDevelopedAppInfoReplyMultiError is an error wrapping multiple
// validation errors returned by GetTobeDevelopedAppInfoReply.ValidateAll() if
// the designated constraints aren't met.
type GetTobeDevelopedAppInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTobeDevelopedAppInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTobeDevelopedAppInfoReplyMultiError) AllErrors() []error { return m }

// GetTobeDevelopedAppInfoReplyValidationError is the validation error returned
// by GetTobeDevelopedAppInfoReply.Validate if the designated constraints
// aren't met.
type GetTobeDevelopedAppInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTobeDevelopedAppInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTobeDevelopedAppInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTobeDevelopedAppInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTobeDevelopedAppInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTobeDevelopedAppInfoReplyValidationError) ErrorName() string {
	return "GetTobeDevelopedAppInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetTobeDevelopedAppInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTobeDevelopedAppInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTobeDevelopedAppInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTobeDevelopedAppInfoReplyValidationError{}

// Validate checks the field values on ListTobeDevelopedAppInfoRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTobeDevelopedAppInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTobeDevelopedAppInfoRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListTobeDevelopedAppInfoRequestMultiError, or nil if none found.
func (m *ListTobeDevelopedAppInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTobeDevelopedAppInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPage() < 1 {
		err := ListTobeDevelopedAppInfoRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := ListTobeDevelopedAppInfoRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for CorpId

	// no validation rules for CorpName

	// no validation rules for Ids

	if len(errors) > 0 {
		return ListTobeDevelopedAppInfoRequestMultiError(errors)
	}

	return nil
}

// ListTobeDevelopedAppInfoRequestMultiError is an error wrapping multiple
// validation errors returned by ListTobeDevelopedAppInfoRequest.ValidateAll()
// if the designated constraints aren't met.
type ListTobeDevelopedAppInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTobeDevelopedAppInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTobeDevelopedAppInfoRequestMultiError) AllErrors() []error { return m }

// ListTobeDevelopedAppInfoRequestValidationError is the validation error
// returned by ListTobeDevelopedAppInfoRequest.Validate if the designated
// constraints aren't met.
type ListTobeDevelopedAppInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTobeDevelopedAppInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTobeDevelopedAppInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTobeDevelopedAppInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTobeDevelopedAppInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTobeDevelopedAppInfoRequestValidationError) ErrorName() string {
	return "ListTobeDevelopedAppInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTobeDevelopedAppInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTobeDevelopedAppInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTobeDevelopedAppInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTobeDevelopedAppInfoRequestValidationError{}

// Validate checks the field values on ListTobeDevelopedAppInfoReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTobeDevelopedAppInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTobeDevelopedAppInfoReply with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListTobeDevelopedAppInfoReplyMultiError, or nil if none found.
func (m *ListTobeDevelopedAppInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTobeDevelopedAppInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTobeDevelopedAppInfoReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTobeDevelopedAppInfoReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTobeDevelopedAppInfoReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTobeDevelopedAppInfoReplyMultiError(errors)
	}

	return nil
}

// ListTobeDevelopedAppInfoReplyMultiError is an error wrapping multiple
// validation errors returned by ListTobeDevelopedAppInfoReply.ValidateAll()
// if the designated constraints aren't met.
type ListTobeDevelopedAppInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTobeDevelopedAppInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTobeDevelopedAppInfoReplyMultiError) AllErrors() []error { return m }

// ListTobeDevelopedAppInfoReplyValidationError is the validation error
// returned by ListTobeDevelopedAppInfoReply.Validate if the designated
// constraints aren't met.
type ListTobeDevelopedAppInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTobeDevelopedAppInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTobeDevelopedAppInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTobeDevelopedAppInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTobeDevelopedAppInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTobeDevelopedAppInfoReplyValidationError) ErrorName() string {
	return "ListTobeDevelopedAppInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListTobeDevelopedAppInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTobeDevelopedAppInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTobeDevelopedAppInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTobeDevelopedAppInfoReplyValidationError{}

// Validate checks the field values on
// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoMultiError, or nil if
// none found.
func (m *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreateTime

	// no validation rules for CreatorId

	// no validation rules for CreatorName

	// no validation rules for UpdateTime

	// no validation rules for UpdaterId

	// no validation rules for UpdaterName

	// no validation rules for AgentId

	// no validation rules for Name

	// no validation rules for Token

	// no validation rules for EncodingAesKey

	// no validation rules for RobotCode

	// no validation rules for RobotEffectTime

	// no validation rules for CorpId

	// no validation rules for CorpName

	if len(errors) > 0 {
		return ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoMultiError(errors)
	}

	return nil
}

// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoMultiError is an error
// wrapping multiple validation errors returned by
// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo.ValidateAll() if the
// designated constraints aren't met.
type ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoMultiError) AllErrors() []error { return m }

// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError is the
// validation error returned by
// ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo.Validate if the
// designated constraints aren't met.
type ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError) Field() string {
	return e.field
}

// Reason function returns reason value.
func (e ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError) Cause() error {
	return e.cause
}

// Key function returns key value.
func (e ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError) ErrorName() string {
	return "ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfoValidationError{}
