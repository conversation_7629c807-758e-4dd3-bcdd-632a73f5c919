package tenant_management

import (
	common "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddTenantManagementParam struct {
	structure_base.Param
	CompanyName     string        `json:"company_name"`      // 公司名称
	Phone           string        `json:"phone"`             // 手机号
	TenantPackageID uint64        `json:"tenant_package_id"` // 套餐ID
	PayWay          common.PayWay `json:"pay_way"`           // 支付方式
	TenantContacts  string        `json:"tenant_contacts"`   // 租户联系人
}

type ResTenantManagementIDData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type OcrCodeTenantManagementIDData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type ResPayUrlData struct {
	structure_base.ResponseData
	PayUrl string `json:"pay_url"` // 支付url
	Price  int    `json:"price"`   // 支付金额
}

type GetTenantManagementListQuery struct {
	structure_base.ListQuery
	CreateStartTime        tools.QueryTime               `form:"create_start_time"`        // 创建开始时间
	CreateEndTime          tools.QueryTime               `form:"create_end_time"`          // 创建结束时间
	IDOrName               string                        `form:"id_or_name"`               // ID或名称
	Phone                  string                        `form:"phone"`                    // 手机号
	TenantContacts         string                        `form:"tenant_contacts"`          //  联系人
	TenantManagementStatus common.TenantManagementStatus `form:"tenant_management_status"` // 状态
}

type GetTenantManagementListData struct {
	structure_base.ResponseData
	Id                         uint64                        `json:"id"`                            // Id
	CompanyName                string                        `json:"company_name"`                  // 公司名称
	Phone                      string                        `json:"phone"`                         // 手机号
	Contacts                   string                        `json:"contacts"`                      // 租户联系人
	CreateTime                 tools.MyTime                  `json:"create_time"`                   // 创建时间
	CreatorName                string                        `json:"creator_name"`                  // 创建人
	UpdateTime                 tools.MyTime                  `json:"update_time"`                   // 更新时间
	UpdateUserName             string                        `json:"update_user_name"`              // 更新人
	ActivationTime             tools.MyTime                  `json:"activation_time"`               // 激活时间
	Deadline                   tools.MyTime                  `json:"deadline"`                      // 截止日期
	TenantManagementStatus     common.TenantManagementStatus `json:"tenant_management_status"`      // 状态
	TenantManagementStatusName string                        `json:"tenant_management_status_name"` // 状态名称

	CodeListOrcDeadLine tools.MyTime          `json:"code_list_orc_dead_line"` // 码单有效期
	CodeListOrcStatus   common.CodeListStatus `json:"code_list_orc_status"`    // 码单状态

	ElectronicColorCardStatus   common.ElectronicColorCardStatus `json:"electronic_color_card_status"`    // 电子色卡状态
	ElectronicColorCardDeadLine tools.MyTime                     `json:"electronic_color_card_dead_line"` // 电子色卡截止日期
	SearchImageStatus           common.ElectronicColorCardStatus `json:"search_image_status"`             // 电子色卡状态
	SearchImageDeadLine         tools.MyTime                     `json:"search_image_dead_line"`          // 电子色卡截止日期
}

type GetTenantManagementListDataList []GetTenantManagementListData

func (l GetTenantManagementListDataList) Adjust() {

}

type GetTenantManagementQuery struct {
	structure_base.Query
	Id     uint64 `form:"id"` // Id
	UserId uint64 `form:"-"`  // 用户id
}

type GetTenantManagementData struct {
	structure_base.ResponseData
	Id                         uint64                        `json:"id"`                            // Id
	CompanyName                string                        `json:"company_name"`                  // 公司名称
	Phone                      string                        `json:"phone"`                         // 手机号
	Contacts                   string                        `json:"contacts"`                      // 联系人
	CreateTime                 tools.MyTime                  `json:"create_time"`                   // 创建时间
	CreatorName                string                        `json:"creator_name"`                  // 创建人
	UpdateTime                 tools.MyTime                  `json:"update_time"`                   // 更新时间
	UpdateUserName             string                        `json:"update_user_name"`              // 更新人
	ActivationTime             tools.MyTime                  `json:"activation_time"`               // 激活时间
	Deadline                   tools.MyTime                  `json:"deadline"`                      // 截止日期
	CodeListOrcDeadLine        tools.MyTime                  `json:"code_list_orc_dead_line"`       // 码单识别截止日期
	TenantManagementStatus     common.TenantManagementStatus `json:"tenant_management_status"`      // 状态
	TenantManagementStatusName string                        `json:"tenant_management_status_name"` // 状态名称
	PayRecordDataList          []PayRecordData               `json:"pay_record_data_list"`          // 支付记录
}

type PayRecordData struct {
	Id                  uint64           `json:"id"`                     // Id
	TradeNo             string           `json:"trade_no"`               // 商户交易号
	PayOrderNo          string           `json:"pay_order_no"`           // 支付订单号
	PayFinishTime       tools.MyTime     `json:"pay_finish_time"`        // 支付完成时间
	PayPrice            int              `json:"pay_price"`              // 支付金额
	PayWay              common.PayWay    `json:"pay_way"`                // 支付方式
	PayWayName          string           `json:"pay_way_name"`           // 支付方式名称
	PayStatus           common.PayStatus `json:"pay_status"`             // 支付状态
	PayStatusName       string           `json:"pay_status_name"`        // 支付状态名称
	TenantPackageID     uint64           `json:"tenant_package_id"`      // 套餐ID
	TenantPackageName   string           `json:"tenant_package_name"`    // 套餐名称
	ActivationTime      tools.MyTime     `json:"activation_time"`        // 激活时间
	Deadline            tools.MyTime     `json:"deadline"`               // 截止日期
	UpdateUserName      string           `json:"update_user_name"`       // 更新人
	UpdateTime          tools.MyTime     `json:"update_time"`            // 更新时间
	PayStatusChangeTime tools.MyTime     `json:"pay_status_change_time"` // 支付状态变更时间
	PayerName           string           `json:"payer_name"`             // 支付者昵称
}

type UpdateTenantManagementNameParam struct {
	structure_base.Param
	Id          uint64 `json:"id"`           // Id
	UserID      uint64 `json:"user_id"`      // 用户id
	CompanyName string `json:"company_name"` // 租户公司名称
}

type DisableTenantManagementParam struct {
	structure_base.Param
	IDs []uint64 `json:"ids" relate:"tenant_management_id"` // IDs
}

type EnableTenantManagementParam struct {
	structure_base.Param
	Id uint64 `json:"id" relate:"tenant_management_id"` // Id
}

type GetMPTenantManagementData struct {
	structure_base.ResponseData
	Id                          uint64                        `json:"id"`                              // Id
	CompanyName                 string                        `json:"company_name"`                    // 公司名称
	Phone                       string                        `json:"phone"`                           // 手机号
	Contacts                    string                        `json:"contacts"`                        // 联系人
	CreateTime                  tools.MyTime                  `json:"create_time"`                     // 创建时间
	CreatorName                 string                        `json:"creator_name"`                    // 创建人
	UpdateTime                  tools.MyTime                  `json:"update_time"`                     // 更新时间
	UpdateUserName              string                        `json:"update_user_name"`                // 更新人
	Deadline                    tools.MyTime                  `json:"deadline"`                        // 截止日期
	TenantManagementStatus      common.TenantManagementStatus `json:"tenant_management_status"`        // 状态
	TenantManagementStatusName  string                        `json:"tenant_management_status_name"`   // 状态名称
	AdminName                   string                        `json:"admin_name"`                      // 管理员
	SubTenantManagementDataList []SubTenantManagementData     `json:"sub_tenant_management_data_list"` // 用户账号列表
}

type SubTenantManagementData struct {
	Id              uint64                     `json:"id"`                // Id
	UserName        string                     `json:"user_name"`         // 用户名
	Phone           string                     `json:"phone"`             // 手机号码
	IsAdminTenant   bool                       `json:"is_admin_tenant"`   // 是否管理员
	DepartmentID    uint64                     `json:"department_id"`     // 部门ID
	DepartmentName  string                     `json:"department_name"`   // 部门名称
	DutyIDs         []uint64                   `json:"duty_ids"`          // 职责列表IDs
	DutyName        string                     `json:"duty_name"`         // 职务名称
	AccessScope     common.RoleAccessDataScope `json:"access_scope"`      // 数据权限范围
	AccessScopeName string                     `json:"access_scope_name"` // 数据权限范围名称
	Status          common.Status              `json:"status"`            // 状态
	StatusName      string                     `json:"status_name"`       // 状态名称
	RoleDataList    []RoleData                 `json:"role_data_list"`    // 角色列表
}

type RoleData struct {
	Id   uint64 `json:"id"`   // Id
	Name string `json:"name"` // 名称
}

type AddOrUpdateSubTenantManagementParam struct {
	structure_base.Param
	TenantManagementID uint64                     `json:"tenant_management_id"` // 账套ID
	UserID             uint64                     `json:"user_id"`              // 用户ID
	UserName           string                     `json:"user_name"`            // 用户名
	Phone              string                     `json:"phone"`                // 手机号码
	Password           string                     `json:"password"`             // 密码
	DepartmentID       uint64                     `json:"department_id"`        // 部门ID
	RoleIDs            []uint64                   `json:"role_ids"`             // 角色IDs
	DutyIDs            []uint64                   `json:"duty_ids"`             // 职责列表IDs
	AccessScope        common.RoleAccessDataScope `json:"access_scope"`         // 数据权限范围
	Status             common.Status              `json:"status"`               // 状态
}

type ModifyTenantManagementPasswordParam struct {
	structure_base.Param
	TenantManagementID uint64 `json:"tenant_management_id"` // 账套ID
	UserID             uint64 `json:"user_id"`              // 用户ID
	Password           string `json:"password"`             // 密码
}

type GetUserTenantManagementData struct {
	structure_base.ResponseData
	TenantManagementID uint64 `json:"tenant_management_id"` // 账套ID
	CompanyName        string `json:"company_name"`         // 公司名称
	TenantPhone        string `json:"tenant_phone"`         // 租户联系人手机号
	TenantContacts     string `json:"tenant_contacts"`      // 租户联系人
	DatabaseName       string `json:"database_name"`        // 数据库名称
	Secret             string `json:"secret"`               // 秘钥
}

type GetUserTenantManagementDataList []GetUserTenantManagementData

func (l GetUserTenantManagementDataList) Adjust() {

}

type TenantManagementFeedbackParam struct {
	structure_base.Param
	Urls       tools.QueryStringList `json:"urls"`       // 图片地址，多个用逗号隔开
	Suggestion string                `json:"suggestion"` // 建议
}

type TenantManagementFeedbackData struct {
	structure_base.ResponseData
}

type CleanTenantManagementParam struct {
	structure_base.Param
	Id    uint64 `json:"id" form:"id" relate:"tenant_management_id"` // Id
	Phone string `json:"phone" form:"phone"`                         // 手机号码
}

type FillDomainParam struct {
	structure_base.Param
	RequestDomain       string `json:"request_domain"`        // 要填充的域名值
	RequestDomainPrefix string `json:"request_domain_prefix"` // 要填充的域名前缀
}

type QYWXGetUnBindTenantManagementListQuery struct {
	structure_base.ListQuery
	QueryStr                 string `form:"query_str"` // 手机号或名称
	BoundTenantManagementIds tools.QueryIntList
}

type QYWXGetUnBindTenantManagementListData struct {
	structure_base.ResponseData
	List  []QYWXGetUnBindTenantManagementListDataItem `json:"list"`
	Total int                                         `json:"total"`
}

type QYWXGetUnBindTenantManagementListDataItem struct {
	Id          uint64 `json:"id"`           // Id
	CompanyName string `json:"company_name"` // 公司名称
	Phone       string `json:"phone"`        // 手机号
	Contacts    string `json:"contacts"`     // 租户联系人
}

type CompatibleOldTenantParam struct {
	TenantPhone string `json:"tenant_phone" binding:""` // 租户手机号，空表示所有租户
}

func (c CompatibleOldTenantParam) Adjust() {

}

func (c CompatibleOldTenantParam) Validate() error {
	return nil
}

type CompatibleOldTenantResponse struct {
	structure_base.Response
}

func (c CompatibleOldTenantResponse) Adjust() {
}
