package product

import (
	"context"
	"gorm.io/gorm"
	common "hcscm/common/product"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	"hcscm/vars"

	"hcscm/common/errors"
)

func GetFpmInOrderItemIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_in_order_item_id")
}

type FpmInOrderItemList []FpmInOrderItem

func (r FpmInOrderItemList) List() []FpmInOrderItem {
	return r
}

func (r FpmInOrderItemList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmInOrderItemList) GetOrderIDs() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.ParentId)
	}
	return o
}

func (r FpmInOrderItemList) GetQuoteOrderItemIds() (ids []uint64) {
	var (
		o = make([]uint64, 0)
	)
	for _, t := range r {
		if t.QuoteOrderItemId != 0 {
			o = append(o, t.QuoteOrderItemId)
		}
	}
	return o
}

func (r FpmInOrderItemList) One() FpmInOrderItem {
	return r[0]
}

func (r FpmInOrderItemList) Pick(id uint64) (o FpmInOrderItem) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r FpmInOrderItemList) PickByParentId(parentId uint64) (o FpmInOrderItemList) {
	var list = make(FpmInOrderItemList, 0)
	for _, t := range r {
		if t.ParentId == parentId {
			list = append(list, t)
		}
	}
	return list
}

func (r FpmInOrderItemList) GetTotalWaitCollectWeight(totalWeight int, quoteOrderItemId uint64) (totalWaitCollectWeight int) {
	totalWaitCollectWeight = totalWeight
	for _, item := range r {
		if item.QuoteOrderItemId == quoteOrderItemId {
			totalWaitCollectWeight -= item.TotalWeight
		}
	}
	return
}

// FpmInOrderItem 成品信息
type FpmInOrderItem struct {
	mysql_base.ModelHard
	Id                            uint64                     `gorm:"column:id;primaryKey" relate:"id"`
	WarehouseInType               common.WarehouseGoodInType `gorm:"column:warehouse_in_type"`                                                // 来源类型
	ParentId                      uint64                     `gorm:"column:parent_id" relate:"parent_id"`                                     // 父id（单号id）
	ParentOrderNo                 string                     `gorm:"column:parent_order_no"`                                                  // 父单号(对应的单据号)(对应的单据号)
	SumStockId                    uint64                     `gorm:"column:sum_stock_id"`                                                     // 汇总库存成品id
	QuoteOrderNo                  string                     `gorm:"column:quote_order_no"`                                                   // 引用数据单号
	QuoteOrderItemId              uint64                     `gorm:"column:quote_order_item_id" relate:"quote_order_item_id"`                 // 引用数据单物料那条id
	ProductCode                   string                     `gorm:"column:product_code"`                                                     // 成品编号 todo:已弃用,待去除
	ProductName                   string                     `gorm:"column:product_name"`                                                     // 成品名称 todo:已弃用,待去除
	CustomerId                    uint64                     `gorm:"column:customer_id" relate:"customer_id,biz_unit_id"`                     // 所属客户id
	ProductColorId                uint64                     `gorm:"column:product_color_id" relate:"product_color_id"`                       // 成品颜色id
	ProductId                     uint64                     `gorm:"column:product_id" relate:"product_id"`                                   // 成品id
	ProductColorCode              string                     `gorm:"column:product_color_code"`                                               // 成品颜色编号
	ProductColorName              string                     `gorm:"column:product_color_name"`                                               // 成品颜色名称
	DyeFactoryColorCode           string                     `gorm:"column:dye_factory_color_code"`                                           // 染厂色号
	DyeFactoryDyelotNumber        string                     `gorm:"column:dye_factory_dyelot_number"`                                        // 染厂缸号
	ProductWidth                  string                     `gorm:"column:product_width"`                                                    // 成品幅宽
	ProductGramWeight             string                     `gorm:"column:product_gram_weight"`                                              // 成品克重
	ProductLevelId                uint64                     `gorm:"column:product_level_id" relate:"product_level_id"`                       // 成品等级
	ProductRemark                 string                     `gorm:"column:product_remark"`                                                   // 成品备注
	ProductCraft                  string                     `gorm:"column:product_craft"`                                                    // 成品工艺 todo:已弃用,待去除
	ProductIngredient             string                     `gorm:"column:product_ingredient"`                                               // 成品成分 todo:已弃用,待去除
	QuoteRoll                     int                        `gorm:"column:quote_roll"`                                                       // 引用数据匹数(件)，乘100存
	QuoteTotalWeight              int                        `gorm:"column:quote_total_weight"`                                               // 引用数据总数量(公斤)，乘10000存
	InRoll                        int                        `gorm:"column:in_roll"`                                                          // 进仓件数(件)，乘100存
	TotalWeight                   int                        `gorm:"column:total_weight"`                                                     // 总数量(公斤)，乘10000存
	WeightError                   int                        `gorm:"column:weight_error"`                                                     // 空差数量(公斤)，乘10000存
	PaperTubeWeight               int                        `gorm:"column:paper_tube_weight"`                                                // 纸筒数量(公斤)，乘10000存
	SettleWeight                  int                        `gorm:"column:settle_weight"`                                                    // 结算数量(公斤)，乘10000存
	QuoteWeight                   int                        `gorm:"column:quote_weight"`                                                     // 引用数据重(公斤)，乘10000存
	UnitPrice                     int                        `gorm:"column:unit_price"`                                                       // 单价(元)，乘10000存
	UnitId                        uint64                     `gorm:"column:unit_id" relate:"measurement_unit_id"`                             // 单位id
	AuxiliaryUnitId               uint64                     `gorm:"column:auxiliary_unit_id" relate:"auxiliary_unit_id,measurement_unit_id"` // 辅助单位id（用于判断计算金额时使用哪个数量）
	InLength                      int                        `gorm:"column:in_length"`                                                        // 进仓长度，乘100存
	QuoteLength                   int                        `gorm:"column:quote_length"`                                                     // 引用数据长度，乘100存
	LengthUnitPrice               int                        `gorm:"column:length_unit_price"`                                                // 长度单价，乘10000存
	OtherPrice                    int                        `gorm:"column:other_price"`                                                      // 其他金额(元)，乘100存(数量单价*结算数量)+(长度单价*进仓长度)+其他金额
	TotalPrice                    int                        `gorm:"column:total_price"`                                                      // 总金额/进仓金额(元)，乘100存
	Remark                        string                     `gorm:"column:remark"`                                                           // 备注
	ActuallyWeight                int                        `gorm:"column:actually_weight"`                                                  // 码单数量
	SettleErrorWeight             int                        `gorm:"column:settle_error_weight"`                                              // 结算空差(公斤)，乘10000存
	ArrangeOrderItemId            uint64                     `gorm:"column:arrange_order_item_id" relate:"arrange_order_item_id"`             // 配布单分录行id
	FinishProductWidthUnitId      uint64                     `gorm:"column:finish_product_width_unit_id" relate:"dictionary_detail_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                     `gorm:"column:finish_product_gram_weight_unit_id" relate:"dictionary_detail_id"` // 成品克重单位id(字典)
	SalePlanOrderItemId           uint64                     `gorm:"column:sale_plan_order_item_id"`                                          // 成品销售计划单子项信息id
	SalePlanOrderItemNo           string                     `gorm:"column:sale_plan_order_item_no"`                                          // 成品销售计划单子项单号
	ReturnRoll                    int                        `gorm:"column:return_roll"`                                                      // 已退匹数
	ReturnWeight                  int                        `gorm:"column:return_weight"`                                                    // 已退数量
	ReturnLength                  int                        `gorm:"column:return_length"`                                                    // 已退长度

	// 成品销售退货进仓单用
	InWeight             int `gorm:"column:in_weight"`               // 进仓数量，乘100存
	ReturnPrice          int `gorm:"column:return_price"`            // 退货单价
	LengthCutReturnPrice int `gorm:"column:length_cut_return_price"` // 剪板退货单价
	SettlePrice          int `gorm:"column:settle_price"`            // 成品销售退货进仓单成品金额
	// 加工进仓单用
	QuoteOrderId           uint64 `gorm:"column:quote_order_id"`             // 引用数据单id（染整）
	QuoteOrderType         int    `gorm:"column:quote_order_type"`           // 单据类型
	DyeingSituId           uint64 `gorm:"column:dyeing_situ_id"`             // 染整进度情况id
	CustomerAccountNum     string `gorm:"column:customer_account_num"`       // 客户款号
	ContractNumber         string `gorm:"column:contract_number"`            // 合同号
	DyeDeliveryOrderId     uint64 `gorm:"column:dye_delivery_order_id"`      // 染厂送货单号id
	DyeDeliveryOrderNo     string `gorm:"column:dye_delivery_order_no"`      // 染厂送货单号
	UseGfRoll              int    `gorm:"column:use_gf_roll"`                // 用坯匹数，乘100存
	UseGfWeight            int    `gorm:"column:use_gf_weight"`              // 用坯数量，乘10000存
	DyeDeliveryOrderWeight int    `gorm:"column:dye_delivery_order_weight"`  // 染厂单据数量，乘100存
	ReceiveWeight          int    `gorm:"column:receive_weight"`             // 本次收货数量(公斤)，乘10000存
	PTWeightAndWeightError int    `gorm:"column:pt_weight_and_weight_error"` // 供方纸筒空差(公斤)，乘10000存
	ShouldPayWeight        int    `gorm:"column:should_pay_weight"`          // 应付数量(公斤)，乘10000存
}

// 查询后的钩子
func (r *FpmInOrderItem) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmInOrderItem) GetId() uint64 {
	return r.Id
}

// TableName FpmInOrderItem 表名
func (FpmInOrderItem) TableName() string {
	return "fpm_in_order_item"
}

func (FpmInOrderItem) isHardDelete() bool {
	return true
}

func (r FpmInOrderItem) IsMain() bool {
	return false
}

func (r FpmInOrderItem) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmInOrderItem) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (FpmInOrderItem) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmInOrderItemNotExist
}

func (FpmInOrderItem) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmInOrderItemAlreadyExist
}

func (r FpmInOrderItem) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func NewFpmInOrderItem(
	ctx context.Context,
	p *structure.AddFpmInOrderItemParam,
) (r FpmInOrderItem) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.SumStockId = p.SumStockId
	r.QuoteOrderNo = p.QuoteOrderNo
	r.QuoteOrderItemId = p.QuoteOrderItemId
	r.CustomerId = p.CustomerId
	r.ProductColorId = p.ProductColorId
	r.ProductId = p.ProductId
	r.ProductName = p.ProductName
	r.ProductCode = p.ProductCode
	r.ProductColorCode = p.ProductColorCode
	r.ProductColorName = p.ProductColorName
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.QuoteRoll = p.QuoteRoll
	r.QuoteTotalWeight = p.QuoteTotalWeight
	r.InRoll = p.InRoll
	r.TotalWeight = p.TotalWeight
	r.WeightError = p.WeightError
	r.PaperTubeWeight = p.PaperTubeWeight
	r.ReceiveWeight = p.ReceiveWeight
	r.PTWeightAndWeightError = p.PTWeightAndWeightError
	r.ShouldPayWeight = p.ShouldPayWeight
	r.SettleWeight = p.SettleWeight
	r.QuoteWeight = p.QuoteWeight
	r.UnitPrice = p.UnitPrice
	r.InLength = p.InLength
	r.QuoteLength = p.QuoteLength
	r.UnitId = p.UnitId
	r.AuxiliaryUnitId = p.AuxiliaryUnitId
	r.LengthUnitPrice = p.LengthUnitPrice
	r.OtherPrice = p.OtherPrice
	r.TotalPrice = p.TotalPrice
	r.Remark = p.Remark
	r.ActuallyWeight = p.ActuallyWeight
	r.SettleErrorWeight = p.SettleErrorWeight
	r.ArrangeOrderItemId = p.ArrangeOrderItemId
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.SalePlanOrderItemId = p.SalePlanOrderItemId
	r.SalePlanOrderItemNo = p.SalePlanOrderItemNo
	return
}

// 加工进仓单
func NewFpmProcessInOrderItem(
	ctx context.Context,
	p *structure.AddFpmProcessInOrderItemParam,
) (r FpmInOrderItem) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.WarehouseInType = common.WarehouseGoodInTypeProcess
	r.SumStockId = p.SumStockId
	r.ParentOrderNo = p.ParentOrderNo
	r.QuoteOrderNo = p.QuoteOrderNo
	r.QuoteOrderItemId = p.QuoteOrderItemId
	r.QuoteOrderType = p.QuoteOrderType
	r.ProductId = p.ProductId
	r.ProductCode = p.ProductCode
	r.ProductName = p.ProductName
	r.CustomerId = p.CustomerId
	r.ProductColorId = p.ProductColorId
	r.ProductColorCode = p.ProductColorCode
	r.ProductColorName = p.ProductColorName
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.ProductCraft = p.ProductCraft
	r.ProductIngredient = p.ProductIngredient
	r.InRoll = p.InRoll
	r.CustomerAccountNum = p.CustomerAccountNum
	r.ContractNumber = p.ContractNumber
	r.DyeDeliveryOrderId = p.DyeDeliveryOrderId
	r.DyeDeliveryOrderNo = p.DyeDeliveryOrderNo
	r.UseGfRoll = p.UseGfRoll
	r.UseGfWeight = p.UseGfWeight
	r.InWeight = p.InWeight
	r.WeightError = p.WeightError
	r.SettleWeight = p.SettleWeight
	r.UnitId = p.UnitId
	r.DyeDeliveryOrderWeight = p.DyeDeliveryOrderWeight
	r.InLength = p.InLength
	r.Remark = p.Remark
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.SalePlanOrderItemId = p.SalePlanOrderItemId
	r.SalePlanOrderItemNo = p.SalePlanOrderItemNo
	r.DyeingSituId = p.DyeingSituId
	return
}

// 销售退货进仓单
func NewFpmSaleReturnInOrderItem(
	ctx context.Context,
	p *structure.AddFpmSaleReturnInOrderItemParam,
) (r FpmInOrderItem) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.WarehouseInType = common.WarehouseGoodInTypeSaleReturn
	r.ParentId = p.ParentId
	r.SumStockId = p.SumStockId
	r.ParentOrderNo = p.ParentOrderNo
	r.QuoteOrderNo = p.QuoteOrderNo
	r.QuoteOrderItemId = p.QuoteOrderItemId
	r.ProductId = p.ProductId
	r.ProductCode = p.ProductCode
	r.ProductName = p.ProductName
	r.CustomerId = p.CustomerId
	r.ProductColorId = p.ProductColorId
	r.ProductColorCode = p.ProductColorCode
	r.ProductColorName = p.ProductColorName
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.ProductLevelId = p.ProductLevelId
	r.ProductRemark = p.ProductRemark
	r.ProductCraft = p.ProductCraft
	r.ProductIngredient = p.ProductIngredient
	r.InRoll = p.InRoll
	r.InWeight = p.InWeight
	r.WeightError = p.WeightError
	r.PaperTubeWeight = p.PaperTubeWeight
	r.SettleWeight = p.SettleWeight
	r.ActuallyWeight = p.ActuallyWeight
	r.SettleErrorWeight = p.SettleErrorWeight
	r.UnitId = p.UnitId
	r.AuxiliaryUnitId = p.AuxiliaryUnitId
	r.InLength = p.InLength
	r.Remark = p.Remark
	r.ReturnRoll = p.ReturnRoll
	r.ReturnWeight = p.ReturnWeight
	r.ReturnLength = p.ReturnLength
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.ReturnPrice = p.ReturnPrice
	r.LengthCutReturnPrice = p.LengthCutReturnPrice
	r.SettlePrice = p.SettlePrice
	return
}

// 成品销售退货进仓 审核反写退货数据
func (r FpmInOrderItemList) ToModifyProductSaleShouldCollectOrderAudit(
	items map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
	productSaleShouldCollectOrderItems structure.ProductSaleItemDataList,
) (
	list map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
	err error,
) {
	for _, fpmSaleReturnInOrderItem := range r {
		if fpmSaleReturnInOrderItem.QuoteOrderItemId == 0 {
			continue
		}
		data, ok := items[fpmSaleReturnInOrderItem.QuoteOrderItemId]
		if !ok {
			productSaleShouldCollectOrderItem := productSaleShouldCollectOrderItems.Pick(fpmSaleReturnInOrderItem.QuoteOrderItemId)
			item := shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder{}
			item.Id = fpmSaleReturnInOrderItem.QuoteOrderItemId
			item.Roll = productSaleShouldCollectOrderItem.Roll
			item.Length = productSaleShouldCollectOrderItem.Length
			item.Weight = productSaleShouldCollectOrderItem.Weight
			item.ReturnRoll += fpmSaleReturnInOrderItem.InRoll
			item.ReturnLength += fpmSaleReturnInOrderItem.InLength
			item.ReturnWeight += fpmSaleReturnInOrderItem.InWeight
			items[item.Id] = item
			continue
		}
		data.ReturnRoll += fpmSaleReturnInOrderItem.InRoll
		data.ReturnLength += fpmSaleReturnInOrderItem.InLength
		data.ReturnWeight += fpmSaleReturnInOrderItem.InWeight
		if data.ReturnRoll > data.Roll {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameterFormat, "退货条数超过送货单条数!"))
			return
		}
		items[data.Id] = data
	}
	list = items
	return
}

// 成品销售退货进仓 消审恢复反写退货数据
func (r FpmInOrderItemList) ToModifyProductSaleShouldCollectOrderWait(
	items map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
) (
	list map[uint64]shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder,
	err error,
) {
	for _, fpmSaleReturnInOrderItem := range r {
		if fpmSaleReturnInOrderItem.QuoteOrderItemId == 0 {
			continue
		}
		data, ok := items[fpmSaleReturnInOrderItem.QuoteOrderItemId]
		if !ok {
			item := shouldCollectOrderStructure.ModifyProductSaleShouldCollectOrder{}
			item.Id = fpmSaleReturnInOrderItem.QuoteOrderItemId
			item.ReturnRoll -= fpmSaleReturnInOrderItem.InRoll
			item.ReturnLength -= fpmSaleReturnInOrderItem.InLength
			item.ReturnWeight -= fpmSaleReturnInOrderItem.InWeight
			items[item.Id] = item
			continue
		}
		data.ReturnRoll -= fpmSaleReturnInOrderItem.InRoll
		data.ReturnLength -= fpmSaleReturnInOrderItem.InLength
		data.ReturnWeight -= fpmSaleReturnInOrderItem.InWeight
		items[data.Id] = data
	}
	list = items
	return
}
