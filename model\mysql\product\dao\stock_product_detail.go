package dao

import (
	common "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"time"

	"gorm.io/gorm"
)

func MustCreateStockProductDetail(tx *mysql_base.Tx, r mysql.StockProductDetail) (o mysql.StockProductDetail, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateStockProductDetail(tx *mysql_base.Tx, r mysql.StockProductDetail) (o mysql.StockProductDetail, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteStockProductDetail(tx *mysql_base.Tx, r mysql.StockProductDetail) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstStockProductDetailByID(tx *mysql_base.Tx, id uint64) (r mysql.StockProductDetail, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

// 根据仓库和二维码获取
func MustFirstStockProductDetailByQrCode(tx *mysql_base.Tx, warehouseId uint64, qrCode string) (r mysql.StockProductDetail, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	if warehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", warehouseId)
	}
	cond.AddEqual("qr_code", qrCode)
	err = mysql_base.MustFirstByCond(tx, &r, cond)

	return
}

// 根据仓库和二维码获取
func FirstStockProductDetailByQrCode(tx *mysql_base.Tx, warehouseId uint64, qrCode string) (r mysql.StockProductDetail, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddTableEqual(r, "warehouse_id", warehouseId)
	cond.AddEqual("qr_code", qrCode)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	if err != nil {
		return
	}
	return
}

func FirstStockProductDetailByID(tx *mysql_base.Tx, id uint64) (r mysql.StockProductDetail, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

// 根据仓库id、缸号、颜色id和卷号获取
func FirstStockProductDetailByColorDyeVolume(tx *mysql_base.Tx, warehouseId uint64, dyelotNumber string, productColorId uint64, volumeNumber int) (r mysql.StockProductDetail, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	if warehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", warehouseId)
	}
	cond.AddTableEqual(r, "dyelot_number", dyelotNumber)
	cond.AddTableEqual(r, "product_color_id", productColorId)
	cond.AddTableEqual(r, "volume_number", volumeNumber)
	// cond.AddNotEqual("id", 0)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	if err != nil {
		return
	}
	return
}

// 根据标签库存id获取
func FindStockProductDetailByID(tx *mysql_base.Tx, objects ...interface{}) (o mysql.StockProductDetailList, err error) {
	ids := mysql.GetStockProductDetailIdList(objects)
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据库存id获取
func FindStockProductDetailByIDs(tx *mysql_base.Tx, ids []uint64) (o mysql.StockProductDetailList, err error) {
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据汇总库存id获取
func FindStockProductDetailByParentID(tx *mysql_base.Tx, stockProductID uint64) (o mysql.StockProductDetailList, err error) {
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "stock_product_id", stockProductID)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据进仓单ID获取库存详情
func FindStockProductDetailByWarehouseInOrderId(tx *mysql_base.Tx, warehouseInOrderId uint64) (o mysql.StockProductDetailList, err error) {
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)
	r.BuildReadCond(tx.Context, cond)

	// 添加查询条件：通过进仓单ID查询
	cond.AddTableEqual(r, "warehouse_in_order_id", warehouseInOrderId)

	// 查询库存详情
	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}

	o = list
	return
}

// 根据汇总库存ids获取
func FindStockProductDetailByParentIDs(tx *mysql_base.Tx, stockProductIDs []uint64) (o mysql.StockProductDetailList, err error) {
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableContainMatch(r, "stock_product_id", stockProductIDs)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

type WashStockProductDetailListQuery struct {
	structure_base.ListQuery
	WarehouseInType common.WarehouseGoodInType
	WarehouseId     uint64
}

// 根据特殊条件获取(清洗数据使用)
func WashStockProductDetailByType(tx *mysql_base.Tx, q *WashStockProductDetailListQuery) (o mysql.StockProductDetailList, count int, err error) {
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)
	r.BuildReadCond(tx.Context, cond)

	if q.WarehouseInType != 0 {
		cond.AddTableEqual(r, "warehouse_in_type", q.WarehouseInType)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}

	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

type VolumeNumber struct {
	volumeNumber int `gorm:"column:volume_number" sqlf:"stock_product_detail.volume_number"`
}

// 根据缸号和颜色id获取最大卷号
func GetMaxVolumeNumber(tx *mysql_base.Tx, dyelotNumber string, productColorId uint64, volumeNumberReq int) (volumeNumber int, exist bool, err error) {
	var (
		r mysql.StockProductDetail
		// list  []VolumeNumber
		cond = mysql_base.NewCondition()
	)
	r.BuildReadCond(tx.Context, cond)

	// if volumeNumberReq != 0 {
	//	cond.AddTableEqual(r, "volume_number", volumeNumberReq)
	// }
	cond.AddTableEqual(r, "dyelot_number", dyelotNumber)
	cond.AddTableEqual(r, "product_color_id", productColorId)
	cond.AddSort("-volume_number")

	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	if err != nil {
		return
	}

	// 获取数据库今日单据最大数
	// sql := "dyelot_number= ? and product_color_id = ?"
	// values := []interface{}{dyelotNumber, productColorId}
	// total, err = mysql_base.GetMaxVolumeNumber(tx, r, sql, values)
	// if err != nil {
	//	return
	// }
	if exist {
		volumeNumber = r.VolumeNumber
	} else {
		// if volumeNumberReq != 0 {
		//	volumeNumber = volumeNumberReq
		// }
		volumeNumber = 0
	}
	return
}

// 获取成品细码库存列表
func SearchStockProductDetail(tx *mysql_base.Tx, q *structure.GetStockProductDetailListQuery) (o mysql.StockProductDetailList, count int, err error) {
	var (
		r           mysql.StockProductDetail
		product     mysql.FinishProduct
		color       mysql.FinishProductColor
		cond        = mysql_base.NewCondition()
		list        []mysql.StockProductDetail
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableLeftJoiner(r, product, "product_id", "id")
	cond.AddTableLeftJoiner(r, color, "product_color_id", "id")
	if q.StockProductId != 0 {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
	}
	// 仓位名称与仓位id互斥
	if q.WarehouseBinName != "" {
		cond.AddTableContainMatch(r, "warehouse_bin_id", q.WarehouseBinIds)
	} else if q.WarehouseBinId != 0 {
		cond.AddTableEqual(r, "warehouse_bin_id", q.WarehouseBinId)
	}
	if q.WarehouseInOrderNo != "" {
		cond.AddTableEqual(r, "warehouse_in_order_no", q.WarehouseInOrderNo)
	}
	if q.WarehouseInType != 0 {
		cond.AddTableEqual(r, "warehouse_in_type", q.WarehouseInType)
	}
	if q.QualityCheckStatus != 0 {
		cond.AddTableEqual(r, "quality_check_status", q.QualityCheckStatus)
	}
	if !q.StartWarehouseInTime.IsYMDZero() && !q.EndWarehouseInTime.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.StartWarehouseInTime.StringYMD(), q.EndWarehouseInTime.StringYMD2DayListTimeYMDHMS())
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.ProductColorKindId != 0 {
		cond.AddTableEqual(r, "product_color_kind_id", q.ProductColorKindId)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if q.CustomerId != 0 {
		cond.AddTableEqual(r, "customer_id", q.CustomerId)
	}
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductLevelId != 0 {
		cond.AddTableEqual(r, "product_level_id", q.ProductLevelId)
	}
	if q.DyelotNumber != "" {
		cond.AddTableFuzzyMatch(r, "dyelot_number", q.DyelotNumber)
	}
	if q.StockShowType != 0 {
		if q.StockShowType == common.StockShowTypeRoll {
			cond.AddTableNotEqual(r, "roll", 0)
			cond.AddTableNotEqual(r, "weight", 0)
		}
		if q.StockShowType == common.StockShowTypeWeight {
			cond.AddTableEqual(r, "roll", 0)
			cond.AddTableNotEqual(r, "weight", 0)
		}
	}
	if q.ProductCode != "" || q.ProductName != "" {
		cond.AddTableContainMatch(r, "product_id", q.ProductIds)
	}
	if q.ProductColorCode != "" || q.ProductColorName != "" {
		cond.AddTableContainMatch(r, "product_color_id", q.ProductColorIds)
	}
	if q.StockStatus != 0 {
		cond.AddTableEqual(r, "status", q.StockStatus)
	} else {
		cond.AddTableEqual(r, "status", common_system.StockStatusWarehouseIn)
	}

	cond.AddSort("finish_product.finish_product_code,finish_product_color.product_color_code,stock_product_detail.dyelot_number,stock_product_detail.volume_number")
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 获取成品缸号库存分组列表
func GetStockProductDyelotNumberList(tx *mysql_base.Tx, q *structure.GetStockProductDyelotNumberDetailListQuery) (o StockDyelotNumberDetailList, count int, err error) {
	var (
		r           mysql.StockProductDetail
		product     mysql.FinishProduct
		color       mysql.FinishProductColor
		cond        = mysql_base.NewCondition()
		list        []StockDyelotNumberDetail
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableLeftJoiner(r, product, "product_id", "id")
	cond.AddTableLeftJoiner(r, color, "product_color_id", "id")
	if q.StockProductId != 0 {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
	}
	if q.WarehouseBinId != 0 {
		cond.AddTableEqual(r, "warehouse_bin_id", q.WarehouseBinId)
	}
	if !q.StartWarehouseInTime.IsYMDZero() && !q.EndWarehouseInTime.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.StartWarehouseInTime.StringYMD(), q.EndWarehouseInTime.StringYMD2DayListTimeYMDHMS())
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.ProductColorKindId != 0 {
		cond.AddTableEqual(r, "product_color_kind_id", q.ProductColorKindId)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if q.CustomerId != 0 {
		cond.AddTableEqual(r, "customer_id", q.CustomerId)
	}
	if q.SupplierId != 0 {
		cond.AddTableEqual(r, "supplier_id", q.SupplierId)
	}
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductLevelId != 0 {
		cond.AddTableEqual(r, "product_level_id", q.ProductLevelId)
	}
	if q.DyelotNumber != "" {
		cond.AddTableFuzzyMatch(r, "dyelot_number", q.DyelotNumber)
	}
	if q.StockShowType != 0 {
		if q.StockShowType == common.StockShowTypeRoll {
			cond.AddTableNotEqual(r, "roll", 0)
			cond.AddTableNotEqual(r, "weight", 0)
		}
		if q.StockShowType == common.StockShowTypeWeight {
			cond.AddTableEqual(r, "roll", 0)
			cond.AddTableNotEqual(r, "weight", 0)
		}
		if q.StockShowType == common.StockShowTypeZero {
			cond.AddTableEqual(r, "roll", 0)
			cond.AddTableEqual(r, "weight", 0)
		}
	} else {
		// 默认只显示有重量的
		cond.AddTableNotEqual(r, "weight", 0)
	}
	if q.ProductName != "" || q.ProductCode != "" {
		cond.AddTableContainMatch(r, "product_id", q.ProductIds)
	}
	if q.ProductColorName != "" || q.ProductColorCode != "" {
		cond.AddTableContainMatch(r, "product_color_id", q.ProductColorIds)
	}
	if q.IsUseByCheckReport {
		// cond.AddTableNotEqual(r, "supplier_id", 0)
		cond.AddSort("-stock_product_detail.warehouse_in_time")
	} else {
		cond.AddSort("finish_product.finish_product_code,finish_product_color.product_color_code,stock_product_detail.dyelot_number")
	}
	if q.IsUseByCheckReport {
		groupFields = []string{"stock_product_detail.product_color_id", "stock_product_detail.dyelot_number", "stock_product_detail.stock_product_id", "stock_product_detail.supplier_id"}
	} else {
		groupFields = []string{"stock_product_detail.product_color_id", "stock_product_detail.dyelot_number", "stock_product_detail.stock_product_id"}
	}

	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 获取成品缸号库存分组列表
func GetStockProductDyelotNumberListV2(tx *mysql_base.Tx, q *structure.GetStockProductDyelotNumberDetailListQuery) (o StockDyelotNumberDetailList, count int, err error) {
	var (
		r           mysql.StockProductDetail
		product     mysql.FinishProduct
		color       mysql.FinishProductColor
		cond        = mysql_base.NewCondition()
		list        []StockDyelotNumberDetail
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableLeftJoiner(r, product, "product_id", "id")
	cond.AddTableLeftJoiner(r, color, "product_color_id", "id")
	if q.StockProductId != 0 {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
	}
	if q.WarehouseBinId != 0 {
		cond.AddTableEqual(r, "warehouse_bin_id", q.WarehouseBinId)
	}
	if !q.StartWarehouseInTime.IsYMDZero() && !q.EndWarehouseInTime.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.StartWarehouseInTime.StringYMD(), q.EndWarehouseInTime.StringYMD2DayListTimeYMDHMS())
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.ProductColorKindId != 0 {
		cond.AddTableEqual(r, "product_color_kind_id", q.ProductColorKindId)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if q.CustomerId != 0 {
		cond.AddTableEqual(r, "customer_id", q.CustomerId)
	}
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductLevelId != 0 {
		cond.AddTableEqual(r, "product_level_id", q.ProductLevelId)
	}
	if q.DyelotNumber != "" {
		cond.AddTableFuzzyMatch(r, "dyelot_number", q.DyelotNumber)
	}
	if q.StockShowType != 0 {
		if q.StockShowType == common.StockShowTypeRoll {
			cond.AddTableNotEqual(r, "roll", 0)
			cond.AddTableNotEqual(r, "weight", 0)
		}
		if q.StockShowType == common.StockShowTypeWeight {
			cond.AddTableEqual(r, "roll", 0)
			cond.AddTableNotEqual(r, "weight", 0)
		}
	}
	if q.ProductName != "" || q.ProductCode != "" {
		cond.AddTableContainMatch(r, "product_id", q.ProductIds)
	}
	if q.ProductColorName != "" || q.ProductColorCode != "" {
		cond.AddTableContainMatch(r, "product_color_id", q.ProductColorIds)
	}
	cond.AddSort("finish_product.finish_product_code,finish_product_color.product_color_code,stock_product_detail.dyelot_number")
	groupFields = []string{"stock_product_detail.product_color_id"}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

type StockDyelotNumberDetailList []StockDyelotNumberDetail

type StockDyelotNumberDetail struct {
	Id                      uint64                     `gorm:"column:id;primaryKey"  sqlf:"stock_product_detail.id" relate:"stock_product_detail_id"`
	StockProductId          uint64                     `gorm:"column:stock_product_id"  sqlf:"stock_product_detail.stock_product_id" relate:"stock_product_id"`               // 库存id
	WarehouseBinId          uint64                     `gorm:"column:warehouse_bin_id"  sqlf:"stock_product_detail.warehouse_bin_id" relate:"warehouse_bin_id"`               // 仓位id
	WarehouseInTime         time.Time                  `gorm:"column:warehouse_in_time" sqlf:"stock_product_detail.warehouse_in_time"`                                        // 入仓时间
	WarehouseOutOrderId     uint64                     `gorm:"column:warehouse_out_order_id" sqlf:"stock_product_detail.warehouse_out_order_id"`                              // 出仓单id
	WarehouseOutOrderNo     string                     `gorm:"column:warehouse_out_order_no" sqlf:"stock_product_detail.warehouse_out_order_no"`                              // 出仓单号
	DyeFactoryColorCode     string                     `gorm:"column:dye_factory_color_code" sqlf:"stock_product_detail.dye_factory_color_code"`                              // 染厂色号
	ProductColorId          uint64                     `gorm:"column:product_color_id"  sqlf:"stock_product_detail.product_color_id" relate:"product_color_id"`               // 颜色id
	ProductColorKindId      uint64                     `gorm:"column:product_color_kind_id" sqlf:"stock_product_detail.product_color_kind_id" relate:"product_color_kind_id"` // 颜色类别id
	WarehouseId             uint64                     `gorm:"column:warehouse_id"  sqlf:"stock_product_detail.warehouse_id" relate:"warehouse_id"`                           // 仓库id
	CustomerId              uint64                     `gorm:"column:customer_id"  sqlf:"stock_product_detail.customer_id" relate:"biz_unit_id,customer_id"`                  // 所属客户id
	ProductId               uint64                     `gorm:"column:product_id"  sqlf:"stock_product_detail.product_id" relate:"product_id"`                                 // 成品id
	FinishProductWidth      string                     `gorm:"column:finish_product_width" sqlf:"stock_product_detail.finish_product_width"`                                  // 成品幅宽
	FinishProductGramWeight string                     `gorm:"column:finish_product_gram_weight" sqlf:"stock_product_detail.finish_product_gram_weight"`                      // 成品克重
	ProductLevelId          uint64                     `gorm:"column:product_level_id"  sqlf:"stock_product_detail.product_level_id" relate:"product_level_id"`               // 成品等级id
	ProductKindId           uint64                     `gorm:"column:product_kind_id"  sqlf:"stock_product_detail.product_kind_id" relate:"product_kind_id"`                  // 布种类型id
	DyelotNumber            string                     `gorm:"column:dyelot_number" sqlf:"stock_product_detail.dyelot_number"`                                                // 缸号
	VolumeNumber            int                        `gorm:"column:volume_number" sqlf:"stock_product_detail.volume_number"`                                                // 卷号
	ProductRemark           string                     `gorm:"column:product_remark" sqlf:"stock_product_detail.product_remark"`                                              // 成品备注
	WeightError             int                        `gorm:"column:weight_error" sqlf:"stock_product_detail.weight_error"`                                                  // 空差数量(公斤)
	PaperTubeWeight         int                        `gorm:"column:paper_tube_weight" sqlf:"stock_product_detail.paper_tube_weight"`                                        // 纸筒数量(公斤)
	Weight                  int                        `gorm:"column:weight" sqlf:"sum(stock_product_detail.weight)"`                                                         // 数量
	Length                  int                        `gorm:"column:length" sqlf:"sum(stock_product_detail.length)"`                                                         // 长度
	Roll                    int                        `gorm:"column:roll" sqlf:"sum(stock_product_detail.roll)"`                                                             // 匹数
	AvailableRoll           int                        `gorm:"column:available_roll" sqlf:"sum(if(stock_product_detail.status=2,0,stock_product_detail.roll))"`               // 可用匹数
	AvailableWeight         int                        `gorm:"column:available_weight" sqlf:"sum(if(stock_product_detail.status=2,0,stock_product_detail.weight))"`           // 可用数量
	DigitalCode             string                     `gorm:"column:digital_code" sqlf:"stock_product_detail.digital_code"`                                                  // 数字码
	ContractNumber          string                     `gorm:"column:contract_number" sqlf:"stock_product_detail.contract_number"`                                            // 合同号
	CustomerPoNum           string                     `gorm:"column:customer_po_num" sqlf:"stock_product_detail.customer_po_num"`                                            // 客户po号
	CustomerAccountNum      string                     `gorm:"column:customer_account_num" sqlf:"stock_product_detail.customer_account_num"`                                  // 客户款号
	MeasurementUnitId       uint64                     `gorm:"column:measurement_unit_id"  sqlf:"stock_product_detail.measurement_unit_id" relate:"measurement_unit_id"`      // 计量单位id
	Remark                  string                     `gorm:"column:remark" sqlf:"stock_product_detail.remark"`                                                              // 库存备注
	Status                  common_system.StockStatus  `gorm:"column:status" sqlf:"stock_product_detail.status"`                                                              // 库存状态1已入库2配布中3已出库
	WarehouseInType         common.WarehouseGoodInType `gorm:"column:warehouse_in_type" sqlf:"stock_product_detail.warehouse_in_type"`                                        // 来源类型
	WarehouseInOrderId      uint64                     `gorm:"column:warehouse_in_order_id" sqlf:"stock_product_detail.warehouse_in_order_id"`                                // 进仓单id
	WarehouseInOrderNo      string                     `gorm:"column:warehouse_in_order_no" sqlf:"stock_product_detail.warehouse_in_order_no"`                                // 进仓单号
	SupplierId              uint64                     `gorm:"column:supplier_id"  sqlf:"stock_product_detail.supplier_id"`                                                   // 供应商id

	// BookRoll   int `gorm:"column:book_roll" sqlf:"sum(stock_product_detail.book_roll)"`     // 预约匹数
	// BookWeight int `gorm:"column:book_weight" sqlf:"sum(stock_product_detail.book_weight)"` // 预约数量
	// StockRoll  int `gorm:"column:stock_roll" sqlf:"sum(stock_product_detail.stock_roll)"`   // 库存匹数

	FinishProductWidthUnitId      uint64 `gorm:"column:finish_product_width_unit_id" sqlf:"stock_product_detail.finish_product_width_unit_id" relate:"dictionary_detail_id"`             // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64 `gorm:"column:finish_product_gram_weight_unit_id" sqlf:"stock_product_detail.finish_product_gram_weight_unit_id" relate:"dictionary_detail_id"` // 成品克重单位id(字典)
}

func (r StockDyelotNumberDetailList) PickByStockProductIdAndDyelotNumber(id uint64, dyelotNumber string) (o StockDyelotNumberDetail) {
	for _, stockDyelotNumberDetail := range r {
		if stockDyelotNumberDetail.StockProductId == id && stockDyelotNumberDetail.DyelotNumber == dyelotNumber {
			return stockDyelotNumberDetail
		}
	}
	return
}

// 获取详情缸号分组列表
func SearchStockProductDyelotNumberDetail(tx *mysql_base.Tx, q *structure.GetStockProductDyelotNumberDetailListQuery) (o StockDyelotNumberDetailList, count int, err error) {
	var (
		r           mysql.StockProductDetail
		cond        = mysql_base.NewCondition()
		list        []StockDyelotNumberDetail
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.StockProductId != 0 {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
	}
	if q.WarehouseBinId != 0 {
		cond.AddTableEqual(r, "warehouse_bin_id", q.WarehouseBinId)
	}
	if !q.StartWarehouseInTime.IsYMDZero() && !q.EndWarehouseInTime.IsYMDZero() {
		cond.AddTableBetween(r, "warehouse_in_time", q.StartWarehouseInTime.StringYMD(), q.EndWarehouseInTime.StringYMD2DayListTimeYMDHMS())
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.ProductColorKindId != 0 {
		cond.AddTableEqual(r, "product_color_kind_id", q.ProductColorKindId)
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if q.CustomerId != 0 {
		cond.AddTableEqual(r, "customer_id", q.CustomerId)
	}
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductLevelId != 0 {
		cond.AddTableEqual(r, "product_level_id", q.ProductLevelId)
	}
	if q.DyelotNumber != "" {
		cond.AddTableFuzzyMatch(r, "dyelot_number", q.DyelotNumber)
	}
	if q.FinishProductWidth != "" {
		cond.AddTableFuzzyMatch(r, "", q.FinishProductWidth)
	}
	if q.FinishProductGramWeight != "" {
		cond.AddTableFuzzyMatch(r, "", q.FinishProductGramWeight)
	}
	// if q.AvailableOnly {
	//	cond.AddTableEqual(r, "status", common_system.StockStatusWarehouseIn)
	// } else {
	//	cond.AddTableContainMatch(r, "status", []common_system.StockStatus{common_system.StockStatusWarehouseIn, common_system.StockStatusArrange})
	// }
	// 处理AvailableOnly条件，不再修改原始条件对象
	// 在后续查询中使用临时事务对象添加HAVING子句
	if q.ProductName != "" || q.ProductCode != "" {
		cond.AddTableContainMatch(r, "product_id", q.ProductIds)
	}
	if q.ProductColorName != "" || q.ProductColorCode != "" {
		cond.AddTableContainMatch(r, "product_color_id", q.ProductColorIds)
	}
	groupFields = []string{"product_color_id", "dyelot_number", "stock_product_id"}

	// 根据是否需要过滤可用库存决定使用哪种查询方式
	if q.AvailableOnly {
		// 创建临时事务对象，添加HAVING条件
		tempDB := tx.DB.Session(&gorm.Session{}).Having("sum(if(stock_product_detail.status=2,0,stock_product_detail.roll)) OR sum(if(stock_product_detail.status=2,0,stock_product_detail.weight)) > 0")
		tempTx := &mysql_base.Tx{DB: tempDB}

		// 使用临时事务执行查询
		count, err = mysql_base.SearchListGroupForPaging(tempTx, &r, q, &list, cond, groupFields...)
	} else {
		// 使用原始事务执行查询
		count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	}

	if err != nil {
		return
	}
	o = list
	return
}

func SearchStockProductDyelotNumberDetailByStockProductId(tx *mysql_base.Tx, ids []uint64) (o StockDyelotNumberDetailList, err error) {
	var (
		r           mysql.StockProductDetail
		cond        = mysql_base.NewCondition()
		list        []StockDyelotNumberDetail
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	cond.AddTableContainMatch(r, "stock_product_id", ids)

	cond.AddTableEqual(r, "status", common_system.StockStatusWarehouseIn)

	groupFields = []string{"product_color_id", "dyelot_number", "stock_product_id"}
	err = mysql_base.SearchListGroup(tx, &r, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

type StockDetailList []StockDetail

type StockDetail struct {
	Id                      uint64                     `gorm:"column:id;primaryKey"  sqlf:"stock_product_detail.id" relate:"stock_product_detail_id"`
	StockProductId          uint64                     `gorm:"column:stock_product_id"  sqlf:"stock_product_detail.stock_product_id" relate:"stock_product_id"`               // 库存id
	WarehouseBinId          uint64                     `gorm:"column:warehouse_bin_id"  sqlf:"stock_product_detail.warehouse_bin_id" relate:"warehouse_bin_id"`               // 仓位id
	WarehouseInTime         time.Time                  `gorm:"column:warehouse_in_time" sqlf:"stock_product_detail.warehouse_in_time"`                                        // 入仓时间
	WarehouseOutOrderId     uint64                     `gorm:"column:warehouse_out_order_id" sqlf:"stock_product_detail.warehouse_out_order_id"`                              // 出仓单id
	WarehouseOutOrderNo     string                     `gorm:"column:warehouse_out_order_no" sqlf:"stock_product_detail.warehouse_out_order_no"`                              // 出仓单号
	DyeFactoryColorCode     string                     `gorm:"column:dye_factory_color_code" sqlf:"stock_product_detail.dye_factory_color_code"`                              // 染厂色号
	ProductColorId          uint64                     `gorm:"column:product_color_id"  sqlf:"stock_product_detail.product_color_id" relate:"product_color_id"`               // 颜色id
	ProductColorKindId      uint64                     `gorm:"column:product_color_kind_id" sqlf:"stock_product_detail.product_color_kind_id" relate:"product_color_kind_id"` // 颜色类别id
	WarehouseId             uint64                     `gorm:"column:warehouse_id"  sqlf:"stock_product_detail.warehouse_id" relate:"warehouse_id"`                           // 仓库id
	CustomerId              uint64                     `gorm:"column:customer_id"  sqlf:"stock_product_detail.customer_id" relate:"biz_unit_id"`                              // 所属客户id
	ProductId               uint64                     `gorm:"column:product_id"  sqlf:"stock_product_detail.product_id" relate:"product_id"`                                 // 成品id
	FinishProductWidth      string                     `gorm:"column:finish_product_width" sqlf:"stock_product_detail.finish_product_width"`                                  // 成品幅宽
	FinishProductGramWeight string                     `gorm:"column:finish_product_gram_weight" sqlf:"stock_product_detail.finish_product_gram_weight"`                      // 成品克重
	ProductLevelId          uint64                     `gorm:"column:product_level_id"  sqlf:"stock_product_detail.product_level_id" relate:"product_level_id"`               // 成品等级id
	ProductKindId           uint64                     `gorm:"column:product_kind_id"  sqlf:"stock_product_detail.product_kind_id" relate:"product_kind_id"`                  // 布种类型id
	DyelotNumber            string                     `gorm:"column:dyelot_number" sqlf:"stock_product_detail.dyelot_number"`                                                // 缸号
	VolumeNumber            int                        `gorm:"column:volume_number" sqlf:"stock_product_detail.volume_number"`                                                // 卷号
	ProductRemark           string                     `gorm:"column:product_remark" sqlf:"stock_product_detail.product_remark"`                                              // 成品备注
	WeightError             int                        `gorm:"column:weight_error" sqlf:"stock_product_detail.weight_error"`                                                  // 空差数量(公斤)
	PaperTubeWeight         int                        `gorm:"column:paper_tube_weight" sqlf:"stock_product_detail.paper_tube_weight"`                                        // 纸筒数量(公斤)
	Weight                  int                        `gorm:"column:weight" sqlf:"stock_product_detail.weight"`                                                              // 数量
	Length                  int                        `gorm:"column:length" sqlf:"stock_product_detail.length"`                                                              // 长度
	Roll                    int                        `gorm:"column:roll" sqlf:"stock_product_detail.roll"`                                                                  // 匹数
	AvailableRoll           int                        `gorm:"column:available_roll" sqlf:"if(status=2,0,stock_product_detail.roll)"`                                         // 可用匹数
	AvailableWeight         int                        `gorm:"column:available_weight" sqlf:"if(status=2,0,stock_product_detail.weight)"`                                     // 可用数量
	DigitalCode             string                     `gorm:"column:digital_code" sqlf:"stock_product_detail.digital_code"`                                                  // 数字码
	ContractNumber          string                     `gorm:"column:contract_number" sqlf:"stock_product_detail.contract_number"`                                            // 合同号
	CustomerPoNum           string                     `gorm:"column:customer_po_num" sqlf:"stock_product_detail.customer_po_num"`                                            // 客户po号
	CustomerAccountNum      string                     `gorm:"column:customer_account_num" sqlf:"stock_product_detail.customer_account_num"`                                  // 客户款号
	ShelfNo                 string                     `gorm:"column:shelf_no" sqlf:"stock_product_detail.shelf_no"`                                                          // 货架号
	MeasurementUnitId       uint64                     `gorm:"column:measurement_unit_id"  sqlf:"stock_product_detail.measurement_unit_id" relate:"measurement_unit_id"`      // 计量单位id
	Remark                  string                     `gorm:"column:remark" sqlf:"stock_product_detail.remark"`                                                              // 库存备注
	InternalRemark          string                     `gorm:"column:internal_remark" sqlf:"stock_product_detail.internal_remark"`                                            // 内部备注
	Status                  common_system.StockStatus  `gorm:"column:status" sqlf:"stock_product_detail.status"`                                                              // 库存状态1已入库2配布中3已出库
	WarehouseInType         common.WarehouseGoodInType `gorm:"column:warehouse_in_type" sqlf:"stock_product_detail.warehouse_in_type"`                                        // 来源类型
	WarehouseInOrderId      uint64                     `gorm:"column:warehouse_in_order_id" sqlf:"stock_product_detail.warehouse_in_order_id"`                                // 进仓单id
	WarehouseInOrderNo      string                     `gorm:"column:warehouse_in_order_no" sqlf:"stock_product_detail.warehouse_in_order_no"`                                // 进仓单号

	FinishProductWidthUnitId      uint64 `gorm:"column:finish_product_width_unit_id" sqlf:"stock_product_detail.finish_product_width_unit_id" relate:"dictionary_detail_id"`             // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64 `gorm:"column:finish_product_gram_weight_unit_id" sqlf:"stock_product_detail.finish_product_gram_weight_unit_id" relate:"dictionary_detail_id"` // 成品克重单位id(字典)
	SettleErrorWeight             int    `gorm:"column:settle_error_weight" sqlf:"stock_product_detail.settle_error_weight"`                                                             // 结算空差
	BarCode                       string `gorm:"column:bar_code" sqlf:"stock_product_detail.bar_code"`
}

// 获取详情细码选择列表
func SearchStockProductWeightDetail(tx *mysql_base.Tx, q *structure.GetStockProductWeightDetailListQuery) (o StockDetailList, count int, err error) {
	var (
		r           mysql.StockProductDetail
		cond        = mysql_base.NewCondition()
		list        []StockDetail
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.ProductId != 0 {
		cond.AddTableEqual(r, "product_id", q.ProductId)
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.WarehouseBinId != 0 {
		cond.AddTableEqual(r, "warehouse_bin_id", q.WarehouseBinId)
	}
	if q.WarehouseInType != 0 {
		cond.AddTableEqual(r, "warehouse_in_type", q.WarehouseInType)
	}
	if q.WarehouseInOrderNo != "" {
		cond.AddTableFuzzyMatch(r, "warehouse_in_order_no", q.WarehouseInOrderNo)
	}
	if q.DyelotNumber != "" {
		cond.AddTableEqual(r, "dyelot_number", q.DyelotNumber)
	}
	if q.StockProductId != 0 {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
	}
	if q.WarehouseOutOrderId != 0 {
		cond.AddTableEqual(r, "warehouse_out_order_id", q.WarehouseOutOrderId)
	}
	if q.Status != 0 {
		cond.AddTableContainMatch(r, "status", []common_system.StockStatus{q.Status})
	} else {
		cond.AddTableContainMatch(r, "status", []common_system.StockStatus{common_system.StockStatusWarehouseIn})
	}
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}

	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据缸号和颜色，获取最新的库存信息WM:warehouse manage
func FindStockProductDyelotNumberWM(tx *mysql_base.Tx, q *structure.GetWarehouseManageQuery) (o StockDyelotNumberDetailList, err error) {
	var (
		r           mysql.StockProductDetail
		cond        = mysql_base.NewCondition()
		list        []StockDyelotNumberDetail
		groupFields []string
	)
	if q.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
	}
	if q.StockProductId != 0 {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
	}
	if q.ProductColorId != 0 {
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
	}
	if q.DyelotNumber != "" {
		cond.AddTableEqual(r, "dyelot_number", q.DyelotNumber)
	}
	groupFields = []string{"product_color_id", "dyelot_number", "stock_product_id"}
	err = mysql_base.SearchListGroup(tx, &r, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据缸号和颜色，获取详细信息List
func FindStockProductByDyelotNumberAndColorList(tx *mysql_base.Tx, q *structure.UnionOutAndInBaseListQuery) (o mysql.StockProductDetailList, err error) {
	var (
		r    mysql.StockProductDetail
		cond = mysql_base.NewCondition()
		list []mysql.StockProductDetail
	)
	if q.IsNoSkipEmpty {
		cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
		cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
		cond.AddTableEqual(r, "dyelot_number", q.DyelotNumber)
	} else {
		if q.WarehouseId != 0 {
			cond.AddTableEqual(r, "warehouse_id", q.WarehouseId)
		}
		if q.StockProductId != 0 {
			cond.AddTableEqual(r, "stock_product_id", q.StockProductId)
		}
		if q.ProductColorId != 0 {
			cond.AddTableEqual(r, "product_color_id", q.ProductColorId)
		}
		if q.DyelotNumber != "" {
			cond.AddTableEqual(r, "dyelot_number", q.DyelotNumber)
		}
	}
	if q.SupplierID != 0 {
		cond.AddTableEqual(r, "supplier_id", q.SupplierID)
	}

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 库存细码出入仓情况
type UnionWarehouseInFc struct {
	Id               uint64                   `gorm:"column:id" sqlf:"fpm_in_order_item_fc.id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time" sqlf:"fpm_in_order_item_fc.create_time"`        // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type" sqlf:"1"`                                         // 1进仓，2出仓
	OrderTime        tools.MyTime             `gorm:"column:order_time" sqlf:"fpm_in_order_item_fc.order_time"`          // 单据时间
	OrderNo          string                   `gorm:"column:order_no" sqlf:"fpm_in_order_item_fc.warehouse_in_order_no"` // 单据编号
	OrderType        int                      `gorm:"column:order_type" sqlf:"fpm_in_order_item_fc.warehouse_in_type"`   // 单据类型
	OutRoll          int                      `gorm:"column:out_roll" sqlf:"0"`                                          // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight" sqlf:"0"`                                        // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length" sqlf:"0"`                                        // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll" sqlf:"fpm_in_order_item_fc.roll"`                   // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight" sqlf:"fpm_in_order_item_fc.base_unit_weight"`     // 数量（进仓）
	InLength         int                      `gorm:"column:in_length" sqlf:"fpm_in_order_item_fc.length"`               // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll" sqlf:"0"`                                   // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight" sqlf:"0"`                                 // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length" sqlf:"0"`                                 // 长度（盈亏）
}

func (*UnionWarehouseInFc) TableName() string {
	return "fpm_in_order_item_fc"
}

func (*UnionWarehouseOutFc) TableName() string {
	return "fpm_out_order_item_fc"
}

func (*UnionWarehouseCheckFc) TableName() string {
	return "product_check_order_weight_item"
}

type UnionWarehouseOutFc struct {
	Id               uint64                   `gorm:"column:id" sqlf:"fpm_out_order_item_fc.id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time" sqlf:"fpm_out_order_item_fc.create_time"`         // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type" sqlf:"2"`                                           // 1进仓，2出仓
	OrderTime        tools.MyTime             `gorm:"column:order_time" sqlf:"fpm_out_order_item_fc.order_time"`           // 单据时间
	OrderNo          string                   `gorm:"column:order_no" sqlf:"fpm_out_order_item_fc.warehouse_out_order_no"` // 单据编号
	OrderType        int                      `gorm:"column:order_type" sqlf:"fpm_out_order_item_fc.warehouse_out_type"`   // 单据类型
	OutRoll          int                      `gorm:"column:out_roll" sqlf:"fpm_out_order_item_fc.roll"`                   // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight" sqlf:"fpm_out_order_item_fc.base_unit_weight"`     // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length" sqlf:"fpm_out_order_item_fc.length"`               // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll" sqlf:"0"`                                             // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight" sqlf:"0"`                                           // 数量（进仓）
	InLength         int                      `gorm:"column:in_length" sqlf:"0"`                                           // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll" sqlf:"0"`                                     // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight" sqlf:"0"`                                   // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length" sqlf:"0"`                                   // 长度（盈亏）
}

type UnionWarehouseCheckFc struct {
	Id               uint64                   `gorm:"column:id" sqlf:"product_check_order_weight_item.id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time" sqlf:"product_check_order_weight_item.create_time"`             // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type" sqlf:"3"`                                                         // 1进仓，2出仓
	OrderTime        tools.MyTime             `gorm:"column:order_time"  sqlf:"product_check_order.check_time"`                          // 单据时间
	OrderNo          string                   `gorm:"column:order_no"  sqlf:"product_check_order.order_no"`                              // 单据编号
	OrderType        int                      `gorm:"column:order_type"  sqlf:"0"`                                                       // 单据类型
	OutRoll          int                      `gorm:"column:out_roll" sqlf:"0"`                                                          // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight" sqlf:"0"`                                                        // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length" sqlf:"0"`                                                        // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll" sqlf:"0"`                                                           // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight" sqlf:"0"`                                                         // 数量（进仓）
	InLength         int                      `gorm:"column:in_length" sqlf:"0"`                                                         // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll" sqlf:"product_check_order_weight_item.difference_roll"`     // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight" sqlf:"product_check_order_weight_item.difference_weight"` // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length" sqlf:"product_check_order_weight_item.difference_length"` // 长度（盈亏）
}

type UnionOutAndInBase struct {
	Id               uint64                   `gorm:"column:id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time"`       // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type"`         // 1进仓，2出仓
	OrderTime        tools.MyTime             `gorm:"column:order_time"`        // 单据时间
	UpdaterId        uint64                   `gorm:"column:updater_id"`        // 更新人
	UpdaterName      string                   `gorm:"column:updater_name"`      // 更新人名称
	OrderNo          string                   `gorm:"column:order_no"`          // 单据编号
	OrderType        int                      `gorm:"column:order_type"`        // 单据类型
	OutRoll          int                      `gorm:"column:out_roll"`          // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight"`        // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length"`        // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll"`           // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight"`         // 数量（进仓）
	InLength         int                      `gorm:"column:in_length"`         // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll"`   // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight"` // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length"` // 长度（盈亏）
}

type UnionOutAndInBaseList []UnionOutAndInBase

func FindWarehouseOutAndInList(tx *mysql_base.Tx, q *structure.UnionOutAndInBaseListQuery) (r UnionOutAndInBaseList, total int, err error) {
	var (
		unionWarehouseInOrder,
		unionWarehouseOutOrder,
		unionWarehouseCheckOrder mysql_base.UnionItem

		unionAggregate = mysql_base.NewUnionAggregate()
	)

	unionWarehouseInOrder = allWarehouseInOrderV2(q)
	unionWarehouseOutOrder = allWarehouseOutOrderV2(q)
	unionWarehouseCheckOrder = allWarehouseCheckOrderV2(q)

	unionAggregate.AddUnionItem(unionWarehouseInOrder)
	unionAggregate.AddUnionItem(unionWarehouseOutOrder)
	unionAggregate.AddUnionItem(unionWarehouseCheckOrder)
	unionAggregate.AddSort("order_time", "create_time")

	total, err = unionAggregate.Find(tx, q, &r)
	if err != nil {
		return
	}

	return
}

func allWarehouseInOrderV2(q *structure.UnionOutAndInBaseListQuery) (unionWarehouseInOrder mysql_base.UnionItem) {
	cond := mysql_base.NewCondition()
	// stockProduct := mysql.StockProduct{}
	inFc := mysql.FpmInOrderItemFc{}
	if q.StockProductId > 0 {
		cond.AddTableEqual(inFc, "sum_stock_id", q.StockProductId)
	}
	if !q.BeginTime.IsYMDZero() && !q.EndTime.IsYMDZero() {
		cond.AddTableBetween(inFc, "order_time", q.BeginTime.StringYMD(), q.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddEqual("is_in_warehouse", true)
	if len(q.Ids) > 0 {
		cond.AddTableContainMatch(inFc, "stock_id", q.Ids)
	}
	if q.StockDetailID > 0 {
		cond.AddTableEqual(inFc, "stock_id", q.StockDetailID)
	}

	unionWarehouseInOrder = mysql_base.UnionItem{
		MainTable: inFc,
		SubExpr:   UnionWarehouseInFc{},
		Condition: cond,
		BaseType:  1,
	}
	return
}

func allWarehouseOutOrderV2(q *structure.UnionOutAndInBaseListQuery) (unionWarehouseOutOrder mysql_base.UnionItem) {
	cond := mysql_base.NewCondition()
	outFc := mysql.FpmOutOrderItemFc{}
	if q.StockProductId > 0 {
		cond.AddTableEqual(outFc, "sum_stock_id", q.StockProductId)
	}
	if !q.BeginTime.IsYMDZero() && !q.EndTime.IsYMDZero() {
		cond.AddTableBetween(outFc, "order_time", q.BeginTime.StringYMD(), q.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddEqual("is_out_warehouse", true)

	if len(q.Ids) > 0 {
		cond.AddTableContainMatch(outFc, "stock_id", q.Ids)
	}
	if q.StockDetailID > 0 {
		cond.AddTableEqual(outFc, "stock_id", q.StockDetailID)
	}

	unionWarehouseOutOrder = mysql_base.UnionItem{
		MainTable: outFc,
		SubExpr:   UnionWarehouseOutFc{},
		Condition: cond,
		BaseType:  2,
	}
	return
}

func allWarehouseCheckOrderV2(q *structure.UnionOutAndInBaseListQuery) (unionWarehouseOutOrder mysql_base.UnionItem) {
	cond := mysql_base.NewCondition()
	checkFc := mysql.ProductCheckOrderWeightItem{}
	order := mysql.ProductCheckOrder{}
	if q.StockProductId > 0 {
		cond.AddTableEqual(checkFc, "stock_product_id", q.StockProductId)
	}
	cond.AddTableJoiner(checkFc, order, "product_check_order_id", "id")
	if !q.BeginTime.IsYMDZero() && !q.EndTime.IsYMDZero() {
		cond.AddTableBetween(order, "check_time", q.BeginTime.StringYMD(), q.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddTableEqual(checkFc, "delete_time", 0)
	cond.AddTableEqual(order, "delete_time", 0)
	cond.AddTableEqual(order, "audit_status", common_system.OrderStatusAudited)

	if len(q.Ids) > 0 {
		cond.AddTableContainMatch(checkFc, "stock_product_detail_id", q.Ids)
	}
	if q.StockDetailID > 0 {
		cond.AddTableEqual(checkFc, "stock_product_detail_id", q.StockDetailID)
	}

	unionWarehouseOutOrder = mysql_base.UnionItem{
		MainTable: checkFc,
		SubExpr:   UnionWarehouseCheckFc{},
		Condition: cond,
		BaseType:  3,
	}
	return
}

// 库存汇总出入仓情况
type UnionWarehouseInOrder struct {
	Id               uint64                   `gorm:"column:id" sqlf:"fpm_in_order_item_fc.warehouse_in_order_id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time" sqlf:"fpm_in_order_item_fc.create_time"`                // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type" sqlf:"1"`                                                 // 1进仓，2出仓
	OrderTime        tools.MyTime             `gorm:"column:order_time" sqlf:"fpm_in_order_item_fc.order_time"`                  // 单据时间
	UpdaterId        uint64                   `gorm:"column:updater_id" sqlf:"fpm_in_order_item_fc.updater_id" relate:"user_id"` // 更新人
	UpdaterName      string                   `gorm:"column:updater_name" sqlf:"fpm_in_order_item_fc.updater_name"`              // 更新人名称
	OrderNo          string                   `gorm:"column:order_no" sqlf:"fpm_in_order_item_fc.warehouse_in_order_no"`         // 单据编号
	OrderType        int                      `gorm:"column:order_type" sqlf:"fpm_in_order_item_fc.warehouse_in_type"`           // 单据类型
	OutRoll          int                      `gorm:"column:out_roll" sqlf:"0"`                                                  // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight" sqlf:"0"`                                                // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length" sqlf:"0"`                                                // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll" sqlf:"sum(fpm_in_order_item_fc.roll)"`                      // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight" sqlf:"sum(fpm_in_order_item_fc.base_unit_weight)"`        // 数量（进仓）
	InLength         int                      `gorm:"column:in_length" sqlf:"sum(fpm_in_order_item_fc.length)"`                  // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll" sqlf:"0"`                                           // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight" sqlf:"0"`                                         // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length" sqlf:"0"`                                         // 长度（盈亏）
}

func (*UnionWarehouseInOrder) TableName() string {
	return "fpm_in_order_item_fc"
}

func (*UnionWarehouseOutOrder) TableName() string {
	return "fpm_out_order_item_fc"
}

type UnionWarehouseOutOrder struct {
	Id               uint64                   `gorm:"column:id" sqlf:"fpm_out_order_item_fc.warehouse_out_order_id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time" sqlf:"fpm_out_order_item_fc.create_time"`                // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type" sqlf:"2"`                                                  // 1进仓，2出仓
	OrderTime        tools.MyTime             `gorm:"column:order_time" sqlf:"fpm_out_order_item_fc.order_time"`                  // 单据时间
	UpdaterId        uint64                   `gorm:"column:updater_id" sqlf:"fpm_out_order_item_fc.updater_id" relate:"user_id"` // 更新人
	UpdaterName      string                   `gorm:"column:updater_name" sqlf:"fpm_out_order_item_fc.updater_name"`              // 更新人名称
	OrderNo          string                   `gorm:"column:order_no" sqlf:"fpm_out_order_item_fc.warehouse_out_order_no"`        // 单据编号
	OrderType        int                      `gorm:"column:order_type" sqlf:"fpm_out_order_item_fc.warehouse_out_type"`          // 单据类型
	OutRoll          int                      `gorm:"column:out_roll" sqlf:"sum(fpm_out_order_item_fc.roll)"`                     // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight" sqlf:"sum(fpm_out_order_item_fc.base_unit_weight)"`       // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length" sqlf:"sum(fpm_out_order_item_fc.length)"`                 // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll" sqlf:"0"`                                                    // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight" sqlf:"0"`                                                  // 数量（进仓）
	InLength         int                      `gorm:"column:in_length" sqlf:"0"`                                                  // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll" sqlf:"0"`                                            // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight" sqlf:"0"`                                          // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length" sqlf:"0"`                                          // 长度（盈亏）
}

// 盘点 product_check_order_weight_item
type UnionWarehouseCheckOrder struct {
	Id               uint64                   `gorm:"column:id" sqlf:"product_check_order.id"`
	CreateTime       tools.MyTime             `gorm:"column:create_time" sqlf:"product_check_order.create_time"`                              // 单据时间
	DataType         common.WarehouseGoodType `gorm:"column:data_type" sqlf:"3"`                                                              // 1进仓，2出仓 3盘点
	OrderTime        tools.MyTime             `gorm:"column:order_time" sqlf:"product_check_order.check_time"`                                // 单据时间
	UpdaterId        uint64                   `gorm:"column:updater_id" sqlf:"product_check_order.updater_id" relate:"user_id"`               // 更新人
	UpdaterName      string                   `gorm:"column:updater_name" sqlf:"product_check_order.updater_name"`                            // 更新人名称
	OrderNo          string                   `gorm:"column:order_no" sqlf:"product_check_order.order_no"`                                    // 单据编号
	OrderType        int                      `gorm:"column:order_type" sqlf:"0"`                                                             // 单据类型
	OutRoll          int                      `gorm:"column:out_roll" sqlf:"0"`                                                               // 匹数（出仓）
	OutWeight        int                      `gorm:"column:out_weight" sqlf:"0"`                                                             // 数量（出仓）
	OutLength        int                      `gorm:"column:out_length" sqlf:"0"`                                                             // 长度（出仓）
	InRoll           int                      `gorm:"column:in_roll" sqlf:"0"`                                                                // 匹数（进仓）
	InWeight         int                      `gorm:"column:in_weight" sqlf:"0"`                                                              // 数量（进仓）
	InLength         int                      `gorm:"column:in_length" sqlf:"0"`                                                              // 长度（进仓）
	DifferenceRoll   int                      `gorm:"column:difference_roll" sqlf:"sum(product_check_order_weight_item.difference_roll)"`     // 匹数（盈亏）
	DifferenceWeight int                      `gorm:"column:difference_weight" sqlf:"sum(product_check_order_weight_item.difference_weight)"` // 数量（盈亏）
	DifferenceLength int                      `gorm:"column:difference_length" sqlf:"sum(product_check_order_weight_item.difference_length)"` // 长度（盈亏）
}

func FindWarehouseOutAndInSumList(tx *mysql_base.Tx, q *structure.UnionOutAndInBaseListQuery) (r UnionOutAndInBaseList, total int, err error) {
	var (
		unionWarehouseInOrder,
		unionWarehouseOutOrder,
		unionWarehouseCheckOrder mysql_base.UnionItem

		unionAggregate = mysql_base.NewUnionAggregate()
	)

	unionWarehouseInOrder = allWarehouseInOrderV3(q)
	unionWarehouseOutOrder = allWarehouseOutOrderV3(q)
	unionWarehouseCheckOrder = allWarehouseCheckOrderV3(q)

	unionAggregate.AddUnionItem(unionWarehouseInOrder)
	unionAggregate.AddUnionItem(unionWarehouseOutOrder)
	unionAggregate.AddUnionItem(unionWarehouseCheckOrder)
	unionAggregate.AddSort("order_time", "create_time", "id")

	total, err = unionAggregate.Find(tx, q, &r)
	if err != nil {
		return
	}

	return
}

func allWarehouseInOrderV3(q *structure.UnionOutAndInBaseListQuery) (unionWarehouseInOrder mysql_base.UnionItem) {
	cond := mysql_base.NewCondition()
	// stockProduct := mysql.StockProduct{}
	inFc := mysql.FpmInOrderItemFc{}
	if q.StockProductId > 0 {
		cond.AddTableEqual(inFc, "sum_stock_id", q.StockProductId)
	}
	if !q.BeginTime.IsYMDZero() && !q.EndTime.IsYMDZero() {
		cond.AddTableBetween(inFc, "order_time", q.BeginTime.StringYMD(), q.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddEqual("is_in_warehouse", true)
	if len(q.Ids) > 0 {
		cond.AddTableContainMatch(inFc, "stock_id", q.Ids)
	}
	if q.StockDetailID > 0 {
		cond.AddTableEqual(inFc, "stock_id", q.StockDetailID)
	}

	cond.AddGroupFiled(inFc, "warehouse_in_order_id")
	unionWarehouseInOrder = mysql_base.UnionItem{
		MainTable: inFc,
		SubExpr:   UnionWarehouseInOrder{},
		Condition: cond,
		BaseType:  1,
	}
	return
}

func allWarehouseOutOrderV3(q *structure.UnionOutAndInBaseListQuery) (unionWarehouseOutOrder mysql_base.UnionItem) {
	cond := mysql_base.NewCondition()
	outFc := mysql.FpmOutOrderItemFc{}
	if q.StockProductId > 0 {
		cond.AddTableEqual(outFc, "sum_stock_id", q.StockProductId)
	}
	if !q.BeginTime.IsYMDZero() && !q.EndTime.IsYMDZero() {
		cond.AddTableBetween(outFc, "order_time", q.BeginTime.StringYMD(), q.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddEqual("is_out_warehouse", true)

	if len(q.Ids) > 0 {
		cond.AddTableContainMatch(outFc, "stock_id", q.Ids)
	}
	if q.StockDetailID > 0 {
		cond.AddTableEqual(outFc, "stock_id", q.StockDetailID)
	}

	cond.AddGroupFiled(outFc, "warehouse_out_order_id")
	unionWarehouseOutOrder = mysql_base.UnionItem{
		MainTable: outFc,
		SubExpr:   UnionWarehouseOutOrder{},
		Condition: cond,
		BaseType:  2,
	}
	return
}
func allWarehouseCheckOrderV3(q *structure.UnionOutAndInBaseListQuery) (unionWarehouseOutOrder mysql_base.UnionItem) {
	cond := mysql_base.NewCondition()
	checkFc := mysql.ProductCheckOrderWeightItem{}
	order := mysql.ProductCheckOrder{}
	if q.StockProductId > 0 {
		cond.AddTableEqual(checkFc, "stock_product_id", q.StockProductId)
	}
	cond.AddTableJoiner(checkFc, order, "product_check_order_id", "id")
	if !q.BeginTime.IsYMDZero() && !q.EndTime.IsYMDZero() {
		cond.AddTableBetween(order, "check_time", q.BeginTime.StringYMD(), q.EndTime.StringYMD2DayListTimeYMDHMS())
	}
	cond.AddTableEqual(order, "audit_status", common_system.OrderStatusAudited)
	cond.AddTableEqual(checkFc, "delete_time", 0)
	cond.AddTableEqual(order, "delete_time", 0)

	if len(q.Ids) > 0 {
		cond.AddTableContainMatch(checkFc, "stock_product_detail_id", q.Ids)
	}
	if q.StockDetailID > 0 {
		cond.AddTableEqual(checkFc, "stock_product_detail_id", q.StockDetailID)
	}

	cond.AddGroupFiled(checkFc, "product_check_order_id")

	unionWarehouseOutOrder = mysql_base.UnionItem{
		MainTable: checkFc,
		SubExpr:   UnionWarehouseCheckOrder{},
		Condition: cond,
		BaseType:  3,
	}
	return
}

func MustFirstStockProductDetailByCond(tx *mysql_base.Tx, req *structure.GetStockProductDetailListQuery) (r mysql.StockProductDetail, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	if req.WarehouseId != 0 {
		cond.AddTableEqual(r, "warehouse_id", req.WarehouseId)
	}
	if req.BarCode != "" {
		cond.AddTableEqual(r, "bar_code", req.BarCode)
	} else if req.QrCode != "" {
		cond.AddTableFuzzyMatch(r, "qr_code", req.QrCode)
	} else {
		return
	}
	_, err = mysql_base.FirstByCond(tx, &r, cond)

	return
}
