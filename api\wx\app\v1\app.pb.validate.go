// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wx/app/app.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on SyncUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncUserInfoRequestMultiError, or nil if none found.
func (m *SyncUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantManagementId

	if len(errors) > 0 {
		return SyncUserInfoRequestMultiError(errors)
	}

	return nil
}

// SyncUserInfoRequestMultiError is an error wrapping multiple validation
// errors returned by SyncUserInfoRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncUserInfoRequestMultiError) AllErrors() []error { return m }

// SyncUserInfoRequestValidationError is the validation error returned by
// SyncUserInfoRequest.Validate if the designated constraints aren't met.
type SyncUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncUserInfoRequestValidationError) ErrorName() string {
	return "SyncUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncUserInfoRequestValidationError{}

// Validate checks the field values on SyncUserInfoReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SyncUserInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncUserInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncUserInfoReplyMultiError, or nil if none found.
func (m *SyncUserInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncUserInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncUserInfoReplyMultiError(errors)
	}

	return nil
}

// SyncUserInfoReplyMultiError is an error wrapping multiple validation errors
// returned by SyncUserInfoReply.ValidateAll() if the designated constraints
// aren't met.
type SyncUserInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncUserInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncUserInfoReplyMultiError) AllErrors() []error { return m }

// SyncUserInfoReplyValidationError is the validation error returned by
// SyncUserInfoReply.Validate if the designated constraints aren't met.
type SyncUserInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncUserInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncUserInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncUserInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncUserInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncUserInfoReplyValidationError) ErrorName() string {
	return "SyncUserInfoReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SyncUserInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncUserInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncUserInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncUserInfoReplyValidationError{}

// Validate checks the field values on SyncExternalContactRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncExternalContactRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncExternalContactRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncExternalContactRequestMultiError, or nil if none found.
func (m *SyncExternalContactRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncExternalContactRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantManagementId

	if len(errors) > 0 {
		return SyncExternalContactRequestMultiError(errors)
	}

	return nil
}

// SyncExternalContactRequestMultiError is an error wrapping multiple
// validation errors returned by SyncExternalContactRequest.ValidateAll() if
// the designated constraints aren't met.
type SyncExternalContactRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncExternalContactRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncExternalContactRequestMultiError) AllErrors() []error { return m }

// SyncExternalContactRequestValidationError is the validation error returned
// by SyncExternalContactRequest.Validate if the designated constraints aren't met.
type SyncExternalContactRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncExternalContactRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncExternalContactRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncExternalContactRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncExternalContactRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncExternalContactRequestValidationError) ErrorName() string {
	return "SyncExternalContactRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncExternalContactRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncExternalContactRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncExternalContactRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncExternalContactRequestValidationError{}

// Validate checks the field values on SyncExternalContactReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncExternalContactReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncExternalContactReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncExternalContactReplyMultiError, or nil if none found.
func (m *SyncExternalContactReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncExternalContactReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncExternalContactReplyMultiError(errors)
	}

	return nil
}

// SyncExternalContactReplyMultiError is an error wrapping multiple validation
// errors returned by SyncExternalContactReply.ValidateAll() if the designated
// constraints aren't met.
type SyncExternalContactReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncExternalContactReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncExternalContactReplyMultiError) AllErrors() []error { return m }

// SyncExternalContactReplyValidationError is the validation error returned by
// SyncExternalContactReply.Validate if the designated constraints aren't met.
type SyncExternalContactReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncExternalContactReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncExternalContactReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncExternalContactReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncExternalContactReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncExternalContactReplyValidationError) ErrorName() string {
	return "SyncExternalContactReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SyncExternalContactReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncExternalContactReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncExternalContactReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncExternalContactReplyValidationError{}

// Validate checks the field values on SyncGroupChatRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncGroupChatRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncGroupChatRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncGroupChatRequestMultiError, or nil if none found.
func (m *SyncGroupChatRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncGroupChatRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantManagementId

	if len(errors) > 0 {
		return SyncGroupChatRequestMultiError(errors)
	}

	return nil
}

// SyncGroupChatRequestMultiError is an error wrapping multiple validation
// errors returned by SyncGroupChatRequest.ValidateAll() if the designated
// constraints aren't met.
type SyncGroupChatRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncGroupChatRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncGroupChatRequestMultiError) AllErrors() []error { return m }

// SyncGroupChatRequestValidationError is the validation error returned by
// SyncGroupChatRequest.Validate if the designated constraints aren't met.
type SyncGroupChatRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncGroupChatRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncGroupChatRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncGroupChatRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncGroupChatRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncGroupChatRequestValidationError) ErrorName() string {
	return "SyncGroupChatRequestValidationError"
}

// Error satisfies the builtin error interface
func (e SyncGroupChatRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncGroupChatRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncGroupChatRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncGroupChatRequestValidationError{}

// Validate checks the field values on SyncGroupChatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SyncGroupChatReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SyncGroupChatReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SyncGroupChatReplyMultiError, or nil if none found.
func (m *SyncGroupChatReply) ValidateAll() error {
	return m.validate(true)
}

func (m *SyncGroupChatReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return SyncGroupChatReplyMultiError(errors)
	}

	return nil
}

// SyncGroupChatReplyMultiError is an error wrapping multiple validation errors
// returned by SyncGroupChatReply.ValidateAll() if the designated constraints
// aren't met.
type SyncGroupChatReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SyncGroupChatReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SyncGroupChatReplyMultiError) AllErrors() []error { return m }

// SyncGroupChatReplyValidationError is the validation error returned by
// SyncGroupChatReply.Validate if the designated constraints aren't met.
type SyncGroupChatReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SyncGroupChatReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SyncGroupChatReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SyncGroupChatReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SyncGroupChatReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SyncGroupChatReplyValidationError) ErrorName() string {
	return "SyncGroupChatReplyValidationError"
}

// Error satisfies the builtin error interface
func (e SyncGroupChatReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSyncGroupChatReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SyncGroupChatReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SyncGroupChatReplyValidationError{}

// Validate checks the field values on GetQYWXUsersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetQYWXUsersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQYWXUsersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQYWXUsersRequestMultiError, or nil if none found.
func (m *GetQYWXUsersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQYWXUsersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Page

	// no validation rules for Size

	if len(errors) > 0 {
		return GetQYWXUsersRequestMultiError(errors)
	}

	return nil
}

// GetQYWXUsersRequestMultiError is an error wrapping multiple validation
// errors returned by GetQYWXUsersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetQYWXUsersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQYWXUsersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQYWXUsersRequestMultiError) AllErrors() []error { return m }

// GetQYWXUsersRequestValidationError is the validation error returned by
// GetQYWXUsersRequest.Validate if the designated constraints aren't met.
type GetQYWXUsersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQYWXUsersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQYWXUsersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQYWXUsersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQYWXUsersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQYWXUsersRequestValidationError) ErrorName() string {
	return "GetQYWXUsersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetQYWXUsersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQYWXUsersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQYWXUsersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQYWXUsersRequestValidationError{}

// Validate checks the field values on QYWXUser with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *QYWXUser) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on QYWXUser with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in QYWXUserMultiError, or nil
// if none found.
func (m *QYWXUser) ValidateAll() error {
	return m.validate(true)
}

func (m *QYWXUser) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for UserId

	if len(errors) > 0 {
		return QYWXUserMultiError(errors)
	}

	return nil
}

// QYWXUserMultiError is an error wrapping multiple validation errors returned
// by QYWXUser.ValidateAll() if the designated constraints aren't met.
type QYWXUserMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m QYWXUserMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m QYWXUserMultiError) AllErrors() []error { return m }

// QYWXUserValidationError is the validation error returned by
// QYWXUser.Validate if the designated constraints aren't met.
type QYWXUserValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e QYWXUserValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e QYWXUserValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e QYWXUserValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e QYWXUserValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e QYWXUserValidationError) ErrorName() string { return "QYWXUserValidationError" }

// Error satisfies the builtin error interface
func (e QYWXUserValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sQYWXUser.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = QYWXUserValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = QYWXUserValidationError{}

// Validate checks the field values on GetQYWXUsersReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetQYWXUsersReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetQYWXUsersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetQYWXUsersReplyMultiError, or nil if none found.
func (m *GetQYWXUsersReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetQYWXUsersReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetUsers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetQYWXUsersReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetQYWXUsersReplyValidationError{
						field:  fmt.Sprintf("Users[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetQYWXUsersReplyValidationError{
					field:  fmt.Sprintf("Users[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetQYWXUsersReplyMultiError(errors)
	}

	return nil
}

// GetQYWXUsersReplyMultiError is an error wrapping multiple validation errors
// returned by GetQYWXUsersReply.ValidateAll() if the designated constraints
// aren't met.
type GetQYWXUsersReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetQYWXUsersReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetQYWXUsersReplyMultiError) AllErrors() []error { return m }

// GetQYWXUsersReplyValidationError is the validation error returned by
// GetQYWXUsersReply.Validate if the designated constraints aren't met.
type GetQYWXUsersReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetQYWXUsersReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetQYWXUsersReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetQYWXUsersReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetQYWXUsersReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetQYWXUsersReplyValidationError) ErrorName() string {
	return "GetQYWXUsersReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetQYWXUsersReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetQYWXUsersReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetQYWXUsersReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetQYWXUsersReplyValidationError{}

// Validate checks the field values on GetUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoRequestMultiError, or nil if none found.
func (m *GetUserInfoRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Code

	if len(errors) > 0 {
		return GetUserInfoRequestMultiError(errors)
	}

	return nil
}

// GetUserInfoRequestMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoRequest.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoRequestMultiError) AllErrors() []error { return m }

// GetUserInfoRequestValidationError is the validation error returned by
// GetUserInfoRequest.Validate if the designated constraints aren't met.
type GetUserInfoRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoRequestValidationError) ErrorName() string {
	return "GetUserInfoRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetUserInfoRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoRequestValidationError{}

// Validate checks the field values on GetUserInfoReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetUserInfoReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetUserInfoReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetUserInfoReplyMultiError, or nil if none found.
func (m *GetUserInfoReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetUserInfoReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UserId

	if len(errors) > 0 {
		return GetUserInfoReplyMultiError(errors)
	}

	return nil
}

// GetUserInfoReplyMultiError is an error wrapping multiple validation errors
// returned by GetUserInfoReply.ValidateAll() if the designated constraints
// aren't met.
type GetUserInfoReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetUserInfoReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetUserInfoReplyMultiError) AllErrors() []error { return m }

// GetUserInfoReplyValidationError is the validation error returned by
// GetUserInfoReply.Validate if the designated constraints aren't met.
type GetUserInfoReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetUserInfoReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetUserInfoReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetUserInfoReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetUserInfoReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetUserInfoReplyValidationError) ErrorName() string { return "GetUserInfoReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetUserInfoReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetUserInfoReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetUserInfoReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetUserInfoReplyValidationError{}

// Validate checks the field values on GetCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomersRequestMultiError, or nil if none found.
func (m *GetCustomersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for FollowUserId

	// no validation rules for Page

	// no validation rules for Size

	if len(errors) > 0 {
		return GetCustomersRequestMultiError(errors)
	}

	return nil
}

// GetCustomersRequestMultiError is an error wrapping multiple validation
// errors returned by GetCustomersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetCustomersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomersRequestMultiError) AllErrors() []error { return m }

// GetCustomersRequestValidationError is the validation error returned by
// GetCustomersRequest.Validate if the designated constraints aren't met.
type GetCustomersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomersRequestValidationError) ErrorName() string {
	return "GetCustomersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomersRequestValidationError{}

// Validate checks the field values on GetCustomersReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetCustomersReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomersReplyMultiError, or nil if none found.
func (m *GetCustomersReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomersReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomersReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomersReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomersReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCustomersReplyMultiError(errors)
	}

	return nil
}

// GetCustomersReplyMultiError is an error wrapping multiple validation errors
// returned by GetCustomersReply.ValidateAll() if the designated constraints
// aren't met.
type GetCustomersReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomersReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomersReplyMultiError) AllErrors() []error { return m }

// GetCustomersReplyValidationError is the validation error returned by
// GetCustomersReply.Validate if the designated constraints aren't met.
type GetCustomersReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomersReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomersReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomersReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomersReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomersReplyValidationError) ErrorName() string {
	return "GetCustomersReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomersReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomersReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomersReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomersReplyValidationError{}

// Validate checks the field values on Customers with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Customers) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Customers with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CustomersMultiError, or nil
// if none found.
func (m *Customers) ValidateAll() error {
	return m.validate(true)
}

func (m *Customers) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for Type

	// no validation rules for CorpName

	// no validation rules for CorpFullName

	// no validation rules for Gender

	if len(errors) > 0 {
		return CustomersMultiError(errors)
	}

	return nil
}

// CustomersMultiError is an error wrapping multiple validation errors returned
// by Customers.ValidateAll() if the designated constraints aren't met.
type CustomersMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CustomersMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CustomersMultiError) AllErrors() []error { return m }

// CustomersValidationError is the validation error returned by
// Customers.Validate if the designated constraints aren't met.
type CustomersValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CustomersValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CustomersValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CustomersValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CustomersValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CustomersValidationError) ErrorName() string { return "CustomersValidationError" }

// Error satisfies the builtin error interface
func (e CustomersValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCustomers.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CustomersValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CustomersValidationError{}

// Validate checks the field values on GetGroupChatListRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGroupChatListRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGroupChatListRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGroupChatListRequestMultiError, or nil if none found.
func (m *GetGroupChatListRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroupChatListRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Page

	// no validation rules for Size

	if len(errors) > 0 {
		return GetGroupChatListRequestMultiError(errors)
	}

	return nil
}

// GetGroupChatListRequestMultiError is an error wrapping multiple validation
// errors returned by GetGroupChatListRequest.ValidateAll() if the designated
// constraints aren't met.
type GetGroupChatListRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroupChatListRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroupChatListRequestMultiError) AllErrors() []error { return m }

// GetGroupChatListRequestValidationError is the validation error returned by
// GetGroupChatListRequest.Validate if the designated constraints aren't met.
type GetGroupChatListRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroupChatListRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroupChatListRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroupChatListRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroupChatListRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroupChatListRequestValidationError) ErrorName() string {
	return "GetGroupChatListRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetGroupChatListRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroupChatListRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroupChatListRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroupChatListRequestValidationError{}

// Validate checks the field values on GetGroupChatListReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGroupChatListReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGroupChatListReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGroupChatListReplyMultiError, or nil if none found.
func (m *GetGroupChatListReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroupChatListReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetGroupChatListReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetGroupChatListReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetGroupChatListReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetGroupChatListReplyMultiError(errors)
	}

	return nil
}

// GetGroupChatListReplyMultiError is an error wrapping multiple validation
// errors returned by GetGroupChatListReply.ValidateAll() if the designated
// constraints aren't met.
type GetGroupChatListReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroupChatListReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroupChatListReplyMultiError) AllErrors() []error { return m }

// GetGroupChatListReplyValidationError is the validation error returned by
// GetGroupChatListReply.Validate if the designated constraints aren't met.
type GetGroupChatListReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroupChatListReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroupChatListReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroupChatListReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroupChatListReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroupChatListReplyValidationError) ErrorName() string {
	return "GetGroupChatListReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetGroupChatListReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroupChatListReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroupChatListReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroupChatListReplyValidationError{}

// Validate checks the field values on GroupChat with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GroupChat) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GroupChat with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GroupChatMultiError, or nil
// if none found.
func (m *GroupChat) ValidateAll() error {
	return m.validate(true)
}

func (m *GroupChat) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Owner

	// no validation rules for ChatId

	// no validation rules for CreateTime

	if len(errors) > 0 {
		return GroupChatMultiError(errors)
	}

	return nil
}

// GroupChatMultiError is an error wrapping multiple validation errors returned
// by GroupChat.ValidateAll() if the designated constraints aren't met.
type GroupChatMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GroupChatMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GroupChatMultiError) AllErrors() []error { return m }

// GroupChatValidationError is the validation error returned by
// GroupChat.Validate if the designated constraints aren't met.
type GroupChatValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GroupChatValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GroupChatValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GroupChatValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GroupChatValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GroupChatValidationError) ErrorName() string { return "GroupChatValidationError" }

// Error satisfies the builtin error interface
func (e GroupChatValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGroupChat.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GroupChatValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GroupChatValidationError{}

// Validate checks the field values on GetCustomersWithDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetCustomersWithDetailReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetCustomersWithDetailReply with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetCustomersWithDetailReplyMultiError, or nil if none found.
func (m *GetCustomersWithDetailReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomersWithDetailReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomersWithDetailReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomersWithDetailReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomersWithDetailReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCustomersWithDetailReplyMultiError(errors)
	}

	return nil
}

// GetCustomersWithDetailReplyMultiError is an error wrapping multiple
// validation errors returned by GetCustomersWithDetailReply.ValidateAll() if
// the designated constraints aren't met.
type GetCustomersWithDetailReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomersWithDetailReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomersWithDetailReplyMultiError) AllErrors() []error { return m }

// GetCustomersWithDetailReplyValidationError is the validation error returned
// by GetCustomersWithDetailReply.Validate if the designated constraints
// aren't met.
type GetCustomersWithDetailReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomersWithDetailReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomersWithDetailReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetCustomersWithDetailReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomersWithDetailReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomersWithDetailReplyValidationError) ErrorName() string {
	return "GetCustomersWithDetailReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomersWithDetailReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomersWithDetailReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomersWithDetailReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomersWithDetailReplyValidationError{}

// Validate checks the field values on CorpWeChatFriendInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CorpWeChatFriendInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CorpWeChatFriendInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CorpWeChatFriendInfoMultiError, or nil if none found.
func (m *CorpWeChatFriendInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CorpWeChatFriendInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for AddCreateTime

	if len(errors) > 0 {
		return CorpWeChatFriendInfoMultiError(errors)
	}

	return nil
}

// CorpWeChatFriendInfoMultiError is an error wrapping multiple validation
// errors returned by CorpWeChatFriendInfo.ValidateAll() if the designated
// constraints aren't met.
type CorpWeChatFriendInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CorpWeChatFriendInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CorpWeChatFriendInfoMultiError) AllErrors() []error { return m }

// CorpWeChatFriendInfoValidationError is the validation error returned by
// CorpWeChatFriendInfo.Validate if the designated constraints aren't met.
type CorpWeChatFriendInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CorpWeChatFriendInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CorpWeChatFriendInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CorpWeChatFriendInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CorpWeChatFriendInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CorpWeChatFriendInfoValidationError) ErrorName() string {
	return "CorpWeChatFriendInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CorpWeChatFriendInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCorpWeChatFriendInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CorpWeChatFriendInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CorpWeChatFriendInfoValidationError{}

// Validate checks the field values on CorpGroupChatInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CorpGroupChatInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CorpGroupChatInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CorpGroupChatInfoMultiError, or nil if none found.
func (m *CorpGroupChatInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CorpGroupChatInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for GroupChatName

	// no validation rules for JoinTime

	if len(errors) > 0 {
		return CorpGroupChatInfoMultiError(errors)
	}

	return nil
}

// CorpGroupChatInfoMultiError is an error wrapping multiple validation errors
// returned by CorpGroupChatInfo.ValidateAll() if the designated constraints
// aren't met.
type CorpGroupChatInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CorpGroupChatInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CorpGroupChatInfoMultiError) AllErrors() []error { return m }

// CorpGroupChatInfoValidationError is the validation error returned by
// CorpGroupChatInfo.Validate if the designated constraints aren't met.
type CorpGroupChatInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CorpGroupChatInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CorpGroupChatInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CorpGroupChatInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CorpGroupChatInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CorpGroupChatInfoValidationError) ErrorName() string {
	return "CorpGroupChatInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CorpGroupChatInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCorpGroupChatInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CorpGroupChatInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CorpGroupChatInfoValidationError{}

// Validate checks the field values on BindCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindCustomersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindCustomersRequestMultiError, or nil if none found.
func (m *BindCustomersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BindCustomersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BizUnitId

	if len(errors) > 0 {
		return BindCustomersRequestMultiError(errors)
	}

	return nil
}

// BindCustomersRequestMultiError is an error wrapping multiple validation
// errors returned by BindCustomersRequest.ValidateAll() if the designated
// constraints aren't met.
type BindCustomersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindCustomersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindCustomersRequestMultiError) AllErrors() []error { return m }

// BindCustomersRequestValidationError is the validation error returned by
// BindCustomersRequest.Validate if the designated constraints aren't met.
type BindCustomersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindCustomersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindCustomersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindCustomersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindCustomersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindCustomersRequestValidationError) ErrorName() string {
	return "BindCustomersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BindCustomersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindCustomersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindCustomersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindCustomersRequestValidationError{}

// Validate checks the field values on BindCustomersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindCustomersReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindCustomersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindCustomersReplyMultiError, or nil if none found.
func (m *BindCustomersReply) ValidateAll() error {
	return m.validate(true)
}

func (m *BindCustomersReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BindCustomersReplyMultiError(errors)
	}

	return nil
}

// BindCustomersReplyMultiError is an error wrapping multiple validation errors
// returned by BindCustomersReply.ValidateAll() if the designated constraints
// aren't met.
type BindCustomersReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindCustomersReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindCustomersReplyMultiError) AllErrors() []error { return m }

// BindCustomersReplyValidationError is the validation error returned by
// BindCustomersReply.Validate if the designated constraints aren't met.
type BindCustomersReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindCustomersReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindCustomersReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindCustomersReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindCustomersReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindCustomersReplyValidationError) ErrorName() string {
	return "BindCustomersReplyValidationError"
}

// Error satisfies the builtin error interface
func (e BindCustomersReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindCustomersReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindCustomersReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindCustomersReplyValidationError{}

// Validate checks the field values on GetBindCustomersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBindCustomersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBindCustomersRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBindCustomersRequestMultiError, or nil if none found.
func (m *GetBindCustomersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBindCustomersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetBindCustomersRequestMultiError(errors)
	}

	return nil
}

// GetBindCustomersRequestMultiError is an error wrapping multiple validation
// errors returned by GetBindCustomersRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBindCustomersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBindCustomersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBindCustomersRequestMultiError) AllErrors() []error { return m }

// GetBindCustomersRequestValidationError is the validation error returned by
// GetBindCustomersRequest.Validate if the designated constraints aren't met.
type GetBindCustomersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBindCustomersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBindCustomersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBindCustomersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBindCustomersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBindCustomersRequestValidationError) ErrorName() string {
	return "GetBindCustomersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBindCustomersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBindCustomersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBindCustomersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBindCustomersRequestValidationError{}

// Validate checks the field values on GetBindCustomersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBindCustomersReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBindCustomersReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBindCustomersReplyMultiError, or nil if none found.
func (m *GetBindCustomersReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBindCustomersReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBindCustomersReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBindCustomersReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBindCustomersReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetBindCustomers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBindCustomersReplyValidationError{
						field:  fmt.Sprintf("BindCustomers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBindCustomersReplyValidationError{
						field:  fmt.Sprintf("BindCustomers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBindCustomersReplyValidationError{
					field:  fmt.Sprintf("BindCustomers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBindCustomersReplyMultiError(errors)
	}

	return nil
}

// GetBindCustomersReplyMultiError is an error wrapping multiple validation
// errors returned by GetBindCustomersReply.ValidateAll() if the designated
// constraints aren't met.
type GetBindCustomersReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBindCustomersReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBindCustomersReplyMultiError) AllErrors() []error { return m }

// GetBindCustomersReplyValidationError is the validation error returned by
// GetBindCustomersReply.Validate if the designated constraints aren't met.
type GetBindCustomersReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBindCustomersReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBindCustomersReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBindCustomersReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBindCustomersReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBindCustomersReplyValidationError) ErrorName() string {
	return "GetBindCustomersReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetBindCustomersReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBindCustomersReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBindCustomersReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBindCustomersReplyValidationError{}

// Validate checks the field values on GetBindCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBindCustomerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBindCustomerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBindCustomerRequestMultiError, or nil if none found.
func (m *GetBindCustomerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBindCustomerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ExternalUserId

	if len(errors) > 0 {
		return GetBindCustomerRequestMultiError(errors)
	}

	return nil
}

// GetBindCustomerRequestMultiError is an error wrapping multiple validation
// errors returned by GetBindCustomerRequest.ValidateAll() if the designated
// constraints aren't met.
type GetBindCustomerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBindCustomerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBindCustomerRequestMultiError) AllErrors() []error { return m }

// GetBindCustomerRequestValidationError is the validation error returned by
// GetBindCustomerRequest.Validate if the designated constraints aren't met.
type GetBindCustomerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBindCustomerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBindCustomerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBindCustomerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBindCustomerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBindCustomerRequestValidationError) ErrorName() string {
	return "GetBindCustomerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetBindCustomerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBindCustomerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBindCustomerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBindCustomerRequestValidationError{}

// Validate checks the field values on GetBindCustomerReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBindCustomerReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBindCustomerReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetBindCustomerReplyMultiError, or nil if none found.
func (m *GetBindCustomerReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBindCustomerReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ExternalUserId

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for ExternalType

	// no validation rules for ExternalTypeName

	// no validation rules for CorpName

	// no validation rules for CorpFullName

	// no validation rules for BizUnitId

	if len(errors) > 0 {
		return GetBindCustomerReplyMultiError(errors)
	}

	return nil
}

// GetBindCustomerReplyMultiError is an error wrapping multiple validation
// errors returned by GetBindCustomerReply.ValidateAll() if the designated
// constraints aren't met.
type GetBindCustomerReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBindCustomerReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBindCustomerReplyMultiError) AllErrors() []error { return m }

// GetBindCustomerReplyValidationError is the validation error returned by
// GetBindCustomerReply.Validate if the designated constraints aren't met.
type GetBindCustomerReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBindCustomerReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBindCustomerReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBindCustomerReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBindCustomerReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBindCustomerReplyValidationError) ErrorName() string {
	return "GetBindCustomerReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetBindCustomerReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBindCustomerReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBindCustomerReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBindCustomerReplyValidationError{}

// Validate checks the field values on GetSignatureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSignatureRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSignatureRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSignatureRequestMultiError, or nil if none found.
func (m *GetSignatureRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSignatureRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NonceStr

	// no validation rules for Timestamp

	// no validation rules for Url

	if len(errors) > 0 {
		return GetSignatureRequestMultiError(errors)
	}

	return nil
}

// GetSignatureRequestMultiError is an error wrapping multiple validation
// errors returned by GetSignatureRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSignatureRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSignatureRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSignatureRequestMultiError) AllErrors() []error { return m }

// GetSignatureRequestValidationError is the validation error returned by
// GetSignatureRequest.Validate if the designated constraints aren't met.
type GetSignatureRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSignatureRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSignatureRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSignatureRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSignatureRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSignatureRequestValidationError) ErrorName() string {
	return "GetSignatureRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSignatureRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSignatureRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSignatureRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSignatureRequestValidationError{}

// Validate checks the field values on GetSignatureReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSignatureReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSignatureReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSignatureReplyMultiError, or nil if none found.
func (m *GetSignatureReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSignatureReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CorpSignature

	// no validation rules for AppSignature

	if len(errors) > 0 {
		return GetSignatureReplyMultiError(errors)
	}

	return nil
}

// GetSignatureReplyMultiError is an error wrapping multiple validation errors
// returned by GetSignatureReply.ValidateAll() if the designated constraints
// aren't met.
type GetSignatureReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSignatureReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSignatureReplyMultiError) AllErrors() []error { return m }

// GetSignatureReplyValidationError is the validation error returned by
// GetSignatureReply.Validate if the designated constraints aren't met.
type GetSignatureReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSignatureReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSignatureReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSignatureReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSignatureReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSignatureReplyValidationError) ErrorName() string {
	return "GetSignatureReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetSignatureReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSignatureReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSignatureReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSignatureReplyValidationError{}

// Validate checks the field values on
// GetCustomersWithDetailReply_CustomerWithDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetCustomersWithDetailReply_CustomerWithDetail) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on
// GetCustomersWithDetailReply_CustomerWithDetail with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in
// GetCustomersWithDetailReply_CustomerWithDetailMultiError, or nil if none found.
func (m *GetCustomersWithDetailReply_CustomerWithDetail) ValidateAll() error {
	return m.validate(true)
}

func (m *GetCustomersWithDetailReply_CustomerWithDetail) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ExternalUserId

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for ExternalType

	// no validation rules for ExternalTypeName

	// no validation rules for CorpName

	// no validation rules for CorpFullName

	// no validation rules for BizUnitId

	// no validation rules for IsBind

	// no validation rules for CorpWechatFriendCount

	// no validation rules for CorpGroupChatCount

	// no validation rules for Gender

	for idx, item := range m.GetCorpWechatFriendInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomersWithDetailReply_CustomerWithDetailValidationError{
						field:  fmt.Sprintf("CorpWechatFriendInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomersWithDetailReply_CustomerWithDetailValidationError{
						field:  fmt.Sprintf("CorpWechatFriendInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomersWithDetailReply_CustomerWithDetailValidationError{
					field:  fmt.Sprintf("CorpWechatFriendInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetCorpGroupChatInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetCustomersWithDetailReply_CustomerWithDetailValidationError{
						field:  fmt.Sprintf("CorpGroupChatInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetCustomersWithDetailReply_CustomerWithDetailValidationError{
						field:  fmt.Sprintf("CorpGroupChatInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetCustomersWithDetailReply_CustomerWithDetailValidationError{
					field:  fmt.Sprintf("CorpGroupChatInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetCustomersWithDetailReply_CustomerWithDetailMultiError(errors)
	}

	return nil
}

// GetCustomersWithDetailReply_CustomerWithDetailMultiError is an error
// wrapping multiple validation errors returned by
// GetCustomersWithDetailReply_CustomerWithDetail.ValidateAll() if the
// designated constraints aren't met.
type GetCustomersWithDetailReply_CustomerWithDetailMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetCustomersWithDetailReply_CustomerWithDetailMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetCustomersWithDetailReply_CustomerWithDetailMultiError) AllErrors() []error { return m }

// GetCustomersWithDetailReply_CustomerWithDetailValidationError is the
// validation error returned by
// GetCustomersWithDetailReply_CustomerWithDetail.Validate if the designated
// constraints aren't met.
type GetCustomersWithDetailReply_CustomerWithDetailValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetCustomersWithDetailReply_CustomerWithDetailValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetCustomersWithDetailReply_CustomerWithDetailValidationError) Reason() string {
	return e.reason
}

// Cause function returns cause value.
func (e GetCustomersWithDetailReply_CustomerWithDetailValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetCustomersWithDetailReply_CustomerWithDetailValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetCustomersWithDetailReply_CustomerWithDetailValidationError) ErrorName() string {
	return "GetCustomersWithDetailReply_CustomerWithDetailValidationError"
}

// Error satisfies the builtin error interface
func (e GetCustomersWithDetailReply_CustomerWithDetailValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetCustomersWithDetailReply_CustomerWithDetail.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetCustomersWithDetailReply_CustomerWithDetailValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetCustomersWithDetailReply_CustomerWithDetailValidationError{}

// Validate checks the field values on GetBindCustomersReply_BindCustomer with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *GetBindCustomersReply_BindCustomer) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBindCustomersReply_BindCustomer
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// GetBindCustomersReply_BindCustomerMultiError, or nil if none found.
func (m *GetBindCustomersReply_BindCustomer) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBindCustomersReply_BindCustomer) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for CustomerId

	// no validation rules for ExternalUserId

	// no validation rules for Name

	// no validation rules for Avatar

	// no validation rules for ExternalType

	// no validation rules for ExternalTypeName

	// no validation rules for CorpName

	// no validation rules for CorpFullName

	// no validation rules for BizUnitId

	if len(errors) > 0 {
		return GetBindCustomersReply_BindCustomerMultiError(errors)
	}

	return nil
}

// GetBindCustomersReply_BindCustomerMultiError is an error wrapping multiple
// validation errors returned by
// GetBindCustomersReply_BindCustomer.ValidateAll() if the designated
// constraints aren't met.
type GetBindCustomersReply_BindCustomerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBindCustomersReply_BindCustomerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBindCustomersReply_BindCustomerMultiError) AllErrors() []error { return m }

// GetBindCustomersReply_BindCustomerValidationError is the validation error
// returned by GetBindCustomersReply_BindCustomer.Validate if the designated
// constraints aren't met.
type GetBindCustomersReply_BindCustomerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBindCustomersReply_BindCustomerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBindCustomersReply_BindCustomerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBindCustomersReply_BindCustomerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBindCustomersReply_BindCustomerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBindCustomersReply_BindCustomerValidationError) ErrorName() string {
	return "GetBindCustomersReply_BindCustomerValidationError"
}

// Error satisfies the builtin error interface
func (e GetBindCustomersReply_BindCustomerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBindCustomersReply_BindCustomer.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBindCustomersReply_BindCustomerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBindCustomersReply_BindCustomerValidationError{}

// Validate checks the field values on GetBindCustomersReply_BizUnit with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetBindCustomersReply_BizUnit) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetBindCustomersReply_BizUnit with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// GetBindCustomersReply_BizUnitMultiError, or nil if none found.
func (m *GetBindCustomersReply_BizUnit) ValidateAll() error {
	return m.validate(true)
}

func (m *GetBindCustomersReply_BizUnit) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for BizUnitId

	for idx, item := range m.GetBindCustomers() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, GetBindCustomersReply_BizUnitValidationError{
						field:  fmt.Sprintf("BindCustomers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, GetBindCustomersReply_BizUnitValidationError{
						field:  fmt.Sprintf("BindCustomers[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return GetBindCustomersReply_BizUnitValidationError{
					field:  fmt.Sprintf("BindCustomers[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return GetBindCustomersReply_BizUnitMultiError(errors)
	}

	return nil
}

// GetBindCustomersReply_BizUnitMultiError is an error wrapping multiple
// validation errors returned by GetBindCustomersReply_BizUnit.ValidateAll()
// if the designated constraints aren't met.
type GetBindCustomersReply_BizUnitMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetBindCustomersReply_BizUnitMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetBindCustomersReply_BizUnitMultiError) AllErrors() []error { return m }

// GetBindCustomersReply_BizUnitValidationError is the validation error
// returned by GetBindCustomersReply_BizUnit.Validate if the designated
// constraints aren't met.
type GetBindCustomersReply_BizUnitValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetBindCustomersReply_BizUnitValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetBindCustomersReply_BizUnitValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetBindCustomersReply_BizUnitValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetBindCustomersReply_BizUnitValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetBindCustomersReply_BizUnitValidationError) ErrorName() string {
	return "GetBindCustomersReply_BizUnitValidationError"
}

// Error satisfies the builtin error interface
func (e GetBindCustomersReply_BizUnitValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetBindCustomersReply_BizUnit.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetBindCustomersReply_BizUnitValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetBindCustomersReply_BizUnitValidationError{}
