package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	"hcscm/common/product"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/info_basic_data"
	product_basic_pb "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	saleSys "hcscm/extern/pb/sale_system"
	user_pb "hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strconv"
	"strings"
	"time"
)

type IFpmQualityCheckoutReportRepo interface {
	Add(ctx context.Context, req *structure.AddFpmQualityCheckoutReportParam) (id uint64, err error)
	Update(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportParam) (id uint64, err error)
	UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportBusinessCloseParam) (err error)
	UpdateStatusPass(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (err error)
	UpdateStatusWait(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (err error)
	UpdateStatusReject(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (data *structure.UpdateFpmQualityCheckoutReportData, err error)
	UpdateStatusCancel(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (data *structure.UpdateFpmQualityCheckoutReportData, err error)
	Delete(ctx context.Context, req *structure.DeleteFpmQualityCheckoutReportParam) (err error)
	Get(ctx context.Context, id uint64) (data *structure.GetFpmQualityCheckoutReportData, err error)
	GetList(ctx context.Context, req *structure.GetFpmQualityCheckoutReportListQuery) (list structure.GetFpmQualityCheckoutReportDataList, total int, err error)
	GetForPrint(ctx context.Context, req *structure.GetFpmQualityCheckoutReportQuery) (data structure.FpmQualityReportForPrint, err error)
}

type fpmQualityCheckoutReportRepo struct {
	tx                          *mysql_base.Tx
	isCache                     bool
	fpmQualityCheckoutReportDao mysql.IFpmQualityCheckoutReportDao
}

func (r *fpmQualityCheckoutReportRepo) Update(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportParam) (id uint64, err error) {
	var (
		fpmQualityCheckReport model.FpmQualityCheckoutReport
	)
	fpmQualityCheckReport, err = mysql.MustFirstFpmQualityCheckReportByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmQualityCheckReport.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	fpmQualityCheckReport.UpdateFpmQualityCheckoutReport(ctx, req)

	if fpmQualityCheckReport.AuditStatus == common_system.OrderStatusRejected {
		fpmQualityCheckReport.AuditStatus = common_system.OrderStatusPendingAudit
	}

	fpmQualityCheckReport, err = mysql.MustUpdateFpmQualityCheckReport(r.tx, fpmQualityCheckReport)
	if err != nil {
		return
	}
	return fpmQualityCheckReport.Id, nil
}

func NewFpmQualityCheckoutReportRepo(ctx context.Context, tx *mysql_base.Tx, isCache bool) IFpmQualityCheckoutReportRepo {
	return &fpmQualityCheckoutReportRepo{
		tx:                          tx,
		isCache:                     isCache,
		fpmQualityCheckoutReportDao: mysql.NewFpmQualityCheckoutReportDao(ctx, isCache),
	}
}

func (r *fpmQualityCheckoutReportRepo) Add(ctx context.Context, req *structure.AddFpmQualityCheckoutReportParam) (id uint64, err error) {
	var (
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = saleSys.NewSaleSystemClient()
		saleSysData        = saleSys.Res{}
	)
	if req.HandFeelName != "" && req.HandFeelId == 0 {
		// 添加数据字典item
		req.HandFeelId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.HandFeel, "", req.HandFeelName)
	}
	// 抓毛效果名称id
	if req.HairEffectName != "" && req.HairEffectId == 0 {
		req.HairEffectId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.HairEffect, "", req.HairEffectName)
	}
	// 手感名称id
	if req.HandFeelName != "" && req.HandFeelId == 0 {
		req.HandFeelId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.HandFeel, "", req.HandFeelName)
	}
	// 磨毛效果名称id
	if req.ScouringEffectName != "" && req.SandingEffectId == 0 {
		req.SandingEffectId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.ScouringEffect, "", req.ScouringEffectName)
	}
	// 布面毛头名称id
	if req.HairHeadName != "" && req.FabricHairId == 0 {
		req.FabricHairId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.HairHead, "", req.HairHeadName)
	}
	// 布纹斜度名称id
	if req.FabricTiltName != "" && req.FabricTiltId == 0 {
		req.FabricTiltId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.FabricTilt, "", req.FabricTiltName)
	}
	// 对色光源名称id
	if req.ColorLightName != "" && req.ColorLightId == 0 {
		req.ColorLightId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.ColorLight, "", req.ColorLightName)
	}
	// 疋差名称id
	if req.BatchDifferenceName != "" && req.PillingId == 0 {
		req.PillingId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.BatchDifference, "", req.BatchDifferenceName)
	}
	// 阴阳名称id
	if req.PositiveAndNegativeName != "" && req.YinYangId == 0 {
		req.YinYangId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.PositiveAndNegative, "", req.PositiveAndNegativeName)
	}
	// 缩水率id
	if req.ShrinkageRateName != "" && req.ShrinkageRateId == 0 {
		req.ShrinkageRateId, _ = dictionary.NewDictionaryClient().FirstOrCreateDictionaryDetail(ctx, vars.ShrinkageRate, "", req.ShrinkageRateName)
	}
	fpmQualityCheckoutReport := model.NewFpmQualityCheckoutReport(ctx, req)
	// 1. 给个默认的审核状态,初始化业务为开启
	fpmQualityCheckoutReport.AuditStatus = common_system.OrderStatusPendingAudit
	fpmQualityCheckoutReport.BusinessClose = common_system.BusinessCloseNo
	fpmQualityCheckoutReport.DepartmentId = metadata.GetDepartmentId(ctx)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmQualityCheckoutReportPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmQualityCheckoutReport
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmQualityCheckoutReport, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport.OrderNo = orderNo
	fpmQualityCheckoutReport.Number = int(number)
	for _, v := range req.ItemData {
		fpmQualityCheckoutReport.QualityCheckReportIds = append(fpmQualityCheckoutReport.QualityCheckReportIds, v.ID)
	}
	fpmQualityCheckoutRepo := NewFpmQualityCheckRepo(r.tx)
	err = fpmQualityCheckoutRepo.UpdateCheckoutStatusbyIds(ctx, fpmQualityCheckoutReport.QualityCheckReportIds)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustCreate(ctx, r.tx, fpmQualityCheckoutReport)
	if err != nil {
		return
	}
	// 3. 新增坯布信息
	// todo: 逻辑有问题，需要修改
	// for _, item := range req.ItemData {
	// 	fpmQualityCheckoutReportItem := model.NewFpmQualityCheckoutReportItem(ctx, &item)
	// 	fpmQualityCheckoutReportItem.ParentId = fpmQualityCheckoutReport.Id
	// 	fpmQualityCheckoutReportItem.ParentOrderNo = fpmQualityCheckoutReport.OrderNo
	// 	fpmQualityCheckoutReportItem, err = r.fpmQualityCheckoutReportItemDao.MustCreateFpmQualityCheckoutReportItem(r.tx, fpmQualityCheckoutReportItem)
	// 	if err != nil {
	// 		return
	// 	}
	// 	// 3. 添加细码
	// 	for _, fineCode := range item.ItemFCData {
	// 		itemFc := model.NewFpmQualityCheckoutReportItemFc(ctx, &fineCode)
	// 		itemFc.ParentId = fpmQualityCheckoutReportItem.Id
	// 		itemFc, err = r.fpmQualityCheckoutReportItemFcDao.MustCreateFpmQualityCheckoutReportItemFc(r.tx, itemFc)
	// 		if err != nil {
	// 			return
	// 		}
	// 	}
	// }

	id = fpmQualityCheckoutReport.Id
	return
}

// todo: 逻辑有问题，需要修改
// func (r *fpmQualityCheckoutReportRepo) Update(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportParam) (err error) {
// 	var (
// 		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
// 		itemModel                model.FpmQualityCheckoutReportItem
// 		findCodeModel            model.FpmQualityCheckoutReportItemFc
// 		itemList                 model.FpmQualityCheckoutReportItemList
// 	)
// 	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, req.Id)
// 	if err != nil {
// 		return
// 	}
//
// 	fpmQualityCheckoutReport.UpdateFpmQualityCheckoutReport(ctx, req)
//
// 	if fpmQualityCheckoutReport.AuditStatus == common_system.OrderStatusRejected {
// 		fpmQualityCheckoutReport.AuditStatus = common_system.OrderStatusPendingAudit
// 	}
//
// 	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustUpdate(ctx, r.tx, fpmQualityCheckoutReport)
// 	if err != nil {
// 		return
// 	}
//
// 	// 找出该单下的信息，并删除
// 	itemList, err = r.fpmQualityCheckoutReportDao.FindItemByParentId(ctx, r.tx, req.Id)
// 	itemIds := itemList.GetIds()
// 	if len(itemIds) > 0 {
// 		err = r.fpmQualityCheckoutReportDao.MustDeleteItemByIds(ctx, r.tx, itemIds, itemModel)
// 		if err != nil {
// 			return
// 		}
// 		err = r.fpmQualityCheckoutReportDao.MustDeleteByParentIds(ctx, r.tx, itemIds, findCodeModel, "gfm_sale_delivery_item_id")
// 		if err != nil {
// 			return
// 		}
// 	}
//
// 	// 新增坯布信息
// 	for _, item := range req.ItemData {
// 		fpmQualityCheckoutReportItem := model.NewFpmQualityCheckoutReportItem(ctx, &item)
// 		fpmQualityCheckoutReportItem.ParentId = fpmQualityCheckoutReport.Id
// 		fpmQualityCheckoutReportItem.ParentOrderNo = fpmQualityCheckoutReport.OrderNo
// 		fpmQualityCheckoutReportItem, err = r.fpmQualityCheckoutReportItemDao.MustCreateFpmQualityCheckoutReportItem(r.tx, fpmQualityCheckoutReportItem)
// 		if err != nil {
// 			return
// 		}
// 		// 3. 添加细码
// 		for _, fineCode := range item.ItemFCData {
// 			itemFc := model.NewFpmQualityCheckoutReportItemFc(ctx, &fineCode)
// 			itemFc.ParentId = fpmQualityCheckoutReportItem.Id
// 			itemFc, err = r.fpmQualityCheckoutReportItemFcDao.MustCreateItemFcFpmQualityCheckoutReportItemFc(r.tx, itemFc)
// 			if err != nil {
// 				return
// 			}
// 		}
// 	}
// 	return
// }

func (r *fpmQualityCheckoutReportRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportBusinessCloseParam) (err error) {
	var (
		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
	)

	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}
	err = fpmQualityCheckoutReport.UpdateBusinessClose(ctx, req.BusinessClose)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustUpdate(ctx, r.tx, fpmQualityCheckoutReport)
	if err != nil {
		return
	}

	return
}

func (r *fpmQualityCheckoutReportRepo) UpdateStatusPass(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (err error) {
	var (
		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
		// dataV2                  structure.UpdateFpmQualityCheckoutReportStatusData
	)

	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	// 审核
	err = fpmQualityCheckoutReport.Audit(ctx)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustUpdate(ctx, r.tx, fpmQualityCheckoutReport)
	if err != nil {
		return
	}

	return
}

func (r *fpmQualityCheckoutReportRepo) UpdateStatusWait(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (err error) {
	var (
		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
		// dataV2                  structure.UpdateFpmQualityCheckoutReportStatusData
	)

	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	// 消审
	err = fpmQualityCheckoutReport.Wait(ctx)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustUpdate(ctx, r.tx, fpmQualityCheckoutReport)
	if err != nil {
		return
	}
	return
}

func (r *fpmQualityCheckoutReportRepo) UpdateStatusReject(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (data *structure.UpdateFpmQualityCheckoutReportData, err error) {
	var (
		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
		// dataV2                  structure.UpdateFpmQualityCheckoutReportStatusData
	)

	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	// 驳回
	err = fpmQualityCheckoutReport.Reject(ctx)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustUpdate(ctx, r.tx, fpmQualityCheckoutReport)
	if err != nil {
		return
	}
	// 更新质检项的is_generate_report为0
	err = mysql.UpdateFpmQualityCheckIsGenerateReportByParentIds(r.tx, fpmQualityCheckoutReport.QualityCheckReportIds.ToInt(), product.GenerateReportStatusNotGen)
	if err != nil {
		return
	}

	return
}

func (r *fpmQualityCheckoutReportRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateFpmQualityCheckoutReportStatusParam) (data *structure.UpdateFpmQualityCheckoutReportData, err error) {
	var (
		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
		// dataV2                  structure.UpdateFpmQualityCheckoutReportStatusData
	)

	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, req.Id)
	if err != nil {
		return
	}

	// 作废
	err = fpmQualityCheckoutReport.Cancel(ctx)
	if err != nil {
		return
	}
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustUpdate(ctx, r.tx, fpmQualityCheckoutReport)
	if err != nil {
		return
	}

	return
}

// todo: 逻辑有问题，需要修改
func (r *fpmQualityCheckoutReportRepo) Delete(ctx context.Context, req *structure.DeleteFpmQualityCheckoutReportParam) (err error) {
	var (
		list model.FpmQualityCheckoutReportList
	)

	list, err = r.fpmQualityCheckoutReportDao.FindByIds(ctx, r.tx, req.Id.ToUint64(), false)
	if err != nil {
		return
	}
	for _, v := range list {
		fpmQualityCheckoutReport := model.FpmQualityCheckoutReport{}
		fpmQualityCheckoutReport.Id = v.Id
		// 删除
		err = r.fpmQualityCheckoutReportDao.MustDelete(ctx, r.tx, fpmQualityCheckoutReport)
		if err != nil {
			return
		}
		// data.Id = append(data.Id, fpmQualityCheckoutReport.Id)
	}
	return
}

func (r *fpmQualityCheckoutReportRepo) Get(ctx context.Context, id uint64) (data *structure.GetFpmQualityCheckoutReportData, err error) {
	var (
		fpmQualityCheckoutReport model.FpmQualityCheckoutReport
		productBasicPB           = product_basic_pb.NewProductClient()
		productItems             = make(map[uint64]*product_basic_pb.ProductRes)
		productColorMap          = make(map[uint64]*product_basic_pb.ProductColorRes) // // 色号，名称
		productColorPB           = product_basic_pb.NewProductColorClient()
		userNameMap              = make(map[uint64]string)
		bizUnitMap               = make(map[uint64][2]string)
		userPB                   = user_pb.NewUserClient()
		// itemDatas                model.FpmQualityCheckoutReportItemList
		// fineCodeList             model.FpmQualityCheckoutReportItemFcList
		checkIdMaps     = set.NewUint64Set()
		detailStockIds  = set.NewUint64Set()
		detailStockList model.StockProductDetailList
		detailStockMap  = make(map[uint64]model.StockProductDetail)
	)
	fpmQualityCheckoutReport, err = r.fpmQualityCheckoutReportDao.MustFirstById(ctx, r.tx, id)
	if err != nil {
		return
	}
	ProductLevelName, err := info_basic_data.NewInfoBaseFinishedProductLevelClient().GetInfoBaseFinishedProductLevelNameById(ctx, fpmQualityCheckoutReport.ProductLevelId)
	if err != nil {
		return
	}
	for _, v := range fpmQualityCheckoutReport.QualityCheckReportIds {
		checkIdMaps.Add(v)
	}
	fpmQualityChecks, err := mysql.FindFpmQualityCheckByIDs(r.tx, checkIdMaps.List())
	if err != nil {
		return
	}
	for _, check := range fpmQualityChecks {
		detailStockIds.Add(check.StockId)
	}
	detailStockList, _ = mysql.FindStockProductDetailByIDs(r.tx, detailStockIds.List())
	for _, v := range detailStockList {
		detailStockMap[v.Id] = v
	}

	userIds := mysql_base.GetUInt64List(fpmQualityCheckoutReport, "user_id")
	userNameMap, _ = userPB.GetUserNameByIds(ctx, userIds)
	dst, err1 := toFpmQualityCheckData(ctx, r.tx, fpmQualityChecks)
	if err1 != nil {
		return
	}
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	if fpmQualityCheckoutReport.SupplierId != 0 {
		bizUnitMap, _ = bizUnitSvc.GetBizUnitByIds(ctx, []uint64{fpmQualityCheckoutReport.SupplierId})
	} else {
		bizUnitMap, _ = bizUnitSvc.GetBizUnitByIds(ctx, []uint64{fpmQualityCheckoutReport.CustomerId})
	}

	unitIds := mysql_base.GetUInt64List(fpmQualityCheckoutReport, "product_id")
	productItems, err1 = productBasicPB.GetProductMapByIds(ctx, unitIds)
	unitProductColorIds := mysql_base.GetUInt64List(fpmQualityCheckoutReport, "product_color_id")
	productColorMap, err1 = productColorPB.GetProductColorMapByIds(ctx, unitProductColorIds)
	data = &structure.GetFpmQualityCheckoutReportData{}
	data.Id = fpmQualityCheckoutReport.Id
	data.CreateTime = tools.MyTime(fpmQualityCheckoutReport.CreateTime)
	data.CreatorId = fpmQualityCheckoutReport.CreatorId
	data.CreatorName = fpmQualityCheckoutReport.CreatorName
	data.UpdateTime = tools.MyTime(fpmQualityCheckoutReport.UpdateTime)
	data.UpdaterId = fpmQualityCheckoutReport.UpdaterId
	data.UpdateUserName = fpmQualityCheckoutReport.UpdaterName
	// if len(fpmQualityCheckoutReport.QualityCheckReportIds.ToInt()) != 0 {
	// 	data.OrderType, data.ProductName, data.ProductCode, data.ProductColorCode, data.ProductColorName = r.GetInfoByCheckIds(ctx, fpmQualityCheckoutReport.QualityCheckReportIds.ToInt())
	// }
	// data.Id = fpmQualityCheckoutReport.Id
	data.DyelotNumber = fpmQualityCheckoutReport.DyelotNumber
	data.SupplierId = fpmQualityCheckoutReport.SupplierId
	data.QcTechnicalDirectorId = fpmQualityCheckoutReport.QcTechnicalDirectorId
	data.QcTechnicalDirectorName = userNameMap[fpmQualityCheckoutReport.QcTechnicalDirectorId]
	data.QualityCheckDate = tools.MyTime(fpmQualityCheckoutReport.QualityCheckDate)
	data.NeedleCount = fpmQualityCheckoutReport.NeedleCount
	data.CustomerId = fpmQualityCheckoutReport.CustomerId
	data.DyeFactoryId = fpmQualityCheckoutReport.DyeFactoryId
	data.WeaveFactoryId = fpmQualityCheckoutReport.WeaveFactoryId
	data.YarnBatch = fpmQualityCheckoutReport.YarnBatch
	data.StockInNumber = fpmQualityCheckoutReport.StockInNumber
	data.ProductColorId = fpmQualityCheckoutReport.ProductColorId
	data.TotalRoll = fpmQualityCheckoutReport.DyelotCount
	data.TotalWeight = fpmQualityCheckoutReport.DyelotWeight
	data.ProductLevelId = fpmQualityCheckoutReport.ProductLevelId
	data.ShrinkageRateName = fpmQualityCheckoutReport.ShrinkageRateName
	data.ShrinkageRateId = fpmQualityCheckoutReport.ShrinkageRateId
	data.ColorFastness = fpmQualityCheckoutReport.ColorFastness
	data.HandFeelId = fpmQualityCheckoutReport.HandFeelId
	data.Smell = fpmQualityCheckoutReport.Smell
	data.HairEffectId = fpmQualityCheckoutReport.HairEffectId
	data.FabricTiltId = fpmQualityCheckoutReport.FabricTiltId
	data.ColorLightId = fpmQualityCheckoutReport.ColorLightId
	data.YinYangId = fpmQualityCheckoutReport.YinYangId
	data.WaterAbsorption = fpmQualityCheckoutReport.WaterAbsorption
	data.FabricHairId = fpmQualityCheckoutReport.FabricHairId
	data.SandingEffectId = fpmQualityCheckoutReport.SandingEffectId
	data.QcConclusion = fpmQualityCheckoutReport.QcConclusion
	data.QcOpinion = fpmQualityCheckoutReport.QcOpinion
	data.SalesOpinion = fpmQualityCheckoutReport.SalesOpinion
	data.BusinessClose = fpmQualityCheckoutReport.BusinessClose
	data.BusinessCloseUserId = fpmQualityCheckoutReport.BusinessCloseUserId
	data.BusinessCloseUserName = fpmQualityCheckoutReport.BusinessCloseUserName
	data.BusinessCloseTime = tools.MyTime(fpmQualityCheckoutReport.BusinessCloseTime)
	data.DepartmentId = fpmQualityCheckoutReport.DepartmentId
	data.OrderNo = fpmQualityCheckoutReport.OrderNo
	data.StrengthHorizontal = fpmQualityCheckoutReport.StrengthHorizontal
	data.StrengthVertical = fpmQualityCheckoutReport.StrengthVertical
	data.PillingId = fpmQualityCheckoutReport.PillingId
	data.AuditorId = fpmQualityCheckoutReport.AuditorId
	data.AuditorName = fpmQualityCheckoutReport.AuditorName
	data.SaleSystemId = fpmQualityCheckoutReport.SaleSystemId
	data.WarehouseId = fpmQualityCheckoutReport.WarehouseId
	data.AuditTime = tools.MyTime(fpmQualityCheckoutReport.AuditDate)
	data.InspectTotalRoll = fpmQualityCheckoutReport.TotalSampleCount
	data.InspectTotalWeight = fpmQualityCheckoutReport.TotalSampleWeight
	data.AuditStatus = fpmQualityCheckoutReport.AuditStatus
	data.AuditStatusName = fpmQualityCheckoutReport.AuditStatus.String()
	if fpmQualityCheckoutReport.SupplierId != 0 {
		data.SupplierName = bizUnitMap[fpmQualityCheckoutReport.SupplierId][1]
	} else {
		data.CustomerName = bizUnitMap[fpmQualityCheckoutReport.CustomerId][1]
	}

	data.DyeFactoryName = bizUnitMap[fpmQualityCheckoutReport.SupplierId][1]
	// data.WeaveFactoryName = bizUnitMap[fpmQualityCheckoutReport.SupplierId][1]
	data.HandFeelName = fpmQualityCheckoutReport.HandFeelName
	data.HairEffectName = fpmQualityCheckoutReport.HairEffectName
	data.ScouringEffectName = fpmQualityCheckoutReport.ScouringEffectName
	data.HairHeadName = fpmQualityCheckoutReport.HairHeadName
	data.FabricTiltName = fpmQualityCheckoutReport.FabricTiltName
	data.ColorLightName = fpmQualityCheckoutReport.ColorLightName
	data.BatchDifferenceName = fpmQualityCheckoutReport.BatchDifferenceName
	data.PositiveAndNegativeName = fpmQualityCheckoutReport.PositiveAndNegativeName
	data.ProductLevelName = ProductLevelName
	data.Remark = fpmQualityCheckoutReport.Remark
	data.AttachmentsUrl = tools.QueryStringList(strings.Join(fpmQualityCheckoutReport.AttachmentsUrl.ToString(), ","))
	if product, ok := productItems[fpmQualityCheckoutReport.ProductId]; ok {
		data.ProductId = fpmQualityCheckoutReport.ProductId
		data.ProductCode = product.FinishProductCode
		data.ProductName = product.FinishProductName
		data.YarnCount = product.YarnCount
		data.NeedleSize = product.NeedleSize
		data.Density = product.Density
	}
	if productColor, ok := productColorMap[fpmQualityCheckoutReport.ProductColorId]; ok {
		data.ProductColorId = productColor.Id
		data.ProductColorCode = productColor.ProductColorCode
		data.ProductColorName = productColor.ProductColorName
		// 成品幅宽克重
		data.BuildFPResp(productColor.FinishProductWidth, productColor.FinishProductGramWeight, productColor.FinishProductWidthUnitName,
			productColor.FinishProductGramWeightUnitName, productColor.FinishProductWidthUnitId, productColor.FinishProductGramWeightUnitId)
	}
	// var (
	//	warehouseInOrderNo = set.NewStringSet()
	// )
	for _, v := range fpmQualityChecks {
		// stockDetail := detailStockMap[v.StockId]
		data.ItemData = append(data.ItemData, dst[v.Id])
		// warehouseInOrderNo.Add(stockDetail.WarehouseInOrderNo)
	}
	inspectRatio := tools.DecimalDiv(float64(data.InspectTotalRoll)*100, float64(data.TotalRoll))
	data.InspectRatio = int(inspectRatio)
	data.WarehouseInOrderNo = fpmQualityCheckoutReport.StockInNumber
	return
}

func (r *fpmQualityCheckoutReportRepo) GetList(ctx context.Context, req *structure.GetFpmQualityCheckoutReportListQuery) (list structure.GetFpmQualityCheckoutReportDataList, total int, err error) {
	var (
		fpmQualityCheckoutReports model.FpmQualityCheckoutReportList
		productBasicPB            = product_basic_pb.NewProductClient()
		productItems              = make(map[uint64]*product_basic_pb.ProductRes)
		productColorMap           = make(map[uint64]*product_basic_pb.ProductColorRes) // // 色号，名称
		productColorPB            = product_basic_pb.NewProductColorClient()
		userNameMap               = make(map[uint64]string)
		userPB                    = user_pb.NewUserClient()
	)
	fpmQualityCheckoutReports, total, err = r.fpmQualityCheckoutReportDao.SearchList(ctx, r.tx, req)
	if err != nil {
		return
	}
	userIds := mysql_base.GetUInt64List(fpmQualityCheckoutReports, "user_id")
	userNameMap, _ = userPB.GetUserNameByIds(ctx, userIds)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnitIds := mysql_base.GetUInt64List(fpmQualityCheckoutReports, "biz_unit_id")
	bizUnitMap, _ := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	unitIds := mysql_base.GetUInt64List(fpmQualityCheckoutReports, "product_id")
	productItems, _ = productBasicPB.GetProductMapByIds(ctx, unitIds)
	unitProductColorIds := mysql_base.GetUInt64List(fpmQualityCheckoutReports, "product_color_id")
	productColorMap, _ = productColorPB.GetProductColorMapByIds(ctx, unitProductColorIds)
	for _, fpmQualityCheckoutReport := range fpmQualityCheckoutReports {
		var (
			warehouseInOrderNoMap = make(map[string]product.WarehouseGoodInType)
		)
		o := structure.GetFpmQualityCheckoutReportData{}
		var (
			warehouseInTypeName []string
			detailStockList     model.StockProductDetailList
			// detailStockMap      = make(map[uint64]model.StockProductDetail)
		)
		// 根据fpmQualityCheckoutReport.QualityCheckReportIds找到fpmQualityChecks
		fpmQualityChecks, err1 := mysql.FindFpmQualityCheckByIDs(r.tx, fpmQualityCheckoutReport.QualityCheckReportIds.ToInt())
		if err1 != nil {
			err = err1
			return
		}
		ProductLevelName, _ := info_basic_data.NewInfoBaseFinishedProductLevelClient().GetInfoBaseFinishedProductLevelNameById(ctx, fpmQualityCheckoutReport.ProductLevelId)
		if len(fpmQualityCheckoutReport.QualityCheckReportIds) != 0 {
			detailStockIds := set.NewUint64Set()
			for _, v := range fpmQualityChecks {
				detailStockIds.Add(v.StockId)
			}
			detailStockList, _ = mysql.FindStockProductDetailByIDs(r.tx, detailStockIds.List())
			for _, v := range detailStockList {
				warehouseInOrderNoMap[v.WarehouseInOrderNo] = v.WarehouseInType
				// detailStockMap[v.Id] = v
			}
			for _, v := range warehouseInOrderNoMap {
				// stockDetail := detailStockMap[v.StockId]
				warehouseInTypeName = append(warehouseInTypeName, v.String())
			}
		}

		// 证明它是未质检查库存的
		if len(fpmQualityCheckoutReport.QualityCheckReportIds) == 0 {
			detailStockList, err = mysql.FindStockProductByDyelotNumberAndColorList(r.tx, &structure.UnionOutAndInBaseListQuery{
				ProductColorId: fpmQualityCheckoutReport.ProductColorId,
				DyelotNumber:   fpmQualityCheckoutReport.DyelotNumber,
				SupplierID:     fpmQualityCheckoutReport.SupplierId,
			})
			for _, v := range detailStockList {
				// 只添加v.WarehouseInOrderNo不同的到detailStockMap
				// if !slices.Contains(warehouseInOrderNoMap.List(), v.WarehouseInOrderNo) {
				warehouseInOrderNoMap[v.WarehouseInOrderNo] = v.WarehouseInType
				// detailStockMap[v.Id] = v
				// }
			}
			for _, v := range warehouseInOrderNoMap {
				// stockDetail := detailStockMap[v.Id]
				warehouseInTypeName = append(warehouseInTypeName, v.String())
			}
			// 去除warehouseInTypeName重复元素
			// typeSet := set.NewStringSet()
			// for _, typeName := range warehouseInTypeName {
			//	typeSet.Add(typeName)
			// }
			// warehouseInTypeName = typeSet.List()
		}
		o.OrderType = strings.Join(warehouseInTypeName, ",")
		o.WarehouseInOrderNo = fpmQualityCheckoutReport.StockInNumber
		o.Id = fpmQualityCheckoutReport.Id
		o.CreateTime = tools.MyTime(fpmQualityCheckoutReport.CreateTime)
		o.CreatorId = fpmQualityCheckoutReport.CreatorId
		o.CreatorName = fpmQualityCheckoutReport.CreatorName
		o.UpdateTime = tools.MyTime(fpmQualityCheckoutReport.UpdateTime)
		o.UpdaterId = fpmQualityCheckoutReport.UpdaterId
		o.UpdateUserName = fpmQualityCheckoutReport.UpdaterName
		o.DyelotNumber = fpmQualityCheckoutReport.DyelotNumber
		o.SupplierId = fpmQualityCheckoutReport.SupplierId
		o.QcTechnicalDirectorId = fpmQualityCheckoutReport.QcTechnicalDirectorId
		o.QcTechnicalDirectorName = userNameMap[fpmQualityCheckoutReport.QcTechnicalDirectorId]
		o.QualityCheckDate = tools.MyTime(fpmQualityCheckoutReport.QualityCheckDate)
		o.NeedleCount = fpmQualityCheckoutReport.NeedleCount
		o.CustomerId = fpmQualityCheckoutReport.CustomerId
		o.DyeFactoryId = fpmQualityCheckoutReport.DyeFactoryId
		o.WeaveFactoryId = fpmQualityCheckoutReport.WeaveFactoryId
		o.YarnBatch = fpmQualityCheckoutReport.YarnBatch
		// o.StockInNumber = fpmQualityCheckoutReport.StockInNumber
		o.ProductColorId = fpmQualityCheckoutReport.ProductColorId
		o.TotalRoll = fpmQualityCheckoutReport.DyelotCount
		o.TotalWeight = fpmQualityCheckoutReport.DyelotWeight
		o.ProductLevelId = fpmQualityCheckoutReport.ProductLevelId
		o.ShrinkageRateName = fpmQualityCheckoutReport.ShrinkageRateName
		o.ShrinkageRateId = fpmQualityCheckoutReport.ShrinkageRateId
		o.ColorFastness = fpmQualityCheckoutReport.ColorFastness
		o.HandFeelId = fpmQualityCheckoutReport.HandFeelId
		o.Smell = fpmQualityCheckoutReport.Smell
		o.HairEffectId = fpmQualityCheckoutReport.HairEffectId
		o.FabricTiltId = fpmQualityCheckoutReport.FabricTiltId
		o.ColorLightId = fpmQualityCheckoutReport.ColorLightId
		o.YinYangId = fpmQualityCheckoutReport.YinYangId
		o.WaterAbsorption = fpmQualityCheckoutReport.WaterAbsorption
		o.FabricHairId = fpmQualityCheckoutReport.FabricHairId
		o.SandingEffectId = fpmQualityCheckoutReport.SandingEffectId
		o.QcConclusion = fpmQualityCheckoutReport.QcConclusion
		o.QcOpinion = fpmQualityCheckoutReport.QcOpinion
		o.SalesOpinion = fpmQualityCheckoutReport.SalesOpinion
		o.BusinessClose = fpmQualityCheckoutReport.BusinessClose
		o.BusinessCloseUserId = fpmQualityCheckoutReport.BusinessCloseUserId
		o.BusinessCloseUserName = fpmQualityCheckoutReport.BusinessCloseUserName
		o.BusinessCloseTime = tools.MyTime(fpmQualityCheckoutReport.BusinessCloseTime)
		o.DepartmentId = fpmQualityCheckoutReport.DepartmentId
		o.OrderNo = fpmQualityCheckoutReport.OrderNo
		o.StrengthHorizontal = fpmQualityCheckoutReport.StrengthHorizontal
		o.StrengthVertical = fpmQualityCheckoutReport.StrengthVertical
		o.PillingId = fpmQualityCheckoutReport.PillingId
		o.AuditorId = fpmQualityCheckoutReport.AuditorId
		o.AuditorName = fpmQualityCheckoutReport.AuditorName
		o.SaleSystemId = fpmQualityCheckoutReport.SaleSystemId
		o.WarehouseId = fpmQualityCheckoutReport.WarehouseId
		o.AuditTime = tools.MyTime(fpmQualityCheckoutReport.AuditDate)
		o.InspectTotalRoll = fpmQualityCheckoutReport.TotalSampleCount
		o.InspectTotalWeight = fpmQualityCheckoutReport.TotalSampleWeight
		o.AuditStatus = fpmQualityCheckoutReport.AuditStatus
		o.AuditStatusName = fpmQualityCheckoutReport.AuditStatus.String()
		o.ProductLevelName = ProductLevelName
		o.SupplierName = bizUnitMap[fpmQualityCheckoutReport.SupplierId][1]
		o.DyeFactoryName = bizUnitMap[fpmQualityCheckoutReport.SupplierId][1]
		o.WeaveFactoryName = bizUnitMap[fpmQualityCheckoutReport.SupplierId][1]
		o.HandFeelName = fpmQualityCheckoutReport.HandFeelName
		o.HairEffectName = fpmQualityCheckoutReport.HairEffectName
		o.ScouringEffectName = fpmQualityCheckoutReport.ScouringEffectName
		o.HairHeadName = fpmQualityCheckoutReport.HairHeadName
		o.FabricTiltName = fpmQualityCheckoutReport.FabricTiltName
		o.ColorLightName = fpmQualityCheckoutReport.ColorLightName
		o.BatchDifferenceName = fpmQualityCheckoutReport.BatchDifferenceName
		o.PositiveAndNegativeName = fpmQualityCheckoutReport.PositiveAndNegativeName
		o.Remark = fpmQualityCheckoutReport.Remark
		if product, ok := productItems[fpmQualityCheckoutReport.ProductId]; ok {
			o.ProductId = fpmQualityCheckoutReport.ProductId
			o.ProductCode = product.FinishProductCode
			o.ProductName = product.FinishProductName
			o.YarnCount = product.YarnCount
			o.NeedleSize = product.NeedleSize
			o.Density = product.Density
		}
		if productColor, ok := productColorMap[fpmQualityCheckoutReport.ProductColorId]; ok {
			o.ProductColorId = productColor.Id
			o.ProductColorCode = productColor.ProductColorCode
			o.ProductColorName = productColor.ProductColorName
			// 成品幅宽克重
			o.BuildFPResp(productColor.FinishProductWidth, productColor.FinishProductGramWeight, productColor.FinishProductWidthUnitName,
				productColor.FinishProductGramWeightUnitName, productColor.FinishProductWidthUnitId, productColor.FinishProductGramWeightUnitId)
		}
		o.TotalRoll = fpmQualityCheckoutReport.DyelotCount
		o.TotalWeight = fpmQualityCheckoutReport.DyelotWeight
		o.InspectTotalRoll = fpmQualityCheckoutReport.TotalSampleCount
		o.InspectTotalWeight = fpmQualityCheckoutReport.TotalSampleWeight
		inspectRatio := tools.DecimalDiv(float64(o.InspectTotalRoll)*100, float64(o.TotalRoll))
		o.InspectRatio = int(inspectRatio)
		o.DyelotNumber = fpmQualityCheckoutReport.DyelotNumber
		list = append(list, o)
	}
	return
}

// 通过ids找到fpmQualityCheckslist
func (r *fpmQualityCheckoutReportRepo) GetInfoByCheckIds(ctx context.Context, qIds []uint64) (orderTypeName, productName, productCode, productColorCode, productColorName string) {
	var (
		detailStockIds  = set.NewUint64Set()
		detailStockList model.StockProductDetailList
		detailStockMap  = make(map[uint64]model.StockProductDetail)
		productItems    = make(map[uint64]*product_basic_pb.ProductRes)
		productColorMap = make(map[uint64][2]string) // // 色号，名称
		productColorPB  = product_basic_pb.NewProductColorClient()
		productBasicPB  = product_basic_pb.NewProductClient()
		err             error
	)
	fpmQualityChecks, err := mysql.FindFpmQualityCheckByIDs(r.tx, qIds)
	if err != nil {
		return
	}
	for _, check := range fpmQualityChecks {
		detailStockIds.Add(check.StockId)
	}
	g := errgroup.WithCancel(ctx)
	// 详细库存信息
	g.Go(func(ctx context.Context) error {
		var err1 error
		detailStockList, err1 = mysql.FindStockProductDetailByIDs(r.tx, detailStockIds.List())
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindStockProductDetailByIDs err"+err1.Error()))
		}
		for _, v := range detailStockList {
			detailStockMap[v.Id] = v
		}
		return nil
	})
	// 成品信息
	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(fpmQualityChecks, "product_id")
		productItems, err1 = productBasicPB.GetProductMapByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductByIds err"+err1.Error()))
		}
		return nil
	})
	// 成品颜色
	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(fpmQualityChecks, "product_color_id")
		productColorMap, err1 = productColorPB.GetProductColorItemByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductColorItemByIds err"+err1.Error()))
		}
		return nil
	})
	if err = g.Wait(); err != nil {
		return
	}
	for _, fpmQualityCheck := range fpmQualityChecks {
		stockDetail := detailStockMap[fpmQualityCheck.StockId]
		if productName == "" || productCode == "" {
			if product, ok := productItems[fpmQualityCheck.ProductId]; ok {
				productName = product.FinishProductName
				productCode = product.FinishProductCode
			}
		}
		if productColorCode == "" || productColorName == "" {
			productColorCode = productColorMap[fpmQualityCheck.ProductColorId][0]
			productColorName = productColorMap[fpmQualityCheck.ProductColorId][1]
		}

		orderTypeName += stockDetail.WarehouseInType.String() + ","
	}
	// 去掉最后一个逗号
	orderTypeName = strings.TrimRight(orderTypeName, ",")
	return
}

// 打印质检报告单
func (r *fpmQualityCheckoutReportRepo) GetForPrint(ctx context.Context, req *structure.GetFpmQualityCheckoutReportQuery) (data structure.FpmQualityReportForPrint, err error) {
	var (
		supplierId                uint64
		maxTime                   time.Time
		productInfo               product_basic_pb.ProductRes
		colorInfo                 product_basic_pb.ProductColorRes
		productColorMap           = make(map[uint64]*product_basic_pb.ProductColorRes) // // 色号，名称
		userNameMap               map[uint64]string
		bizUnitMap                map[uint64][2]string
		fpmQualityCheckReport     model.FpmQualityCheckoutReport
		fpmQualityCheckList       model.FpmQualityCheckList
		fpmQualityCheckDefectList model.FpmQualityCheckDefectList
		stockProductDetailList    model.StockProductDetailList
		stockProductDetailMap     = make(map[uint64]model.StockProductDetail)
		_data                     structure.FpmQualityReportForPrint
		mark                      = 'A'
		markMap                   = make(map[uint64]string)
		detailStockIds            = set.NewUint64Set()
		productLevelIds           = set.NewUint64Set()
		userIds                   = set.NewUint64Set()
		// unitIds                   = set.NewUint64Set()
		unitId       uint64
		productLevel map[uint64][2]string
		// measurementUnitName       map[uint64]string
		Ids = make([]uint64, 0)
		// stockProductDetailMap     = make(map[uint64]model.StockProductDetail)
		RecordStockInfoMap = make(map[uint64]structure.RecordStockInfo)
		itemList           = make(structure.FpmQualityReportItemForPrintList, 0)
		defectInfoList     = make(structure.BasicDefectInfoForPrintList, 0)
		productLevelName   string
	)
	// 查数据
	fpmQualityCheckReport, err = mysql.MustFirstFpmQualityCheckoutReportByID(r.tx, req.Id)
	productLevelName, _ = info_basic_data.NewInfoBaseFinishedProductLevelClient().GetInfoBaseFinishedProductLevelNameById(ctx, fpmQualityCheckReport.ProductLevelId)
	if err != nil {
		return
	}
	fpmQualityCheckList, err = mysql.FindFpmQualityCheckByIDs(r.tx, fpmQualityCheckReport.QualityCheckReportIds.ToInt())
	if err != nil {
		return
	}
	for _, check := range fpmQualityCheckList {
		Ids = append(Ids, check.Id)
	}
	if len(fpmQualityCheckReport.QualityCheckReportIds) != 0 {
		// 找这些质检下疵点的数据
		fpmQualityCheckDefectList, err = mysql.FindFpmQualityCheckDefectByParentIDs(r.tx, Ids)
		if err != nil {
			return
		}
		// 找该缸号下的细码
		for _, check := range fpmQualityCheckList {
			stockProductDetailList, err = mysql.FindStockProductByDyelotNumberAndColorList(r.tx, &structure.UnionOutAndInBaseListQuery{
				DyelotNumber:   check.DyelotNumber,
				ProductColorId: check.ProductColorId,
				StockProductId: check.SumStockId,
				IsNoSkipEmpty:  true,
			})

			for _, detail := range stockProductDetailList {
				// if detail.SupplierId > 0 {
				//	supplierId = detail.SupplierId
				// }
				detailStockIds.Add(detail.Id)
				productLevelIds.Add(detail.ProductLevelId)
				// unitIds.Add(detail.MeasurementUnitId)
				stockProductDetailMap[detail.Id] = detail
			}
			userIds.Add(check.QualityCheckerId)
		}
		userIds.Add(fpmQualityCheckReport.QcTechnicalDirectorId)
		var (
			productLevelSvc = info_basic_data.NewInfoBaseFinishedProductLevelClient()
			// measurementUnitNameSvc    = info_basic_data.NewInfoBaseMeasurementUnitClient()
			_detailList               model.FpmInOrderItemFcList
			adjustOrderWeightItemList model.ProductAdjustOrderWeightItemList
			checkOrderWeightItemList  model.ProductCheckOrderWeightItemList
		)
		productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelMapByIds(ctx, productLevelIds.List())
		// 计量单位
		// measurementUnitName, _ = measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())

		_detailList, err = mysql.GetWarehouseInDataByDetailIDs(r.tx, detailStockIds.List())
		if err != nil {
			return
		}
		for _, fc := range _detailList {
			if val, ok := RecordStockInfoMap[fc.StockId]; ok {
				val.Roll += fc.Roll
				val.Weight += fc.BaseUnitWeight
			} else {
				RecordStockInfoMap[fc.StockId] = structure.RecordStockInfo{
					Id:           fc.StockId,
					Roll:         fc.Roll,
					Weight:       fc.BaseUnitWeight,
					VolumeNumber: fc.VolumeNumber,
					// MeasurementUnitId: stockProductDetailMap[fc.StockId].MeasurementUnitId,
				}
			}
		}
		if detailStockIds.Size() > 0 {
			adjustOrderWeightItemList, err = mysql.FindProductAdjustOrderWeightItemByDetailStockIds(r.tx, detailStockIds.List())
			if err != nil {
				return
			}
			for _, fc := range adjustOrderWeightItemList {
				if detailStockIds.In(fc.StockProductDetailId) {
					if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
						val.Roll += fc.AdjustRoll
						val.Weight += fc.AdjustWeight
					} else {
						RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
							Id:           fc.StockProductDetailId,
							Roll:         fc.AdjustRoll,
							Weight:       fc.AdjustWeight,
							VolumeNumber: fc.VolumeNumber,
						}
					}
				}
				if detailStockIds.In(fc.AdjustDetailStockId) {
					if val, ok := RecordStockInfoMap[fc.AdjustDetailStockId]; ok {
						val.Roll += fc.AdjustRoll
						val.Weight += fc.AdjustWeight
					} else {
						RecordStockInfoMap[fc.AdjustDetailStockId] = structure.RecordStockInfo{
							Id:           fc.StockProductDetailId,
							Roll:         fc.AdjustRoll,
							Weight:       fc.AdjustWeight,
							VolumeNumber: fc.VolumeNumber,
						}
					}
				}
			}
			checkOrderWeightItemList, err = mysql.FindProductCheckOrderWeightItemByDetailStockIds(r.tx, detailStockIds.List())
			if err != nil {
				return
			}
			for _, fc := range checkOrderWeightItemList {
				if val, ok := RecordStockInfoMap[fc.StockProductDetailId]; ok {
					val.Roll += fc.DifferenceRoll
					val.Weight += fc.DifferenceWeight
				} else {
					RecordStockInfoMap[fc.StockProductDetailId] = structure.RecordStockInfo{
						Id:           fc.StockProductDetailId,
						Roll:         fc.DifferenceRoll,
						Weight:       fc.DifferenceWeight,
						VolumeNumber: fc.VolumeNumber,
					}
				}
			}
		}
		err = tools.Finish(
			func() error {
				var (
					prevMark string
				)
				// 疵点信息
				basicDefectInfoList, _err := info_basic_data.NewInfoBasicDefectClient().GetInfoBasicDefectList(ctx)
				if _err != nil {
					return _err
				}
				for _, defectInfo := range basicDefectInfoList {
					if mark > 'Z' {
						prevMark += "Z"
						mark = 'A'
					}
					markInfo := &structure.BasicDefectInfoForPrint{
						Id:   defectInfo.Id,
						Mark: fmt.Sprintf("%s%c", prevMark, mark),
						Name: defectInfo.Name,
					}
					defectInfoList = append(defectInfoList, markInfo)
					markMap[defectInfo.Id] = markInfo.Mark
					mark++
				}
				if len(basicDefectInfoList) < 26 {
					for i := mark; i <= 'Z'; i++ {
						defectInfoList = append(defectInfoList, &structure.BasicDefectInfoForPrint{
							Id:   0,
							Mark: fmt.Sprintf("%c", mark),
							Name: "",
						})
						mark++
					}
				}
				return nil
			}, func() error {
				productInfo, err = product_basic_pb.NewProductClient().GetProduct(ctx, product_basic_pb.ProductReq{Id: fpmQualityCheckReport.ProductId})
				// 成品信息
				return nil
			}, func() error {
				// 成品颜色
				colorInfo, err = product_basic_pb.NewProductColorClient().GetProductColorById(ctx, fpmQualityCheckReport.ProductColorId)
				return nil
			}, func() error {
				// 通过fpmQualityCheckReport.QualityCheckerId获取质检员信息 尚未处理
				userNameMap, err = user_pb.NewUserClient().GetUserNameByIds(ctx, userIds.List())
				return err
			},
		)
		if err != nil {
			return
		}
		if fpmQualityCheckReport.SupplierId != 0 {
			unitId = fpmQualityCheckReport.SupplierId
		} else {
			unitId = fpmQualityCheckReport.CustomerId
		}
		bizUnitMap, _ = biz_unit.NewClientBizUnitService().GetBizUnitByIds(ctx, []uint64{unitId})
		// 组装
		for _, detail := range stockProductDetailList {
			// 基本信息
			info := RecordStockInfoMap[detail.Id]
			_data.TotalRoll += info.Roll
			_data.TotalWeight += info.Weight
			// stockProductDetailMap[detail.Id] = detail
			// _data.TotalRoll += detail.Roll
			// _data.TotalWeight += detail.Weight
			_data.ProductLevelId = detail.ProductLevelId
			_data.ProductLevelName = productLevel[detail.ProductLevelId][1]

			_data.ReceiveDate = tools.MyTime(detail.WarehouseInTime)
			_data.DyelotNumber = detail.DyelotNumber
		}
		var (
			inspectAvgEdgeWidth  int
			inspectAvgGramWeight int
			qualityCheckerName   string
		)
		for _, check := range fpmQualityCheckList {
			if qualityCheckerName == "" {
				qualityCheckerName = userNameMap[check.QualityCheckerId]
			}
			detailStockInfo := RecordStockInfoMap[check.StockId]
			if maxTime.Before(check.QualityCheckDate) {
				maxTime = check.QualityCheckDate
			}
			// 检验信息
			_data.InspectTotalRoll += detailStockInfo.Roll
			_data.InspectTotalWeight += check.ActuallyWeight
			_data.InspectAvgUsefulWidth += check.UsefulWidth
			inspectAvgEdgeWidth += check.EdgeWidth
			inspectAvgGramWeight += check.QcGramWeight

			// 面料信息
			temFpmQualityCheckDefectList := fpmQualityCheckDefectList.PickByPid(check.Id)
			tmpFpmQualityItemForPrint := &structure.FpmQualityReportItemForPrint{
				Id:                  check.Id,
				VolumeNumber:        check.VolumeNumber,
				StockWeight:         detailStockInfo.Weight,
				ActuallyWeight:      check.ActuallyWeight,
				EdgeWidth:           check.EdgeWidth,
				UsefulWidth:         check.UsefulWidth,
				QcGramWeight:        check.QcGramWeight,
				IsPass:              true,
				Remark:              check.Remark,
				MeasurementUnitId:   productInfo.MeasurementUnitId,
				MeasurementUnitName: productInfo.MeasurementUnitName,
			}
			var (
				scoreOneCountMap   = make(map[string]int)
				scoreTwoCountMap   = make(map[string]int)
				scoreThreeCountMap = make(map[string]int)
				scoreFourCountMap  = make(map[string]int)
			)
			for _, defect := range temFpmQualityCheckDefectList {
				switch defect.Score {
				case 1:
					tmpFpmQualityItemForPrint.ScoreOneCount += defect.DefectCount
					if markStr, ok := markMap[defect.DefectId]; ok {
						scoreOneCountMap[markStr] += defect.DefectCount
					}
					if defect.DefectId == 0 {
						scoreOneCountMap[defect.DefectName] += defect.DefectCount
					}
				case 2:
					tmpFpmQualityItemForPrint.ScoreTwoCount += defect.DefectCount
					if markStr, ok := markMap[defect.DefectId]; ok {
						scoreTwoCountMap[markStr] += defect.DefectCount
					}
					if defect.DefectId == 0 {
						scoreTwoCountMap[defect.DefectName] += defect.DefectCount
					}
				case 3:
					tmpFpmQualityItemForPrint.ScoreThreeCount += defect.DefectCount
					if markStr, ok := markMap[defect.DefectId]; ok {
						scoreThreeCountMap[markStr] += defect.DefectCount
					}
					if defect.DefectId == 0 {
						scoreThreeCountMap[defect.DefectName] += defect.DefectCount
					}
				case 4:
					tmpFpmQualityItemForPrint.ScoreFourCount += defect.DefectCount
					if markStr, ok := markMap[defect.DefectId]; ok {
						scoreFourCountMap[markStr] += defect.DefectCount
					}
					if defect.DefectId == 0 {
						scoreFourCountMap[defect.DefectName] += defect.DefectCount
					}
				}
				tmpFpmQualityItemForPrint.TotalScore += defect.DefectCount * defect.Score
			}
			for key := range scoreOneCountMap {
				tmpFpmQualityItemForPrint.ScoreOneCountStr += fmt.Sprintf("%v-%v ", key, scoreOneCountMap[key])
			}
			for key := range scoreTwoCountMap {
				tmpFpmQualityItemForPrint.ScoreTwoCountStr += fmt.Sprintf("%v-%v ", key, scoreTwoCountMap[key])
			}
			for key := range scoreThreeCountMap {
				tmpFpmQualityItemForPrint.ScoreThreeCountStr += fmt.Sprintf("%v-%v ", key, scoreThreeCountMap[key])
			}
			for key := range scoreFourCountMap {
				tmpFpmQualityItemForPrint.ScoreFourCountStr += fmt.Sprintf("%v-%v ", key, scoreFourCountMap[key])
			}
			// 封裝計算折算評分
			// 计算折算总评分(米为默认单位)，先判断计量单位为米还是码，都不是的话判断数量转长度是否有值，有值则转换，没有则按码来计算
			if strings.Contains(productInfo.MeasurementUnitName, "米") {
				tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreMeterCM(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, tmpFpmQualityItemForPrint.UsefulWidth)
			} else if strings.Contains(productInfo.MeasurementUnitName, "码") {
				tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreYardageInch(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, tmpFpmQualityItemForPrint.UsefulWidth)
			} else {
				if colorInfo.LengthToWeightRate != 0 {
					tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreWeightCM(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, colorInfo.LengthToWeightRate, tmpFpmQualityItemForPrint.UsefulWidth)
				} else {
					tmpFpmQualityItemForPrint.ConvertTotalScore = ScoreYardageInch(tmpFpmQualityItemForPrint.TotalScore, tmpFpmQualityItemForPrint.ActuallyWeight, tmpFpmQualityItemForPrint.UsefulWidth)
				}
			}
			// 封裝是否合格
			// 判断货物是否合格
			if tmpFpmQualityItemForPrint.ConvertTotalScore > vars.FpmQualityPassScore {
				tmpFpmQualityItemForPrint.IsPass = false
				_data.InspectNoPassRoll++
			} else {
				_data.InspectPassRoll++
			}
			_data.InspectTotalScore += tmpFpmQualityItemForPrint.TotalScore
			// 前端不传递ids则为全部打印
			if req.Ids == "" {
				itemList = append(itemList, tmpFpmQualityItemForPrint)
			} else {
				// 根据前端传递id的选择性打印面行信息
				for _, id := range req.Ids.ToUint64() {
					if id == tmpFpmQualityItemForPrint.Id {
						itemList = append(itemList, tmpFpmQualityItemForPrint)
					}
				}
			}
		}

		// 基本信息
		if fpmQualityCheckReport.SupplierId != 0 {
			_data.SupplierName = bizUnitMap[fpmQualityCheckReport.SupplierId][1]
		} else {
			_data.CustomerName = bizUnitMap[fpmQualityCheckReport.CustomerId][1]
		}

		_data.InspectTotalRoll = len(fpmQualityCheckList) * 100
		_data.TotalRoll = fpmQualityCheckReport.DyelotCount
		_data.QcDate = tools.MyTime(maxTime)
		_data.ProductCode = productInfo.FinishProductCode
		_data.ProductName = productInfo.FinishProductName
		_data.Ingredient = productInfo.FinishProductIngredient
		_data.ProductColorCode = colorInfo.ProductColorCode
		_data.ProductColorName = colorInfo.ProductColorName
		// _data.InspectRatio = tools.IntRoundHalf2One(tools.DecimalDiv(float64(_data.InspectTotalRoll*10000), float64(_data.TotalRoll))) // 检验比例（抽/总）
		_data.InspectRatio = tools.IntRoundHalf2One(tools.DecimalDiv(float64(_data.InspectTotalRoll*10000), float64(fpmQualityCheckReport.DyelotCount))) // 检验比例（抽/总）
		_data.ProductColorCodeAndName = fmt.Sprintf("%v - %v", colorInfo.ProductColorCode, colorInfo.ProductColorName)
		_data.QualityCheckerName = qualityCheckerName
		_data.QcTechnicalDirectorName = userNameMap[fpmQualityCheckReport.QcTechnicalDirectorId]
		// 读取fpmqualitycheckreport的数据
		_data.HairEffectName = fpmQualityCheckReport.HairEffectName
		_data.NeedleSize = productInfo.NeedleSize
		_data.YarnBatch = fpmQualityCheckReport.YarnBatch
		_data.StockInNumber = fpmQualityCheckReport.StockInNumber
		_data.DyelotCount = fpmQualityCheckReport.DyelotCount
		_data.ShrinkageRate = fpmQualityCheckReport.ShrinkageRateName
		_data.ColorFastness = fpmQualityCheckReport.ColorFastness
		_data.Smell = fpmQualityCheckReport.Smell
		_data.FabricPatternSlope = fpmQualityCheckReport.FabricTiltId
		_data.WaterAbsorption = fpmQualityCheckReport.WaterAbsorption
		_data.QcConclusion = fpmQualityCheckReport.QcConclusion
		_data.QcOpinion = fpmQualityCheckReport.QcOpinion
		_data.SalesOpinion = fpmQualityCheckReport.SalesOpinion
		_data.OrderNo = fpmQualityCheckReport.OrderNo
		_data.StrengthHorizontal = fpmQualityCheckReport.StrengthHorizontal
		_data.StrengthVertical = fpmQualityCheckReport.StrengthVertical
		_data.AuditorName = fpmQualityCheckReport.AuditorName
		_data.SaleSystemId = fpmQualityCheckReport.SaleSystemId
		_data.WarehouseId = fpmQualityCheckReport.WarehouseId
		_data.AuditTime = fpmQualityCheckReport.AuditDate
		_data.HandFeelName = fpmQualityCheckReport.HandFeelName
		_data.HairEffectName = fpmQualityCheckReport.HairEffectName
		_data.ScouringEffectName = fpmQualityCheckReport.ScouringEffectName
		_data.HairHeadName = fpmQualityCheckReport.HairHeadName
		_data.FabricTiltName = fpmQualityCheckReport.FabricTiltName
		_data.ColorLightName = fpmQualityCheckReport.ColorLightName
		_data.BatchDifferenceName = fpmQualityCheckReport.BatchDifferenceName
		_data.PositiveAndNegativeName = fpmQualityCheckReport.PositiveAndNegativeName
		_data.Remark = fpmQualityCheckReport.Remark
		_data.TotalSampleCount = fpmQualityCheckReport.TotalSampleCount
		_data.TotalSampleWeight = fpmQualityCheckReport.TotalSampleWeight
		_data.DyeFactoryName = bizUnitMap[fpmQualityCheckReport.SupplierId][1]
		_data.CustomerName = bizUnitMap[fpmQualityCheckReport.CustomerId][1]
		_data.ProductLevelName = productLevelName
		_data.Density = productInfo.Density
		_data.YarnCount = productInfo.YarnCount
		if len(fpmQualityCheckList) > 0 {
			_data.InspectAvgUsefulWidth = _data.InspectAvgUsefulWidth / len(fpmQualityCheckList)
			// _data.InspectAvgEdgeWidth = strconv.Itoa(inspectAvgEdgeWidth / len(fpmQualityCheckList))
			// _data.InspectAvgGramWeight = strconv.Itoa(inspectAvgGramWeight / len(fpmQualityCheckList))
			_data.InspectAvgEdgeWidth = colorInfo.FinishProductWidthUnitName
			_data.InspectAvgGramWeight = colorInfo.FinishProductGramWeightUnitName
		}
		// 计算折算平均分(米为默认单位)，先判断计量单位为米还是码，都不是的话判断数量转长度是否有值，有值则转换，没有则按码来计算
		if strings.Contains(productInfo.MeasurementUnitName, "米") {
			_data.InspectConvertAvgScore = ScoreMeterCM(_data.InspectTotalScore, _data.InspectTotalWeight, _data.InspectAvgUsefulWidth)
		} else if strings.Contains(productInfo.MeasurementUnitName, "码") {
			_data.InspectConvertAvgScore = ScoreYardageInch(_data.InspectTotalScore, _data.InspectTotalWeight, _data.InspectAvgUsefulWidth)
		} else {
			if colorInfo.LengthToWeightRate != 0 {
				_data.InspectConvertAvgScore = ScoreWeightCM(_data.InspectTotalScore, _data.InspectTotalWeight, colorInfo.LengthToWeightRate, _data.InspectAvgUsefulWidth)
			} else {
				_data.InspectConvertAvgScore = ScoreYardageInch(_data.InspectTotalScore, _data.InspectTotalWeight, _data.InspectAvgUsefulWidth)
			}
		}
		data = _data
		data.QcItem = itemList
		data.DefectList = defectInfoList
	}
	if len(fpmQualityCheckReport.QualityCheckReportIds) == 0 {
		// 补全data数据
		_data.OrderNo = fpmQualityCheckReport.OrderNo
		if fpmQualityCheckReport.CustomerId != 0 {
			bizUnitList, _ := biz_unit.NewClientBizUnitService().GetBizUnitNameByIds(ctx, []uint64{fpmQualityCheckReport.CustomerId})
			_data.CustomerName = bizUnitList[fpmQualityCheckReport.CustomerId]
		}
		if fpmQualityCheckReport.SupplierId != 0 {
			bizUnitMap, _ = biz_unit.NewClientBizUnitService().GetBizUnitByIds(ctx, []uint64{fpmQualityCheckReport.SupplierId})
		}
		if fpmQualityCheckReport.ProductId != 0 {
			productInfo, err = product_basic_pb.NewProductClient().GetProduct(ctx, product_basic_pb.ProductReq{Id: fpmQualityCheckReport.ProductId})
		}
		if fpmQualityCheckReport.ProductColorId != 0 {
			productColorMap, err = product_basic_pb.NewProductColorClient().GetProductColorMapByIds(ctx, []uint64{fpmQualityCheckReport.ProductColorId})
		}
		_data.NeedleSize = strconv.Itoa(fpmQualityCheckReport.NeedleCount)
		_data.SupplierName = bizUnitMap[supplierId][1]
		_data.QcDate = tools.MyTime(fpmQualityCheckReport.QualityCheckDate)
		_data.ProductCode = productInfo.FinishProductCode
		_data.ProductName = productInfo.FinishProductName
		_data.Ingredient = productInfo.FinishProductIngredient
		_data.InspectRatio = tools.IntRoundHalf2One(tools.DecimalDiv(float64(_data.InspectTotalRoll*10000), float64(_data.TotalRoll))) // 检验比例（抽/总）
		_data.QcTechnicalDirectorName = userNameMap[fpmQualityCheckReport.QcTechnicalDirectorId]
		// 读取fpmqualitycheckreport的数据
		_data.HairEffectName = fpmQualityCheckReport.HairEffectName
		_data.NeedleSize = productInfo.NeedleSize
		_data.YarnBatch = fpmQualityCheckReport.YarnBatch
		_data.StockInNumber = fpmQualityCheckReport.StockInNumber
		_data.DyelotCount = fpmQualityCheckReport.DyelotCount
		_data.TotalWeight = fpmQualityCheckReport.DyelotWeight
		_data.ShrinkageRate = fpmQualityCheckReport.ShrinkageRateName
		_data.ColorFastness = fpmQualityCheckReport.ColorFastness
		_data.Smell = fpmQualityCheckReport.Smell
		_data.FabricPatternSlope = fpmQualityCheckReport.FabricTiltId
		_data.WaterAbsorption = fpmQualityCheckReport.WaterAbsorption
		_data.QcConclusion = fpmQualityCheckReport.QcConclusion
		_data.QcOpinion = fpmQualityCheckReport.QcOpinion
		_data.SalesOpinion = fpmQualityCheckReport.SalesOpinion
		_data.OrderNo = fpmQualityCheckReport.OrderNo
		_data.StrengthHorizontal = fpmQualityCheckReport.StrengthHorizontal
		_data.StrengthVertical = fpmQualityCheckReport.StrengthVertical
		_data.AuditorName = fpmQualityCheckReport.AuditorName
		_data.SaleSystemId = fpmQualityCheckReport.SaleSystemId
		_data.WarehouseId = fpmQualityCheckReport.WarehouseId
		_data.AuditTime = fpmQualityCheckReport.AuditDate
		if productColor, ok := productColorMap[fpmQualityCheckReport.ProductColorId]; ok {
			_data.ProductColorCode = productColor.ProductColorCode
			_data.ProductColorName = productColor.ProductColorName
			_data.ProductColorCodeAndName = fmt.Sprintf("%v - %v", productColor.ProductColorCode, productColor.ProductColorName)
			// 成品幅宽克重
			_data.InspectAvgEdgeWidth = productColor.FinishProductWidth
			_data.InspectAvgGramWeight = productColor.FinishProductGramWeight
			// _data.BuildFPResp(productColor.FinishProductWidth, productColor.FinishProductGramWeight, productColor.FinishProductWidthUnitName,
			//	productColor.FinishProductGramWeightUnitName, productColor.FinishProductWidthUnitId, productColor.FinishProductGramWeightUnitId)
		}
		_data.DyelotNumber = fpmQualityCheckReport.DyelotNumber
		_data.HandFeelName = fpmQualityCheckReport.HandFeelName
		_data.HairEffectName = fpmQualityCheckReport.HairEffectName
		_data.ScouringEffectName = fpmQualityCheckReport.ScouringEffectName
		_data.HairHeadName = fpmQualityCheckReport.HairHeadName
		_data.FabricTiltName = fpmQualityCheckReport.FabricTiltName
		_data.ColorLightName = fpmQualityCheckReport.ColorLightName
		_data.BatchDifferenceName = fpmQualityCheckReport.BatchDifferenceName
		_data.PositiveAndNegativeName = fpmQualityCheckReport.PositiveAndNegativeName
		_data.Remark = fpmQualityCheckReport.Remark
		_data.TotalSampleCount = fpmQualityCheckReport.TotalSampleCount
		_data.TotalSampleWeight = fpmQualityCheckReport.TotalSampleWeight
		_data.DyeFactoryName = bizUnitMap[fpmQualityCheckReport.SupplierId][1]
		_data.ProductLevelName = productLevelName
		_data.Density = productInfo.Density
		_data.YarnCount = productInfo.YarnCount
		data = _data
	}
	return
}
