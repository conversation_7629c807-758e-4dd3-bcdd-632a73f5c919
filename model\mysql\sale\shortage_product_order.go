package sale

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	common "hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql_system "hcscm/model/mysql/system"
	structure "hcscm/structure/sale"
	"hcscm/tools/metadata"
	"time"

	"hcscm/vars"
)

func GetShortageProductOrderIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "shortage_product_order_id")
}

type ShortageProductOrderList []ShortageProductOrder

func (r ShortageProductOrderList) List() []ShortageProductOrder {
	return r
}

func (r ShortageProductOrderList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r ShortageProductOrderList) One() ShortageProductOrder {
	return r[0]
}

func (r ShortageProductOrderList) Pick(id uint64) (o ShortageProductOrder) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

// ShortageProductOrder 成品欠货单
type ShortageProductOrder struct {
	mysql_base.Order
	Id                    uint64                 `gorm:"column:id;primaryKey"`
	SaleSystemId          uint64                 `gorm:"column:sale_system_id" relate:"sale_system_id"`                       // 营销体系ID
	VoucherNumber         string                 `gorm:"column:voucher_number"`                                               // 凭证单号
	OrderTime             time.Time              `gorm:"column:order_time"`                                                   // 订单日期
	SendProductType       common.SendProductType `gorm:"column:send_product_type"`                                            // 出货类型 1出货 2销调
	WarehouseId           uint64                 `gorm:"column:warehouse_id" relate:"warehouse_id"`                           // 调入仓库id
	CustomerId            uint64                 `gorm:"column:customer_id" relate:"biz_unit_id"`                             // 所属客户id
	SaleUserId            uint64                 `gorm:"column:sale_user_id" relate:"employee_id"`                            // 销售员id
	SaleFollowerId        uint64                 `gorm:"column:sale_follower_id" relate:"employee_id"`                        // 销售跟单员id
	SettleType            common.SettleType      `gorm:"column:settle_type"`                                                  // 结算类型
	ProcessFactoryId      uint64                 `gorm:"column:process_factory_id" relate:"process_factory_id"`               // 加工厂id
	Contacts              string                 `gorm:"column:contacts"`                                                     // 联系人
	ContactPhone          string                 `gorm:"column:contact_phone"`                                                // 联系电话
	PrintTag              string                 `gorm:"column:print_tag"`                                                    // 打印标签(出货标签)
	LogisticsCompanyId    uint64                 `gorm:"column:logistics_company_id" relate:"logistics_company_id"`           // 物流公司id
	LogisticsArea         string                 `gorm:"column:logistics_area"`                                               // 物流区域
	ReceiptAddress        string                 `gorm:"column:receipt_address"`                                              // 收货地址
	InfoSaleTaxableItemId uint64                 `gorm:"column:info_sale_taxable_item_id" relate:"info_sale_taxable_item_id"` // 含税项目id
	SaleTaxRate           int                    `gorm:"column:sale_tax_rate"`                                                // 销售税率
	PostageItems          common.PostageItems    `gorm:"column:postage_items"`                                                // 邮费项目 1包邮 2不包邮
	SaleProductOrderId    uint64                 `gorm:"column:sale_product_order_id" relate:"sale_product_order_id"`         // 成品销售单id
	SaleProductOrderNo    string                 `gorm:"column:sale_product_order_no"`                                        // 成品销售单号
	OrderNo               string                 `gorm:"column:order_no"`                                                     // 成品欠货单号
	Number                int                    `gorm:"column:number"`                                                       // 编号
	DepartmentId          uint64                 `gorm:"column:department_id" relate:"department_id"`                         // 下单用户所属部门
	CompanyId             uint64                 `gorm:"column:company_id" relate:"company_id"`                               // 公司ID
	InternalRemark        string                 `gorm:"column:internal_remark"`                                              // 内部备注
	SendProductRemark     string                 `gorm:"column:send_product_remark"`                                          // 出货备注
	SaleGroupId           uint64                 `gorm:"column:sale_group_id" relate:"sale_group_id"`                         // 销售群体id
	IsWithTaxRate         bool                   `gorm:"column:is_with_tax_rate"`                                             // 单价是否含税
}

// 查询后的钩子
func (r *ShortageProductOrder) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r ShortageProductOrder) GetId() uint64 {
	return r.Id
}

// TableName ShortageProductOrder 表名
func (ShortageProductOrder) TableName() string {
	return "shortage_product_order"
}

func (r ShortageProductOrder) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (ShortageProductOrder) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (ShortageProductOrder) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeShortageProductOrderNotExist
}

func (ShortageProductOrder) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeShortageProductOrderAlreadyExist
}

func (r ShortageProductOrder) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	mysql_system.CommonDataSeparate(ctx, r, cond)
}

func (r ShortageProductOrder) GetOtherFields() []string {
	return []string{
		"creator_id", // 创建人
		"updater_id", // 更新人
	}
}

func (r ShortageProductOrder) GetWarehouseField() []string {
	return []string{
		"",
	}
}

func NewShortageProductOrder(
	ctx context.Context,
	p *structure.AddShortageProductOrderParam,
) (r ShortageProductOrder) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.SaleSystemId = p.SaleSystemId
	r.VoucherNumber = p.VoucherNumber
	r.OrderTime = p.OrderTime.ToTimeYMD()
	r.SendProductType = p.SendProductType
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.SettleType = p.SettleType
	r.ProcessFactoryId = p.ProcessFactoryId
	r.Contacts = p.Contacts
	r.ContactPhone = p.ContactPhone
	r.PrintTag = p.PrintTag
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.LogisticsArea = p.LogisticsArea
	r.ReceiptAddress = p.ReceiptAddress
	r.InfoSaleTaxableItemId = p.InfoSaleTaxableItemId
	r.SaleTaxRate = p.SaleTaxRate
	r.PostageItems = p.PostageItems
	r.SaleProductOrderId = p.SaleProductOrderId
	r.SaleProductOrderNo = p.SaleProductOrderNo
	r.AuditStatus = common_system.OrderStatusPendingAudit
	r.BusinessClose = common_system.BusinessCloseNo
	r.DepartmentId = metadata.GetDepartmentId(ctx)
	r.CompanyId = metadata.GetCompanyId(ctx)
	r.InternalRemark = p.InternalRemark
	r.SendProductRemark = p.SendProductRemark
	r.SaleGroupId = p.SaleGroupId
	r.IsWithTaxRate = p.IsWithTaxRate
	return
}

func (r *ShortageProductOrder) UpdateShortageProductOrder(
	ctx context.Context,
	p *structure.UpdateShortageProductOrderParam,
) {
	if r.AuditStatus == common_system.OrderStatusRejected {
		r.AuditStatus = common_system.OrderStatusPendingAudit
	}
	r.SaleSystemId = p.SaleSystemId
	r.VoucherNumber = p.VoucherNumber
	r.OrderTime = p.OrderTime.ToTimeYMD()
	r.SendProductType = p.SendProductType
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.SettleType = p.SettleType
	r.ProcessFactoryId = p.ProcessFactoryId
	r.Contacts = p.Contacts
	r.ContactPhone = p.ContactPhone
	r.PrintTag = p.PrintTag
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.LogisticsArea = p.LogisticsArea
	r.ReceiptAddress = p.ReceiptAddress
	r.InfoSaleTaxableItemId = p.InfoSaleTaxableItemId
	r.SaleTaxRate = p.SaleTaxRate
	r.PostageItems = p.PostageItems
	r.SaleProductOrderId = p.SaleProductOrderId
	r.SaleProductOrderNo = p.SaleProductOrderNo
	r.InternalRemark = p.InternalRemark
	r.SendProductRemark = p.SendProductRemark
	r.SaleGroupId = p.SaleGroupId
	r.IsWithTaxRate = p.IsWithTaxRate
}
