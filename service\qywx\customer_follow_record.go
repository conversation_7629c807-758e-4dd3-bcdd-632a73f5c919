package qywx

import (
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	app_svc "hcscm/api/wx/app"
	app "hcscm/api/wx/app/v1"
	customer_follow_record_svc "hcscm/api/wx/customer_follow_record"
	customer_follow_record_structure "hcscm/api/wx/customer_follow_record/v1"
	common "hcscm/common/qywx"
	product_svc "hcscm/extern/pb/basic_data/product"
	biz_unit_svc "hcscm/extern/pb/biz_unit"
	employee_svc "hcscm/extern/pb/employee"
	"hcscm/extern/pb/user"
	user_pb "hcscm/extern/pb/user"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/qywx"
	mysql "hcscm/model/mysql/qywx/dao"
	structure "hcscm/structure/qywx"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"strings"
)

type ICustomerFollowRecordLogic interface {
	Create(ctx context.Context, param structure.CustomerFollowRecordParam) (err error)
	CreateComment(ctx context.Context, param structure.CommentCustomerFollowRecordParam) (err error)
	Update(ctx context.Context, param structure.UpdateCustomerFollowRecordParam) (data structure_base.AddAndUpdateResponse, err error)
	Delete(ctx context.Context, customerFollowRecordId uint64) (err error)
	GetDetail(ctx context.Context, query structure.GetCustomerFollowRecordQuery) (data structure.UpToDateCustomerFollowRecordData, err error)
	GetTeamCustomerFollowRecordDataList(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (customerFollowRecordData structure.GetCustomerFollowRecordData, err error)
	GetTeamCustomerFollowRecordNums(ctx context.Context, query structure.GetCustomerFollowRecordNumsQuery) (list structure.GetCustomerFollowRecordNumsDataList, err error)
	GetWechatFriends(ctx context.Context, query structure.GetWechatFriendDataListQuery) (list structure.WechatFriendDataList, total int, err error)
	GetTeamCustomerFollowRecordData(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (data structure.GetTeamCustomerFollowRecordData, err error)
	GetTeamMatchableProductDetails(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (list structure.GetMatchableProductDetailDataList, total int, err error)
	GetTeamMatchableProductCustomerRankDetailList(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (list structure.GetMatchableProductCustomerRankDetailDataList, total int, err error)
	GetTeamDevProductDetailDataList(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (list structure.GetDevProductDetailDataList, summary structure.DevProductProportionList, total int, err error)
}

func NewCustomerFollowRecordLogic(ginCtx *gin.Context, isCache bool) ICustomerFollowRecordLogic {
	return &customerFollowRecordLogic{
		isCache: isCache,
		single:  tools.Single,
		ginCtx:  ginCtx,
	}
}

type customerFollowRecordLogic struct {
	isCache bool
	ginCtx  *gin.Context
	single  tools.SingleFlight
}

func (logic *customerFollowRecordLogic) Create(ctx context.Context, param structure.CustomerFollowRecordParam) (err error) {
	var singleKey []byte
	singleKey, err = json.Marshal(param)
	if err != nil {
		return
	}
	_, err = logic.single.Do(tools.GetRequestKey(logic.ginCtx, singleKey), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
		defer func() {
			err = commit(err, recover())
		}()
		var (
			visitTagRelRepo = mysql.NewVisitTagRelRepo(ctx, logic.isCache)
			followUserId    string
		)
		info := metadata.GetLoginInfo(ctx)
		employee, err := employee_svc.NewClientEmployeeService().GetEmployeeById(ctx, info.GetEmpId())
		if err != nil || employee.QYWXUserId == "" {
			return
		}
		followUserId = employee.QYWXUserId
		// 创建客户拜访记录
		createReq := &customer_follow_record_structure.CreateCustomerFollowRecordRequest{
			SaleUserId:             metadata.GetUserId(ctx),
			BizUnitId:              param.BizUnitId,
			ExternalUserId:         param.ExternalUserId,
			FollowUserId:           followUserId,
			DepartmentId:           metadata.GetDepartmentId(ctx),
			Latitude:               param.Latitude,
			Longitude:              param.Longitude,
			CoordinateSystem:       param.CoordinateSystem,
			LocationName:           param.LocationName,
			LocationAddress:        param.LocationAddress,
			AttachmentUrl:          strings.Join(param.AttachmentUrl, ","),
			NotAddCorpWechatRemark: param.NotAddCorpWechatRemark,
			MatchableProductIds:    tools.FormatIntListToString(param.MatchableProductIds),
			FollowRemark:           param.FollowRemark,
			IsLatest:               int32(common.YesOrNoYes),
			Feedbacks: func() (list []*customer_follow_record_structure.Feedback) {
				list = make([]*customer_follow_record_structure.Feedback, 0)
				for _, feedback := range param.Feedbacks {
					list = append(list, &customer_follow_record_structure.Feedback{
						FeedbackTitle:      feedback.FeedbackTitle,
						FeedbackSolution:   feedback.FeedbackSolution,
						NewProductFeedback: feedback.NewProductFeedback,
					})
				}
				return
			}(),
			Labels: func() (list []*customer_follow_record_structure.Labels) {
				list = make([]*customer_follow_record_structure.Labels, 0)
				for _, label := range param.Labels {
					list = append(list, &customer_follow_record_structure.Labels{
						CorpTagId: label.CorpTagId,
					})
				}
				return
			}(),
			DevProducts: func() (list []*customer_follow_record_structure.DevProduct) {
				list = make([]*customer_follow_record_structure.DevProduct, 0)
				for _, devProduct := range param.DevProducts {
					list = append(list, &customer_follow_record_structure.DevProduct{
						Name:          devProduct.Name,
						WeightDensity: devProduct.WeightDensity,
						Remark:        devProduct.Remark,
					})
				}
				return
			}(),
		}
		reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).CreateCustomerFollowRecord(ctx, createReq)
		if err != nil {
			return
		}
		// 创建客户拜访记录-标签关联记录
		for _, visitTag := range param.VisitingSituations {
			visitTagRel := model.NewVisitTagRel(visitTag.VisitTagId, reply.Id, param.BizUnitId, param.ExternalUserId, visitTag.OtherTagRemark)
			_, err = visitTagRelRepo.MustCreate(ctx, tx, visitTagRel)
			if err != nil {
				return
			}
		}
		return
	})
	if err != nil {
		return
	}

	return
}

func (logic *customerFollowRecordLogic) Update(ctx context.Context, param structure.UpdateCustomerFollowRecordParam) (data structure_base.AddAndUpdateResponse, err error) {
	var singleKey []byte
	singleKey, err = json.Marshal(param)
	if err != nil {
		return
	}
	_, err = logic.single.Do(tools.GetRequestKey(logic.ginCtx, singleKey), func() (res interface{}, err error) {

		return
	})
	if err != nil {
		return
	}

	return
}

func (logic *customerFollowRecordLogic) CreateComment(ctx context.Context, param structure.CommentCustomerFollowRecordParam) (err error) {
	var singleKey []byte
	singleKey, err = json.Marshal(param)
	if err != nil {
		return
	}
	_, err = logic.single.Do(tools.GetRequestKey(logic.ginCtx, singleKey), func() (res interface{}, err error) {
		// 调用企微服务获取数据
		_, err = customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).CreateCustomerFollowRecordComment(ctx, &customer_follow_record_structure.CreateCustomerFollowRecordCommentRequest{
			CustomerFollowRecordId: param.CustomerFollowRecordId,
			CommentText:            param.CommentText,
		})
		if err != nil {
			return
		}
		return
	})
	if err != nil {
		return
	}
	return
}

func (logic *customerFollowRecordLogic) Delete(ctx context.Context, customerFollowRecordId uint64) (err error) {
	// 调用企微服务获取数据
	_, err = customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).DeleteCustomerFollowRecord(ctx, &customer_follow_record_structure.DeleteCustomerFollowRecordRequest{
		Id: customerFollowRecordId,
	})
	if err != nil {
		return
	}
	return
}

func (logic *customerFollowRecordLogic) GetTeamCustomerFollowRecordDataList(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (customerFollowRecordData structure.GetCustomerFollowRecordData, err error) {
	// 查询客户绑定的企微客户id
	if query.BizUnitId != 0 {
		var (
			customerData    = &app.GetCustomersReply{}
			externalUserIds []string
		)
		customerData, err = app_svc.GetAppClient(ctx).GetCustomers(ctx, &app.GetCustomersRequest{})
		if err != nil {
			return
		}
		for _, customer := range customerData.List {
			externalUserIds = append(externalUserIds, customer.Id)
		}
		query.ExternalUserIds = externalUserIds
	}
	// 根据员工id查询提交用户id,如果提交用户id不存在,则返回空
	if len(query.SubmitUserIds) > 0 {
		query.UserIds, _ = user_pb.NewUserClient().GetUserIdsByEmpIds(ctx, query.SubmitUserIds.ToUint64())
		if len(query.UserIds) == 0 {
			return
		}
	}
	// 调用企微服务获取数据
	reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).GetTeamCustomerFollowRecordList(ctx, &customer_follow_record_structure.ListCustomerFollowRecordRequest{
		Page:            uint32(query.Page),
		Size:            uint32(query.Size),
		StartTime:       query.StartTime.Date(),
		EndTime:         query.EndTime.Date(),
		UserIds:         query.UserIds,
		ExternalUserId:  query.ExternalUserId,
		ExternalUserIds: query.ExternalUserIds,
	})
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	var (
		visitTagRepo    = mysql.NewVisitTagRepo(ctx, logic.isCache)
		visitTagRelRepo = mysql.NewVisitTagRelRepo(ctx, logic.isCache)
		visitTagRels    model.VisitTagRelList
		visitTags       model.VisitTagList
		list            = make([]structure.CustomerFollowRecordDetailData, 0)
	)
	// 根据返回的标签id，查询标签名称
	visitTagRels, err = visitTagRelRepo.FindByCustomerFollowRecordIDs(ctx, tx, reply.Ids)
	if err != nil {
		return
	}
	visitTags, err = visitTagRepo.FindByIDs(ctx, tx, visitTagRels)
	if err != nil {
		return
	}
	// 查询用户
	userSvc := user.NewUserClient()
	userMap, err := userSvc.GetUserNameByIds(ctx, reply.SaleUserIds)
	if err != nil {
		return
	}
	bizUnitSvc := biz_unit_svc.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitNameByIds(ctx, reply.BizUnitIds)
	if err != nil {
		return
	}
	productSvc := product_svc.NewProductClient()
	productItem, _, err := productSvc.GetProductByIds(ctx, reply.ProductIds)
	if err != nil {
		return
	}

	customerFollowRecordData.TotalRecordNums = int(reply.TotalRecordNums)
	customerFollowRecordData.EffectiveRecordNums = int(reply.EffectiveRecordNums)
	customerFollowRecordData.Total = int(reply.Total)
	customerFollowRecordData.SelectStartDate = query.StartTime.Date()
	customerFollowRecordData.SelectEndDate = query.EndTime.Date()
	for _, v := range reply.DetailList {
		var (
			detail      = structure.CustomerFollowRecordDetailData{}
			feedbacks   = make([]structure.Feedback, 0)
			comments    = make([]structure.Comment, 0)
			devProducts = make([]structure.DevProduct, 0)
		)

		detail.Id = v.Id
		detail.CreateDate = v.CreateTime
		detail.SubmitUserId = v.SaleUserId
		detail.SubmitUserName = userMap[v.SaleUserId]
		detail.BizUnitId = v.BizUnitId
		detail.BizUnitName = bizUnit[v.BizUnitId]
		detail.LocationName = v.LocationName
		detail.LocationAddress = v.LocationAddress
		detail.Latitude = v.Latitude
		detail.Longitude = v.Longitude
		detail.CoordinateSystem = v.CoordinateSystem
		detail.ExternalUserId = v.ExternalUserId
		detail.ExternalUserName = v.ExternalUserName
		detail.NotAddCorpWechatRemark = v.NotAddCorpWechatRemark
		detail.FollowRemark = v.FollowRemark
		detail.MatchableProductIds = tools.String2UintArray(v.MatchableProductIds, ",")
		detail.MatchableProductNames = func() (matchableProductNames []string) {
			matchableProductNames = make([]string, 0)
			for _, productId := range detail.MatchableProductIds {
				matchableProductNames = append(matchableProductNames, productItem[productId][0])
			}
			return
		}()
		detail.LabelNames = func() (labelNames []string) {
			_visitTagRels := visitTagRels.PickByCustomerFollowRecordID(v.Id)
			for _, rel := range _visitTagRels {
				visitTag := visitTags.PickByID(rel.VisitTagId)
				labelNames = append(labelNames, visitTag.VisitingMode.String())
				if visitTag.Name == "其他" {
					labelNames = append(labelNames, visitTag.Name+"("+rel.OtherTagRemark+")")
				} else {
					labelNames = append(labelNames, visitTag.Name)
				}
			}
			return
		}()
		detail.AttachmentURL = func() (attachmentURLs []string) {
			if v.AttachmentUrl == "" {
				return []string{}
			}
			attachmentURLs = strings.Split(v.AttachmentUrl, ",")
			return
		}()
		for _, feedback := range v.Feedbacks {
			feedbacks = append(feedbacks, structure.Feedback{
				Id:                 feedback.Id,
				FeedbackTitle:      feedback.FeedbackTitle,
				FeedbackSolution:   feedback.FeedbackSolution,
				NewProductFeedback: feedback.NewProductFeedback,
			})
		}
		for _, comment := range v.Comments {
			comments = append(comments, structure.Comment{
				CommentId:       comment.CommentId,
				CommentText:     comment.CommentText,
				CommentUserId:   comment.CommentUserId,
				CommentUserName: userMap[comment.CommentUserId],
				CommentTime:     comment.CommentTime,
			})
		}
		for _, devProduct := range v.DevProducts {
			devProducts = append(devProducts, structure.DevProduct{
				Id:            devProduct.Id,
				Name:          devProduct.Name,
				WeightDensity: devProduct.WeightDensity,
				Remark:        devProduct.Remark,
			})
		}
		detail.Feedbacks = feedbacks
		detail.Comments = comments
		detail.DevProducts = devProducts
		list = append(list, detail)
	}
	customerFollowRecordData.CustomerFollowRecordDetailData = list
	return
}

// 获取团队月份每天拜访记录数据统计
func (logic *customerFollowRecordLogic) GetTeamCustomerFollowRecordNums(ctx context.Context, query structure.GetCustomerFollowRecordNumsQuery) (list structure.GetCustomerFollowRecordNumsDataList, err error) {
	// 调用企微服务获取数据
	reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).GetTeamCustomerFollowRecordMonthNum(ctx, &customer_follow_record_structure.GetCustomerFollowRecordReportDataRequest{
		Date: query.Date,
	})
	if err != nil {
		return
	}
	for _, dayNum := range reply.DayNums {
		data := structure.GetCustomerFollowRecordNumsData{}
		data.Date = dayNum.Date
		data.TotalRecordNums = int(dayNum.TotalRecordNums)
		data.EffectiveRecordNums = int(dayNum.EffectiveRecordNums)
		list = append(list, data)
	}
	return
}

func (logic *customerFollowRecordLogic) GetWechatFriends(ctx context.Context, query structure.GetWechatFriendDataListQuery) (list structure.WechatFriendDataList, total int, err error) {
	var (
		followUserId string
		reply        = new(app.GetCustomersWithDetailReply)
	)
	// todo:权限控制，目前如果绑定了员工的就只能看自己的客户
	info := metadata.GetLoginInfo(ctx)
	employee, err := employee_svc.NewClientEmployeeService().GetEmployeeById(ctx, info.GetEmpId())
	if err != nil || employee.QYWXUserId == "" {
		return
	}
	followUserId = employee.QYWXUserId
	reply, err = app_svc.GetAppClient(ctx).GetCustomersWithDetail(ctx, &app.GetCustomersRequest{
		Name:         query.Name,
		FollowUserId: followUserId,
		Page:         int64(query.Page),
		Size:         int64(query.Size),
	})
	if err != nil {
		return
	}
	total = int(reply.Total)
	bizUnitSvc := biz_unit_svc.NewClientBizUnitService()
	bizUnitMap, err := bizUnitSvc.QueryBizUnitListById(ctx, reply.BizUnitIds)
	if err != nil {
		return
	}
	for _, customer := range reply.List {
		bizUnit := bizUnitMap[customer.BizUnitId]
		list = append(list, structure.WechatFriendData{
			PurchaserClueId:       customer.CustomerId,
			ExternalUserId:        customer.ExternalUserId,
			Name:                  customer.Name,
			Avatar:                customer.Avatar,
			ExternalType:          common.ExternalType(customer.ExternalType),
			ExternalTypeName:      customer.ExternalTypeName,
			CorpName:              customer.CorpName,
			CorpFullName:          customer.CorpFullName,
			BizUnitId:             bizUnit.Id,
			BizUnitName:           bizUnit.Name,
			IsBind:                customer.IsBind,
			Gender:                customer.Gender,
			CorpWeChatFriendCount: int(customer.CorpWechatFriendCount),
			CorpGroupChatCount:    int(customer.CorpGroupChatCount),
			CorpWeChatFriendInfo: func() (list []structure.CorpWeChatFriendInfo) {
				for _, corpWeChatFriendInfo := range customer.CorpWechatFriendInfo {
					list = append(list, structure.CorpWeChatFriendInfo{
						AddCreateTime: corpWeChatFriendInfo.AddCreateTime,
						Name:          corpWeChatFriendInfo.Name,
					})
				}
				return
			}(),
			CorpGroupChatInfo: func() (list []structure.CorpGroupChatInfo) {
				for _, corpGroupChatInfo := range customer.CorpGroupChatInfo {
					list = append(list, structure.CorpGroupChatInfo{
						GroupChatName: corpGroupChatInfo.GroupChatName,
						JoinTime:      corpGroupChatInfo.JoinTime,
					})
				}
				return
			}(),
		})
	}
	return
}

func (logic *customerFollowRecordLogic) GetTeamCustomerFollowRecordData(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (data structure.GetTeamCustomerFollowRecordData, err error) {
	// 根据员工id查询提交用户id,如果提交用户id不存在,则返回空
	if len(query.SubmitUserIds) > 0 {
		query.UserIds, _ = user_pb.NewUserClient().GetUserIdsByEmpIds(ctx, query.SubmitUserIds.ToUint64())
		if len(query.UserIds) == 0 {
			return
		}
	}
	// 调用企微服务获取数据
	reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).GetTeamCustomerFollowRecordReportData(ctx, &customer_follow_record_structure.GetCustomerFollowRecordReportDataRequest{
		StartTime: query.StartTime.Date(),
		EndTime:   query.EndTime.Date(),
		UserIds:   query.UserIds,
	})
	if err != nil {
		return
	}
	data.MatchableProductNums = int(reply.MatchableProductNums)
	data.RecommendationProductNums = int(reply.RecommendationProductNums)
	return
}

func (logic *customerFollowRecordLogic) GetTeamMatchableProductDetails(
	ctx context.Context,
	query structure.GetTeamCustomerFollowRecordQuery,
) (
	list structure.GetMatchableProductDetailDataList,
	total int,
	err error,
) {
	var (
		productSvc = product_svc.NewProductClient()
	)
	// 根据员工id查询提交用户id,如果提交用户id不存在,则返回空
	if len(query.SubmitUserIds) > 0 {
		query.UserIds, _ = user_pb.NewUserClient().GetUserIdsByEmpIds(ctx, query.SubmitUserIds.ToUint64())
		if len(query.UserIds) == 0 {
			return
		}
	}
	// 调用企微服务获取数据
	reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).GetTeamMatchableProductDetailList(ctx, &customer_follow_record_structure.ListTeamProductDetailRequest{
		StartTime: query.StartTime.Date(),
		EndTime:   query.EndTime.Date(),
		UserIds:   query.UserIds,
	})
	if err != nil {
		return
	}
	productMap, err := productSvc.GetProductMapByIds(ctx, reply.ProductIds)
	if err != nil {
		return
	}
	for _, productItem := range reply.ProductList {
		data := structure.GetMatchableProductDetailData{}
		data.ProductId = productItem.ProductId
		data.Roll = int(productItem.Nums)
		data.Proportion = productItem.Proportion
		product, ok := productMap[productItem.ProductId]
		if ok {
			data.ProductCode = product.FinishProductCode
			data.ProductName = product.FinishProductName
		}
		list = append(list, data)
	}
	return
}

func (logic *customerFollowRecordLogic) GetTeamMatchableProductCustomerRankDetailList(
	ctx context.Context,
	query structure.GetTeamCustomerFollowRecordQuery,
) (
	list structure.GetMatchableProductCustomerRankDetailDataList,
	total int,
	err error,
) {
	var (
		productSvc = product_svc.NewProductClient()
		bizUnitSvc = biz_unit_svc.NewClientBizUnitService()
	)
	// 根据员工id查询提交用户id,如果提交用户id不存在,则返回空
	if len(query.SubmitUserIds) > 0 {
		query.UserIds, _ = user_pb.NewUserClient().GetUserIdsByEmpIds(ctx, query.SubmitUserIds.ToUint64())
		if len(query.UserIds) == 0 {
			return
		}
	}
	// 调用企微服务获取数据
	reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).GetTeamMatchableProductCustomerRankDetailList(ctx, &customer_follow_record_structure.ListTeamProductDetailRequest{
		StartTime: query.StartTime.Date(),
		EndTime:   query.EndTime.Date(),
		UserIds:   query.UserIds,
	})
	if err != nil {
		return
	}
	productMap, _ := productSvc.GetProductMapByIds(ctx, reply.ProductIds)
	bizUnitMap, _ := bizUnitSvc.QueryBizUnitListById(ctx, reply.BizUnitIds)
	for _, customerRank := range reply.CustomerRankList {
		bizUnit := bizUnitMap[customerRank.BizUnitId]
		data := structure.GetMatchableProductCustomerRankDetailData{
			BizUnitId:         customerRank.BizUnitId,
			BizUnitName:       bizUnit.Name,
			PurchaserClueId:   customerRank.CustomerId,
			PurchaserClueName: customerRank.CustomerName,
			ContactName:       bizUnit.ContactName,
			Phone:             bizUnit.Phone,
			SaleUserId:        bizUnit.SaleUserId,
			SaleUserName:      bizUnit.SaleUserName,
			MatchableProductList: func() []structure.MatchableProduct {
				var matchableProductList = make([]structure.MatchableProduct, 0)
				for _, matchableProduct := range customerRank.MatchableProductList {
					data := structure.MatchableProduct{
						ProductId: matchableProduct.ProductId,
					}
					product, ok := productMap[matchableProduct.ProductId]
					if ok {
						data.ProductCode = product.FinishProductCode
						data.ProductName = product.FinishProductName
					}
				}
				return matchableProductList
			}(),
		}
		list = append(list, data)
	}
	total = int(reply.Total)
	return
}

func (logic *customerFollowRecordLogic) GetTeamDevProductDetailDataList(ctx context.Context, query structure.GetTeamCustomerFollowRecordQuery) (list structure.GetDevProductDetailDataList, summary structure.DevProductProportionList, total int, err error) {
	var (
		bizUnitSvc = biz_unit_svc.NewClientBizUnitService()
	)
	// 根据员工id查询提交用户id,如果提交用户id不存在,则返回空
	if len(query.SubmitUserIds) > 0 {
		query.UserIds, _ = user_pb.NewUserClient().GetUserIdsByEmpIds(ctx, query.SubmitUserIds.ToUint64())
		if len(query.UserIds) == 0 {
			return
		}
	}
	// 调用企微服务获取数据
	reply, err := customer_follow_record_svc.GetCustomerFollowRecordClient(ctx).GetTeamDevProductDetailDataList(ctx, &customer_follow_record_structure.ListTeamProductDetailRequest{
		StartTime: query.StartTime.Date(),
		EndTime:   query.EndTime.Date(),
		UserIds:   query.UserIds,
	})
	if err != nil {
		return
	}
	bizUnitMap, _ := bizUnitSvc.QueryBizUnitListById(ctx, reply.BizUnitIds)
	for _, devProductDetail := range reply.DevProductDetailList {
		bizUnit := bizUnitMap[devProductDetail.BizUnitId]
		data := structure.GetDevProductDetailData{
			BizUnitId:         devProductDetail.BizUnitId,
			BizUnitName:       bizUnit.Name,
			PurchaserClueId:   devProductDetail.CustomerId,
			PurchaserClueName: devProductDetail.CustomerName,
			ContactName:       bizUnit.ContactName,
			Phone:             bizUnit.Phone,
			SaleUserId:        bizUnit.SaleUserId,
			SaleUserName:      bizUnit.SaleUserName,
			DevProductDetailList: func() []structure.DevProductDetail {
				var devProductDetailList = make([]structure.DevProductDetail, 0)
				for _, devProduct := range devProductDetail.DevProducts {
					devProductDetailList = append(devProductDetailList, structure.DevProductDetail{
						Name:          devProduct.Name,
						WeightDensity: devProduct.WeightDensity,
						Remark:        devProduct.Remark,
					})
				}
				return devProductDetailList
			}(),
		}
		list = append(list, data)
	}
	for _, devProductProportion := range reply.DevProductProportionList {
		summary = append(summary, structure.DevProductProportion{
			DevProductName: devProductProportion.DevProductName,
			Proportion:     devProductProportion.Proportion,
		})
	}
	return
}

func (logic *customerFollowRecordLogic) GetDetail(ctx context.Context, query structure.GetCustomerFollowRecordQuery) (data structure.UpToDateCustomerFollowRecordData, err error) {

	return
}
