package system

import (
	"bytes"
	"encoding/json"
	"fmt"
	"hcscm/middleware"
	structure "hcscm/structure/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"net/http"
	"net/url"
	"reflect"

	"github.com/xuri/excelize/v2"

	"github.com/gin-gonic/gin"

	"hcscm/common/errors"
	"hcscm/tools"
)

func setResult(c *gin.Context, resp interface{}) {
	c.Set("result", resp)
}

func getResult(c *gin.Context) (interface{}, bool) {
	v, ok := c.Get("result")
	if ok {
		return v, true
	}
	return nil, false
}

// 用于解析参数
func ShouldBind(c *gin.Context, p structure_base.IParam) (err error) {
	defer func() {
		if msg := recover(); msg != nil {
			fmt.Println(msg)
			err = middleware.ErrorLog(errors.NewError(errors.ErrCodeServerInternalError), msg)
			return
		}
	}()
	err = c.Should<PERSON>ind(p)
	if err != nil {
		switch vErr := err.(type) {
		case *json.UnmarshalTypeError:
			msg := fmt.Sprintf(`参数"%v"格式错误`, vErr.Field)
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameterFormat, msg), p)
		default:
			err = middleware.WarnLog(errors.NewDetailError(errors.ErrCodeBusinessParameterFormat, err), p)
		}
		return
	}

	p.Adjust()
	err = p.Validate()
	if err != nil {
		return
	}

	err = structure_base.ParseValidate(c, p)
	if err != nil {
		return err
	}

	return

}

// 用于返回错误
func BuildCustomError(c *gin.Context, err error) {
	if _, ok := getResult(c); ok {
		return
	}
	var (
		response structure_base.Response
	)
	response.SetVersion(vars.Version)
	switch vErr := err.(type) {
	case *errors.Error:
		response.Code = vErr.GetCode()
		response.Msg = vErr.GetMsg()
	default:
		response.Code = -1
		response.Msg = err.Error()
	}
	c.JSON(http.StatusOK, response)
	c.Abort()
	setResult(c, response)
}

// 用于返回详情数据
func BuildDataContext(c *gin.Context, data structure_base.IResponseData) {
	if _, ok := getResult(c); ok {
		return
	}

	if data != nil {
		data.Adjust()
	}
	response := structure_base.Response{}
	response.SetVersion(vars.Version)
	response.Code = 0
	response.Msg = "success"
	response.Data = data
	jsonRes, _ := json.Marshal(response)
	// fmt.Println(jsonRes)
	var mapRes map[string]any
	json.Unmarshal(jsonRes, &mapRes)
	var dataDesensitization = make([]string, 0)
	value, exist := c.Get(metadata.DataSeparate)
	if exist && value != "" {
		dataDesensitization = value.([]string)
	}
	_ = structure_base.ModifyDesensitizationItemList(c, &mapRes, response, dataDesensitization)
	c.JSON(http.StatusOK, mapRes)
	c.Abort()
	setResult(c, response)
}

func BuildDataDesensitizationContext(c *gin.Context, data structure_base.IResponseData) {
	if _, ok := getResult(c); ok {
		return
	}

	if data != nil {
		data.Adjust()
	}
	response := structure_base.Response{}
	response.SetVersion(vars.Version)
	response.Code = 0
	response.Msg = "success"
	response.Data = data
	jsonRes, _ := json.Marshal(response)
	// fmt.Println(jsonRes)
	var mapRes map[string]any
	json.Unmarshal(jsonRes, &mapRes)
	var dataDesensitization = make([]string, 0)
	value, exist := c.Get(metadata.DataSeparate)
	if exist && value != "" {
		dataDesensitization = value.([]string)
	}
	_ = structure_base.ModifyDesensitizationItemList(c, &mapRes, response, dataDesensitization)
	c.JSON(http.StatusOK, mapRes)

	c.Abort()
	setResult(c, response)
}

// 用于返回错误或详情数据
func BuildResponse(c *gin.Context, err error, data structure_base.IResponseData) {
	if err != nil {
		BuildCustomError(c, err)
	} else {
		// BuildDataContext(c, data)
		BuildDataDesensitizationContext(c, data)
	}
}

// pda端专用返回数据的函数
func BuildPDAResponse(c *gin.Context, err error, data structure_base.IPDAResponse) {
	if err != nil {
		if vErr, ok := err.(*errors.Error); ok {
			if pdaCode := errors.FilterPDAErrCode(*vErr); pdaCode > 0 {
				data.SetCode(pdaCode, err)
				BuildDataContext(c, data)
				return
			}
		}
		BuildCustomError(c, err)
	} else {
		BuildDataContext(c, data)
	}
}

// 用于返回列表数据（支持数据报表导出） 需要配合结构体的excel标签使用
func BuildListResponseV2(c *gin.Context, q structure_base.IListQuery, fileName string, err error, list structure_base.IListResponseData, summary structure_base.IResponseData, total int, sheetName ...string) {
	if summary == nil {
		summary = structure_base.ResponseData{}
	}
	if err != nil {
		BuildCustomError(c, err)
	} else {
		if q.GetDownload() {
			BuildXlsxResponseV2(c, err, q, fileName, list, summary, sheetName...)
		} else {
			BuildListResponseWithSummary(c, err, list, summary, total)
		}
	}
}

// 用于返回列表数据（支持数据报表导出）,导出结构体参考已使用方法
func BuildListResponseV3(c *gin.Context, q structure_base.IListQuery, err error, list structure_base.IListResponseData, summary structure_base.IResponseData, total int, sheetName ...string) {
	if summary == nil {
		summary = structure_base.ResponseData{}
	}
	if err != nil {
		BuildCustomError(c, err)
	} else {
		if q.GetDownload() {
			BuildXlsxResponseV3(c, err, q, list, summary)
		} else {
			BuildListResponseWithSummary(c, err, list, summary, total)
		}
	}
}

// 用于返回列表数据（不支持数据报表导出）  （新的API建议使用buildListResponseV2）
func BuildListResponse(c *gin.Context, err error, list structure_base.IResponseData, total int) {
	if err != nil {
		BuildCustomError(c, err)
	} else {
		if _, ok := getResult(c); ok {
			return
		}

		response := structure_base.ListResponse{}
		response.SetVersion(vars.Version)
		response.Code = 0
		response.Msg = "success"
		// 判断list值是否为空
		if !reflect.ValueOf(list).IsNil() {
			list.Adjust()
			response.Data.List = list
			response.Data.Total = total
		}
		jsonRes, _ := json.Marshal(response)
		// fmt.Println(jsonRes)
		var mapRes map[string]any
		json.Unmarshal(jsonRes, &mapRes)
		var dataDesensitization = make([]string, 0)
		value, exist := c.Get(metadata.DataSeparate)
		if exist && value != "" {
			dataDesensitization = value.([]string)
		}
		_ = structure_base.ModifyDesensitizationItemList(c, &mapRes, response, dataDesensitization)
		c.JSON(http.StatusOK, mapRes)
		setResult(c, response)
	}
}

// 用于返回列表数据（包含汇总数据）（不支持数据报表导出）
func BuildListResponseWithSummary(c *gin.Context, err error, list structure_base.IListResponseData, summary structure_base.IResponseData, total int) {
	if err != nil {
		BuildCustomError(c, err)
	} else {
		if _, ok := getResult(c); ok {
			return
		}

		response := structure_base.ListResponse{}
		response.SetVersion(vars.Version)
		response.Code = 0
		response.Msg = "success"
		// 判断list值是否为空
		if !reflect.ValueOf(list).IsNil() {
			list.Adjust()
			response.Data.List = list
			response.Data.Summary = summary
			response.Data.Total = total
		}
		jsonRes, _ := json.Marshal(response)
		// fmt.Println(jsonRes)
		var mapRes map[string]interface{}
		json.Unmarshal(jsonRes, &mapRes)
		var dataDesensitization = make([]string, 0)
		value, exist := c.Get(metadata.DataSeparate)
		if exist && value != "" {
			dataDesensitization = value.([]string)
		}
		_ = structure_base.ModifyDesensitizationItemList(c, &mapRes, response, dataDesensitization)
		c.JSON(http.StatusOK, mapRes)
		setResult(c, response)
	}
}

// 用于返回枚举下拉列表接口
func BuildMapResponse(c *gin.Context, m map[string]int) {
	if _, ok := getResult(c); ok {
		return
	}

	response := structure_base.ListBaseDataResponse{}
	response.SetVersion(vars.Version)
	response.Code = 0
	response.Msg = "success"

	list := make([]structure_base.BaseData, 0)
	for k, v := range m {
		t := structure_base.BaseData{
			Id:   v,
			Name: k,
		}
		list = append(list, t)
	}

	response.Data.List = list
	response.Adjust()
	c.JSON(http.StatusOK, response)
	setResult(c, response)

}

// 用于返回枚举下拉列表接口
func BuildMapResponseV2(c *gin.Context, m map[int]string) {
	if _, ok := getResult(c); ok {
		return
	}

	response := structure_base.ListBaseDataResponse{}
	response.SetVersion(vars.Version)
	response.Code = 0
	response.Msg = "success"

	list := make([]structure_base.BaseData, 0)
	for k, v := range m {
		t := structure_base.BaseData{
			Id:   k,
			Name: v,
		}
		list = append(list, t)
	}

	response.Data.List = list
	response.Adjust()
	c.JSON(http.StatusOK, response)
	setResult(c, response)

}

// 用于返回通过数据库查询的下拉列表接口
func BuildBaseDataResponse(c *gin.Context, err error, data []structure_base.BaseData) {

	if err != nil {
		BuildCustomError(c, err)
	} else {
		if _, ok := getResult(c); ok {
			return
		}
		response := structure_base.ListBaseDataResponse{}
		response.SetVersion(vars.Version)
		response.Code = 0
		response.Msg = "success"

		response.Data.List = data
		response.Adjust()
		c.JSON(http.StatusOK, response)
		setResult(c, response)
	}
}

// 无权限访问
func BuildForbidden(c *gin.Context) {
	if _, ok := getResult(c); ok {
		return
	}
	defer setResult(c, true)
	err := middleware.WarnLog(errors.NewError(errors.ErrCodeBusinessNoAccess))
	BuildCustomError(c, err)
}

// 无身份信息
func BuildUnauthorized(c *gin.Context) {
	if _, ok := getResult(c); ok {
		return
	}
	defer setResult(c, true)
	// _ = log.WarnLog(errors.NewError(errors.ErrCodeBusinessAuthorize))
	c.AbortWithStatus(http.StatusUnauthorized)
}

// 重定向
func BuildRedirectURIResponse(c *gin.Context, err error, uri string) {
	if err != nil {
		BuildCustomError(c, err)
	} else {
		c.Redirect(301, uri)
	}

}

// 用于返回图片
func BuildJPEGResponse(c *gin.Context, err error, data []byte) {

	if err != nil {
		BuildCustomError(c, err)
	} else {
		if _, ok := getResult(c); ok {
			return
		}
		c.Writer.Header().Add("content-type", "image/jpeg")
		_, _ = c.Writer.Write(data)
		setResult(c, true)
		return
	}
}

// 用于返回报表的函数
func BuildXlsxResponse(c *gin.Context, err error, fileName string, data []byte) {
	if err != nil {
		BuildCustomError(c, err)
		return
	} else {
		if _, ok := getResult(c); ok {
			return
		}
		c.Header("Content-Description", "File Transfer")
		c.Header("Content-Disposition", "attachment;filename="+url.QueryEscape(fileName))
		c.Data(http.StatusOK, "application/x-xls", data)
		setResult(c, true)
		return
	}
}

// 用于返回报表的高级抽象函数（可以通过重写structure.IListQuery的GetExcelFilter方法将查询条件拼接到报表的名字）
func BuildXlsxResponseV2(c *gin.Context, err error, q structure_base.IListQuery, fileName string, list interface{}, summary interface{}, sheetName ...string) {
	if err != nil {
		BuildCustomError(c, err)
		return
	} else {
		var (
			contents [][]interface{}
			title    []interface{}
			title2   []interface{}
			footer   []interface{}
			buffer   = &bytes.Buffer{}
		)

		title, title2 = GetXlsxHeader(list)
		contents = GetXlsxItemListByList(list)
		footer = GetXlsxItemList(summary)

		_, err = createXlsxFile(buffer, title, title2, sheetName, contents, footer)
		if err != nil {
			return
		}

		if _, ok := getResult(c); ok {
			return
		}

		c.Header("Content-Description", "File Transfer")
		c.Header("Content-Disposition", "attachment;filename="+url.QueryEscape(fileName+q.GetExcelFilter())+".xlsx")
		c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", buffer.Bytes())
		setResult(c, true)
		return
	}
}

func GetXlsxHeader(list interface{}) ([]interface{}, []interface{}) {
	r := make([]interface{}, 0)
	vList := reflect.ValueOf(list)
	if vList.Kind() == reflect.Ptr {
		vList = vList.Elem()
	}
	tList := vList.Type()
	if tList.Kind() == reflect.Slice {
		item := tList.Elem()
		if item.Kind() == reflect.Ptr {
			item = item.Elem()
		}
		for i := 0; i < item.NumField(); i++ {
			field := item.Field(i)
			tag := tools.GetTagKey(field, "excel")
			unitName := ""
			value := reflect.New(field.Type).Interface()
			if unit, ok := value.(tools.IUnit); ok {
				unitName = unit.UnitName()
			}

			if field.Type.Kind() == reflect.Struct {
				headers := getNestedHeaders(field.Type, make([]interface{}, 0))
				r = append(r, headers...)
			}

			if tag != "" {
				if unitName != "" {
					r = append(r, tag+"("+unitName+")")
				} else {
					r = append(r, tag)
				}
			}
		}
	}
	r2 := make([]interface{}, 0)
	if tList.Kind() == reflect.Slice {
		item := tList.Elem()
		if item.Kind() == reflect.Ptr {
			item = item.Elem()
		}
		for i := 0; i < item.NumField(); i++ {
			field := item.Field(i)
			tag := tools.GetTagKey(field, "excel2")
			unitName := ""
			value := reflect.New(field.Type).Interface()
			if unit, ok := value.(tools.IUnit); ok {
				unitName = unit.UnitName()
			}

			if tag != "" {
				if unitName != "" {
					r2 = append(r2, tag+"("+unitName+")")
				} else {
					r2 = append(r2, tag)
				}
			}
		}
	}
	return r, r2
}

// 递归获取嵌套结构体字段值
func getNestedHeaders(t reflect.Type, content []interface{}) []interface{} {

	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)

		tag := tools.GetTagKey(field, "excel")

		unitName := ""
		value := reflect.New(field.Type).Interface()
		if unit, ok := value.(tools.IUnit); ok {
			unitName = unit.UnitName()
		}

		if tag != "" {
			if unitName != "" {
				content = append(content, tag+"("+unitName+")")
			} else {
				content = append(content, tag)
			}
		}
		// // 检查是否是嵌套结构体
		if t.Field(i).Type.Kind() == reflect.Struct {
			nestedContent := getNestedHeaders(t.Field(i).Type, make([]interface{}, 0))
			content = append(content, nestedContent...)
		}
	}
	return content
}

func GetXlsxHeaderField(list interface{}) []interface{} {
	r := make([]interface{}, 0)
	vList := reflect.ValueOf(list)
	if vList.Kind() == reflect.Ptr {
		vList = vList.Elem()
	}
	tList := vList.Type()
	for i := 0; i < tList.NumField(); i++ {
		tag := tools.GetTagKey(tList.Field(i), "excel")
		if tag != "" {
			r = append(r, vList.Field(i).Interface())
		}
	}
	return r

}

func GetXlsxItemListByList(list interface{}) [][]interface{} {
	vList := reflect.ValueOf(list)
	contents := make([][]interface{}, 0)
	for i := 0; i < vList.Len(); i++ {
		vItem := vList.Index(i)
		if vItem.Kind() == reflect.Ptr {
			vItem = vItem.Elem()
		}
		content := make([]interface{}, 0)
		tList := vItem.Type()
		for i := 0; i < tList.NumField(); i++ {
			tag := tools.GetTagKey(tList.Field(i), "excel")
			if tag != "" {
				content = append(content, vItem.Field(i).Interface())
			}
			// 检查是否是嵌套结构体
			if tList.Field(i).Type.Kind() == reflect.Struct {
				nestedContent := getNestedFields(vItem.Field(i), tList.Field(i).Type, make([]interface{}, 0))
				content = append(content, nestedContent...)
			}
		}
		contents = append(contents, content)
	}
	return contents
}

// 递归获取嵌套结构体字段值
func getNestedFields(vItem reflect.Value, t reflect.Type, content []interface{}) []interface{} {
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		tag := tools.GetTagKey(field, "excel")
		if tag != "" {
			content = append(content, vItem.Field(i).Interface())
		}

		// 检查是否是嵌套结构体
		if field.Type.Kind() == reflect.Struct {
			nestedContent := getNestedFields(vItem.Field(i), field.Type, make([]interface{}, 0))
			content = append(content, nestedContent...)
		}
	}
	return content
}

func GetXlsxItemList(v interface{}) []interface{} {
	vItem := reflect.ValueOf(v)
	if vItem.IsZero() {
		return []interface{}{}
	}
	if vItem.Kind() == reflect.Ptr {
		vItem = vItem.Elem()
	}
	content := make([]interface{}, 0)
	tList := vItem.Type()
	for i := 0; i < tList.NumField(); i++ {
		tag := tools.GetTagKey(tList.Field(i), "excel")
		if tag != "" {
			content = append(content, vItem.Field(i).Interface())
		}
	}
	// content = append(content, content)

	return content
}

// BuildXlsxResponseV3 用于导出成品库存汇总表的高级抽象函数
// 使用方法：传入导出配置和数据，自动处理表头合并、样式等
// 注意事项：
// 1. 需要传入实现了GetExcelExportConfig接口的数据列表
// 2. 支持多级表头自动合并
// 3. 支持自定义样式配置
func BuildXlsxResponseV3(c *gin.Context, err error, q structure_base.IListQuery, list interface{}, summary interface{}) {
	if err != nil {
		BuildCustomError(c, err)
		return
	}

	// 获取导出配置
	exporter, ok := list.(interface {
		GetExcelExportConfig() structure.ExcelExport
	})
	if !ok {
		BuildCustomError(c, err)
		return
	}

	config := exporter.GetExcelExportConfig()
	if config.SheetName == "" {
		BuildCustomError(c, err)
		return
	}

	var buffer = &bytes.Buffer{}

	// 创建Excel文件
	f := excelize.NewFile()
	defer f.Close()

	// 创建新的Sheet
	sheetName := config.SheetName
	index, _ := f.NewSheet(sheetName)

	// 设置默认列宽
	f.SetColWidth(sheetName, "A", "ZZ", 15)

	// 写入表头并合并单元格
	for _, mergeCell := range config.MergeCells {
		// 写入标题
		cell, _ := excelize.CoordinatesToCellName(mergeCell.StartCol, mergeCell.StartRow)
		f.SetCellValue(sheetName, cell, mergeCell.Title)

		// 合并单元格
		startCell, _ := excelize.CoordinatesToCellName(mergeCell.StartCol, mergeCell.StartRow)
		endCell, _ := excelize.CoordinatesToCellName(mergeCell.EndCol, mergeCell.EndRow)
		f.MergeCell(sheetName, startCell, endCell)

		// 设置样式
		style, _ := f.NewStyle(config.HeaderStyle)
		f.SetCellStyle(sheetName, startCell, endCell, style)

		// 设置列宽
		if mergeCell.Width > 0 {
			f.SetColWidth(sheetName, startCell[:1], startCell[:1], float64(mergeCell.Width))
		}
	}

	// 写入数据
	// lastRow := len(config.MergeCells)
	lastRow := config.HeaderMaxLevel
	for rowIndex, dataRow := range config.DataRows {
		for colIndex, field := range config.Fields {
			cell, _ := excelize.CoordinatesToCellName(colIndex+1, rowIndex+lastRow+1)
			f.SetCellValue(sheetName, cell, dataRow[field])
		}
	}

	// 写入汇总行
	if summary != nil {
		_summary, ok := summary.(structure_base.ExportData)
		if ok {
			dataRow := _summary.ToExcelRow()
			for colIndex, field := range config.Fields {
				cell, _ := excelize.CoordinatesToCellName(colIndex+1, len(config.DataRows)+lastRow+1)
				f.SetCellValue(sheetName, cell, dataRow[field])
			}
		}
	}

	f.SetActiveSheet(index)

	// 写入buffer
	if err := f.Write(buffer); err != nil {
		BuildCustomError(c, err)
		return
	}

	if _, ok := getResult(c); ok {
		return
	}

	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Disposition", "attachment;filename="+url.QueryEscape(config.FileName+q.GetExcelFilter())+".xlsx")
	c.Data(http.StatusOK, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", buffer.Bytes())
	setResult(c, true)
}
