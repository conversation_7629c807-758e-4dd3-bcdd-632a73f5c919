package product

import (
	iconst "hcscm/common/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/vars"
)

type AddFpmInOrderItemParamList []AddFpmInOrderItemParam

func (r AddFpmInOrderItemParamList) Adjust() {

}

type AddFpmInOrderItemParam struct {
	structure_base.Param
	ItemFCData             AddFpmInOrderItemFcParamList `json:"item_fc_data"` // 成品信息
	Id                     uint64                       `json:"id,omitempty"`
	ParentId               uint64                       `json:"parent_id"`                  // 父id（单号id）
	ParentOrderNo          string                       `json:"parent_order_no"`            // 父单号(对应的单据号)
	SumStockId             uint64                       `json:"sum_stock_id"`               // 汇总库存成品id
	QuoteOrderNo           string                       `json:"quote_order_no"`             // 引用数据单号
	QuoteOrderItemId       uint64                       `json:"quote_order_item_id"`        // 引用数据单物料那条id
	ProductCode            string                       `json:"product_code"`               // 成品编号
	ProductName            string                       `json:"product_name"`               // 成品名称
	CustomerId             uint64                       `json:"customer_id"`                // 所属客户id
	ProductColorId         uint64                       `json:"product_color_id"`           // 成品颜色id
	ProductId              uint64                       `json:"product_id"`                 // 成品id
	ProductColorCode       string                       `json:"product_color_code"`         // 成品颜色编号
	ProductColorName       string                       `json:"product_color_name"`         // 成品颜色名称
	DyeFactoryColorCode    string                       `json:"dye_factory_color_code"`     // 染厂色号
	DyeFactoryDyelotNumber string                       `json:"dye_factory_dyelot_number"`  // 染厂缸号
	ProductWidth           string                       `json:"product_width"`              // 成品幅宽
	ProductGramWeight      string                       `json:"product_gram_weight"`        // 成品克重
	ProductLevelId         uint64                       `json:"product_level_id"`           // 成品等级
	ProductRemark          string                       `json:"product_remark"`             // 成品备注
	ProductCraft           string                       `json:"product_craft"`              // 成品工艺
	ProductIngredient      string                       `json:"product_ingredient"`         // 成品成分
	QuoteRoll              int                          `json:"quote_roll"`                 // 引用数据匹数(件)，乘100存
	QuoteTotalWeight       int                          `json:"quote_total_weight"`         // 引用数据总数量(公斤)，乘10000存
	InRoll                 int                          `json:"in_roll"`                    // 进仓件数(件)，乘100存
	TotalWeight            int                          `json:"total_weight"`               // 总数量(公斤)，乘10000存
	WeightError            int                          `json:"weight_error"`               // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int                          `json:"paper_tube_weight"`          // 纸筒数量(公斤)，乘10000存
	ReceiveWeight          int                          `json:"receive_weight"`             // 本次收货数量(公斤)，乘10000存
	PTWeightAndWeightError int                          `json:"pt_weight_and_weight_error"` // 供方纸筒空差(公斤)，乘10000存
	ShouldPayWeight        int                          `json:"should_pay_weight"`          // 应付数量(公斤)，乘10000存
	SettleWeight           int                          `json:"settle_weight"`              // 结算数量(公斤)，乘10000存
	QuoteWeight            int                          `json:"quote_weight"`               // 引用数据重(公斤)，乘10000存(待收数量)
	UnitPrice              int                          `json:"unit_price"`                 // 单价(元)，乘10000存
	UnitId                 uint64                       `json:"unit_id"`                    // 单位id
	AuxiliaryUnitId        uint64                       `json:"auxiliary_unit_id"`          // 辅助单位id
	InLength               int                          `json:"in_length"`                  // 进仓长度，乘100存
	QuoteLength            int                          `json:"quote_length"`               // 引用数据长度，乘100存
	LengthUnitPrice        int                          `json:"length_unit_price"`          // 长度单价，乘10000存
	OtherPrice             int                          `json:"other_price"`                // 其他金额(元)，乘100存(数量单价*结算数量)+(长度单价*进仓长度)+其他金额
	TotalPrice             int                          `json:"total_price"`                // 总金额/进仓金额(元)，乘100存
	Remark                 string                       `json:"remark"`                     // 备注
	ArrangeOrderItemId     uint64                       `json:"arrange_order_item_id"`      // 配布单分录行id

	// 结算数量，结算空差
	ActuallyWeight    int `json:"actually_weight"`     // 码单数量(销售退货进仓单使用)
	SettleErrorWeight int `json:"settle_error_weight"` // 结算空差(销售退货进仓单使用)

	FinishProductWidthUnitId      uint64 `json:"finish_product_width_unit_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64 `json:"finish_product_gram_weight_unit_id"` // 成品克重单位id(字典)
	SalePlanOrderItemId           uint64 `json:"sale_plan_order_item_id"`            // 成品销售计划单子项信息id
	SalePlanOrderItemNo           string `json:"sale_plan_order_item_no"`            // 成品销售计划单子项单号
}

type AddFpmInOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// item 使用
// tw : 总数量
// tp : 总价
// tl : 总长
// te : 总空差
// tpp : 总纸筒数量
// tsw : 结算数量
func (r AddFpmInOrderItemParam) GetTotalWPL(inType iconst.WarehouseGoodInType) (
	totalWeight int, totalPrice int, totalLength int,
	weightError int, settleWeightError int, totalWeightError int,
	totalPaperTubeWeight int, totalSettleWeight int, totalActuallyWeight int) {
	var weightPrice float64
	// if inType == iconst.WarehouseGoodInTypePurchase {
	for _, v2 := range r.ItemFCData {
		totalWeight += v2.BaseUnitWeight
		totalLength += v2.Length
		weightError += v2.WeightError
		settleWeightError += v2.SettleErrorWeight
		totalWeightError += v2.WeightError + v2.SettleErrorWeight // 总空差=码单空差+结算空差
		totalPaperTubeWeight += v2.PaperTubeWeight
	}
	totalSettleWeight = totalWeight - totalWeightError
	totalActuallyWeight = totalWeight - weightError

	if inType == iconst.WarehouseGoodInTypePurchase {
		weightPrice = tools.DecimalDiv(float64(r.ShouldPayWeight*r.UnitPrice), vars.WeightUnitPriceMult)
	} else {
		weightPrice = tools.DecimalDiv(float64(totalSettleWeight*r.UnitPrice), vars.WeightUnitPriceMult)
	}
	// 2024-09-10长度修改为10000进位
	lenPrice := tools.DecimalDiv(float64(totalLength*r.LengthUnitPrice), vars.LengthUnitPriceMult)
	// lenPrice := tools.DecimalDiv(float64(totalLength*r.LengthUnitPrice), 10000)
	tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
	totalPrice = tempPrice + r.OtherPrice
	// }
	return
}

func (r *AddFpmInOrderItemParam) Swap2ItemParam(req GetFpmOutOrderItemData) {
	r.SumStockId = req.SumStockId
	r.ProductCode = req.ProductCode
	r.ProductName = req.ProductName
	r.CustomerId = req.CustomerId
	r.ProductColorId = req.ProductColorId
	r.ProductId = req.ProductId
	r.ProductColorCode = req.ProductColorCode
	r.ProductColorName = req.ProductColorName
	r.DyeFactoryColorCode = req.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = req.DyeFactoryDyelotNumber
	r.ProductWidth = req.ProductWidth
	r.ProductGramWeight = req.ProductGramWeight
	r.ProductLevelId = req.ProductLevelId
	r.ProductRemark = req.ProductRemark
	r.ProductCraft = req.ProductCraft
	r.ProductIngredient = req.ProductIngredient
	r.InRoll = req.OutRoll
	r.TotalWeight = req.TotalWeight
	r.WeightError = req.WeightError
	r.PaperTubeWeight = req.PaperTubeWeight
	r.SettleWeight = req.SettleWeight
	r.UnitPrice = req.UnitPrice
	r.UnitId = req.UnitId
	r.AuxiliaryUnitId = req.AuxiliaryUnitId
	r.InLength = req.OutLength
	r.LengthUnitPrice = req.LengthUnitPrice
	r.OtherPrice = req.OtherPrice
	r.TotalPrice = req.TotalPrice
	r.Remark = req.Remark
	r.ArrangeOrderItemId = req.ArrangeItemId
	r.QuoteOrderItemId = req.Id
	r.SettleErrorWeight = req.SettleErrorWeight
}

type GetFpmInOrderItemQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmInOrderItemListQuery struct {
	structure_base.ListQuery
	InOrderType            iconst.WarehouseGoodInType `form:"in_order_type"`             // 入仓类型
	SumStockId             uint64                     `form:"sum_stock_id"`              // 汇总库存成品id
	ParentId               uint64                     `form:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string                     `form:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string                     `form:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64                     `form:"quote_order_item_id"`       // 引用数据单物料那条id
	ProductCode            string                     `form:"product_code"`              // 成品编号
	ProductName            string                     `form:"product_name"`              // 成品名称
	CustomerId             uint64                     `form:"customer_id"`               // 所属客户id
	ProductColorId         uint64                     `form:"product_color_id"`          // 成品颜色id
	DyeFactoryColorCode    string                     `form:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                     `form:"dye_factory_dyelot_number"` // 染厂缸号
	ProductWidth           string                     `form:"product_width"`             // 成品幅宽
	ProductGramWeight      string                     `form:"product_gram_weight"`       // 成品克重
	ProductLevelId         uint64                     `form:"product_level_id"`          // 成品等级
	ProductRemark          string                     `form:"product_remark"`            // 成品备注
	ProductCraft           string                     `form:"product_craft"`             // 成品工艺
	ProductIngredient      string                     `form:"product_ingredient"`        // 成品成分
	Remark                 string                     `form:"remark"`                    // 备注
	ProductId              uint64                     `form:"product_id"`                // 成品id

	OrderNo          string   `form:"order_no"`           // 成品销售退货进仓单号
	ProductColorCode string   `form:"product_color_code"` // 成品颜色编号
	ProductColorName string   `form:"product_color_name"` // 成品颜色名称
	UnitId           uint64   `form:"unit_id"`            // 单位id
	OrderPassIds     []uint64 // 已审核的单据id
	UseByEnum        bool     // 是否用左枚举或者下拉
}

func (r GetFpmInOrderItemListQuery) Adjust() {

}

type GetFpmInOrderItemData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ItemFCData             GetFpmInOrderItemFcDataList `json:"item_fc_data"`               // 成品信息
	SumStockId             uint64                      `json:"sum_stock_id"`               // 汇总库存成品id
	ParentId               uint64                      `json:"parent_id"`                  // 父id（单号id）
	ParentOrderNo          string                      `json:"parent_order_no"`            // 父单号(对应的单据号)
	QuoteOrderNo           string                      `json:"quote_order_no"`             // 引用数据单号
	QuoteOrderId           uint64                      `json:"quote_order_id"`             // 引用数据单id
	QuoteOrderItemId       uint64                      `json:"quote_order_item_id"`        // 引用数据单物料那条id
	ProductId              uint64                      `json:"product_id"`                 // 成品id
	ProductCode            string                      `json:"product_code"`               // 成品编号
	ProductName            string                      `json:"product_name"`               // 成品名称
	CustomerId             uint64                      `json:"customer_id"`                // 所属客户id
	ProductColorId         uint64                      `json:"product_color_id"`           // 成品颜色id
	DyeFactoryColorCode    string                      `json:"dye_factory_color_code"`     // 染厂色号
	DyeFactoryDyelotNumber string                      `json:"dye_factory_dyelot_number"`  // 染厂缸号
	ProductLevelId         uint64                      `json:"product_level_id"`           // 成品等级
	ProductRemark          string                      `json:"product_remark"`             // 成品备注
	ProductCraft           string                      `json:"product_craft"`              // 成品工艺
	ProductIngredient      string                      `json:"product_ingredient"`         // 成品成分
	QuoteRoll              int                         `json:"quote_roll"`                 // 引用数据匹数(件)，乘100存
	QuoteTotalWeight       int                         `json:"quote_total_weight"`         // 引用数据总数量(公斤)，乘10000存
	InRoll                 int                         `json:"in_roll"`                    // 进仓件数(件)，乘100存
	TotalWeight            int                         `json:"total_weight"`               // 总数量(公斤)，乘10000存
	WeightError            int                         `json:"weight_error"`               // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int                         `json:"paper_tube_weight"`          // 纸筒数量(公斤)，乘10000存
	ReceiveWeight          int                         `json:"receive_weight"`             // 本次收货数量(公斤)，乘10000存
	PTWeightAndWeightError int                         `json:"pt_weight_and_weight_error"` // 供方纸筒空差(公斤)，乘10000存
	ShouldPayWeight        int                         `json:"should_pay_weight"`          // 应付数量(公斤)，乘10000存
	SettleWeight           int                         `json:"settle_weight"`              // 结算数量(公斤)，乘10000存
	QuoteWeight            int                         `json:"quote_weight"`               // 引用数据重(公斤)，乘10000存
	UnitPrice              int                         `json:"unit_price"`                 // 单价(元)，乘10000存
	InLength               int                         `json:"in_length"`                  // 进仓长度，乘100存
	QuoteLength            int                         `json:"quote_length"`               // 引用数据长度，乘100存
	LengthUnitPrice        int                         `json:"length_unit_price"`          // 长度单价，乘10000存
	OtherPrice             int                         `json:"other_price"`                // 其他金额(元)，乘100存(数量单价*结算数量)+(长度单价*进仓长度)+其他金额
	TotalPrice             int                         `json:"total_price"`                // 总金额/进仓金额(元)，乘100存
	Remark                 string                      `json:"remark"`                     // 备注
	UnitId                 uint64                      `json:"unit_id"`                    // 单位id
	AuxiliaryUnitId        uint64                      `json:"auxiliary_unit_id"`          // 辅助单位id
	ProductColorCode       string                      `json:"product_color_code"`         // 成品颜色编号
	ProductColorName       string                      `json:"product_color_name"`         // 成品颜色名称
	ActuallyWeight         int                         `json:"actually_weight"`            // 码单数量
	SettleErrorWeight      int                         `json:"settle_error_weight"`        // 结算空差(公斤)，乘10000存
	ArrangeOrderItemId     uint64                      `json:"arrange_order_item_id"`      // 配布单分录行id
	// 转义
	UnitName          string `json:"unit_name"`           // 单位id
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 辅助单位名称
	CustomerName      string `json:"customer_name"`       // 所属客户name
	ProductLevelName  string `json:"product_level_name"`  // 成品等级name
	// ColorName     string `json:"product_color_name"`      // 成品颜色name
	// 调拨信息
	AllocateRoll   int `json:"allocate_roll"`   // 调拨匹数
	AllocateWeight int `json:"allocate_weight"` // 调拨数量
	AllocateLength int `json:"allocate_length"` // 调拨长度
	// 出仓信息
	OutRoll                int    `json:"out_roll"`                            // 出仓匹数
	OutWeight              int    `json:"out_weight"`                          // 出仓数量
	OutLength              int    `json:"out_length"`                          // 出仓长度
	SalePlanOrderItemId    uint64 `json:"sale_plan_order_item_id"`             // 成品销售计划单子项信息id
	SalePlanOrderItemNo    string `json:"sale_plan_order_item_no"`             // 成品销售计划单子项单号
	TotalWaitCollectWeight int    `json:"total_wait_collect_weight,omitempty"` // 待收货总数量
}

type GetFpmInOrderItemDataList []GetFpmInOrderItemData

func (g GetFpmInOrderItemDataList) PickById(id uint64) (data GetFpmInOrderItemData) {
	for _, i := range g {
		if i.Id == id {
			data = i
			return
		}
	}
	return
}

func (g GetFpmInOrderItemDataList) Pick(productId, colorId uint64, dyelotNumber string) (data GetFpmInOrderItemData) {
	for _, i := range g {
		if i.ProductId == productId && i.ProductColorId == colorId && i.DyeFactoryDyelotNumber == dyelotNumber {
			data = i
			return
		}
	}
	return
}

func (g GetFpmInOrderItemDataList) Adjust() {

}

type DeleteFpmInOrderItemParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmInOrderItemData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type GetFpmInOrderItemDropdownData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	WarehouseInTime        tools.MyTime                `json:"warehouse_in_time"`         // 进仓时间
	ItemFCData             GetFpmInOrderItemFcDataList `json:"item_fc_data"`              // 成品信息
	SumStockId             uint64                      `json:"sum_stock_id"`              // 汇总库存成品id
	ParentId               uint64                      `json:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string                      `json:"parent_order_no"`           // 父单号(对应的单据号)
	QuoteOrderNo           string                      `json:"quote_order_no"`            // 引用数据单号
	QuoteOrderItemId       uint64                      `json:"quote_order_item_id"`       // 引用数据单物料那条id
	ProductId              uint64                      `json:"product_id"`                // 成品id
	ProductCode            string                      `json:"product_code"`              // 成品编号
	ProductName            string                      `json:"product_name"`              // 成品名称
	CustomerId             uint64                      `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64                      `json:"product_color_id"`          // 成品颜色id
	DyeFactoryColorCode    string                      `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                      `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductLevelId         uint64                      `json:"product_level_id"`          // 成品等级
	ProductRemark          string                      `json:"product_remark"`            // 成品备注
	ProductCraft           string                      `json:"product_craft"`             // 成品工艺
	ProductIngredient      string                      `json:"product_ingredient"`        // 成品成分
	QuoteRoll              int                         `json:"quote_roll"`                // 引用数据匹数(件)，乘100存
	QuoteTotalWeight       int                         `json:"quote_total_weight"`        // 引用数据总数量(公斤)，乘10000存
	InRoll                 int                         `json:"in_roll"`                   // 进仓件数(件)，乘100存
	TotalWeight            int                         `json:"total_weight"`              // 总数量(公斤)，乘10000存
	WeightError            int                         `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int                         `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleWeight           int                         `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	QuoteWeight            int                         `json:"quote_weight"`              // 引用数据重(公斤)，乘10000存
	UnitPrice              int                         `json:"unit_price"`                // 单价(元)，乘10000存
	InLength               int                         `json:"in_length"`                 // 进仓长度，乘100存
	QuoteLength            int                         `json:"quote_length"`              // 引用数据长度，乘100存
	LengthUnitPrice        int                         `json:"length_unit_price"`         // 长度单价，乘10000存
	OtherPrice             int                         `json:"other_price"`               // 其他金额(元)，乘100存(数量单价*结算数量)+(长度单价*进仓长度)+其他金额
	TotalPrice             int                         `json:"total_price"`               // 总金额/进仓金额(元)，乘100存
	Remark                 string                      `json:"remark"`                    // 备注
	UnitId                 uint64                      `json:"unit_id"`                   // 单位id
	AuxiliaryUnitId        uint64                      `json:"auxiliary_unit_id"`         // 辅助单位id
	ProductColorCode       string                      `json:"product_color_code"`        // 成品颜色编号
	ProductColorName       string                      `json:"product_color_name"`        // 成品颜色名称
	ActuallyWeight         int                         `json:"actually_weight"`           // 码单数量
	SettleErrorWeight      int                         `json:"settle_error_weight"`       // 结算空差(公斤)，乘10000存
	ArrangeOrderItemId     uint64                      `json:"arrange_order_item_id"`     // 配布单分录行id
	// 转义
	UnitName          string `json:"unit_name"`           // 单位id
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 辅助单位名称
	CustomerName      string `json:"customer_name"`       // 所属客户name
	ProductLevelName  string `json:"product_level_name"`  // 成品等级name
	// ColorName     string `json:"product_color_name"`      // 成品颜色name
	// 调拨信息
	AllocateRoll   int `json:"allocate_roll"`   // 调拨匹数
	AllocateWeight int `json:"allocate_weight"` // 调拨数量
	AllocateLength int `json:"allocate_length"` // 调拨长度
	// 出仓信息
	OutRoll                int    `json:"out_roll"`                            // 出仓匹数
	OutWeight              int    `json:"out_weight"`                          // 出仓数量
	OutLength              int    `json:"out_length"`                          // 出仓长度
	SalePlanOrderItemId    uint64 `json:"sale_plan_order_item_id"`             // 成品销售计划单子项信息id
	SalePlanOrderItemNo    string `json:"sale_plan_order_item_no"`             // 成品销售计划单子项单号
	TotalWaitCollectWeight int    `json:"total_wait_collect_weight,omitempty"` // 待收货总数量
	AvailableRoll          int    `json:"available_roll"`                      // 可用匹数
	AvailableWeight        int    `json:"available_weight"`                    // 可用数量
	AvailableLength        int    `json:"available_length"`                    // 可用长度
}

type GetFpmInOrderItemDropdownDataList []GetFpmInOrderItemDropdownData

func (g GetFpmInOrderItemDropdownDataList) Pick(id uint64) (item GetFpmInOrderItemDropdownData) {
	for _, i := range g {
		if i.Id == id {
			item = i
			break
		}
	}
	return
}

func (g GetFpmInOrderItemDropdownDataList) Adjust() {

}

type FpmProInOrderUpdateBuoyantWeightPriceParam struct {
	structure_base.Param
	BumId              uint64 `json:"bum_id"`
	BuoyantWeightPrice int    `json:"buoyant_weight_price"`
}
