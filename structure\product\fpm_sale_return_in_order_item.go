package product

import (
	common "hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/vars"
)

type AddFpmSaleReturnInOrderItemParamList []AddFpmSaleReturnInOrderItemParam

func (r AddFpmSaleReturnInOrderItemParamList) Adjust() {

}

type AddFpmSaleReturnInOrderItemParam struct {
	structure_base.Param
	ItemFCData AddFpmInOrderItemFcParamList `json:"item_fc_data"` // 细码
	// Id                            uint64                       `json:"id,omitempty"`
	ParentId                      uint64               `json:"parent_id"`                          // 父id（单id）
	SumStockId                    uint64               `json:"sum_stock_id"`                       // 汇总库存成品id
	ParentOrderNo                 string               `json:"parent_order_no"`                    // 父单号(成品销售退货进仓单号)
	QuoteOrderNo                  string               `json:"quote_order_no"`                     // 销售送货单号
	QuoteOrderItemId              uint64               `json:"quote_order_item_id"`                // 销售送货单号item 的id
	ProductId                     uint64               `json:"product_id"`                         // 成品id
	ProductCode                   string               `json:"product_code"`                       // 成品编号
	ProductName                   string               `json:"product_name"`                       // 成品名称
	CustomerId                    uint64               `json:"customer_id"`                        // 所属客户id
	ProductColorId                uint64               `json:"product_color_id"`                   // 成品颜色id
	ProductColorCode              string               `json:"product_color_code"`                 // 成品名称
	ProductColorName              string               `json:"product_color_name"`                 // 成品名称
	DyeFactoryColorCode           string               `json:"dye_factory_color_code"`             // 染厂色号
	DyeFactoryDyelotNumber        string               `json:"dye_factory_dyelot_number"`          // 染厂缸号
	ProductWidth                  string               `json:"product_width"`                      // 成品幅宽
	ProductGramWeight             string               `json:"product_gram_weight"`                // 成品克重
	ProductLevelId                uint64               `json:"product_level_id"`                   // 成品等级
	ProductRemark                 string               `json:"product_remark"`                     // 成品备注
	ProductCraft                  string               `json:"product_craft"`                      // 成品工艺
	ProductIngredient             string               `json:"product_ingredient"`                 // 成品成分
	InRoll                        int                  `json:"in_roll"`                            // 进仓件数(件)，乘100存
	InWeight                      int                  `json:"in_weight"`                          // 进仓数量，乘100存
	WeightError                   int                  `json:"weight_error"`                       // 空差数量(公斤)，乘10000存
	PaperTubeWeight               int                  `json:"paper_tube_weight"`                  // 纸筒数量(公斤)，乘10000存
	SettleWeight                  int                  `json:"settle_weight"`                      // 结算数量(公斤)，乘10000存
	ActuallyWeight                int                  `json:"actually_weight"`                    // 码单数量(公斤)，乘10000存
	SettleErrorWeight             int                  `json:"settle_error_weight"`                // 结算空差数量(公斤)，乘10000存
	UnitId                        uint64               `json:"unit_id"`                            // 单位id
	AuxiliaryUnitId               uint64               `json:"auxiliary_unit_id"`                  // 辅助单位id（用于判断计算金额时使用哪个数量）
	InLength                      int                  `json:"in_length"`                          // 进仓长度，乘100存
	Remark                        string               `json:"remark"`                             // 备注
	ReturnRoll                    int                  `json:"return_roll"`                        // 已退匹数
	ReturnWeight                  int                  `json:"return_weight"`                      // 已退数量
	ReturnLength                  int                  `json:"return_length"`                      // 已退长度
	ReturnPrice                   int                  `json:"return_price"`                       // 退货单价
	LengthCutReturnPrice          int                  `json:"length_cut_return_price"`            // 剪板退货单价
	FinishProductWidthUnitId      uint64               `json:"finish_product_width_unit_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64               `json:"finish_product_gram_weight_unit_id"` // 成品克重单位id(字典)
	OtherPrice                    int                  `json:"other_price"`                        // 其他金额(元)，乘100存
	SettlePrice                   int                  `json:"settle_price"`                       // 成品总金额
	OrderType                     common.SaleOrderType `json:"order_type"`                         // 订单类型 1大货 2剪板 3客订大货 4客订剪板
}

type AddFpmSaleReturnInOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// item 使用
func (r AddFpmSaleReturnInOrderItemParam) GetTotalWPL() (
	totalWeight int, totalPrice int, totalLength int,
	weightError int, settleWeightError int, totalWeightError int,
	totalPaperTubeWeight int, totalSettleWeight int, totalActuallyWeight int) {
	// if inType == iconst.WarehouseGoodInTypePurchase {
	for _, v2 := range r.ItemFCData {
		totalWeight += v2.BaseUnitWeight
		totalLength += v2.Length
		weightError += v2.WeightError
		settleWeightError += v2.SettleErrorWeight
		totalWeightError += v2.WeightError + v2.SettleErrorWeight // 总空差=码单空差+结算空差
		totalPaperTubeWeight += v2.PaperTubeWeight
	}
	totalSettleWeight = totalWeight - totalWeightError
	totalActuallyWeight = totalWeight - weightError

	weightPrice := tools.DecimalDiv(float64(totalSettleWeight*r.ReturnPrice), vars.WeightUnitPriceMult)
	// 2024-09-10长度修改为10000进位
	lenPrice := tools.DecimalDiv(float64(totalLength*r.LengthCutReturnPrice), vars.LengthUnitPriceMult)
	// lenPrice := tools.DecimalDiv(float64(totalLength*r.LengthUnitPrice), 10000)
	tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
	totalPrice = tempPrice + r.OtherPrice
	// }
	return
}

type UpdateFpmSaleReturnInOrderItemBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateFpmSaleReturnInOrderItemStatusParam struct {
	structure_base.Param
	Id          tools.QueryIntList        `json:"id"`
	AuditStatus common_system.OrderStatus `json:"audit_status"`
}

type UpdateFpmSaleReturnInOrderItemStatusData struct {
	structure_base.ResponseData
}

type GetFpmSaleReturnInOrderItemQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmSaleReturnInOrderItemListQuery struct {
	structure_base.ListQuery
	ParentId               uint64 `form:"parent_id"`                 // 父id（成品销售退货进仓单号id）
	SumStockId             uint64 `form:"sum_stock_id"`              // 汇总库存成品id
	OrderNo                string `form:"order_no"`                  // 成品销售退货进仓单号
	QuoteOrderNo           string `form:"quote_order_no"`            // 销售送货单号
	QuoteOrderItemId       uint64 `form:"quote_order_item_id"`       // 销售送货单号item 的id
	ProductId              uint64 `form:"product_id"`                // 成品id
	ProductCode            string `form:"product_code"`              // 成品编号
	ProductName            string `form:"product_name"`              // 成品名称
	CustomerId             uint64 `form:"customer_id"`               // 所属客户id
	ProductColorId         uint64 `form:"product_color_id"`          // 成品颜色id
	ProductColorCode       string `form:"product_color_code"`        // 成品名称
	ProductColorName       string `form:"product_color_name"`        // 成品名称
	DyeFactoryColorCode    string `form:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string `form:"dye_factory_dyelot_number"` // 染厂缸号
	ProductWidth           string `form:"product_width"`             // 成品幅宽
	ProductGramWeight      string `form:"product_gram_weight"`       // 成品克重
	ProductLevelId         uint64 `form:"product_level_id"`          // 成品等级
	ProductRemark          string `form:"product_remark"`            // 成品备注
	ProductCraft           string `form:"product_craft"`             // 成品工艺
	ProductIngredient      string `form:"product_ingredient"`        // 成品成分
	InRoll                 int    `form:"in_roll"`                   // 进仓件数(件)，乘100存
	InWeight               int    `form:"in_weight"`                 // 进仓数量，乘100存
	WeightError            int    `form:"weight_error"`              // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int    `form:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleWeight           int    `form:"settle_weight"`             // 结算数量(公斤)，乘10000存
	ActuallyWeight         int    `form:"actually_weight"`           // 码单数量(公斤)，乘10000存
	SettleErrorWeight      int    `form:"settle_error_weight"`       // 结算空差数量(公斤)，乘10000存
	UnitId                 uint64 `form:"unit_id"`                   // 单位id
	InLength               int    `form:"in_length"`                 // 进仓长度，乘100存
	Remark                 string `form:"remark"`                    // 备注
	ReturnRoll             int    `form:"return_roll"`               // 已退匹数
	ReturnWeight           int    `form:"return_weight"`             // 已退数量
	ReturnLength           int    `form:"return_length"`             // 已退长度

	UseByEnum bool
}

func (r GetFpmSaleReturnInOrderItemListQuery) Adjust() {

}

type GetFpmSaleReturnInOrderItemData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	ItemFCData             GetFpmInOrderItemFcDataList `json:"item_fc_data"`              // 细码
	ParentId               uint64                      `json:"parent_id"`                 // 父id（单id）
	SumStockId             uint64                      `json:"sum_stock_id"`              // 汇总库存成品id
	ParentOrderNo          string                      `json:"parent_order_no"`           // 父单号(成品销售退货进仓单号)
	QuoteOrderNo           string                      `json:"quote_order_no"`            // 销售送货单号
	QuoteOrderItemId       uint64                      `json:"quote_order_item_id"`       // 销售送货单号item 的id
	ProductId              uint64                      `json:"product_id"`                // 成品id
	ProductCode            string                      `json:"product_code"`              // 成品编号
	ProductName            string                      `json:"product_name"`              // 成品名称
	CustomerId             uint64                      `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64                      `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string                      `json:"product_color_code"`        // 成品名称
	ProductColorName       string                      `json:"product_color_name"`        // 成品名称
	DyeFactoryColorCode    string                      `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string                      `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductLevelId         uint64                      `json:"product_level_id"`          // 成品等级
	ProductRemark          string                      `json:"product_remark"`            // 成品备注
	ProductCraft           string                      `json:"product_craft"`             // 成品工艺
	ProductIngredient      string                      `json:"product_ingredient"`        // 成品成分
	InRoll                 int                         `json:"in_roll"`                   // 进仓件数(件)，乘100存
	InWeight               int                         `json:"in_weight"`                 // 进仓数量，乘100存
	WeightError            int                         `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int                         `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	SettleWeight           int                         `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	ActuallyWeight         int                         `json:"actually_weight"`           // 码单数量(公斤)，乘10000存
	SettleErrorWeight      int                         `json:"settle_error_weight"`       // 结算空差数量(公斤)，乘10000存
	UnitId                 uint64                      `json:"unit_id"`                   // 单位id
	AuxiliaryUnitId        uint64                      `json:"auxiliary_unit_id"`         // 辅助单位id（用于判断计算金额时使用哪个数量）
	InLength               int                         `json:"in_length"`                 // 进仓长度，乘100存
	Remark                 string                      `json:"remark"`                    // 备注
	ReturnRoll             int                         `json:"return_roll"`               // 已退匹数
	ReturnWeight           int                         `json:"return_weight"`             // 已退数量
	ReturnLength           int                         `json:"return_length"`             // 已退长度
	ReturnPrice            int                         `json:"return_price"`              // 退货单价
	LengthCutReturnPrice   int                         `json:"length_cut_return_price"`   // 剪版退货单价
	SettlePrice            int                         `json:"settle_price"`              // 成品退货总金额
	OrderType              common.SaleOrderType        `json:"order_type"`                // 订单类型 1大货 2剪板 3客订大货 4客订剪板

	// 转义
	UnitName          string `json:"unit_name"`           // 单位id
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 辅助id
	CustomerName      string `json:"customer_name"`       // 所属客户name
	ProductLevelName  string `json:"product_level_name"`  // 成品等级name

	// 出仓信息
	OutRoll   int `json:"out_roll"`   // 出仓匹数
	OutWeight int `json:"out_weight"` // 出仓数量
	OutLength int `json:"out_length"` // 出仓长度
	// 可退信息
	NoReturnRoll   int    `json:"no_return_roll"`   // 未退匹数
	NoReturnLength int    `json:"no_return_length"` // 未退数量
	NoReturnWeight int    `json:"no_return_weight"` // 未退长度
	OrderTypeName  string `json:"order_type_name"`  // 订单类型
}

type GetFpmSaleReturnInOrderItemDataList []GetFpmSaleReturnInOrderItemData

func (g GetFpmSaleReturnInOrderItemDataList) Adjust() {

}

type DeleteFpmSaleReturnInOrderItemParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmSaleReturnInOrderItemData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}
