package product

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	"hcscm/vars"
)

func GetFpmOutOrderItemIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_out_order_item_id")
}

type FpmOutOrderItemList []FpmOutOrderItem

func (r FpmOutOrderItemList) List() []FpmOutOrderItem {
	return r
}

func (r FpmOutOrderItemList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmOutOrderItemList) GetParentIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.ParentId)
	}
	return o
}

func (r FpmOutOrderItemList) One() FpmOutOrderItem {
	return r[0]
}

func (r FpmOutOrderItemList) Pick(id uint64) (o FpmOutOrderItem) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r FpmOutOrderItemList) PickByArrangeItemId(id uint64) (o FpmOutOrderItemList) {
	for _, t := range r {
		if t.ArrangeItemId == id {
			o = append(o, t)
		}
	}
	return
}

func (r FpmOutOrderItemList) PickByParentId(parentId uint64) (o FpmOutOrderItemList) {
	var list = make(FpmOutOrderItemList, 0)
	for _, t := range r {
		if t.ParentId == parentId {
			list = append(list, t)
		}
	}
	return list
}

// FpmOutOrderItem 出仓成品信息
type FpmOutOrderItem struct {
	mysql_base.ModelHard
	Id                     uint64                         `gorm:"column:id;primaryKey"`
	WarehouseOutType       cus_const.WarehouseGoodOutType `gorm:"column:warehouse_out_type"`                                     // 出仓类型
	ParentId               uint64                         `gorm:"column:parent_id" relate:"parent_id"`                           // 父id（单号id）
	SumStockId             uint64                         `gorm:"column:sum_stock_id" relate:"sum_stock_id"`                     // 汇总库存成品id
	ParentOrderNo          string                         `gorm:"column:parent_order_no"`                                        // 父单号(对应的单据号)(对应的单据号)
	QuoteOrderNo           string                         `gorm:"column:quote_order_no"`                                         // 引用数据单号
	QuoteOrderItemId       uint64                         `gorm:"column:quote_order_item_id" relate:"quote_order_item_id"`       // 引用数据单物料那条id
	ProductCode            string                         `gorm:"column:product_code"`                                           // 成品编号
	ProductName            string                         `gorm:"column:product_name"`                                           // 成品名称
	CustomerId             uint64                         `gorm:"column:customer_id" relate:"biz_unit_id"`                       // 所属客户id
	ProductColorId         uint64                         `gorm:"column:product_color_id" relate:"product_color_id"`             // 成品颜色id
	ProductId              uint64                         `gorm:"column:product_id" relate:"product_id"`                         // 成品id
	ProductColorCode       string                         `gorm:"column:product_color_code"`                                     // 成品名称
	ProductColorName       string                         `gorm:"column:product_color_name"`                                     // 成品名称
	ProductLevelId         uint64                         `gorm:"column:product_level_id" relate:"product_level_id"`             // 成品等级
	ProductWidth           string                         `gorm:"column:product_width"`                                          // 成品幅宽
	DyeFactoryColorCode    string                         `gorm:"column:dye_factory_color_code"`                                 // 染厂色号
	DyeFactoryDyelotNumber string                         `gorm:"column:dye_factory_dyelot_number"`                              // 染厂缸号
	ProductGramWeight      string                         `gorm:"column:product_gram_weight"`                                    // 成品克重
	ProductRemark          string                         `gorm:"column:product_remark"`                                         // 成品备注
	ProductCraft           string                         `gorm:"column:product_craft"`                                          // 成品工艺
	ProductIngredient      string                         `gorm:"column:product_ingredient"`                                     // 成品成分
	OutRoll                int                            `gorm:"column:out_roll"`                                               // 出仓件数(件)，乘100存
	TotalWeight            int                            `gorm:"column:total_weight"`                                           // 总数量(公斤)，乘10000存
	WeightError            int                            `gorm:"column:weight_error"`                                           // 空差数量(公斤)，乘10000存
	PaperTubeWeight        int                            `gorm:"column:paper_tube_weight"`                                      // 纸筒数量(公斤)，乘10000存
	SettleWeight           int                            `gorm:"column:settle_weight"`                                          // 结算数量(公斤)，乘10000存
	SettleLength           int                            `gorm:"column:settle_length"`                                          // 结算长度(米)，乘100存
	ActuallyWeight         int                            `gorm:"column:actually_weight"`                                        // 码单数量
	SettleErrorWeight      int                            `gorm:"column:settle_error_weight"`                                    // 结算空差数量
	UnitId                 uint64                         `gorm:"column:unit_id" relate:"measurement_unit_id,unit_id"`           // 单位id
	AuxiliaryUnitId        uint64                         `gorm:"column:auxiliary_unit_id" relate:"measurement_unit_id,unit_id"` // 辅助单位id（用于判断计算金额时使用哪个数量）
	UnitPrice              int                            `gorm:"column:unit_price"`                                             // 单价(元)，乘10000存
	OutLength              int                            `gorm:"column:out_length"`                                             // 出仓长度，乘100存
	LengthUnitPrice        int                            `gorm:"column:length_unit_price"`                                      // 长度单价，乘10000存
	OtherPrice             int                            `gorm:"column:other_price"`                                            // 其他金额(元)，乘100存
	TotalPrice             int                            `gorm:"column:total_price"`                                            // 总金额/出仓金额(元)，乘100存（单价*结算数量)+(长度单价*进仓长度)+其他金额
	Remark                 string                         `gorm:"column:remark"`                                                 // 备注
	ArrangeItemId          uint64                         `gorm:"column:arrange_item_id" relate:"arrange_item_id"`               // 配布成品信息id
	SalePlanOrderItemId    uint64                         `gorm:"column:sale_plan_order_item_id"`                                // 成品销售计划单子项信息id
	FpmInOrderItemId       uint64                         `gorm:"column:fpm_in_order_item_id"`                                   // 成品进仓单详情id
	SumStockRoll           int                            `gorm:"column:sum_stock_roll"`                                         // 汇总库存成品匹数
	SumStockWeight         int                            `gorm:"column:sum_stock_weight"`                                       // 汇总库存成品数量
	SumStockLength         int                            `gorm:"column:sum_stock_length"`                                       // 汇总库存成品长度
	// 成品加工出仓单
	ReturnRoll   int `gorm:"column:return_roll"`   // 退货匹数，乘匹数进位存
	ReturnWeight int `gorm:"column:return_weight"` // 退货数量，乘数量进位存
	ReturnLength int `gorm:"column:return_length"` // 退货长度，乘长度进位存
	DyeRoll      int `gorm:"column:dye_roll"`      // 已排染匹数，乘匹数进位存
	DyeWeight    int `gorm:"column:dye_weight"`    // 已排染数量，乘数量进位存
}

// 查询后的钩子
func (r *FpmOutOrderItem) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmOutOrderItem) GetId() uint64 {
	return r.Id
}

// TableName FpmOutOrderItem 表名
func (FpmOutOrderItem) TableName() string {
	return "fpm_out_order_item"
}

func (r FpmOutOrderItem) IsMain() bool {
	return false
}

func (r FpmOutOrderItem) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmOutOrderItem) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

// ErrCodeFpmOutOrderItemAlreadyExist     ErrCode = 51XX1 // 出仓成品信息已存在
// ErrCodeFpmOutOrderItemNotExist         ErrCode = 51XX2 // 出仓成品信息不存在
func (FpmOutOrderItem) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmOutOrderItemNotExist
}

func (FpmOutOrderItem) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmOutOrderItemAlreadyExist
}

func (r FpmOutOrderItem) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func NewFpmOutOrderItem(
	ctx context.Context,
	p *structure.AddFpmOutOrderItemParam,
) (r FpmOutOrderItem) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.SumStockId = p.SumStockId
	r.QuoteOrderNo = p.QuoteOrderNo
	r.QuoteOrderItemId = p.QuoteOrderItemId
	r.ProductCode = p.ProductCode
	r.ProductName = p.ProductName
	r.CustomerId = p.CustomerId
	r.ProductColorId = p.ProductColorId
	r.ProductId = p.ProductId
	r.ProductColorCode = p.ProductColorCode
	r.ProductColorName = p.ProductColorName
	r.ProductLevelId = p.ProductLevelId
	r.ProductWidth = p.ProductWidth
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductGramWeight = p.ProductGramWeight
	r.ProductRemark = p.ProductRemark
	r.ProductCraft = p.ProductCraft
	r.ProductIngredient = p.ProductIngredient
	r.OutRoll = p.OutRoll
	r.SumStockId = p.SumStockId
	r.TotalWeight = p.TotalWeight
	r.WeightError = p.WeightError
	r.PaperTubeWeight = p.PaperTubeWeight
	r.SettleWeight = p.SettleWeight
	r.SettleLength = p.SettleLength
	r.ActuallyWeight = p.ActuallyWeight
	r.SettleErrorWeight = p.SettleErrorWeight
	r.UnitId = p.UnitId
	r.AuxiliaryUnitId = p.AuxiliaryUnitId
	r.UnitPrice = p.UnitPrice
	r.OutLength = p.OutLength
	r.LengthUnitPrice = p.LengthUnitPrice
	r.OtherPrice = p.OtherPrice
	r.TotalPrice = p.TotalPrice
	r.Remark = p.Remark
	r.ArrangeItemId = p.ArrangeItemId
	r.SalePlanOrderItemId = p.SalePlanOrderItemId
	r.SumStockRoll = p.SumStockRoll
	r.SumStockWeight = p.SumStockWeight
	r.SumStockLength = p.SumStockLength
	r.FpmInOrderItemId = p.FpmInOrderItemId
	return
}

func NewFpmProcessOutOrderItem(
	ctx context.Context,
	p *structure.AddFpmProcessOutOrderItemParam,
) (r FpmOutOrderItem) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ParentId = p.ParentId
	r.ParentOrderNo = p.ParentOrderNo
	r.QuoteOrderNo = p.QuoteOrderNo
	r.QuoteOrderItemId = p.QuoteOrderItemId
	r.ProductId = p.ProductId
	r.ProductCode = p.ProductCode
	r.ProductName = p.ProductName
	r.CustomerId = p.CustomerId
	r.ProductColorId = p.ProductColorId
	r.ProductColorCode = p.ProductColorCode
	r.ProductColorName = p.ProductColorName
	r.ProductLevelId = p.ProductLevelId
	r.ProductWidth = p.ProductWidth
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductGramWeight = p.ProductGramWeight
	r.ProductRemark = p.ProductRemark
	r.ProductCraft = p.ProductCraft
	r.ProductIngredient = p.ProductIngredient
	r.OutRoll = p.OutRoll
	r.SumStockId = p.SumStockId
	r.TotalWeight = p.TotalWeight
	r.WeightError = p.WeightError
	r.SettleWeight = p.SettleWeight
	r.UnitId = p.UnitId
	r.OutLength = p.OutLength
	r.Remark = p.Remark
	r.ReturnRoll = p.ReturnRoll
	r.ReturnWeight = p.ReturnWeight
	r.ReturnLength = p.ReturnLength
	r.DyeRoll = p.DyeRoll
	r.DyeWeight = p.DyeWeight
	r.ArrangeItemId = p.ArrangeItemId
	r.SumStockRoll = p.SumStockRoll
	r.SumStockWeight = p.SumStockWeight
	r.SumStockLength = p.SumStockLength
	return
}

func (m *FpmOutOrderItem) UpdateRollAndWeight(param [2]int, isAdd bool) (err error) {
	if isAdd {
		m.DyeRoll += param[0]
		m.DyeWeight += param[1]

	}
	if !isAdd {
		m.DyeRoll -= param[0]
		m.DyeWeight -= param[1]
	}

	if m.DyeWeight > m.TotalWeight {
		err = errors.NewCustomError(errors.ErrCodeMysqlUpdate, " 超过可返数量")
		return
	}
	if m.DyeWeight < 0 {
		err = errors.NewCustomError(errors.ErrCodeMysqlUpdate, " 超过可返数量")
		return
	}
	return
}
