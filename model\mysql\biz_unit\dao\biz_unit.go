package dao

import (
	"context"
	"hcscm/common/errors"
	"hcscm/middleware"
	mysql "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/mysql_base"

	"gorm.io/gorm"
)

func (d *Dao) UpdateBizUnit(ctx context.Context, po *mysql.BizUnit) error {
	// code数据库自增 更新忽略
	// status单独更新
	d.db.WithContext(ctx).Omit("code", "status").Updates(po)
	return d.db.Select("is_blacklist").WithContext(ctx).Omit("code", "status").Updates(po).Error
}

func (d *Dao) UpdateMultiBizUnitStatus(ctx context.Context, ids []uint64, status int) error {
	po := &mysql.BizUnit{
		Status: status,
	}
	return d.db.WithContext(ctx).Where("id in (?)", ids).Updates(po).Error
}

func (d *Dao) DeleteBizUnit(ctx context.Context, ids []uint64) error {
	return d.db.WithContext(ctx).Where("id in (?)", ids).Delete(&mysql.BizUnit{}).Error
}

func (d *Dao) QueryBizUnitByUnq(ctx context.Context, id uint64, category int, name, phone, customCode string) (*mysql.BizUnit, error) {
	var err error
	var po mysql.BizUnit
	db := d.db.WithContext(ctx).Where("id!=? and category=?", id, category)
	db2 := d.db.WithContext(ctx).Where("id!=? and category=?", id, category)
	db3 := d.db.WithContext(ctx).Where("id!=? and category=?", id, category)
	// 名称
	err = db.Where("name = ?", name).First(&po).Error
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}
	if po.Id > 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOperate, "名称重复"))
		return nil, err
	}

	// 编号
	err = db2.Where("custom_code = ?", customCode).First(&po).Error
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}
	if po.Id > 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOperate, "编号重复"))
		return nil, err
	}

	// 电话
	if phone != "" {
		err = db3.Where("phone = ?", phone).First(&po).Error
		if err != nil {
			if err != gorm.ErrRecordNotFound {
				return nil, err
			}
		}
		if po.Id > 0 {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOperate, "电话重复"))
			return nil, err
		}
	}

	// db = db.Where("name=? or phone=? or custom_code=?", name, phone, customCode)
	return &po, nil
}

func (d *Dao) QueryBizUnitByUnqSimple(ctx context.Context, id uint64, category int, name, phone, customCode string) (*mysql.BizUnit, error) {
	var err error
	var po mysql.BizUnit
	db := d.db.WithContext(ctx).Where("id!=? and category=?", id, category)
	db2 := d.db.WithContext(ctx).Where("id!=? and category=?", id, category)

	// 名称
	err = db.Where("name = ?", name).First(&po).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	if po.Id > 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOperate, "名称重复"))
		return nil, err
	}
	// 编号
	err = db2.Where("custom_code = ?", customCode).First(&po).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	if po.Id > 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOperate, "编号重复"))
		return nil, err
	}

	return &po, nil
}

func (d *Dao) QueryBizUnit(ctx context.Context, id uint64) (*mysql.BizUnit, error) {
	var po mysql.BizUnit
	db := d.db.WithContext(ctx).Where("id=?", id)
	if err := db.First(&po).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &po, nil
}

func (d *Dao) QueryBizUnitByPhone(ctx context.Context, category int, phone string) (*mysql.BizUnit, bool, error) {
	var po mysql.BizUnit
	db := d.db.WithContext(ctx).Where("category = ? and phone= ?", category, phone)
	if err := db.First(&po).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, false, nil
		}
		return nil, false, err
	}
	return &po, true, nil
}

func (d *Dao) QueryMultiBizUnit(ctx context.Context, ids []uint64) ([]*mysql.BizUnit, error) {
	var po []*mysql.BizUnit
	db := d.db.WithContext(ctx).Where("id in (?)", ids)
	if err := db.Find(&po).Error; err != nil {
		return nil, err
	}
	return po, nil
}

func (d *Dao) QueryMultiBizUnitList(ctx context.Context) ([]*mysql.BizUnit, error) {
	var po []*mysql.BizUnit
	db := d.db.WithContext(ctx)
	if err := db.Find(&po).Error; err != nil {
		return nil, err
	}
	return po, nil
}

func (d *Dao) QueryMultiBizUnitByNameLike(ctx context.Context, name string) ([]*mysql.BizUnit, error) {
	var po []*mysql.BizUnit
	db := d.db.WithContext(ctx).Where("name like ?", "%"+name+"%")
	if err := db.Find(&po).Error; err != nil {
		return nil, err
	}
	return po, nil
}

func (d *Dao) QueryBizUnitByName(ctx context.Context, name []string) ([]*mysql.BizUnit, error) {
	var po []*mysql.BizUnit
	db := d.db.WithContext(ctx).Where("name in (?)", name).Where("delete_time = '0000-00-00 00:00:00'")
	if err := db.Find(&po).Error; err != nil {
		return nil, err
	}
	return po, nil
}

func (d *Dao) QueryBizUnitIdsByCategoryAndType(ctx context.Context, ids []uint64, category uint8, status uint8) ([]uint64, error) {
	var res []uint64
	db := d.db.Model(&mysql.BizUnit{}).Scopes(mysql_base.Undel())
	db = db.Where("category = ?", category)
	if len(ids) > 0 {
		db = db.Where("id in (?)", ids)
	}
	if status > 0 {
		db = db.Where("status = ?", status)
	}
	if err := db.Select("id").Scan(&res).Error; err != nil {
		return nil, err
	}
	return res, nil
}

func (d *Dao) UpdateBizUnitLocation(ctx context.Context, tx *mysql_base.Tx, bizUnitId uint64, location string) error {
	db := tx.DB.WithContext(ctx)
	return db.Model(&mysql.BizUnit{}).
		Where("id = ?", bizUnitId).
		Update("location", location).
		Error
}

func MustFirstBizUnitByID(tx *mysql_base.Tx, id uint64) (r mysql.BizUnit, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func MustUpdateBizUnit(tx *mysql_base.Tx, r mysql.BizUnit) (o mysql.BizUnit, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}
