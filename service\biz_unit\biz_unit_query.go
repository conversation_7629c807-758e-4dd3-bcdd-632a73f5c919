package biz_unit

import (
	"context"
	"hcscm/aggs/biz_unit"
	"hcscm/aggs/qywx"
	app_svc "hcscm/api/wx/app"
	app "hcscm/api/wx/app/v1"
	common_basic "hcscm/common/basic_data"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/domain/biz_unit/entity"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/employee"
	"hcscm/extern/pb/sale_system"
	mysql "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/mysql_base"
	qywxModel "hcscm/model/mysql/qywx"
	structure "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"strings"
)

type BizUnitQueryService struct {
	saleGroupQueryService *SaleGroupQueryService
	saleAreaQueryService  *SaleAreaQueryService
}

func NewBizUnitQueryService() *BizUnitQueryService {
	return &BizUnitQueryService{
		saleGroupQueryService: NewSaleGroupQueryService(),
		saleAreaQueryService:  NewSaleAreaQueryService(),
	}
}

func (s *BizUnitQueryService) GetBizUnitByIds(ctx context.Context, tx *mysql_base.Tx, ids []uint64) ([]*mysql.BizUnit, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	bizUnitList, err := repo.QueryMultiBizUnit(ctx, ids)
	if err != nil {
		return nil, err
	}
	return bizUnitList, nil
}

func (s *BizUnitQueryService) QueryBizUnitNameByIds(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (map[uint64]string, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	bizUnitList, err := repo.QueryMultiBizUnit(ctx, ids)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64]string)
	for _, item := range bizUnitList {
		m[item.Id] = item.Name
	}
	return m, nil
}

func (s *BizUnitQueryService) QueryBizUnitByIds(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (map[uint64][2]string, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)

	repo := biz_unit.NewRepo(tx)
	bizUnitList, err := repo.QueryMultiBizUnit(ctx, ids)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64][2]string)
	for _, item := range bizUnitList {
		m[item.Id] = [2]string{item.CustomCode, item.Name}
	}
	return m, nil
}

func (s *BizUnitQueryService) QueryBizUnitIdsByNameLike(ctx context.Context, tx *mysql_base.Tx, name string) ([]uint64, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	bizUnitList, err := repo.QueryMultiBizUnitByNameLike(ctx, name)
	if err != nil {
		return nil, err
	}
	return mysql.GetIds(bizUnitList), nil
}

func (s *BizUnitQueryService) QueryBizUnitByName(ctx context.Context, tx *mysql_base.Tx, name []string) ([]*structure.GetBizUnitListItem, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var (
		saleSystemRelList mysql.BizUnitSaleSystemRelList
		repo              = biz_unit.NewRepo(tx)
	)

	list, err := repo.QueryMultiBizUnitByName(ctx, name)
	if err != nil {
		return nil, err
	}
	unitIds := mysql.GetIds(list)
	saleSystemRelList, err = repo.QueryMultiBizUnitSaleSystem(ctx, unitIds)
	if err != nil {
		return nil, err
	}

	var (
		saleGroupMap   map[uint64]string
		saleAreaMap    map[uint64]string
		employeeMap    map[uint64][2]string
		saleSystemMap  map[uint64]string
		unitSaleMap    map[uint64]*mysql.BizUnitSale
		unitTypeMap    map[uint64]string
		unitTypeRelMap = make(map[uint64][]uint64)
	)

	saleSystemIds := set.NewUint64Set()
	mainUnitTypeIds := set.NewUint64Set()
	for _, item := range list {
		saleSystemIds.Add(item.SaleSystemId)
		mainUnitTypeIds.Add(item.MainUnitTypeId)
	}

	for _, item := range saleSystemRelList {
		saleSystemIds.Add(item.SaleSystemId)
	}

	g := errgroup.WithCancel(ctx)

	// 获取销售信息
	g.Go(func(ctx context.Context) error {
		saleList, err := repo.QueryMultiBizUnitSale(ctx, unitIds)
		if err != nil {
			return err
		}
		unitSaleMap = mysql.GetUnitIdSaleMap(saleList)

		saleGroupIds := set.NewUint64Set()
		saleAreaIds := set.NewUint64Set()
		employeeIds := set.NewUint64Set()
		for _, item := range saleList {
			saleGroupIds.Add(item.SaleGroupID)
			saleAreaIds.Add(item.SaleAreaID)
			employeeIds.Add(item.SellerID)
			employeeIds.Add(item.OrderFollowerID)
			employeeIds.Add(item.OrderQcUserId)
		}
		// 获取销售群体信息
		saleGroupMap, _ = s.saleGroupQueryService.QuerySaleGroupNameByIds(ctx, tx, saleGroupIds.List())
		// 获取销售群体信息
		saleAreaMap, _ = s.saleAreaQueryService.QuerySaleAreaNameByIds(ctx, tx, saleAreaIds.List())
		// 获取销售员信息
		employeeMap, _ = employee.NewClientEmployeeService().GetEmployeeNamePhoneByIds(ctx, employeeIds.List())
		return nil
	})

	// 销售系统
	g.Go(func(ctx context.Context) error {
		saleSystemMap, _ = sale_system.NewSaleSystemClient().GetSaleSystemByIds(ctx, saleSystemIds.List())
		return nil
	})

	// 单位类型
	g.Go(func(ctx context.Context) error {
		unitTypeList, err := repo.QueryMultiBizUnitTypeRel(ctx, unitIds)
		if err != nil {
			return err
		}
		unitTypeIds := set.NewUint64Set()
		for _, item := range unitTypeList {
			unitTypeIds.Add(item.UnitTypeId)
			unitTypeRelMap[item.UnitId] = append(unitTypeRelMap[item.UnitId], item.UnitTypeId)
		}
		unitTypeIds.AddList(mainUnitTypeIds.List())
		unitTypeMap, _ = type_basic_data.NewTypeIntercourseUnitsClient().GetTypeIntercourseUnitsNameByIds(ctx, unitTypeIds.List())
		return nil
	})

	// 企业微信客户
	g.Go(func(ctx context.Context) error {

		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, err
	}

	var data = make([]*structure.GetBizUnitListItem, 0, len(list))
	for _, item := range list {
		var (
			_saleSystemNames []string
			_saleSystemIds   []uint64
		)
		_saleSystemRelList := saleSystemRelList.PickByUnitId(item.Id)
		for _, _saleSystemRel := range _saleSystemRelList {
			_saleSystemNames = append(_saleSystemNames, saleSystemMap[_saleSystemRel.SaleSystemId])
			_saleSystemIds = append(_saleSystemIds, _saleSystemRel.SaleSystemId)
		}
		dataItem := &structure.GetBizUnitListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			CategoryName: entity.CategoryMap[item.Category],
			BizUnitInfo: structure.BizUnitInfo{
				Name:            item.Name,
				FullName:        item.FullName,
				Code:            item.Code,
				CustomCode:      item.CustomCode,
				UnitTypeID:      unitTypeRelMap[item.Id],
				SaleSystemID:    item.SaleSystemId,
				SaleSystemIds:   _saleSystemIds,
				Phone:           item.Phone,
				Address:         item.Address,
				ContactName:     item.ContactName,
				Email:           item.Email,
				Remark:          item.Remark,
				SettleType:      item.SettleType,
				SettleCycle:     item.SettleCycle,
				CreditLimit:     item.CreditLimit,
				CreditLevel:     item.CreditLevel,
				SellerID:        0,
				OrderFollowerID: 0,
				OrderQcUserId:   0,
				SaleAreaID:      0,
				SaleGroupID:     0,
				CreditCode:      item.CreditCode,
				MainUnitTypeId:  item.MainUnitTypeId,
			},
			UnitTypeName:     tools.GetStringByListAndMap(unitTypeRelMap[item.Id], unitTypeMap),
			MainUnitTypeName: unitTypeMap[item.MainUnitTypeId],
			SaleSystemName:   saleSystemMap[item.SaleSystemId],
			SaleSystemNames:  strings.Join(_saleSystemNames, ","),
			Status:           item.Status,
			IsBlacklist:      item.IsBlacklist,
			IsBlacklistName: func() string {
				if item.IsBlacklist {
					return "是"
				}
				if !item.IsBlacklist {
					return "否"
				}
				return "否"
			}(),
			SettleTypeName:    common.SettleTypeMap()[item.SettleType],
			CreditLevelName:   entity.CreditLevelMap[item.CreditLevel],
			DnfChargingMethod: item.DnfChargingMethod,
		}
		if saleInfo := unitSaleMap[item.Id]; saleInfo != nil {
			dataItem.SaleAreaID = saleInfo.SaleAreaID
			dataItem.SaleAreaName = saleAreaMap[saleInfo.SaleAreaID]
			dataItem.SaleGroupID = saleInfo.SaleGroupID
			dataItem.SaleGroupName = saleGroupMap[saleInfo.SaleGroupID]
			dataItem.SellerID = saleInfo.SellerID
			dataItem.SellerName = employeeMap[saleInfo.SellerID][0]
			dataItem.OrderFollowerID = saleInfo.OrderFollowerID
			dataItem.OrderFollowerName = employeeMap[saleInfo.OrderFollowerID][0]
			dataItem.OrderFollowerPhone = employeeMap[saleInfo.OrderFollowerID][1]
			dataItem.OrderQcUserId = saleInfo.OrderQcUserId
			dataItem.OrderQcUserName = employeeMap[saleInfo.OrderQcUserId][0]
		}
		data = append(data, dataItem)
	}
	return data, nil
}

func (s *BizUnitQueryService) QueryBizUnitTypeById(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (map[uint64][]uint64, error) {
	var (
		unitTypeId = set.NewUint64Set()
		bTypeList  map[uint64]type_basic_data.GetTypeIntercourseUnitsData
	)
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	bizUnitTypeList, err := repo.QueryMultiBizUnitTypeRel(ctx, ids)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64][]uint64)
	// 根据UnitTypeId 查询
	for _, rel := range bizUnitTypeList {
		unitTypeId.Add(rel.UnitTypeId)
	}
	if len(unitTypeId.List()) > 0 {
		bTypeList, err = type_basic_data.NewTypeIntercourseUnitsClient().GetTypeIntercourseUnitsMapByIds(ctx, unitTypeId.List())
		if err != nil {
			return nil, err
		}
	}
	for _, item := range bizUnitTypeList {
		if val, ok := bTypeList[item.UnitTypeId]; ok {
			m[item.UnitId] = append(m[item.UnitId], uint64(val.Type))
		}
	}
	return m, nil
}

func (s *BizUnitQueryService) QueryBizUnitTypeByIdSingle(ctx context.Context, tx *mysql_base.Tx, id uint64) ([]uint64, error) {
	typeIds := []uint64{}
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	rels, err := repo.QueryBizUnitTypeRel(ctx, id)
	if err != nil {
		return typeIds, err
	}
	for _, rel := range rels {
		typeIds = append(typeIds, rel.UnitTypeId)
	}
	return typeIds, nil
}

func (s *BizUnitQueryService) QuerySettleTypeById(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (map[uint64][2]int, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	bizUnitList, err := repo.QueryMultiBizUnit(ctx, ids)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64][2]int)
	for _, item := range bizUnitList {
		m[item.Id] = [2]int{item.SettleType, item.SettleCycle}
	}
	return m, nil
}

// QueryBizUnitDnfChargingMethod 查询供应商的染费收费方式
func (s *BizUnitQueryService) QueryBizUnitDnfChargingMethod(ctx context.Context, tx *mysql_base.Tx, id uint64) (int, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	po, err := repo.QueryBizUnit(ctx, id)
	if err != nil {
		return 0, err
	}
	if po == nil {
		return 0, po.GetNotExistError()
	}
	return po.DnfChargingMethod, nil
}

// 根据加工厂id查找加工厂名称
func (s *BizUnitQueryService) QueryBizUnitFactoryLogisticsById(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (map[uint64]string, error) {
	m := make(map[uint64]string)
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	po, err := repo.QueryBizUnitFactoryLogisticsByIds(ctx, ids)
	if err != nil {
		return m, err
	}
	for _, logistics := range po {
		m[logistics.Id] = logistics.Name
	}
	return m, nil
}

// 获取收货单位名N、联系人M、收货电话P、收货地址A (NMPA)
func (s *BizUnitQueryService) QueryBizUnitNMPAById(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (map[uint64][4]string, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	bizUnitList, err := repo.QueryMultiBizUnit(ctx, ids)
	if err != nil {
		return nil, err
	}
	m := make(map[uint64][4]string)
	for _, item := range bizUnitList {
		m[item.Id] = [4]string{item.Name, item.ContactName, item.Phone, item.Address}
	}
	return m, nil
}

// 列表
func (s *BizUnitQueryService) QueryBizUnitList(ctx context.Context, tx *mysql_base.Tx, req *structure.GetBizUnitListParams) ([]*structure.GetBizUnitListItem, int, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var (
		repo = biz_unit.NewRepo(tx)
	)

	// if !req.BizUnitType.IsNil() {
	//	 // 根据type 获取id
	//	bizUnitIds, err := type_basic_data.NewTypeIntercourseUnitsClient().GetTypeIntercourseUnitsIDByBizUnitType(ctx, req.BizUnitType.ToUint64())
	//	if err != nil {
	//		return nil, 0, nil
	//	}
	//	req.UnitTypeId = bizUnitIds
	// }

	// 获取列表
	list, total, err := repo.QueryBizUnitList(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	if total == 0 {
		return nil, total, nil
	}

	unitIds := mysql.GetIds(list)
	saleSystemRelList, err := repo.QueryMultiBizUnitSaleSystem(ctx, unitIds)
	if err != nil {
		return nil, 0, err
	}

	var (
		saleGroupMap      map[uint64]string
		saleAreaMap       map[uint64]string
		employeeMap       map[uint64][2]string
		saleSystemMap     map[uint64]string
		unitSaleMap       map[uint64]*mysql.BizUnitSale
		unitTypeMap       map[uint64]string
		unitTypeRelMap    = make(map[uint64][]uint64)
		qywxGroupChatRels qywxModel.QYWXGroupChatRelList
	)

	saleSystemIds := set.NewUint64Set()
	mainUnitTypeIds := set.NewUint64Set()
	for _, item := range list {
		saleSystemIds.Add(item.SaleSystemId)
		mainUnitTypeIds.Add(item.MainUnitTypeId)
	}

	for _, item := range saleSystemRelList {
		saleSystemIds.Add(item.SaleSystemId)
	}

	userSaleSystemIds := metadata.GetLoginInfo(ctx).GetSaleSystemIds()
	saleSystemIds.AddList(userSaleSystemIds)

	g := errgroup.WithCancel(ctx)

	// 获取销售信息
	g.Go(func(ctx context.Context) error {
		saleList, err := repo.QueryMultiBizUnitSale(ctx, unitIds)
		if err != nil {
			return err
		}
		unitSaleMap = mysql.GetUnitIdSaleMap(saleList)

		saleGroupIds := set.NewUint64Set()
		saleAreaIds := set.NewUint64Set()
		employeeIds := set.NewUint64Set()
		for _, item := range saleList {
			saleGroupIds.Add(item.SaleGroupID)
			saleAreaIds.Add(item.SaleAreaID)
			employeeIds.Add(item.SellerID)
			employeeIds.Add(item.OrderFollowerID)
			employeeIds.Add(item.OrderQcUserId)
		}
		// 获取销售群体信息
		saleGroupMap, _ = s.saleGroupQueryService.QuerySaleGroupNameByIds(ctx, tx, saleGroupIds.List())
		// 获取销售群体信息
		saleAreaMap, _ = s.saleAreaQueryService.QuerySaleAreaNameByIds(ctx, tx, saleAreaIds.List())
		// 获取销售员信息
		employeeMap, _ = employee.NewClientEmployeeService().GetEmployeeNamePhoneByIds(ctx, employeeIds.List())
		return nil
	})

	// 销售系统
	g.Go(func(ctx context.Context) error {
		saleSystemMap, _ = sale_system.NewSaleSystemClient().GetSaleSystemByIds(ctx, saleSystemIds.List())
		return nil
	})

	// 单位类型
	g.Go(func(ctx context.Context) error {
		unitTypeList, err := repo.QueryMultiBizUnitTypeRel(ctx, unitIds)
		if err != nil {
			return err
		}
		unitTypeIds := set.NewUint64Set()
		for _, item := range unitTypeList {
			unitTypeIds.Add(item.UnitTypeId)
			unitTypeRelMap[item.UnitId] = append(unitTypeRelMap[item.UnitId], item.UnitTypeId)
		}
		unitTypeIds.AddList(mainUnitTypeIds.List())
		unitTypeMap, _ = type_basic_data.NewTypeIntercourseUnitsClient().GetTypeIntercourseUnitsNameByIds(ctx, unitTypeIds.List())
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	var qywxCustomerRels = &app.GetBindCustomersReply{}
	qywxCustomerRels, _ = app_svc.GetAppClient(ctx).GetBindCustomers(ctx, &app.GetBindCustomersRequest{
		BizUnitIds: mysql_base.GetUInt64List(list, "biz_unit_id"),
	})

	groupChatRelRepo := qywx.NewQYWXGroupChatRelRepo(tx)
	qywxGroupChatRels, err = groupChatRelRepo.GetQYWXGroupChatRelByBizUnitIDs(mysql_base.GetUInt64List(list, "biz_unit_id"))
	if err != nil {
		return nil, 0, err
	}

	var data = make([]*structure.GetBizUnitListItem, 0, len(list))
	for _, item := range list {
		var (
			_saleSystemNames []string
			_saleSystemIds   []uint64
			qywxCustomers    []structure.QYWXCustomer
			qywxGroupChats   []structure.QYWXGroupChat
		)
		_saleSystemRelList := saleSystemRelList.PickByUnitId(item.Id)
		for _, _saleSystemRel := range _saleSystemRelList {
			if req.IsNeedChooseSaleSystem {
				if !tools.UInt64Contains(_saleSystemRel.SaleSystemId, userSaleSystemIds...) {
					continue
				}
			}
			_saleSystemNames = append(_saleSystemNames, saleSystemMap[_saleSystemRel.SaleSystemId])
			_saleSystemIds = append(_saleSystemIds, _saleSystemRel.SaleSystemId)
		}
		if ids, ok := unitTypeRelMap[item.Id]; ok {
			if len(ids) >= 1 {
				if item.MainUnitTypeId == 0 {
					item.MainUnitTypeId = ids[0]
				}
			}
		}

		// 获取企微客户信息
		if qywxCustomerRels != nil {
			for _, _qywxCustomerRels := range qywxCustomerRels.List {
				if _qywxCustomerRels.BizUnitId == item.Id {
					for _, customerRel := range _qywxCustomerRels.BindCustomers {
						qywxCustomers = append(qywxCustomers, structure.QYWXCustomer{
							ID:   customerRel.ExternalUserId,
							Type: customerRel.ExternalTypeName,
							Name: customerRel.Name,
						})
					}
				}
			}
		}

		for _, groupChatRel := range qywxGroupChatRels.PickByBizUnitID(item.Id) {
			qywxGroupChats = append(qywxGroupChats, structure.QYWXGroupChat{
				ID:             groupChatRel.QYWXGroupChatID,
				NotifyType:     groupChatRel.NotifyType,
				NotifyTypeName: groupChatRel.NotifyType.String(),
				Name:           groupChatRel.QYWXGroupChatName,
			})
		}
		dataItem := &structure.GetBizUnitListItem{
			RecordData: structure.RecordData{
				Id:             item.Id,
				CreateTime:     tools.MyTime(item.CreateTime),
				UpdateTime:     tools.MyTime(item.UpdateTime),
				CreatorId:      item.CreatorId,
				CreatorName:    item.CreatorName,
				UpdaterId:      item.UpdaterId,
				UpdateUserName: item.UpdaterName,
			},
			CategoryName: entity.CategoryMap[item.Category],
			BizUnitInfo: structure.BizUnitInfo{
				Name:            item.Name,
				FullName:        item.FullName,
				PinYin:          item.PinYin,
				Code:            item.Code,
				CustomCode:      item.CustomCode,
				UnitTypeID:      unitTypeRelMap[item.Id],
				SaleSystemID:    item.SaleSystemId,
				SaleSystemIds:   _saleSystemIds,
				Phone:           item.Phone,
				Address:         item.Address,
				ContactName:     item.ContactName,
				Email:           item.Email,
				Remark:          item.Remark,
				SettleType:      item.SettleType,
				SettleCycle:     item.SettleCycle,
				CreditLimit:     item.CreditLimit,
				CreditLevel:     item.CreditLevel,
				SellerID:        0,
				OrderFollowerID: 0,
				OrderQcUserId:   0,
				SaleAreaID:      0,
				SaleGroupID:     0,
				CreditCode:      item.CreditCode,
				FaxNumber:       item.FaxNumber,
				MainUnitTypeId:  item.MainUnitTypeId,
				Location:        item.Location,
				QYWXCustomers:   qywxCustomers,
				QYWXGroupChats:  qywxGroupChats,
			},
			UnitTypeName:     tools.GetStringByListAndMap(unitTypeRelMap[item.Id], unitTypeMap),
			MainUnitTypeName: unitTypeMap[item.MainUnitTypeId],
			SaleSystemName:   saleSystemMap[item.SaleSystemId],
			SaleSystemNames:  strings.Join(_saleSystemNames, ","),
			Status:           item.Status,
			IsBlacklist:      item.IsBlacklist,
			IsBlacklistName: func() string {
				if item.IsBlacklist {
					return "是"
				}
				if !item.IsBlacklist {
					return "否"
				}
				return "否"
			}(),
			SettleTypeName:    common.SettleTypeMap()[item.SettleType],
			CreditLevelName:   entity.CreditLevelMap[item.CreditLevel],
			DnfChargingMethod: item.DnfChargingMethod,
		}
		if saleInfo := unitSaleMap[item.Id]; saleInfo != nil {
			dataItem.SaleAreaID = saleInfo.SaleAreaID
			dataItem.SaleAreaName = saleAreaMap[saleInfo.SaleAreaID]
			dataItem.SaleGroupID = saleInfo.SaleGroupID
			dataItem.SaleGroupName = saleGroupMap[saleInfo.SaleGroupID]
			dataItem.SellerID = saleInfo.SellerID
			dataItem.SellerName = employeeMap[saleInfo.SellerID][0]
			dataItem.OrderFollowerID = saleInfo.OrderFollowerID
			dataItem.OrderFollowerName = employeeMap[saleInfo.OrderFollowerID][0]
			dataItem.OrderFollowerPhone = employeeMap[saleInfo.OrderFollowerID][1]
			dataItem.OrderQcUserId = saleInfo.OrderQcUserId
			dataItem.OrderQcUserName = employeeMap[saleInfo.OrderQcUserId][0]
		}
		data = append(data, dataItem)
	}
	return data, total, nil
}

// 获取往来单位的一些字段(目前在填编号的时候会用到)
func (s *BizUnitQueryService) QuerySomeBizUnitFieldList(ctx context.Context, req *structure.GetBizUnitListParams) (data []*structure.GetSomeBizUnitFieldData, err error) {
	var (
		tx    = mysql_base.TransactionSlaveEx(nil, ctx, false)
		repo  = biz_unit.NewRepo(tx)
		_data = make([]*structure.GetSomeBizUnitFieldData, 0)
	)

	list, _, err := repo.QuerySomeBizUnitFieldList(tx, req)

	for _, bizUnit := range list {
		_data = append(_data, bizUnit.BuildSomeBizUnitFieldResp())
	}
	data = _data
	return
}

type QueryBizUnitDetailReq struct {
	Id uint64

	Category int
	Phone    string
}

// 详情
func (s *BizUnitQueryService) QueryBizUnitDetail(ctx context.Context, tx *mysql_base.Tx, req *QueryBizUnitDetailReq) (*structure.GetBizUnitDetailResponse, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	var (
		repo    = biz_unit.NewRepo(tx)
		bizUnit *mysql.BizUnit
		err     error
	)

	if req.Id != 0 {
		// 获取客户
		bizUnit, err = repo.QueryBizUnit(ctx, req.Id)
		if err != nil {
			return nil, err
		}
	} else {
		var exist bool
		bizUnit, exist, err = repo.QueryBizUnitByPhone(ctx, req.Category, req.Phone)
		if err != nil {
			return nil, err
		}
		if !exist {
			return nil, errors.NewError(errors.ErrCodeCustomerNotExist)
		}
	}

	if bizUnit == nil {
		return nil, errors.NewError(errors.ErrCodeCustomerNotExist)
	}

	var (
		bizUnitSale       *mysql.BizUnitSale
		unitTypeIds       []uint64
		ssIds             []uint64
		logistics         []*mysql.BizUnitFactoryLogistics
		sellerName        = ""
		orderFollowerName = ""
		orderQcUserName   = ""
		saleAreaName      = ""
		saleGroupName     = ""
		typeName          = ""
		saleSystemName    = ""
		saleSystemNames   = ""
		unitTypeMap       = make(map[uint64]type_basic_data.GetTypeIntercourseUnitsData)
		mainBizUnitType   common_basic.BizUnitType
		unitTypeNameMap   = make(map[uint64]string)
		qywxGroupChatRels qywxModel.QYWXGroupChatRelList
		qywxCustomers     []structure.QYWXCustomer
		qywxGroupChats    []structure.QYWXGroupChat
	)
	g := errgroup.WithCancel(ctx)

	// 获取类型名称
	g.Go(func(ctx context.Context) error {
		unitTypeList, err := repo.QueryBizUnitTypeRel(ctx, bizUnit.Id)
		if err != nil {
			return err
		}
		unitTypeIds = make([]uint64, 0, len(unitTypeList))
		for _, item := range unitTypeList {
			// 主要往来单位类型为空的时候，默认为第一个
			if bizUnit.MainUnitTypeId == 0 {
				bizUnit.MainUnitTypeId = item.UnitTypeId
			}
			unitTypeIds = append(unitTypeIds, item.UnitTypeId)
		}
		unitTypeMap, _ = type_basic_data.NewTypeIntercourseUnitsClient().GetTypeIntercourseUnitsMapList(ctx, &type_basic_data.GetTypeIntercourseUnitsListQuery{Ids: append(unitTypeIds, bizUnit.MainUnitTypeId)})
		for u, data := range unitTypeMap {
			unitTypeNameMap[u] = data.Name
		}
		typeName = tools.GetStringByListAndMap(unitTypeIds, unitTypeNameMap)
		return nil
	})

	// 获取客户的销售信息
	g.Go(func(ctx context.Context) error {
		var err error
		// 获取销售信息
		bizUnitSale, err = repo.QueryBizUnitSale(ctx, bizUnit.Id)
		if err != nil {
			return err
		}

		if bizUnitSale == nil {
			return nil
		}
		// 获取销售员和跟单员名称
		employeeIds := make([]uint64, 0)
		if bizUnitSale.OrderFollowerID > 0 {
			employeeIds = append(employeeIds, bizUnitSale.OrderFollowerID)
		}
		if bizUnitSale.SellerID > 0 {
			employeeIds = append(employeeIds, bizUnitSale.SellerID)
		}
		if bizUnitSale.OrderQcUserId > 0 {
			employeeIds = append(employeeIds, bizUnitSale.OrderQcUserId)
		}
		if len(employeeIds) > 0 {
			employeeMap, _ := employee.NewClientEmployeeService().GetEmployeeNameByIds(ctx, employeeIds)
			sellerName = employeeMap[bizUnitSale.SellerID]
			orderFollowerName = employeeMap[bizUnitSale.OrderFollowerID]
			orderQcUserName = employeeMap[bizUnitSale.OrderQcUserId]
		}

		// 获取销售区域名
		if bizUnitSale.SaleAreaID > 0 {
			saleArea, _ := s.saleAreaQueryService.QuerySaleArea(ctx, tx, bizUnitSale.SaleAreaID)
			saleAreaName = saleArea.GetName()
		}

		// 获取销售群体名
		if bizUnitSale.SaleGroupID > 0 {
			saleGroup, _ := s.saleGroupQueryService.QuerySaleGroup(ctx, tx, bizUnitSale.SaleGroupID)
			saleGroupName = saleGroup.GetName()
		}
		return nil
	})

	// 获取工厂物流信息
	g.Go(func(ctx context.Context) error {
		var err error
		logistics, err = repo.QueryBizUnitFactoryLogistics(ctx, bizUnit.Id)
		if err != nil {
			return err
		}
		return nil
	})

	// 获取营销体系名称
	g.Go(func(ctx context.Context) error {
		var err error
		ssRel, err := repo.QueryBizUnitSaleSystem(ctx, bizUnit.Id)
		if err != nil {
			return err
		}
		for _, item := range ssRel {
			ssIds = append(ssIds, item.SaleSystemId)
		}

		saleSystemService := sale_system.NewSaleSystemClient()
		saleSystemMap, _ := saleSystemService.GetSaleSystemByIds(ctx, ssIds)
		saleSystemName = saleSystemMap[bizUnit.SaleSystemId]
		saleSystemNames = tools.GetStringByListAndMap(ssIds, saleSystemMap)
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, err
	}

	mainBizUnitType = unitTypeMap[bizUnit.MainUnitTypeId].Type

	var qywxCustomerRels = &app.GetBindCustomersReply{}
	qywxCustomerRels, _ = app_svc.GetAppClient(ctx).GetBindCustomers(ctx, &app.GetBindCustomersRequest{
		BizUnitIds: []uint64{bizUnit.Id},
	})
	// 获取企微客户信息
	if qywxCustomerRels != nil {
		for _, _qywxCustomerRels := range qywxCustomerRels.List {
			if _qywxCustomerRels.BizUnitId == bizUnit.Id {
				for _, customerRel := range _qywxCustomerRels.BindCustomers {
					qywxCustomers = append(qywxCustomers, structure.QYWXCustomer{
						ID:   customerRel.ExternalUserId,
						Type: customerRel.ExternalTypeName,
						Name: customerRel.Name,
					})
				}
			}
		}
	}

	groupChatRelRepo := qywx.NewQYWXGroupChatRelRepo(tx)
	qywxGroupChatRels, err = groupChatRelRepo.GetQYWXGroupChatRelByBizUnitIDs([]uint64{bizUnit.Id})
	if err != nil {
		return nil, err
	}
	for _, groupChatRel := range qywxGroupChatRels {
		qywxGroupChats = append(qywxGroupChats, structure.QYWXGroupChat{
			ID:             groupChatRel.QYWXGroupChatID,
			NotifyType:     groupChatRel.NotifyType,
			NotifyTypeName: groupChatRel.NotifyType.String(),
			Name:           groupChatRel.QYWXGroupChatName,
		})
	}
	data := &structure.GetBizUnitDetailResponse{
		Id: bizUnit.Id,
		BizUnitInfo: structure.BizUnitInfo{
			Location:        bizUnit.Location,
			Name:            bizUnit.Name,
			FullName:        bizUnit.FullName,
			Code:            bizUnit.Code,
			CustomCode:      bizUnit.CustomCode,
			UnitTypeID:      unitTypeIds,
			SaleSystemID:    bizUnit.SaleSystemId,
			SaleSystemIds:   ssIds,
			Phone:           bizUnit.Phone,
			Address:         bizUnit.Address,
			ContactName:     bizUnit.ContactName,
			Email:           bizUnit.Email,
			Remark:          bizUnit.Remark,
			SettleType:      bizUnit.SettleType,
			SettleCycle:     bizUnit.SettleCycle,
			CreditLimit:     bizUnit.CreditLimit,
			CreditLevel:     bizUnit.CreditLevel,
			SellerID:        0,
			OrderFollowerID: 0,
			OrderQcUserId:   0,
			SaleAreaID:      0,
			SaleGroupID:     0,
			CreditCode:      bizUnit.CreditCode,
			FaxNumber:       bizUnit.FaxNumber,
			Category:        bizUnit.Category,
			MainUnitTypeId:  bizUnit.MainUnitTypeId,
			QYWXCustomers:   qywxCustomers,
			QYWXGroupChats:  qywxGroupChats,
		},
		IsBlacklist:           bizUnit.IsBlacklist,
		BlankFabricMin:        0,
		BlankFabricMax:        0,
		FactoryLogistics:      buildBizUnitFactoryLogisticsItem(logistics),
		UnitTypeName:          typeName,
		MainUnitTypeName:      unitTypeNameMap[bizUnit.MainUnitTypeId],
		MainBizUnitType:       mainBizUnitType,
		SaleSystemName:        saleSystemName,
		SaleSystemNames:       saleSystemNames,
		SellerName:            sellerName,
		OrderFollowerName:     orderFollowerName,
		OrderQcUserName:       orderQcUserName,
		SaleAreaName:          saleAreaName,
		SaleGroupName:         saleGroupName,
		SettleTypeName:        common.SettleTypeMap()[bizUnit.SettleType],
		CreditLevelName:       entity.CreditLevelMap[bizUnit.CreditLevel],
		DnfChargingMethod:     bizUnit.DnfChargingMethod,
		DnfChargingMethodName: entity.DnfChargingMethodMap[bizUnit.DnfChargingMethod],
	}
	if bizUnitSale != nil {
		data.SellerID = bizUnitSale.SellerID
		data.OrderFollowerID = bizUnitSale.OrderFollowerID
		data.OrderQcUserId = bizUnitSale.OrderQcUserId
		data.SaleAreaID = bizUnitSale.SaleAreaID
		data.SaleGroupID = bizUnitSale.SaleGroupID
		data.BlankFabricMin = bizUnitSale.BlankFabricMin
		data.BlankFabricMax = bizUnitSale.BlankFabricMax
	}
	return data, nil
}

func buildBizUnitFactoryLogisticsItem(logistics []*mysql.BizUnitFactoryLogistics) []*structure.BizUnitFactoryLogisticsItem {
	data := make([]*structure.BizUnitFactoryLogisticsItem, 0, len(logistics))
	for _, item := range logistics {
		data = append(data, &structure.BizUnitFactoryLogisticsItem{
			Name:             item.Name,
			ContactName:      item.ContactName,
			Phone:            item.Phone,
			Location:         item.Location,
			Address:          item.Address,
			PrintTag:         item.PrintTag,
			IsDefault:        item.IsDefault,
			LogisticsArea:    item.LogisticsArea,
			LogisticsCompany: item.LogisticsCompany,
		})
	}
	return data
}

// 获取有效的染整厂id：ids传则过滤，不传不过滤
func (s *BizUnitQueryService) QueryEnableBizUnitIdsByCategoryAndType(ctx context.Context, tx *mysql_base.Tx, ids []uint64, category uint8, unitTypeId uint64) ([]uint64, error) {
	tx = mysql_base.TransactionSlaveEx(tx, ctx, false)
	repo := biz_unit.NewRepo(tx)
	// 查询染整厂类型的单位类型
	unitTypeIds, err := type_basic_data.NewTypeIntercourseUnitsClient().GetTypeIntercourseUnitsIDByBizUnitType(ctx, []uint64{unitTypeId})
	if err != nil {
		return nil, err
	}
	if len(unitTypeIds) <= 0 {
		return nil, nil
	}

	bizUnitIds, err := repo.QueryEnableBizUnitIdsByCategoryAndType(ctx, ids, category, unitTypeIds)
	if err != nil {
		return nil, err
	}
	return bizUnitIds, nil
}

// BizUnitAddSaleGroup 添加客户到销售群体
func (s *BizUnitQueryService) BizUnitAddSaleGroup(ctx context.Context) (res structure.BizUnitAddSaleGroupRes, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		repo = biz_unit.NewRepo(tx)
	)
	res, err = repo.BizUnitAddSaleGroup(ctx, tx)
	if err != nil {
		return
	}
	return
}

// BizUnitLocationWash 清洗客户地区数据
func (s *BizUnitQueryService) BizUnitLocationWash(ctx context.Context, q *structure.BizUnitLocationWashQuery) (res structure.ResponseData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	var (
		bizUnits []*entity.BizUnit
		count    int
	)
	repo := biz_unit.NewRepo(tx)
	bizUnits, err = repo.GetMultiBizUnits(ctx)
	if err != nil {
		return
	}

	// 查询出所有的省、市、区数据

	// 根据level来进行关系绑定，省绑定市，市绑定县

	// 查询出重名的省市区进行特殊处理，后续识别出是只有这个重名的，但是没有其他信息的则可能需要查询三方接口查具体详细的位置，查不到则不处理

	// 城市与省份的绑定关系
	cityToProvince := map[string]string{
		"佛山":   "广东省",
		"广州":   "广东省",
		"深圳":   "广东省",
		"东莞":   "广东省",
		"中山":   "广东省",
		"珠海":   "广东省",
		"惠州":   "广东省",
		"江门":   "广东省",
		"汕头":   "广东省",
		"湛江":   "广东省",
		"肇庆":   "广东省",
		"茂名":   "广东省",
		"梅州":   "广东省",
		"清远":   "广东省",
		"韶关":   "广东省",
		"潮州":   "广东省",
		"揭阳":   "广东省",
		"云浮":   "广东省",
		"汕尾":   "广东省",
		"河源":   "广东省",
		"阳江":   "广东省",
		"上海":   "上海市",
		"北京":   "北京市",
		"天津":   "天津市",
		"重庆":   "重庆市",
		"杭州":   "浙江省",
		"宁波":   "浙江省",
		"温州":   "浙江省",
		"南京":   "江苏省",
		"苏州":   "江苏省",
		"无锡":   "江苏省",
		"合肥":   "安徽省",
		"武汉":   "湖北省",
		"长沙":   "湖南省",
		"成都":   "四川省",
		"西安":   "陕西省",
		"郑州":   "河南省",
		"济南":   "山东省",
		"青岛":   "山东省",
		"沈阳":   "辽宁省",
		"大连":   "辽宁省",
		"哈尔滨":  "黑龙江省",
		"长春":   "吉林省",
		"南宁":   "广西壮族自治区",
		"昆明":   "云南省",
		"贵阳":   "贵州省",
		"福州":   "福建省",
		"厦门":   "福建省",
		"南昌":   "江西省",
		"石家庄":  "河北省",
		"太原":   "山西省",
		"兰州":   "甘肃省",
		"西宁":   "青海省",
		"银川":   "宁夏回族自治区",
		"呼和浩特": "内蒙古自治区",
		"乌鲁木齐": "新疆维吾尔自治区",
		"拉萨":   "西藏自治区",
		"海口":   "海南省",
		"三亚":   "海南省",
		"赣州":   "江西省",
		"九江":   "江西省",
		"晋江":   "福建省",
		"泉州":   "福建省",
	}

	// 区县与城市的绑定关系
	districtToCity := map[string]string{
		"禅城区":  "佛山市",
		"禅城":   "佛山市",
		"南海区":  "佛山市",
		"南海":   "佛山市",
		"狮山":   "佛山市", // 添加狮山，属于南海区
		"顺德区":  "佛山市",
		"顺德":   "佛山市",
		"三水区":  "佛山市",
		"三水":   "佛山市",
		"高明区":  "佛山市",
		"高明":   "佛山市",
		"番禺区":  "广州市",
		"番禺":   "广州市",
		"海珠区":  "广州市",
		"海珠":   "广州市",
		"天河区":  "广州市",
		"天河":   "广州市",
		"白云区":  "广州市",
		"白云":   "广州市",
		"黄埔区":  "广州市",
		"黄埔":   "广州市",
		"荔湾区":  "广州市",
		"荔湾":   "广州市",
		"越秀区":  "广州市",
		"越秀":   "广州市",
		"花都区":  "广州市",
		"花都":   "广州市",
		"南沙区":  "广州市",
		"南沙":   "广州市",
		"从化区":  "广州市",
		"从化":   "广州市",
		"增城区":  "广州市",
		"增城":   "广州市",
		"松江区":  "上海市",
		"松江":   "上海市",
		"浦东新区": "上海市",
		"浦东":   "上海市",
		"黄浦区":  "上海市",
		"黄浦":   "上海市",
		"徐汇区":  "上海市",
		"徐汇":   "上海市",
		"长宁区":  "上海市",
		"长宁":   "上海市",
		"静安区":  "上海市",
		"静安":   "上海市",
		"普陀区":  "上海市",
		"普陀":   "上海市",
		"虹口区":  "上海市",
		"虹口":   "上海市",
		"杨浦区":  "上海市",
		"杨浦":   "上海市",
		"闵行区":  "上海市",
		"闵行":   "上海市",
		"宝山区":  "上海市",
		"宝山":   "上海市",
		"嘉定区":  "上海市",
		"嘉定":   "上海市",
		"金山区":  "上海市",
		"金山":   "上海市",
		"青浦区":  "上海市",
		"青浦":   "上海市",
		"奉贤区":  "上海市",
		"奉贤":   "上海市",
		"崇明区":  "上海市",
		"崇明":   "上海市",
	}

	// 特殊地点标记
	specialLocations := map[string]string{
		"碧桂园":   "广东省/佛山市/禅城区",
		"森林公园":  "广东省/佛山市/三水区",
		"纺织厂":   "广东省/佛山市/南海区",
		"火炬园":   "广东省/佛山市/禅城区",
		"佛山海口村": "广东省/佛山市/南海区",
		"博士后基地": "广东省/佛山市/南海区",
		"狮山":    "广东省/佛山市/南海区",
	}

	// 需要排除的地址模式（避免错误识别）
	excludePatterns := []string{
		"佛山海口",
		"佛山市海口",
	}

	for _, bizUnit := range bizUnits {
		if bizUnit.Address == "" {
			continue
		}

		var location string
		var province, city, district string
		var skipCityExtraction bool = false
		// 先检查特殊地点标记
		for marker, loc := range specialLocations {
			if strings.Contains(bizUnit.Address, marker) {
				location = loc
				skipCityExtraction = true
				break
			}
		}

		// 检查是否匹配排除模式
		if !skipCityExtraction {
			for _, pattern := range excludePatterns {
				if strings.Contains(bizUnit.Address, pattern) {
					// 对于佛山海口这样的地址，直接设置为佛山
					province = "广东省"
					city = "佛山市"
					skipCityExtraction = true
					break
				}
			}
		}

		// 如果没有匹配到特殊地点，尝试提取省市区
		if location == "" {
			// 尝试从地址中提取区县
			for d, c := range districtToCity {
				if strings.Contains(bizUnit.Address, d) {
					district = d
					// 标准化区县名称
					if !strings.HasSuffix(district, "区") && !strings.HasSuffix(district, "县") &&
						!strings.HasSuffix(district, "市") && !strings.HasSuffix(district, "镇") {
						district = district + "区"
					}
					city = c
					if p, ok := cityToProvince[strings.TrimSuffix(city, "市")]; ok {
						province = p
					}
					skipCityExtraction = true
					break
				}
			}

			// 如果没有找到区县，且不在排除列表中，尝试提取城市
			if district == "" && !skipCityExtraction {
				for c, p := range cityToProvince {
					if strings.Contains(bizUnit.Address, c) {
						city = c + "市"
						province = p
						break
					}
				}
			}

			// 组合省市区
			if province != "" {
				location = province
				if city != "" {
					location += "," + city
					if district != "" {
						location += "," + district
					}
				}
			}
		}
		// 如果提取到了地址信息，更新数据库
		if location != "" && location != bizUnit.Location {
			err = repo.UpdateBizUnitLocation(ctx, tx, bizUnit.Id, location)
			if err != nil {
				return
			}
			count++
		}
	}
	return res, nil
}

// BizUnitDefaultAddressWash 清洗客户默认地址
func (s *BizUnitQueryService) BizUnitDefaultAddressWash(ctx context.Context, req *structure.BizUnitLocationWashQuery) (res structure.ResponseData, err error) {
	// 开启事务
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()

	var (
		bizUnits                    []*entity.BizUnit
		bizUnitFactoryLogisticss    structure.GetBizUnitFactoryLogisticsListDataList
		repo                        = biz_unit.NewRepo(tx)
		bizUnitFactoryLogisticsRepo = biz_unit.NewBizUnitFactoryLogisticsRepo(tx)
	)
	// 查找客户
	bizUnits, err = repo.GetMultiBizUnits(ctx)
	if err != nil {
		return
	}
	// 查找客户默认地址
	bizUnitFactoryLogisticss, err = bizUnitFactoryLogisticsRepo.FindAllDefault(ctx, structure.AddBizUnitFactoryLogisticsParam{})
	if err != nil {
		return
	}

	for _, bizUnit := range bizUnits {
		// 判断客户联系人和省市区是否为空
		// if bizUnit.ContactName == "" {
		//
		// }
		// 判断如果客户没有默认地址则取客户的联系人，手机号码，省市区，详细地址创建一个默认地址
		bizUnitFactoryLogistics := bizUnitFactoryLogisticss.PickByBizUnitId(bizUnit.Id)
		if bizUnitFactoryLogistics.Id == 0 {
			_, err = bizUnitFactoryLogisticsRepo.Create(ctx, structure.AddBizUnitFactoryLogisticsParam{
				BizUintID:   bizUnit.Id,
				ContactName: bizUnit.ContactName,
				Phone:       bizUnit.Phone,
				Location:    bizUnit.Location,
				Address:     bizUnit.Address,
				IsDefault:   true,
			})
			if err != nil {
				return
			}
		}
	}

	return res, err
}
