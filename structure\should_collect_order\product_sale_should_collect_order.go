package should_collect_order

import (
	commonAi "hcscm/common/ai"
	"hcscm/common/sale"
	common_sale "hcscm/common/sale"
	common "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddProductSaleShouldCollectOrderParam struct {
	structure_base.Param
	Id             uint64                                          `json:"id"`               // 出仓需要取消送货单id(不传)
	SrcId          uint64                                          `json:"src_id"`           // 来源单ID(1配布单 2退货进仓单 3原料销售、退货单 4坯布销售、退货单)
	SrcOrderNo     string                                          `json:"src_order_no"`     // 来源单号
	SrcOrderType   common.SrcOrderType                             `json:"-"`                // 来源单类型
	SaleSystemId   uint64                                          `json:"sale_system_id"`   // 营销体系ID
	CustomerId     uint64                                          `json:"customer_id"`      // 所属客户id(biz_unit_id)
	OrderRemark    string                                          `json:"order_remark"`     // 内部备注(单据备注)
	CancelRemark   string                                          `json:"cancel_remark"`    // 作废备注(作废时使用)
	OrderTime      tools.QueryTime                                 `json:"order_time"`       // 单据日期(送货日期)
	SaleUserId     uint64                                          `json:"sale_user_id"`     // 销售员id
	SaleFollowerId uint64                                          `json:"sale_follower_id"` // 销售跟单员id
	SettleType     common_system.SettleType                        `json:"settle_type"`      // 结算类型
	CustomCycle    int                                             `json:"custom_cycle"`     // 结算天数或月份或周期
	VoucherNumber  string                                          `json:"voucher_number"`   // 凭证单号
	SaleOrderID    uint64                                          `json:"sale_order_id"`    // 销售单id
	SaleOrderNo    string                                          `json:"sale_order_no"`    // 销售单号
	Roll           int                                             `json:"-"`                // 总匹数
	Weight         int                                             `json:"-"`                // 总数量
	AuditStatus    common_system.OrderStatus                       `json:"-"`                //
	TaxRate        int                                             `json:"tax_rate"`         // 税率
	IsWithTaxRate  bool                                            `json:"is_with_tax_rate"` // 单价是否含税
	ItemData       AddProductSaleShouldCollectOrderDetailParamList `json:"items"`            // 应收单详情信息
	SaleMode       common_sale.SaleOrderType                       `json:"sale_mode"`        // 订单类型 1大货 2剪板 3客订大货 4客订剪板

	OffsetPrice    int `json:"offset_price"`    // 优惠金额
	DiscountPrice  int `json:"discount_price"`  // 折扣金额
	ReducePrice    int `json:"reduce_price"`    // 扣款金额
	CollectedMoney int `json:"collected_money"` // 已收金额

	TotalSettleMoney        int `json:"total_settle_price"`         // 总销售金额
	TotalShouldCollectMoney int `json:"total_should_collect_money"` // 总应收金额
	TotalUnCollectMoney     int `json:"total_un_collect_money"`     // 总未收金额
}

func (r *AddProductSaleShouldCollectOrderParam) Adjust() {
	r.Roll = 0
	r.Weight = 0
	for _, item := range r.ItemData {
		r.Roll += item.Roll
		r.Weight += item.SettleWeight
	}
}

type AddProductSaleShouldCollectOrderParamList []*AddProductSaleShouldCollectOrderParam

func (r AddProductSaleShouldCollectOrderParamList) GetIds() (ids []uint64) {
	for _, item := range r {
		ids = append(ids, item.Id)
	}
	return
}

type UpdateProductSaleShouldCollectOrderParam struct {
	structure_base.Param
	Id             uint64                                          `json:"id"`
	VoucherNumber  string                                          `json:"voucher_number"`   // 凭证单号
	OrderTime      tools.QueryTime                                 `json:"order_time"`       // 单据日期(送货日期)
	SaleUserId     uint64                                          `json:"sale_user_id"`     // 销售员id
	SaleFollowerId uint64                                          `json:"sale_follower_id"` // 销售跟单员id
	OrderRemark    string                                          `json:"order_remark"`     // 内部备注(单据备注)
	SettleType     common_system.SettleType                        `json:"settle_type"`      // 结算类型
	Roll           int                                             `json:"-"`                // 总匹数
	Weight         int                                             `json:"-"`                // 总数量
	TaxRate        int                                             `json:"tax_rate"`         // 税率
	IsWithTaxRate  bool                                            `json:"is_with_tax_rate"` // 单价是否含税
	ItemData       AddProductSaleShouldCollectOrderDetailParamList `json:"items"`            // 应收单详情信息
	SaleMode       common_sale.SaleOrderType                       `json:"sale_mode"`        // 订单类型 1大货 2剪板 3客订大货 4客订剪板
}

func (r *UpdateProductSaleShouldCollectOrderParam) Adjust() {

}

type UpdateProductSaleShouldCollectOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateMergeProductSaleShouldCollectOrderParam struct {
	structure_base.Param
	Id             uint64   `json:"id"`                // 销售送货单id
	IDs            []uint64 `json:"ids"`               // 合并ids
	SaleOutOrderNo string   `json:"sale_out_order_no"` // 销售出仓单号
	SaleOutOrderId uint64   `json:"sale_out_order_id"` // 销售出仓单ID
}

type DeleteProductSaleShouldCollectOrderParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteProductSaleShouldCollectOrderData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type UpdateProductSaleShouldCollectOrderAuditStatusParam struct {
	structure_base.Param
	Id           tools.QueryIntList `json:"id"`
	IDs          []uint64           `json:"ids"`
	CancelRemark string             `json:"cancel_remark"`
}

type UpdateProductSaleShouldCollectOrderAuditStatusData struct {
	structure_base.ResponseData
}

type UpdateProductSaleShouldCollectOrderBusinessCloseData struct {
	structure_base.ResponseData
}

type GetProductSaleShouldCollectOrderQuery struct {
	structure_base.Query
	Id            uint64 `form:"id"`              // id
	DetailID      uint64 `form:"detail_id"`       // 详情id
	SrcId         uint64 `form:"src_id"`          // 来源id
	WithAllWeight bool   `form:"with_all_weight"` // 是否带上所有细码
	OrderType     int    `form:"order_type"`      // 订单类型(1销售调货单 2退货调货单)
}

func (p GetProductSaleShouldCollectOrderQuery) Validate() (err error) {
	return err
}

type GetProductSaleShouldCollectOrderListQuery struct {
	structure_base.ListQuery
	OrderNo              string                      `form:"order_no"`                // 应收单号
	CollectType          common.CollectType          `form:"collect_type"`            // 应收单类型 1成品 2成品退款 3原料 4坯布 5其他
	SrcOrderNo           string                      `form:"src_order_no"`            // 配布单号
	SaleOrderNo          string                      `form:"sale_order_no"`           // 销售单号
	SaleSystemId         uint64                      `form:"sale_system_id"`          // 营销体系ID
	CustomerId           uint64                      `form:"customer_id"`             // 所属客户id(biz_unit_id)
	AuditStatus          tools.QueryIntList          `form:"audit_status"`            // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId         uint64                      `form:"department_id"`           // 下单用户所属部门
	AuditorId            uint64                      `form:"auditor_id"`              // 审核人ID
	StartAuditDate       tools.QueryTime             `form:"start_audit_date"`        // 开始审核时间
	EndAuditDate         tools.QueryTime             `form:"end_audit_date"`          // 结束审核时间
	BusinessClose        common_system.BusinessClose `form:"business_close"`          // 业务关闭
	BusinessCloseUserId  uint64                      `form:"business_close_user_id"`  // 业务关闭操作人
	StartOrderTime       tools.QueryTime             `form:"start_order_time"`        // 开始单据日期(送货日期)
	EndOrderTime         tools.QueryTime             `form:"end_order_time"`          // 结束单据日期(送货日期)
	StartCreateTime      tools.QueryTime             `form:"start_create_time"`       // 开始创建时间
	EndCreateTime        tools.QueryTime             `form:"end_create_time"`         // 结束创建时间
	UpdaterID            uint64                      `form:"updater_id"`              // 修改人(用户id)
	StartUpdateTime      tools.QueryTime             `form:"start_update_time"`       // 开始更新时间
	EndUpdateTime        tools.QueryTime             `form:"end_update_time"`         // 结束更新时间
	SaleUserId           uint64                      `form:"sale_user_id"`            // 销售员id
	SaleFollowerId       uint64                      `form:"sale_follower_id"`        // 销售跟单员id
	SettleType           common_system.SettleType    `form:"settle_type"`             // 结算类型
	SettleCycle          int                         `form:"settle_cycle"`            // 结算周期
	CollectStatus        common.CollectStatus        `form:"collect_status"`          // 收款状态 1未收款 2已收部分 3已收全款
	VoucherNumber        string                      `form:"voucher_number"`          // 凭证单号
	ShouldCollectOrderId uint64                      `form:"should_collect_order_id"` // 需要排除的应收单id
	IsUsefulOrder        bool                        // 有效单（非作废）
	FpmArrangeOrderIds   []uint64                    // 配布单id(销售单号查询出来)
}

func (r GetProductSaleShouldCollectOrderListQuery) Adjust() {

}

type GetProductSaleShouldCollectOrderListData struct {
	structure_base.RecordData
	OrderNo                 string                      `json:"order_no" excel:"应收单号"`                   // 应收单号
	CollectType             common.CollectType          `json:"collect_type"`                            // 应收单类型
	CollectTypeName         string                      `json:"collect_type_name" excel:"应收单类型"`         // 应收单类型名称
	SrcOrderType            common.SrcOrderType         `json:"src_order_type"`                          // 来源单类型
	SrcOrderTypeName        string                      `json:"src_order_type_name" excel:"来源单类型"`       // 来源单类型名
	SrcId                   uint64                      `json:"src_id"`                                  // 来源单ID(1配布单 2退货进仓单 3原料销售、退货单 4坯布销售、退货单)
	SrcName                 string                      `json:"src_name" excel:"来源单"`                    // 来源单ID(1配布单 2退货进仓单 3原料销售、退货单 4坯布销售、退货单)名称
	SrcOrderNo              string                      `json:"src_order_no" excel:"来源单号"`               // 来源单号
	SaleSystemId            uint64                      `json:"sale_system_id"`                          // 营销体系ID
	SaleSystemName          string                      `json:"sale_system_name" excel:"营销体系"`           // 营销体系ID名称
	CustomerId              uint64                      `json:"customer_id"`                             // 所属客户id
	CustomerCode            string                      `json:"customer_code"`                           // 所属客户编号
	CustomerName            string                      `json:"customer_name" excel:"所属客户"`              // 所属客户名称
	InternalRemark          string                      `json:"internal_remark" excel:"内部备注"`            // 内部备注
	SendProductRemark       string                      `json:"send_product_remark" excel:"出货备注"`        // 出货备注
	OrderRemark             string                      `json:"order_remark" excel:"单据备注"`               // 单据备注
	CancelRemark            string                      `json:"cancel_remark"`                           // 作废备注(作废时使用)
	AuditStatus             common_system.OrderStatus   `json:"audit_status"`                            // 订单状态
	AuditStatusName         string                      `json:"audit_status_name" excel:"订单状态"`          // 订单状态名称
	AuditorId               uint64                      `json:"auditor_id"`                              // 审核人ID
	AuditorName             string                      `json:"auditor_name"`                            // 审核人ID名称
	AuditDate               tools.MyTime                `json:"audit_date"`                              // 审核时间
	BusinessClose           common_system.BusinessClose `json:"business_close"`                          // 业务关闭
	BusinessCloseName       string                      `json:"business_close_name"`                     // 业务关闭
	BusinessCloseUserId     uint64                      `json:"business_close_user_id"`                  // 业务关闭操作人
	BusinessCloseUserName   string                      `json:"business_close_user_name"`                // 业务关闭操作人名称
	BusinessCloseTime       tools.MyTime                `json:"business_close_time"`                     // 业务关闭时间
	OrderTime               tools.MyTime                `json:"order_time" excel:"单据日期"`                 // 单据日期(送货日期)
	SaleUserId              uint64                      `json:"sale_user_id"`                            // 销售员id
	SaleUserName            string                      `json:"sale_user_name" excel:"销售员"`              // 销售员名称
	SaleFollowerId          uint64                      `json:"sale_follower_id"`                        // 销售跟单员id
	SaleFollowerName        string                      `json:"sale_follower_name" excel:"销售跟单员"`        // 销售跟单员名称
	SettleType              common_system.SettleType    `json:"settle_type"`                             // 结算类型
	SettleTypeName          string                      `json:"settle_type_name" excel:"结算类型"`           // 结算类型
	CustomCycle             int                         `json:"custom_cycle" excel:"结算天数或月份或周期"`         // 结算天数或月份或周期
	CollectStatus           common.CollectStatus        `json:"collect_status"`                          // 收款状态 1未收款 2已收部分 3已收全款
	CollectStatusName       string                      `json:"collect_status_name" excel:"收款状态"`        // 收款状态 1未收款 2已收部分 3已收全款
	VoucherNumber           string                      `json:"voucher_number" excel:"凭证单号"`             // 凭证单号
	WarehouseOutRemark      string                      `json:"warehouse_out_remark" excel:"出仓备注"`       // 出仓备注
	ProcessFactoryId        uint64                      `json:"process_factory_id"`                      // 加工厂id
	ProcessFactoryName      string                      `json:"process_factory_name" excel:"加工厂"`        // 加工厂名称
	Roll                    tools.Hundred               `json:"roll" excel:"匹数"`                         // 匹数
	Weight                  tools.Milligram             `json:"weight" excel:"数量"`                       // 数量
	TotalSaleMoney          tools.Cent                  `json:"total_sale_money" excel:"销售金额"`           // 销售金额
	TotalShouldCollectMoney tools.Cent                  `json:"total_should_collect_money" excel:"应收金额"` // 应收金额（产品缺陷，这个才是真正的应收金额）
	SaleMode                common_sale.SaleOrderType   `json:"sale_mode"`                               // 订单类型 1大货 2剪板 3客订大货 4客订剪板
	SaleModeName            string                      `json:"sale_mode_name"`                          // 订单类型
}

type GetProductSaleShouldCollectOrderListDataList []GetProductSaleShouldCollectOrderListData

func (r GetProductSaleShouldCollectOrderListDataList) Adjust() {

}

type GetProductSaleShouldCollectOrderData struct {
	structure_base.RecordData
	OrderNo               string                                         `json:"order_no"`                 // 应收单号
	CollectType           common.CollectType                             `json:"collect_type"`             // 应收单类型
	CollectTypeName       string                                         `json:"collect_type_name"`        // 应收单类型名称
	SrcOrderType          common.SrcOrderType                            `json:"src_order_type"`           // 来源单类型
	SrcOrderTypeName      string                                         `json:"src_order_type_name"`      // 来源单类型名
	SrcId                 uint64                                         `json:"src_id"`                   // 来源单ID(1配布单 2退货进仓单 3原料销售、退货单 4坯布销售、退货单)
	SrcOrderNo            string                                         `json:"src_order_no"`             // 来源单号
	SaleSystemId          uint64                                         `json:"sale_system_id"`           // 营销体系ID
	SaleSystemName        string                                         `json:"sale_system_name"`         // 营销体系名称
	SaleSystemAddr        string                                         `json:"sale_system_addr"`         // 营销体系地址
	SaleSystemContacts    string                                         `json:"sale_system_contacts"`     // 营销体系联系人
	SaleSystemPhone       string                                         `json:"sale_system_phone"`        // 营销体系电话
	CustomerId            uint64                                         `json:"customer_id"`              // 所属客户id(biz_unit_id)
	CustomerCode          string                                         `json:"customer_code"`            // 所属客户编号
	CustomerName          string                                         `json:"customer_name"`            // 所属客户名称
	CustomerFullName      string                                         `json:"customer_full_name"`       // 所属客户全称
	InternalRemark        string                                         `json:"internal_remark"`          // 内部备注
	SendProductRemark     string                                         `json:"send_product_remark"`      // 出货备注
	OrderRemark           string                                         `json:"order_remark"`             // 单据备注
	CancelRemark          string                                         `json:"cancel_remark"`            // 作废备注(作废时使用)
	AuditStatus           common_system.OrderStatus                      `json:"audit_status"`             // 订单状态
	AuditStatusName       string                                         `json:"audit_status_name"`        // 订单状态名称
	DepartmentId          uint64                                         `json:"department_id"`            // 下单用户所属部门
	DepartmentName        string                                         `json:"department_name"`          // 下单用户所属部门名称
	CompanyId             uint64                                         `json:"company_id"`               // 公司ID
	CompanyName           string                                         `json:"company_name"`             // 公司ID名称
	AuditorId             uint64                                         `json:"auditor_id"`               // 审核人ID
	AuditorName           string                                         `json:"auditor_name"`             // 审核人ID名称
	AuditDate             tools.MyTime                                   `json:"audit_date"`               // 审核时间
	BusinessClose         common_system.BusinessClose                    `json:"business_close"`           // 业务关闭
	BusinessCloseName     string                                         `json:"business_close_name"`      // 业务关闭
	BusinessCloseUserId   uint64                                         `json:"business_close_user_id"`   // 业务关闭操作人
	BusinessCloseUserName string                                         `json:"business_close_user_name"` // 业务关闭操作人名称
	BusinessCloseTime     tools.MyTime                                   `json:"business_close_time"`      // 业务关闭时间
	OrderTime             tools.MyTime                                   `json:"order_time"`               // 单据日期(送货日期)
	SaleUserId            uint64                                         `json:"sale_user_id"`             // 销售员id
	SaleUserName          string                                         `json:"sale_user_name"`           // 销售员名称
	SaleFollowerId        uint64                                         `json:"sale_follower_id"`         // 销售跟单员id
	SaleFollowerName      string                                         `json:"sale_follower_name"`       // 销售跟单员名称
	SettleType            common_system.SettleType                       `json:"settle_type"`              // 结算类型
	SettleTypeName        string                                         `json:"settle_type_name"`         // 结算类型
	CustomCycle           int                                            `json:"custom_cycle"`             // 结算天数或月份或周期
	CollectStatus         common.CollectStatus                           `json:"collect_status"`           // 收款状态 1未收款 2已收部分 3已收全款
	CollectStatusName     string                                         `json:"collect_status_name"`      // 收款状态 1未收款 2已收部分 3已收全款
	VoucherNumber         string                                         `json:"voucher_number"`           // 凭证单号
	ItemData              GetProductSaleShouldCollectOrderDetailDataList `json:"items"`                    // 应收单详情信息
	ReceiveTag            string                                         `json:"receive_tag"`              // 收货标签（出货标签）
	SendProductType       sale.SendProductType                           `json:"send_product_type"`        // 出货类型 1出货 2销调
	SendProductTypeName   string                                         `json:"send_product_type_name"`   // 出货类型名称
	SaleGroupId           uint64                                         `json:"sale_group_id"`            // 销售群体id
	SaleGroupName         string                                         `json:"sale_group_name"`          // 销售群体名称
	LogisticsArea         string                                         `json:"logistics_area"`           // 物流区域
	PostageItems          sale.PostageItems                              `json:"postage_items"`            // 邮费项目 1包邮 2不包邮
	PostageItemsName      string                                         `json:"postage_items_name"`       // 邮费项目名称
	ProcessFactoryId      uint64                                         `json:"process_factory_id"`       // 加工厂id
	ProcessFactoryName    string                                         `json:"process_factory_name"`     // 加工厂名称
	Contacts              string                                         `json:"contacts"`                 // 联系人
	ReceiveAddr           string                                         `json:"receive_addr"`             // 收货地址
	ReceivePhone          string                                         `json:"receive_phone"`            // 收货电话
	LogisticsCompanyId    uint64                                         `json:"logistics_company_id"`     // 物流公司id
	LogisticsCompanyName  string                                         `json:"logistics_company_name"`   // 物流公司名称
	Roll                  int                                            `json:"roll"`                     // 匹数
	Weight                int                                            `json:"weight"`                   // 数量
	TotalSaleMoney        int                                            `json:"total_sale_money"`         // 销售金额
	TaxRate               int                                            `json:"tax_rate"`                 // 税率
	IsWithTaxRate         bool                                           `json:"is_with_tax_rate"`         // 单价是否含税
	SaleMode              common_sale.SaleOrderType                      `json:"sale_mode"`                // 订单类型 1大货 2剪板 3客订大货 4客订剪板

	TotalShouldCollectMoney int    `json:"total_should_collect_money"` // 应收金额（产品缺陷，这个才是真正的应收金额）
	SaleSystemFaxNumber     string `json:"sale_system_fax_number"`     // 传真号
	MeasurementUnitName     string `json:"measurement_unit_name"`      // 单位

	TotalSettleMoney     int                      `json:"total_settle_money"`     // 应收金额(结算金额之和)
	TotalCollectedMoney  int                      `json:"total_collected_money"`  // 已收金额
	TotalRemoveMoney     int                      `json:"total_remove_money"`     // 优惠金额(抹零)
	TotalDiscountMoney   int                      `json:"total_discount_money"`   // 折扣金额
	TotalChargebackMoney int                      `json:"total_chargeback_money"` // 扣款金额
	TotalUncollectMoney  int                      `json:"total_uncollect_money"`  // 未收金额
	TotalArrearsAmount   int                      `json:"total_arrears_amount"`   // 累计欠款
	PaymentDeadline      tools.MyTime             `json:"payment_deadline"`       // 回款截止日期
	LiquidatedDay        int                      `json:"liquidated_day"`         // 逾期天数
	CollectRecords       GetCollectRecordDataList `json:"collect_records"`        // 回款记录
	SaleModeName         string                   `json:"sale_mode_name"`         // 订单类型
}

type GetProductSaleShouldCollectOrderDataForTransferDetail struct {
	Id                      uint64                    `json:"id"`
	OrderNo                 string                    `json:"order_no"`
	CollectType             common.CollectType        `json:"collect_type"`               // 应收单类型
	CollectTypeName         string                    `json:"collect_type_name"`          // 应收单类型名称
	OrderTime               tools.MyTime              `json:"order_time"`                 // 单据日期(送货日期)
	Roll                    int                       `json:"roll"`                       // 总匹数
	Weight                  int                       `json:"weight"`                     // 总数量
	Length                  int                       `json:"length"`                     // 总长度
	TotalShouldCollectMoney int                       `json:"total_should_collect_money"` // 应收金额（产品缺陷，这个才是真正的应收金额）
	SaleMode                common_sale.SaleOrderType `json:"sale_mode"`                  // 订单类型 1大货 2剪板 3客订大货 4客订剪板
	SaleModeName            string                    `json:"sale_mode_name"`             // 订单类型

	TotalSettleMoney     int                      `json:"total_settle_money"`     // 应收金额(结算金额之和)
	TotalCollectedMoney  int                      `json:"total_collected_money"`  // 已收金额
	TotalRemoveMoney     int                      `json:"total_remove_money"`     // 优惠金额(抹零)
	TotalDiscountMoney   int                      `json:"total_discount_money"`   // 折扣金额
	TotalChargebackMoney int                      `json:"total_chargeback_money"` // 扣款金额
	TotalUncollectMoney  int                      `json:"total_uncollect_money"`  // 未收金额
	TotalArrearsAmount   int                      `json:"total_arrears_amount"`   // 累计欠款
	CollectStatus        int                      `json:"collect_status"`         // 收款状态
	CollectStatusName    string                   `json:"collect_status_name"`    // 收款状态名
	WriteOffPrice        int                      `json:"write_off_price"`        // 核销金额（扣款金额 + 折扣金额 + 付款金额）
	CustomerId           uint64                   `json:"customer_id"`
	CollectRecords       GetCollectRecordDataList `json:"collect_records"` // 回款记录
}

func (g GetProductSaleShouldCollectOrderDataForTransferDetail) Adjust() {
	return
}

type GetProductSaleShouldCollectOrderDataList []GetProductSaleShouldCollectOrderData

func (g GetProductSaleShouldCollectOrderDataList) Adjust() {

}

type GetProductSaleShouldCollectOrderDropdownData struct {
	structure_base.RecordData
	OrderNo            string                    `json:"order_no"`             // 应收单号
	CollectType        common.CollectType        `json:"collect_type"`         // 应收单类型 1成品 2成品退款 3原料 4坯布 5其他
	CollectTypeName    string                    `json:"collect_type_name"`    // 应收单类型 1成品 2成品退款 3原料 4坯布 5其他
	SrcOrderType       common.SrcOrderType       `json:"src_order_type"`       // 来源单类型
	SrcOrderTypeName   string                    `json:"src_order_type_name"`  // 来源单类型名
	SrcId              uint64                    `json:"src_id"`               // 来源单ID(1配布单 2退货进仓单 3原料销售、退货单 4坯布销售、退货单)
	SrcOrderNo         string                    `json:"src_order_no"`         // 来源单号(配布单)
	SaleOrderID        uint64                    `json:"sale_order_id"`        // 销售单id
	SaleOrderNo        string                    `json:"sale_order_no"`        // 销售单号
	SaleSystemId       uint64                    `json:"sale_system_id"`       // 营销体系ID
	SaleSystemName     string                    `json:"sale_system_name"`     // 营销体系名称
	CustomerId         uint64                    `json:"customer_id"`          // 所属客户id(biz_unit_id)
	CustomerCode       string                    `json:"customer_code"`        // 所属客户编号
	CustomerName       string                    `json:"customer_name"`        // 所属客户名称
	OrderRemark        string                    `json:"order_remark"`         // 内部备注(单据备注)
	SaleUserId         uint64                    `json:"sale_user_id"`         // 销售员id
	SaleUserName       string                    `json:"sale_user_name"`       // 销售员名称
	SaleFollowerId     uint64                    `json:"sale_follower_id"`     // 销售跟单员id
	SaleFollowerName   string                    `json:"sale_follower_name"`   // 销售跟单员名称
	SettleType         common_system.SettleType  `json:"settle_type"`          // 结算类型
	SettleTypeName     string                    `json:"settle_type_name"`     // 结算类型
	CustomCycle        int                       `json:"custom_cycle"`         // 结算天数或月份或周期
	CollectStatus      common.CollectStatus      `json:"collect_status"`       // 收款状态 1未收款 2已收部分 3已收全款
	CollectStatusName  string                    `json:"collect_status_name"`  // 收款状态 1未收款 2已收部分 3已收全款
	OrderTime          tools.MyTime              `json:"order_time"`           // 单据日期(送货日期)
	ProcessFactoryId   uint64                    `json:"process_factory_id"`   // 加工厂id
	ProcessFactoryName string                    `json:"process_factory_name"` // 加工厂名称
	Contacts           string                    `json:"contacts"`             // 联系人
	ReceiveAddr        string                    `json:"receive_addr"`         // 收货地址
	ReceivePhone       string                    `json:"receive_phone"`        // 收货电话
	TotalSettleMoney   int                       `json:"total_settle_money"`   // 应收金额(结算金额之和)
	Roll               int                       `json:"roll"`                 // 匹数
	Weight             int                       `json:"weight"`               // 数量
	TotalSaleMoney     int                       `json:"total_sale_money"`     // 销售金额
	SaleMode           common_sale.SaleOrderType `json:"sale_mode"`            // 订单类型 1大货 2剪板 3客订大货 4客订剪板
	SaleModeName       string                    `json:"sale_mode_name"`       // 订单类型

	TotalShouldCollectMoney int `json:"total_should_collect_money"` // 应收金额（产品缺陷，这个才是真正的应收金额）
}

type GetProductSaleShouldCollectOrderDropdownDataList []GetProductSaleShouldCollectOrderDropdownData

func (g GetProductSaleShouldCollectOrderDropdownDataList) Adjust() {

}

type GetProductSaleProductItemListQuery struct {
	structure_base.ListQuery
	OrderNo        string `form:"order_no"`         // 应收单号
	ProductId      uint64 `form:"product_id"`       // 成品id
	ProductColorId uint64 `form:"product_color_id"` // 颜色id
	SaleSystemId   uint64 `form:"sale_system_id"`   // 营销体系ID
	CustomerId     uint64 `form:"customer_id"`      // 所属客户id(biz_unit_id)
	DyelotNumber   string `form:"dyelot_number"`    // 染厂缸号
}

type GetProductSaleProductItemDropdownData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	OrderId             uint64                   `json:"order_id"`               // 应收单id
	OrderNo             string                   `json:"order_no"`               // 应收单号
	SrcId               uint64                   `json:"src_id"`                 // 来源单ID(1配布单 2退货进仓单 3原料销售、退货单 4坯布销售、退货单)
	SrcOrderNo          string                   `json:"src_order_no"`           // 来源单号(配布单)
	SaleOrderID         uint64                   `json:"sale_order_id"`          // 销售单id
	SaleOrderNo         string                   `json:"sale_order_no"`          // 销售单号
	SaleSystemId        uint64                   `json:"sale_system_id"`         // 营销体系ID
	SaleSystemName      string                   `json:"sale_system_name"`       // 营销体系名称
	CustomerId          uint64                   `json:"customer_id"`            // 所属客户id(biz_unit_id)
	CustomerCode        string                   `json:"customer_code"`          // 所属客户编号
	CustomerName        string                   `json:"customer_name"`          // 所属客户名称
	OrderRemark         string                   `json:"order_remark"`           // 内部备注(单据备注)
	SaleUserId          uint64                   `json:"sale_user_id"`           // 销售员id
	SaleUserName        string                   `json:"sale_user_name"`         // 销售员名称
	SaleFollowerId      uint64                   `json:"sale_follower_id"`       // 销售跟单员id
	SaleFollowerName    string                   `json:"sale_follower_name"`     // 销售跟单员名称
	SettleType          common_system.SettleType `json:"settle_type"`            // 结算类型
	SettleTypeName      string                   `json:"settle_type_name"`       // 结算类型
	CustomCycle         int                      `json:"custom_cycle"`           // 结算天数或月份或周期
	ProductId           uint64                   `json:"product_id"`             // 成品id
	ProductName         string                   `json:"product_name"`           // 成品名称
	ProductCode         string                   `json:"product_code"`           // 成品编号
	ProductColorId      uint64                   `json:"product_color_id"`       // 成品颜色id
	ProductColorName    string                   `json:"product_color_name"`     // 成品颜色名称
	ProductColorCode    string                   `json:"product_color_code"`     // 成品名称
	ProductLevelId      uint64                   `json:"product_level_id"`       // 成品等级id
	ProductLevelName    string                   `json:"product_level_name"`     // 成品等级名称
	DyeFactoryColorCode string                   `json:"dye_factory_color_code"` // 染厂色号
	DyelotNumber        string                   `json:"dyelot_number"`          // 染厂缸号
	Roll                int                      `json:"roll"`                   // 可退匹数
	Length              int                      `json:"length"`                 // 可退长度
	Weight              int                      `json:"weight"`                 // 可退数量
	Remark              string                   `json:"remark"`                 // 备注(手填)
	WeightError         int                      `json:"weight_error"`           // 码单空差数量(公斤)，乘10000存
	ActuallyWeight      int                      `json:"actually_weight"`        // 码单数量(公斤)，乘10000存
	PaperTubeWeight     int                      `json:"paper_tube_weight"`      // 纸筒数量(公斤)，乘10000存
	SettleErrorWeight   int                      `json:"settle_error_weight"`    // 结算空差数量(公斤)，乘10000存
	SettleWeight        int                      `json:"settle_weight"`          // 结算数量(公斤)，乘10000存
	SalePrice           int                      `json:"sale_price"`             // 销售价
	LengthCutSalePrice  int                      `json:"length_cut_sale_price"`  // 辅助数量销售价
	ArrangeRoll         int                      `json:"arrange_roll"`           // 出仓匹数
	ArrangeLength       int                      `json:"arrange_length"`         // 出仓长度
	ArrangeWeight       int                      `json:"arrange_weight"`         // 出仓数量
	ProductCraft        string                   `json:"product_craft"`          // 成品工艺
	ProductIngredient   string                   `json:"product_ingredient"`     // 成品成分
	UnitId              uint64                   `json:"unit_id"`                // 计量单位id
	UnitName            string                   `json:"unit_name"`              // 计量单位名称
	AuxiliaryUnitId     uint64                   `json:"auxiliary_unit_id"`      // 辅助单位id
	AuxiliaryUnitName   string                   `json:"auxiliary_unit_name"`    // 辅助单位名称
}

type GetProductSaleProductItemDropdownDataList []GetProductSaleProductItemDropdownData

func (g GetProductSaleProductItemDropdownDataList) Adjust() {

}

type GetProductSaleArrangeOrderItemFcData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	Roll                   int          `json:"roll"`                      // 条数(条)，乘100存
	WarehouseId            uint64       `json:"warehouse_id"`              // 仓库id
	WarehouseBinId         uint64       `json:"warehouse_bin_id"`          // 仓号id
	VolumeNumber           int          `json:"volume_number"`             // 卷号
	WarehouseOutType       int          `json:"warehouse_out_type"`        // 出仓类型
	WarehouseOutOrderId    uint64       `json:"warehouse_out_order_id"`    // 出仓单id
	WarehouseOutOrderNo    string       `json:"warehouse_out_order_no"`    // 出仓单号
	WarehouseInType        int          `json:"warehouse_in_type"`         // 来源类型
	WarehouseInOrderId     uint64       `json:"warehouse_in_order_id"`     // 进仓单id
	WarehouseInOrderNo     string       `json:"warehouse_in_order_no"`     // 进仓单号
	ArrangeOrderNo         string       `json:"arrange_order_no"`          // 配布单号
	StockId                uint64       `json:"stock_id"`                  // 库存成品id
	SumStockId             uint64       `json:"sum_stock_id"`              // 汇总库存成品id
	BaseUnitWeight         int          `json:"base_unit_weight"`          // 基本单位数量(公斤)，乘10000存
	PaperTubeWeight        int          `json:"paper_tube_weight"`         // 纸筒数量(公斤)，乘10000存
	WeightError            int          `json:"weight_error"`              // 空差数量(公斤)，乘10000存
	ActuallyWeight         int          `json:"actually_weight"`           // 码单数量
	SettleErrorWeight      int          `json:"settle_error_weight"`       // 结算空差数量
	UnitId                 uint64       `json:"unit_id"`                   // 单位id（kg）
	Length                 int          `json:"length"`                    // 长度，乘100存
	SettleWeight           int          `json:"settle_weight"`             // 结算数量(公斤)，乘10000存
	DigitalCode            string       `json:"digital_code"`              // 数字码
	ShelfNo                string       `json:"shelf_no"`                  // 货架号
	ContractNumber         string       `json:"contract_number"`           // 合同号
	CustomerPoNum          string       `json:"customer_po_num"`           // 客户po号
	AccountNum             string       `json:"account_num"`               // 客户款号
	DyeFactoryColorCode    string       `json:"dye_factory_color_code"`    // 染厂色号
	DyeFactoryDyelotNumber string       `json:"dye_factory_dyelot_number"` // 染厂缸号
	StockRemark            string       `json:"stock_remark"`              // 库存备注
	Remark                 string       `json:"remark"`                    // 备注
	ScanUserId             uint64       `json:"scan_user_id"`              // 扫描人id
	ScanUserName           string       `json:"scan_user_name"`            // 扫描人名称
	ScanTime               tools.MyTime `json:"scan_time"`                 // 扫描时间
	// 转义
	WarehouseBinName string `json:"warehouse_bin_name"` // 仓位name
	UnitName         string `json:"unit_name"`          // 单位id
	// 240118 补充
	ProductId               uint64       `json:"product_id"`
	BarCode                 string       `json:"bar_code"`                  // 条码
	BleachName              string       `json:"bleach_name"`               // 漂染性名称
	Density                 string       `json:"density"`                   // 密度
	DyelotNumber            string       `json:"dyelot_number"`             // 缸号(统一字段，打印用)
	FinishProductCraft      string       `json:"finish_product_craft"`      // 成品工艺
	MeasurementUnitName     string       `json:"measurement_unit_name"`     // 计量单位名
	PrintDate               tools.MyTime `json:"print_date"`                // 打印日期
	ProductKindName         string       `json:"product_kind_name"`         // 布种类型名称
	QRCode                  string       `json:"qr_code"`                   // 二维码
	WeavingOrganizationName string       `json:"weaving_organization_name"` // 织造组织名称
	YarnCount               string       `json:"yarn_count"`                // 纱支
	ProductColorName        string       `json:"product_color_name"`        // 颜色
	ProductColorCode        string       `json:"product_color_code"`        // 色号
	ProductName             string       `json:"product_name"`              // 成品名
	ProductCode             string       `json:"product_code"`              // 成品编号

	OrderDetailFcId uint64 `json:"order_detail_fc_id"` // 应收细码id
}

type GetProductSaleArrangeOrderItemFcDataList []GetProductSaleArrangeOrderItemFcData

func (g GetProductSaleArrangeOrderItemFcDataList) Adjust() {

}

func (g GetProductSaleArrangeOrderItemFcDataList) GetIds() (ids []uint64) {
	for _, v := range g {
		ids = append(ids, v.Id)
	}
	return
}

type ModifyProductSaleShouldCollectOrder struct {
	Id           uint64 `json:"id" relate:"should_collect_order_detail_id"`
	Roll         int    `json:"roll"`
	Length       int    `json:"length"`
	Weight       int    `json:"weight"`
	ReturnRoll   int    `json:"return_roll"`
	ReturnLength int    `json:"return_length"`
	ReturnWeight int    `json:"return_weight"`
}

type ModifyProductSaleShouldCollectOrderList []ModifyProductSaleShouldCollectOrder

type AIAnalysisQuery struct {
	structure_base.Query
	AnalysisType commonAi.AnalysisType `json:"analysis_type"`
	StartTime    tools.QueryTime       `json:"start_time"`
	EndTime      tools.QueryTime       `json:"end_time"`
	SaleUser     string                `json:"sale_user"`
	ProductCode  string                `json:"product_code"`
	ProductName  string                `json:"product_name"`
	Location     string                `json:"location"`
	Customer     string                `json:"customer"`
}

type AIAnalysisData struct {
	structure_base.ResponseData
	AnalysisType   commonAi.AnalysisType      `json:"analysis_type"`
	OrderNum       int                        `json:"order_num"`
	TotalPrice     float64                    `json:"total_price"`
	IncreaseRate   string                     `json:"increase_rate"`
	TotalRoll      float64                    `json:"total_roll"`
	TopTenProduct  []string                   `json:"top_ten_product"`
	TopTenSaleArea []string                   `json:"top_ten_sale_area"`
	TopTenCustomer []string                   `json:"top_ten_customer"`
	SaleInfo       AIAnalysisDailySaleData    `json:"sale_info"`
	ChartType      string                     `json:"chart_type"`
	ImportantKey   AIAnalysisImportantKeyData `json:"important_key"`
	CustomerRate   string                     `json:"customer_rate"`
}

type AIAnalysisImportantKeyData struct {
	CollectedRate    string  `json:"collected_rate"`
	UncollectedPrice float64 `json:"uncollected_price"`
	NewCustomerNum   int     `json:"new_customer_num"`
}

type AIAnalysisDailySaleData struct {
	Date           []string  `json:"date"`
	TotalSalePrice []float64 `json:"total_sale_price"`
	TotalSaleRoll  []float64 `json:"total_sale_roll"`
}

type GetMatrixQuery struct {
	structure_base.Query
	StartTime    tools.QueryTime `form:"start_time"`
	EndTime      tools.QueryTime `form:"end_time"`
	CustomerName string          `form:"customer_name"`
}

type GetMatrixData struct {
	structure_base.ResponseData
	AverageProfitMargin   string                 `json:"average_profit_margin"`    // 平均利润率
	AverageSaleGrowthRate string                 `json:"average_sale_growth_rate"` // 平均销售增长率
	MaxProfitMargin       string                 `json:"max_profit_margin"`        // 最大利润率
	MinProfitMargin       string                 `json:"min_profit_margin"`        // 最小利润率
	MaxSaleGrowthRate     string                 `json:"max_sale_growth_rate"`     // 最大销售增长率
	MinSaleGrowthRate     string                 `json:"min_sale_growth_rate"`     // 最小销售增长率
	ProductMatrix         []GetProductMatrixData `json:"product_matrix"`
	CustomerMatrix        GetCustomerMatrixData  `json:"customer_matrix"`
}

type GetProductMatrixData struct {
	structure_base.ResponseData
	ProductId      uint64 `json:"product_id"`       // 面料id
	Product        string `json:"product"`          //  面料
	ProfitMargin   string `json:"profit_margin"`    // 利润率
	SaleGrowthRate string `json:"sale_growth_rate"` // 销售增长率
	DetailNum      int    `json:"detail_num"`       // 订单详情数量
}

type GetCustomerMatrixData struct {
	UnActive        int `json:"un_active"`        // 普通客户
	UnActiveGrowth  int `json:"un_active_growth"` // 普通客户增长数
	Ordinary        int `json:"ordinary"`         // 活跃低价值客户
	OrdinaryGrowth  int `json:"ordinary_growth"`  // 活跃低价值客户增长数
	Potential       int `json:"potential"`        // 潜力客户
	PotentialGrowth int `json:"potential_growth"` // 潜力客户增长数
	Core            int `json:"core"`             // 核心客户
	CoreGrowth      int `json:"core_growth"`      // 核心客户增长数
}

type GetCustomerMatrixDetailData struct {
	structure_base.ResponseData
	UnActiveList     []CustomerMatrixDetail `json:"un_active_list"`     // 普通客户
	OrdinaryList     []CustomerMatrixDetail `json:"ordinary_list"`      // 活跃低价值客户
	PotentialList    []CustomerMatrixDetail `json:"potential_list"`     // 潜力客户
	CoreList         []CustomerMatrixDetail `json:"core_list"`          // 核心客户
	AverageOrderNum  int                    `json:"average_order_num"`  // 平均订单数（购买频率）
	AverageSalePrice int                    `json:"average_sale_price"` // 平均销售金额
}

type CustomerMatrixDetail struct {
	CustomerId     uint64                    `json:"customer_id"`      // 客户id
	CustomerName   string                    `json:"customer_name"`    // 客户名称
	OrderNum       int                       `json:"order_num"`        // 购买频率
	TotalSalePrice int                       `json:"total_sale_price"` // 采购金额
	OrderTime      tools.QueryTime           `json:"order_time"`       // 最近交易日期
	Type           common.MatrixCustomerType `json:"type"`             // 客户类型
}
