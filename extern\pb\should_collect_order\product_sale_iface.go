package should_collect_order

import (
	"context"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/should_collect_order"
	mysql "hcscm/model/mysql/should_collect_order/dao"
	"hcscm/service/should_collect_order"
	structure "hcscm/structure/should_collect_order"
	structure_base "hcscm/structure/system"
)

type ProductSaleClient interface {
	GetProductSaleByIds(ctx context.Context, req ProductSaleReq) (list ProductSaleResList, err error)
	GetProductSaleItemByIds(ctx context.Context, req ProductSaleReq) (list ProductSaleItemResList, err error)
	JudgeProductSalePassByArrangeOrderItemIds(ctx context.Context, arrangeOrderItemIds []uint64) (isNoPass bool, orderNos string, err error)
	NewProductSaleShouldCollectOrder(ctx context.Context, req structure.AddProductSaleShouldCollectOrderParamList) (id map[uint64]uint64, err error)
	WaitShouldCollectOrderBySrcId(ctx context.Context, srcId uint64) (err error)
	WaitShouldCollectOrderBySrcIds(ctx context.Context, srcId []uint64) (err error)
	CancelShouldCollectOrderBySrcId(ctx context.Context, srcId uint64) (err error)
	CancelShouldCollectOrderBySrcIds(ctx context.Context, srcId []uint64) (err error)
	GetProductSaleBySrcIds(ctx context.Context, srcIDs []uint64) (list ProductSaleResList, err error)
	NewProductSaleReturnShouldCollectOrder(ctx context.Context, req structure.AddProductReturnShouldCollectOrderParam) (id uint64, err error)
	GetWaitAuditOrderCount(ctx context.Context) (count int, err error)
}

func NewProductSaleClient() ProductSaleClient {
	return &productSale{}
}

type productSale struct {
}

func (s productSale) GetProductSaleByIds(ctx context.Context, req ProductSaleReq) (list ProductSaleResList, err error) {
	// TODO implement me
	panic("implement me")
}

func (s productSale) GetProductSaleBySrcId(ctx context.Context, req ProductSaleReq) (data ProductSaleRes, err error) {
	var (
		productSaleShouldCollectOrder model.ShouldCollectOrder
		exist                         bool
	)
	if req.SrcID == 0 {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	productSaleShouldCollectOrder, exist, err = mysql.FirstShouldCollectOrderBySrcID(tx, req.SrcID)
	if err != nil {
		return
	}
	if !exist {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrder, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productSaleShouldCollectOrder)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}

	o := ProductSaleRes{}
	o.Id = productSaleShouldCollectOrder.Id
	o.OrderNo = productSaleShouldCollectOrder.OrderNo
	o.SrcId = productSaleShouldCollectOrder.SrcId
	o.SrcOrderNo = productSaleShouldCollectOrder.SrcOrderNo
	o.SaleSystemId = productSaleShouldCollectOrder.SaleSystemId
	o.SaleSystemName = saleSystem[productSaleShouldCollectOrder.SaleSystemId]
	o.CustomerId = productSaleShouldCollectOrder.CustomerId
	o.CustomerCode = bizUnit[productSaleShouldCollectOrder.CustomerId][0]
	o.CustomerName = bizUnit[productSaleShouldCollectOrder.CustomerId][1]
	o.AuditStatus = int(productSaleShouldCollectOrder.AuditStatus)
	o.AuditStatusName = productSaleShouldCollectOrder.AuditStatus.String()
	o.CollectStatus = int(productSaleShouldCollectOrder.CollectStatus)
	o.CollectStatusName = productSaleShouldCollectOrder.CollectStatus.String()
	o.Roll = productSaleShouldCollectOrder.Roll
	o.Weight = productSaleShouldCollectOrder.Weight
	data = o
	return
}

// 生成成品销售送货单
func (r productSale) NewProductSaleShouldCollectOrder(ctx context.Context, req structure.AddProductSaleShouldCollectOrderParamList) (id map[uint64]uint64, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	productSaleShouldCollectOrderSvc := should_collect_order.NewProductSaleShouldCollectOrderService(nil)
	return productSaleShouldCollectOrderSvc.Add(ctx, tx, req)
}

func (s productSale) GetProductSaleBySrcIds(ctx context.Context, srcIDs []uint64) (list ProductSaleResList, err error) {
	var (
		productSaleShouldCollectOrders model.ShouldCollectOrderList
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	productSaleShouldCollectOrders, err = mysql.FindShouldCollectOrderBySrcID(tx, srcIDs)
	if err != nil {
		return
	}

	saleSystemIds := mysql_base.GetUInt64List(productSaleShouldCollectOrders, "sale_system_id")
	saleSystemSvc := sale_system.NewSaleSystemClient()
	saleSystem, err := saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds)
	if err != nil {
		return
	}

	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", productSaleShouldCollectOrders)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnit, err := bizUnitSvc.GetBizUnitByIds(ctx, bizUnitIds)
	if err != nil {
		return
	}
	for _, order := range productSaleShouldCollectOrders {
		list = append(list, ProductSaleRes{
			Id:                order.Id,
			OrderNo:           order.OrderNo,
			CollectType:       int(order.CollectType),
			CollectTypeName:   order.CollectType.String(),
			SrcId:             order.SrcId,
			SrcOrderNo:        order.SrcOrderNo,
			SaleSystemId:      order.SaleSystemId,
			SaleSystemName:    saleSystem[order.SaleSystemId],
			CustomerId:        order.CustomerId,
			CustomerCode:      bizUnit[order.CustomerId][0],
			CustomerName:      bizUnit[order.CustomerId][1],
			AuditStatus:       int(order.AuditStatus),
			AuditStatusName:   order.AuditStatus.String(),
			CollectStatus:     int(order.CollectStatus),
			CollectStatusName: order.CollectStatus.String(),
			Roll:              order.Roll,
			Weight:            order.Weight,
		})
	}
	return
}

func (r productSale) NewProductSaleReturnShouldCollectOrder(ctx context.Context, req structure.AddProductReturnShouldCollectOrderParam) (id uint64, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	productSaleShouldCollectOrderSvc := should_collect_order.NewProductReturnShouldCollectOrderService()
	add, err := productSaleShouldCollectOrderSvc.Add(ctx, tx, &req)
	id = add
	return
}

func (s productSale) GetProductSaleItemByIds(ctx context.Context, req ProductSaleReq) (list ProductSaleItemResList, err error) {
	var (
		productSaleShouldCollectOrderDetails model.ShouldCollectOrderDetailList
		m                                    = make(ProductSaleItemResList, 0)
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	productSaleShouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByIDs(tx, req.IDs)
	if err != nil {
		return
	}

	for _, productSaleShouldCollectOrderDetail := range productSaleShouldCollectOrderDetails {
		item := ProductSaleItemRes{}
		item.Id = productSaleShouldCollectOrderDetail.Id
		item.ShouldCollectOrderId = productSaleShouldCollectOrderDetail.ShouldCollectOrderId
		item.SrcDetailId = productSaleShouldCollectOrderDetail.SrcDetailId
		item.CollectType = productSaleShouldCollectOrderDetail.CollectType
		item.CollectTypeName = productSaleShouldCollectOrderDetail.CollectType.String()
		item.ProductColorId = productSaleShouldCollectOrderDetail.ProductColorId
		item.ProductColorCode = productSaleShouldCollectOrderDetail.ProductColorCode
		item.ProductColorName = productSaleShouldCollectOrderDetail.ProductColorName
		item.DyelotNumber = productSaleShouldCollectOrderDetail.DyelotNumber
		item.Roll = productSaleShouldCollectOrderDetail.Roll
		item.Weight = productSaleShouldCollectOrderDetail.Weight
		item.StandardWeightError = productSaleShouldCollectOrderDetail.StandardWeightError
		item.OffsetWeightError = productSaleShouldCollectOrderDetail.OffsetWeightError
		item.AdjustWeightError = productSaleShouldCollectOrderDetail.AdjustWeightError
		item.WeightError = productSaleShouldCollectOrderDetail.WeightError
		item.ActuallyWeight = productSaleShouldCollectOrderDetail.ActuallyWeight
		item.SettleErrorWeight = productSaleShouldCollectOrderDetail.SettleErrorWeight
		item.SettleWeight = productSaleShouldCollectOrderDetail.SettleWeight
		item.Length = productSaleShouldCollectOrderDetail.Length
		item.StandardSalePrice = productSaleShouldCollectOrderDetail.StandardSalePrice
		item.SaleLevelId = productSaleShouldCollectOrderDetail.SaleLevelId
		item.OffsetSalePrice = productSaleShouldCollectOrderDetail.OffsetSalePrice
		item.SalePrice = productSaleShouldCollectOrderDetail.SalePrice
		item.StandardLengthCutSalePrice = productSaleShouldCollectOrderDetail.StandardLengthCutSalePrice
		item.OffsetLengthCutSalePrice = productSaleShouldCollectOrderDetail.OffsetLengthCutSalePrice
		item.LengthCutSalePrice = productSaleShouldCollectOrderDetail.LengthCutSalePrice
		item.SaleTaxRate = productSaleShouldCollectOrderDetail.SaleTaxRate
		item.OtherPrice = productSaleShouldCollectOrderDetail.OtherPrice
		item.SettlePrice = productSaleShouldCollectOrderDetail.SettlePrice
		item.Remark = productSaleShouldCollectOrderDetail.Remark
		item.NoReturnRoll = productSaleShouldCollectOrderDetail.Roll - productSaleShouldCollectOrderDetail.ReturnRoll
		item.NoReturnLength = productSaleShouldCollectOrderDetail.Length - productSaleShouldCollectOrderDetail.Weight
		if item.NoReturnLength < 0 {
			item.NoReturnLength = 0
		}
		item.NoReturnWeight = productSaleShouldCollectOrderDetail.Weight - productSaleShouldCollectOrderDetail.ReturnWeight
		m = append(m, item)
	}
	list = m
	return
}

func (s productSale) JudgeProductSalePassByArrangeOrderItemIds(ctx context.Context, arrangeOrderItemIds []uint64) (isNoPass bool, orderNos string, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	var (
		itemList model.ShouldCollectOrderDetailList
		order    model.ShouldCollectOrder
	)
	itemList, err = mysql.FindShouldCollectOrderDetailByArrangeOrderIds(tx, arrangeOrderItemIds)
	if err != nil {
		return
	}
	// 由于合并后是原单是会被作废的，所以一个出仓单只会对应到一个送货单(只循环一次就可以)
	if len(itemList) == 0 {
		middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "没有找到对应的送货单数据，本单可能存在错误"))
		return
	}
	for _, detail := range itemList {
		order, err = mysql.MustFirstShouldCollectOrderByID(tx, detail.ShouldCollectOrderId)
		if err != nil {
			return
		}
		if order.AuditStatus != common.OrderStatusAudited {
			isNoPass = true
			orderNos = order.OrderNo
			return
		}
		break
	}

	return
}

func (s productSale) WaitShouldCollectOrderBySrcId(ctx context.Context, srcId uint64) (err error) {
	var (
		productSaleShouldCollectOrder model.ShouldCollectOrder
		exist                         bool
	)
	if srcId == 0 {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	productSaleShouldCollectOrder, exist, err = mysql.FirstShouldCollectOrderBySrcID(tx, srcId)
	if err != nil {
		return
	}
	if !exist {
		return
	}

	// 判断是否收钱
	actItemList, err := mysql.FindActuallyCollectOrderItemBySrcOrderIDs(tx, []uint64{productSaleShouldCollectOrder.Id})
	if len(actItemList) > 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "已收钱，不能消审"))
		return
	}
	// 不是已审核，则跳过
	if productSaleShouldCollectOrder.AuditStatus != common.OrderStatusAudited {
		return
	}
	err = productSaleShouldCollectOrder.Wait(ctx)
	if err != nil {
		return
	}
	productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(tx, productSaleShouldCollectOrder)
	if err != nil {
		return
	}
	return
}

func (s productSale) WaitShouldCollectOrderBySrcIds(ctx context.Context, srcId []uint64) (err error) {
	var (
		productSaleShouldCollectOrders model.ShouldCollectOrderList
	)
	if len(srcId) == 0 {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	productSaleShouldCollectOrders, err = mysql.FindShouldCollectOrderBySrcID(tx, srcId)
	if err != nil {
		return
	}

	// 判断是否收钱
	actItemList, err := mysql.FindActuallyCollectOrderItemBySrcOrderIDs(tx, productSaleShouldCollectOrders.GetIds())
	if len(actItemList) > 0 {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "已收钱，不能消审"))
		return
	}
	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrders {
		// 不是已审核，则跳过
		if productSaleShouldCollectOrder.AuditStatus != common.OrderStatusAudited {
			continue
		}
		err = productSaleShouldCollectOrder.Wait(ctx)
		if err != nil {
			return
		}
		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
	}
	return
}

func (s productSale) CancelShouldCollectOrderBySrcId(ctx context.Context, srcId uint64) (err error) {
	var (
		productSaleShouldCollectOrder model.ShouldCollectOrder
		exist                         bool
	)
	if srcId == 0 {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	productSaleShouldCollectOrder, exist, err = mysql.FirstShouldCollectOrderBySrcID(tx, srcId)
	if err != nil {
		return
	}
	if !exist {
		return
	}

	err = productSaleShouldCollectOrder.Cancel(ctx)
	if err != nil {
		return
	}
	productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(tx, productSaleShouldCollectOrder)
	if err != nil {
		return
	}
	return
}
func (s productSale) CancelShouldCollectOrderBySrcIds(ctx context.Context, srcId []uint64) (err error) {
	var (
		productSaleShouldCollectOrders model.ShouldCollectOrderList
	)
	if len(srcId) == 0 {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	productSaleShouldCollectOrders, err = mysql.FindShouldCollectOrderBySrcID(tx, srcId)
	if err != nil {
		return
	}
	for _, productSaleShouldCollectOrder := range productSaleShouldCollectOrders {
		err = productSaleShouldCollectOrder.Cancel(ctx)
		if err != nil {
			return
		}
		productSaleShouldCollectOrder, err = mysql.MustUpdateShouldCollectOrder(tx, productSaleShouldCollectOrder)
		if err != nil {
			return
		}
	}
	return
}

func (s productSale) GetWaitAuditOrderCount(ctx context.Context) (count int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	_, count, err = mysql.SearchWaitAuditProductSaleShouldCollectOrder(tx, structure_base.ListQuery{})
	if err != nil {
		return
	}
	return
}
