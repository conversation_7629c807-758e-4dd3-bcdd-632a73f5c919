package metadata

import (
	common "hcscm/common/system_consts"
	"time"
)

type IOperator interface {
	GetUserId() uint64                    // 获取用户ID
	GetEmpId() uint64                     // 获取用户员工id
	GetUserName() string                  // 获取用户名称
	GetCorpID() string                    // 获取企业id
	GetTobeDevelopedAppEndpoint() string  // 获取代开发应用端点
	GetTobeDevelopedAppRobotCode() string // 获取代开发应用阿布机器人编码
	GetTenantManagementId() uint64        // 获取租户Id
	GetTenantManagementDbName() string    // 获取租户的数据库名称
	SetTenantManagement(Id uint64, corpID, tobeDevelopedAppEndpoint, tobeDevelopedAppRobotCode, TenantPhoneNumber string, TenantContacts string, TenantCompanyName string, DatabaseName string, ActivationTime time.Time, ConfigJson string, AssignPort int)
	GetTenantManagementName() string        // 获取租户Id
	GetBizUnitId() uint64                   // 获取用户往来单位ID
	GetDefaultSaleSystemId() uint64         // 获取默认营销体系ID
	GetDepartmentId() uint64                // 获取用户所属部门ID
	IsSaasCompany() bool                    // 所属公司是否为saas组织
	IsAvailable() bool                      // 用户是否可用
	GetToken() string                       // 获取用户登录token
	GetUserType() common.UserType           // 获取注册用户类型 后台注册/小程序商城注册
	GetSubDepartmentId() []uint64           // 获取用户所在部门的子部门
	GetRoleAccessIds() []uint64             // 获取用户所属角色权限列表
	GetPlatform() common.Platform           // 获取用户登录的终端
	GetLoginTime() time.Time                // 获取登录时间
	GetMenuIds() (r []uint64)               // 获取菜单ID
	GetResourceRouterNames() (r []string)   // 获取前端路由名称
	GetButtonCodes() (r []string)           // 获取前端按钮编号
	GetMPResourceRouterNames() (r []string) // 获取前端内部商城路由名称
	GetMPButtonCodes() (r []string)         // 获取前端内部商城按钮编号
	GetDataAccessScope() []byte             // 获取数据权限
	GetDataSeparate() (data []string)       // 获取数据脱敏权限
	GetMPDataSeparate() (data []string)     // 获取内部商城数据脱敏权限
	GetWarehouseIds() (r []uint64)          // 获取仓库权限
	GetSaleSystemIds() (r []uint64)         // 获取营销体系权限
	GetBizUnitIds() (r []uint64)            // 获取往来单位权限
	IsAllowUpdateOrder() (r bool)           // 是否允许更新别人的单据
	IsAllowCancelOther() (r bool)           // 是否允许作废别人的单据
	IsAllowAuditSelf() (r bool)             // 是否允许审核自己的单据
	GetUserLoginType() common.UserLoginType // 获取用户登录类型
}

type IMallOperator interface {
	IOperator
	GetWeChatOpenUserId() uint64 // 获取用户openid
	GetAppid() string            // 获取用户所属小程序appid
}
