package redis

import (
	"context"
	"hcscm/tools"
	"time"
)

// 电子商城账套令牌(不登录打开账套的电子商场)
var mallTenantManagementToken = NewDuplexKVOperate(getCmd, "mall_tenant_management", 24*time.Hour)

// 通过令牌获取商城用户身份
func GetMallTenantManagementByToken(ctx context.Context, token string) (tenantManagementId uint64, exist bool, err error) {
	tenantManagementId, exist, err = mallTenantManagementToken.GetUInt64Key(ctx, token)
	return
}

// 创建商城令牌
func AddMallTenantManagementToken(ctx context.Context, tenantManagementId uint64, token string) (err error) {
	key := tools.UInt642String(tenantManagementId)
	err = mallTenantManagementToken.AddValue(ctx, key, token)
	return
}

// 删除商城 token
func DeleteMallTenantManagementToken(ctx context.Context, token string) (err error) {
	err = mallTenantManagementToken.DelKey(ctx, token)
	return
}

// 电子商城用户令牌(账套用户登录电子商场)
var mallUserToken = NewDuplexKVOperate(getCmd, "mall_user", 24*time.Hour)

// 通过令牌获取商城用户身份
func GetMallUserByToken(ctx context.Context, token string) (wechatOpenUserID uint64, exist bool, err error) {
	wechatOpenUserID, exist, err = mallUserToken.GetUInt64Key(ctx, token)
	return
}

// 创建商城令牌
func AddMallUserToken(ctx context.Context, wechatOpenUserID uint64, token string) (err error) {
	key := tools.UInt642String(wechatOpenUserID)
	err = mallUserToken.AddValue(ctx, key, token)
	return
}

// 删除商城 token
func DeleteMallUserToken(ctx context.Context, token string) (err error) {
	err = mallUserToken.DelKey(ctx, token)
	return
}

// 电子商城客户令牌(客户登录电子商场)
var mallBizUnitToken = NewDuplexKVOperate(getCmd, "mall_biz_unit", 24*time.Hour)

// 通过令牌获取商城用户身份
func GetMallBizUnitByToken(ctx context.Context, token string) (bizUnitId uint64, exist bool, err error) {
	bizUnitId, exist, err = mallBizUnitToken.GetUInt64Key(ctx, token)
	return
}

// 创建商城令牌
func AddMallBizUnitToken(ctx context.Context, bizUnitId uint64, token string) (err error) {
	key := tools.UInt642String(bizUnitId)
	err = mallBizUnitToken.AddValue(ctx, key, token)
	return
}

// 删除商城 token
func DeleteMallBizUnitToken(ctx context.Context, token string) (err error) {
	err = mallBizUnitToken.DelKey(ctx, token)
	return
}
