package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/extern/pb/should_collect_order"
	should_collect_pb "hcscm/extern/pb/should_collect_order"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"strconv"
	"time"
)

type FpmSaleOutOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmSaleOutOrderRepo(tx *mysql_base.Tx) *FpmSaleOutOrderRepo {
	return &FpmSaleOutOrderRepo{tx: tx}
}

func (r *FpmSaleOutOrderRepo) Add(ctx context.Context, req *structure.AddFpmSaleOutOrderParam) (data structure.AddFpmSaleOutOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
	)

	fpmSaleOutOrder := model.NewFpmSaleOutOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmSaleOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmSaleOutOrder.BusinessClose = common_system.BusinessCloseNo
	fpmSaleOutOrder.DepartmentId = info.GetDepartmentId()
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleOutOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleOutOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmSaleOutOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmSaleOutOrder.OrderNo = orderNo
	fpmSaleOutOrder.Number = int(number)

	fpmSaleOutOrder.TotalWeight, fpmSaleOutOrder.TotalRoll, fpmSaleOutOrder.TotalLength = req.GetTotalPWR()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleOutOrder, err = mysql.MustCreateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmSaleOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		fpmSaleOutOrderItem.ParentId = fpmSaleOutOrder.Id
		fpmSaleOutOrderItem.ParentOrderNo = fpmSaleOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(cus_const.WarehouseGoodOutTypeSale)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmSaleOutOrderItem.TotalWeight = tw
		fpmSaleOutOrderItem.TotalPrice = tp
		fpmSaleOutOrderItem.OutLength = tl
		fpmSaleOutOrderItem.WeightError = weightError
		fpmSaleOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmSaleOutOrderItem.PaperTubeWeight = tpp
		fpmSaleOutOrderItem.SettleWeight = tsw
		fpmSaleOutOrderItem.ActuallyWeight = taw
		fpmSaleOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmSaleOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmSaleOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmSaleOutOrderItem.Id
			itemFc.WarehouseId = fpmSaleOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeSale
			itemFc.ActuallyWeight = fineCode.BaseUnitWeight - fineCode.WeightError
			itemFc.WarehouseOutOrderId = fpmSaleOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmSaleOutOrder.OrderNo
			itemFc.OrderTime = fpmSaleOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleOutOrder.Id
	return
}

func (r *FpmSaleOutOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmSaleOutOrderParam) (data structure.UpdateFpmSaleOutOrderData, err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder
		itemModel       model.FpmOutOrderItem
		findCodeModel   model.FpmOutOrderItemFc
		itemList        model.FpmOutOrderItemList
	)
	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmSaleOutOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	fpmSaleOutOrder.UpdateFpmSaleOutOrder(ctx, req)

	fpmSaleOutOrder.TotalWeight, fpmSaleOutOrder.TotalRoll, fpmSaleOutOrder.TotalLength = req.GetTotalPWR()

	if fpmSaleOutOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmSaleOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleOutOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
		if err != nil {
			return
		}
	}

	// 新增成品信息
	for _, item := range req.ItemData {
		fpmSaleOutOrderItem := model.NewFpmOutOrderItem(ctx, &item)
		fpmSaleOutOrderItem.ParentId = fpmSaleOutOrder.Id
		fpmSaleOutOrderItem.ParentOrderNo = fpmSaleOutOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(cus_const.WarehouseGoodOutTypeSale)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmSaleOutOrderItem.TotalWeight = tw
		fpmSaleOutOrderItem.TotalPrice = tp
		fpmSaleOutOrderItem.OutLength = tl
		fpmSaleOutOrderItem.WeightError = weightError
		fpmSaleOutOrderItem.SettleErrorWeight = settleErrorWeight
		fpmSaleOutOrderItem.PaperTubeWeight = tpp
		fpmSaleOutOrderItem.SettleWeight = tsw
		fpmSaleOutOrderItem.ActuallyWeight = taw
		fpmSaleOutOrderItem, err = mysql.MustCreateFpmOutOrderItem(r.tx, fpmSaleOutOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFc := model.NewFpmOutOrderItemFc(ctx, &fineCode)
			if itemFc.DyeFactoryDyelotNumber == "" {
				itemFc.DyeFactoryDyelotNumber = fpmSaleOutOrderItem.DyeFactoryDyelotNumber
			}
			itemFc.ParentId = fpmSaleOutOrderItem.Id
			itemFc.WarehouseId = fpmSaleOutOrder.WarehouseId
			itemFc.WarehouseOutType = cus_const.WarehouseGoodOutTypeSale
			itemFc.ActuallyWeight = fineCode.BaseUnitWeight - fineCode.WeightError
			itemFc.WarehouseOutOrderId = fpmSaleOutOrder.Id
			itemFc.WarehouseOutOrderNo = fpmSaleOutOrder.OrderNo
			itemFc.OrderTime = fpmSaleOutOrder.WarehouseOutTime
			itemFc, err = mysql.MustCreateFpmOutOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleOutOrder.Id
	return
}

func (r *FpmSaleOutOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmSaleOutOrderBusinessCloseParam) (data structure.UpdateFpmSaleOutOrderData, err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, v)
		if err != nil {
			return
		}
		// 更新业务状态
		err = fpmSaleOutOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmSaleOutOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (
	data structure.UpdateFpmSaleOutOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		fpmSaleOutOrder         model.FpmOutOrder
		fpmSaleOutOrderItemList model.FpmOutOrderItemList
		_salePlanOrderItemIds   = make([]uint64, 0)
	)

	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	fpmSaleOutOrderItemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, id)
	if err != nil {
		return
	}

	// 销售调拨进仓单是否已审核
	if fpmSaleOutOrder.SaleAlloInOrderId > 0 {
		alloInOrder, err2 := mysql.MustFirstFpmInOrderByID(r.tx, fpmSaleOutOrder.SaleAlloInOrderId)
		if err2 != nil {
			err = err2
			return
		}
		if alloInOrder.AuditStatus != common_system.OrderStatusAudited {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeSrcOrderNoPassCanAudit, "销售调拨进仓单："+alloInOrder.OrderNo))
			return
		}
	}

	updateItems, err = r.judgeAuditPass(id, fpmSaleOutOrder, ctx)
	if err != nil {
		return
	}

	// 审核
	err = fpmSaleOutOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return

	}
	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmSaleOutOrder.Id, true)
	if err != nil {
		return
	}

	for _, item := range fpmSaleOutOrderItemList {
		if item.SalePlanOrderItemId > 0 && item.ArrangeItemId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
	}
	salePlanOrderItemIds = _salePlanOrderItemIds

	data.ArrangeId = fpmSaleOutOrder.ArrangeOrderId
	data.WarehouseOutTime = tools.MyTime(fpmSaleOutOrder.WarehouseOutTime)
	return
}

func (r *FpmSaleOutOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (
	data structure.UpdateFpmSaleOutOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	err error) {

	var (
		fpmSaleOutOrder         model.FpmOutOrder
		fpmSaleOutOrderItemList model.FpmOutOrderItemList
		_salePlanOrderItemIds   = make([]uint64, 0)
	)

	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	fpmSaleOutOrderItemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, id)
	if err != nil {
		return
	}

	updateItems, err = r.judgeAuditWait(id, fpmSaleOutOrder, ctx)
	if err != nil {
		return
	}
	// 消审
	err = fpmSaleOutOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return

	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmSaleOutOrder.Id, false)
	if err != nil {
		return
	}
	for _, item := range fpmSaleOutOrderItemList {
		if item.SalePlanOrderItemId > 0 && item.ArrangeItemId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
	}
	salePlanOrderItemIds = _salePlanOrderItemIds

	data.ArrangeId = fpmSaleOutOrder.ArrangeOrderId
	data.SaleAlloOrderId = fpmSaleOutOrder.SaleAlloInOrderId
	data.WarehouseOutTime = tools.MyTime(fpmSaleOutOrder.WarehouseOutTime)
	return
}

func (r *FpmSaleOutOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmSaleOutOrderStatusData, err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder
	)

	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 拒绝/驳回
	err = fpmSaleOutOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return

	}
	return
}

func (r *FpmSaleOutOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmSaleOutOrderStatusData, err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder

		// fcOutList       model.FpmOutOrderItemFcList
	)
	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 作废
	err = fpmSaleOutOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return
	}

	data.Id = fpmSaleOutOrder.Id
	data.ArrangeId = fpmSaleOutOrder.ArrangeOrderId
	// 获取细码的库存id，用来解除占用,该逻辑不正确，作废出仓单不需要解除占用，解除占用的逻辑应该在出仓单审核通过时执行，消审需要重新占用回来
	// if fpmSaleOutOrder.ArrangeOrderId > 0 {
	// 	fcOutList, _ = mysql.FindFpmOutOrderItemFcByOrderId(r.tx, fpmSaleOutOrder.Id)
	// 	if len(fcOutList) > 0 {
	// 		data.StockDetailIds = fcOutList.GetStockIDs()
	// 	}
	// }

	return
}

func (r *FpmSaleOutOrderRepo) Get(ctx context.Context, req *structure.GetFpmSaleOutOrderQuery) (data structure.GetFpmSaleOutOrderData, err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder
		itemDatas       model.FpmOutOrderItemList
		fineCodeList    model.FpmOutOrderItemFcList
		detailStockList model.StockProductDetailList
		warehousePB     = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB          = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc          = dictionary.NewDictionaryClient()
	)
	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmSaleOutOrderData{}
	r.swapListModel2Data(fpmSaleOutOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmOutOrderItemByParentID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmOutOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmSaleOutOrder.AuditStatus != common_system.OrderStatusAudited {
			stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
				StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
			for _, v := range stockList {
				// 库存信息,2023-12-20 需求1001412改为获取可用数量和匹数
				itemGetData.SumStockRoll = v.AvailableRoll
				itemGetData.SumStockWeight = v.AvailableWeight
				itemGetData.SumStockLength = v.Length
			}
		}
		o.TotalSettlePrice += itemData.TotalPrice
		// 添加细码信息
		fineCodeList, err = mysql.FindFpmOutOrderItemFcByParenTID(r.tx, itemData.Id)
		if err != nil {
			return
		}

		if len(fineCodeList) == 0 {
			itemGetData.ItemFCData = make(structure.GetFpmOutOrderItemFcDataList, 0)
			o.ItemData = append(o.ItemData, itemGetData)
			continue
		}

		mUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "measurement_unit_id")
		mAuxiliaryUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "auxiliary_unit_id")
		wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
		stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, tools.MergeSlicesUint64(mUnitFcIds, mAuxiliaryUnitFcIds))
		binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
		detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
		if err != nil {
			return
		}
		dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
		dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

		for _, fineCode := range fineCodeList {
			fineCodeGetData := structure.GetFpmOutOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
			fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
			fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.ActuallyWeight = fineCode.WeightError
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.AuxiliaryUnitId = fineCode.AuxiliaryUnitId
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.OrderTime = tools.MyTime(fineCode.OrderTime)
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			fineCodeGetData.IsBooked = fineCode.IsBooked
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			fineCodeGetData.AuxiliaryUnitName = unitNameMap[fineCode.AuxiliaryUnitId]
			detailStock := detailStockList.Pick(fineCode.StockId)

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)

			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmSaleOutOrderRepo) GetList(ctx context.Context, req *structure.GetFpmOutOrderListQuery) (list structure.GetFpmSaleOutOrderDataList, total int, err error) {
	var (
		orders      model.FpmOutOrderList
		ordersItems model.FpmOutOrderItemList
		bizPB       = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmOutOrder(r.tx, req)
	if err != nil {
		return
	}

	ordersItems, err = mysql.FindFpmOutOrderItemByParenTIDs(r.tx, orders.GetIds())
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", orders, ordersItems)
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, warehouseIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "employee_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		var (
			countSaleWeightMap = make(map[uint64]int)
		)
		_ordersItems := ordersItems.PickByParentId(src.Id)
		for _, _ordersItem := range _ordersItems {
			if _ordersItem.AuxiliaryUnitId != 0 {
				if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
				} else {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.OutLength
				}
			} else {
				countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
			}
		}
		dst := structure.GetFpmSaleOutOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.ArrangeOrderId = src.ArrangeOrderId
		dst.ArrangeOrderNo = src.ArrangeOrderNo
		dst.SaleSystemId = src.SaleSystemId
		dst.CustomerId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
		dst.ProcessFactoryId = src.ProcessFactoryId
		dst.ReceiveName = src.ReceiveName
		dst.ReceiveAddr = src.ReceiveAddr
		dst.ReceivePhone = src.ReceivePhone
		dst.ReceiveTag = src.ReceiveTag
		dst.SaleUserId = src.SaleUserId
		dst.SaleFollowerId = src.SaleFollowerId
		dst.StoreKeeperId = src.StoreKeeperId
		dst.ArrangeUserId = src.ArrangeUserId
		dst.DriverId = src.DriverId
		dst.LogisticsCompanyId = src.LogisticsCompanyId
		dst.InternalRemark = src.InternalRemark
		dst.SaleRemark = src.SaleRemark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.TextureUrl = src.TextureUrl
		dst.SaleMode = src.SaleMode
		dst.SaleModeName = src.SaleMode.String()
		dst.MergeWeightInfo = func() (str string) {
			for k, v := range countSaleWeightMap {
				fmtRound := tools.GetRound(v, 2)
				if str == "" {
					str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				} else {
					str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				}
			}
			return
		}()
		for _, itemData := range dst.ItemData {
			dst.TotalSettlePrice += itemData.TotalPrice
		}
		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.CustomerName = bizNameMap[src.BizUnitId]
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.UnitName = unitNameMap[src.UnitId]
		list = append(list, dst)
	}
	return
}

func (r *FpmSaleOutOrderRepo) swapListModel2Data(src model.FpmOutOrder, dst *structure.GetFpmSaleOutOrderData, ctx context.Context) {
	var (
		bizService       = biz_pb.NewClientBizUnitService()
		saleSysPBSerbice = sale_sys_pb.NewSaleSystemClient()
		employeePB       = empl_pb.NewClientEmployeeService()
		// userPB           = user_pb.NewUserClient()
		employeeName = make(map[uint64]string)
		unitPB       = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB  = warehouse_pb.NewPhysicalWarehouseClient()
		company      = base_info_pb.NewInfoSaleLogisticsCompanyClient()
	)

	companyName, _ := company.GetInfoSaleLogisticsCompanyById(r.tx.Context, &base_info_pb.GetInfoSaleLogisticsCompanyQuery{Id: src.LogisticsCompanyId})
	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleSystemMap, err2 := saleSysPBSerbice.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	employeeName, _ = employeePB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId, src.SaleUserId, src.SaleFollowerId, src.ArrangeUserId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)
	// userName, _ := userPB.GetUserNameByIds(r.tx.Context, []uint64{src.ArrangeUserId})
	logisticsName, _ := bizService.GetBizUnitFactoryLogisticsNameByIds(ctx, []uint64{src.ProcessFactoryId})
	driverIds := tools.String2UintArray(src.DriverId, ",")
	driverNameMap, _ := employeePB.GetEmployeeNameByIds(r.tx.Context, driverIds)
	driverName := tools.GetMapValAppend2String(driverNameMap, ",")

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ArrangeOrderId = src.ArrangeOrderId
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.SaleSystemId = src.SaleSystemId
	dst.CustomerId = src.BizUnitId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseOutTime = tools.MyTime(src.WarehouseOutTime)
	dst.ProcessFactoryId = src.ProcessFactoryId
	dst.ReceiveName = src.ReceiveName
	dst.ReceiveAddr = src.ReceiveAddr
	dst.ReceivePhone = src.ReceivePhone
	dst.ReceiveTag = src.ReceiveTag
	dst.SaleUserId = src.SaleUserId
	dst.SaleFollowerId = src.SaleFollowerId
	dst.StoreKeeperId = src.StoreKeeperId
	dst.ArrangeUserId = src.ArrangeUserId
	dst.DriverId = src.DriverId
	dst.LogisticsCompanyId = src.LogisticsCompanyId
	dst.InternalRemark = src.InternalRemark
	dst.SaleRemark = src.SaleRemark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.TextureUrl = src.TextureUrl
	dst.SaleMode = src.SaleMode

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.SaleModeName = src.SaleMode.String()
	dst.WarehouseName = warehouseName
	dst.UnitName = unitName
	dst.DriverName = driverName
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.CustomerName = val
	}
	if val, ok := employeeName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	if val, ok := logisticsName[src.ProcessFactoryId]; ok {
		dst.ProcessFactoryName = val
	}
	if val, ok := employeeName[src.ArrangeUserId]; ok {
		dst.ArrangeUserName = val
	}
	if val, ok := employeeName[src.SaleUserId]; ok {
		dst.SaleUserName = val
	}
	if val, ok := employeeName[src.SaleFollowerId]; ok {
		dst.SaleFollowerName = val
	}
	dst.LogisticsCompanyName = companyName.Name
	dst.LogisticsCompanyArea = companyName.LogisticsArea
}

func (r *FpmSaleOutOrderRepo) swapItemModel2Data(item model.FpmOutOrderItem, dst *structure.GetFpmOutOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{item.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, item.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, []uint64{item.UnitId, item.AuxiliaryUnitId})
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, item.ProductColorId)

	dst.Id = item.Id
	dst.CreateTime = tools.MyTime(item.CreateTime)
	dst.UpdateTime = tools.MyTime(item.UpdateTime)
	dst.CreatorId = item.CreatorId
	dst.CreatorName = item.CreatorName
	dst.UpdaterId = item.UpdaterId
	dst.UpdateUserName = item.UpdaterName
	dst.ParentId = item.ParentId
	dst.SumStockId = item.SumStockId
	dst.ParentOrderNo = item.ParentOrderNo
	dst.QuoteOrderNo = item.QuoteOrderNo
	dst.QuoteOrderItemId = item.QuoteOrderItemId
	dst.ProductId = item.ProductId
	dst.ProductCode = item.ProductCode
	dst.ProductName = item.ProductName
	dst.CustomerId = item.CustomerId
	dst.ProductColorId = item.ProductColorId
	dst.ProductColorCode = item.ProductColorCode
	dst.ProductColorName = item.ProductColorName
	dst.ProductLevelId = item.ProductLevelId
	dst.DyeFactoryColorCode = item.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
	dst.ProductRemark = item.ProductRemark
	dst.ProductWidth = item.ProductWidth
	dst.ProductCraft = item.ProductCraft
	dst.ProductGramWeight = item.ProductGramWeight
	dst.ProductIngredient = item.ProductIngredient
	dst.OutRoll = item.OutRoll
	dst.SumStockId = item.SumStockId
	dst.TotalWeight = item.TotalWeight
	dst.WeightError = item.WeightError
	dst.ActuallyWeight = item.ActuallyWeight
	dst.PaperTubeWeight = item.PaperTubeWeight
	dst.SettleErrorWeight = item.SettleErrorWeight
	dst.SettleWeight = item.SettleWeight
	dst.UnitId = item.UnitId
	dst.AuxiliaryUnitId = item.AuxiliaryUnitId
	dst.AuxiliaryUnitName = unitName[item.AuxiliaryUnitId]
	if item.AuxiliaryUnitId == 0 {
		dst.AuxiliaryUnitId = item.UnitId
		dst.AuxiliaryUnitName = unitName[item.UnitId]
	}
	dst.OutLength = item.OutLength
	dst.Remark = item.Remark
	dst.ArrangeItemId = item.ArrangeItemId
	dst.UnitPrice = item.UnitPrice
	dst.LengthUnitPrice = item.LengthUnitPrice
	dst.OtherPrice = item.OtherPrice
	dst.TotalPrice = item.TotalPrice
	dst.SalePlanOrderItemId = item.SalePlanOrderItemId
	dst.FpmInOrderItemId = item.FpmInOrderItemId

	// 转义
	dst.UnitName = unitName[item.UnitId]
	dst.AuxiliaryUnitName = unitName[item.AuxiliaryUnitId]
	if val, ok := customerMap[item.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
	dst.SumStockRoll = item.SumStockRoll
	dst.SumStockLength = item.SumStockLength
	dst.SumStockWeight = item.SumStockWeight
}

func (r *FpmSaleOutOrderRepo) swapFcModel2Data(src model.FpmOutOrderItemFc, dst *structure.GetFpmOutOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, src.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.Roll = src.Roll
	dst.WarehouseBinId = src.WarehouseBinId
	dst.VolumeNumber = src.VolumeNumber
	dst.WarehouseOutType = src.WarehouseOutType
	dst.WarehouseOutOrderId = src.WarehouseOutOrderId
	dst.WarehouseOutOrderNo = src.WarehouseOutOrderNo
	dst.WarehouseInType = src.WarehouseInType
	dst.WarehouseInOrderId = src.WarehouseInOrderId
	dst.WarehouseInOrderNo = src.WarehouseInOrderNo
	dst.ArrangeOrderNo = src.ArrangeOrderNo
	dst.StockId = src.StockId
	dst.SumStockId = src.SumStockId
	dst.BaseUnitWeight = src.BaseUnitWeight
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.WeightError = src.WeightError
	dst.UnitId = src.UnitId
	dst.Length = src.Length
	dst.SettleWeight = src.SettleWeight
	dst.DigitalCode = src.DigitalCode
	dst.ShelfNo = src.ShelfNo
	dst.ContractNumber = src.ContractNumber
	dst.CustomerPoNum = src.CustomerPoNum
	dst.AccountNum = src.AccountNum
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductWidth = src.ProductWidth
	dst.ProductGramWeight = src.ProductGramWeight
	dst.StockRemark = src.StockRemark
	dst.Remark = src.Remark
	dst.InternalRemark = src.InternalRemark
	dst.ScanUserId = src.ScanUserId
	dst.ScanUserName = src.ScanUserName
	dst.ScanTime = tools.MyTime(src.ScanTime)
	dst.WarehouseId = src.WarehouseId
	dst.ActuallyWeight = src.ActuallyWeight
	dst.SettleErrorWeight = src.SettleErrorWeight
	dst.OrderTime = tools.MyTime(src.OrderTime)
	dst.ArrangeItemFcId = src.ArrangeItemFcId
	dst.IsBooked = src.IsBooked

	// 转义
	dst.WarehouseBinName = binName
	dst.UnitName = unitName
}

func (r *FpmSaleOutOrderRepo) judgeAuditPass(id uint64, order model.FpmOutOrder, ctx context.Context) (
	updateItems structure.UpdateStockProductDetailParamList,
	err error) {

	var (
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		// arrangeItemIds       = make([]uint64, 0)
		arrangeOrder model.FpmArrangeOrder
	)

	// 判断成品数量是否符合
	itemList, _ := mysql.FindFpmOutOrderItemByParentID(r.tx, id)

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	for _, item := range itemList {
		// arrangeItemIds = append(arrangeItemIds, item.ArrangeItemId)
		totalRoll := 0
		totalLength := 0
		totalWeight := 0

		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		swap2StockFieldParam.WarehouseId = order.WarehouseId

		fineCodeList, _ := mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		// if item.OutRoll == 0 && item.TotalWeight == 0 {
		// 	err = middleware.ErrorLog(errors.NewError(errors.ErrCodeRollAndWeightCanNotAllZero))
		// 	return
		// }
		// if len(fineCodeList) == 0 {
		// 	err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
		// 	return
		// }
		for _, fineCode := range fineCodeList {
			totalRoll = totalRoll + fineCode.Roll
			totalWeight = totalWeight + fineCode.ActuallyWeight
			totalLength += fineCode.Length
			updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParamBack(ctx, swap2StockFieldParam))
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			// 通过arrangeItem.ParentId获取配布单
			arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, arrangeItem.ParentId)
			if err != nil {
				return
			}
			updateItemWeight.WarehouseId = order.WarehouseId
			updateItemWeight.Type = 4
			updateItemWeight.StockProductId = arrangeItem.SumStockId

			if arrangeOrder.OutOrderType == cus_const.WarehouseGoodOutTypeSaleAllocate {
				inOrderItem, errFind := mysql.FindFpmInOrderItemByFpmArrangeItemId(r.tx, item.FpmInOrderItemId)
				if errFind != nil {
					return
				}
				updateItemWeight.BookOrderId = inOrderItem.ParentId
				updateItemWeight.BookRoll -= totalRoll
				updateItemWeight.BookWeight -= totalWeight
			} else {
				updateItemWeight.BookRoll -= arrangeItem.PushRoll
				updateItemWeight.BookWeight -= arrangeItem.PushWeight + arrangeItem.AdjustWeightError
				updateItemWeight.BookOrderId = arrangeOrder.SrcId
			}
			updateItemWeight.OrderId = order.Id
			updateItemWeight.OrderNo = order.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductSaleOutPass
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}

		//
		if totalRoll != item.OutRoll {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.OutLength > 0 && totalLength > 0 && item.OutLength != totalLength {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}

	// 判断销售送货单是否已审核(成品销售单生成)
	if order.ArrangeOrderId != 0 {
		// var (
		//	shouldCollectOrder should_collect_order.ShouldCollectOrderRes
		// )
		shouldCollectOrderSvc := should_collect_order.NewProductSaleClient()
		// shouldCollectOrder, err = shouldCollectOrderSvc.GetShouldCollectOrderBySrcId(ctx, order.ArrangeOrderId)
		// if err != nil {
		//	return
		// }
		// if shouldCollectOrder.AuditStatus != 2 {
		//	err = middleware.WarnLog(errors.NewError(errors.ErrCodeSrcOrderNoPassCanAudit))
		//	return
		// }
		noPass, saleDeliveryOrderNo, err2 := shouldCollectOrderSvc.JudgeProductSalePassByArrangeOrderItemIds(ctx, arrangeItemIds)
		if err2 != nil {
			err = err2
			return
		}
		if noPass {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "送货单："+saleDeliveryOrderNo+"未审核"))
			return
		}
	}

	updateBookWeight = append(updateBookWeight, updateWeight...)
	updateItems = updateBookWeight
	return
}

func (r *FpmSaleOutOrderRepo) judgeAuditWait(id uint64, fpmSaleOutOrder model.FpmOutOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {

	var (
		itemList             = model.FpmOutOrderItemList{}
		arrangeItemList      = model.FpmArrangeOrderItemList{}
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		arrangeOrder         model.FpmArrangeOrder
	)

	// 判断成品数量是否符合
	itemList, err = mysql.FindFpmOutOrderItemByParentID(r.tx, id)
	if err != nil {
		return
	}

	arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_item_id")
	if len(arrangeItemIds) > 0 {
		arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
		if err != nil {
			return
		}
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		totalRoll := 0
		totalWeight := 0

		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.WarehouseId = fpmSaleOutOrder.WarehouseId
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		fineCodeList, _ := mysql.FindFpmOutOrderItemFcByParenTID(r.tx, item.Id)
		for _, fineCode := range fineCodeList {
			totalRoll += fineCode.Roll
			totalWeight += fineCode.ActuallyWeight
			updateWeight = append(updateWeight, fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam))
		}
		// 补充扣减出仓预约数
		if item.ArrangeItemId != 0 {
			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			// 通过arrangeItem.ParentId获取配布单
			arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, arrangeItem.ParentId)
			if err != nil {
				return
			}
			updateItemWeight.WarehouseId = fpmSaleOutOrder.WarehouseId
			updateItemWeight.Type = 4
			updateItemWeight.StockProductId = arrangeItem.SumStockId

			if arrangeOrder.OutOrderType == cus_const.WarehouseGoodOutTypeSaleAllocate {
				inOrderItem, errFind := mysql.FindFpmInOrderItemByFpmArrangeItemId(r.tx, item.FpmInOrderItemId)
				if errFind != nil {
					return
				}
				updateItemWeight.BookOrderId = inOrderItem.ParentId
				updateItemWeight.BookRoll += totalRoll
				updateItemWeight.BookWeight += totalWeight
			} else {
				updateItemWeight.BookRoll += arrangeItem.PushRoll
				updateItemWeight.BookWeight += arrangeItem.PushWeight
				updateItemWeight.BookOrderId = arrangeOrder.SrcId
			}
			updateItemWeight.OrderId = fpmSaleOutOrder.Id
			updateItemWeight.OrderNo = fpmSaleOutOrder.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductSaleOutWait
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
		//if item.FpmInOrderItemId != 0 {
		//	// 获取进仓单明细
		//	inOrderItem, errFind := mysql.MustFirstFpmInOrderItemByID(r.tx, item.FpmInOrderItemId)
		//	if errFind != nil {
		//		return
		//	}
		//	var updateItemWeight = &structure.UpdateStockProductDetailParam{}
		//	// 通过inOrderItem.ParentId获取进仓单
		//	inOrder, errFind := mysql.MustFirstFpmInOrderByID(r.tx, inOrderItem.ParentId)
		//	if errFind != nil {
		//		return
		//	}
		//	updateItemWeight.WarehouseId = fpmSaleOutOrder.WarehouseId
		//	updateItemWeight.BookRoll += inOrderItem.InRoll
		//	updateItemWeight.BookWeight += inOrderItem.TotalWeight
		//	updateItemWeight.Type = 4
		//	updateItemWeight.BookOrderId = inOrder.Id
		//	updateItemWeight.OrderId = fpmSaleOutOrder.Id
		//	updateItemWeight.OrderNo = fpmSaleOutOrder.OrderNo
		//	updateItemWeight.OrderType = common_system.BookOrderTypeProductSaleOutWait
		//	updateBookWeight = append(updateBookWeight, updateItemWeight)
		//}

	}
	updateWeight = append(updateWeight, updateBookWeight...)
	updateItems = updateWeight
	return
}

func (r *FpmSaleOutOrderRepo) UpdateStatusWaitUseByChangeOrder(ctx context.Context, id uint64) (data structure.UpdateFpmSaleOutOrderStatusData, err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder
		info            = metadata.GetLoginInfo(ctx)
	)

	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 单据为待审核/已驳回/已作废状态
	if fpmSaleOutOrder.AuditStatus == common_system.OrderStatusPendingAudit || fpmSaleOutOrder.AuditStatus == common_system.OrderStatusRejected || fpmSaleOutOrder.AuditStatus == common_system.OrderStatusVoided {
		fpmSaleOutOrder.AuditorName = info.GetUserName()
		fpmSaleOutOrder.AuditorId = info.GetUserId()
		fpmSaleOutOrder.AuditDate = time.Now()
		fpmSaleOutOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}
	// 单据已审核状态
	if fpmSaleOutOrder.AuditStatus == common_system.OrderStatusAudited {
		// todo: 已审核状态逻辑待补充
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return

	}
	return
}

// 判断是否处于审核状态，并更新已出仓库的状态
func (r *FpmSaleOutOrderRepo) MPUpdateStatusWait(ctx context.Context, id uint64) (
	data structure.MPUpdateFpmSaleOutOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	err error) {

	var (
		fpmSaleOutOrder model.FpmOutOrder

		isNoCancel bool
	)
	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}
	data.SaleAlloOrderId = fpmSaleOutOrder.SaleAlloInOrderId
	updateItems, err = r.judgeAuditWait(id, fpmSaleOutOrder, ctx)
	if err != nil {
		return
	}

	isNoCancel, err = should_collect_pb.NewShouldCollectOrderClient().ShouldCollectOrderIsNoCancel(ctx, fpmSaleOutOrder.Id)
	if err != nil {
		return
	}
	if isNoCancel {
		err = errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, " 销售送货单（应收单）未作废")
		return
	}
	// 消审
	err = fpmSaleOutOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return

	}

	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmSaleOutOrder.Id, false)
	if err != nil {
		return
	}
	return
}

func (r *FpmSaleOutOrderRepo) MPUpdateStatusPass(ctx context.Context, id uint64) (
	data structure.MPUpdateFpmSaleOutOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	err error) {
	var (
		fpmSaleOutOrder model.FpmOutOrder
	)

	fpmSaleOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, id)
	if err != nil {
		return
	}

	updateItems, err = r.judgeAuditPass(id, fpmSaleOutOrder, ctx)
	if err != nil {
		return
	}
	// 审核
	err = fpmSaleOutOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmSaleOutOrder, err = mysql.MustUpdateFpmOutOrder(r.tx, fpmSaleOutOrder)
	if err != nil {
		return

	}
	// 更新已经出仓库
	err = mysql.MustUpdateIsOutWarehouseByOrderId(r.tx, fpmSaleOutOrder.Id, true)
	if err != nil {
		return
	}
	return
}

func (r *FpmSaleOutOrderRepo) GetIDsBySrcOrderID(ctx context.Context, srcIDs []uint64) (ids []uint64, err error) {
	var (
		orders model.FpmOutOrderList
	)
	ids = make([]uint64, 0)
	orders, err = mysql.FindFpmOutOrderBySrcOrderIDs(r.tx, srcIDs)
	if err != nil {
		return
	}

	for _, order := range orders {
		ids = append(ids, order.Id)
	}
	return
}
