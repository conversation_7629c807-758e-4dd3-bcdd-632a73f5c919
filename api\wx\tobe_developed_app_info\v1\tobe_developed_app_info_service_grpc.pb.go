// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.1
// source: api/wx/tobe_developed_app_info/tobe_developed_app_info_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfo_FullMethodName      = "/wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo/UpdateTobeDevelopedAppInfo"
	TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfoRobot_FullMethodName = "/wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo/UpdateTobeDevelopedAppInfoRobot"
	TobeDevelopedAppInfo_GetTobeDevelopedAppInfo_FullMethodName         = "/wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo/GetTobeDevelopedAppInfo"
	TobeDevelopedAppInfo_ListTobeDevelopedAppInfo_FullMethodName        = "/wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo/ListTobeDevelopedAppInfo"
)

// TobeDevelopedAppInfoClient is the client API for TobeDevelopedAppInfo service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type TobeDevelopedAppInfoClient interface {
	// UpdateTobeDevelopedAppInfo 更新代开发应用信息
	UpdateTobeDevelopedAppInfo(ctx context.Context, in *UpdateTobeDevelopedAppInfoRequest, opts ...grpc.CallOption) (*UpdateTobeDevelopedAppInfoReply, error)
	// UpdateTobeDevelopedAppInfo 更新代开发应用阿布机器人信息
	UpdateTobeDevelopedAppInfoRobot(ctx context.Context, in *UpdateTobeDevelopedAppInfoRobotRequest, opts ...grpc.CallOption) (*UpdateTobeDevelopedAppInfoRobotReply, error)
	// GetTobeDevelopedAppInfo 获取指定的代开发应用信息
	GetTobeDevelopedAppInfo(ctx context.Context, in *GetTobeDevelopedAppInfoRequest, opts ...grpc.CallOption) (*GetTobeDevelopedAppInfoReply, error)
	// ListTobeDevelopedAppInfo 获取代开发应用信息列表
	ListTobeDevelopedAppInfo(ctx context.Context, in *ListTobeDevelopedAppInfoRequest, opts ...grpc.CallOption) (*ListTobeDevelopedAppInfoReply, error)
}

type tobeDevelopedAppInfoClient struct {
	cc grpc.ClientConnInterface
}

func NewTobeDevelopedAppInfoClient(cc grpc.ClientConnInterface) TobeDevelopedAppInfoClient {
	return &tobeDevelopedAppInfoClient{cc}
}

func (c *tobeDevelopedAppInfoClient) UpdateTobeDevelopedAppInfo(ctx context.Context, in *UpdateTobeDevelopedAppInfoRequest, opts ...grpc.CallOption) (*UpdateTobeDevelopedAppInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateTobeDevelopedAppInfoReply)
	err := c.cc.Invoke(ctx, TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tobeDevelopedAppInfoClient) UpdateTobeDevelopedAppInfoRobot(ctx context.Context, in *UpdateTobeDevelopedAppInfoRobotRequest, opts ...grpc.CallOption) (*UpdateTobeDevelopedAppInfoRobotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateTobeDevelopedAppInfoRobotReply)
	err := c.cc.Invoke(ctx, TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfoRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tobeDevelopedAppInfoClient) GetTobeDevelopedAppInfo(ctx context.Context, in *GetTobeDevelopedAppInfoRequest, opts ...grpc.CallOption) (*GetTobeDevelopedAppInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetTobeDevelopedAppInfoReply)
	err := c.cc.Invoke(ctx, TobeDevelopedAppInfo_GetTobeDevelopedAppInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *tobeDevelopedAppInfoClient) ListTobeDevelopedAppInfo(ctx context.Context, in *ListTobeDevelopedAppInfoRequest, opts ...grpc.CallOption) (*ListTobeDevelopedAppInfoReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListTobeDevelopedAppInfoReply)
	err := c.cc.Invoke(ctx, TobeDevelopedAppInfo_ListTobeDevelopedAppInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// TobeDevelopedAppInfoServer is the server API for TobeDevelopedAppInfo service.
// All implementations must embed UnimplementedTobeDevelopedAppInfoServer
// for forward compatibility.
type TobeDevelopedAppInfoServer interface {
	// UpdateTobeDevelopedAppInfo 更新代开发应用信息
	UpdateTobeDevelopedAppInfo(context.Context, *UpdateTobeDevelopedAppInfoRequest) (*UpdateTobeDevelopedAppInfoReply, error)
	// UpdateTobeDevelopedAppInfo 更新代开发应用阿布机器人信息
	UpdateTobeDevelopedAppInfoRobot(context.Context, *UpdateTobeDevelopedAppInfoRobotRequest) (*UpdateTobeDevelopedAppInfoRobotReply, error)
	// GetTobeDevelopedAppInfo 获取指定的代开发应用信息
	GetTobeDevelopedAppInfo(context.Context, *GetTobeDevelopedAppInfoRequest) (*GetTobeDevelopedAppInfoReply, error)
	// ListTobeDevelopedAppInfo 获取代开发应用信息列表
	ListTobeDevelopedAppInfo(context.Context, *ListTobeDevelopedAppInfoRequest) (*ListTobeDevelopedAppInfoReply, error)
	mustEmbedUnimplementedTobeDevelopedAppInfoServer()
}

// UnimplementedTobeDevelopedAppInfoServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedTobeDevelopedAppInfoServer struct{}

func (UnimplementedTobeDevelopedAppInfoServer) UpdateTobeDevelopedAppInfo(context.Context, *UpdateTobeDevelopedAppInfoRequest) (*UpdateTobeDevelopedAppInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTobeDevelopedAppInfo not implemented")
}
func (UnimplementedTobeDevelopedAppInfoServer) UpdateTobeDevelopedAppInfoRobot(context.Context, *UpdateTobeDevelopedAppInfoRobotRequest) (*UpdateTobeDevelopedAppInfoRobotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTobeDevelopedAppInfoRobot not implemented")
}
func (UnimplementedTobeDevelopedAppInfoServer) GetTobeDevelopedAppInfo(context.Context, *GetTobeDevelopedAppInfoRequest) (*GetTobeDevelopedAppInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTobeDevelopedAppInfo not implemented")
}
func (UnimplementedTobeDevelopedAppInfoServer) ListTobeDevelopedAppInfo(context.Context, *ListTobeDevelopedAppInfoRequest) (*ListTobeDevelopedAppInfoReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTobeDevelopedAppInfo not implemented")
}
func (UnimplementedTobeDevelopedAppInfoServer) mustEmbedUnimplementedTobeDevelopedAppInfoServer() {}
func (UnimplementedTobeDevelopedAppInfoServer) testEmbeddedByValue()                              {}

// UnsafeTobeDevelopedAppInfoServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to TobeDevelopedAppInfoServer will
// result in compilation errors.
type UnsafeTobeDevelopedAppInfoServer interface {
	mustEmbedUnimplementedTobeDevelopedAppInfoServer()
}

func RegisterTobeDevelopedAppInfoServer(s grpc.ServiceRegistrar, srv TobeDevelopedAppInfoServer) {
	// If the following call pancis, it indicates UnimplementedTobeDevelopedAppInfoServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&TobeDevelopedAppInfo_ServiceDesc, srv)
}

func _TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTobeDevelopedAppInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TobeDevelopedAppInfoServer).UpdateTobeDevelopedAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TobeDevelopedAppInfoServer).UpdateTobeDevelopedAppInfo(ctx, req.(*UpdateTobeDevelopedAppInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfoRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTobeDevelopedAppInfoRobotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TobeDevelopedAppInfoServer).UpdateTobeDevelopedAppInfoRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfoRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TobeDevelopedAppInfoServer).UpdateTobeDevelopedAppInfoRobot(ctx, req.(*UpdateTobeDevelopedAppInfoRobotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TobeDevelopedAppInfo_GetTobeDevelopedAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTobeDevelopedAppInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TobeDevelopedAppInfoServer).GetTobeDevelopedAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TobeDevelopedAppInfo_GetTobeDevelopedAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TobeDevelopedAppInfoServer).GetTobeDevelopedAppInfo(ctx, req.(*GetTobeDevelopedAppInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _TobeDevelopedAppInfo_ListTobeDevelopedAppInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTobeDevelopedAppInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(TobeDevelopedAppInfoServer).ListTobeDevelopedAppInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: TobeDevelopedAppInfo_ListTobeDevelopedAppInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(TobeDevelopedAppInfoServer).ListTobeDevelopedAppInfo(ctx, req.(*ListTobeDevelopedAppInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// TobeDevelopedAppInfo_ServiceDesc is the grpc.ServiceDesc for TobeDevelopedAppInfo service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var TobeDevelopedAppInfo_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "wx.api.wx.tobe_developed_app_info.v1.TobeDevelopedAppInfo",
	HandlerType: (*TobeDevelopedAppInfoServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "UpdateTobeDevelopedAppInfo",
			Handler:    _TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfo_Handler,
		},
		{
			MethodName: "UpdateTobeDevelopedAppInfoRobot",
			Handler:    _TobeDevelopedAppInfo_UpdateTobeDevelopedAppInfoRobot_Handler,
		},
		{
			MethodName: "GetTobeDevelopedAppInfo",
			Handler:    _TobeDevelopedAppInfo_GetTobeDevelopedAppInfo_Handler,
		},
		{
			MethodName: "ListTobeDevelopedAppInfo",
			Handler:    _TobeDevelopedAppInfo_ListTobeDevelopedAppInfo_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wx/tobe_developed_app_info/tobe_developed_app_info_service.proto",
}
