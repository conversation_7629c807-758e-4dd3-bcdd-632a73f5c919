package data_analysis

import (
	"hcscm/server/data_analysis"
	"hcscm/server/system"
)

func InitDataAnalysis(routerGroup *system.RouterGroup) {
	{
		analysis := routerGroup.Group("analysis")
		analysis.GET("获取工作台数据", "getWorkbenchData", data_analysis.GetWorkbenchData)

		// 产品分析路由组
		productAnalysis := analysis.Group("productAnalysis")
		productAnalysis.GET("获取统一分析数据(为了AI分析使用)", "getUnifiedHomeAnalysis", data_analysis.GetUnifiedHomeAnalysisData)
		productAnalysis.GET("获取统一产品分析详情数据(为了AI分析使用)", "getUnifiedProductDetailAnalysis", data_analysis.GetUnifiedProductDetailAnalysisData)
		productAnalysis.GET("获取统一产品分析客户详情数据(为了AI分析使用)", "getUnifiedCustomerDetailAnalysis", data_analysis.GetUnifiedCustomerDetailAnalysisData)
		productAnalysis.GET("首页", "home", data_analysis.GetAnalysisHomeData)
		productAnalysis.GET("首页-趋势分析", "trend", data_analysis.GetProAnalysisTrendData)
		productAnalysis.GET("产品维度分析", "product", data_analysis.GetProAnalysisProductData)
		productAnalysis.GET("客户维度分析", "customer", data_analysis.GetProAnalysisCustomerData)
		// 获取前十产品
		productAnalysis.GET("Top产品", "topProduct", data_analysis.GetTopProductData)
		productAnalysis.GET("Top颜色", "topColor", data_analysis.GetTopColorData)
		productAnalysis.GET("Top客户", "topCustomer", data_analysis.GetTopCustomerData)
		// 客户分析路由组
		customerAnalysis := analysis.Group("customerAnalysis")
		customerAnalysis.GET("客户维度分析", "customer", data_analysis.GetCusAnalysisCustomerData)
		customerAnalysis.GET("产品维度分析", "product", data_analysis.GetCusAnalysisProductData)
		customerAnalysis.GET("获取统一客户分析详情数据(为了AI分析使用)", "getUnifiedProductDetailAnalysis", data_analysis.GetUnifiedCustomerDetailCustomerAnalysisData)
		// 获取指定客户的指定时间段的应收单记录
		customerAnalysis.GET("获取指定客户的指定时间段的应收单记录", "getReceivableOrderList", data_analysis.GetReceivableOrderList)
	}
}
