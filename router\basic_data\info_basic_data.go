package basic_data

import (
	tenant "hcscm/server/basic_data"
	web "hcscm/server/basic_data/info_basic_data"
	"hcscm/server/system"
)

func InitInfoBasicData(routerGroup *system.RouterGroup) {

	bao := routerGroup.Group("info_basic_data")
	// 计量单位
	{
		infoBaseMeasurementUnit := bao.Group("infoBaseMeasurementUnit")
		infoBaseMeasurementUnit.POSTNeedAuth("新增", "addInfoBaseMeasurementUnit", web.AddInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.PUTNeedAuth("更新", "updateInfoBaseMeasurementUnit", web.UpdateInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.PUTNeedAuth("更新默认单位", "updateIsDefault", web.UpdateInfoBaseMeasurementIsDefaultUnit)
		infoBaseMeasurementUnit.PUTNeedAuth("更新状态", "updateInfoBaseMeasurementUnitStatus", web.UpdateInfoBaseMeasurementUnitStatus)
		infoBaseMeasurementUnit.DELETENeedAuth("删除", "deleteInfoBaseMeasurementUnit", web.DeleteInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.GET("获取", "getInfoBaseMeasurementUnit", web.GetInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.GET("获取列表", "getInfoBaseMeasurementUnitList", web.GetInfoBaseMeasurementUnitList)
		infoBaseMeasurementUnit.GET("获取枚举列表", "getInfoBaseMeasurementUnitEnumList", web.GetInfoBaseMeasurementUnitEnumList)
	}
	// 成品等级
	{
		infoBaseFinishedProductLevel := bao.Group("infoBaseFinishedProductLevel")
		infoBaseFinishedProductLevel.POSTNeedAuth("新增", "addInfoBaseFinishedProductLevel", web.AddInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.PUTNeedAuth("更新", "updateInfoBaseFinishedProductLevel", web.UpdateInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.PUTNeedAuth("更新状态", "updateInfoBaseFinishedProductLevelStatus", web.UpdateInfoBaseFinishedProductLevelStatus)
		infoBaseFinishedProductLevel.DELETENeedAuth("删除", "deleteInfoBaseFinishedProductLevel", web.DeleteInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.GET("获取", "getInfoBaseFinishedProductLevel", web.GetInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.GET("获取列表", "getInfoBaseFinishedProductLevelList", web.GetInfoBaseFinishedProductLevelList)
		infoBaseFinishedProductLevel.GET("获取枚举列表", "getInfoBaseFinishedProductLevelEnumList", web.GetInfoBaseFinishedProductLevelEnumList)
	}
	// 坯布等级
	{
		infoBaseGreyFabricLevel := bao.Group("infoBaseGreyFabricLevel")
		infoBaseGreyFabricLevel.POSTNeedAuth("新增", "addInfoBaseGreyFabricLevel", web.AddInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.PUTNeedAuth("更新", "updateInfoBaseGreyFabricLevel", web.UpdateInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.PUTNeedAuth("更新状态", "updateInfoBaseGreyFabricLevelStatus", web.UpdateInfoBaseGreyFabricLevelStatus)
		infoBaseGreyFabricLevel.DELETENeedAuth("删除", "deleteInfoBaseGreyFabricLevel", web.DeleteInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.GET("获取", "getInfoBaseGreyFabricLevel", web.GetInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.GET("获取列表", "getInfoBaseGreyFabricLevelList", web.GetInfoBaseGreyFabricLevelList)
		infoBaseGreyFabricLevel.GET("获取列表-其他地方使用", "getInfoBaseGreyFabricLevelListUseByOther", web.GetInfoBaseGreyFabricLevelListUseByOther)
	}
	// 坯布颜色
	{
		infoProductGrayFabricColor := bao.Group("infoProductGrayFabricColor")
		infoProductGrayFabricColor.POSTNeedAuth("新增", "addInfoProductGrayFabricColor", web.AddInfoProductGrayFabricColor)
		infoProductGrayFabricColor.PUTNeedAuth("更新", "updateInfoProductGrayFabricColor", web.UpdateInfoProductGrayFabricColor)
		infoProductGrayFabricColor.PUTNeedAuth("更新状态", "updateInfoProductGrayFabricColorStatus", web.UpdateInfoProductGrayFabricColorStatus)
		infoProductGrayFabricColor.DELETENeedAuth("删除", "deleteInfoProductGrayFabricColor", web.DeleteInfoProductGrayFabricColor)
		infoProductGrayFabricColor.GET("获取", "getInfoProductGrayFabricColor", web.GetInfoProductGrayFabricColor)
		infoProductGrayFabricColor.GET("获取列表", "getInfoProductGrayFabricColorList", web.GetInfoProductGrayFabricColorList)
		infoProductGrayFabricColor.GET("获取枚举列表", "getInfoProductGrayFabricColorEnumList", web.GetInfoProductGrayFabricColorEnumList)
		infoProductGrayFabricColor.GET("获取枚举列表(填编号的时候用的)", "searchForInfoProductGrayFabricColorField", web.SearchForInfoProductGrayFabricColorField)
	}

	// 原料等级
	{
		infoBaseRawMaterialLevel := bao.Group("infoBaseRawMaterialLevel")
		infoBaseRawMaterialLevel.POSTNeedAuth("新增", "addInfoBaseRawMaterialLevel", web.AddInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.PUTNeedAuth("更新", "updateInfoBaseRawMaterialLevel", web.UpdateInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.PUTNeedAuth("更新状态", "updateInfoBaseRawMaterialLevelStatus", web.UpdateInfoBaseRawMaterialLevelStatus)
		infoBaseRawMaterialLevel.DELETENeedAuth("删除", "deleteInfoBaseRawMaterialLevel", web.DeleteInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.GET("获取", "getInfoBaseRawMaterialLevel", web.GetInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.GET("获取列表", "getInfoBaseRawMaterialLevelList", web.GetInfoBaseRawMaterialLevelList)
		infoBaseRawMaterialLevel.GET("获取枚举列表", "getInfoBaseRawMaterialLevelEnumList", web.GetInfoBaseRawMaterialLevelEnumList)
	}
	// 烫染进度
	{
		infoDyeingFinishingProgress := bao.Group("infoDyeingFinishingProgress")
		infoDyeingFinishingProgress.POSTNeedAuth("新增", "addInfoDyeingFinishingProgress", web.AddInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.PUTNeedAuth("更新", "updateInfoDyeingFinishingProgress", web.UpdateInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.PUTNeedAuth("更新状态", "updateInfoDyeingFinishingProgressStatus", web.UpdateInfoDyeingFinishingProgressStatus)
		infoDyeingFinishingProgress.DELETENeedAuth("删除", "deleteInfoDyeingFinishingProgress", web.DeleteInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.GET("获取", "getInfoDyeingFinishingProgress", web.GetInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.GET("获取列表", "getInfoDyeingFinishingProgressList", web.GetInfoDyeingFinishingProgressList)
		infoDyeingFinishingProgress.GET("获取枚举列表", "getInfoDyeingFinishingProgressEnumList", web.GetInfoDyeingFinishingProgressEnumList)
	}
	// 染整工艺资料 info_dyeing_finishing_process_data
	{
		infoDyeingFinishingProcessData := bao.Group("infoDyeingFinishingProcessData")
		infoDyeingFinishingProcessData.POSTNeedAuth("新增", "addInfoDyeingFinishingProcessData", web.AddInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.PUTNeedAuth("更新", "updateInfoDyeingFinishingProcessData", web.UpdateInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.PUTNeedAuth("更新状态", "updateInfoDyeingFinishingProcessDataStatus", web.UpdateInfoDyeingFinishingProcessDataStatus)
		infoDyeingFinishingProcessData.DELETENeedAuth("删除", "deleteInfoDyeingFinishingProcessData", web.DeleteInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.GET("获取", "getInfoDyeingFinishingProcessData", web.GetInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.GET("获取列表", "getInfoDyeingFinishingProcessDataList", web.GetInfoDyeingFinishingProcessDataList)
		infoDyeingFinishingProcessData.GET("获取枚举列表", "getInfoDyeingFinishingProcessDataEnumList", web.GetInfoDyeingFinishingProcessDataEnumList)
	}

	// 染整工艺资料-分类 info_dyeing_finishing_process_data_type
	{
		infoDyeingFinishingProcessDataType := bao.Group("infoDyeingFinishingProcessDataType")
		infoDyeingFinishingProcessDataType.POSTNeedAuth("新增", "addInfoDyeingFinishingProcessDataType", web.AddInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.PUTNeedAuth("更新", "updateInfoDyeingFinishingProcessDataType", web.UpdateInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.DELETENeedAuth("删除", "deleteInfoDyeingFinishingProcessDataType", web.DeleteInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.GET("获取", "getInfoDyeingFinishingProcessDataType", web.GetInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.GET("获取列表", "getInfoDyeingFinishingProcessDataTypeList", web.GetInfoDyeingFinishingProcessDataTypeList)
	}

	// 流水方向
	{
		infoFinanceFlowDirection := bao.Group("infoFinanceFlowDirection")
		infoFinanceFlowDirection.POSTNeedAuth("新增", "addInfoFinanceFlowDirection", web.AddInfoFinanceFlowDirection)
		infoFinanceFlowDirection.PUTNeedAuth("更新", "updateInfoFinanceFlowDirection", web.UpdateInfoFinanceFlowDirection)
		infoFinanceFlowDirection.PUTNeedAuth("更新状态", "updateInfoFinanceFlowDirectionStatus", web.UpdateInfoFinanceFlowDirectionStatus)
		infoFinanceFlowDirection.DELETENeedAuth("删除", "deleteInfoFinanceFlowDirection", web.DeleteInfoFinanceFlowDirection)
		infoFinanceFlowDirection.GET("获取", "getInfoFinanceFlowDirection", web.GetInfoFinanceFlowDirection)
		infoFinanceFlowDirection.GET("获取列表", "getInfoFinanceFlowDirectionList", web.GetInfoFinanceFlowDirectionList)
		infoFinanceFlowDirection.GET("获取枚举列表", "getInfoFinanceFlowDirectionEnumList", web.GetInfoFinanceFlowDirectionEnumList)
	}

	// 发票抬头
	{
		infoPurchaseInvoiceHeader := bao.Group("infoPurchaseInvoiceHeader")
		infoPurchaseInvoiceHeader.POSTNeedAuth("新增", "addInfoPurchaseInvoiceHeader", web.AddInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.PUTNeedAuth("更新", "updateInfoPurchaseInvoiceHeader", web.UpdateInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.PUTNeedAuth("更新状态", "updateInfoPurchaseInvoiceHeaderStatus", web.UpdateInfoPurchaseInvoiceHeaderStatus)
		infoPurchaseInvoiceHeader.DELETENeedAuth("删除", "deleteInfoPurchaseInvoiceHeader", web.DeleteInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.GET("获取", "getInfoPurchaseInvoiceHeader", web.GetInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.GET("获取列表", "getInfoPurchaseInvoiceHeaderList", web.GetInfoPurchaseInvoiceHeaderList)
		infoPurchaseInvoiceHeader.GET("获取列表-其他地方使用", "getInfoPurchaseInvoiceHeaderListUseByOther", web.GetInfoPurchaseInvoiceHeaderListUseByOther)
	}

	// 织造规格
	{
		infoProductWeaveSpecification := bao.Group("infoProductWeaveSpecification")
		infoProductWeaveSpecification.POSTNeedAuth("新增", "addInfoProductWeaveSpecification", web.AddInfoProductWeaveSpecification)
		infoProductWeaveSpecification.PUTNeedAuth("更新", "updateInfoProductWeaveSpecification", web.UpdateInfoProductWeaveSpecification)
		infoProductWeaveSpecification.PUTNeedAuth("更新状态", "updateInfoProductWeaveSpecificationStatus", web.UpdateInfoProductWeaveSpecificationStatus)
		infoProductWeaveSpecification.DELETENeedAuth("删除", "deleteInfoProductWeaveSpecification", web.DeleteInfoProductWeaveSpecification)
		infoProductWeaveSpecification.GET("获取", "getInfoProductWeaveSpecification", web.GetInfoProductWeaveSpecification)
		infoProductWeaveSpecification.GET("获取列表", "getInfoProductWeaveSpecificationList", web.GetInfoProductWeaveSpecificationList)
		infoProductWeaveSpecification.GET("获取枚举列表", "getInfoProductWeaveSpecificationEnumList", web.GetInfoProductWeaveSpecificationEnumList)
	}

	// 织机机型
	{
		infoProductLoomModel := bao.Group("infoProductLoomModel")
		infoProductLoomModel.POSTNeedAuth("新增", "addInfoProductLoomModel", web.AddInfoProductLoomModel)
		infoProductLoomModel.PUTNeedAuth("更新", "updateInfoProductLoomModel", web.UpdateInfoProductLoomModel)
		infoProductLoomModel.PUTNeedAuth("更新状态", "updateInfoProductLoomModelStatus", web.UpdateInfoProductLoomModelStatus)
		infoProductLoomModel.DELETENeedAuth("删除", "deleteInfoProductLoomModel", web.DeleteInfoProductLoomModel)
		infoProductLoomModel.GET("获取", "getInfoProductLoomModel", web.GetInfoProductLoomModel)
		infoProductLoomModel.GET("获取列表", "getInfoProductLoomModelList", web.GetInfoProductLoomModelList)
		infoProductLoomModel.GET("获取枚举列表", "getInfoProductLoomModelEnumList", web.GetInfoProductLoomModelEnumList)
	}

	// 寸针数
	{
		infoProductNeedleSize := bao.Group("infoProductNeedleSize")
		infoProductNeedleSize.POSTNeedAuth("新增", "addInfoProductNeedleSize", web.AddInfoProductNeedleSize)
		infoProductNeedleSize.PUTNeedAuth("更新", "updateInfoProductNeedleSize", web.UpdateInfoProductNeedleSize)
		infoProductNeedleSize.PUTNeedAuth("更新状态", "updateInfoProductNeedleSizeStatus", web.UpdateInfoProductNeedleSizeStatus)
		infoProductNeedleSize.DELETENeedAuth("删除", "deleteInfoProductNeedleSize", web.DeleteInfoProductNeedleSize)
		infoProductNeedleSize.GET("获取", "getInfoProductNeedleSize", web.GetInfoProductNeedleSize)
		infoProductNeedleSize.GET("获取列表", "getInfoProductNeedleSizeList", web.GetInfoProductNeedleSizeList)
		infoProductNeedleSize.GET("获取枚举列表", "getInfoProductNeedleSizeEnumList", web.GetInfoProductNeedleSizeEnumList)
	}

	// 付款期限
	{
		infoProductPaymentTerm := bao.Group("infoProductPaymentTerm")
		infoProductPaymentTerm.POSTNeedAuth("新增", "addInfoProductPaymentTerm", web.AddInfoProductPaymentTerm)
		infoProductPaymentTerm.PUTNeedAuth("更新", "updateInfoProductPaymentTerm", web.UpdateInfoProductPaymentTerm)
		infoProductPaymentTerm.PUTNeedAuth("更新状态", "updateInfoProductPaymentTermStatus", web.UpdateInfoProductPaymentTermStatus)
		infoProductPaymentTerm.DELETENeedAuth("删除", "deleteInfoProductPaymentTerm", web.DeleteInfoProductPaymentTerm)
		infoProductPaymentTerm.GET("获取", "getInfoProductPaymentTerm", web.GetInfoProductPaymentTerm)
		infoProductPaymentTerm.GET("获取列表", "getInfoProductPaymentTermList", web.GetInfoProductPaymentTermList)
		infoProductPaymentTerm.GET("获取枚举列表", "getInfoProductPaymentTermEnumList", web.GetInfoProductPaymentTermEnumList)
	}

	// 产品来源
	{
		infoSaleFinishedProductSource := bao.Group("infoSaleFinishedProductSource")
		infoSaleFinishedProductSource.POSTNeedAuth("新增", "addInfoSaleFinishedProductSource", web.AddInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.PUTNeedAuth("更新", "updateInfoSaleFinishedProductSource", web.UpdateInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.PUTNeedAuth("更新状态", "updateInfoSaleFinishedProductSourceStatus", web.UpdateInfoSaleFinishedProductSourceStatus)
		infoSaleFinishedProductSource.DELETENeedAuth("删除", "deleteInfoSaleFinishedProductSource", web.DeleteInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.GET("获取", "getInfoSaleFinishedProductSource", web.GetInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.GET("获取列表", "getInfoSaleFinishedProductSourceList", web.GetInfoSaleFinishedProductSourceList)
		infoSaleFinishedProductSource.GET("获取枚举列表", "getInfoSaleFinishedProductSourceEnumList", web.GetInfoSaleFinishedProductSourceEnumList)
	}

	// 结算方式
	{
		infoSaleSettlementMethod := bao.Group("infoSaleSettlementMethod")
		infoSaleSettlementMethod.POSTNeedAuth("新增", "addInfoSaleSettlementMethod", web.AddInfoSaleSettlementMethod)
		infoSaleSettlementMethod.PUTNeedAuth("更新", "updateInfoSaleSettlementMethod", web.UpdateInfoSaleSettlementMethod)
		infoSaleSettlementMethod.PUTNeedAuth("更新状态", "updateInfoSaleSettlementMethodStatus", web.UpdateInfoSaleSettlementMethodStatus)
		infoSaleSettlementMethod.DELETENeedAuth("删除", "deleteInfoSaleSettlementMethod", web.DeleteInfoSaleSettlementMethod)
		infoSaleSettlementMethod.GET("获取", "getInfoSaleSettlementMethod", web.GetInfoSaleSettlementMethod)
		infoSaleSettlementMethod.GET("获取列表", "getInfoSaleSettlementMethodList", web.GetInfoSaleSettlementMethodList)
		infoSaleSettlementMethod.GET("获取枚举列表", "getInfoSaleSettlementMethodEnumList", web.GetInfoSaleSettlementMethodEnumList)
	}

	// 订单类别 info_sale_order_category
	{
		infoSaleOrderCategory := bao.Group("infoSaleOrderCategory")
		infoSaleOrderCategory.POSTNeedAuth("新增", "addInfoSaleOrderCategory", web.AddInfoSaleOrderCategory)
		infoSaleOrderCategory.PUTNeedAuth("更新", "updateInfoSaleOrderCategory", web.UpdateInfoSaleOrderCategory)
		infoSaleOrderCategory.PUTNeedAuth("更新状态", "updateInfoSaleOrderCategoryStatus", web.UpdateInfoSaleOrderCategoryStatus)
		infoSaleOrderCategory.DELETENeedAuth("删除", "deleteInfoSaleOrderCategory", web.DeleteInfoSaleOrderCategory)
		infoSaleOrderCategory.GET("获取", "getInfoSaleOrderCategory", web.GetInfoSaleOrderCategory)
		infoSaleOrderCategory.GET("获取列表", "getInfoSaleOrderCategoryList", web.GetInfoSaleOrderCategoryList)
		infoSaleOrderCategory.GET("获取枚举列表", "getInfoSaleOrderCategoryEnumList", web.GetInfoSaleOrderCategoryEnumList)
	}

	// 物流公司 info_sale_logistics_company
	{
		infoSaleLogisticsCompany := bao.Group("infoSaleLogisticsCompany")
		infoSaleLogisticsCompany.POSTNeedAuth("新增", "addInfoSaleLogisticsCompany", web.AddInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.PUTNeedAuth("更新", "updateInfoSaleLogisticsCompany", web.UpdateInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.PUTNeedAuth("更新状态", "updateInfoSaleLogisticsCompanyStatus", web.UpdateInfoSaleLogisticsCompanyStatus)
		infoSaleLogisticsCompany.DELETENeedAuth("删除", "deleteInfoSaleLogisticsCompany", web.DeleteInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.GET("获取", "getInfoSaleLogisticsCompany", web.GetInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.GET("获取（其他地方使用）", "getInfoSaleLogisticsCompanyUseOther", web.GetInfoSaleLogisticsCompanyUseOther)
		infoSaleLogisticsCompany.GET("获取列表", "getInfoSaleLogisticsCompanyList", web.GetInfoSaleLogisticsCompanyList)
		infoSaleLogisticsCompany.GET("获取枚举列表", "getInfoSaleLogisticsCompanyEnumList", web.GetInfoSaleLogisticsCompanyEnumList)
	}
	// 物流公司分类 info_sale_logistics_company_type
	{
		infoSaleLogisticsCompanyType := bao.Group("infoSaleLogisticsCompanyType")
		infoSaleLogisticsCompanyType.POSTNeedAuth("新增", "addInfoSaleLogisticsCompanyType", web.AddInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.PUTNeedAuth("更新", "updateInfoSaleLogisticsCompanyType", web.UpdateInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.DELETENeedAuth("删除", "deleteInfoSaleLogisticsCompanyType", web.DeleteInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.GET("获取", "getInfoSaleLogisticsCompanyType", web.GetInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.GET("获取列表", "getInfoSaleLogisticsCompanyTypeList", web.GetInfoSaleLogisticsCompanyTypeList)
	}

	// 缩水 info_sale_shrink
	{
		infoSaleShrink := bao.Group("infoSaleShrink")
		infoSaleShrink.POSTNeedAuth("新增", "addInfoSaleShrink", web.AddInfoSaleShrink)
		infoSaleShrink.PUTNeedAuth("更新", "updateInfoSaleShrink", web.UpdateInfoSaleShrink)
		infoSaleShrink.PUTNeedAuth("更新状态", "updateInfoSaleShrinkStatus", web.UpdateInfoSaleShrinkStatus)
		infoSaleShrink.DELETENeedAuth("删除", "deleteInfoSaleShrink", web.DeleteInfoSaleShrink)
		infoSaleShrink.GET("获取", "getInfoSaleShrink", web.GetInfoSaleShrink)
		infoSaleShrink.GET("获取列表", "getInfoSaleShrinkList", web.GetInfoSaleShrinkList)
		infoSaleShrink.GET("获取枚举列表", "getInfoSaleShrinkEnumList", web.GetInfoSaleShrinkEnumList)
	}

	// 日晒牢度 info_sale_light_fastness
	{
		infoSaleLightFastness := bao.Group("infoSaleLightFastness")
		infoSaleLightFastness.POSTNeedAuth("新增", "addInfoSaleLightFastness", web.AddInfoSaleLightFastness)
		infoSaleLightFastness.PUTNeedAuth("更新", "updateInfoSaleLightFastness", web.UpdateInfoSaleLightFastness)
		infoSaleLightFastness.PUTNeedAuth("更新状态", "updateInfoSaleLightFastnessStatus", web.UpdateInfoSaleLightFastnessStatus)
		infoSaleLightFastness.DELETENeedAuth("删除", "deleteInfoSaleLightFastness", web.DeleteInfoSaleLightFastness)
		infoSaleLightFastness.GET("获取", "getInfoSaleLightFastness", web.GetInfoSaleLightFastness)
		infoSaleLightFastness.GET("获取列表", "getInfoSaleLightFastnessList", web.GetInfoSaleLightFastnessList)
		infoSaleLightFastness.GET("获取枚举列表", "getInfoSaleLightFastnessEnumList", web.GetInfoSaleLightFastnessEnumList)
	}

	// 水洗 info_sale_wash
	{
		infoSaleWash := bao.Group("infoSaleWash")
		infoSaleWash.POSTNeedAuth("新增", "addInfoSaleWash", web.AddInfoSaleWash)
		infoSaleWash.PUTNeedAuth("更新", "updateInfoSaleWash", web.UpdateInfoSaleWash)
		infoSaleWash.PUTNeedAuth("更新状态", "updateInfoSaleWashStatus", web.UpdateInfoSaleWashStatus)
		infoSaleWash.DELETENeedAuth("删除", "deleteInfoSaleWash", web.DeleteInfoSaleWash)
		infoSaleWash.GET("获取", "getInfoSaleWash", web.GetInfoSaleWash)
		infoSaleWash.GET("获取列表", "getInfoSaleWashList", web.GetInfoSaleWashList)
		infoSaleWash.GET("获取枚举列表", "getInfoSaleWashEnumList", web.GetInfoSaleWashEnumList)
	}

	// 湿擦 info_sale_wet_wipe
	{
		infoSaleWetWipe := bao.Group("infoSaleWetWipe")
		infoSaleWetWipe.POSTNeedAuth("新增", "addInfoSaleWetWipe", web.AddInfoSaleWetWipe)
		infoSaleWetWipe.PUTNeedAuth("更新", "updateInfoSaleWetWipe", web.UpdateInfoSaleWetWipe)
		infoSaleWetWipe.PUTNeedAuth("更新状态", "updateInfoSaleWetWipeStatus", web.UpdateInfoSaleWetWipeStatus)
		infoSaleWetWipe.DELETENeedAuth("删除", "deleteInfoSaleWetWipe", web.DeleteInfoSaleWetWipe)
		infoSaleWetWipe.GET("获取", "getInfoSaleWetWipe", web.GetInfoSaleWetWipe)
		infoSaleWetWipe.GET("获取列表", "getInfoSaleWetWipeList", web.GetInfoSaleWetWipeList)
		infoSaleWetWipe.GET("获取枚举列表", "getInfoSaleWetWipeEnumList", web.GetInfoSaleWetWipeEnumList)
	}

	// 运费 info_sale_freight
	{
		infoSaleFreight := bao.Group("infoSaleFreight")
		infoSaleFreight.POSTNeedAuth("新增", "addInfoSaleFreight", web.AddInfoSaleFreight)
		infoSaleFreight.PUTNeedAuth("更新", "updateInfoSaleFreight", web.UpdateInfoSaleFreight)
		infoSaleFreight.PUTNeedAuth("更新状态", "updateInfoSaleFreightStatus", web.UpdateInfoSaleFreightStatus)
		infoSaleFreight.DELETENeedAuth("删除", "deleteInfoSaleFreight", web.DeleteInfoSaleFreight)
		infoSaleFreight.GET("获取", "getInfoSaleFreight", web.GetInfoSaleFreight)
		infoSaleFreight.GET("获取列表", "getInfoSaleFreightList", web.GetInfoSaleFreightList)
		infoSaleFreight.GET("获取枚举列表", "getInfoSaleFreightEnumList", web.GetInfoSaleFreightEnumList)
	}

	// 含税项目 info_sale_taxable_item
	{
		infoSaleTaxableItem := bao.Group("infoSaleTaxableItem")
		infoSaleTaxableItem.POSTNeedAuth("新增", "addInfoSaleTaxableItem", web.AddInfoSaleTaxableItem)
		infoSaleTaxableItem.PUTNeedAuth("更新", "updateInfoSaleTaxableItem", web.UpdateInfoSaleTaxableItem)
		infoSaleTaxableItem.PUTNeedAuth("更新状态", "updateInfoSaleTaxableItemStatus", web.UpdateInfoSaleTaxableItemStatus)
		infoSaleTaxableItem.DELETENeedAuth("删除", "deleteInfoSaleTaxableItem", web.DeleteInfoSaleTaxableItem)
		infoSaleTaxableItem.GET("获取", "getInfoSaleTaxableItem", web.GetInfoSaleTaxableItem)
		infoSaleTaxableItem.GET("获取列表", "getInfoSaleTaxableItemList", web.GetInfoSaleTaxableItemList)
		infoSaleTaxableItem.GET("获取枚举列表", "getInfoSaleTaxableItemEnumList", web.GetInfoSaleTaxableItemEnumList)
	}

	// 疵点 info_basic_defect
	infoBasicDefect := bao.Group("infoBasicDefect")
	infoBasicDefect.POSTNeedAuth("新增", "", web.AddInfoBasicDefect)
	infoBasicDefect.PUTNeedAuth("更新", "", web.UpdateInfoBasicDefect)
	infoBasicDefect.PUTNeedAuth("更新状态", "updateStatus", web.UpdateInfoBasicDefectStatus)
	infoBasicDefect.DELETENeedAuth("删除", "", web.DeleteInfoBasicDefect)
	infoBasicDefect.GET("获取", "", web.GetInfoBasicDefect)
	infoBasicDefect.GET("获取列表", "list", web.GetInfoBasicDefectList)
	infoBasicDefect.GET("获取列表", "listEnum", web.GetInfoBasicDefectListEnum)

	// 账套用户收货地址
	{
		tenantReceiveAddr := bao.Group("tenantReceiveAddr")
		tenantReceiveAddr.POSTNeedAuth("新增", "", tenant.AddTenantReceiveAddr)
		tenantReceiveAddr.PUTNeedAuth("更新", "", tenant.UpdateTenantReceiveAddr)
		tenantReceiveAddr.PUTNeedAuth("更新状态", "status", tenant.UpdateTenantReceiveAddrStatus)
		tenantReceiveAddr.DELETENeedAuth("删除", "", tenant.DeleteTenantReceiveAddr)
		tenantReceiveAddr.GET("获取", "", tenant.GetTenantReceiveAddr)
		tenantReceiveAddr.GET("获取列表", "list", tenant.GetTenantReceiveAddrList)
		tenantReceiveAddr.GET("获取下拉列表", "list_enum", tenant.GetTenantReceiveAddrDropdownList)
	}

	{
		infoSaleShipmentType := bao.Group("infoSaleShipmentType")
		infoSaleShipmentType.POSTNeedAuth("新增", "addInfoSaleShipmentType", web.AddInfoSaleShipmentType)
		infoSaleShipmentType.PUTNeedAuth("更新", "updateInfoSaleShipmentType", web.UpdateInfoSaleShipmentType)
		infoSaleShipmentType.DELETENeedAuth("删除", "deleteInfoSaleShipmentType", web.DeleteInfoSaleShipmentType)
		infoSaleShipmentType.GET("获取", "getInfoSaleShipmentType", web.GetInfoSaleShipmentType)
		infoSaleShipmentType.GET("获取列表", "list", web.GetInfoSaleShipmentTypeList)
	}

}

// 账套用户收货地址
func MPInitInfoBasicData(routerGroup *system.RouterGroup) {
	bao := routerGroup.Group("info_basic_data")
	// 计量单位
	{
		infoBaseMeasurementUnit := bao.Group("infoBaseMeasurementUnit")
		infoBaseMeasurementUnit.POST("新增", "addInfoBaseMeasurementUnit", web.AddInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.PUT("更新", "updateInfoBaseMeasurementUnit", web.UpdateInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.PUT("更新默认单位", "updateIsDefault", web.UpdateInfoBaseMeasurementIsDefaultUnit)
		infoBaseMeasurementUnit.PUT("更新状态", "updateInfoBaseMeasurementUnitStatus", web.UpdateInfoBaseMeasurementUnitStatus)
		infoBaseMeasurementUnit.DELETE("删除", "deleteInfoBaseMeasurementUnit", web.DeleteInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.GET("获取", "getInfoBaseMeasurementUnit", web.GetInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.GET("获取列表", "getInfoBaseMeasurementUnitList", web.GetInfoBaseMeasurementUnitList)
		infoBaseMeasurementUnit.GET("获取枚举列表", "getInfoBaseMeasurementUnitEnumList", web.GetInfoBaseMeasurementUnitEnumList)
	}
	// 成品等级
	{
		infoBaseFinishedProductLevel := bao.Group("infoBaseFinishedProductLevel")
		infoBaseFinishedProductLevel.POST("新增", "addInfoBaseFinishedProductLevel", web.AddInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.PUT("更新", "updateInfoBaseFinishedProductLevel", web.UpdateInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.PUT("更新状态", "updateInfoBaseFinishedProductLevelStatus", web.UpdateInfoBaseFinishedProductLevelStatus)
		infoBaseFinishedProductLevel.DELETE("删除", "deleteInfoBaseFinishedProductLevel", web.DeleteInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.GET("获取", "getInfoBaseFinishedProductLevel", web.GetInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.GET("获取列表", "getInfoBaseFinishedProductLevelList", web.GetInfoBaseFinishedProductLevelList)
		infoBaseFinishedProductLevel.GET("获取枚举列表", "getInfoBaseFinishedProductLevelEnumList", web.GetInfoBaseFinishedProductLevelEnumList)
	}
	// 坯布等级
	{
		infoBaseGreyFabricLevel := bao.Group("infoBaseGreyFabricLevel")
		infoBaseGreyFabricLevel.POST("新增", "addInfoBaseGreyFabricLevel", web.AddInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.PUT("更新", "updateInfoBaseGreyFabricLevel", web.UpdateInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.PUT("更新状态", "updateInfoBaseGreyFabricLevelStatus", web.UpdateInfoBaseGreyFabricLevelStatus)
		infoBaseGreyFabricLevel.DELETE("删除", "deleteInfoBaseGreyFabricLevel", web.DeleteInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.GET("获取", "getInfoBaseGreyFabricLevel", web.GetInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.GET("获取列表", "getInfoBaseGreyFabricLevelList", web.GetInfoBaseGreyFabricLevelList)
		infoBaseGreyFabricLevel.GET("获取列表-其他地方使用", "getInfoBaseGreyFabricLevelListUseByOther", web.GetInfoBaseGreyFabricLevelListUseByOther)
	}
	// 坯布颜色
	{
		infoProductGrayFabricColor := bao.Group("infoProductGrayFabricColor")
		infoProductGrayFabricColor.POST("新增", "addInfoProductGrayFabricColor", web.AddInfoProductGrayFabricColor)
		infoProductGrayFabricColor.PUT("更新", "updateInfoProductGrayFabricColor", web.UpdateInfoProductGrayFabricColor)
		infoProductGrayFabricColor.PUT("更新状态", "updateInfoProductGrayFabricColorStatus", web.UpdateInfoProductGrayFabricColorStatus)
		infoProductGrayFabricColor.DELETE("删除", "deleteInfoProductGrayFabricColor", web.DeleteInfoProductGrayFabricColor)
		infoProductGrayFabricColor.GET("获取", "getInfoProductGrayFabricColor", web.GetInfoProductGrayFabricColor)
		infoProductGrayFabricColor.GET("获取列表", "getInfoProductGrayFabricColorList", web.GetInfoProductGrayFabricColorList)
		infoProductGrayFabricColor.GET("获取枚举列表", "getInfoProductGrayFabricColorEnumList", web.GetInfoProductGrayFabricColorEnumList)
	}

	// 原料等级
	{
		infoBaseRawMaterialLevel := bao.Group("infoBaseRawMaterialLevel")
		infoBaseRawMaterialLevel.POST("新增", "addInfoBaseRawMaterialLevel", web.AddInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.PUT("更新", "updateInfoBaseRawMaterialLevel", web.UpdateInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.PUT("更新状态", "updateInfoBaseRawMaterialLevelStatus", web.UpdateInfoBaseRawMaterialLevelStatus)
		infoBaseRawMaterialLevel.DELETE("删除", "deleteInfoBaseRawMaterialLevel", web.DeleteInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.GET("获取", "getInfoBaseRawMaterialLevel", web.GetInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.GET("获取列表", "getInfoBaseRawMaterialLevelList", web.GetInfoBaseRawMaterialLevelList)
		infoBaseRawMaterialLevel.GET("获取枚举列表", "getInfoBaseRawMaterialLevelEnumList", web.GetInfoBaseRawMaterialLevelEnumList)
	}
	// 烫染进度
	{
		infoDyeingFinishingProgress := bao.Group("infoDyeingFinishingProgress")
		infoDyeingFinishingProgress.POST("新增", "addInfoDyeingFinishingProgress", web.AddInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.PUT("更新", "updateInfoDyeingFinishingProgress", web.UpdateInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.PUT("更新状态", "updateInfoDyeingFinishingProgressStatus", web.UpdateInfoDyeingFinishingProgressStatus)
		infoDyeingFinishingProgress.DELETE("删除", "deleteInfoDyeingFinishingProgress", web.DeleteInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.GET("获取", "getInfoDyeingFinishingProgress", web.GetInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.GET("获取列表", "getInfoDyeingFinishingProgressList", web.GetInfoDyeingFinishingProgressList)
		infoDyeingFinishingProgress.GET("获取枚举列表", "getInfoDyeingFinishingProgressEnumList", web.GetInfoDyeingFinishingProgressEnumList)
	}
	// 染整工艺资料 info_dyeing_finishing_process_data
	{
		infoDyeingFinishingProcessData := bao.Group("infoDyeingFinishingProcessData")
		infoDyeingFinishingProcessData.POST("新增", "addInfoDyeingFinishingProcessData", web.AddInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.PUT("更新", "updateInfoDyeingFinishingProcessData", web.UpdateInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.PUT("更新状态", "updateInfoDyeingFinishingProcessDataStatus", web.UpdateInfoDyeingFinishingProcessDataStatus)
		infoDyeingFinishingProcessData.DELETE("删除", "deleteInfoDyeingFinishingProcessData", web.DeleteInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.GET("获取", "getInfoDyeingFinishingProcessData", web.GetInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.GET("获取列表", "getInfoDyeingFinishingProcessDataList", web.GetInfoDyeingFinishingProcessDataList)
		infoDyeingFinishingProcessData.GET("获取枚举列表", "getInfoDyeingFinishingProcessDataEnumList", web.GetInfoDyeingFinishingProcessDataEnumList)
	}

	// 染整工艺资料-分类 info_dyeing_finishing_process_data_type
	{
		infoDyeingFinishingProcessDataType := bao.Group("infoDyeingFinishingProcessDataType")
		infoDyeingFinishingProcessDataType.POST("新增", "addInfoDyeingFinishingProcessDataType", web.AddInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.PUT("更新", "updateInfoDyeingFinishingProcessDataType", web.UpdateInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.DELETE("删除", "deleteInfoDyeingFinishingProcessDataType", web.DeleteInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.GET("获取", "getInfoDyeingFinishingProcessDataType", web.GetInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.GET("获取列表", "getInfoDyeingFinishingProcessDataTypeList", web.GetInfoDyeingFinishingProcessDataTypeList)
	}

	// 流水方向
	{
		infoFinanceFlowDirection := bao.Group("infoFinanceFlowDirection")
		infoFinanceFlowDirection.POST("新增", "addInfoFinanceFlowDirection", web.AddInfoFinanceFlowDirection)
		infoFinanceFlowDirection.PUT("更新", "updateInfoFinanceFlowDirection", web.UpdateInfoFinanceFlowDirection)
		infoFinanceFlowDirection.PUT("更新状态", "updateInfoFinanceFlowDirectionStatus", web.UpdateInfoFinanceFlowDirectionStatus)
		infoFinanceFlowDirection.DELETE("删除", "deleteInfoFinanceFlowDirection", web.DeleteInfoFinanceFlowDirection)
		infoFinanceFlowDirection.GET("获取", "getInfoFinanceFlowDirection", web.GetInfoFinanceFlowDirection)
		infoFinanceFlowDirection.GET("获取列表", "getInfoFinanceFlowDirectionList", web.GetInfoFinanceFlowDirectionList)
		infoFinanceFlowDirection.GET("获取枚举列表", "getInfoFinanceFlowDirectionEnumList", web.GetInfoFinanceFlowDirectionEnumList)
	}

	// 发票抬头
	{
		infoPurchaseInvoiceHeader := bao.Group("infoPurchaseInvoiceHeader")
		infoPurchaseInvoiceHeader.POST("新增", "addInfoPurchaseInvoiceHeader", web.AddInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.PUT("更新", "updateInfoPurchaseInvoiceHeader", web.UpdateInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.PUT("更新状态", "updateInfoPurchaseInvoiceHeaderStatus", web.UpdateInfoPurchaseInvoiceHeaderStatus)
		infoPurchaseInvoiceHeader.DELETE("删除", "deleteInfoPurchaseInvoiceHeader", web.DeleteInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.GET("获取", "getInfoPurchaseInvoiceHeader", web.GetInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.GET("获取列表", "getInfoPurchaseInvoiceHeaderList", web.GetInfoPurchaseInvoiceHeaderList)
		infoPurchaseInvoiceHeader.GET("获取列表-其他地方使用", "getInfoPurchaseInvoiceHeaderListUseByOther", web.GetInfoPurchaseInvoiceHeaderListUseByOther)
	}

	// 织造规格
	{
		infoProductWeaveSpecification := bao.Group("infoProductWeaveSpecification")
		infoProductWeaveSpecification.POST("新增", "addInfoProductWeaveSpecification", web.AddInfoProductWeaveSpecification)
		infoProductWeaveSpecification.PUT("更新", "updateInfoProductWeaveSpecification", web.UpdateInfoProductWeaveSpecification)
		infoProductWeaveSpecification.PUT("更新状态", "updateInfoProductWeaveSpecificationStatus", web.UpdateInfoProductWeaveSpecificationStatus)
		infoProductWeaveSpecification.DELETE("删除", "deleteInfoProductWeaveSpecification", web.DeleteInfoProductWeaveSpecification)
		infoProductWeaveSpecification.GET("获取", "getInfoProductWeaveSpecification", web.GetInfoProductWeaveSpecification)
		infoProductWeaveSpecification.GET("获取列表", "getInfoProductWeaveSpecificationList", web.GetInfoProductWeaveSpecificationList)
		infoProductWeaveSpecification.GET("获取枚举列表", "getInfoProductWeaveSpecificationEnumList", web.GetInfoProductWeaveSpecificationEnumList)
	}

	// 织机机型
	{
		infoProductLoomModel := bao.Group("infoProductLoomModel")
		infoProductLoomModel.POST("新增", "addInfoProductLoomModel", web.AddInfoProductLoomModel)
		infoProductLoomModel.PUT("更新", "updateInfoProductLoomModel", web.UpdateInfoProductLoomModel)
		infoProductLoomModel.PUT("更新状态", "updateInfoProductLoomModelStatus", web.UpdateInfoProductLoomModelStatus)
		infoProductLoomModel.DELETE("删除", "deleteInfoProductLoomModel", web.DeleteInfoProductLoomModel)
		infoProductLoomModel.GET("获取", "getInfoProductLoomModel", web.GetInfoProductLoomModel)
		infoProductLoomModel.GET("获取列表", "getInfoProductLoomModelList", web.GetInfoProductLoomModelList)
		infoProductLoomModel.GET("获取枚举列表", "getInfoProductLoomModelEnumList", web.GetInfoProductLoomModelEnumList)
	}

	// 寸针数
	{
		infoProductNeedleSize := bao.Group("infoProductNeedleSize")
		infoProductNeedleSize.POST("新增", "addInfoProductNeedleSize", web.AddInfoProductNeedleSize)
		infoProductNeedleSize.PUT("更新", "updateInfoProductNeedleSize", web.UpdateInfoProductNeedleSize)
		infoProductNeedleSize.PUT("更新状态", "updateInfoProductNeedleSizeStatus", web.UpdateInfoProductNeedleSizeStatus)
		infoProductNeedleSize.DELETE("删除", "deleteInfoProductNeedleSize", web.DeleteInfoProductNeedleSize)
		infoProductNeedleSize.GET("获取", "getInfoProductNeedleSize", web.GetInfoProductNeedleSize)
		infoProductNeedleSize.GET("获取列表", "getInfoProductNeedleSizeList", web.GetInfoProductNeedleSizeList)
		infoProductNeedleSize.GET("获取枚举列表", "getInfoProductNeedleSizeEnumList", web.GetInfoProductNeedleSizeEnumList)
	}

	// 付款期限
	{
		infoProductPaymentTerm := bao.Group("infoProductPaymentTerm")
		infoProductPaymentTerm.POST("新增", "addInfoProductPaymentTerm", web.AddInfoProductPaymentTerm)
		infoProductPaymentTerm.PUT("更新", "updateInfoProductPaymentTerm", web.UpdateInfoProductPaymentTerm)
		infoProductPaymentTerm.PUT("更新状态", "updateInfoProductPaymentTermStatus", web.UpdateInfoProductPaymentTermStatus)
		infoProductPaymentTerm.DELETE("删除", "deleteInfoProductPaymentTerm", web.DeleteInfoProductPaymentTerm)
		infoProductPaymentTerm.GET("获取", "getInfoProductPaymentTerm", web.GetInfoProductPaymentTerm)
		infoProductPaymentTerm.GET("获取列表", "getInfoProductPaymentTermList", web.GetInfoProductPaymentTermList)
		infoProductPaymentTerm.GET("获取枚举列表", "getInfoProductPaymentTermEnumList", web.GetInfoProductPaymentTermEnumList)
	}

	// 产品来源
	{
		infoSaleFinishedProductSource := bao.Group("infoSaleFinishedProductSource")
		infoSaleFinishedProductSource.POST("新增", "addInfoSaleFinishedProductSource", web.AddInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.PUT("更新", "updateInfoSaleFinishedProductSource", web.UpdateInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.PUT("更新状态", "updateInfoSaleFinishedProductSourceStatus", web.UpdateInfoSaleFinishedProductSourceStatus)
		infoSaleFinishedProductSource.DELETE("删除", "deleteInfoSaleFinishedProductSource", web.DeleteInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.GET("获取", "getInfoSaleFinishedProductSource", web.GetInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.GET("获取列表", "getInfoSaleFinishedProductSourceList", web.GetInfoSaleFinishedProductSourceList)
		infoSaleFinishedProductSource.GET("获取枚举列表", "getInfoSaleFinishedProductSourceEnumList", web.GetInfoSaleFinishedProductSourceEnumList)
	}

	// 结算方式
	{
		infoSaleSettlementMethod := bao.Group("infoSaleSettlementMethod")
		infoSaleSettlementMethod.POST("新增", "addInfoSaleSettlementMethod", web.AddInfoSaleSettlementMethod)
		infoSaleSettlementMethod.PUT("更新", "updateInfoSaleSettlementMethod", web.UpdateInfoSaleSettlementMethod)
		infoSaleSettlementMethod.PUT("更新状态", "updateInfoSaleSettlementMethodStatus", web.UpdateInfoSaleSettlementMethodStatus)
		infoSaleSettlementMethod.DELETE("删除", "deleteInfoSaleSettlementMethod", web.DeleteInfoSaleSettlementMethod)
		infoSaleSettlementMethod.GET("获取", "getInfoSaleSettlementMethod", web.GetInfoSaleSettlementMethod)
		infoSaleSettlementMethod.GET("获取列表", "getInfoSaleSettlementMethodList", web.GetInfoSaleSettlementMethodList)
		infoSaleSettlementMethod.GET("获取枚举列表", "getInfoSaleSettlementMethodEnumList", web.GetInfoSaleSettlementMethodEnumList)
	}

	// 订单类别 info_sale_order_category
	{
		infoSaleOrderCategory := bao.Group("infoSaleOrderCategory")
		infoSaleOrderCategory.POST("新增", "addInfoSaleOrderCategory", web.AddInfoSaleOrderCategory)
		infoSaleOrderCategory.PUT("更新", "updateInfoSaleOrderCategory", web.UpdateInfoSaleOrderCategory)
		infoSaleOrderCategory.PUT("更新状态", "updateInfoSaleOrderCategoryStatus", web.UpdateInfoSaleOrderCategoryStatus)
		infoSaleOrderCategory.DELETE("删除", "deleteInfoSaleOrderCategory", web.DeleteInfoSaleOrderCategory)
		infoSaleOrderCategory.GET("获取", "getInfoSaleOrderCategory", web.GetInfoSaleOrderCategory)
		infoSaleOrderCategory.GET("获取列表", "getInfoSaleOrderCategoryList", web.GetInfoSaleOrderCategoryList)
		infoSaleOrderCategory.GET("获取枚举列表", "getInfoSaleOrderCategoryEnumList", web.GetInfoSaleOrderCategoryEnumList)
	}

	// 物流公司 info_sale_logistics_company
	{
		infoSaleLogisticsCompany := bao.Group("infoSaleLogisticsCompany")
		infoSaleLogisticsCompany.POST("新增", "addInfoSaleLogisticsCompany", web.AddInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.PUT("更新", "updateInfoSaleLogisticsCompany", web.UpdateInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.PUT("更新状态", "updateInfoSaleLogisticsCompanyStatus", web.UpdateInfoSaleLogisticsCompanyStatus)
		infoSaleLogisticsCompany.DELETE("删除", "deleteInfoSaleLogisticsCompany", web.DeleteInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.GET("获取", "getInfoSaleLogisticsCompany", web.GetInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.GET("获取（其他地方使用）", "getInfoSaleLogisticsCompanyUseOther", web.GetInfoSaleLogisticsCompanyUseOther)
		infoSaleLogisticsCompany.GET("获取列表", "getInfoSaleLogisticsCompanyList", web.GetInfoSaleLogisticsCompanyList)
		infoSaleLogisticsCompany.GET("获取枚举列表", "getInfoSaleLogisticsCompanyEnumList", web.GetInfoSaleLogisticsCompanyEnumList)
	}
	// 物流公司分类 info_sale_logistics_company_type
	{
		infoSaleLogisticsCompanyType := bao.Group("infoSaleLogisticsCompanyType")
		infoSaleLogisticsCompanyType.POST("新增", "addInfoSaleLogisticsCompanyType", web.AddInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.PUT("更新", "updateInfoSaleLogisticsCompanyType", web.UpdateInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.DELETE("删除", "deleteInfoSaleLogisticsCompanyType", web.DeleteInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.GET("获取", "getInfoSaleLogisticsCompanyType", web.GetInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.GET("获取列表", "getInfoSaleLogisticsCompanyTypeList", web.GetInfoSaleLogisticsCompanyTypeList)
	}

	// 缩水 info_sale_shrink
	{
		infoSaleShrink := bao.Group("infoSaleShrink")
		infoSaleShrink.POST("新增", "addInfoSaleShrink", web.AddInfoSaleShrink)
		infoSaleShrink.PUT("更新", "updateInfoSaleShrink", web.UpdateInfoSaleShrink)
		infoSaleShrink.PUT("更新状态", "updateInfoSaleShrinkStatus", web.UpdateInfoSaleShrinkStatus)
		infoSaleShrink.DELETE("删除", "deleteInfoSaleShrink", web.DeleteInfoSaleShrink)
		infoSaleShrink.GET("获取", "getInfoSaleShrink", web.GetInfoSaleShrink)
		infoSaleShrink.GET("获取列表", "getInfoSaleShrinkList", web.GetInfoSaleShrinkList)
		infoSaleShrink.GET("获取枚举列表", "getInfoSaleShrinkEnumList", web.GetInfoSaleShrinkEnumList)
	}

	// 日晒牢度 info_sale_light_fastness
	{
		infoSaleLightFastness := bao.Group("infoSaleLightFastness")
		infoSaleLightFastness.POST("新增", "addInfoSaleLightFastness", web.AddInfoSaleLightFastness)
		infoSaleLightFastness.PUT("更新", "updateInfoSaleLightFastness", web.UpdateInfoSaleLightFastness)
		infoSaleLightFastness.PUT("更新状态", "updateInfoSaleLightFastnessStatus", web.UpdateInfoSaleLightFastnessStatus)
		infoSaleLightFastness.DELETE("删除", "deleteInfoSaleLightFastness", web.DeleteInfoSaleLightFastness)
		infoSaleLightFastness.GET("获取", "getInfoSaleLightFastness", web.GetInfoSaleLightFastness)
		infoSaleLightFastness.GET("获取列表", "getInfoSaleLightFastnessList", web.GetInfoSaleLightFastnessList)
		infoSaleLightFastness.GET("获取枚举列表", "getInfoSaleLightFastnessEnumList", web.GetInfoSaleLightFastnessEnumList)
	}

	// 水洗 info_sale_wash
	{
		infoSaleWash := bao.Group("infoSaleWash")
		infoSaleWash.POST("新增", "addInfoSaleWash", web.AddInfoSaleWash)
		infoSaleWash.PUT("更新", "updateInfoSaleWash", web.UpdateInfoSaleWash)
		infoSaleWash.PUT("更新状态", "updateInfoSaleWashStatus", web.UpdateInfoSaleWashStatus)
		infoSaleWash.DELETE("删除", "deleteInfoSaleWash", web.DeleteInfoSaleWash)
		infoSaleWash.GET("获取", "getInfoSaleWash", web.GetInfoSaleWash)
		infoSaleWash.GET("获取列表", "getInfoSaleWashList", web.GetInfoSaleWashList)
		infoSaleWash.GET("获取枚举列表", "getInfoSaleWashEnumList", web.GetInfoSaleWashEnumList)
	}

	// 湿擦 info_sale_wet_wipe
	{
		infoSaleWetWipe := bao.Group("infoSaleWetWipe")
		infoSaleWetWipe.POST("新增", "addInfoSaleWetWipe", web.AddInfoSaleWetWipe)
		infoSaleWetWipe.PUT("更新", "updateInfoSaleWetWipe", web.UpdateInfoSaleWetWipe)
		infoSaleWetWipe.PUT("更新状态", "updateInfoSaleWetWipeStatus", web.UpdateInfoSaleWetWipeStatus)
		infoSaleWetWipe.DELETE("删除", "deleteInfoSaleWetWipe", web.DeleteInfoSaleWetWipe)
		infoSaleWetWipe.GET("获取", "getInfoSaleWetWipe", web.GetInfoSaleWetWipe)
		infoSaleWetWipe.GET("获取列表", "getInfoSaleWetWipeList", web.GetInfoSaleWetWipeList)
		infoSaleWetWipe.GET("获取枚举列表", "getInfoSaleWetWipeEnumList", web.GetInfoSaleWetWipeEnumList)
	}

	// 运费 info_sale_freight
	{
		infoSaleFreight := bao.Group("infoSaleFreight")
		infoSaleFreight.POST("新增", "addInfoSaleFreight", web.AddInfoSaleFreight)
		infoSaleFreight.PUT("更新", "updateInfoSaleFreight", web.UpdateInfoSaleFreight)
		infoSaleFreight.PUT("更新状态", "updateInfoSaleFreightStatus", web.UpdateInfoSaleFreightStatus)
		infoSaleFreight.DELETE("删除", "deleteInfoSaleFreight", web.DeleteInfoSaleFreight)
		infoSaleFreight.GET("获取", "getInfoSaleFreight", web.GetInfoSaleFreight)
		infoSaleFreight.GET("获取列表", "getInfoSaleFreightList", web.GetInfoSaleFreightList)
		infoSaleFreight.GET("获取枚举列表", "getInfoSaleFreightEnumList", web.GetInfoSaleFreightEnumList)
	}

	// 含税项目 info_sale_taxable_item
	{
		infoSaleTaxableItem := bao.Group("infoSaleTaxableItem")
		infoSaleTaxableItem.POST("新增", "addInfoSaleTaxableItem", web.AddInfoSaleTaxableItem)
		infoSaleTaxableItem.PUT("更新", "updateInfoSaleTaxableItem", web.UpdateInfoSaleTaxableItem)
		infoSaleTaxableItem.PUT("更新状态", "updateInfoSaleTaxableItemStatus", web.UpdateInfoSaleTaxableItemStatus)
		infoSaleTaxableItem.DELETE("删除", "deleteInfoSaleTaxableItem", web.DeleteInfoSaleTaxableItem)
		infoSaleTaxableItem.GET("获取", "getInfoSaleTaxableItem", web.GetInfoSaleTaxableItem)
		infoSaleTaxableItem.GET("获取列表", "getInfoSaleTaxableItemList", web.GetInfoSaleTaxableItemList)
		infoSaleTaxableItem.GET("获取枚举列表", "getInfoSaleTaxableItemEnumList", web.GetInfoSaleTaxableItemEnumList)
	}

	// 疵点 info_basic_defect
	infoBasicDefect := bao.Group("infoBasicDefect")
	infoBasicDefect.POST("新增", "", web.AddInfoBasicDefect)
	infoBasicDefect.PUT("更新", "", web.UpdateInfoBasicDefect)
	infoBasicDefect.PUT("更新状态", "updateStatus", web.UpdateInfoBasicDefectStatus)
	infoBasicDefect.DELETE("删除", "", web.DeleteInfoBasicDefect)
	infoBasicDefect.GET("获取", "", web.GetInfoBasicDefect)
	infoBasicDefect.GET("获取列表", "list", web.GetInfoBasicDefectList)
	infoBasicDefect.GET("获取列表", "listEnum", web.GetInfoBasicDefectListEnum)

	// bao := routerGroup.Group("info_basic_data")
	{
		tenantReceiveAddr := bao.Group("tenantReceiveAddr")
		tenantReceiveAddr.POST("新增", "", tenant.AddTenantReceiveAddr)
		tenantReceiveAddr.PUT("更新", "", tenant.UpdateTenantReceiveAddr)
		tenantReceiveAddr.PUT("更新状态", "status", tenant.UpdateTenantReceiveAddrStatus)
		tenantReceiveAddr.DELETE("删除", "", tenant.DeleteTenantReceiveAddr)
		tenantReceiveAddr.GET("获取", "", tenant.GetTenantReceiveAddr)
		tenantReceiveAddr.GET("获取列表", "list", tenant.GetTenantReceiveAddrList)
		tenantReceiveAddr.GET("获取下拉列表", "list_enum", tenant.GetTenantReceiveAddrDropdownList)
	}

	{
		infoSaleShipmentType := bao.Group("infoSaleShipmentType")
		infoSaleShipmentType.POSTNeedAuth("新增", "addInfoSaleShipmentType", web.AddInfoSaleShipmentType)
		infoSaleShipmentType.PUTNeedAuth("更新", "updateInfoSaleShipmentType", web.UpdateInfoSaleShipmentType)
		infoSaleShipmentType.DELETENeedAuth("删除", "deleteInfoSaleShipmentType", web.DeleteInfoSaleShipmentType)
		infoSaleShipmentType.GET("获取", "getInfoSaleShipmentType", web.GetInfoSaleShipmentType)
		infoSaleShipmentType.GET("获取列表", "list", web.GetInfoSaleShipmentTypeList)
	}
}

func H5InitInfoBasicData(routerGroup *system.RouterGroup) {
	bao := routerGroup.Group("info_basic_data")
	// 计量单位
	{
		infoBaseMeasurementUnit := bao.Group("infoBaseMeasurementUnit")
		infoBaseMeasurementUnit.POST("新增", "addInfoBaseMeasurementUnit", web.AddInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.PUT("更新", "updateInfoBaseMeasurementUnit", web.UpdateInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.PUT("更新默认单位", "updateIsDefault", web.UpdateInfoBaseMeasurementIsDefaultUnit)
		infoBaseMeasurementUnit.PUT("更新状态", "updateInfoBaseMeasurementUnitStatus", web.UpdateInfoBaseMeasurementUnitStatus)
		infoBaseMeasurementUnit.DELETE("删除", "deleteInfoBaseMeasurementUnit", web.DeleteInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.GET("获取", "getInfoBaseMeasurementUnit", web.GetInfoBaseMeasurementUnit)
		infoBaseMeasurementUnit.GET("获取列表", "getInfoBaseMeasurementUnitList", web.GetInfoBaseMeasurementUnitList)
		infoBaseMeasurementUnit.GET("获取枚举列表", "getInfoBaseMeasurementUnitEnumList", web.GetInfoBaseMeasurementUnitEnumList)
	}
	// 成品等级
	{
		infoBaseFinishedProductLevel := bao.Group("infoBaseFinishedProductLevel")
		infoBaseFinishedProductLevel.POST("新增", "addInfoBaseFinishedProductLevel", web.AddInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.PUT("更新", "updateInfoBaseFinishedProductLevel", web.UpdateInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.PUT("更新状态", "updateInfoBaseFinishedProductLevelStatus", web.UpdateInfoBaseFinishedProductLevelStatus)
		infoBaseFinishedProductLevel.DELETE("删除", "deleteInfoBaseFinishedProductLevel", web.DeleteInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.GET("获取", "getInfoBaseFinishedProductLevel", web.GetInfoBaseFinishedProductLevel)
		infoBaseFinishedProductLevel.GET("获取列表", "getInfoBaseFinishedProductLevelList", web.GetInfoBaseFinishedProductLevelList)
		infoBaseFinishedProductLevel.GET("获取枚举列表", "getInfoBaseFinishedProductLevelEnumList", web.GetInfoBaseFinishedProductLevelEnumList)
	}
	// 坯布等级
	{
		infoBaseGreyFabricLevel := bao.Group("infoBaseGreyFabricLevel")
		infoBaseGreyFabricLevel.POST("新增", "addInfoBaseGreyFabricLevel", web.AddInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.PUT("更新", "updateInfoBaseGreyFabricLevel", web.UpdateInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.PUT("更新状态", "updateInfoBaseGreyFabricLevelStatus", web.UpdateInfoBaseGreyFabricLevelStatus)
		infoBaseGreyFabricLevel.DELETE("删除", "deleteInfoBaseGreyFabricLevel", web.DeleteInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.GET("获取", "getInfoBaseGreyFabricLevel", web.GetInfoBaseGreyFabricLevel)
		infoBaseGreyFabricLevel.GET("获取列表", "getInfoBaseGreyFabricLevelList", web.GetInfoBaseGreyFabricLevelList)
		infoBaseGreyFabricLevel.GET("获取列表-其他地方使用", "getInfoBaseGreyFabricLevelListUseByOther", web.GetInfoBaseGreyFabricLevelListUseByOther)
	}
	// 坯布颜色
	{
		infoProductGrayFabricColor := bao.Group("infoProductGrayFabricColor")
		infoProductGrayFabricColor.POST("新增", "addInfoProductGrayFabricColor", web.AddInfoProductGrayFabricColor)
		infoProductGrayFabricColor.PUT("更新", "updateInfoProductGrayFabricColor", web.UpdateInfoProductGrayFabricColor)
		infoProductGrayFabricColor.PUT("更新状态", "updateInfoProductGrayFabricColorStatus", web.UpdateInfoProductGrayFabricColorStatus)
		infoProductGrayFabricColor.DELETE("删除", "deleteInfoProductGrayFabricColor", web.DeleteInfoProductGrayFabricColor)
		infoProductGrayFabricColor.GET("获取", "getInfoProductGrayFabricColor", web.GetInfoProductGrayFabricColor)
		infoProductGrayFabricColor.GET("获取列表", "getInfoProductGrayFabricColorList", web.GetInfoProductGrayFabricColorList)
		infoProductGrayFabricColor.GET("获取枚举列表", "getInfoProductGrayFabricColorEnumList", web.GetInfoProductGrayFabricColorEnumList)
	}

	// 原料等级
	{
		infoBaseRawMaterialLevel := bao.Group("infoBaseRawMaterialLevel")
		infoBaseRawMaterialLevel.POST("新增", "addInfoBaseRawMaterialLevel", web.AddInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.PUT("更新", "updateInfoBaseRawMaterialLevel", web.UpdateInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.PUT("更新状态", "updateInfoBaseRawMaterialLevelStatus", web.UpdateInfoBaseRawMaterialLevelStatus)
		infoBaseRawMaterialLevel.DELETE("删除", "deleteInfoBaseRawMaterialLevel", web.DeleteInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.GET("获取", "getInfoBaseRawMaterialLevel", web.GetInfoBaseRawMaterialLevel)
		infoBaseRawMaterialLevel.GET("获取列表", "getInfoBaseRawMaterialLevelList", web.GetInfoBaseRawMaterialLevelList)
		infoBaseRawMaterialLevel.GET("获取枚举列表", "getInfoBaseRawMaterialLevelEnumList", web.GetInfoBaseRawMaterialLevelEnumList)
	}
	// 烫染进度
	{
		infoDyeingFinishingProgress := bao.Group("infoDyeingFinishingProgress")
		infoDyeingFinishingProgress.POST("新增", "addInfoDyeingFinishingProgress", web.AddInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.PUT("更新", "updateInfoDyeingFinishingProgress", web.UpdateInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.PUT("更新状态", "updateInfoDyeingFinishingProgressStatus", web.UpdateInfoDyeingFinishingProgressStatus)
		infoDyeingFinishingProgress.DELETE("删除", "deleteInfoDyeingFinishingProgress", web.DeleteInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.GET("获取", "getInfoDyeingFinishingProgress", web.GetInfoDyeingFinishingProgress)
		infoDyeingFinishingProgress.GET("获取列表", "getInfoDyeingFinishingProgressList", web.GetInfoDyeingFinishingProgressList)
		infoDyeingFinishingProgress.GET("获取枚举列表", "getInfoDyeingFinishingProgressEnumList", web.GetInfoDyeingFinishingProgressEnumList)
	}
	// 染整工艺资料 info_dyeing_finishing_process_data
	{
		infoDyeingFinishingProcessData := bao.Group("infoDyeingFinishingProcessData")
		infoDyeingFinishingProcessData.POST("新增", "addInfoDyeingFinishingProcessData", web.AddInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.PUT("更新", "updateInfoDyeingFinishingProcessData", web.UpdateInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.PUT("更新状态", "updateInfoDyeingFinishingProcessDataStatus", web.UpdateInfoDyeingFinishingProcessDataStatus)
		infoDyeingFinishingProcessData.DELETE("删除", "deleteInfoDyeingFinishingProcessData", web.DeleteInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.GET("获取", "getInfoDyeingFinishingProcessData", web.GetInfoDyeingFinishingProcessData)
		infoDyeingFinishingProcessData.GET("获取列表", "getInfoDyeingFinishingProcessDataList", web.GetInfoDyeingFinishingProcessDataList)
		infoDyeingFinishingProcessData.GET("获取枚举列表", "getInfoDyeingFinishingProcessDataEnumList", web.GetInfoDyeingFinishingProcessDataEnumList)
	}

	// 染整工艺资料-分类 info_dyeing_finishing_process_data_type
	{
		infoDyeingFinishingProcessDataType := bao.Group("infoDyeingFinishingProcessDataType")
		infoDyeingFinishingProcessDataType.POST("新增", "addInfoDyeingFinishingProcessDataType", web.AddInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.PUT("更新", "updateInfoDyeingFinishingProcessDataType", web.UpdateInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.DELETE("删除", "deleteInfoDyeingFinishingProcessDataType", web.DeleteInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.GET("获取", "getInfoDyeingFinishingProcessDataType", web.GetInfoDyeingFinishingProcessDataType)
		infoDyeingFinishingProcessDataType.GET("获取列表", "getInfoDyeingFinishingProcessDataTypeList", web.GetInfoDyeingFinishingProcessDataTypeList)
	}

	// 流水方向
	{
		infoFinanceFlowDirection := bao.Group("infoFinanceFlowDirection")
		infoFinanceFlowDirection.POST("新增", "addInfoFinanceFlowDirection", web.AddInfoFinanceFlowDirection)
		infoFinanceFlowDirection.PUT("更新", "updateInfoFinanceFlowDirection", web.UpdateInfoFinanceFlowDirection)
		infoFinanceFlowDirection.PUT("更新状态", "updateInfoFinanceFlowDirectionStatus", web.UpdateInfoFinanceFlowDirectionStatus)
		infoFinanceFlowDirection.DELETE("删除", "deleteInfoFinanceFlowDirection", web.DeleteInfoFinanceFlowDirection)
		infoFinanceFlowDirection.GET("获取", "getInfoFinanceFlowDirection", web.GetInfoFinanceFlowDirection)
		infoFinanceFlowDirection.GET("获取列表", "getInfoFinanceFlowDirectionList", web.GetInfoFinanceFlowDirectionList)
		infoFinanceFlowDirection.GET("获取枚举列表", "getInfoFinanceFlowDirectionEnumList", web.GetInfoFinanceFlowDirectionEnumList)
	}

	// 发票抬头
	{
		infoPurchaseInvoiceHeader := bao.Group("infoPurchaseInvoiceHeader")
		infoPurchaseInvoiceHeader.POST("新增", "addInfoPurchaseInvoiceHeader", web.AddInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.PUT("更新", "updateInfoPurchaseInvoiceHeader", web.UpdateInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.PUT("更新状态", "updateInfoPurchaseInvoiceHeaderStatus", web.UpdateInfoPurchaseInvoiceHeaderStatus)
		infoPurchaseInvoiceHeader.DELETE("删除", "deleteInfoPurchaseInvoiceHeader", web.DeleteInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.GET("获取", "getInfoPurchaseInvoiceHeader", web.GetInfoPurchaseInvoiceHeader)
		infoPurchaseInvoiceHeader.GET("获取列表", "getInfoPurchaseInvoiceHeaderList", web.GetInfoPurchaseInvoiceHeaderList)
		infoPurchaseInvoiceHeader.GET("获取列表-其他地方使用", "getInfoPurchaseInvoiceHeaderListUseByOther", web.GetInfoPurchaseInvoiceHeaderListUseByOther)
	}

	// 织造规格
	{
		infoProductWeaveSpecification := bao.Group("infoProductWeaveSpecification")
		infoProductWeaveSpecification.POST("新增", "addInfoProductWeaveSpecification", web.AddInfoProductWeaveSpecification)
		infoProductWeaveSpecification.PUT("更新", "updateInfoProductWeaveSpecification", web.UpdateInfoProductWeaveSpecification)
		infoProductWeaveSpecification.PUT("更新状态", "updateInfoProductWeaveSpecificationStatus", web.UpdateInfoProductWeaveSpecificationStatus)
		infoProductWeaveSpecification.DELETE("删除", "deleteInfoProductWeaveSpecification", web.DeleteInfoProductWeaveSpecification)
		infoProductWeaveSpecification.GET("获取", "getInfoProductWeaveSpecification", web.GetInfoProductWeaveSpecification)
		infoProductWeaveSpecification.GET("获取列表", "getInfoProductWeaveSpecificationList", web.GetInfoProductWeaveSpecificationList)
		infoProductWeaveSpecification.GET("获取枚举列表", "getInfoProductWeaveSpecificationEnumList", web.GetInfoProductWeaveSpecificationEnumList)
	}

	// 织机机型
	{
		infoProductLoomModel := bao.Group("infoProductLoomModel")
		infoProductLoomModel.POST("新增", "addInfoProductLoomModel", web.AddInfoProductLoomModel)
		infoProductLoomModel.PUT("更新", "updateInfoProductLoomModel", web.UpdateInfoProductLoomModel)
		infoProductLoomModel.PUT("更新状态", "updateInfoProductLoomModelStatus", web.UpdateInfoProductLoomModelStatus)
		infoProductLoomModel.DELETE("删除", "deleteInfoProductLoomModel", web.DeleteInfoProductLoomModel)
		infoProductLoomModel.GET("获取", "getInfoProductLoomModel", web.GetInfoProductLoomModel)
		infoProductLoomModel.GET("获取列表", "getInfoProductLoomModelList", web.GetInfoProductLoomModelList)
		infoProductLoomModel.GET("获取枚举列表", "getInfoProductLoomModelEnumList", web.GetInfoProductLoomModelEnumList)
	}

	// 寸针数
	{
		infoProductNeedleSize := bao.Group("infoProductNeedleSize")
		infoProductNeedleSize.POST("新增", "addInfoProductNeedleSize", web.AddInfoProductNeedleSize)
		infoProductNeedleSize.PUT("更新", "updateInfoProductNeedleSize", web.UpdateInfoProductNeedleSize)
		infoProductNeedleSize.PUT("更新状态", "updateInfoProductNeedleSizeStatus", web.UpdateInfoProductNeedleSizeStatus)
		infoProductNeedleSize.DELETE("删除", "deleteInfoProductNeedleSize", web.DeleteInfoProductNeedleSize)
		infoProductNeedleSize.GET("获取", "getInfoProductNeedleSize", web.GetInfoProductNeedleSize)
		infoProductNeedleSize.GET("获取列表", "getInfoProductNeedleSizeList", web.GetInfoProductNeedleSizeList)
		infoProductNeedleSize.GET("获取枚举列表", "getInfoProductNeedleSizeEnumList", web.GetInfoProductNeedleSizeEnumList)
	}

	// 付款期限
	{
		infoProductPaymentTerm := bao.Group("infoProductPaymentTerm")
		infoProductPaymentTerm.POST("新增", "addInfoProductPaymentTerm", web.AddInfoProductPaymentTerm)
		infoProductPaymentTerm.PUT("更新", "updateInfoProductPaymentTerm", web.UpdateInfoProductPaymentTerm)
		infoProductPaymentTerm.PUT("更新状态", "updateInfoProductPaymentTermStatus", web.UpdateInfoProductPaymentTermStatus)
		infoProductPaymentTerm.DELETE("删除", "deleteInfoProductPaymentTerm", web.DeleteInfoProductPaymentTerm)
		infoProductPaymentTerm.GET("获取", "getInfoProductPaymentTerm", web.GetInfoProductPaymentTerm)
		infoProductPaymentTerm.GET("获取列表", "getInfoProductPaymentTermList", web.GetInfoProductPaymentTermList)
		infoProductPaymentTerm.GET("获取枚举列表", "getInfoProductPaymentTermEnumList", web.GetInfoProductPaymentTermEnumList)
	}

	// 产品来源
	{
		infoSaleFinishedProductSource := bao.Group("infoSaleFinishedProductSource")
		infoSaleFinishedProductSource.POST("新增", "addInfoSaleFinishedProductSource", web.AddInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.PUT("更新", "updateInfoSaleFinishedProductSource", web.UpdateInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.PUT("更新状态", "updateInfoSaleFinishedProductSourceStatus", web.UpdateInfoSaleFinishedProductSourceStatus)
		infoSaleFinishedProductSource.DELETE("删除", "deleteInfoSaleFinishedProductSource", web.DeleteInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.GET("获取", "getInfoSaleFinishedProductSource", web.GetInfoSaleFinishedProductSource)
		infoSaleFinishedProductSource.GET("获取列表", "getInfoSaleFinishedProductSourceList", web.GetInfoSaleFinishedProductSourceList)
		infoSaleFinishedProductSource.GET("获取枚举列表", "getInfoSaleFinishedProductSourceEnumList", web.GetInfoSaleFinishedProductSourceEnumList)
	}

	// 结算方式
	{
		infoSaleSettlementMethod := bao.Group("infoSaleSettlementMethod")
		infoSaleSettlementMethod.POST("新增", "addInfoSaleSettlementMethod", web.AddInfoSaleSettlementMethod)
		infoSaleSettlementMethod.PUT("更新", "updateInfoSaleSettlementMethod", web.UpdateInfoSaleSettlementMethod)
		infoSaleSettlementMethod.PUT("更新状态", "updateInfoSaleSettlementMethodStatus", web.UpdateInfoSaleSettlementMethodStatus)
		infoSaleSettlementMethod.DELETE("删除", "deleteInfoSaleSettlementMethod", web.DeleteInfoSaleSettlementMethod)
		infoSaleSettlementMethod.GET("获取", "getInfoSaleSettlementMethod", web.GetInfoSaleSettlementMethod)
		infoSaleSettlementMethod.GET("获取列表", "getInfoSaleSettlementMethodList", web.GetInfoSaleSettlementMethodList)
		infoSaleSettlementMethod.GET("获取枚举列表", "getInfoSaleSettlementMethodEnumList", web.GetInfoSaleSettlementMethodEnumList)
	}

	// 订单类别 info_sale_order_category
	{
		infoSaleOrderCategory := bao.Group("infoSaleOrderCategory")
		infoSaleOrderCategory.POST("新增", "addInfoSaleOrderCategory", web.AddInfoSaleOrderCategory)
		infoSaleOrderCategory.PUT("更新", "updateInfoSaleOrderCategory", web.UpdateInfoSaleOrderCategory)
		infoSaleOrderCategory.PUT("更新状态", "updateInfoSaleOrderCategoryStatus", web.UpdateInfoSaleOrderCategoryStatus)
		infoSaleOrderCategory.DELETE("删除", "deleteInfoSaleOrderCategory", web.DeleteInfoSaleOrderCategory)
		infoSaleOrderCategory.GET("获取", "getInfoSaleOrderCategory", web.GetInfoSaleOrderCategory)
		infoSaleOrderCategory.GET("获取列表", "getInfoSaleOrderCategoryList", web.GetInfoSaleOrderCategoryList)
		infoSaleOrderCategory.GET("获取枚举列表", "getInfoSaleOrderCategoryEnumList", web.GetInfoSaleOrderCategoryEnumList)
	}

	// 物流公司 info_sale_logistics_company
	{
		infoSaleLogisticsCompany := bao.Group("infoSaleLogisticsCompany")
		infoSaleLogisticsCompany.POST("新增", "addInfoSaleLogisticsCompany", web.AddInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.PUT("更新", "updateInfoSaleLogisticsCompany", web.UpdateInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.PUT("更新状态", "updateInfoSaleLogisticsCompanyStatus", web.UpdateInfoSaleLogisticsCompanyStatus)
		infoSaleLogisticsCompany.DELETE("删除", "deleteInfoSaleLogisticsCompany", web.DeleteInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.GET("获取", "getInfoSaleLogisticsCompany", web.GetInfoSaleLogisticsCompany)
		infoSaleLogisticsCompany.GET("获取（其他地方使用）", "getInfoSaleLogisticsCompanyUseOther", web.GetInfoSaleLogisticsCompanyUseOther)
		infoSaleLogisticsCompany.GET("获取列表", "getInfoSaleLogisticsCompanyList", web.GetInfoSaleLogisticsCompanyList)
		infoSaleLogisticsCompany.GET("获取枚举列表", "getInfoSaleLogisticsCompanyEnumList", web.GetInfoSaleLogisticsCompanyEnumList)
	}
	// 物流公司分类 info_sale_logistics_company_type
	{
		infoSaleLogisticsCompanyType := bao.Group("infoSaleLogisticsCompanyType")
		infoSaleLogisticsCompanyType.POST("新增", "addInfoSaleLogisticsCompanyType", web.AddInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.PUT("更新", "updateInfoSaleLogisticsCompanyType", web.UpdateInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.DELETE("删除", "deleteInfoSaleLogisticsCompanyType", web.DeleteInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.GET("获取", "getInfoSaleLogisticsCompanyType", web.GetInfoSaleLogisticsCompanyType)
		infoSaleLogisticsCompanyType.GET("获取列表", "getInfoSaleLogisticsCompanyTypeList", web.GetInfoSaleLogisticsCompanyTypeList)
	}

	// 缩水 info_sale_shrink
	{
		infoSaleShrink := bao.Group("infoSaleShrink")
		infoSaleShrink.POST("新增", "addInfoSaleShrink", web.AddInfoSaleShrink)
		infoSaleShrink.PUT("更新", "updateInfoSaleShrink", web.UpdateInfoSaleShrink)
		infoSaleShrink.PUT("更新状态", "updateInfoSaleShrinkStatus", web.UpdateInfoSaleShrinkStatus)
		infoSaleShrink.DELETE("删除", "deleteInfoSaleShrink", web.DeleteInfoSaleShrink)
		infoSaleShrink.GET("获取", "getInfoSaleShrink", web.GetInfoSaleShrink)
		infoSaleShrink.GET("获取列表", "getInfoSaleShrinkList", web.GetInfoSaleShrinkList)
		infoSaleShrink.GET("获取枚举列表", "getInfoSaleShrinkEnumList", web.GetInfoSaleShrinkEnumList)
	}

	// 日晒牢度 info_sale_light_fastness
	{
		infoSaleLightFastness := bao.Group("infoSaleLightFastness")
		infoSaleLightFastness.POST("新增", "addInfoSaleLightFastness", web.AddInfoSaleLightFastness)
		infoSaleLightFastness.PUT("更新", "updateInfoSaleLightFastness", web.UpdateInfoSaleLightFastness)
		infoSaleLightFastness.PUT("更新状态", "updateInfoSaleLightFastnessStatus", web.UpdateInfoSaleLightFastnessStatus)
		infoSaleLightFastness.DELETE("删除", "deleteInfoSaleLightFastness", web.DeleteInfoSaleLightFastness)
		infoSaleLightFastness.GET("获取", "getInfoSaleLightFastness", web.GetInfoSaleLightFastness)
		infoSaleLightFastness.GET("获取列表", "getInfoSaleLightFastnessList", web.GetInfoSaleLightFastnessList)
		infoSaleLightFastness.GET("获取枚举列表", "getInfoSaleLightFastnessEnumList", web.GetInfoSaleLightFastnessEnumList)
	}

	// 水洗 info_sale_wash
	{
		infoSaleWash := bao.Group("infoSaleWash")
		infoSaleWash.POST("新增", "addInfoSaleWash", web.AddInfoSaleWash)
		infoSaleWash.PUT("更新", "updateInfoSaleWash", web.UpdateInfoSaleWash)
		infoSaleWash.PUT("更新状态", "updateInfoSaleWashStatus", web.UpdateInfoSaleWashStatus)
		infoSaleWash.DELETE("删除", "deleteInfoSaleWash", web.DeleteInfoSaleWash)
		infoSaleWash.GET("获取", "getInfoSaleWash", web.GetInfoSaleWash)
		infoSaleWash.GET("获取列表", "getInfoSaleWashList", web.GetInfoSaleWashList)
		infoSaleWash.GET("获取枚举列表", "getInfoSaleWashEnumList", web.GetInfoSaleWashEnumList)
	}

	// 湿擦 info_sale_wet_wipe
	{
		infoSaleWetWipe := bao.Group("infoSaleWetWipe")
		infoSaleWetWipe.POST("新增", "addInfoSaleWetWipe", web.AddInfoSaleWetWipe)
		infoSaleWetWipe.PUT("更新", "updateInfoSaleWetWipe", web.UpdateInfoSaleWetWipe)
		infoSaleWetWipe.PUT("更新状态", "updateInfoSaleWetWipeStatus", web.UpdateInfoSaleWetWipeStatus)
		infoSaleWetWipe.DELETE("删除", "deleteInfoSaleWetWipe", web.DeleteInfoSaleWetWipe)
		infoSaleWetWipe.GET("获取", "getInfoSaleWetWipe", web.GetInfoSaleWetWipe)
		infoSaleWetWipe.GET("获取列表", "getInfoSaleWetWipeList", web.GetInfoSaleWetWipeList)
		infoSaleWetWipe.GET("获取枚举列表", "getInfoSaleWetWipeEnumList", web.GetInfoSaleWetWipeEnumList)
	}

	// 运费 info_sale_freight
	{
		infoSaleFreight := bao.Group("infoSaleFreight")
		infoSaleFreight.POST("新增", "addInfoSaleFreight", web.AddInfoSaleFreight)
		infoSaleFreight.PUT("更新", "updateInfoSaleFreight", web.UpdateInfoSaleFreight)
		infoSaleFreight.PUT("更新状态", "updateInfoSaleFreightStatus", web.UpdateInfoSaleFreightStatus)
		infoSaleFreight.DELETE("删除", "deleteInfoSaleFreight", web.DeleteInfoSaleFreight)
		infoSaleFreight.GET("获取", "getInfoSaleFreight", web.GetInfoSaleFreight)
		infoSaleFreight.GET("获取列表", "getInfoSaleFreightList", web.GetInfoSaleFreightList)
		infoSaleFreight.GET("获取枚举列表", "getInfoSaleFreightEnumList", web.GetInfoSaleFreightEnumList)
	}

	// 含税项目 info_sale_taxable_item
	{
		infoSaleTaxableItem := bao.Group("infoSaleTaxableItem")
		infoSaleTaxableItem.POST("新增", "addInfoSaleTaxableItem", web.AddInfoSaleTaxableItem)
		infoSaleTaxableItem.PUT("更新", "updateInfoSaleTaxableItem", web.UpdateInfoSaleTaxableItem)
		infoSaleTaxableItem.PUT("更新状态", "updateInfoSaleTaxableItemStatus", web.UpdateInfoSaleTaxableItemStatus)
		infoSaleTaxableItem.DELETE("删除", "deleteInfoSaleTaxableItem", web.DeleteInfoSaleTaxableItem)
		infoSaleTaxableItem.GET("获取", "getInfoSaleTaxableItem", web.GetInfoSaleTaxableItem)
		infoSaleTaxableItem.GET("获取列表", "getInfoSaleTaxableItemList", web.GetInfoSaleTaxableItemList)
		infoSaleTaxableItem.GET("获取枚举列表", "getInfoSaleTaxableItemEnumList", web.GetInfoSaleTaxableItemEnumList)
	}

	// 疵点 info_basic_defect
	infoBasicDefect := bao.Group("infoBasicDefect")
	infoBasicDefect.POST("新增", "", web.AddInfoBasicDefect)
	infoBasicDefect.PUT("更新", "", web.UpdateInfoBasicDefect)
	infoBasicDefect.PUT("更新状态", "updateStatus", web.UpdateInfoBasicDefectStatus)
	infoBasicDefect.DELETE("删除", "", web.DeleteInfoBasicDefect)
	infoBasicDefect.GET("获取", "", web.GetInfoBasicDefect)
	infoBasicDefect.GET("获取列表", "list", web.GetInfoBasicDefectList)
	infoBasicDefect.GET("获取列表", "listEnum", web.GetInfoBasicDefectListEnum)

	// bao := routerGroup.Group("info_basic_data")
	{
		tenantReceiveAddr := bao.Group("tenantReceiveAddr")
		tenantReceiveAddr.POST("新增", "", tenant.AddTenantReceiveAddr)
		tenantReceiveAddr.PUT("更新", "", tenant.UpdateTenantReceiveAddr)
		tenantReceiveAddr.PUT("更新状态", "status", tenant.UpdateTenantReceiveAddrStatus)
		tenantReceiveAddr.DELETE("删除", "", tenant.DeleteTenantReceiveAddr)
		tenantReceiveAddr.GET("获取", "", tenant.GetTenantReceiveAddr)
		tenantReceiveAddr.GET("获取列表", "list", tenant.GetTenantReceiveAddrList)
		tenantReceiveAddr.GET("获取下拉列表", "list_enum", tenant.GetTenantReceiveAddrDropdownList)
	}

	{
		infoSaleShipmentType := bao.Group("infoSaleShipmentType")
		infoSaleShipmentType.POST("新增", "addInfoSaleShipmentType", web.AddInfoSaleShipmentType)
		infoSaleShipmentType.PUT("更新", "updateInfoSaleShipmentType", web.UpdateInfoSaleShipmentType)
		infoSaleShipmentType.DELETE("删除", "deleteInfoSaleShipmentType", web.DeleteInfoSaleShipmentType)
		infoSaleShipmentType.GET("获取", "getInfoSaleShipmentType", web.GetInfoSaleShipmentType)
		infoSaleShipmentType.GET("获取列表", "list", web.GetInfoSaleShipmentTypeList)
	}
}
