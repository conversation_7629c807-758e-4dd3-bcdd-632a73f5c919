// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.29.1
// source: api/wx/group_robot/group_robot_service.proto

package v1

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_api_wx_group_robot_group_robot_service_proto protoreflect.FileDescriptor

var file_api_wx_group_robot_group_robot_service_proto_rawDesc = []byte{
	0x0a, 0x2c, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18,
	0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x1a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78,
	0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x32, 0xdf,
	0x04, 0x0a, 0x0a, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x78, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x12, 0x31, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78,
	0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x31, 0x2e, 0x77, 0x78,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f,
	0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22,
	0x00, 0x12, 0x78, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x31, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x2e, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x77,
	0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x0e,
	0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x12, 0x2f,
	0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2d, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x42, 0x33, 0x0a, 0x18, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x42, 0x0c, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e, 0x2f,
	0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var file_api_wx_group_robot_group_robot_service_proto_goTypes = []any{
	(*CreateGroupRobotRequest)(nil), // 0: wx.api.wx.group_robot.v1.CreateGroupRobotRequest
	(*UpdateGroupRobotRequest)(nil), // 1: wx.api.wx.group_robot.v1.UpdateGroupRobotRequest
	(*DeleteGroupRobotRequest)(nil), // 2: wx.api.wx.group_robot.v1.DeleteGroupRobotRequest
	(*GetGroupRobotRequest)(nil),    // 3: wx.api.wx.group_robot.v1.GetGroupRobotRequest
	(*ListGroupRobotRequest)(nil),   // 4: wx.api.wx.group_robot.v1.ListGroupRobotRequest
	(*CreateGroupRobotReply)(nil),   // 5: wx.api.wx.group_robot.v1.CreateGroupRobotReply
	(*UpdateGroupRobotReply)(nil),   // 6: wx.api.wx.group_robot.v1.UpdateGroupRobotReply
	(*DeleteGroupRobotReply)(nil),   // 7: wx.api.wx.group_robot.v1.DeleteGroupRobotReply
	(*GetGroupRobotReply)(nil),      // 8: wx.api.wx.group_robot.v1.GetGroupRobotReply
	(*ListGroupRobotReply)(nil),     // 9: wx.api.wx.group_robot.v1.ListGroupRobotReply
}
var file_api_wx_group_robot_group_robot_service_proto_depIdxs = []int32{
	0, // 0: wx.api.wx.group_robot.v1.GroupRobot.CreateGroupRobot:input_type -> wx.api.wx.group_robot.v1.CreateGroupRobotRequest
	1, // 1: wx.api.wx.group_robot.v1.GroupRobot.UpdateGroupRobot:input_type -> wx.api.wx.group_robot.v1.UpdateGroupRobotRequest
	2, // 2: wx.api.wx.group_robot.v1.GroupRobot.DeleteGroupRobot:input_type -> wx.api.wx.group_robot.v1.DeleteGroupRobotRequest
	3, // 3: wx.api.wx.group_robot.v1.GroupRobot.GetGroupRobot:input_type -> wx.api.wx.group_robot.v1.GetGroupRobotRequest
	4, // 4: wx.api.wx.group_robot.v1.GroupRobot.ListGroupRobot:input_type -> wx.api.wx.group_robot.v1.ListGroupRobotRequest
	5, // 5: wx.api.wx.group_robot.v1.GroupRobot.CreateGroupRobot:output_type -> wx.api.wx.group_robot.v1.CreateGroupRobotReply
	6, // 6: wx.api.wx.group_robot.v1.GroupRobot.UpdateGroupRobot:output_type -> wx.api.wx.group_robot.v1.UpdateGroupRobotReply
	7, // 7: wx.api.wx.group_robot.v1.GroupRobot.DeleteGroupRobot:output_type -> wx.api.wx.group_robot.v1.DeleteGroupRobotReply
	8, // 8: wx.api.wx.group_robot.v1.GroupRobot.GetGroupRobot:output_type -> wx.api.wx.group_robot.v1.GetGroupRobotReply
	9, // 9: wx.api.wx.group_robot.v1.GroupRobot.ListGroupRobot:output_type -> wx.api.wx.group_robot.v1.ListGroupRobotReply
	5, // [5:10] is the sub-list for method output_type
	0, // [0:5] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_api_wx_group_robot_group_robot_service_proto_init() }
func file_api_wx_group_robot_group_robot_service_proto_init() {
	if File_api_wx_group_robot_group_robot_service_proto != nil {
		return
	}
	file_api_wx_group_robot_group_robot_proto_init()
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_group_robot_group_robot_service_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_api_wx_group_robot_group_robot_service_proto_goTypes,
		DependencyIndexes: file_api_wx_group_robot_group_robot_service_proto_depIdxs,
	}.Build()
	File_api_wx_group_robot_group_robot_service_proto = out.File
	file_api_wx_group_robot_group_robot_service_proto_rawDesc = nil
	file_api_wx_group_robot_group_robot_service_proto_goTypes = nil
	file_api_wx_group_robot_group_robot_service_proto_depIdxs = nil
}
