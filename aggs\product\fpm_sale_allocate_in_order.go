package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	cus_error "hcscm/common/errors"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"time"
)

type FpmSaleAllocateInOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmSaleAllocateInOrderRepo(tx *mysql_base.Tx) *FpmSaleAllocateInOrderRepo {
	return &FpmSaleAllocateInOrderRepo{tx: tx}
}

func (r *FpmSaleAllocateInOrderRepo) Add(ctx context.Context, req *structure.AddFpmSaleAllocateInOrderParam) (data structure.AddFpmSaleAllocateInOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
	)

	fpmSaleAllocateInOrder := model.NewFpmSaleAllocateInOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmSaleAllocateInOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmSaleAllocateInOrder.BusinessClose = common_system.BusinessCloseNo
	fpmSaleAllocateInOrder.DepartmentId = info.GetDepartmentId()

	// 在营销体系中获取一部分前缀
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})

	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmSaleAllocateInOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmSaleAllocateInOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmSaleAllocateInOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmSaleAllocateInOrder.OrderNo = orderNo
	fpmSaleAllocateInOrder.Number = int(number)

	fpmSaleAllocateInOrder.TotalWeight, fpmSaleAllocateInOrder.TotalRoll, fpmSaleAllocateInOrder.TotalLength = req.GetTotalPWL()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleAllocateInOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleAllocateInOrder, err = mysql.MustCreateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmInOrderItem := model.NewFpmInOrderItem(ctx, &item)
		fpmInOrderItem.ParentId = fpmSaleAllocateInOrder.Id
		fpmInOrderItem.ParentOrderNo = fpmSaleAllocateInOrder.OrderNo
		fpmInOrderItem.WarehouseInType = fpmSaleAllocateInOrder.InOrderType
		tw, tp, tl, weightError, _, _, tpp, tsw, taw := item.GetTotalWPL(req.InOrderType)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmInOrderItem.TotalWeight = tw
		fpmInOrderItem.TotalPrice = tp
		fpmInOrderItem.InLength = tl
		fpmInOrderItem.WeightError = weightError
		//fpmInOrderItem.SettleErrorWeight = settleErrorWeight
		fpmInOrderItem.PaperTubeWeight = tpp
		fpmInOrderItem.SettleWeight = tsw
		fpmInOrderItem.ActuallyWeight = taw
		fpmInOrderItem, err = mysql.MustCreateFpmInOrderItem(r.tx, fpmInOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			// fineCode.DyeFactoryColorCode = fpmInOrderItem.DyeFactoryColorCode
			// fineCode.DyeFactoryDyelotNumber = fpmInOrderItem.DyeFactoryDyelotNumber
			itemFc := model.NewFpmInOrderItemFc(ctx, &fineCode)
			itemFc.ParentId = fpmInOrderItem.Id
			itemFc.WarehouseInType = cus_const.WarehouseGoodInTypeSaleAllocate
			itemFc.WarehouseInOrderId = fpmSaleAllocateInOrder.Id
			itemFc.WarehouseInOrderNo = fpmSaleAllocateInOrder.OrderNo
			itemFc.OrderTime = fpmSaleAllocateInOrder.WarehouseInTime
			itemFc.WarehouseId = fpmSaleAllocateInOrder.WarehouseId
			itemFc.SumStockId = item.SumStockId
			itemFc.WarehouseId = fpmSaleAllocateInOrder.WarehouseId
			if itemFc.ProductGramWeight == "" && itemFc.ProductWidth == "" && itemFc.FinishProductGramWeightUnitId == 0 && itemFc.FinishProductWidthUnitId == 0 {
				itemFc.ProductWidth = item.ProductWidth
				itemFc.ProductGramWeight = item.ProductGramWeight
				itemFc.FinishProductWidthUnitId = item.FinishProductWidthUnitId
				itemFc.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
			}
			// itemFc.GenBarQrCode(ctx, item.ProductCode, item.ProductColorCode)
			itemFc, err = mysql.MustCreateFpmInOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleAllocateInOrder.Id
	return
}

func (r *FpmSaleAllocateInOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmSaleAllocateInOrderParam) (data structure.UpdateFpmSaleAllocateInOrderData, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder
		itemModel              model.FpmInOrderItem
		findCodeModel          model.FpmInOrderItemFc
		itemList               model.FpmInOrderItemList
		fcList                 model.FpmInOrderItemFcList
	)
	fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmSaleAllocateInOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，当前单据状态不能更新。")
		return
	}

	fpmSaleAllocateInOrder.UpdateFpmSaleAllocateInOrder(ctx, req)

	fpmSaleAllocateInOrder.TotalWeight, fpmSaleAllocateInOrder.TotalRoll, fpmSaleAllocateInOrder.TotalLength = req.GetTotalPWL()

	if fpmSaleAllocateInOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmSaleAllocateInOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmSaleAllocateInOrder.UnitId = item.UnitId
			break
		}
	}

	fpmSaleAllocateInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		fcList, _ = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemIds)
		if len(fcList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
			if err != nil {
				return
			}
		}
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmInOrderItem := model.NewFpmInOrderItem(ctx, &item)
		fpmInOrderItem.ParentId = fpmSaleAllocateInOrder.Id
		fpmInOrderItem.ParentOrderNo = fpmSaleAllocateInOrder.OrderNo
		fpmInOrderItem.WarehouseInType = fpmSaleAllocateInOrder.InOrderType
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(0)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmInOrderItem.TotalWeight = tw
		fpmInOrderItem.TotalPrice = tp
		fpmInOrderItem.InLength = tl
		fpmInOrderItem.WeightError = weightError
		fpmInOrderItem.SettleErrorWeight = settleErrorWeight
		fpmInOrderItem.PaperTubeWeight = tpp
		fpmInOrderItem.SettleWeight = tsw
		fpmInOrderItem.ActuallyWeight = taw
		fpmInOrderItem, err = mysql.MustCreateFpmInOrderItem(r.tx, fpmInOrderItem)
		if err != nil {
			return
		}
		// oldItem := itemList.Pick(item.Id)
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			// oldFc := fcList.Pick(fineCode.Id)
			//fineCode.DyeFactoryColorCode = fpmInOrderItem.DyeFactoryColorCode
			//fineCode.DyeFactoryDyelotNumber = fpmInOrderItem.DyeFactoryDyelotNumber
			itemFc := model.NewFpmInOrderItemFc(ctx, &fineCode)
			itemFc.ArrangeItemFcId = fineCode.ArrangeItemFcId
			itemFc.ParentId = fpmInOrderItem.Id
			itemFc.WarehouseInType = cus_const.WarehouseGoodInTypeSaleAllocate
			itemFc.WarehouseInOrderId = fpmSaleAllocateInOrder.Id
			itemFc.WarehouseInOrderNo = fpmSaleAllocateInOrder.OrderNo
			itemFc.OrderTime = fpmSaleAllocateInOrder.WarehouseInTime
			itemFc.WarehouseId = fpmSaleAllocateInOrder.WarehouseId
			itemFc.SumStockId = item.SumStockId
			itemFc.WarehouseId = fpmSaleAllocateInOrder.WarehouseId
			if itemFc.ProductGramWeight == "" && itemFc.ProductWidth == "" && itemFc.FinishProductGramWeightUnitId == 0 && itemFc.FinishProductWidthUnitId == 0 {
				itemFc.ProductWidth = item.ProductWidth
				itemFc.ProductGramWeight = item.ProductGramWeight
				itemFc.FinishProductWidthUnitId = item.FinishProductWidthUnitId
				itemFc.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
			}
			// itemFc.GenBarQrCodeV2(ctx, item.ProductCode, item.ProductColorCode, item.ProductId, item.ProductColorId, oldItem.ProductId, oldItem.ProductColorId, oldFc)
			itemFc, err = mysql.MustCreateFpmInOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmSaleAllocateInOrder.Id
	return
}

func (r *FpmSaleAllocateInOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmSaleAllocateInOrderBusinessCloseParam) (data structure.UpdateFpmSaleAllocateInOrderData, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, v)
		if err != nil {
			return
		}
		// 更新业务状态
		err = fpmSaleAllocateInOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmSaleAllocateInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmSaleAllocateInOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, addItems structure.AddStockProductDetailParamList, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder

		outOrder model.FpmOutOrder
	)

	fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	err, addItems = r.judgeAuditPass(id, fpmSaleAllocateInOrder, ctx)
	if err != nil {
		return
	}

	// 出仓单未审核，进仓单不可以审核
	outOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, fpmSaleAllocateInOrder.SaleAllocateOutId)
	if err != nil {
		return
	}
	if outOrder.AuditStatus != common_system.OrderStatusAudited {
		err = middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeSrcOrderNoPassFalse, "操作失败，请先审核出仓单"))
		return structure.UpdateFpmSaleAllocateInOrderStatusData{}, addItems, err
	}

	// 审核
	err = fpmSaleAllocateInOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
	if err != nil {
		return
	}

	// 更新已经入仓库
	err = mysql.MustUpdateIsInWarehouseByOrderId(r.tx, fpmSaleAllocateInOrder.Id, true)
	if err != nil {
		return
	}

	data.WarehouseInTime = tools.MyTime(fpmSaleAllocateInOrder.WarehouseInTime)
	return
}

func (r *FpmSaleAllocateInOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, updateItems structure.UpdateStockProductDetailParamList, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder
	)

	fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	updateItems, err = r.judgeAuditWait(id, fpmSaleAllocateInOrder, ctx)
	if err != nil {
		return
	}

	// 消审
	err = fpmSaleAllocateInOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
	if err != nil {
		return
	}

	// 更新已经入仓库
	err = mysql.MustUpdateIsInWarehouseByOrderId(r.tx, fpmSaleAllocateInOrder.Id, false)
	if err != nil {
		return
	}

	data.WarehouseInTime = tools.MyTime(fpmSaleAllocateInOrder.WarehouseInTime)
	return
}

func (r *FpmSaleAllocateInOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder
	)

	fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 拒绝/驳回
	err = fpmSaleAllocateInOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmSaleAllocateInOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmSaleAllocateInOrderStatusData, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder
		alloOutOrder           model.FpmOutOrder
	)

	fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 判断生成该进进仓单的状态是不是非已审核
	alloOutOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, fpmSaleAllocateInOrder.SaleAllocateOutId)
	if err != nil {
		return
	}
	if alloOutOrder.AuditStatus == common_system.OrderStatusAudited {
		return structure.UpdateFpmSaleAllocateInOrderStatusData{}, cus_error.NewCustomError(cus_error.ErrCodeSrcOrderPassFalse, "操作失败，请先消审调拨出仓单")
	}

	// 作废
	err = fpmSaleAllocateInOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmSaleAllocateInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmSaleAllocateInOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmSaleAllocateInOrderRepo) Get(ctx context.Context, req *structure.GetFpmSaleAllocateInOrderQuery) (data structure.GetFpmSaleAllocateInOrderData, err error) {
	var (
		fpmSaleAllocateInOrder model.FpmInOrder
		itemDatas              model.FpmInOrderItemList
		fineCodeList           model.FpmInOrderItemFcList
		detailStockList        model.StockProductDetailList
		warehousePB            = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB                 = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc                 = dictionary.NewDictionaryClient()
	)
	fpmSaleAllocateInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmSaleAllocateInOrderData{}
	r.swapDetailModel2Data(fpmSaleAllocateInOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmInOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		// 添加细码信息
		fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTID(r.tx, itemData.Id)
		if err != nil {
			return
		}

		mUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "measurement_unit_id")
		wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
		stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitFcIds)
		binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
		detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
		if err != nil {
			return
		}
		dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
		dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

		for _, fineCode := range fineCodeList {
			fineCodeGetData := structure.GetFpmInOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			fineCodeGetData.SrcId = fineCode.SrcId
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.Length = fineCode.Length
			for _, v := range detailStockList {
				if fineCodeGetData.StockId == v.Id {
					fineCodeGetData.BarCode = v.BarCode
				}
			}
			// dst.SettleWeight = src.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			// dst.CustomerPoNum = src.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.DyelotNumber = fineCode.DyeFactoryDyelotNumber
			// 增加旧单判断，致盛旧单问题，如果重新更新才能去掉
			// if fineCode.DyeFactoryDyelotNumber != itemData.DyeFactoryDyelotNumber {
			// 	fineCodeGetData.DyeFactoryDyelotNumber = itemData.DyeFactoryDyelotNumber
			// 	fineCodeGetData.DyelotNumber = itemData.DyeFactoryDyelotNumber
			// }
			// if fineCode.DyeFactoryColorCode != itemData.DyeFactoryColorCode {
			//	fineCodeGetData.DyeFactoryColorCode = itemData.DyeFactoryColorCode
			// }
			// dst.StockRemark = src.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ArrangeItemFcId = fineCode.ArrangeItemFcId
			// dst.ActuallyWeight = src.ActuallyWeight
			// dst.SettleErrorWeight = src.SettleErrorWeight
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
			fineCodeGetData.WarehouseInTypeName = fineCodeGetData.WarehouseInType.String()
			detailStock := detailStockList.Pick(fineCode.StockId)

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)

			fineCodeGetData.GetFPWidthAndGramWeightAndUnitName()
			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmSaleAllocateInOrderRepo) GetList(ctx context.Context, req *structure.GetFpmInOrderListQuery) (list structure.GetFpmSaleAllocateInOrderDataList, total int, err error) {
	var (
		fpmSaleAllocateInOrders model.FpmInOrderList
		bizPB                   = biz_pb.NewClientBizUnitService()
		salePB                  = sale_sys_pb.NewSaleSystemClient()
		unitPB                  = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB             = warehouse_pb.NewPhysicalWarehouseClient()
		emplPB                  = empl_pb.NewClientEmployeeService()
		company                 = base_info_pb.NewInfoSaleLogisticsCompanyClient()
	)
	fpmSaleAllocateInOrders, total, err = mysql.SearchFpmInOrder(r.tx, req)
	if err != nil {
		return
	}

	unitIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "unit_id")
	unitNameMap, err1 := unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
	}

	saleAllocateOutOrderIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "sale_allocate_out_id")
	saleAllocateOutOrders, err1 := mysql.FindFpmOutOrderByIDs(r.tx, saleAllocateOutOrderIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
	}

	saleSysIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "sale_system_id")
	saleSysNameMap, err1 := salePB.GetSaleSystemByIds(ctx, saleSysIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
	}

	warehouseIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "warehouse_id")
	warehouseOutIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "warehouse_out_id")
	wareIds := tools.MergeSlicesUint64(warehouseIds, warehouseOutIds)
	wareNameMap, err1 := warehousePB.GetPhysicalWarehouseByIds(ctx, wareIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseByIds err"))
	}

	bizUnitIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "biz_unit_id")
	bizNameMap, err1 := bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
	}

	companyIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "logistics_company_id")
	companyNameMap, err1 := company.GetInfoSaleLogisticsCompanyNameByIds(ctx, companyIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetInfoSaleLogisticsCompanyNameByIds err"))
	}

	driverIdStrSlice := fpmSaleAllocateInOrders.GetDriverId2StringSlice()
	storeKeeperIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "store_keeper_id")
	saleUserIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "sale_user_id")
	followerIds := mysql_base.GetUInt64List(fpmSaleAllocateInOrders, "sale_follower_id")
	driverIds := fpmSaleAllocateInOrders.GetDriverIds()
	empIds := tools.MergeSlicesUint64(storeKeeperIds, saleUserIds, followerIds, driverIds)
	empNameMap, err1 := emplPB.GetEmployeeNameByIds(ctx, empIds)
	if err1 != nil {
		middleware.WarnLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
	}

	driverNameMap := tools.Uint64MapMergeStringMap(empNameMap, driverIdStrSlice, ",", ",")

	for _, src := range fpmSaleAllocateInOrders.List() {
		dst := structure.GetFpmSaleAllocateInOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.SaleAllocateOutId = src.SaleAllocateOutId
		dst.SaleAllocateOutOrderNo = src.SaleAllocateOutOrderNo
		dst.SaleSystemId = src.SaleSystemId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseOutId = src.WarehouseOutId
		dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
		dst.CustomerId = src.BizUnitId
		dst.StoreKeeperId = src.StoreKeeperId
		dst.SaleUserId = src.SaleUserId
		dst.SaleFollowerId = src.SaleFollowerId
		dst.DriverId = src.DriverId
		dst.LogisticsCompanyId = src.LogisticsCompanyId
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)

		// 转义
		outOrder := saleAllocateOutOrders.Pick(src.SaleAllocateOutId)
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.WarehouseOutName = wareNameMap[src.WarehouseOutId]
		dst.UnitName = unitNameMap[src.UnitId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.CustomerName = bizNameMap[src.BizUnitId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.ArrangeOrderId = outOrder.ArrangeOrderId
		dst.ArrangeOrderNo = outOrder.ArrangeOrderNo
		dst.DriverName = driverNameMap[src.DriverId]
		dst.SaleUserName = empNameMap[src.SaleUserId]
		dst.SaleFollowerName = empNameMap[src.SaleFollowerId]
		dst.LogisticsCompanyName = companyNameMap[src.LogisticsCompanyId]
		list = append(list, dst)
	}
	return
}
func (r *FpmSaleAllocateInOrderRepo) swapDetailModel2Data(src model.FpmInOrder, dst *structure.GetFpmSaleAllocateInOrderData, ctx context.Context) {
	var (
		bizService       = biz_pb.NewClientBizUnitService()
		saleSysPBSerbice = sale_sys_pb.NewSaleSystemClient()
		userName         = make(map[uint64]string)
		unitPB           = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB      = warehouse_pb.NewPhysicalWarehouseClient()
		driverName       string
		emplPB           = empl_pb.NewClientEmployeeService()
		company          = base_info_pb.NewInfoSaleLogisticsCompanyClient()
	)

	bizMap, err := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err != nil {
		fmt.Println(err)
	}
	saleSystemMap, err := saleSysPBSerbice.GetSaleSystemById(ctx, src.SaleSystemId)
	if err != nil {
		fmt.Println(err)
	}

	outOrder, err := mysql.MustFirstFpmOutOrderByID(r.tx, src.SaleAllocateOutId)

	companyName, _ := company.GetInfoSaleLogisticsCompanyNameById(r.tx.Context, src.LogisticsCompanyId)
	userName, _ = emplPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId, src.SaleUserId, src.SaleFollowerId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)
	warehouseOutName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseOutId)
	driverIds := tools.String2UintArray(src.DriverId, ",")
	if len(driverIds) != 0 {
		driverNameMap, _ := emplPB.GetEmployeeNameByIds(r.tx.Context, driverIds)
		driverName = tools.GetMapValAppend2String(driverNameMap, ",")
	}

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.SaleAllocateOutId = src.SaleAllocateOutId
	dst.SaleAllocateOutOrderNo = src.SaleAllocateOutOrderNo
	dst.SaleSystemId = src.SaleSystemId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseOutId = src.WarehouseOutId
	dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
	dst.CustomerId = src.BizUnitId
	dst.StoreKeeperId = src.StoreKeeperId
	dst.SaleUserId = src.SaleUserId
	dst.SaleFollowerId = src.SaleFollowerId
	dst.DriverId = src.DriverId
	dst.LogisticsCompanyId = src.LogisticsCompanyId
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.WarehouseName = warehouseName
	dst.WarehouseOutName = warehouseOutName
	dst.UnitName = unitName
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.CustomerName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	dst.ArrangeOrderId = outOrder.ArrangeOrderId
	dst.ArrangeOrderNo = outOrder.ArrangeOrderNo
	dst.DriverName = driverName
	dst.SaleUserName = userName[src.SaleUserId]
	dst.SaleFollowerName = userName[src.SaleFollowerId]
	dst.LogisticsCompanyName = companyName
	dst.SaleAllocateOutId = src.SaleAllocateOutId
}

func (r *FpmSaleAllocateInOrderRepo) swapItemModel2Data(src model.FpmInOrderItem, dst *structure.GetFpmInOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
		productSvc = product.NewProductClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)
	productMap, _ := productSvc.GetProductMapByIds(ctx, []uint64{src.ProductId})

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.SumStockId = src.SumStockId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.QuoteOrderNo = src.QuoteOrderNo
	dst.QuoteOrderItemId = src.QuoteOrderItemId
	dst.ProductId = src.ProductId
	if product, ok := productMap[src.ProductId]; ok {
		dst.ProductCode = product.FinishProductCode
		dst.ProductName = product.FinishProductName
		dst.ProductCraft = product.FinishProductCraft
		dst.ProductIngredient = product.FinishProductIngredient
		dst.FinishProductWidthUnitId = product.FinishProductWidthAndWightUnit.FinishProductWidthUnitId
		dst.FinishProductWidthUnitName = product.FinishProductWidthAndWightUnit.FinishProductWidthUnitName
		dst.FinishProductWidthAndUnitName = product.FinishProductWidthAndWightUnit.FinishProductWidthAndUnitName
		dst.FinishProductGramWeightUnitId = product.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitId
		dst.FinishProductGramWeightUnitName = product.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitName
		dst.FinishProductGramWeightAndUnitName = product.FinishProductWidthAndWightUnit.FinishProductGramWeightAndUnitName
	}
	dst.CustomerId = src.CustomerId
	dst.ProductColorId = src.ProductColorId
	dst.ProductColorCode = src.ProductColorCode
	dst.ProductColorName = src.ProductColorName
	dst.ProductLevelId = src.ProductLevelId
	dst.ProductWidth = src.ProductWidth
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductGramWeight = src.ProductGramWeight
	dst.ProductRemark = src.ProductRemark
	dst.InRoll = src.InRoll
	dst.SumStockId = src.SumStockId
	dst.TotalWeight = src.TotalWeight
	dst.WeightError = src.WeightError
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.SettleWeight = src.SettleWeight
	dst.UnitId = src.UnitId
	dst.InLength = src.InLength
	dst.Remark = src.Remark
	dst.ActuallyWeight = src.ActuallyWeight
	dst.SettleErrorWeight = src.SettleErrorWeight
	dst.ArrangeOrderItemId = src.ArrangeOrderItemId

	// 转义

	dst.UnitName = unitName
	if val, ok := customerMap[src.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName
	outItem, _, _ := mysql.FirstFpmOutOrderItemByID(r.tx, dst.QuoteOrderItemId)
	// 调拨信息
	dst.AllocateRoll = outItem.OutRoll
	dst.AllocateWeight = outItem.TotalWeight
	dst.AllocateLength = outItem.OutLength

}

func (r *FpmSaleAllocateInOrderRepo) swapFcModel2Data(fineCode model.FpmInOrderItemFc, fineCodeGetData *structure.GetFpmInOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, fineCode.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, fineCode.UnitId)

	fineCodeGetData.Id = fineCode.Id
	fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
	fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
	fineCodeGetData.CreatorId = fineCode.CreatorId
	fineCodeGetData.CreatorName = fineCode.CreatorName
	fineCodeGetData.UpdaterId = fineCode.UpdaterId
	fineCodeGetData.UpdateUserName = fineCode.UpdaterName
	fineCodeGetData.ParentId = fineCode.ParentId
	fineCodeGetData.Roll = fineCode.Roll
	fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
	fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
	fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
	fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
	fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
	fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
	fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
	fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
	fineCodeGetData.StockId = fineCode.StockId
	fineCodeGetData.SumStockId = fineCode.SumStockId
	fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
	fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
	fineCodeGetData.WeightError = fineCode.WeightError
	fineCodeGetData.UnitId = fineCode.UnitId
	fineCodeGetData.Length = fineCode.Length
	// dst.SettleWeight = src.SettleWeight
	fineCodeGetData.DigitalCode = fineCode.DigitalCode
	fineCodeGetData.ShelfNo = fineCode.ShelfNo
	fineCodeGetData.ContractNumber = fineCode.ContractNumber
	// dst.CustomerPoNum = src.CustomerPoNum
	fineCodeGetData.AccountNum = fineCode.AccountNum
	fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
	fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
	fineCodeGetData.DyelotNumber = fineCode.DyeFactoryDyelotNumber
	fineCodeGetData.ProductWidth = fineCode.ProductWidth
	fineCodeGetData.ProductGramWeight = fineCode.ProductGramWeight
	// dst.StockRemark = src.StockRemark
	fineCodeGetData.Remark = fineCode.Remark
	fineCodeGetData.InternalRemark = fineCode.InternalRemark
	fineCodeGetData.ScanUserId = fineCode.ScanUserId
	fineCodeGetData.ScanUserName = fineCode.ScanUserName
	fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
	fineCodeGetData.WarehouseId = fineCode.WarehouseId
	// dst.ActuallyWeight = src.ActuallyWeight
	// dst.SettleErrorWeight = src.SettleErrorWeight
	fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
	fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
	fineCodeGetData.SettleWeight = fineCode.SettleWeight

	// 转义
	fineCodeGetData.WarehouseBinName = binName
	fineCodeGetData.UnitName = unitName
	fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
}

func (r *FpmSaleAllocateInOrderRepo) judgeAuditPass(id uint64, order model.FpmInOrder, ctx context.Context) (err error, addItems structure.AddStockProductDetailParamList) {
	var (
		fineCodeList         = model.FpmInOrderItemFcList{}
		addWeight            = make([]*structure.AddStockProductDetailParam, 0)
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		stockDetailList      = make(model.StockProductDetailList, 0)
	)

	// 判断成品数量是否符合

	swap2StockFieldParam.WarehouseInType = cus_const.WarehouseGoodInTypeSaleAllocate
	swap2StockFieldParam.WarehouseInOrderNo = order.OrderNo
	swap2StockFieldParam.WarehouseInOrderId = order.Id
	swap2StockFieldParam.WarehouseId = order.WarehouseId

	itemList, _ := mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	for _, item := range itemList {
		var fpmOutOrderItemFcs model.FpmOutOrderItemFcList
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		totalRoll := 0
		totalLength := 0

		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]

		fineCodeList, _ = mysql.FindFpmInOrderItemFcByParenTID(r.tx, item.Id)
		if item.InRoll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(cus_error.NewError(cus_error.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(fineCodeList) == 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
			return
		}

		stockDetail := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		stockDetailList, err = mysql.FindStockProductDetailByIDs(r.tx, stockDetail)
		if err != nil {
			return
		}
		fpmOutOrderItemFcIds := make([]uint64, 0, len(fineCodeList))
		for _, fineCode := range fineCodeList {
			fpmOutOrderItemFcIds = append(fpmOutOrderItemFcIds, fineCode.SrcId)
		}
		fpmOutOrderItemFcs, err = mysql.FindFpmOutOrderItemFcByIDs(r.tx, fpmOutOrderItemFcIds)
		if err != nil {
			return
		}
		for _, fineCode := range fineCodeList {
			totalRoll = totalRoll + fineCode.Roll
			totalLength += fineCode.Length
			detailStock := stockDetailList.Pick(fineCode.StockId)
			{
				swap2StockFieldParam.FinishProductWidthUnitId = detailStock.FinishProductWidthUnitId
				swap2StockFieldParam.FinishProductGramWeightUnitId = detailStock.FinishProductGramWeightUnitId
				swap2StockFieldParam.OutStockProductDetailId = fpmOutOrderItemFcs.Pick(fineCode.SrcId).StockId
			}
			weight := fineCode.ToAddStockProductDetailParam(ctx, swap2StockFieldParam)
			weight.BookWeight = weight.Weight
			weight.BookRoll = weight.Roll
			weight.WarehouseInOrderId = order.Id                               // 进仓单ID
			weight.WarehouseInOrderNo = order.OrderNo                          // 进仓单号
			weight.SrcId = order.SrcId                                         // 销售单ID（来源ID）
			weight.WarehouseInType = cus_const.WarehouseGoodInTypeSaleAllocate // 来源类型为销售调拨
			weight.SupplierId = detailStock.SupplierId
			addWeight = append(addWeight, weight)
		}

		if totalRoll != item.InRoll {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.InLength > 0 && totalLength > 0 && item.InLength != totalLength {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
	}
	addItems = addWeight
	return
}

func (r *FpmSaleAllocateInOrderRepo) judgeAuditWait(id uint64, fpmSaleAllocateInOrder model.FpmInOrder, ctx context.Context) (updateItems structure.UpdateStockProductDetailParamList, err error) {

	var (
		updateWeight         = make([]*structure.UpdateStockProductDetailParam, 0)
		updateBookWeight     = make([]*structure.UpdateStockProductDetailParam, 0)
		itemList             = model.FpmInOrderItemList{}
		swap2StockFieldParam = structure.Swap2StockFieldParam{}
		//arrangeItemList  = model.FpmArrangeOrderItemList{}
	)
	// 判断成品数量是否符合
	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}
	//arrangeItemIds := mysql_base.GetUInt64List(itemList, "arrange_order_item_id")
	//if len(arrangeItemIds) > 0 {
	//	arrangeItemList, err = mysql.FindFpmArrangeOrderItemByIDs(r.tx, arrangeItemIds)
	//}

	for _, item := range itemList {
		// 补充扣减出仓预约数
		if item.ArrangeOrderItemId != 0 {
			//arrangeItem := arrangeItemList.Pick(item.ArrangeOrderItemId)
			var updateItemWeight = &structure.UpdateStockProductDetailParam{}
			updateItemWeight.StockProductId = item.SumStockId
			updateItemWeight.BookRoll += -item.InRoll
			updateItemWeight.BookWeight += -item.TotalWeight
			updateItemWeight.Type = 4
			updateItemWeight.OrderId = fpmSaleAllocateInOrder.Id
			updateItemWeight.OrderNo = fpmSaleAllocateInOrder.OrderNo
			updateItemWeight.OrderType = common_system.BookOrderTypeProductSaleAlloInWait
			updateItemWeight.StockProductKey = fmt.Sprintf("%v", updateItemWeight.StockProductId)
			updateBookWeight = append(updateBookWeight, updateItemWeight)
		}
		swap2StockFieldParam.WarehouseInType = fpmSaleAllocateInOrder.InOrderType
		swap2StockFieldParam.WarehouseInOrderNo = fpmSaleAllocateInOrder.OrderNo
		swap2StockFieldParam.WarehouseInOrderId = fpmSaleAllocateInOrder.Id
		swap2StockFieldParam.WarehouseId = fpmSaleAllocateInOrder.WarehouseId
		fineCodeList, _ := mysql.FindFpmInOrderItemFcByParenTID(r.tx, item.Id)
		for _, fc := range fineCodeList {
			productColor := productColorItem.PickByProductColorId(item.ProductColorId)
			swap2StockFieldParam.ProductId = item.ProductId
			swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
			swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
			swap2StockFieldParam.ProductColorId = item.ProductColorId
			swap2StockFieldParam.CustomerId = item.CustomerId
			swap2StockFieldParam.ProductLevelId = item.ProductLevelId
			swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
			swap2StockFieldParam.ItemProductRemark = item.ProductRemark
			swap2StockFieldParam.MeasurementUnitId = item.UnitId
			swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
			updateWeight = append(updateWeight, fc.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam, true))
		}
	}
	_, exist, err := mysql.FirstFpmSaleOutOrderByAlloInID(r.tx, id)
	if exist {
		return updateItems, cus_error.NewCustomError(cus_error.ErrCodeTheOrderIsQuoted, "操作失败,请先取消成品销售出仓单")
	}
	updateWeight = append(updateBookWeight, updateWeight...)
	updateItems = updateWeight
	return
}
