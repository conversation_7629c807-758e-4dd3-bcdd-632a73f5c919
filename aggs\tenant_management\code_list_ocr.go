package tenant_management

import (
	"context"
	"gorm.io/gorm"
	EmployeeRepo "hcscm/aggs/employee"
	payRepo "hcscm/aggs/pay_record"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/extern/pb/dictionary"
	mysqlEmployee "hcscm/model/mysql/employee"
	"hcscm/model/mysql/mysql_base"
	payRecordDao "hcscm/model/mysql/pay_record/dao"
	mysqlSystem "hcscm/model/mysql/system"
	userDao "hcscm/model/mysql/system/dao"
	mysql "hcscm/model/mysql/tenant_management"
	tenantManagementDao "hcscm/model/mysql/tenant_management/dao"
	"hcscm/msg/msg_publish"
	structure "hcscm/structure/tenant_management"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"strings"
	"time"
)

type ICodeListOrcManagementRepo interface {
	SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error)
	GetDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetCodeListOrcManagementQuery) (data structure.GetCodeListOrcManagementData, err error)
	GetMPDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetCodeListOrcManagementQuery) (data structure.GetMPCodeListOrcManagementData, err error)
	UpdateCompanyName(ctx context.Context, tx *mysql_base.Tx, param structure.UpdateCodeListOrcManagementNameParam) (data structure.ResTenantManagementIDData, err error)
	// 启用禁用ocr识别功能
	UpdateStatusEnable(ctx context.Context, tx *mysql_base.Tx, param structure.EnableCodeListOrcManagementParam) (err error)
	UpdateStatusDisable(ctx context.Context, tx *mysql_base.Tx, param structure.DisableCodeListOrcManagementParam) (err error)
	// 启用禁用电子色卡功能
	UpdateStatusEnableEleColorCard(ctx context.Context, tx *mysql_base.Tx, param structure.EnableCodeListOrcManagementParam) (err error)
	UpdateStatusDisableEleColorCard(ctx context.Context, tx *mysql_base.Tx, param structure.DisableCodeListOrcManagementParam) (err error)
	// 启用禁用搜索图片功能
	UpdateStatusSearchImage(ctx context.Context, tx *mysql_base.Tx, param structure.EnableSearchImageParam) (err error)

	Recharge(ctx context.Context, tx *mysql_base.Tx, param structure.RechargeParam) (data structure.RechargeData, err error)
	EleColorCardServerRecharge(ctx context.Context, tx *mysql_base.Tx, param structure.RechargeParam) (data structure.RechargeData, err error)
	GetRechargeHistorys(ctx context.Context, tx *mysql_base.Tx, query structure.RechargeHistoryListQuery) (list structure.GetRechargeHistoryListDataList, total int, err error)
	IsOcrExpired(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isExpired bool, err error)
	IsEleColorCardExpired(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isExpired bool, err error)
	IsSearchImageExpired(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isExpired bool, err error)
	IsRecharged(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isRecharged bool, err error)
}
type codeListOrcManagementRepo struct {
	codeListOrcManagementDao      tenantManagementDao.ICodeListOrcManagementDao
	tenantManagementPackageRelDao tenantManagementDao.ITenantManagementPackageRelDao
	tenantManagementUserRelDao    tenantManagementDao.ITenantManagementUserRelDao
	tenantPackageDao              tenantManagementDao.ITenantPackageDao
	payRecordRepo                 payRecordDao.IPayRecordDao
	userDao                       userDao.IUserDao
	aliPayRepo                    payRepo.IPayRepo
	rechargeHistoryDao            tenantManagementDao.IRechargeHistoryDao
}

func NewCodeListOrcManagementRepo() ICodeListOrcManagementRepo {
	return &codeListOrcManagementRepo{
		codeListOrcManagementDao:      tenantManagementDao.NewCodeListOrcManagementDao(),
		tenantManagementPackageRelDao: tenantManagementDao.NewTenantManagementPackageRelDao(),
		tenantManagementUserRelDao:    tenantManagementDao.NewTenantManagementUserRelDao(),
		tenantPackageDao:              tenantManagementDao.NewTenantPackageDao(),
		payRecordRepo:                 payRecordDao.NewPayRecordDao(),
		userDao:                       userDao.NewUserDao(),
		aliPayRepo:                    payRepo.NewPayRepo(),
		rechargeHistoryDao:            tenantManagementDao.NewRechargeHistoryDao(),
	}
}

func (repo *codeListOrcManagementRepo) SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error) {
	var tenantManagements mysql.TenantManagementList

	tenantManagements, total, err = repo.codeListOrcManagementDao.SearchList(ctx, tx, query)
	if err != nil {
		return
	}

	list = make(structure.GetTenantManagementListDataList, 0)
	for _, codeListOrcManagement := range tenantManagements {
		list = append(list, structure.GetTenantManagementListData{
			Id:                         codeListOrcManagement.Id,
			CompanyName:                codeListOrcManagement.TenantCompanyName,
			Phone:                      codeListOrcManagement.TenantPhoneNumber,
			CreateTime:                 tools.MyTime(codeListOrcManagement.CreateTime),
			CreatorName:                codeListOrcManagement.CreatorName,
			UpdateTime:                 tools.MyTime(codeListOrcManagement.UpdateTime),
			UpdateUserName:             codeListOrcManagement.UpdaterName,
			ActivationTime:             tools.MyTime(codeListOrcManagement.ActivationTime),
			Deadline:                   tools.MyTime(codeListOrcManagement.Deadline),
			TenantManagementStatus:     codeListOrcManagement.TenantManagementStatus,
			TenantManagementStatusName: codeListOrcManagement.TenantManagementStatus.String(),
			Contacts:                   codeListOrcManagement.TenantContacts,

			CodeListOrcDeadLine:         tools.MyTime(codeListOrcManagement.CodeListOrcDeadLine),
			CodeListOrcStatus:           codeListOrcManagement.CodeListOrcStatus,
			ElectronicColorCardStatus:   codeListOrcManagement.ElectronicColorCardStatus,
			ElectronicColorCardDeadLine: tools.MyTime(codeListOrcManagement.ElectronicColorCardDeadLine),
			SearchImageStatus:           codeListOrcManagement.SearchImageStatus,
			SearchImageDeadLine:         tools.MyTime(codeListOrcManagement.SearchImageDeadLine),
		})
	}
	return
}

func (repo *codeListOrcManagementRepo) GetDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetCodeListOrcManagementQuery) (data structure.GetCodeListOrcManagementData, err error) {
	var (
		tenantManagement mysql.TenantManagement
	)

	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, query.Id)
	if err != nil {
		return
	}

	data = structure.GetCodeListOrcManagementData{
		Id:                         tenantManagement.Id,
		CodeListName:               tenantManagement.TenantCompanyName,
		Phone:                      tenantManagement.TenantPhoneNumber,
		CreateTime:                 tools.MyTime(tenantManagement.CreateTime),
		CreatorName:                tenantManagement.CreatorName,
		UpdateTime:                 tools.MyTime(tenantManagement.UpdateTime),
		UpdateUserName:             tenantManagement.UpdaterName,
		ActivationTime:             tools.MyTime(tenantManagement.ActivationTime),
		Deadline:                   tools.MyTime(tenantManagement.Deadline),
		TenantManagementStatus:     tenantManagement.TenantManagementStatus,
		TenantManagementStatusName: tenantManagement.TenantManagementStatus.String(),
		Contacts:                   tenantManagement.TenantContacts,

		CodeListOrcDeadLine: tools.MyTime(tenantManagement.CodeListOrcDeadLine),
		CodeListOrcStatus:   tenantManagement.CodeListOrcStatus,
	}
	return
}

func (repo *codeListOrcManagementRepo) GetMPDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetCodeListOrcManagementQuery) (data structure.GetMPCodeListOrcManagementData, err error) {
	var (
		codeListOrcManagement    mysql.CodeListOrcManagement
		tenantManagement         mysql.TenantManagement
		tenantManagementUserRels mysql.TenantManagementUserRelList
		users                    mysqlSystem.UserList
		departments              mysqlSystem.DepartmentList
		userRoleRels             mysqlSystem.UserRoleRelList
		roles                    mysqlSystem.RoleList
		dutyService              = dictionary.NewDictionaryClient()
		employeeRepo             = EmployeeRepo.NewRepo(tx)
		employees                []*mysqlEmployee.Employee
		dutyMap                  = make(map[uint64][2]string)
		dictionaryDetailIDs      []uint64
		userIDs                  []uint64
	)

	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, metadata.GetLoginInfo(ctx).GetTenantManagementId())
	if err != nil {
		return
	}

	tenantManagementUserRels, err = repo.tenantManagementUserRelDao.FindByTenantManagementIDs(ctx, tx, codeListOrcManagement)
	if err != nil {
		return
	}

	for _, tenantManagementUserRel := range tenantManagementUserRels {
		userIDs = append(userIDs, tenantManagementUserRel.UserID)
	}

	users, err = repo.userDao.FindByID(ctx, tx, userIDs)
	if err != nil {
		return
	}

	departments, err = mysqlSystem.FindDepartment(tx, users)
	if err != nil {
		return
	}

	userRoleRels, err = mysqlSystem.FindUserRoleRelByUIDs(tx, users)
	if err != nil {
		return
	}

	roles, err = mysqlSystem.FindRole(tx, userRoleRels)
	if err != nil {
		return
	}

	employees, err = employeeRepo.QueryEmployeeByIds(ctx, mysql_base.GetUInt64ListV2("employee_id", users))
	if err != nil {
		return
	}

	for _, employee := range employees {
		dictionaryDetailIDs = append(dictionaryDetailIDs, employee.Duty.ToInt()...)
	}
	dutyMap, err = dutyService.GetDictionaryNameByIds(ctx, dictionaryDetailIDs)
	if err != nil {
		return
	}
	return structure.GetMPCodeListOrcManagementData{
		Id:                         tenantManagement.Id,
		CodeListName:               tenantManagement.TenantCompanyName,
		Phone:                      tenantManagement.TenantPhoneNumber,
		CreateTime:                 tools.MyTime(tenantManagement.CreateTime),
		CreatorName:                tenantManagement.CreatorName,
		UpdateTime:                 tools.MyTime(tenantManagement.UpdateTime),
		UpdateUserName:             tenantManagement.UpdaterName,
		Deadline:                   tools.MyTime(tenantManagement.Deadline),
		TenantManagementStatus:     tenantManagement.TenantManagementStatus,
		TenantManagementStatusName: tenantManagement.TenantManagementStatus.String(),
		Contacts:                   tenantManagement.TenantContacts,
		AdminName:                  users.PickAdmin().EmployeeName,
		CodeListOrcDeadLine:        tools.MyTime(tenantManagement.CodeListOrcDeadLine),
		CodeListOrcStatus:          tenantManagement.CodeListOrcStatus,
		SubTenantManagementDataList: func() (list []structure.SubTenantManagementData) {
			for _, user := range users {
				department := departments.Pick(user.DepartmentID)
				list = append(list, structure.SubTenantManagementData{
					Id:              user.Id,
					UserName:        user.EmployeeName,
					Phone:           user.Phone,
					IsAdminTenant:   user.IsTenantAdmin,
					DepartmentID:    user.DepartmentID,
					DepartmentName:  department.Name,
					AccessScope:     user.AccessScope,
					AccessScopeName: user.AccessScope.String(),
					Status:          user.Status,
					StatusName:      user.Status.String(),
					DutyIDs: func() (ids []uint64) {
						for _, employee := range employees {
							if employee.Id == user.EmployeeID {
								return employee.Duty.ToInt()
							}
						}
						return
					}(),
					DutyName: func() (str string) {
						return strings.Join(func() (dutyNames []string) {
							for _, employee := range employees {
								if employee.Id == user.EmployeeID {
									for _, dutyID := range employee.Duty.ToInt() {
										dutyNames = append(dutyNames, dutyMap[dutyID][1])
									}
									return
								}
							}
							return []string{}
						}(), ",")
					}(),
					RoleDataList: func() (list []structure.RoleData) {
						for _, rel := range userRoleRels.PickByUserID(user.Id) {
							role := roles.Pick(rel.RoleID)
							list = append(list, structure.RoleData{
								Id:   role.Id,
								Name: role.Name,
							})
						}
						return
					}(),
				})
			}
			return
		}(),
	}, nil
}

func (repo *codeListOrcManagementRepo) UpdateCompanyName(ctx context.Context, tx *mysql_base.Tx, param structure.UpdateCodeListOrcManagementNameParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		tenantManagement mysql.TenantManagement
	)
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.TenantCompanyName = param.CodeListName
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	data.Id = tenantManagement.Id
	return
}

func (repo *codeListOrcManagementRepo) UpdateStatusEnable(ctx context.Context, tx *mysql_base.Tx, param structure.EnableCodeListOrcManagementParam) (err error) {
	var (
		tenantManagement mysql.TenantManagement
		_db              *gorm.DB
	)
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.CodeListOrcStatus = common.CodeListStatusEnable
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}
	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	_db.Updates(&tenantManagement)
	return
}

func (repo *codeListOrcManagementRepo) UpdateStatusEnableEleColorCard(ctx context.Context, tx *mysql_base.Tx, param structure.EnableCodeListOrcManagementParam) (err error) {
	var (
		tenantManagement mysql.TenantManagement
		_db              *gorm.DB
	)
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.ElectronicColorCardStatus = common.ElectronicColorCardStatusEnable
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}
	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	_db.Updates(&tenantManagement)
	return
}

func (repo *codeListOrcManagementRepo) UpdateStatusSearchImage(ctx context.Context, tx *mysql_base.Tx, param structure.EnableSearchImageParam) (err error) {
	var (
		tenantManagement mysql.TenantManagement
		_db              *gorm.DB
	)
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.SearchImageStatus = param.SearchImageStatus
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}
	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	_db.Updates(&tenantManagement)
	return
}

func (repo *codeListOrcManagementRepo) UpdateStatusDisable(ctx context.Context, tx *mysql_base.Tx, param structure.DisableCodeListOrcManagementParam) (err error) {
	var (
		tenantManagement mysql.TenantManagement
		_db              *gorm.DB
	)

	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.CodeListOrcStatus = common.CodeListStatusDisable
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}
	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	_db.Updates(&tenantManagement)

	return
}

func (repo *codeListOrcManagementRepo) UpdateStatusDisableEleColorCard(ctx context.Context, tx *mysql_base.Tx, param structure.DisableCodeListOrcManagementParam) (err error) {
	var (
		tenantManagement mysql.TenantManagement
		_db              *gorm.DB
	)

	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.ElectronicColorCardStatus = common.ElectronicColorCardStatusDisable
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}
	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	_db.Updates(&tenantManagement)

	return
}

func (repo *codeListOrcManagementRepo) Recharge(ctx context.Context, tx *mysql_base.Tx, param structure.RechargeParam) (data structure.RechargeData, err error) {
	var (
		tenantManagement mysql.TenantManagement
		rechargeHistory  mysql.RechargeHistory
		_db              *gorm.DB
	)

	// 获取现有记录
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.RenewUpdateOcr(param.Deadline.ToTime())

	// 更新记录。
	updatedTenantManagement, err := repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	// 记录充值记录
	rechargeHistory = mysql.RechargeHistory{
		Id:             vars.Snowflake.GenerateId().UInt64(),
		CodeListOrcID:  updatedTenantManagement.Id,
		DeadLine:       updatedTenantManagement.CodeListOrcDeadLine,
		Remark:         param.Remark,
		Voucher:        param.Voucher,
		UpdateUserName: updatedTenantManagement.UpdaterName,
		Type:           common.RechargeTypeOcr,
	}

	rechargeHistory, err = repo.codeListOrcManagementDao.MustCreateRechargeHistory(ctx, tx, rechargeHistory)
	if err != nil {
		return
	}

	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	err = _db.Updates(&updatedTenantManagement).Error
	if err != nil {
		return
	}

	err = _db.Create(&rechargeHistory).Error
	if err != nil {
		return
	}

	data.Id = updatedTenantManagement.Id
	data.Deadline = tools.MyTime(updatedTenantManagement.CodeListOrcDeadLine)
	data.Status = 1
	data.Message = "充值成功"
	data.Remark = param.Remark
	data.Voucher = param.Voucher
	return
}

func (repo *codeListOrcManagementRepo) EleColorCardServerRecharge(ctx context.Context, tx *mysql_base.Tx, param structure.RechargeParam) (data structure.RechargeData, err error) {
	var (
		tenantManagement mysql.TenantManagement
		rechargeHistory  mysql.RechargeHistory
		_db              *gorm.DB
	)

	// 获取现有记录
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	if param.RechargeType == common.RechargeTypeEleColorCard {
		tenantManagement.RenewUpdateEleColorCard(param.Deadline.ToTime())
	}
	if param.RechargeType == common.RechargeTypeSearchImage {
		tenantManagement.RenewUpdateSearchImage(param.Deadline.ToTime())
	}

	// 更新记录。
	updatedTenantManagement, err := repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	// 记录充值记录
	rechargeHistory = mysql.RechargeHistory{
		Id:             vars.Snowflake.GenerateId().UInt64(),
		CodeListOrcID:  updatedTenantManagement.Id,
		DeadLine:       param.Deadline.ToTime(),
		Remark:         param.Remark,
		Voucher:        param.Voucher,
		UpdateUserName: updatedTenantManagement.UpdaterName,
		Type:           param.RechargeType,
	}

	rechargeHistory, err = repo.codeListOrcManagementDao.MustCreateRechargeHistory(ctx, tx, rechargeHistory)
	if err != nil {
		return
	}

	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	err = _db.Updates(&updatedTenantManagement).Error
	if err != nil {
		return
	}

	err = _db.Create(&rechargeHistory).Error
	if err != nil {
		return
	}

	data.Id = updatedTenantManagement.Id
	data.ElectronicColorCardDeadLine = tools.MyTime(updatedTenantManagement.ElectronicColorCardDeadLine)
	data.SearchImageDeadLine = tools.MyTime(updatedTenantManagement.SearchImageDeadLine)
	data.Status = 1
	data.Message = "充值成功"
	// data.RechargeNumber = param.RechargeNumber
	data.Remark = param.Remark
	data.Voucher = param.Voucher
	return
}

func (repo *codeListOrcManagementRepo) GetRechargeHistorys(ctx context.Context, tx *mysql_base.Tx, query structure.RechargeHistoryListQuery) (list structure.GetRechargeHistoryListDataList, total int, err error) {
	var rechargeHistorys mysql.RechargeHistoryList
	//
	rechargeHistorys, total, err = repo.codeListOrcManagementDao.GetRechargeHistorys(ctx, tx, query)
	if err != nil {
		return
	}
	for _, rechargeHistory := range rechargeHistorys {
		data := structure.RechargeHistoryListData{}
		data.Id = rechargeHistory.Id
		data.CreateTime = tools.MyTime(rechargeHistory.CreateTime)
		data.UpdateTime = tools.MyTime(rechargeHistory.UpdateTime)
		data.CreatorId = rechargeHistory.Id
		data.CreatorName = rechargeHistory.CreatorName
		data.UpdaterId = rechargeHistory.Id
		data.UpdateUserName = rechargeHistory.UpdaterName
		data.Deadline = tools.MyTime(rechargeHistory.DeadLine)
		data.EleCardDeadline = tools.MyTime(rechargeHistory.DeadLine)
		data.Remark = rechargeHistory.Remark
		data.Voucher = rechargeHistory.Voucher
		data.Type = rechargeHistory.Type
		data.TypeName = rechargeHistory.Type.String()
		list = append(list, data)
	}
	return
}

// 判断OCR识别是否过期
func (repo *codeListOrcManagementRepo) IsOcrExpired(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isExpired bool, err error) {
	var (
		tenantManagement mysql.TenantManagement
		now              = time.Now()
	)

	// 获取现有记录
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}

	// 检查码单状态是否为启用状态
	if tenantManagement.CodeListOrcStatus != common.CodeListStatusEnable {
		isExpired = true
		return isExpired, errors.NewError(errors.ErrCodeCodeListOrcManagementStatusIsUnrecognized)
	}

	// 检查码单是否过期
	if tenantManagement.CodeListOrcDeadLine.Before(now) {
		isExpired = true
		tenantManagement.CodeListOrcStatus = common.CodeListStatusDisable
		// err = repo.codeListOrcManagementDao.Update(ctx, tx, tenantManagement)
		tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
		if err != nil {
			return isExpired, err
		}
		_, err = repo.codeListOrcManagementDao.MustUpdate(context.Background(), nil, tenantManagement)
		if err != nil {
			return isExpired, err
		}
		return isExpired, errors.NewError(errors.ErrCodeCodeListOrcManagementStatusIsExpire)
	}

	return false, nil
}

// 判断电子色卡是否过期
func (repo *codeListOrcManagementRepo) IsEleColorCardExpired(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isExpired bool, err error) {
	var (
		tenantManagement mysql.TenantManagement
		now              = time.Now()
	)

	// 获取现有记录
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}

	// 检查码单状态是否为启用状态
	if tenantManagement.ElectronicColorCardStatus != common.ElectronicColorCardStatusEnable {
		isExpired = true
		return isExpired, errors.NewCustomError(errors.ErrCodeBusinessParameter, "，服务过期！")
	}

	// 检查码单是否过期
	if tenantManagement.ElectronicColorCardDeadLine.Before(now) {
		isExpired = true
		tenantManagement.ElectronicColorCardStatus = common.ElectronicColorCardStatusDisable
		// err = repo.codeListOrcManagementDao.Update(ctx, tx, tenantManagement)
		tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
		if err != nil {
			return isExpired, err
		}
		_, err = repo.codeListOrcManagementDao.MustUpdate(context.Background(), nil, tenantManagement)
		if err != nil {
			return isExpired, err
		}
		return isExpired, errors.NewCustomError(errors.ErrCodeBusinessParameter, "，服务过期！")
	}

	return false, nil
}

// 判断搜索图片是否过期
func (repo *codeListOrcManagementRepo) IsSearchImageExpired(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isExpired bool, err error) {
	var (
		tenantManagement mysql.TenantManagement
		now              = time.Now()
	)

	// 获取现有记录
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}

	// 检查码单状态是否为启用状态
	if tenantManagement.SearchImageStatus != common.ElectronicColorCardStatusEnable {
		isExpired = true
		return isExpired, errors.NewCustomError(errors.ErrCodeBusinessParameter, "，服务过期！")
	}

	// 检查码单是否过期
	if tenantManagement.SearchImageDeadLine.Before(now) {
		isExpired = true
		tenantManagement.SearchImageStatus = common.ElectronicColorCardStatusDisable
		// err = repo.codeListOrcManagementDao.Update(ctx, tx, tenantManagement)
		tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
		if err != nil {
			return isExpired, err
		}
		_, err = repo.codeListOrcManagementDao.MustUpdate(context.Background(), nil, tenantManagement)
		if err != nil {
			return isExpired, err
		}
		return isExpired, errors.NewCustomError(errors.ErrCodeBusinessParameter, "，服务过期！")
	}

	return false, nil
}

// 判断码单是否充值过
func (repo *codeListOrcManagementRepo) IsRecharged(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (isRecharged bool, err error) {
	var (
		tenantManagement mysql.TenantManagement
		rechargeHistory  mysql.RechargeHistory
		now              = time.Now()
		exist            bool
	)

	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}

	// 检查是否已经有充值记录
	rechargeHistory, exist, err = repo.rechargeHistoryDao.FirstByTenantManagementId(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}

	// 如果找到了充值记录，则表示已经充值过
	if exist {
		if tenantManagement.CodeListOrcIsRecognize == false {
			tenantManagement.CodeListOrcIsRecognize = true
			// 更新租户账套信息
			tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
			if err != nil {
				return
			}
			// 更新平台账套信息
			_, err = repo.codeListOrcManagementDao.MustUpdate(context.Background(), nil, tenantManagement)
			if err != nil {
				return
			}
		}
		isRecharged = true
		return
	}

	// 如果码单已经被使用过或有效期还未过期，就不设置三天的试用期
	if tenantManagement.CodeListOrcIsRecognize || !tenantManagement.CodeListOrcDeadLine.IsZero() {
		isRecharged = true
		return
	}

	// 如果没有找到充值记录 或者码单没有被使用过，则设置三天的试用期
	// 设置试用期
	tenantManagement.CodeListOrcDeadLine = now.AddDate(0, 0, 3)
	tenantManagement.CodeListOrcStatus = common.CodeListStatusEnable
	tenantManagement.CodeListOrcIsRecognize = true

	// 更新账套信息
	tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	// 创建充值记录
	rechargeHistory = mysql.NewRechargeHistory(tenantManagementId, tenantManagement.CodeListOrcDeadLine, "试用三天", "")

	// 保存充值记录
	rechargeHistory, err = repo.rechargeHistoryDao.MustCreate(ctx, tx, rechargeHistory)
	if err != nil {
		return
	}
	// 更新账套信息
	_, err = repo.codeListOrcManagementDao.MustUpdate(context.Background(), nil, tenantManagement)
	if err != nil {
		return
	}
	// 保存充值记录
	_, err = repo.rechargeHistoryDao.MustCreate(context.Background(), nil, rechargeHistory)
	if err != nil {
		return
	}

	isRecharged = true
	return
}

// 判断OCR识别是否过期
func (repo *codeListOrcManagementRepo) GetExpiringSoonList(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (err error) {
	var (
		tenantManagements           mysql.TenantManagementList
		tenantManagementPackageRels mysql.TenantManagementPackageRelList

		tenantManagement mysql.TenantManagement
		now              = time.Now()
	)
	tenantManagements, err = repo.codeListOrcManagementDao.GetExpiringSoonList(ctx, tx)
	if err != nil {
		return
	}

	tenantManagementPackageRels, err = repo.tenantManagementPackageRelDao.GetExpiringSoonList(ctx, tx)
	if err != nil {
		return
	}

	for _, itenantManagement := range tenantManagements {
		err = msg_publish.PushExpireOcrCodeManagement(ctx, itenantManagement)
		if err != nil {
			continue
		}
	}

	for _, tenantManagementPackageRel := range tenantManagementPackageRels {
		err = msg_publish.PushExpireTenantPackage(ctx, tenantManagementPackageRel)
		if err != nil {
			continue
		}
	}

	// 获取现有记录
	tenantManagement, err = repo.codeListOrcManagementDao.MustFirst(ctx, tx, tenantManagementId)
	if err != nil {
		return
	}

	// 检查码单状态是否为启用状态
	if tenantManagement.CodeListOrcStatus != common.CodeListStatusEnable {
		return errors.NewError(errors.ErrCodeCodeListOrcManagementStatusIsUnrecognized)
	}

	// 检查码单是否过期
	if tenantManagement.CodeListOrcDeadLine.Before(now) {
		tenantManagement.CodeListOrcStatus = common.CodeListStatusDisable
		tenantManagement, err = repo.codeListOrcManagementDao.MustUpdate(ctx, tx, tenantManagement)
		if err != nil {
			return
		}
		return errors.NewError(errors.ErrCodeCodeListOrcManagementStatusIsExpire)
	}

	return
}
