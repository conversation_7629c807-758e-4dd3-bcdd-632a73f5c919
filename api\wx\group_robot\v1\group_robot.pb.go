// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.0
// 	protoc        v5.29.1
// source: api/wx/group_robot/group_robot.proto

package v1

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateGroupRobotRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	TobeDevelopedAppInfoId uint64                 `protobuf:"varint,1,opt,name=Tobe_developed_app_info_id,json=TobeDevelopedAppInfoId,proto3" json:"Tobe_developed_app_info_id,omitempty"`
	Name                   string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	Url                    string                 `protobuf:"bytes,3,opt,name=Url,proto3" json:"Url,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *CreateGroupRobotRequest) Reset() {
	*x = CreateGroupRobotRequest{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupRobotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupRobotRequest) ProtoMessage() {}

func (x *CreateGroupRobotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupRobotRequest.ProtoReflect.Descriptor instead.
func (*CreateGroupRobotRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{0}
}

func (x *CreateGroupRobotRequest) GetTobeDevelopedAppInfoId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppInfoId
	}
	return 0
}

func (x *CreateGroupRobotRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateGroupRobotRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type CreateGroupRobotReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupRobotReply) Reset() {
	*x = CreateGroupRobotReply{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupRobotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupRobotReply) ProtoMessage() {}

func (x *CreateGroupRobotReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupRobotReply.ProtoReflect.Descriptor instead.
func (*CreateGroupRobotReply) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{1}
}

func (x *CreateGroupRobotReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateGroupRobotRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	TobeDevelopedAppInfoId uint64                 `protobuf:"varint,1,opt,name=Tobe_developed_app_info_id,json=TobeDevelopedAppInfoId,proto3" json:"Tobe_developed_app_info_id,omitempty"`
	Name                   string                 `protobuf:"bytes,2,opt,name=Name,proto3" json:"Name,omitempty"`
	Url                    string                 `protobuf:"bytes,3,opt,name=Url,proto3" json:"Url,omitempty"`
	Id                     uint64                 `protobuf:"varint,4,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *UpdateGroupRobotRequest) Reset() {
	*x = UpdateGroupRobotRequest{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGroupRobotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroupRobotRequest) ProtoMessage() {}

func (x *UpdateGroupRobotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroupRobotRequest.ProtoReflect.Descriptor instead.
func (*UpdateGroupRobotRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateGroupRobotRequest) GetTobeDevelopedAppInfoId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppInfoId
	}
	return 0
}

func (x *UpdateGroupRobotRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateGroupRobotRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *UpdateGroupRobotRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateGroupRobotReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateGroupRobotReply) Reset() {
	*x = UpdateGroupRobotReply{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateGroupRobotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateGroupRobotReply) ProtoMessage() {}

func (x *UpdateGroupRobotReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateGroupRobotReply.ProtoReflect.Descriptor instead.
func (*UpdateGroupRobotReply) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateGroupRobotReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteGroupRobotRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteGroupRobotRequest) Reset() {
	*x = DeleteGroupRobotRequest{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteGroupRobotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGroupRobotRequest) ProtoMessage() {}

func (x *DeleteGroupRobotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGroupRobotRequest.ProtoReflect.Descriptor instead.
func (*DeleteGroupRobotRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteGroupRobotRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type DeleteGroupRobotReply struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteGroupRobotReply) Reset() {
	*x = DeleteGroupRobotReply{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteGroupRobotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteGroupRobotReply) ProtoMessage() {}

func (x *DeleteGroupRobotReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteGroupRobotReply.ProtoReflect.Descriptor instead.
func (*DeleteGroupRobotReply) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{5}
}

type GetGroupRobotRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetGroupRobotRequest) Reset() {
	*x = GetGroupRobotRequest{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupRobotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupRobotRequest) ProtoMessage() {}

func (x *GetGroupRobotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupRobotRequest.ProtoReflect.Descriptor instead.
func (*GetGroupRobotRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{6}
}

func (x *GetGroupRobotRequest) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetGroupRobotReply struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Id                     uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime             string                 `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CreatorId              uint64                 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CreatorName            string                 `protobuf:"bytes,4,opt,name=creator_name,json=creatorName,proto3" json:"creator_name,omitempty"`
	UpdateTime             string                 `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UpdaterId              uint64                 `protobuf:"varint,6,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdaterName            string                 `protobuf:"bytes,7,opt,name=updater_name,json=updaterName,proto3" json:"updater_name,omitempty"`
	TobeDevelopedAppInfoId uint64                 `protobuf:"varint,8,opt,name=Tobe_developed_app_info_id,json=TobeDevelopedAppInfoId,proto3" json:"Tobe_developed_app_info_id,omitempty"`
	Name                   string                 `protobuf:"bytes,9,opt,name=Name,proto3" json:"Name,omitempty"`
	Url                    string                 `protobuf:"bytes,10,opt,name=Url,proto3" json:"Url,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *GetGroupRobotReply) Reset() {
	*x = GetGroupRobotReply{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetGroupRobotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetGroupRobotReply) ProtoMessage() {}

func (x *GetGroupRobotReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetGroupRobotReply.ProtoReflect.Descriptor instead.
func (*GetGroupRobotReply) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{7}
}

func (x *GetGroupRobotReply) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *GetGroupRobotReply) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *GetGroupRobotReply) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *GetGroupRobotReply) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *GetGroupRobotReply) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *GetGroupRobotReply) GetUpdaterId() uint64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *GetGroupRobotReply) GetUpdaterName() string {
	if x != nil {
		return x.UpdaterName
	}
	return ""
}

func (x *GetGroupRobotReply) GetTobeDevelopedAppInfoId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppInfoId
	}
	return 0
}

func (x *GetGroupRobotReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *GetGroupRobotReply) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

type ListGroupRobotRequest struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Page                   uint32                 `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`
	Size                   uint32                 `protobuf:"varint,2,opt,name=size,proto3" json:"size,omitempty"`
	TobeDevelopedAppInfoId uint64                 `protobuf:"varint,3,opt,name=tobe_developed_app_info_id,json=tobeDevelopedAppInfoId,proto3" json:"tobe_developed_app_info_id,omitempty"`
	Name                   string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ListGroupRobotRequest) Reset() {
	*x = ListGroupRobotRequest{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGroupRobotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroupRobotRequest) ProtoMessage() {}

func (x *ListGroupRobotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroupRobotRequest.ProtoReflect.Descriptor instead.
func (*ListGroupRobotRequest) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{8}
}

func (x *ListGroupRobotRequest) GetPage() uint32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListGroupRobotRequest) GetSize() uint32 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *ListGroupRobotRequest) GetTobeDevelopedAppInfoId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppInfoId
	}
	return 0
}

func (x *ListGroupRobotRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ListGroupRobotReply struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Total         uint32                            `protobuf:"varint,1,opt,name=total,proto3" json:"total,omitempty"`
	List          []*ListGroupRobotReply_GroupRobot `protobuf:"bytes,2,rep,name=list,proto3" json:"list,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListGroupRobotReply) Reset() {
	*x = ListGroupRobotReply{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGroupRobotReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroupRobotReply) ProtoMessage() {}

func (x *ListGroupRobotReply) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroupRobotReply.ProtoReflect.Descriptor instead.
func (*ListGroupRobotReply) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{9}
}

func (x *ListGroupRobotReply) GetTotal() uint32 {
	if x != nil {
		return x.Total
	}
	return 0
}

func (x *ListGroupRobotReply) GetList() []*ListGroupRobotReply_GroupRobot {
	if x != nil {
		return x.List
	}
	return nil
}

type ListGroupRobotReply_GroupRobot struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Id                     uint64                 `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	CreateTime             string                 `protobuf:"bytes,2,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	CreatorId              uint64                 `protobuf:"varint,3,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	CreatorName            string                 `protobuf:"bytes,4,opt,name=creator_name,json=creatorName,proto3" json:"creator_name,omitempty"`
	UpdateTime             string                 `protobuf:"bytes,5,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
	UpdaterId              uint64                 `protobuf:"varint,6,opt,name=updater_id,json=updaterId,proto3" json:"updater_id,omitempty"`
	UpdaterName            string                 `protobuf:"bytes,7,opt,name=updater_name,json=updaterName,proto3" json:"updater_name,omitempty"`
	TobeDevelopedAppInfoId uint64                 `protobuf:"varint,8,opt,name=Tobe_developed_app_info_id,json=TobeDevelopedAppInfoId,proto3" json:"Tobe_developed_app_info_id,omitempty"`
	Name                   string                 `protobuf:"bytes,9,opt,name=Name,proto3" json:"Name,omitempty"`
	Url                    string                 `protobuf:"bytes,10,opt,name=Url,proto3" json:"Url,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ListGroupRobotReply_GroupRobot) Reset() {
	*x = ListGroupRobotReply_GroupRobot{}
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListGroupRobotReply_GroupRobot) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListGroupRobotReply_GroupRobot) ProtoMessage() {}

func (x *ListGroupRobotReply_GroupRobot) ProtoReflect() protoreflect.Message {
	mi := &file_api_wx_group_robot_group_robot_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListGroupRobotReply_GroupRobot.ProtoReflect.Descriptor instead.
func (*ListGroupRobotReply_GroupRobot) Descriptor() ([]byte, []int) {
	return file_api_wx_group_robot_group_robot_proto_rawDescGZIP(), []int{9, 0}
}

func (x *ListGroupRobotReply_GroupRobot) GetId() uint64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *ListGroupRobotReply_GroupRobot) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *ListGroupRobotReply_GroupRobot) GetCreatorId() uint64 {
	if x != nil {
		return x.CreatorId
	}
	return 0
}

func (x *ListGroupRobotReply_GroupRobot) GetCreatorName() string {
	if x != nil {
		return x.CreatorName
	}
	return ""
}

func (x *ListGroupRobotReply_GroupRobot) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ListGroupRobotReply_GroupRobot) GetUpdaterId() uint64 {
	if x != nil {
		return x.UpdaterId
	}
	return 0
}

func (x *ListGroupRobotReply_GroupRobot) GetUpdaterName() string {
	if x != nil {
		return x.UpdaterName
	}
	return ""
}

func (x *ListGroupRobotReply_GroupRobot) GetTobeDevelopedAppInfoId() uint64 {
	if x != nil {
		return x.TobeDevelopedAppInfoId
	}
	return 0
}

func (x *ListGroupRobotReply_GroupRobot) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListGroupRobotReply_GroupRobot) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_api_wx_group_robot_group_robot_proto protoreflect.FileDescriptor

var file_api_wx_group_robot_group_robot_proto_rawDesc = []byte{
	0x0a, 0x24, 0x61, 0x70, 0x69, 0x2f, 0x77, 0x78, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72,
	0x6f, 0x62, 0x6f, 0x74, 0x2f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x18, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77,
	0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31,
	0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x7b, 0x0a, 0x17, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x1a, 0x54, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76,
	0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x22, 0x27, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22,
	0x8b, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x1a, 0x54,
	0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70,
	0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52,
	0x16, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70,
	0x70, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55,
	0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x27, 0x0a,
	0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x22, 0x29, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69,
	0x64, 0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x26, 0x0a, 0x14, 0x47, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02,
	0x69, 0x64, 0x22, 0xcc, 0x02, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x3a, 0x0a, 0x1a, 0x54, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65,
	0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x04, 0x52, 0x16, 0x54, 0x6f, 0x62, 0x65, 0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70,
	0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e,
	0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72,
	0x6c, 0x22, 0xa1, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1b, 0x0a, 0x04, 0x70,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02,
	0x28, 0x01, 0x52, 0x04, 0x70, 0x61, 0x67, 0x65, 0x12, 0x1b, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x2a, 0x02, 0x28, 0x01, 0x52,
	0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x3a, 0x0a, 0x1a, 0x74, 0x6f, 0x62, 0x65, 0x5f, 0x64, 0x65,
	0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x74, 0x6f, 0x62, 0x65, 0x44,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x49,
	0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc0, 0x03, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x05, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x12, 0x4c, 0x0a, 0x04, 0x6c, 0x69, 0x73, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x38, 0x2e, 0x77, 0x78, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f, 0x74, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x2e, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74, 0x52, 0x04, 0x6c, 0x69, 0x73,
	0x74, 0x1a, 0xc4, 0x02, 0x0a, 0x0a, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x04, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x21, 0x0a, 0x0c, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x04, 0x52, 0x09, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x72, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x1a, 0x54, 0x6f, 0x62, 0x65, 0x5f, 0x64,
	0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x5f, 0x61, 0x70, 0x70, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x04, 0x52, 0x16, 0x54, 0x6f, 0x62, 0x65,
	0x44, 0x65, 0x76, 0x65, 0x6c, 0x6f, 0x70, 0x65, 0x64, 0x41, 0x70, 0x70, 0x49, 0x6e, 0x66, 0x6f,
	0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x55, 0x72, 0x6c, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x55, 0x72, 0x6c, 0x42, 0x33, 0x0a, 0x18, 0x77, 0x78, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x77, 0x78, 0x2e, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x72, 0x6f, 0x62, 0x6f,
	0x74, 0x2e, 0x76, 0x31, 0x42, 0x0c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x6f, 0x62, 0x6f, 0x74,
	0x56, 0x31, 0x50, 0x01, 0x5a, 0x07, 0x2e, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x31, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_api_wx_group_robot_group_robot_proto_rawDescOnce sync.Once
	file_api_wx_group_robot_group_robot_proto_rawDescData = file_api_wx_group_robot_group_robot_proto_rawDesc
)

func file_api_wx_group_robot_group_robot_proto_rawDescGZIP() []byte {
	file_api_wx_group_robot_group_robot_proto_rawDescOnce.Do(func() {
		file_api_wx_group_robot_group_robot_proto_rawDescData = protoimpl.X.CompressGZIP(file_api_wx_group_robot_group_robot_proto_rawDescData)
	})
	return file_api_wx_group_robot_group_robot_proto_rawDescData
}

var file_api_wx_group_robot_group_robot_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_api_wx_group_robot_group_robot_proto_goTypes = []any{
	(*CreateGroupRobotRequest)(nil),        // 0: wx.api.wx.group_robot.v1.CreateGroupRobotRequest
	(*CreateGroupRobotReply)(nil),          // 1: wx.api.wx.group_robot.v1.CreateGroupRobotReply
	(*UpdateGroupRobotRequest)(nil),        // 2: wx.api.wx.group_robot.v1.UpdateGroupRobotRequest
	(*UpdateGroupRobotReply)(nil),          // 3: wx.api.wx.group_robot.v1.UpdateGroupRobotReply
	(*DeleteGroupRobotRequest)(nil),        // 4: wx.api.wx.group_robot.v1.DeleteGroupRobotRequest
	(*DeleteGroupRobotReply)(nil),          // 5: wx.api.wx.group_robot.v1.DeleteGroupRobotReply
	(*GetGroupRobotRequest)(nil),           // 6: wx.api.wx.group_robot.v1.GetGroupRobotRequest
	(*GetGroupRobotReply)(nil),             // 7: wx.api.wx.group_robot.v1.GetGroupRobotReply
	(*ListGroupRobotRequest)(nil),          // 8: wx.api.wx.group_robot.v1.ListGroupRobotRequest
	(*ListGroupRobotReply)(nil),            // 9: wx.api.wx.group_robot.v1.ListGroupRobotReply
	(*ListGroupRobotReply_GroupRobot)(nil), // 10: wx.api.wx.group_robot.v1.ListGroupRobotReply.GroupRobot
}
var file_api_wx_group_robot_group_robot_proto_depIdxs = []int32{
	10, // 0: wx.api.wx.group_robot.v1.ListGroupRobotReply.list:type_name -> wx.api.wx.group_robot.v1.ListGroupRobotReply.GroupRobot
	1,  // [1:1] is the sub-list for method output_type
	1,  // [1:1] is the sub-list for method input_type
	1,  // [1:1] is the sub-list for extension type_name
	1,  // [1:1] is the sub-list for extension extendee
	0,  // [0:1] is the sub-list for field type_name
}

func init() { file_api_wx_group_robot_group_robot_proto_init() }
func file_api_wx_group_robot_group_robot_proto_init() {
	if File_api_wx_group_robot_group_robot_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_api_wx_group_robot_group_robot_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_api_wx_group_robot_group_robot_proto_goTypes,
		DependencyIndexes: file_api_wx_group_robot_group_robot_proto_depIdxs,
		MessageInfos:      file_api_wx_group_robot_group_robot_proto_msgTypes,
	}.Build()
	File_api_wx_group_robot_group_robot_proto = out.File
	file_api_wx_group_robot_group_robot_proto_rawDesc = nil
	file_api_wx_group_robot_group_robot_proto_goTypes = nil
	file_api_wx_group_robot_group_robot_proto_depIdxs = nil
}
