# HCSCM 项目建议命令

## Windows系统命令

### 基本系统命令
- `dir` - 列出目录内容
- `cd` - 切换目录
- `type` - 查看文件内容
- `findstr` - 在文件中搜索文本
- `copy` - 复制文件
- `del` - 删除文件
- `mkdir` - 创建目录
- `rmdir` - 删除目录

### Git命令
- `git status` - 查看仓库状态
- `git add .` - 添加所有更改
- `git commit -m "message"` - 提交更改
- `git push` - 推送到远程仓库
- `git pull` - 拉取远程更改
- `git branch` - 查看分支
- `git checkout -b branch_name` - 创建并切换分支

### Go开发命令
- `go run main.go config.yaml` - 运行应用程序
- `go build -o hcscm.exe ./main.go` - 构建Windows可执行文件
- `go build -o hcscm ./main.go` - 构建Linux可执行文件
- `go mod tidy` - 整理依赖
- `go mod download` - 下载依赖
- `go test ./...` - 运行所有测试
- `go fmt ./...` - 格式化代码
- `go vet ./...` - 静态分析

### 项目特定命令
- `swag init` - 生成Swagger文档
- `generate` - 生成枚举_map.go文件
- `stringer` - 生成枚举_string.go文件

### Docker命令
- `docker run -itd --name mariadb -p 3306:3306 -e MYSQL_ROOT_PASSWORD=password mariadb` - 运行MySQL
- `docker run -itd --name redis -p 6379:6379 redis` - 运行Redis
- `docker run -itd --name mongo -p 27017:27017 mongo` - 运行MongoDB
- `docker-compose up -d` - 启动RabbitMQ服务

### 性能分析命令
- `go tool pprof http://127.0.0.1:8080/debug/pprof/heap` - 内存分析
- `go tool pprof http://127.0.0.1:8080/debug/pprof/profile` - CPU分析
- `go tool pprof http://127.0.0.1:8080/debug/pprof/goroutine` - Goroutine分析