/*此文件用于sql语句拼装，*/

package mysql_base

import (
	"fmt"
	"hcscm/tools"
	"reflect"
	"strconv"
	"strings"

	jsoniter "github.com/json-iterator/go"
)

type IEqualJoinTable interface {
	ToSQL() (sql string, args []interface{})
	Equals(IEqualJoinTable) bool
}

// 左联表left join
type equalLeftJoinTable struct {
	source      ITable // 左表
	join        ITable // 联表
	alias       string // 别名
	sourceField string // 左表字段
	joinField   string // 联表字段
}

func (r equalLeftJoinTable) ToSQL() (sql string, args []interface{}) {
	if r.alias != "" {
		sql = fmt.Sprintf("LEFT JOIN %v AS %v ON %v.%v = %v.%v", r.join.TableName(), r.alias, r.alias, r.joinField, r.source.TableName(), r.sourceField)
	} else {
		sql = fmt.Sprintf("LEFT JOIN %v ON %v.%v = %v.%v", r.join.TableName(), r.join.TableName(), r.joinField, r.source.TableName(), r.sourceField)
	}
	return
}

// 判断结构体是否一致
func (r equalLeftJoinTable) Equals(v IEqualJoinTable) bool {
	return reflect.DeepEqual(r, v)
}

// 联表join
type equalJoinTable struct {
	source      ITable // 左表
	join        ITable // 联表
	alias       string // 别名
	sourceField string // 左表字段
	joinField   string // 联表字段
}

func (r equalJoinTable) ToSQL() (sql string, args []interface{}) {
	if r.alias != "" {
		sql = fmt.Sprintf("JOIN %v AS %v ON %v.%v = %v.%v", r.join.TableName(), r.alias, r.alias, r.joinField, r.source.TableName(), r.sourceField)
	} else {
		sql = fmt.Sprintf("JOIN %v ON %v.%v = %v.%v", r.join.TableName(), r.join.TableName(), r.joinField, r.source.TableName(), r.sourceField)
	}
	return
}

func (r equalJoinTable) Equals(v IEqualJoinTable) bool {
	return reflect.DeepEqual(r, v)
}

type IEqualJoinTableWithCond interface {
	ToSQL() (sql string, args []interface{})
	Equals(IEqualJoinTable) bool
}

// 多条件左联left join on ... and where...
type equalLeftJoinTableWithCond struct {
	source      ITable
	join        ITable
	sourceField string
	joinField   string
	cond        ICondition
}

func (r equalLeftJoinTableWithCond) ToSQL() (sql string, args []interface{}) {
	sql = fmt.Sprintf("LEFT JOIN %v ON %v.%v = %v.%v", r.join.TableName(), r.join.TableName(), r.joinField, r.source.TableName(), r.sourceField)
	whereSQL, whereArgs := r.cond.ToWhereSQL()
	if whereSQL != "" {
		sql += " AND "
		sql += whereSQL
		args = whereArgs
	}
	return
}

func (r equalLeftJoinTableWithCond) Equals(v IEqualJoinTable) bool {
	return reflect.DeepEqual(r, v)
}

type pair struct {
	MainField   string
	LinkerField string
}

// 多条件左联left join on ... and ...
type multiEqualLeftJoinTable struct {
	source ITable
	join   ITable
	pairs  []pair
}

func (r multiEqualLeftJoinTable) ToSQL() (sql string, args []interface{}) {
	equalsOnSQL := ""
	for i, pair := range r.pairs {
		if i != 0 {
			equalsOnSQL += " AND "
		}
		equalsOnSQL += fmt.Sprintf("%v.%v = %v.%v", r.join.TableName(), pair.LinkerField, r.source.TableName(), pair.MainField)
	}

	sql = fmt.Sprintf("LEFT JOIN %v ON %v", r.join.TableName(), equalsOnSQL)
	return
}

func (r multiEqualLeftJoinTable) Equals(v IEqualJoinTable) bool {
	return reflect.DeepEqual(r, v)
}

type ISQLCondition interface {
	ToSQL() (sql string, values []interface{})
}

type ICondition interface {
	ISort
	AddEqualWithCheck(table ITable, field string, value interface{})
	AddTableEqual(table ITable, field string, value interface{})
	AddTableNotEqual(table ITable, field string, value interface{})
	AddTableContainMatch(table ITable, field string, value interface{})
	AddTableContainMatchWithCheck(table ITable, field string, value interface{})
	AddTableMultiFieldEqual(table ITable, field []string, value interface{})
	AddTableLeftJoiner(source, join ITable, sourceField, joinField string)
	AddGroupFiled(table ITable, field string)
	SetSelect(v interface{}, fields ...string)
	ToJoinSQL(tx *Tx) (rtx *Tx)
	ToWhere(tx *Tx) (rtx *Tx)
	ToSQLWhere() (string, []interface{})
	ToGroup(tx *Tx) (rtx *Tx)
	ToSelect(tx *Tx) (rtx *Tx)
	ToWhereSQL() (sql string, values []interface{})
	GetTables() []ITable
	AddTableEqualToOR(table ITable, field string, value interface{})                 // 带表前缀的AddEqualToOR
	AddTableEqualToORWithCheck(table ITable, field string, value interface{})        // 带字段检查的AddTableEqualToOR
	AddTableContainMatchToOR(table ITable, field string, value interface{})          // 带表前缀的AddContainMatchToOR
	AddTableContainMatchToORWithCheck(table ITable, field string, value interface{}) // 带字段检查的AddTableContainMatchToOR

}

type Equal struct {
	cs    int
	field string
	value interface{}
}

func (r Equal) ToSQL() (sql string, values []interface{}) {
	if r.cs != 3 {
		values = append(values, r.value)
	}
	if r.cs == 1 {
		sql = fmt.Sprintf("binary %v = ? ", r.field)
	} else if r.cs == 2 {
		sql = fmt.Sprintf("trim(replace(%v,' ','')) =  trim(replace(?,' ','')) ", r.field)
	} else if r.cs == 3 {
		sql = fmt.Sprintf("%v ", r.field)
	} else {
		sql = fmt.Sprintf("%v = ? ", r.field)
	}
	return
}

type Between struct {
	cs     bool
	field  string
	value1 interface{}
	value2 interface{}
}

func (r Between) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value1)
	values = append(values, r.value2)
	if r.cs {
		sql = fmt.Sprintf("binary %v between ? and ? ", r.field)
	} else {
		sql = fmt.Sprintf(
			"%v between ? and ? ", r.field)
	}
	return
}

type NotEqual struct {
	cs    bool
	field string
	value interface{}
}

func (r NotEqual) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	if r.cs {
		sql = fmt.Sprintf("binary %v != ? ", r.field)
	} else {
		sql = fmt.Sprintf("%v != ? ", r.field)
	}
	return
}

type RangeGT struct {
	field string
	value interface{}
}

func (r RangeGT) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	sql = fmt.Sprintf("%v > ? ", r.field)
	return
}

type RangeGE struct {
	field string
	value interface{}
}

func (r RangeGE) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	sql = fmt.Sprintf("%v >= ? ", r.field)
	return
}

type MultiFieldLikeMatchRangeGE struct {
	field []string
	value interface{}
}

func (r MultiFieldLikeMatchRangeGE) ToSQL() (sql string, values []interface{}) {
	for i := range r.field {
		values = append(values, r.value)
		if i != 0 {
			sql += " OR "
		}
		sql += fmt.Sprintf("%v >= ? ", r.field[i])
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return
}

type MultiFieldLikeMatchRangeLT struct {
	field []string
	value interface{}
}

func (r MultiFieldLikeMatchRangeLT) ToSQL() (sql string, values []interface{}) {
	for i := range r.field {
		values = append(values, r.value)
		if i != 0 {
			sql += " OR "
		}
		sql += fmt.Sprintf("%v < ? ", r.field[i])
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return
}

type RangeLT struct {
	field string
	value interface{}
}

func (r RangeLT) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	sql = fmt.Sprintf("%v < ? ", r.field)
	return
}

type RangeLE struct {
	field string
	value interface{}
}

func (r RangeLE) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	sql = fmt.Sprintf("%v <= ? ", r.field)
	return
}

type LikeMatch struct {
	cs    int
	field string
	value string
}

func (r LikeMatch) ToSQL() (sql string, values []interface{}) {
	if r.cs == 1 {
		values = append(values, `%`+r.value+`%`)
		sql = fmt.Sprintf("binary %v like ? ", r.field)
	} else if r.cs == 2 {
		values = append(values, `%`+r.value+`%`)
		sql = fmt.Sprintf("trim(replace(%v,' ','')) like trim(replace(?,' ','')) ", r.field)
	} else {
		values = append(values, `%`+r.value+`%`)
		sql = fmt.Sprintf("%v like ? ", r.field)
	}
	return
}

type InContain struct {
	field string
	value interface{}
}

func (r InContain) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	sql = fmt.Sprintf("%v in (?) ", r.field)
	return

}

type NotInContain struct {
	field string
	value interface{}
}

func (r NotInContain) ToSQL() (sql string, values []interface{}) {
	values = append(values, r.value)
	sql = fmt.Sprintf("%v not in (?) ", r.field)
	return
}

type MultiFieldLikeMatch struct {
	field []string
	value string
}

func (r MultiFieldLikeMatch) ToSQL() (sql string, values []interface{}) {
	for i := range r.field {
		values = append(values, `%`+r.value+`%`)
		if i != 0 {
			sql += " OR "
		}
		sql += fmt.Sprintf("%v like ? ", r.field[i])
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return

}

type MultiFieldEqual struct {
	field []string
	value interface{}
}

func (r MultiFieldEqual) ToSQL() (sql string, values []interface{}) {
	for i := range r.field {
		values = append(values, r.value)
		if i != 0 {
			sql += " OR "
		}
		sql += fmt.Sprintf("%v = ? ", r.field[i])
	}
	return

}

type MultiFieldNotEqual struct {
	field []string
	value interface{}
}

func (r MultiFieldNotEqual) ToSQL() (sql string, values []interface{}) {
	for i := range r.field {
		values = append(values, r.value)
		if i != 0 {
			sql += " OR "
		}
		sql += fmt.Sprintf("%v != ? ", r.field[i])
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return

}

type IntListJsonContainMatch struct {
	field  string
	values []int
}

func (r IntListJsonContainMatch) ToSQL() (sql string, values []interface{}) {
	regexMatch := ""
	for i, t := range r.values {
		if i != 0 {
			regexMatch += "|"
		}
		regexMatch += strconv.Itoa(t)
	}
	values = append(values, `[\[,\,]{1}(`+regexMatch+`)[\],\,]{1}`)

	sql = fmt.Sprintf("%v RegExp ? ", r.field)
	return
}

type UInt64ListJsonContainMatch struct {
	field  string
	values []uint64
}

func (r UInt64ListJsonContainMatch) ToSQL() (sql string, values []interface{}) {
	regexMatch := ""
	for i, t := range r.values {
		if i != 0 {
			regexMatch += "|"
		}
		regexMatch += strconv.FormatUint(t, 10)
	}
	values = append(values, `[\[,\,]{1}(`+regexMatch+`)[\],\,]{1}`)

	sql = fmt.Sprintf("%v RegExp ? ", r.field)
	return
}

type EqualMap struct {
	m map[string]interface{}
}

func (r EqualMap) ToSQL() (sql string, values []interface{}) {
	var (
		idx = 0
	)
	for k, v := range r.m {
		if idx != 0 {
			sql += " AND "
		}
		sql += fmt.Sprintf("binary %v = ? ", k)
		values = append(values, v)
		idx += 1
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return
}

// ContainMatchMap map 转 in sql
type ContainMatchMap struct {
	m map[string]interface{}
}

func (r ContainMatchMap) ToSQL() (sql string, values []interface{}) {
	var (
		idx = 0
	)
	for k, v := range r.m {
		if idx != 0 {
			sql += " AND "
		}
		sql += fmt.Sprintf("%v in (?) ", k)
		values = append(values, v)
		idx += 1
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return
}

type Condition struct {
	ors    []ISQLCondition
	l      []ISQLCondition
	j      []IEqualJoinTable
	s      ISort
	sel    ISelect
	g      []IGroup
	tables map[string]ITable
}

func (c Condition) String() string {
	return ""
}

func (c Condition) MarshalJSON() ([]byte, error) {
	return jsoniter.Marshal(c.l)
}

func (c Condition) CloneWithCond() *Condition {
	r := &Condition{}
	r.ors = c.ors
	r.l = c.l
	r.j = c.j
	return r
}

func NewCondition() *Condition {
	ors := make([]ISQLCondition, 0)
	l := make([]ISQLCondition, 0)
	j := make([]IEqualJoinTable, 0)
	tables := make(map[string]ITable)
	return &Condition{
		ors:    ors,
		l:      l,
		j:      j,
		tables: tables,
	}
}

func (c *Condition) addTable(t ITable) {
	if c.tables == nil {
		c.tables = make(map[string]ITable)
	}
	c.tables[t.TableName()] = t
}

func (c *Condition) AddTableLeftJoiner(source, join ITable, sourceField, joinField string) {

	for _, t := range c.j {
		if t.Equals(equalLeftJoinTable{
			source:      source,
			join:        join,
			sourceField: sourceField,
			joinField:   joinField,
		}) {
			return
		}
	}
	c.j = append(c.j, equalLeftJoinTable{
		source:      source,
		join:        join,
		sourceField: sourceField,
		joinField:   joinField,
	})

	c.addTable(source)
	c.addTable(join)

}

func (c *Condition) AddTableJoiner(source, join ITable, sourceField, joinField string) {

	for _, t := range c.j {
		if t.Equals(equalJoinTable{
			source:      source,
			join:        join,
			sourceField: sourceField,
			joinField:   joinField,
		}) {
			return
		}
	}
	c.j = append(c.j, equalJoinTable{
		source:      source,
		join:        join,
		sourceField: sourceField,
		joinField:   joinField,
	})

	c.addTable(source)
	c.addTable(join)

}

func (c *Condition) AddAliasTableLeftJoiner(source, join ITable, alias, sourceField, joinField string) {

	for _, t := range c.j {
		if t.Equals(equalLeftJoinTable{
			source:      source,
			join:        join,
			alias:       alias,
			sourceField: sourceField,
			joinField:   joinField,
		}) {
			return
		}
	}
	c.j = append(c.j, equalLeftJoinTable{
		source:      source,
		join:        join,
		alias:       alias,
		sourceField: sourceField,
		joinField:   joinField,
	})

	c.addTable(source)
	c.addTable(join)
}

func (c *Condition) AddTableLeftJoinerWithCond(source, join ITable, sourceField, joinField string, cond ICondition) {

	for _, t := range c.j {
		if t.Equals(equalLeftJoinTableWithCond{
			source:      source,
			join:        join,
			sourceField: sourceField,
			joinField:   joinField,
			cond:        cond,
		}) {
			return
		}
	}
	c.j = append(c.j, equalLeftJoinTableWithCond{
		source:      source,
		join:        join,
		sourceField: sourceField,
		joinField:   joinField,
		cond:        cond,
	})

	c.addTable(source)
	c.addTable(join)
}

func (c *Condition) AddTableLeftJoinerPairs(source, join ITable, pairs []pair) {
	c.j = append(c.j, multiEqualLeftJoinTable{
		source: source,
		join:   join,
		pairs:  pairs,
	})

	c.addTable(source)
	c.addTable(join)
}

// 大于
func (c *Condition) AddRangeGTOR(field string, value interface{}) {
	r := RangeGT{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

// 大于
func (c *Condition) AddRangeGT(field string, value interface{}) {
	r := RangeGT{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 大于
func (c *Condition) AddTableRangeGTOR(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddRangeGTOR(field, value)
}

// 大于
func (c *Condition) AddTableRangeGT(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddRangeGT(field, value)
}

// 大于等于
func (c *Condition) AddRangeGEOR(field string, value interface{}) {
	r := RangeGE{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

// 大于等于
func (c *Condition) AddRangeGE(field string, value interface{}) {
	r := RangeGE{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 大于等于
func (c *Condition) AddTableRangeGEOR(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddRangeGEOR(field, value)
}

// 大于等于
func (c *Condition) AddTableRangeGE(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddRangeGE(field, value)
}

// 小于
func (c *Condition) AddRangeLT(field string, value interface{}) {
	r := RangeLT{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 小于
func (c *Condition) AddTableRangeLT(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddRangeLT(field, value)
}

// 小于等于
func (c *Condition) AddRangeLE(field string, value interface{}) {
	r := RangeLE{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 小于等于
func (c *Condition) AddTableRangeLE(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddRangeLE(field, value)
}

// 单字段=
func (c *Condition) AddEqual(field string, value interface{}) {

	r := Equal{
		cs:    0,
		field: field,
		value: value,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}

	c.l = append(c.l, r)
}

func (c *Condition) AddTableEqual(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddEqual(field, value)
}

// 单字段=
func (c *Condition) AddSql(field string) {

	r := Equal{
		cs:    3,
		field: field,
		value: nil,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}

	c.l = append(c.l, r)
}

// equal 查询是否有gorm标签后，添加相等条件
func (c *Condition) AddEqualWithCheck(r ITable, field string, value interface{}) {
	if CheckWhereField(r, field) {
		c.AddTableEqual(r, field, value)
	}
}

// AddEqualToOR 单字段= 加到Condition的ors数组：
// 一个AddEqualToOR("id",1) 相当于 where id = 1;
// 多个AddEqualToOR("id",1),AddEqualToOR("name","aa"),用or拼接 相当于where (id = 1 or name='aa')
func (c *Condition) AddEqualToOR(field string, value interface{}) {
	r := Equal{
		cs:    0,
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

// AddNotEqualToOR 单字段= 加到Condition的ors数组：
// 一个AddNotEqualToOR("id",1) 相当于 where id != 1;
// 多个AddNotEqualToOR("id",1),AddEqualToOR("name","aa"),用or拼接 相当于where (id != 1 or name!='aa')
func (c *Condition) AddNotEqualToOR(field string, value interface{}) {
	r := NotEqual{
		cs:    false,
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

// AddContainMatchToOR 相当于sql的in,加到Condition的ors数组：
// 一个AddContainMatchToOR("id",1) 相当于 where id in (1);
// 多个AddContainMatchToOR("id",1),AddContainMatchToOR("name",[]{"aa","bb"}),用or拼接,
// 相当于where (id in (1) or name in ('aa','bb'))
func (c *Condition) AddContainMatchToOR(field string, value interface{}) {
	r := InContain{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableContainMatchToOR(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddContainMatchToOR(field, value)
}

func (c *Condition) AddTableContainMatchToORWithCheck(r ITable, field string, value interface{}) {
	if CheckWhereField(r, field) {
		c.AddTableContainMatchToOR(r, field, value)
	}
}

// or not in
func (c *Condition) AddNotContainMatchToOR(field string, value interface{}) {
	r := NotInContain{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableNotContainMatchToOR(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddNotContainMatchToOR(field, value)
}

func (c *Condition) AddTableNotContainMatchToORWithCheck(r ITable, field string, value interface{}) {
	if CheckWhereField(r, field) {
		c.AddTableNotContainMatchToOR(r, field, value)
	}
}

func (c *Condition) Between(field string, value1, value2 interface{}) {

	r := Between{
		cs:     false,
		field:  field,
		value1: value1,
		value2: value2,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}

	c.l = append(c.l, r)
}

func (c *Condition) AddTableBetween(table ITable, field string, value1, value2 interface{}) {
	field = table.TableName() + "." + field
	c.Between(field, value1, value2)
}

// or =
func (c *Condition) AddTableEqualToOR(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddEqualToOR(field, value)
}

func (c *Condition) AddTableEqualToORWithCheck(r ITable, field string, value interface{}) {
	if CheckWhereField(r, field) {
		c.AddTableEqualToOR(r, field, value)
	}
}

// or !=
func (c *Condition) AddTableNotEqualToOR(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddNotEqualToOR(field, value)
}

func (c *Condition) AddTableNotEqualToORWithCheck(r ITable, field string, value interface{}) {
	if CheckWhereField(r, field) {
		c.AddTableNotEqualToOR(r, field, value)
	}
}

// 不等于
func (c *Condition) AddNotEqual(field string, value interface{}) {

	r := NotEqual{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableNotEqual(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddNotEqual(field, value)
}

// 大小写敏感等于
func (c *Condition) AddBinaryEqual(field string, value interface{}) {
	r := Equal{
		cs:    1,
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 单字段去空格=
func (c *Condition) AddTrimEqual(field string, value interface{}) {

	r := Equal{
		cs:    2,
		field: field,
		value: value,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}

	c.l = append(c.l, r)
}

// 模糊匹配
func (c *Condition) AddFuzzyMatch(field string, value string) {
	r := LikeMatch{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 模糊匹配
func (c *Condition) AddTableFuzzyMatch(table ITable, field string, value string) {
	field = table.TableName() + "." + field
	c.AddFuzzyMatch(field, value)
}

func (c *Condition) AddFuzzyMatchToOR(field string, value string) {
	r := LikeMatch{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableFuzzyMatchToOR(table ITable, field string, value string) {
	field = table.TableName() + "." + field
	c.AddFuzzyMatchToOR(field, value)
}

// 大小写敏感的模糊匹配。
func (c *Condition) AddBinaryFuzzyMatch(field string, value string) {
	r := LikeMatch{
		cs:    1,
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 去空格的模糊匹配
func (c *Condition) AddTrimFuzzyMatch(field string, value string) {
	r := LikeMatch{
		cs:    2,
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 相当于sql的in

func (c *Condition) AddContainMatch(field string, value interface{}) {
	r := InContain{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableContainMatch(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddContainMatch(field, value)
}

func (c *Condition) AddTableContainMatchWithCheck(r ITable, field string, value interface{}) {
	if CheckWhereField(r, field) {
		c.AddTableContainMatch(r, field, value)
	}
}

// not in
func (c *Condition) AddNotContainMatch(field string, value interface{}) {
	r := NotInContain{
		field: field,
		value: value,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableNotContainMatch(table ITable, field string, value interface{}) {
	field = table.TableName() + "." + field
	c.AddNotContainMatch(field, value)
}

// 用于or(多字段模糊匹配)
func (c *Condition) AddMultiFieldLikeMatch(field []string, value string) {
	r := MultiFieldLikeMatch{
		field: field,
		value: value,
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableMultiFieldLikeMatch(table ITable, field []string, value string) {
	for i := range field {
		field[i] = table.TableName() + "." + field[i]
	}
	c.AddMultiFieldLikeMatch(field, value)
}

// 用于or(多字段模糊匹配)
func (c *Condition) AddMultiFieldLikeMatchToOR(field []string, value string) {
	r := MultiFieldLikeMatch{
		field: field,
		value: value,
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableMultiFieldLikeMatchToOR(table ITable, field []string, value string) {
	for i := range field {
		field[i] = table.TableName() + "." + field[i]
	}
	c.AddMultiFieldLikeMatchToOR(field, value)
}

// 用于or大于等于(多字段大于等于)
func (c *Condition) AddMultiFieldLikeMatchRangeGE(field []string, value interface{}) {
	r := MultiFieldLikeMatchRangeGE{
		field: field,
		value: value,
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableMultiFieldLikeMatchRangeGE(table ITable, field []string, value interface{}) {
	for i := range field {
		field[i] = table.TableName() + "." + field[i]
	}
	c.AddMultiFieldLikeMatchRangeGE(field, value)
}

// 用于or小于
func (c *Condition) AddMultiFieldLikeMatchRangeLT(field []string, value interface{}) {
	r := MultiFieldLikeMatchRangeLT{
		field: field,
		value: value,
	}
	c.l = append(c.l, r)
}

// AND 单字段is not null
func (c *Condition) AddNotNull(field string) {

	r := NotNull{
		field: field,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}

	c.l = append(c.l, r)
}

func (c *Condition) AddTableNotNull(table ITable, field string) {
	field = table.TableName() + "." + field
	c.AddNotNull(field)
}

// OR 单字段is not null
func (c *Condition) AddNotNullToOR(field string) {
	r := NotNull{
		field: field,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableNotNullToOR(table ITable, field string) {
	field = table.TableName() + "." + field
	c.AddNotNullToOR(field)
}

// 单字段is null
func (c *Condition) AddNull(field string) {
	r := Null{
		field: field,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableNull(table ITable, field string) {
	field = table.TableName() + "." + field
	c.AddNull(field)
}

// OR 单字段is null
func (c *Condition) AddNullToOR(field string) {
	r := Null{
		field: field,
	}

	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableNullToOR(table ITable, field string) {
	field = table.TableName() + "." + field
	c.AddNullToOR(field)
}

// 多字段 =
func (c *Condition) AddMultiFieldEqual(field []string, value interface{}) {
	r := MultiFieldEqual{
		field: field,
		value: value,
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableMultiFieldEqual(table ITable, field []string, value interface{}) {
	for i := range field {
		field[i] = table.TableName() + "." + field[i]
	}
	c.AddMultiFieldEqual(field, value)
}

// 多字段 !=
func (c *Condition) AddMultiFieldNotEqual(field []string, value interface{}) {
	r := MultiFieldNotEqual{
		field: field,
		value: value,
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableMultiFieldNotEqual(table ITable, field []string, value interface{}) {
	for i := range field {
		field[i] = table.TableName() + "." + field[i]
	}
	c.AddMultiFieldNotEqual(field, value)
}

// table正则搜索
func (c *Condition) AddTableUInt64ListJsonContainMatch(table ITable, field string, values ...uint64) {
	field = table.TableName() + "." + field
	c.AddUInt64ListJsonContainMatch(field, values...)
}

// 正则搜索
func (c *Condition) AddUInt64ListJsonContainMatch(field string, values ...uint64) {
	r := UInt64ListJsonContainMatch{
		field:  field,
		values: values,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// table or正则搜索
func (c *Condition) AddTableUInt64ListJsonContainMatchToOR(table ITable, field string, values ...uint64) {
	field = table.TableName() + "." + field
	c.AddUInt64ListJsonContainMatchToOR(field, values...)
}

// or正则搜索
func (c *Condition) AddUInt64ListJsonContainMatchToOR(field string, values ...uint64) {
	r := UInt64ListJsonContainMatch{
		field:  field,
		values: values,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.ors = append(c.ors, r)
}

func (c *Condition) AddTableIntListJsonContainMatch(table ITable, field string, values ...int) {
	field = table.TableName() + "." + field
	c.AddIntListJsonContainMatch(field, values...)
}

func (c *Condition) AddIntListJsonContainMatch(field string, values ...int) {
	r := IntListJsonContainMatch{
		field:  field,
		values: values,
	}
	if field == "" || field[len(field)-1] == '.' {
		return
	}
	c.l = append(c.l, r)
}

// 多字段排序
func (c *Condition) AddSort(sortKeys ...string) {

	if c.s == nil {
		c.s = &Sort{}
	}
	c.s.AddSortKeys(sortKeys)
}

func (c Condition) ToWhere(tx *Tx) (rtx *Tx) {
	rtx = tx.Clone()
	sql, values := c.ToWhereSQL()
	rtx = tx.Where(sql, values...)
	return rtx
}

func (c Condition) ToSQLWhere() (string, []interface{}) {
	sql, values := c.ToWhereSQL()
	return sql, values
}

func (c Condition) ToJoinSQL(tx *Tx) (rtx *Tx) {
	if len(c.j) == 0 {
		rtx = tx
		return
	} else {
		rtx = tx.Clone()
		// 处理第一个
		if len(c.j) > 0 {
			join := c.j[0]
			sql, args := join.ToSQL()
			rtx = tx.Joins(sql, args...)
		}
		for i := 1; i < len(c.j); i++ {
			join := c.j[i]
			sql, args := join.ToSQL()
			rtx = rtx.Joins(sql, args...)
		}
		return
	}
}

func (c Condition) ToSelect(tx *Tx) (rtx *Tx) {
	if c.sel == nil {
		rtx = tx
		return
	}
	rtx = tx.Clone()
	rtx = tx.Select(c.sel.ToSQL())
	return

}

func (c Condition) ToGroup(tx *Tx) (rtx *Tx) {
	var (
		sql string
	)
	if c.g == nil {
		rtx = tx
		return
	}
	rtx = tx.Clone()

	for i, t := range c.g {
		if i != 0 {
			sql += " , "
		}
		sql += t.ToSQL()
	}
	rtx = tx.Group(sql)
	return
}

func (c Condition) ToWhereSQL() (sql string, values []interface{}) {
	andSQL := ""
	orSQL := ""
	for i := range c.l {
		cond := c.l[i]
		field, value := cond.ToSQL()

		if i != 0 {
			andSQL += " AND "
		}
		andSQL += field
		values = append(values, value...)
	}

	for i := range c.ors {
		cond := c.ors[i]
		field, value := cond.ToSQL()

		if i != 0 {
			orSQL += " OR "
		}
		orSQL += field
		values = append(values, value...)
	}
	if andSQL != "" && orSQL != "" {
		sql = "(" + andSQL + ")" + "AND" + "(" + orSQL + ") "
	} else if andSQL != "" {
		sql = "(" + andSQL + ") "
	} else if orSQL != "" {
		sql = "(" + orSQL + ") "
	}
	return
}

// 拼sql的 &&条件
func (c *Condition) AddEqualMap(m map[string]interface{}) {

	r := EqualMap{
		m: m,
	}
	c.ors = append(c.ors, r)
}

// AddContainMatchMap
// 一个AddContainMatchMap{"id":[1,2],"code":["aa","bb"]}相当于 where (id in (1,2) and code in ("aa","bb"))
// 多个AddContainMatchMap,用or拼接
func (c *Condition) AddContainMatchMap(m map[string]interface{}) {
	r := ContainMatchMap{
		m: m,
	}
	// 加到or条件
	c.ors = append(c.ors, r)
}
func (c *Condition) GetSortKeys() (r []string) {
	if c.s == nil {
		return
	}
	return c.s.GetSortKeys()
}

func (c *Condition) AddSortKeys(keys []string) {
	if c.s == nil {
		c.s = NewSort()
	}
	c.s.AddSortKeys(keys)
}

func (c *Condition) SetSortKeys(keys []string) {
	if c.s == nil {
		c.s = NewSort()
	}
	sortKeys := c.s.GetSortKeys()
	if len(sortKeys) != 0 {
		sortKeys = append(sortKeys, keys...)
		sortKeys = tools.StringDistinctAndFilterZero(sortKeys)
	} else {
		sortKeys = keys
	}
	c.s.SetSortKeys(sortKeys)
}

func (c *Condition) SetSelect(v interface{}, fields ...string) {
	c.sel = Select{
		obj:    v,
		fields: fields,
	}
}

func (c *Condition) AddGroupFiled(table ITable, field string) {
	if c.g == nil {
		c.g = make([]IGroup, 0)
	}
	c.g = append(c.g, Group{table, field})
}

func (c Condition) GetTables() []ITable {
	r := make([]ITable, 0)
	for _, table := range c.tables {
		r = append(r, table)
	}
	return r
}

type ISelect interface {
	ToSQL() (sql string)
}

type Select struct {
	obj    interface{}
	fields []string
}

func (r Select) ToSQL() (sql string) {
	sql = MergeSelect(r.obj, r.fields...)
	return
}

type IGroup interface {
	ToSQL() (sql string)
}

type Group struct {
	table ITable
	field string
}

func (r Group) ToSQL() (sql string) {
	return r.table.TableName() + "." + r.field
}

type NotNull struct {
	field string
}

func (r NotNull) ToSQL() (sql string, values []interface{}) {
	sql = fmt.Sprintf("%v is not null ", r.field)
	return
}

type Null struct {
	field string
}

func (r Null) ToSQL() (sql string, values []interface{}) {
	sql = fmt.Sprintf("%v is null ", r.field)
	return
}

// AddTableEqualToORWithAnd 添加一组OR条件,这组条件与其他条件用AND连接
func (c *Condition) AddTableEqualToORWithAnd(tables []ITable, field string, value interface{}) {
	orConditions := make([]string, 0)
	for _, table := range tables {
		orConditions = append(orConditions, fmt.Sprintf("%s.%s = ?", table.TableName(), field))
	}

	// 将OR条件组合成一个完整的条件字符串
	orSQL := "(" + strings.Join(orConditions, " OR ") + ")"

	// 为每个条件都使用相同的值
	values := make([]interface{}, len(tables))
	for i := range tables {
		values[i] = value
	}

	// 将组合后的条件添加到AND条件列表中
	r := CustomCondition{
		sql:    orSQL,
		values: values,
	}
	c.l = append(c.l, r)
}

// 添加一个新的条件类型
type CustomCondition struct {
	sql    string
	values []interface{}
}

func (r CustomCondition) ToSQL() (string, []interface{}) {
	return r.sql, r.values
}

// AddTableMultiFieldLikeMatchWithAnd 添加一组OR条件的LIKE匹配,这组条件与其他条件用AND连接
func (c *Condition) AddTableMultiFieldLikeMatchWithAnd(tables []ITable, field []string, value string) {
	orConditions := make([]string, 0)
	for _, table := range tables {
		for _, f := range field {
			orConditions = append(orConditions, fmt.Sprintf("%s.%s LIKE ?", table.TableName(), f))
		}
	}

	// 将OR条件组合成一个完整的条件字符串
	orSQL := "(" + strings.Join(orConditions, " OR ") + ")"

	// 为每个条件都使用相同的模糊匹配值
	values := make([]interface{}, len(tables)*len(field))
	for i := range values {
		values[i] = "%" + value + "%"
	}

	// 将组合后的条件添加到AND条件列表中
	r := CustomCondition{
		sql:    orSQL,
		values: values,
	}
	c.l = append(c.l, r)
}

// AddTableContainMatchWithAnd 添加一组OR条件的IN匹配,这组条件与其他条件用AND连接
func (c *Condition) AddTableContainMatchWithAnd(tables []ITable, field string, values []uint64) {
	if len(values) == 0 {
		return
	}

	orConditions := make([]string, 0)
	// 将uint64切片转换为字符串切片
	strValues := make([]string, len(values))
	for i, v := range values {
		strValues[i] = strconv.FormatUint(v, 10)
	}

	for _, table := range tables {
		orConditions = append(orConditions, fmt.Sprintf("%s.%s IN (%s)",
			table.TableName(),
			field,
			strings.Join(strValues, ",")))
	}

	// 将OR条件组合成一个完整的条件字符串
	orSQL := "(" + strings.Join(orConditions, " OR ") + ")"

	// 将组合后的条件添加到AND条件列表中
	r := CustomCondition{
		sql:    orSQL,
		values: []interface{}{}, // IN条件的值已经直接写入SQL，不需要参数
	}
	c.l = append(c.l, r)
}

// AddTableBetweenWithAnd 添加多表的between条件,这组条件与其他条件用AND连接
func (c *Condition) AddTableBetweenWithAnd(tables []ITable, field string, startTime, endTime string) {
	orConditions := make([]string, 0)
	values := make([]interface{}, 0)

	for _, table := range tables {
		orConditions = append(orConditions, fmt.Sprintf("%s.%s BETWEEN ? AND ?", table.TableName(), field))
		values = append(values, startTime, endTime)
	}

	// 将OR条件组合成一个完整的条件字符串
	orSQL := "(" + strings.Join(orConditions, " OR ") + ")"

	r := CustomCondition{
		sql:    orSQL,
		values: values,
	}
	c.l = append(c.l, r)
}

type MultiFieldNotLikeMatchAnd struct {
	field []string
	value string
}

func (r MultiFieldNotLikeMatchAnd) ToSQL() (sql string, values []interface{}) {
	for i := range r.field {
		values = append(values, `%`+r.value+`%`)
		if i != 0 {
			sql += " AND "
		}
		sql += fmt.Sprintf("%v not like ? ", r.field[i])
	}
	if sql != "" {
		sql = "(" + sql + ") "
	}
	return
}

func (c *Condition) AddMultiFieldNotLikeMatchAnd(field []string, value string) {
	r := MultiFieldNotLikeMatchAnd{
		field: field,
		value: value,
	}
	c.l = append(c.l, r)
}

func (c *Condition) AddTableMultiFieldNotLikeMatchAnd(table ITable, field []string, value string) {
	for i := range field {
		field[i] = table.TableName() + "." + field[i]
	}
	c.AddMultiFieldNotLikeMatchAnd(field, value)
}
