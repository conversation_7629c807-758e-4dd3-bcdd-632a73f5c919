package product

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	common "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"time"

	"hcscm/vars"
)

func GetStockProductDetailIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "stock_product_detail_id")
}

type StockProductDetailList []StockProductDetail

func (r StockProductDetailList) List() []StockProductDetail {
	return r
}

func (r StockProductDetailList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r StockProductDetailList) One() StockProductDetail {
	return r[0]
}

func (r StockProductDetailList) Pick(id uint64) (o StockProductDetail) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

// StockProductDetail 成品库存详情
type StockProductDetail struct {
	mysql_base.Model
	Id                            uint64                     `gorm:"column:id;primaryKey" relate:"stock_product_detail_id"`                   // 库存详情id
	StockProductId                uint64                     `gorm:"column:stock_product_id" relate:"stock_product_id,sum_stock_id"`          // 汇总库存id
	WarehouseBinId                uint64                     `gorm:"column:warehouse_bin_id" relate:"warehouse_bin_id"`                       // 仓位id
	WarehouseInTime               time.Time                  `gorm:"column:warehouse_in_time"`                                                // 入仓时间
	WarehouseInType               common.WarehouseGoodInType `gorm:"column:warehouse_in_type"`                                                // 来源类型
	WarehouseInOrderId            uint64                     `gorm:"column:warehouse_in_order_id"`                                            // 进仓单id
	WarehouseInOrderNo            string                     `gorm:"column:warehouse_in_order_no"`                                            // 进仓单号
	WarehouseOutOrderId           uint64                     `gorm:"column:warehouse_out_order_id"`                                           // 出仓单id
	WarehouseOutOrderNo           string                     `gorm:"column:warehouse_out_order_no"`                                           // 出仓单号
	DyeFactoryColorCode           string                     `gorm:"column:dye_factory_color_code"`                                           // 染厂色号
	ProductColorId                uint64                     `gorm:"column:product_color_id" relate:"product_color_id"`                       // 颜色id
	ProductColorKindId            uint64                     `gorm:"column:product_color_kind_id" relate:"product_color_kind_id"`             // 颜色类别id
	WarehouseId                   uint64                     `gorm:"column:warehouse_id" relate:"warehouse_id"`                               // 仓库id
	CustomerId                    uint64                     `gorm:"column:customer_id" relate:"biz_unit_id"`                                 // 所属客户id
	ProductId                     uint64                     `gorm:"column:product_id" relate:"product_id"`                                   // 成品id
	FinishProductWidth            string                     `gorm:"column:finish_product_width"`                                             // 成品幅宽
	FinishProductGramWeight       string                     `gorm:"column:finish_product_gram_weight"`                                       // 成品克重
	ProductLevelId                uint64                     `gorm:"column:product_level_id" relate:"product_level_id"`                       // 成品等级id
	ProductKindId                 uint64                     `gorm:"column:product_kind_id" relate:"product_kind_id"`                         // 布种类型id
	DyelotNumber                  string                     `gorm:"column:dyelot_number"`                                                    // 缸号
	VolumeNumber                  int                        `gorm:"column:volume_number"`                                                    // 卷号
	ProductRemark                 string                     `gorm:"column:product_remark"`                                                   // 成品备注
	WeightError                   int                        `gorm:"column:weight_error"`                                                     // 空差数量(公斤)
	PaperTubeWeight               int                        `gorm:"column:paper_tube_weight"`                                                // 纸筒数量(公斤)
	Weight                        int                        `gorm:"column:weight"`                                                           // 数量
	Length                        int                        `gorm:"column:length"`                                                           // 长度
	Roll                          int                        `gorm:"column:roll"`                                                             // 匹数
	DigitalCode                   string                     `gorm:"column:digital_code"`                                                     // 数字码
	ContractNumber                string                     `gorm:"column:contract_number"`                                                  // 合同号
	CustomerPoNum                 string                     `gorm:"column:customer_po_num"`                                                  // 客户po号
	CustomerAccountNum            string                     `gorm:"column:customer_account_num"`                                             // 客户款号
	ShelfNo                       string                     `gorm:"column:shelf_no"`                                                         // 货架号
	MeasurementUnitId             uint64                     `gorm:"column:measurement_unit_id" relate:"measurement_unit_id"`                 // 计量单位id(已作废，计量单位取成品资料数据)
	Remark                        string                     `gorm:"column:remark"`                                                           // 库存备注
	Status                        common_system.StockStatus  `gorm:"column:status"`                                                           // 库存状态1已入库2配布中3已出库
	QualityCheckStatus            common.QualityCheckStatus  `gorm:"column:quality_check_status"`                                             // 质检状态1未质检2已质检
	CheckStatus                   common_system.CheckStatus  `gorm:"column:check_status"`                                                     // 盘点状态1正常2盘点占用
	CheckUserId                   uint64                     `gorm:"column:check_user_id" relate:"user_id"`                                   // 盘点人id
	CheckTime                     time.Time                  `gorm:"column:check_time"`                                                       // 盘点时间
	BarCode                       string                     `gorm:"column:bar_code"`                                                         // 条码
	QrCode                        string                     `gorm:"column:qr_code"`                                                          // 二维码
	FinishProductWidthUnitId      uint64                     `gorm:"column:finish_product_width_unit_id" relate:"dictionary_detail_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                     `gorm:"column:finish_product_gram_weight_unit_id" relate:"dictionary_detail_id"` // 成品克重单位id(字典)
	SupplierId                    uint64                     `gorm:"column:supplier_id" relate:"biz_unit_id"`                                 // 供应商id
	// OutWeight                     int                        `gorm:"column:out_weight"`                                                       // 出库数量
	// OutLength                     int                        `gorm:"column:out_length"`                                                       // 出库长度
	// OutRoll                       int                        `gorm:"column:out_roll"`                                                         // 出库匹数
	FabricPieceCodeId uint64 `gorm:"column:fabric_piece_code_id" relate:"fabric_piece_code_id"` // 条码单id
	InternalRemark    string `gorm:"column:internal_remark"`                                    // 内部备注
	// BookRoll          int    `gorm:"column:book_roll"`                                          // 预约匹数
	// BookWeight        int    `gorm:"column:book_weight"`                                        // 预约数量
	// StockRoll         int    `gorm:"column:stock_roll"`                                         // 库存匹数
	SettleErrorWeight int `gorm:"column:settle_error_weight"` // 结算空差
}

// 查询后的钩子
func (r *StockProductDetail) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r StockProductDetail) GetId() uint64 {
	return r.Id
}

// TableName StockProductDetail 表名
func (StockProductDetail) TableName() string {
	return "stock_product_detail"
}

// 仓库，颜色id，缸号，卷号
func (r StockProductDetail) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddTableEqual(r, "warehouse_id", r.WarehouseId)
	cond.AddTableEqual(r, "dyelot_number", r.DyelotNumber)
	cond.AddTableEqual(r, "product_color_id", r.ProductColorId)
	cond.AddTableEqual(r, "volume_number", r.VolumeNumber)
	cond.AddNotEqual("id", r.Id)
	exist, err = mysql_base.ExistByCond(tx, &r, cond)
	if err != nil {
		return
	}
	if exist {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "卷号重复或者已存在,请检查。"), r.VolumeNumber)
		return
	}
	return false, nil
}

func (StockProductDetail) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (StockProductDetail) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeStockProductDetailNotExist
}

func (StockProductDetail) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeStockProductDetailAlreadyExist
}

func (r StockProductDetail) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	mysql.CommonDataSeparate(ctx, r, cond)
}

func (r *StockProductDetail) GenBarQrCode(ctx context.Context, noNumber int, productCode, productColorCode string) {
	now := time.Now()
	// 生成条码
	barCode := tools.NewFabricPieceBarCode(
		001,
		001,
		now,
		noNumber,
	)
	if r.BarCode == "" {
		r.BarCode = barCode.GenerateBarCode()
	}

	// 生成二维码
	qrCode := tools.NewFabricPieceCodeQrCode66(
		metadata.GetCompanyId(ctx),
		productCode,
		productColorCode,
		001,
		r.DyelotNumber,
		r.VolumeNumber,
		r.Weight,
		r.BarCode,
	)

	if r.QrCode == "" {
		r.QrCode = qrCode.GenerateQrCode()
	}
}

func NewStockProductDetail(
	ctx context.Context,
	p *structure.AddStockProductDetailParam,
	stockProductId uint64,
) (r StockProductDetail) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.StockProductId = stockProductId
	r.WarehouseBinId = p.WarehouseBinId
	r.WarehouseInTime = time.Now()
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.ProductColorId = p.ProductColorId
	r.ProductColorKindId = p.ProductColorKindId
	r.WarehouseId = p.WarehouseId
	r.CustomerId = p.CustomerId
	r.ProductId = p.ProductId
	r.FinishProductWidth = p.FinishProductWidth
	r.FinishProductGramWeight = p.FinishProductGramWeight
	r.ProductLevelId = p.ProductLevelId
	r.ProductKindId = p.ProductKindId
	r.DyelotNumber = p.DyelotNumber
	r.VolumeNumber = p.VolumeNumber
	r.ProductRemark = p.ProductRemark
	r.WeightError = p.WeightError
	r.SettleErrorWeight = p.SettleErrorWeight
	r.PaperTubeWeight = p.PaperTubeWeight
	r.Weight = p.Weight
	r.Length = p.Length
	r.Roll = p.Roll
	r.DigitalCode = p.DigitalCode
	r.ContractNumber = p.ContractNumber
	r.CustomerPoNum = p.CustomerPoNum
	r.CustomerAccountNum = p.CustomerAccountNum
	r.ShelfNo = p.ShelfNo
	// r.MeasurementUnitId = p.MeasurementUnitId
	r.Remark = p.Remark
	r.InternalRemark = p.InternalRemark
	r.WarehouseInType = p.WarehouseInType
	r.WarehouseInOrderId = p.WarehouseInOrderId
	r.WarehouseInOrderNo = p.WarehouseInOrderNo
	if p.WarehouseInType == common.WarehouseGoodInTypeSaleAllocate {
		r.Status = common_system.StockStatusArrange
	} else {
		r.Status = common_system.StockStatusWarehouseIn
	}
	r.QualityCheckStatus = common.QualityCheckStatusWait
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.BarCode = p.BarCode
	r.QrCode = p.QrCode
	r.SupplierId = p.SupplierId

	// r.BookRoll = p.BookRoll
	// r.BookWeight = p.BookWeight
	// r.StockRoll = p.StockRoll
	return
}

// 出仓,盘点(具体参数请自己添加)
func (r *StockProductDetail) UpdateStockProductDetail(
	ctx context.Context,
	p *structure.UpdateStockProductDetailParam,
) {
	defer func() {
		if p.Status != 0 {
			r.Status = p.Status
			return
		}
		// 如果库存的长度和数量都为0，则表示该库存已完全出库
		if r.Roll == 0 && r.Weight == 0 {
			r.Status = common_system.StockStatusWarehouseOut
		} else {
			// 有库存修改为已入库(盘点不能盘点配布中的成品，所以如果还有库存可以改为已入库)
			r.Status = common_system.StockStatusWarehouseIn
		}
	}()
	// 移架
	if p.Type == 3 {
		r.WarehouseBinId = p.TargetWarehouseBinId
		return
	}
	// 调整(调整库存备注，所属客户，成品等级)
	if p.Type == 5 {
		if p.WarehouseOutOrderId != 0 {
			r.WarehouseOutOrderId = p.WarehouseOutOrderId
			r.WarehouseOutOrderNo = p.WarehouseOutOrderNo
		}
		r.StockProductId = p.StockProductId
		r.Weight += p.Weight
		r.Length += p.Length
		r.Roll += p.Roll
		r.WeightError += p.WeightError
		r.ProductLevelId = p.ProductLevelId
		r.CustomerId = p.CustomerId
		r.Remark = p.Remark

		r.WarehouseBinId = p.WarehouseBinId
		r.DigitalCode = p.DigitalCode
		r.ProductRemark = p.ProductRemark
		r.InternalRemark = p.InternalRemark
		r.DyeFactoryColorCode = p.DyeFactoryColorCode
		r.CustomerAccountNum = p.CustomerAccountNum
		r.ShelfNo = p.ShelfNo
		r.FinishProductWidth = p.FinishProductWidth
		r.FinishProductGramWeight = p.FinishProductGramWeight
		r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
		r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
		return
	}
	// 退货进仓
	if p.Type == 6 {
		r.StockProductId = p.StockProductId
		r.Weight += p.Weight
		r.Length += p.Length
		r.Roll += p.Roll
		r.ProductLevelId = p.ProductLevelId
		r.CustomerId = p.CustomerId
		r.Remark = p.Remark

		r.WarehouseBinId = p.WarehouseBinId
		r.WeightError = p.WeightError
		r.PaperTubeWeight = p.PaperTubeWeight
		r.DigitalCode = p.DigitalCode
		r.ProductRemark = p.ProductRemark
		r.InternalRemark = p.InternalRemark
		r.DyeFactoryColorCode = p.DyeFactoryColorCode
		r.CustomerAccountNum = p.CustomerAccountNum
		r.ShelfNo = p.ShelfNo
		r.FinishProductWidth = p.FinishProductWidth
		r.FinishProductGramWeight = p.FinishProductGramWeight
		r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
		r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
		return
	}
	// 成品库存盘点
	if p.CheckUserId != 0 {
		r.WarehouseBinId = p.WarehouseBinId
		r.Weight += p.Weight
		r.Length += p.Length
		r.Roll += p.Roll
		r.CheckTime = p.CheckTime
		r.CheckUserId = p.CheckUserId

		r.ProductRemark = p.ProductRemark
		r.InternalRemark = p.InternalRemark
		r.DyeFactoryColorCode = p.DyeFactoryColorCode
		r.DyelotNumber = p.DyelotNumber
		r.ContractNumber = p.ContractNumber
		r.CustomerAccountNum = p.CustomerAccountNum
		r.ShelfNo = p.ShelfNo
		r.FinishProductWidth = p.FinishProductWidth
		r.FinishProductGramWeight = p.FinishProductGramWeight
		r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
		r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
		return
	}
	// 出仓(仅根据库存添加适用)
	if p.WarehouseOutOrderId != 0 {
		r.WarehouseOutOrderId = p.WarehouseOutOrderId
		r.WarehouseOutOrderNo = p.WarehouseOutOrderNo
		r.Weight += p.Weight
		r.Length += p.Length
		r.Roll += p.Roll
		r.WeightError += p.WeightError
		r.PaperTubeWeight += p.PaperTubeWeight
	}
}

// 占用细码库存
func (r *StockProductDetail) UpdateStockProductDetailStatusArrange(
	ctx context.Context,
) {
	r.Status = common_system.StockStatusArrange
}

// 释放细码库存
// （配布单取消了出货单）
func (r *StockProductDetail) UpdateStockProductDetailStatusRelease(
	ctx context.Context,
) {
	r.Status = common_system.StockStatusWarehouseIn
}

// 出库
func (r *StockProductDetail) UpdateStockProductDetailStatusOut(
	ctx context.Context,
) {
	if r.Roll+r.Weight == 0 {
		r.Status = common_system.StockStatusWarehouseOut
	}
}

// 盘点
func (r *StockProductDetail) UpdateCheckStatus(
	ctx context.Context,
) {
	r.CheckStatus = common_system.CheckStatusChecking
	r.CheckTime = time.Now()
	r.CheckUserId = metadata.GetUserId(ctx)
}

// 返回库存（配布单取消了出货单）
func (r *StockProductDetail) UpdateStockProductDetailStatusIn(
	ctx context.Context,
) {
	r.Status = common_system.StockStatusWarehouseIn
}

// 更新
func (r *StockProductDetail) IsEqualAndUpdate(
	ctx context.Context,
	p *structure.AddStockProductDetailParam,
) (noUpdate bool) {
	defer func() {
		if noUpdate {
			return
		}

		r.Roll += p.Roll
		r.Weight += p.Weight

		// 如果库存的长度和数量都为0，则表示该库存已完全出库
		if r.Roll+r.Weight == 0 {
			r.Status = common_system.StockStatusWarehouseOut
		} else {
			// 有库存修改为已入库(盘点不能盘点配布中的成品，所以如果还有库存可以改为已入库)
			r.Status = common_system.StockStatusWarehouseIn
		}

	}()

	if r.Roll != 0 || r.Weight != 0 {
		noUpdate = true
		return
	}

	if r.WarehouseBinId != p.WarehouseBinId {
		noUpdate = true
		return
	}

	if r.WeightError != p.WeightError {
		noUpdate = true
		return
	}
	if r.PaperTubeWeight != p.PaperTubeWeight {
		noUpdate = true
		return
	}
	if r.DigitalCode != p.DigitalCode {
		noUpdate = true
		return
	}
	if r.ShelfNo != p.ShelfNo {
		noUpdate = true
		return
	}
	if r.FinishProductWidth != p.FinishProductWidth {
		noUpdate = true
		return
	}
	if r.FinishProductWidthUnitId != p.FinishProductWidthUnitId {
		noUpdate = true
		return
	}
	if r.FinishProductGramWeight != p.FinishProductGramWeight {
		noUpdate = true
		return
	}
	if r.FinishProductGramWeightUnitId != p.FinishProductGramWeightUnitId {
		noUpdate = true
		return
	}
	if r.DyeFactoryColorCode != p.DyeFactoryColorCode {
		noUpdate = true
		return
	}
	if r.CustomerAccountNum != p.CustomerAccountNum {
		noUpdate = true
		return
	}
	return
}
