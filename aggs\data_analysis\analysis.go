package data_analysis

import (
	"context"
	"fmt"
	"hcscm/aggs/biz_unit"
	should_collect_order_agg "hcscm/aggs/should_collect_order"
	common "hcscm/common/should_collect_order"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	info_base_data_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	product2 "hcscm/extern/pb/product"
	"hcscm/model/mysql/basic_data/type_basic_data"
	type_dao "hcscm/model/mysql/basic_data/type_basic_data/dao"
	mysql_biz_unit "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/mysql_base"
	model_should_collect_order "hcscm/model/mysql/should_collect_order"
	mysql "hcscm/model/mysql/should_collect_order/dao"
	structure "hcscm/structure/basic_data/type_basic_data"
	"hcscm/structure/data_analysis"
	structure_should_collect_order "hcscm/structure/should_collect_order"
	"hcscm/tools"
	set2 "hcscm/tools/set"
	"hcscm/vars"
	"math"
	"slices"
	"sort"
	"strconv"
	"strings"
	"time"
)

// fabricTypeInfo 布料类型信息
type FabricTypeInfo struct {
	TypeId   uint64
	TypeName string
	Amount   float64
}

type DataAnalysisRepo struct {
	tx *mysql_base.Tx
}

func extractCustomerCityMap(bizUnits []*mysql_biz_unit.BizUnit) map[uint64]string {
	customerCityMap := make(map[uint64]string)
	for _, logistics := range bizUnits {
		if logistics.Location != "" {
			parts := strings.Split(logistics.Location, ",")
			if len(parts) >= 2 {
				// 先检查parts[0]是否包含"市"
				if strings.Contains(parts[0], "市") {
					customerCityMap[logistics.Id] = parts[0]
				} else if len(parts) >= 3 && strings.Contains(parts[1], "市") {
					// 如果parts[0]不包含"市",则检查parts[1]
					customerCityMap[logistics.Id] = parts[1]
				} else if len(parts) >= 3 && strings.Contains(parts[2], "市") {
					// 如果parts[1]不包含"市",则检查parts[2]
					customerCityMap[logistics.Id] = parts[2]
				} else {
					customerCityMap[logistics.Id] = "其他"
				}
			} else {
				customerCityMap[logistics.Id] = "其他"
			}
		} else {
			customerCityMap[logistics.Id] = "其他"
		}
	}
	return customerCityMap
}

func NewDataAnalysisRepo(tx *mysql_base.Tx) *DataAnalysisRepo {
	return &DataAnalysisRepo{tx: tx}
}

// 计算统计数据
func calcPeriodStats(details []data_analysis.WorkbenchData) data_analysis.PeriodStats {
	stats := data_analysis.PeriodStats{
		BigCustomerIds:     make(map[uint64]struct{}),
		PlateCustomerIds:   make(map[uint64]struct{}),
		BigSettleWeights:   make(map[uint64]float64),
		PlateSettleWeights: make(map[uint64]float64),
	}

	for _, detail := range details {
		if detail.CollectType == int64(common.CollectTypeProductSale) {
			stats.SaleAmount += detail.SettlePrice
		} else if detail.CollectType == int64(common.CollectTypeProductReturn) {
			stats.SaleAmount += detail.SettlePrice
		}
		if detail.SaleMode == 1 || detail.SaleMode == 3 {
			stats.BigCustomerIds[detail.CustomerId] = struct{}{}
			stats.BigSettleWeights[detail.MeasurementUnitId] += detail.SettleWeight
			stats.BigOrderWeights += detail.SettleWeight
		} else if detail.SaleMode == 2 || detail.SaleMode == 4 {
			stats.PlateCustomerIds[detail.CustomerId] = struct{}{}
			stats.PlateSettleWeights[detail.MeasurementUnitId] += detail.SettleWeight
			stats.PlateWeights += detail.SettleWeight
		}
		stats.Roll += detail.Roll
	}
	return stats
}

// 计算较上期比率
func calcRate(curr, last float64) string {
	if last == 0 {
		return "0%"
	}
	rate := (curr - last) / math.Abs(last) * 100
	return fmt.Sprintf("%d", int(rate))
}

// 格式化重量字符串，保留两位小数
func formatWeightStrs(weights map[uint64]float64, unitIds []uint64, unitNames map[uint64]string) string {
	var weightStrs []string
	for _, unitId := range unitIds {
		if weight, ok := weights[unitId]; ok {
			weight = weight / vars.Weight
			weightStrs = append(weightStrs, fmt.Sprintf("%.2f%s", weight, unitNames[unitId]))
		}
	}
	return strings.Join(weightStrs, "、")
}

// GetWorkbenchData 获取工作台数据
func (r *DataAnalysisRepo) GetWorkbenchData(ctx context.Context, req *data_analysis.GetWorkbenchDataReq) (res data_analysis.GetWorkbenchDataRes, err error) {
	var (
		details []data_analysis.WorkbenchData
		// detailsGetSaleMoment []data_analysis.WorkbenchData
		lastDetails   []data_analysis.WorkbenchData
		unitNames     map[uint64]string
		unitIds       = set2.NewUint64Set()
		totalOweMoney float64 // 总欠款
	)

	// 获取当前期间数据
	details, err = mysql.GetShouldCollectOrdersByTimeRange(r.tx, string(req.StartTime), string(req.EndTime))
	if err != nil {
		return
	}

	// 获取所有期间的数据
	// detailsGetSaleMoment, err = mysql.GetShouldCollectOrdersByTimeRange(r.tx, "", "")
	// if err != nil {
	// 	return
	// }

	// 获取上期数据
	lastDetails, err = mysql.GetShouldCollectOrdersByTimeRange(r.tx, string(req.LastStartTime), string(req.LastEndTime))
	if err != nil {
		return
	}

	// 计算当前期间统计数据
	currStats := calcPeriodStats(details)
	// 计算上期统计数据
	lastStats := calcPeriodStats(lastDetails)
	// 计算销售统计数据
	// saleStats := calcPeriodStats(detailsGetSaleMoment)
	startTime, _ := time.Parse("2006-01-02", string(req.StartTime))
	endTime, _ := time.Parse("2006-01-02", string(req.EndTime))
	dateDiff := int(endTime.Sub(startTime).Hours()/24) + 1
	// 查询欠款数据
	repo := should_collect_order_agg.NewReportFormsRepo(r.tx)
	customerOweMoneys, _, err := repo.GetCustomerOweMoneyList(ctx, &structure_should_collect_order.GetCustomerOweMoneyListQuery{})
	if err != nil {
		return
	}
	for _, customerOweMoney := range customerOweMoneys {
		totalOweMoney += float64(customerOweMoney.EndPeriod)
	}
	// 如果日期间隔小于15天，使用15天作为除数
	// 如果日期间隔大于等于15天，使用实际间隔天数
	// if dateDiff < 15 {
	//	days = 15
	// } else {
	//	days = dateDiff
	// }

	// 计算各种日均值
	saleAmountDailyAvg := fmt.Sprintf("%.2f", (currStats.SaleAmount/vars.PriceMult)/float64(dateDiff))
	bigOrderDailyAvg := fmt.Sprintf("%.2f", (currStats.BigOrderWeights/vars.Weight)/float64(dateDiff))
	plateOrderDailyAvg := fmt.Sprintf("%.2f", (currStats.PlateWeights)/vars.Weight/float64(dateDiff))
	rollDailyAvg := fmt.Sprintf("%.2f", float64(currStats.Roll/vars.Roll)/float64(dateDiff))
	bigCustomerDailyAvg := fmt.Sprintf("%.2f", float64(len(currStats.BigCustomerIds))/float64(dateDiff))
	smallCustomerDailyAvg := fmt.Sprintf("%.2f", float64(len(currStats.PlateCustomerIds))/float64(dateDiff))
	totalCountDailyAvg := fmt.Sprintf("%.2f", float64(len(currStats.BigCustomerIds)+len(currStats.PlateCustomerIds))/float64(dateDiff))
	// 获取计量单位名称
	for unitId := range currStats.BigSettleWeights {
		unitIds.Add(unitId)
	}
	for unitId := range currStats.PlateSettleWeights {
		unitIds.Add(unitId)
	}
	// sort.Slice(unitIds, func(i, j int) bool {
	//	return unitIds[i] < unitIds[j]
	// })
	unitNames, err = base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}

	// 构建返回结果
	res = data_analysis.GetWorkbenchDataRes{
		SaleAmount:            currStats.SaleAmount / vars.PriceMult,
		SaleAmountLastRate:    calcRate(currStats.SaleAmount, lastStats.SaleAmount),
		SaleAmountDailyAvg:    saleAmountDailyAvg,
		Roll:                  currStats.Roll / vars.Roll,
		RollLastRate:          calcRate(float64(currStats.Roll), float64(lastStats.Roll)),
		RollDailyAvg:          rollDailyAvg,
		BigCustomerCount:      len(currStats.BigCustomerIds),
		BigCustomerLastRate:   calcRate(float64(len(currStats.BigCustomerIds)), float64(len(lastStats.BigCustomerIds))),
		BigCustomerDailyAvg:   bigCustomerDailyAvg,
		BigOrderWeight:        formatWeightStrs(currStats.BigSettleWeights, unitIds.List(), unitNames),
		BigOrderLastRate:      calcRate(currStats.BigOrderWeights, lastStats.BigOrderWeights),
		BigOrderDailyAvg:      bigOrderDailyAvg,
		PlateOrderWeight:      formatWeightStrs(currStats.PlateSettleWeights, unitIds.List(), unitNames),
		PlateOrderLastRate:    calcRate(currStats.PlateWeights, lastStats.PlateWeights),
		PlateOrderDailyAvg:    plateOrderDailyAvg,
		SmallCustomerCount:    len(currStats.PlateCustomerIds),
		SmallCustomerLastRate: calcRate(float64(len(currStats.PlateCustomerIds)), float64(len(lastStats.PlateCustomerIds))),
		SmallCustomerDailyAvg: smallCustomerDailyAvg,
		TotalCount: func() int {
			totalCustomers := make(map[uint64]struct{})
			for id := range currStats.BigCustomerIds {
				totalCustomers[id] = struct{}{}
			}
			for id := range currStats.PlateCustomerIds {
				totalCustomers[id] = struct{}{}
			}
			return len(totalCustomers)
		}(),
		TotalCountLastRate: func() string {
			currTotal := make(map[uint64]struct{})
			lastTotal := make(map[uint64]struct{})
			for id := range currStats.BigCustomerIds {
				currTotal[id] = struct{}{}
			}
			for id := range currStats.PlateCustomerIds {
				currTotal[id] = struct{}{}
			}
			for id := range lastStats.BigCustomerIds {
				lastTotal[id] = struct{}{}
			}
			for id := range lastStats.PlateCustomerIds {
				lastTotal[id] = struct{}{}
			}
			return calcRate(float64(len(currTotal)), float64(len(lastTotal)))
		}(),
		TotalCountDailyAvg: totalCountDailyAvg,
	}

	// 获取预收单金额
	advanceCollectAmount, err := mysql.GetAdvanceCollectOrderAmount(r.tx, string(req.StartTime), string(req.EndTime))
	if err != nil {
		return
	}
	res.AdvanceCollectAmount = math.Round(advanceCollectAmount*100) / 100

	// 获取实收单金额
	actuallyCollectAmount, err := mysql.GetActuallyCollectOrderAmount(r.tx, string(req.StartTime), string(req.EndTime))
	if err != nil {
		return
	}
	res.ActuallyCollectAmount = math.Round(actuallyCollectAmount*100) / 100

	// 计算总欠款
	// res.TotalDebt = math.Round((saleStats.SaleAmount+res.ActuallyCollectAmount+res.AdvanceCollectAmount)*100) / 100

	res.TotalDebt = totalOweMoney / vars.PriceMult

	return
}

// GetAnalysisHomeData 获取产品分析首页数据
func (r *DataAnalysisRepo) GetAnalysisHomeData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetAnalysisHomeRes, err error) {
	var (
		details              []data_analysis.ProductAnalysisData
		detailsForChart      []data_analysis.ProductAnalysisData
		productMap           map[uint64]*product.ProductRes
		settleWeights        = make(map[uint64]float64)
		totalSaleAmount      float64
		totalRoll            int64
		customerSaleGroupMap = make(map[uint64]uint64)
		// 用于图表统计的map（不含退货）
		chartSaleGroupSales   = make(map[uint64]float64)
		chartNewCustomerSales = make(map[uint64]float64)
		chartOldCustomerSales = make(map[uint64]float64)
		chartTotalSaleAmount  float64
		// 新增：用于统计布料类型销售额
		fabricTypeSales = make(map[uint64]*FabricTypeInfo)
		// 新增：产品类别占比和客户占比
		top10ProductCategoryRatio []data_analysis.ProductCategoryRatio
		newCustomerRatio          string
		oldCustomerRatio          string
		saleGroupRatios           []data_analysis.SaleGroupRatio
		fabricTypeSaleList        []*FabricTypeInfo
		unitNameMapIds            = make(map[uint64]string)
		repo                      = biz_unit.NewRepo(r.tx)
		typeFabricIdMapParentId   = make(map[uint64]uint64)
		typeFabricIdMapInfo       = make(map[uint64]*type_dao.TypeFabric)
		citySales                 = make(map[string]float64)  // 初始化城市销售额统计map
		customerCityMap           = make(map[uint64]string)   // 创建客户ID到城市的映射
		historyCustomerIds        = make(map[uint64]struct{}) // 统计历史客户ID和客户ID
	)

	// 获取订单明细数据（包含退货的）
	details, err = mysql.GetProductAnalysisData(r.tx, req, 1, false)
	if err != nil {
		return
	}

	// 获取订单明细数据（不包含退货的）
	detailsForChart, err = mysql.GetProductAnalysisData(r.tx, req, 2, false)
	if err != nil {
		return
	}

	// 获取历史订单数据(查询开始时间之前的订单)
	req.CustomerRatioDate = tools.QueryTime(time.Now().AddDate(0, 0, -31).Format("2006-01-02"))
	historyDetails, err := mysql.GetHistoryProductAnalysisData(r.tx)
	if err != nil {
		return
	}
	customerRatioTime, err := time.Parse("2006-01-02", string(req.CustomerRatioDate))
	if err != nil {
		return res, err
	}

	// 获取所有产品ID并获取产品信息
	productIds := mysql_base.GetUInt64List(details, "product_id")
	customerIds := mysql_base.GetUInt64List(details, "customer_id")
	customerIdsForChart := mysql_base.GetUInt64List(detailsForChart, "customer_id")
	unitIds := mysql_base.GetUInt64List(details, "measurement_unit_id")
	productMap, _ = product.NewProductClient().GetProductMapByIds(ctx, productIds)
	unitNameMapIds, _ = info_base_data_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
	bizUnits, _ := repo.QueryMultiBizUnit(ctx, customerIds)
	saleList, _ := mysql.FindBizUnitSaleByUnitIds(r.tx, customerIdsForChart)
	typeFabricList, _ := type_basic_data.SearchTypeFabricNoPage(r.tx, &structure.GetTypeFabricListQuery{})
	// 统计历史客户ID和当前客户ID
	historyCustomerIds = r.GethistoryCustomerIds(ctx, historyDetails, customerRatioTime)
	for _, sale := range saleList {
		customerSaleGroupMap[sale.UnitID] = sale.SaleGroupID
	}
	// 取出所有typefabric内容
	for i := range typeFabricList {
		typeFabricIdMapParentId[typeFabricList[i].Id] = typeFabricList[i].ParentID
		typeFabricIdMapInfo[typeFabricList[i].Id] = &typeFabricList[i]
	}
	// 从物流地址中提取城市信息
	customerCityMap = extractCustomerCityMap(bizUnits)
	// 统计图表相关数据（不含退货）和布料类型销售额
	for _, d := range detailsForChart {
		// 统计每个城市的销售额（使用details，包含所有数据）
		if city, ok := customerCityMap[d.CustomerId]; ok {
			citySales[city] += d.SaleAmount
		}
		if saleGroupId := customerSaleGroupMap[d.CustomerId]; saleGroupId > 0 {
			chartSaleGroupSales[saleGroupId] += d.SaleAmount
		}
		chartTotalSaleAmount += d.SaleAmount
		// 统计新老客户销售额
		if _, ok := historyCustomerIds[d.CustomerId]; ok {
			chartOldCustomerSales[d.CustomerId] += d.SaleAmount
		} else {
			chartNewCustomerSales[d.CustomerId] += d.SaleAmount
		}
		if _, ok := fabricTypeSales[d.ProductId]; ok {
			fabricTypeSales[d.ProductId].Amount += d.SaleAmount
		} else {
			if typeFabricIdMapParentId[productMap[d.ProductId].TypeGreyFabricId] != 0 {
				fabricTypeSales[d.ProductId] = &FabricTypeInfo{
					TypeId:   typeFabricIdMapParentId[productMap[d.ProductId].TypeGreyFabricId],
					TypeName: typeFabricIdMapInfo[typeFabricIdMapParentId[productMap[d.ProductId].TypeGreyFabricId]].Name,
					Amount:   d.SaleAmount,
				}
			} else {
				// 统计布料类型销售额
				fabricTypeSales[d.ProductId] = &FabricTypeInfo{
					TypeId:   productMap[d.ProductId].TypeGreyFabricId,
					TypeName: productMap[d.ProductId].TypeGreyFabricName,
					Amount:   d.SaleAmount,
				}
			}
		}
	}
	newCustomerRatio, oldCustomerRatio = getNewOldCustomerRatio(chartTotalSaleAmount, chartNewCustomerSales, chartOldCustomerSales)

	// 合并相同TypeId的布料类型销售额
	mergedFabricTypeSales := make(map[uint64]*FabricTypeInfo)
	for _, info := range fabricTypeSales {
		typeId := info.TypeId
		if existing, ok := mergedFabricTypeSales[typeId]; ok {
			// 如果已存在相同TypeId的记录，合并销售额
			existing.Amount += info.Amount
		} else {
			// 否则添加新记录
			mergedFabricTypeSales[typeId] = &FabricTypeInfo{
				TypeId:   info.TypeId,
				TypeName: info.TypeName,
				Amount:   info.Amount,
			}
		}
	}
	// 将合并后的布料类型销售额转换为切片并排序
	fabricTypeSaleList = nil // 清空原有列表
	for _, info := range mergedFabricTypeSales {
		fabricTypeSaleList = append(fabricTypeSaleList, info)
	}
	sort.Slice(fabricTypeSaleList, func(i, j int) bool {
		return fabricTypeSaleList[i].Amount > fabricTypeSaleList[j].Amount
	})
	var displayTotalAmount float64

	for _, fts := range fabricTypeSaleList {
		displayTotalAmount += fts.Amount
	}

	// 重新计算前9条的占比，确保总和为100%
	for i, fts := range fabricTypeSaleList {
		ratio := "0%"
		if displayTotalAmount > 0 {
			// 使用前9条的总额计算百分比
			ratio = fmt.Sprintf("%.2f%%", fts.Amount/displayTotalAmount*100)
		}
		if i < 9 {
			info := data_analysis.ProductCategoryRatio{
				TypeId:    fts.TypeId,
				TypeName:  fts.TypeName,
				SaleRatio: ratio,
			}
			top10ProductCategoryRatio = append(top10ProductCategoryRatio, info)
		}
	}
	//else {
	//	otherAmount += fts.Amount
	//}
	// 添加"其他"类型
	//if chartTotalSaleAmount > 0 && otherAmount > 0 {
	//	top10ProductCategoryRatio = append(top10ProductCategoryRatio, data_analysis.ProductCategoryRatio{
	//		TypeId:    0,
	//		TypeName:  "其他",
	//		SaleRatio: fmt.Sprintf("%.2f%%", otherAmount/chartTotalSaleAmount*100),
	//	})
	//}
	// 统计数据
	for _, d := range details {
		totalSaleAmount += d.SaleAmount
		totalRoll += d.TotalRoll
		settleWeights[d.MeasurementUnitId] += d.TotalSettleWeight
	}
	// 准备销售地区统计数据
	var cityOtherAmount float64
	salesAreaStat := make([]data_analysis.SalesAreaStat, 0)
	getSalesAreaStat(citySales, &salesAreaStat, &cityOtherAmount)

	// 计算销售群体占比
	var saleGroupList []struct {
		id     uint64
		amount float64
	}
	for groupId, amount := range chartSaleGroupSales {
		saleGroupList = append(saleGroupList, struct {
			id     uint64
			amount float64
		}{groupId, amount})
	}

	// 按销售金额排序并只保留前10个销售群体
	sort.Slice(saleGroupList, func(i, j int) bool {
		return saleGroupList[i].amount > saleGroupList[j].amount
	})
	if len(saleGroupList) > 10 {
		saleGroupList = saleGroupList[:10]
	}

	// 获取销售群体名称并计算占比
	saleGroupIds := make([]uint64, 0, len(saleGroupList))
	for _, sg := range saleGroupList {
		saleGroupIds = append(saleGroupIds, sg.id)
	}
	saleGroupNames, err := mysql.QuerySaleGroupNameByIds(r.tx, saleGroupIds)
	if err != nil {
		return
	}

	// 计算所有销售群体的总金额，用于重新计算占比
	var totalSalesAmount float64
	for _, sg := range saleGroupList {
		totalSalesAmount += sg.amount
	}

	// 重新计算每个群体的占比，确保总和为100%
	for _, sg := range saleGroupList {
		ratio := "0%"
		if totalSalesAmount > 0 {
			// 使用新的总额计算百分比
			ratio = fmt.Sprintf("%.2f%%", sg.amount/totalSalesAmount*100)
		}
		saleGroupRatios = append(saleGroupRatios, data_analysis.SaleGroupRatio{
			SaleGroupId:   sg.id,
			SaleGroupName: saleGroupNames[sg.id],
			SaleRatio:     ratio,
		})
	}

	// 已去掉"其他"类别的添加逻辑，保证所有显示的类别百分比总和为100%

	// 新增：计算客户销售占比（仅在type=2时计算）
	var customerSaleRatios []data_analysis.Top10CustomerSaleRatio
	if req.Type == 2 {
		// 合并新老客户销售额到一个map
		customerSales := make(map[uint64]float64)
		for customerId, amount := range chartNewCustomerSales {
			customerSales[customerId] = amount
		}
		for customerId, amount := range chartOldCustomerSales {
			customerSales[customerId] = amount
		}

		// 转换为切片并排序
		type customerSale struct {
			customerId uint64
			amount     float64
		}
		var customerSaleList []customerSale
		for customerId, amount := range customerSales {
			customerSaleList = append(customerSaleList, customerSale{customerId, amount})
		}
		sort.Slice(customerSaleList, func(i, j int) bool {
			return customerSaleList[i].amount > customerSaleList[j].amount
		})

		// 获取客户名称
		customerIds := make([]uint64, 0)
		for _, cs := range customerSaleList {
			customerIds = append(customerIds, cs.customerId)
		}
		customerNames, err := mysql.QueryCustomerNameByIds(r.tx, customerIds)
		if err != nil {
			return res, err
		}

		var displayTotalAmount float64

		for _, cs := range customerSaleList {
			displayTotalAmount += cs.amount
		}

		// 重新计算前九条客户的销售占比，确保总和为100%
		//var otherAmount float64
		for i, cs := range customerSaleList {
			ratio := "0%"
			if displayTotalAmount > 0 {
				// 使用前9条的总额计算百分比
				ratio = fmt.Sprintf("%.2f%%", cs.amount/displayTotalAmount*100)
			}
			if i < 9 {
				customerSaleRatios = append(customerSaleRatios, data_analysis.Top10CustomerSaleRatio{
					CustomerId:   cs.customerId,
					CustomerName: customerNames[cs.customerId],
					SaleRatio:    ratio,
				})
			}
			//else {
			//	otherAmount += cs.amount
			//}
		}

		// 添加"其他"类别
		//if chartTotalSaleAmount > 0 && otherAmount > 0 {
		//	customerSaleRatios = append(customerSaleRatios, data_analysis.Top10CustomerSaleRatio{
		//		CustomerId:   0,
		//		CustomerName: "其他",
		//		SaleRatio:    fmt.Sprintf("%.2f%%", otherAmount/chartTotalSaleAmount*100),
		//	})
		//}
	}

	// 构建返回结果
	res = data_analysis.GetAnalysisHomeRes{
		TotalSaleAmount:           totalSaleAmount / vars.PriceMult,
		TotalRoll:                 totalRoll / vars.Roll,
		TotalSettleWeight:         formatWeightStrs(settleWeights, unitIds, unitNameMapIds),
		TotalCustomerCount:        len(customerIds),
		NewCustomerRatio:          newCustomerRatio,
		OldCustomerRatio:          oldCustomerRatio,
		Top10SaleGroupRatio:       saleGroupRatios,
		Top10ProductCategoryRatio: top10ProductCategoryRatio,
		SalesAreaStat:             salesAreaStat,
	}

	if req.Type == 2 { // 客户分析
		// 设置客户分析相关字段
		res.Top10ProductCategoryRatio = nil             // type=2时不返回产品类别占比
		res.Top10CustomerSaleRatio = customerSaleRatios // 返回客户销售占比
	}

	return
}

// GetProAnalysisTrendData 获取产品分析趋势数据
func (r *DataAnalysisRepo) GetProAnalysisTrendData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetProAnalysisTrendRes, err error) {
	currentTime := time.Date(time.Now().Year(), time.Now().Month(), time.Now().Day(), 23, 59, 59, 0, time.Now().Location()).AddDate(0, 0, 0)
	var (
		dayCount, monthCount int
		startTime, endTime   time.Time
		isUpdateEstimateRoll bool
	)
	// 根据Trendtype区分日分析和月分析
	if req.Type == 1 { // 日分析
		if req.TrendStartTime.IsYMDZero() || req.TrendEndTime.IsYMDZero() {
			// 没有筛选时间，取最近15天
			endTime = currentTime
			startTime = endTime.AddDate(0, 0, -15) // 往前推14天，总共15天
			dayCount = int(endTime.Sub(startTime).Hours() / 24)
			req.IsTrendForDay = true
		} else {
			startTime = req.TrendStartTime.ToTimeYMD()
			endTime = req.TrendEndTime.ToTimeEndOfDay()
			// 检查时间范围是否超过15天
			days := int(endTime.Sub(startTime).Hours() / 24)
			if days > 15 {
				return res, fmt.Errorf("日分析时间范围不能超过15天")
			}
			dayCount = int(endTime.Sub(startTime).Hours()/24) + 1
		}

		// 初始化三年数据的切片
		res.ThreeYearRoll.ThisYear = make([]int64, dayCount)
		res.ThreeYearRoll.LastYear = make([]int64, dayCount)
		res.ThreeYearRoll.BeforeYear = make([]int64, dayCount)

		thisYearSaleAmount := make([]float64, dayCount)
		lastYearSaleAmount := make([]float64, dayCount)
		beforeYearSaleAmount := make([]float64, dayCount)

		thisYearSettleWeight := make([]float64, dayCount)
		lastYearSettleWeight := make([]float64, dayCount)
		beforeYearSettleWeight := make([]float64, dayCount)

		// 遍历每一天
		for dayIndex := 0; dayIndex < dayCount; dayIndex++ {
			currentDayStart := startTime.AddDate(0, 0, dayIndex)
			currentDayEnd := currentDayStart.Add(24*time.Hour - time.Second)
			if currentDayEnd.After(endTime) {
				currentDayEnd = endTime
			}

			// 判断是否是未来日期
			isFutureDay := currentDayStart.After(currentTime)
			isCurrentDay := currentDayEnd.Day() == currentTime.Day()
			// 遍历三年
			for year := 0; year < 3; year++ {
				// 计算目标年份的同期时间
				targetDayStart := currentDayStart.AddDate(-year, 0, 0)
				targetDayEnd := currentDayEnd.AddDate(-year, 0, 0)
				req.TrendStartTime = tools.QueryTime(targetDayStart.Format("2006-01-02"))
				req.TrendEndTime = tools.QueryTime(targetDayEnd.Format("2006-01-02"))
				// 查询数据
				details, err := mysql.GetProductAnalysisData(r.tx, req, 1, true)
				if err != nil {
					return res, err
				}
				// 统计数据
				var totalRoll int64
				var totalAmount, totalWeight float64
				for _, d := range details {
					totalRoll += d.TotalRoll
					totalAmount += d.SaleAmount
					totalWeight += d.TotalSettleWeight
				}

				// 根据年份存储数据
				switch year {
				case 0: // 本期
					if isFutureDay {
						// 未来日期暂时存0，等收集完去年和前年的数据后再计算预估值
						res.ThreeYearRoll.ThisYear[dayIndex] = 0
						thisYearSaleAmount[dayIndex] = 0
						thisYearSettleWeight[dayIndex] = 0
					} else if isCurrentDay {
						// 如果是当前未结束的月份，获取上一年和前年同期的数据用于计算预估值
						req.TrendStartTime = tools.QueryTime(targetDayStart.AddDate(-1, 0, 0).Format("2006-01-02"))
						req.TrendEndTime = tools.QueryTime(targetDayEnd.AddDate(-1, 0, 0).Format("2006-01-02"))
						lastDayDetails, err := mysql.GetProductAnalysisData(r.tx, req, 1, true)
						if err != nil {
							return res, err
						}
						req.TrendStartTime = tools.QueryTime(targetDayStart.AddDate(-2, 0, 0).Format("2006-01-02"))
						req.TrendEndTime = tools.QueryTime(targetDayEnd.AddDate(-2, 0, 0).Format("2006-01-02"))
						beforeDayDetails, err := mysql.GetProductAnalysisData(r.tx, req, 1, true)
						if err != nil {
							return res, err
						}

						// 计算上一年同期数据
						var lastDayRoll int64
						var lastDayAmount, lastDayWeight float64
						for _, d := range lastDayDetails {
							lastDayRoll += d.TotalRoll
							lastDayAmount += d.SaleAmount
							lastDayWeight += d.TotalSettleWeight
						}

						// 计算前年同期数据
						var beforeDayRoll int64
						var beforeDayAmount, beforeDayWeight float64
						for _, d := range beforeDayDetails {
							beforeDayRoll += d.TotalRoll
							beforeDayAmount += d.SaleAmount
							beforeDayWeight += d.TotalSettleWeight
						}

						// 计算预估值
						estimateRoll := float64(lastDayRoll)*0.7 + float64(beforeDayRoll)*0.3
						estimateSaleAmount := lastDayAmount*0.7 + beforeDayAmount*0.3
						estimateSettleWeight := lastDayWeight*0.7 + beforeDayWeight*0.3

						res.ThreeYearRoll.ThisDayEstimateRoll = math.Floor(estimateRoll / float64(vars.Roll))
						res.ThreeYearSaleAmount.ThisDayEstimateSaleAmount = estimateSaleAmount / float64(vars.PriceMult)
						res.ThreeYearSettleWeight.ThisDayEstimateSettleWeight = estimateSettleWeight / float64(vars.Weight)

						// 将当前实际值保存
						res.ThreeYearRoll.ThisYear[dayIndex] = totalRoll / vars.Roll
						thisYearSaleAmount[dayIndex] = totalAmount / vars.PriceMult
						thisYearSettleWeight[dayIndex] = totalWeight / vars.Weight

					} else {
						res.ThreeYearRoll.ThisYear[dayIndex] = totalRoll / vars.Roll
						thisYearSaleAmount[dayIndex] = totalAmount / vars.PriceMult
						thisYearSettleWeight[dayIndex] = totalWeight / vars.Weight
					}
				case 1: // 去年同期
					res.ThreeYearRoll.LastYear[dayIndex] = totalRoll / vars.Roll
					lastYearSaleAmount[dayIndex] = totalAmount / vars.PriceMult
					lastYearSettleWeight[dayIndex] = totalWeight / vars.Weight
				case 2: // 前年同期
					res.ThreeYearRoll.BeforeYear[dayIndex] = totalRoll / vars.Roll
					beforeYearSaleAmount[dayIndex] = totalAmount / vars.PriceMult
					beforeYearSettleWeight[dayIndex] = totalWeight / vars.Weight
				}
			}

			// 如果是未来日期，计算预估值
			if isFutureDay {
				res.ThreeYearRoll.ThisYear[dayIndex] = 0
				thisYearSaleAmount[dayIndex] = 0
				thisYearSettleWeight[dayIndex] = 0
			}
		}

		// 设置销售金额和结算重量数据
		res.ThreeYearSaleAmount.ThisYear = thisYearSaleAmount
		res.ThreeYearSaleAmount.LastYear = lastYearSaleAmount
		res.ThreeYearSaleAmount.BeforeYear = beforeYearSaleAmount

		res.ThreeYearSettleWeight.ThisYear = thisYearSettleWeight
		res.ThreeYearSettleWeight.LastYear = lastYearSettleWeight
		res.ThreeYearSettleWeight.BeforeYear = beforeYearSettleWeight

	} else { // 月分析
		req.TrendStartTime = req.StartTime
		req.TrendEndTime = req.EndTime
		req.TrendEndTime = req.TrendEndTime.ToTimeEndOfMonth()
		if req.TrendStartTime.IsYMDZero() || req.TrendEndTime.IsYMDZero() {
			// 没有筛选时间,返回当年所有月份数据
			monthCount = 12
			startTime = time.Date(currentTime.Year(), 1, 1, 0, 0, 0, 0, time.Local)
			endTime = time.Date(currentTime.Year(), 12, 31, 23, 59, 59, 999, time.Local)
		} else {
			// 有筛选时间,计算需要返回的月份数
			startTime = req.TrendStartTime.ToTimeYMD()
			endTime = req.TrendEndTime.ToTimeEndOfDay()
			// 计算月份差
			monthCount = (endTime.Year()-startTime.Year())*12 + int(endTime.Month()-startTime.Month()) + 1

			// 如果筛选时间不到六个月，则以结束日期为基准往前取六个月的数据
			if monthCount < 6 {
				// 调整起始时间为结束日期往前推6个月
				startTime = time.Date(endTime.Year(), endTime.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, -5, 0)
				// 重新计算月份数
				monthCount = 6
			}
		}

		// 初始化三年数据的切片,长度为实际需要的月份数
		res.ThreeYearRoll.ThisYear = make([]int64, monthCount)
		res.ThreeYearRoll.LastYear = make([]int64, monthCount)
		res.ThreeYearRoll.BeforeYear = make([]int64, monthCount)

		thisYearSaleAmount := make([]float64, monthCount)
		lastYearSaleAmount := make([]float64, monthCount)
		beforeYearSaleAmount := make([]float64, monthCount)

		thisYearSettleWeight := make([]float64, monthCount)
		lastYearSettleWeight := make([]float64, monthCount)
		beforeYearSettleWeight := make([]float64, monthCount)

		// 遍历月份
		for monthIndex := 0; monthIndex < monthCount; monthIndex++ {
			// 计算当月的起止时间
			currentMonthStart := time.Date(startTime.Year(), startTime.Month(), 1, 0, 0, 0, 0, time.Local).AddDate(0, monthIndex, 0)
			currentMonthEnd := currentMonthStart.AddDate(0, 1, 0).Add(-time.Second)
			if currentMonthEnd.After(endTime) {
				currentMonthEnd = endTime
			}

			// 判断是否是未来月份或当前月份
			isFutureMonth := currentMonthStart.After(currentTime)
			isCurrentMonth := currentMonthStart.Year() == currentTime.Year() && currentMonthStart.Month() == currentTime.Month()

			// 遍历三年
			for year := 0; year < 3; year++ {
				// 计算目标年份的同期时间
				targetMonthStart := currentMonthStart.AddDate(-year, 0, 0)
				targetMonthEnd := currentMonthEnd.AddDate(-year, 0, 0)
				req.TrendStartTime = tools.QueryTime(targetMonthStart.Format("2006-01-02"))
				req.TrendEndTime = tools.QueryTime(targetMonthEnd.Format("2006-01-02"))
				// 查询数据
				details, err := mysql.GetProductAnalysisData(r.tx, req, 1, true)
				if err != nil {
					return res, err
				}

				// 统计数据
				var totalRoll int64
				var totalAmount, totalWeight float64
				for _, d := range details {
					totalRoll += d.TotalRoll
					totalAmount += d.SaleAmount
					totalWeight += d.TotalSettleWeight
				}

				// 根据年份存储数据
				switch year {
				case 0: // 本期
					if isFutureMonth {
						// 未来月份暂时存0，等收集完去年和前年的数据后再计算预估值
						res.ThreeYearRoll.ThisYear[monthIndex] = 0
						thisYearSaleAmount[monthIndex] = 0
						thisYearSettleWeight[monthIndex] = 0
					} else if isCurrentMonth {
						isUpdateEstimateRoll = true
						lastYearRoll, beforeYearRoll, _, _, _, _ := GetEstimateData(r.tx, targetMonthStart, targetMonthEnd, req)
						// 计算预估值
						estimateRoll := float64(lastYearRoll)*0.7 + float64(beforeYearRoll)*0.3
						//estimateSaleAmount := lastYearAmount*0.7 + beforeYearAmount*0.3
						//estimateSettleWeight := lastYearWeight*0.7 + beforeYearWeight*0.3

						res.ThreeYearRoll.ThisMonthEstimateRoll = int64(math.Floor(estimateRoll / float64(vars.Roll)))
						//res.ThreeYearSaleAmount.ThisMonthEstimateSaleAmount = estimateSaleAmount / float64(vars.PriceMult)
						//res.ThreeYearSettleWeight.ThisMonthEstimateSettleWeight = estimateSettleWeight / float64(vars.Weight)

						// 设置当月实际值
						res.ThreeYearRoll.ThisYear[monthIndex] = totalRoll / vars.Roll
						thisYearSaleAmount[monthIndex] = totalAmount / vars.PriceMult
						thisYearSettleWeight[monthIndex] = totalWeight / vars.Weight
					} else {
						res.ThreeYearRoll.ThisYear[monthIndex] = totalRoll / vars.Roll
						thisYearSaleAmount[monthIndex] = totalAmount / vars.PriceMult
						thisYearSettleWeight[monthIndex] = totalWeight / vars.Weight
					}
				case 1: // 去年同期
					res.ThreeYearRoll.LastYear[monthIndex] = totalRoll / vars.Roll
					lastYearSaleAmount[monthIndex] = totalAmount / vars.PriceMult
					lastYearSettleWeight[monthIndex] = totalWeight / vars.Weight
				case 2: // 前年同期
					res.ThreeYearRoll.BeforeYear[monthIndex] = totalRoll / vars.Roll
					beforeYearSaleAmount[monthIndex] = totalAmount / vars.PriceMult
					beforeYearSettleWeight[monthIndex] = totalWeight / vars.Weight
				}
			}

			// 如果是未来月份，直接置0
			if isFutureMonth {
				res.ThreeYearRoll.ThisYear[monthIndex] = 0
				thisYearSaleAmount[monthIndex] = 0
				thisYearSettleWeight[monthIndex] = 0
			}
		}

		// 设置销售金额和结算重量数据
		res.ThreeYearSaleAmount.ThisYear = thisYearSaleAmount
		res.ThreeYearSaleAmount.LastYear = lastYearSaleAmount
		res.ThreeYearSaleAmount.BeforeYear = beforeYearSaleAmount

		res.ThreeYearSettleWeight.ThisYear = thisYearSettleWeight
		res.ThreeYearSettleWeight.LastYear = lastYearSettleWeight
		res.ThreeYearSettleWeight.BeforeYear = beforeYearSettleWeight

		// 计算下个月的预测数据（使用指数平滑法）
		if len(res.ThreeYearRoll.ThisYear) >= 2 {
			// 预测匹数
			predictRoll := ExponentialSmoothingPredict(res.ThreeYearRoll.ThisYear, 0.3)
			predictRollInt := int(math.Floor(predictRoll))
			rangeValue := int(math.Ceil(float64(predictRollInt) * 0.05))
			res.MayPredictData.PredictData = float64(predictRollInt)
			res.MayPredictData.Range = float64(rangeValue)
			if isUpdateEstimateRoll {
				res.ThreeYearRoll.ThisMonthEstimateRoll = int64(predictRoll)
			}
		}

	}

	return
}

// GetProAnalysisProductData 获取产品维度分析数据
func (r *DataAnalysisRepo) GetProAnalysisProductData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetProAnalysisProductRes, err error) {
	var (
		details                      []data_analysis.ProductAnalysisData
		detailsForChart              []data_analysis.ProductAnalysisData
		productList                  product.ProductResList
		unitNames                    map[uint64]string
		customerIds                  = set2.NewUint64Set()
		settleWeights                = make(map[uint64]float64)
		totalSaleAmount              float64
		totalSaleAmountContainReturn float64
		totalRoll                    int64
		// colorNames                   = make(map[uint64]string)
		// colorCodes                   = make(map[uint64]string)
		// 新增：用于统计色号销售额占比
		colorSaleAmounts = make(map[uint64]float64)
		colorRatios      []data_analysis.ColorRatio
		// 用于图表统计的map（不含退货）
		chartNewCustomerSales = make(map[uint64]float64)
		chartOldCustomerSales = make(map[uint64]float64)
		chartTotalSaleAmount  float64
		saleGroupRatios       []data_analysis.SaleGroupRatio
		newCustomerAmount     string
		oldCustomerAmount     string
		// 新增：用于销售地区统计
		repo               = biz_unit.NewRepo(r.tx)
		citySales          = make(map[string]float64)
		customerCityMap    = make(map[uint64]string)
		unitIds            = set2.NewUint64Set()
		historyCustomerIds = make(map[uint64]struct{}) // 统计历史客户ID
	)
	// 获取订单明细数据（包含退货的）
	details, err = mysql.GetProductAnalysisData(r.tx, req, 1, false)
	if err != nil {
		return
	}

	// 获取订单明细数据（不包含退货的）
	detailsForChart, err = mysql.GetProductAnalysisData(r.tx, req, 2, false)
	if err != nil {
		return
	}
	productIds := mysql_base.GetUInt64List(details, "product_id")
	customerIdList := mysql_base.GetUInt64List(detailsForChart, "customer_id")
	// 获取客户物流地址信息
	bizUnits, _ := repo.QueryMultiBizUnit(ctx, customerIdList)
	// 获取产品信息
	productList, err = product.NewProductClient().GetEnableProduct(ctx, product.ProductReq{Ids: productIds})
	// 获取历史订单数据(查询开始时间之前的订单)
	req.CustomerRatioDate = tools.QueryTime(time.Now().AddDate(0, 0, -31).Format("2006-01-02"))
	historyDetails, err := mysql.GetHistoryProductAnalysisData(r.tx)
	if err != nil {
		return
	}
	customerRatioTime, err := time.Parse("2006-01-02", string(req.CustomerRatioDate))
	if err != nil {
		return res, err
	}
	for _, d := range historyDetails {
		// 如果首次下单时间在30天前，则为老客户
		if d.FirstOrderTime.Before(customerRatioTime) {
			historyCustomerIds[d.CustomerId] = struct{}{}
		}
	}
	saleList, err := mysql.FindBizUnitSaleByUnitIds(r.tx, customerIdList)
	if err != nil {
		return
	}
	customerSaleGroupMap := make(map[uint64]uint64)
	for _, sale := range saleList {
		customerSaleGroupMap[sale.UnitID] = sale.SaleGroupID
	}

	// 统计图表相关数据（不含退货）
	chartSaleGroupSales := make(map[uint64]float64) // saleGroupId -> amount
	for _, d := range detailsForChart {
		chartTotalSaleAmount += d.SaleAmount
		totalSaleAmount += d.SaleAmount
		// 统计新老客户销售额
		if _, ok := historyCustomerIds[d.CustomerId]; ok {
			chartOldCustomerSales[d.CustomerId] += d.SaleAmount
		} else {
			chartNewCustomerSales[d.CustomerId] += d.SaleAmount
		}
		// 新增：统计色号销售额
		colorSaleAmounts[d.ProductColorId] += d.SaleAmount
		// 新增：统计销售群体销售额
		if saleGroupId := customerSaleGroupMap[d.CustomerId]; saleGroupId > 0 {
			chartSaleGroupSales[saleGroupId] += d.SaleAmount
		}
	}

	// 计算销售群体占比
	var saleGroupList []struct {
		id     uint64
		amount float64
	}
	for groupId, amount := range chartSaleGroupSales {
		saleGroupList = append(saleGroupList, struct {
			id     uint64
			amount float64
		}{groupId, amount})
	}

	// 按销售金额排序并只保留前10个销售群体
	sort.Slice(saleGroupList, func(i, j int) bool {
		return saleGroupList[i].amount > saleGroupList[j].amount
	})
	if len(saleGroupList) > 10 {
		saleGroupList = saleGroupList[:10]
	}

	// 获取销售群体名称并计算占比
	saleGroupIds := make([]uint64, 0, len(saleGroupList))
	for _, sg := range saleGroupList {
		saleGroupIds = append(saleGroupIds, sg.id)
	}
	saleGroupNames, err := mysql.QuerySaleGroupNameByIds(r.tx, saleGroupIds)
	if err != nil {
		return
	}

	var totalRatio float64
	for _, sg := range saleGroupList {
		ratio := "0%"
		var ratioValue float64
		if chartTotalSaleAmount > 0 {
			ratioValue = sg.amount / chartTotalSaleAmount * 100
			ratio = fmt.Sprintf("%.2f%%", ratioValue)
			totalRatio += ratioValue
		}
		saleGroupRatios = append(saleGroupRatios, data_analysis.SaleGroupRatio{
			SaleGroupId:   sg.id,
			SaleGroupName: saleGroupNames[sg.id],
			SaleRatio:     ratio,
		})
	}

	// 如果总占比不足100%,添加其他类别
	if totalRatio < 100 && chartTotalSaleAmount > 0 {
		otherRatio := fmt.Sprintf("%.2f%%", 100-totalRatio)
		saleGroupRatios = append(saleGroupRatios, data_analysis.SaleGroupRatio{
			SaleGroupId:   0,
			SaleGroupName: "其他",
			SaleRatio:     otherRatio,
		})
	}
	newCustomerAmount, oldCustomerAmount = getNewOldCustomerRatio(chartTotalSaleAmount, chartNewCustomerSales, chartOldCustomerSales)

	// 统计数据
	for _, d := range details {
		totalSaleAmountContainReturn += d.SaleAmount
		totalRoll += d.TotalRoll
		settleWeights[d.MeasurementUnitId] += d.TotalSettleWeight
		customerIds.Add(d.CustomerId)
		unitIds.Add(d.MeasurementUnitId)
	}
	// 获取计量单位名称
	unitNames, err = base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}
	// 获取颜色信息
	colorIds := make([]uint64, 0, len(colorSaleAmounts)) // 修改这里使用colorSaleAmounts
	for colorId := range colorSaleAmounts {              // 修改这里使用colorSaleAmounts
		colorIds = append(colorIds, colorId)
	}
	colorList, err := product.NewProductColorClient().GetProductColorItemByIds(ctx, colorIds)
	if err != nil {
		return
	}
	// for _, color := range colorList {
	//	colorNames[color.Id] = color.ProductColorName
	//	colorCodes[color.Id] = color.ProductColorCode
	// }
	var colorSaleList []data_analysis.ColorSaleInfo
	for colorId, amount := range colorSaleAmounts {
		colorSaleList = append(colorSaleList, data_analysis.ColorSaleInfo{ColorId: colorId, Amount: amount})
	}
	// 按销售额排序
	sort.Slice(colorSaleList, func(i, j int) bool {
		return colorSaleList[i].Amount > colorSaleList[j].Amount
	})

	// 计算前九个色号的占比和其他色号的总额
	var otherAmount float64
	for i, cs := range colorSaleList {
		ratio := "0%"
		if totalSaleAmount > 0 {
			ratio = fmt.Sprintf("%.2f%%", cs.Amount/totalSaleAmount*100)
		}

		if i < 9 {
			colorRatios = append(colorRatios, data_analysis.ColorRatio{
				ColorId:   cs.ColorId,
				ColorCode: colorList[cs.ColorId][0],
				ColorName: colorList[cs.ColorId][1],
				SaleRatio: ratio,
			})
		} else {
			otherAmount += cs.Amount
		}
	}

	// 添加"其他"类别
	if totalSaleAmount > 0 && otherAmount > 0 {
		colorRatios = append(colorRatios, data_analysis.ColorRatio{
			ColorId:   0,
			ColorCode: "其他",
			ColorName: "其他",
			SaleRatio: fmt.Sprintf("%.2f%%", otherAmount/totalSaleAmount*100),
		})
	}
	// 从物流地址中提取城市信息
	customerCityMap = extractCustomerCityMap(bizUnits)
	// 统计每个城市的销售额
	for _, d := range detailsForChart {
		if city, ok := customerCityMap[d.CustomerId]; ok {
			citySales[city] += d.SaleAmount
		}
	}
	var cityOtherAmount float64
	salesAreaStat := make([]data_analysis.SalesAreaStat, 0)
	getSalesAreaStat(citySales, &salesAreaStat, &cityOtherAmount)

	// 构建返回结果
	res = data_analysis.GetProAnalysisProductRes{
		ProductId:           req.ProductId,
		ProductCode:         productList[0].FinishProductCode,
		ProductName:         productList[0].FinishProductName,
		TotalSaleAmount:     totalSaleAmountContainReturn / vars.PriceMult,
		TotalRoll:           totalRoll / vars.Roll,
		TotalSettleWeight:   formatWeightStrs(settleWeights, unitIds.List(), unitNames),
		Top10SaleGroupRatio: saleGroupRatios,
		TotalCustomerCount:  len(customerIds.List()),
		NewCustomerRatio:    newCustomerAmount,
		OldCustomerRatio:    oldCustomerAmount,
		ColorRatio:          colorRatios,
		SalesAreaStat:       salesAreaStat,
	}

	return
}

// GetProAnalysisCustomerData 获取产品客户维度分析数据
func (r *DataAnalysisRepo) GetProAnalysisCustomerData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetProAnalysisCustomerRes, err error) {
	var (
		details         []data_analysis.ProductAnalysisData
		detailsForChart []data_analysis.ProductAnalysisData
		productList     product.ProductResList
		colorList       product.ProductColorResList
		unitNames       map[uint64]string
		customerIds     = set2.NewUint64Set()
		settleWeights   = make(map[uint64]float64)
		totalSaleAmount float64
		totalRoll       int64
		// 新增：用于销售地区统计
		repo            = biz_unit.NewRepo(r.tx)
		citySales       = make(map[string]float64)
		customerCityMap = make(map[uint64]string)
		unitIds         = set2.NewUint64Set()
	)

	// 获取订单明细数据
	details, err = mysql.GetProductAnalysisData(r.tx, req, 1, false)
	if err != nil {
		return
	}
	// 获取订单明细数据
	detailsForChart, err = mysql.GetProductAnalysisData(r.tx, req, 2, false)
	if err != nil {
		return
	}

	// 统计数据
	for _, d := range details {
		totalSaleAmount += d.SaleAmount
		totalRoll += d.TotalRoll
		settleWeights[d.MeasurementUnitId] += d.TotalSettleWeight
		customerIds.Add(d.CustomerId)
	}

	// 获取客户名称
	for unitId := range settleWeights {
		unitIds.Add(unitId)
	}
	// 获取计量单位名称
	unitNames, _ = base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	productIds := mysql_base.GetUInt64List(details, "product_id")
	colorIds := mysql_base.GetUInt64List(details, "product_color_id")
	productList, _ = product.NewProductClient().GetEnableProductForAnalysisData(ctx, product.ProductReq{Ids: productIds})
	colorList, _ = product.NewProductColorClient().GetProductColorByIds(ctx, colorIds)
	bizUnits, _ := repo.QueryMultiBizUnit(ctx, customerIds.List())
	// 从物流地址中提取城市信息
	customerCityMap = extractCustomerCityMap(bizUnits)
	// 统计每个城市的销售额
	for _, d := range detailsForChart {
		// if d.ProductId == req.ProductId || d.ProductColorId == req.ProductColorId {
		if city, ok := customerCityMap[d.CustomerId]; ok {
			citySales[city] += d.SaleAmount
		}
		// }
	}
	salesAreaStat := make([]data_analysis.SalesAreaStat, 0)
	var cityOtherAmount float64
	getSalesAreaStat(citySales, &salesAreaStat, &cityOtherAmount)

	// 构建返回结果
	res = data_analysis.GetProAnalysisCustomerRes{
		ProductId:          req.ProductId,
		ProductCode:        productList[0].FinishProductCode,
		ProductName:        productList[0].FinishProductName,
		ProductColorId:     req.ProductColorId.ToUint64()[0],
		ProductColorCode:   colorList[0].ProductColorCode,
		ProductColorName:   colorList[0].ProductColorName,
		TotalSaleAmount:    totalSaleAmount / vars.PriceMult,
		TotalRoll:          totalRoll / vars.Roll,
		TotalSettleWeight:  formatWeightStrs(settleWeights, unitIds.List(), unitNames),
		TotalCustomerCount: len(customerIds.List()),
		SalesAreaStat:      salesAreaStat, // 新增：添加销售地区统计数据
	}

	return
}

// GetCusAnalysisCustomerData 获取客户维度分析数据
func (r *DataAnalysisRepo) GetCusAnalysisCustomerData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetCusAnalysisCustomerRes, err error) {
	var (
		details            []data_analysis.ProductAnalysisData
		detailsForChart    []data_analysis.ProductAnalysisData
		productMapIds      = make(map[uint64]*product.ProductRes)
		unitNames          = make(map[uint64]string)
		settleWeights      = make(map[uint64]float64)
		totalSaleAmount    float64
		totalRoll          int64
		fabricTypeSales    = make(map[uint64]*FabricTypeInfo)
		fabricTypeSaleList []*FabricTypeInfo
		unitIds            = set2.NewUint64Set()
	)

	// 获取订单明细数据（包含退货的）
	details, err = mysql.GetProductAnalysisData(r.tx, req, 1, false)
	if err != nil {
		return
	}

	// 获取订单明细数据（不包含退货的）
	detailsForChart, err = mysql.GetProductAnalysisData(r.tx, req, 2, false)
	if err != nil {
		return
	}

	// 获取所有产品ID并获取产品信息
	productIds := mysql_base.GetUInt64List(detailsForChart, "product_id")

	productMapIds, err = product.NewProductClient().GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}

	// 统计布料类型销售额
	for _, d := range detailsForChart {
		for _, p := range productMapIds {
			if p.Id == d.ProductId {
				if _, ok := fabricTypeSales[p.TypeGreyFabricId]; !ok {
					fabricTypeSales[p.TypeGreyFabricId] = &FabricTypeInfo{
						TypeId:   p.TypeGreyFabricId,
						TypeName: p.TypeGreyFabricName,
					}
					if fabricTypeSales[p.TypeGreyFabricId].TypeName == "" {
						fabricTypeSales[p.TypeGreyFabricId].TypeName = "未知类型"
					}
				}
				fabricTypeSales[p.TypeGreyFabricId].Amount += d.SaleAmount
				break
			}
		}
	}

	// 将布料类型销售额转换为切片并排序
	for _, info := range fabricTypeSales {
		fabricTypeSaleList = append(fabricTypeSaleList, info)
	}
	sort.Slice(fabricTypeSaleList, func(i, j int) bool {
		return fabricTypeSaleList[i].Amount > fabricTypeSaleList[j].Amount
	})

	// 准备Top10布料类型占比数据
	var top10ProductCategoryRatio []data_analysis.ProductCategoryRatio
	var otherAmount float64
	var chartTotalSaleAmount float64
	for _, d := range detailsForChart {
		chartTotalSaleAmount += d.SaleAmount
	}

	for i, fts := range fabricTypeSaleList {
		ratio := "0%"
		if chartTotalSaleAmount > 0 {
			ratio = fmt.Sprintf("%.2f%%", fts.Amount/chartTotalSaleAmount*100)
		}

		if i < 9 {
			top10ProductCategoryRatio = append(top10ProductCategoryRatio, data_analysis.ProductCategoryRatio{
				TypeId:    fts.TypeId,
				TypeName:  fts.TypeName,
				SaleRatio: ratio,
			})
		} else {
			otherAmount += fts.Amount
		}
	}
	// 合并相同TypeId的SaleRatio
	mergeSameTypeIdSaleRatio(&top10ProductCategoryRatio)
	// 添加"其他"类型
	if chartTotalSaleAmount > 0 && otherAmount > 0 {
		top10ProductCategoryRatio = append(top10ProductCategoryRatio, data_analysis.ProductCategoryRatio{
			TypeId:    0,
			TypeName:  "其他",
			SaleRatio: fmt.Sprintf("%.2f%%", otherAmount/chartTotalSaleAmount*100),
		})
	}

	// 统计数据
	for _, d := range details {
		totalSaleAmount += d.SaleAmount
		totalRoll += d.TotalRoll
		settleWeights[d.MeasurementUnitId] += d.TotalSettleWeight
	}
	// 获取计量单位名称
	for unitId := range settleWeights {
		unitIds.Add(unitId)
	}
	unitNames, err = base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}

	// 获取客户名称
	customerNames, err := mysql.QueryCustomerNameByIds(r.tx, req.CustomerId.ToUint64())
	if err != nil {
		return
	}

	// 构建返回结果
	res = data_analysis.GetCusAnalysisCustomerRes{
		CustomerId:                req.CustomerId.ToUint64()[0],
		CustomerName:              customerNames[req.CustomerId.ToUint64()[0]],
		TotalSaleAmount:           totalSaleAmount / vars.PriceMult,
		TotalRoll:                 totalRoll / vars.Roll,
		TotalSettleWeight:         formatWeightStrs(settleWeights, unitIds.List(), unitNames),
		Top10ProductCategoryRatio: top10ProductCategoryRatio,
	}

	return
}

// GetCusAnalysisProductData 获取客户产品维度分析数据
func (r *DataAnalysisRepo) GetCusAnalysisProductData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetCusAnalysisProductRes, err error) {
	var (
		details         []data_analysis.ProductAnalysisData
		productList     product.ProductResList
		unitNames       map[uint64]string
		settleWeights   = make(map[uint64]float64)
		totalSaleAmount float64
		totalRoll       int64
		unitIds         = set2.NewUint64Set()
		productIds      []uint64
	)

	// 获取订单明细数据
	details, err = mysql.GetProductAnalysisData(r.tx, req, 1, false)
	if err != nil {
		return
	}
	productIds = mysql_base.GetUInt64List(details, "product_id")
	// 获取产品信息
	productList, err = product.NewProductClient().GetEnableProduct(ctx, product.ProductReq{Ids: productIds})
	if err != nil {
		return
	}

	// 初始化颜色统计map
	for _, d := range details {
		totalSaleAmount += d.SaleAmount
		totalRoll += d.TotalRoll
		settleWeights[d.MeasurementUnitId] += d.TotalSettleWeight
	}
	// 获取计量单位名称

	for unitId := range settleWeights {
		unitIds.Add(unitId)
	}
	unitNames, err = base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}

	// 获取客户名称
	customerNames, err := mysql.QueryCustomerNameByIds(r.tx, req.CustomerId.ToUint64())
	if err != nil {
		return
	}

	// 构建返回结果
	res = data_analysis.GetCusAnalysisProductRes{
		CustomerId:        req.CustomerId.ToUint64()[0],
		CustomerName:      customerNames[req.CustomerId.ToUint64()[0]],
		ProductId:         req.ProductId,
		ProductCode:       productList[0].FinishProductCode,
		ProductName:       productList[0].FinishProductName,
		TotalSaleAmount:   totalSaleAmount / vars.PriceMult,
		TotalRoll:         totalRoll / vars.Roll,
		TotalSettleWeight: formatWeightStrs(settleWeights, unitIds.List(), unitNames),
	}

	return
}

// GetTopProductData 获取前十产品数据
func (r *DataAnalysisRepo) GetTopProductData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetTopDataRes, err error) {
	var (
		TotalRollNotContainReturn int64
		data                      []data_analysis.TopProductDetail
		dataFor15Day              []data_analysis.TopProductDetail
		tempData                  []data_analysis.GetTopProductResForTop
		details                   []data_analysis.ProductAnalysisData
		unitIds                   = set2.NewUint64Set()
		dataFor15DayMap           = make(map[uint64]data_analysis.TopProductDetail)
		total                     int
	)
	ReqFor15DayStartTime := tools.QueryTime(time.Now().AddDate(0, 0, -14).Format("2006-01-02") + " 00:00:00")
	// 以req.EndTime为基准，往前推14天
	ReqFor15Day := data_analysis.GetAnalysisHomeReq{
		StartTime:             ReqFor15DayStartTime,
		EndTime:               tools.QueryTime(time.Now().Format("2006-01-02") + " 23:59:59"),
		SaleSystemId:          req.SaleSystemId,
		SaleUserId:            req.SaleUserId,
		CustomerId:            req.CustomerId,
		ProductId:             req.ProductId,
		TypeFabricId:          req.TypeFabricId,
		SaleGroupId:           req.SaleGroupId,
		IfShowOldCustomerData: req.IfShowOldCustomerData,
		IfShowNewCustomerData: req.IfShowNewCustomerData,
		Location:              req.Location,
		SaleModeIden:          req.SaleModeIden,
	}
	// 获取订单明细数据（包含退货的）
	data, err, total = mysql.GetTopProductAnalysisData(r.tx, req)
	details, err = mysql.GetProductAnalysisData(r.tx, &data_analysis.GetAnalysisHomeReq{
		StartTime:             req.StartTime,
		EndTime:               req.EndTime,
		CustomerId:            req.CustomerId,
		SaleSystemId:          req.SaleSystemId,
		SaleUserId:            req.SaleUserId,
		Location:              req.Location,
		SaleGroupId:           req.SaleGroupId,
		TypeFabricId:          req.TypeFabricId,
		IfShowNewCustomerData: req.IfShowNewCustomerData,
		IfShowOldCustomerData: req.IfShowOldCustomerData,
	}, 2, false)
	if err != nil {
		return
	}
	dataFor15Day, err, _ = mysql.GetTopProductAnalysisData(r.tx, &ReqFor15Day)
	if err != nil {
		return
	}
	// 把datafor15day的改成一个map键为productid，值为datafor15day
	for _, d := range dataFor15Day {
		dataFor15DayMap[d.ProductId] = d
	}
	// 获取产品信息
	productIds := mysql_base.GetUInt64List(data, "product_id")
	productList, _ := product.NewProductClient().GetEnableProductForAnalysisData(ctx, product.ProductReq{Ids: productIds})
	// 创建产品ID到产品信息的映射
	productMap := make(map[uint64]product.ProductRes)
	for _, p := range productList {
		productMap[p.Id] = p
	}

	for _, d := range data {
		unitIds.Add(d.MeasurementUnitId)
	}
	for _, d := range details {
		TotalRollNotContainReturn += d.TotalRoll
	}
	// 获取计量单位名称
	unitNames, err := base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}

	for _, d := range data {
		// 安全地获取产品信息
		p, ok := productMap[d.ProductId]
		if !ok {
			continue
		}
		// 获取该产品的15天销售数据
		last15DaySaleAmount := make([]float64, 15)
		if d15, ok := dataFor15DayMap[d.ProductId]; ok {
			getLast15DaySaleAmountForTopProduct(d15, &last15DaySaleAmount)
			slices.Reverse(last15DaySaleAmount)
		}

		data := data_analysis.GetTopProductResForTop{
			ProductId:           d.ProductId,
			ProductName:         p.FinishProductName,
			ProductCode:         p.FinishProductCode,
			ColorCount:          d.ColorCount,
			ProductColorIds:     d.ProductColorIds,
			CustomerCount:       d.CustomerCount,
			CustomerIds:         d.CustomerIds,
			TotalSaleAmount:     d.SaleAmount / vars.PriceMult,
			SaleRoll:            d.SaleRoll / vars.Roll,
			ReturnRoll:          d.ReturnRoll / vars.Roll,
			TotalRoll:           d.TotalRoll / vars.Roll,
			TotalSettleWeight:   fmt.Sprintf("%.2f%s", d.TotalWeight/vars.Weight, unitNames[d.MeasurementUnitId]),
			Last15DaySaleAmount: last15DaySaleAmount,
		}

		// 安全计算比率
		if TotalRollNotContainReturn > 0 {
			data.RollRatio = fmt.Sprintf("%.2f%%", float64(d.SaleRoll)/float64(TotalRollNotContainReturn)*100)
		} else {
			data.RollRatio = "0%"
		}

		tempData = append(tempData, data)
	}

	res.TopDataList = tempData
	res.Total = total
	return
}

// GetTopColorData 获取前十颜色数据
func (r *DataAnalysisRepo) GetTopColorData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetTopDataRes, err error) {
	var (
		tempData                  []data_analysis.GetTopColorResForTop
		data                      []data_analysis.TopColorDetail
		dataFor15Day              []data_analysis.TopColorDetail
		baseProductPB             = product.NewProductColorClient()
		unitIds                   = set2.NewUint64Set()
		TotalRollNotContainReturn int64
		details                   []data_analysis.ProductAnalysisData
		dataFor15DayMap           = make(map[uint64]data_analysis.TopColorDetail)
		total                     int
	)

	data, err, total = mysql.GetTopColorAnalysisData(r.tx, req)
	if err != nil {
		return
	}
	productIds := mysql_base.GetUInt64List(data, "product_id")
	productInfoMap, _, _ := product.NewProductClient().GetProductByIds(ctx, productIds)
	ReqFor15DayStartTime := tools.QueryTime(time.Now().AddDate(0, 0, -14).Format("2006-01-02") + " 00:00:00")
	dataFor15Day, err, _ = mysql.GetTopColorAnalysisData(r.tx, &data_analysis.GetAnalysisHomeReq{
		StartTime:             ReqFor15DayStartTime,
		EndTime:               tools.QueryTime(time.Now().Format("2006-01-02") + " 23:59:59"),
		SaleSystemId:          req.SaleSystemId,
		SaleUserId:            req.SaleUserId,
		CustomerId:            req.CustomerId,
		ProductId:             req.ProductId,
		SaleGroupId:           req.SaleGroupId,
		ProductColorId:        req.ProductColorId,
		IfShowNewCustomerData: req.IfShowNewCustomerData,
		IfShowOldCustomerData: req.IfShowOldCustomerData,
		Location:              req.Location,
		SaleModeIden:          req.SaleModeIden,
	})
	if err != nil {
		return
	}

	details, err = mysql.GetProductAnalysisData(r.tx, &data_analysis.GetAnalysisHomeReq{StartTime: req.StartTime, EndTime: req.EndTime, ProductId: req.ProductId, SaleSystemId: req.SaleSystemId, SaleUserId: req.SaleUserId, CustomerId: req.CustomerId, Location: req.Location, SaleGroupId: req.SaleGroupId, TypeFabricId: req.TypeFabricId, IfShowNewCustomerData: req.IfShowNewCustomerData, IfShowOldCustomerData: req.IfShowOldCustomerData}, 2, false)
	if err != nil {
		return
	}

	// 把datafor15day的改成一个map键为colorid，值为datafor15day
	for _, d := range dataFor15Day {
		dataFor15DayMap[d.ProductColorId] = d
	}

	ColorMap, err := baseProductPB.GetProductColorItemByIds(ctx, mysql_base.GetUInt64List(data, "product_color_id"))
	if err != nil {
		return
	}
	for _, d := range data {
		unitIds.Add(d.MeasurementUnitId)
	}
	for _, d := range details {
		TotalRollNotContainReturn += d.TotalRoll
	}
	// 获取计量单位名称
	unitNames, err := base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}

	for _, d := range data {
		var rollRatio string
		// 获取该颜色的15天销售数据
		last15DaySaleAmount := make([]float64, 15)
		if d15, ok := dataFor15DayMap[d.ProductColorId]; ok {
			getLast15DaySaleAmountForTopColor(d15, &last15DaySaleAmount)
			slices.Reverse(last15DaySaleAmount)
		}
		if TotalRollNotContainReturn == 0 {
			rollRatio = "0%"
		} else {
			rollRatio = fmt.Sprintf("%.2f%%", float64(d.SaleRoll)/float64(TotalRollNotContainReturn)*100)
		}
		tempData = append(tempData, data_analysis.GetTopColorResForTop{
			ProductId:           d.ProductId,
			ProductName:         productInfoMap[d.ProductId][1],
			ProductCode:         productInfoMap[d.ProductId][0],
			ProductColorId:      d.ProductColorId,
			ProductColorName:    ColorMap[d.ProductColorId][1],
			ProductColorCode:    ColorMap[d.ProductColorId][0],
			CustomerCount:       d.CustomerCount,
			CustomerIds:         d.CustomerIds,
			TotalSaleAmount:     d.TotalSaleAmount / vars.PriceMult,
			SaleRoll:            d.SaleRoll / vars.Roll,
			ReturnRoll:          d.ReturnRoll / vars.Roll,
			TotalRoll:           d.TotalRoll / vars.Roll,
			TotalSettleWeight:   fmt.Sprintf("%.2f%s", d.SettleWeight/vars.Weight, unitNames[d.MeasurementUnitId]),
			Last15DaySaleAmount: last15DaySaleAmount,
			RollRatio:           rollRatio,
		})
	}
	res.TopDataList = tempData
	res.Total = total
	return
}

// GetTopCustomerData 获取前十客户数据
func (r *DataAnalysisRepo) GetTopCustomerData(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetTopDataRes, err error) {
	var (
		tempData                  []data_analysis.GetTopCustomerResForTop
		data                      []data_analysis.TopCustomerDeatil
		dataFor15Day              []data_analysis.TopCustomerDeatil
		details                   []data_analysis.ProductAnalysisData
		TotalRollNotContainReturn int64
		unitIds                   = set2.NewUint64Set()
		dataFor15DayMap           = make(map[uint64]data_analysis.TopCustomerDeatil)
		total                     int
	)
	data, err, total = mysql.GetTopCustomerAnalysisData(r.tx, req)
	if err != nil {
		return
	}
	ReqFor15DayStartTime := tools.QueryTime(time.Now().AddDate(0, 0, -14).Format("2006-01-02") + " 00:00:00")
	dataFor15Day, err, _ = mysql.GetTopCustomerAnalysisData(r.tx, &data_analysis.GetAnalysisHomeReq{
		StartTime:    ReqFor15DayStartTime,
		EndTime:      tools.QueryTime(time.Now().Format("2006-01-02") + " 23:59:59"),
		SaleSystemId: req.SaleSystemId,
		SaleUserId:   req.SaleUserId,
		CustomerId:   req.CustomerId,
		ProductId:    req.ProductId,
		Location:     req.Location,
		SaleModeIden: req.SaleModeIden,
	})
	if err != nil {
		return
	}
	details, err = mysql.GetProductAnalysisData(r.tx, &data_analysis.GetAnalysisHomeReq{StartTime: req.StartTime, EndTime: req.EndTime, ProductId: req.ProductId, SaleSystemId: req.SaleSystemId, SaleUserId: req.SaleUserId, ProductColorId: req.ProductColorId, Location: req.Location, SaleGroupId: req.SaleGroupId, TypeFabricId: req.TypeFabricId, IfShowNewCustomerData: req.IfShowNewCustomerData, IfShowOldCustomerData: req.IfShowOldCustomerData}, 2, false)
	if err != nil {
		return
	}
	// 把datafor15day的改成一个map键为customerid，值为datafor15day
	for _, d := range dataFor15Day {
		dataFor15DayMap[d.CustomerId] = d
	}

	customerMap, err := mysql.QueryCustomerNameByIds(r.tx, mysql_base.GetUInt64List(data, "customer_id"))
	if err != nil {
		return
	}
	for _, d := range details {
		TotalRollNotContainReturn += d.TotalRoll
	}

	for _, d := range data {
		unitIds.Add(d.MeasurementUnitId)
	}
	// 获取计量单位名称
	unitNames, err := base_info_pb.NewInfoBaseMeasurementUnitClient().GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}

	for _, d := range data {
		var rollRatio string
		// 获取该客户的15天销售数据
		last15DaySaleAmount := make([]float64, 15)
		if d15, ok := dataFor15DayMap[d.CustomerId]; ok {
			getLast15DaySaleAmountForTopCustomer(d15, &last15DaySaleAmount)
			slices.Reverse(last15DaySaleAmount)
		}
		if TotalRollNotContainReturn == 0 {
			rollRatio = "0%"
		} else {
			rollRatio = fmt.Sprintf("%.2f%%", float64(d.SaleRoll)/float64(TotalRollNotContainReturn)*100)
		}
		tempData = append(tempData, data_analysis.GetTopCustomerResForTop{
			CustomerId:          d.CustomerId,
			CustomerName:        customerMap[d.CustomerId],
			ProductCount:        int(d.ProductCount),
			ProductColorCount:   int(d.ColorCount),
			TotalSaleAmount:     d.TotalSaleAmount / vars.PriceMult,
			SaleRoll:            d.SaleRoll / vars.Roll,
			ReturnRoll:          d.ReturnRoll / vars.Roll,
			TotalRoll:           d.TotalRoll / vars.Roll,
			TotalSettleWeight:   fmt.Sprintf("%.2f%s", d.SettleWeight/vars.Weight, unitNames[d.MeasurementUnitId]),
			RollRatio:           rollRatio,
			Last15DaySaleAmount: last15DaySaleAmount,
		})
	}

	res.TopDataList = tempData
	res.Total = total
	return
}

func getLast15DaySaleAmountForTopCustomer(d data_analysis.TopCustomerDeatil, last15DaySaleAmount *[]float64) {
	(*last15DaySaleAmount)[0] = float64(d.Day0SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[1] = float64(d.Day1SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[2] = float64(d.Day2SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[3] = float64(d.Day3SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[4] = float64(d.Day4SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[5] = float64(d.Day5SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[6] = float64(d.Day6SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[7] = float64(d.Day7SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[8] = float64(d.Day8SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[9] = float64(d.Day9SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[10] = float64(d.Day10SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[11] = float64(d.Day11SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[12] = float64(d.Day12SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[13] = float64(d.Day13SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[14] = float64(d.Day14SaleMount / vars.PriceMult)
}
func getLast15DaySaleAmountForTopProduct(d data_analysis.TopProductDetail, last15DaySaleAmount *[]float64) {
	(*last15DaySaleAmount)[0] = float64(d.Day0SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[1] = float64(d.Day1SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[2] = float64(d.Day2SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[3] = float64(d.Day3SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[4] = float64(d.Day4SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[5] = float64(d.Day5SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[6] = float64(d.Day6SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[7] = float64(d.Day7SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[8] = float64(d.Day8SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[9] = float64(d.Day9SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[10] = float64(d.Day10SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[11] = float64(d.Day11SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[12] = float64(d.Day12SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[13] = float64(d.Day13SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[14] = float64(d.Day14SaleMount / vars.PriceMult)
}
func getLast15DaySaleAmountForTopColor(d data_analysis.TopColorDetail, last15DaySaleAmount *[]float64) {
	(*last15DaySaleAmount)[0] = float64(d.Day0SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[1] = float64(d.Day1SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[2] = float64(d.Day2SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[3] = float64(d.Day3SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[4] = float64(d.Day4SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[5] = float64(d.Day5SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[6] = float64(d.Day6SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[7] = float64(d.Day7SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[8] = float64(d.Day8SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[9] = float64(d.Day9SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[10] = float64(d.Day10SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[11] = float64(d.Day11SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[12] = float64(d.Day12SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[13] = float64(d.Day13SaleMount / vars.PriceMult)
	(*last15DaySaleAmount)[14] = float64(d.Day14SaleMount / vars.PriceMult)
}

// getSalesAreaStat 获取销售地区统计数据
func getSalesAreaStat(citySales map[string]float64, salesAreaStat *[]data_analysis.SalesAreaStat, cityOtherAmount *float64) {
	// 转换为切片并排序
	var cityList []data_analysis.CityData
	for city, amount := range citySales {
		cityList = append(cityList, data_analysis.CityData{
			Name:  city,
			Value: amount,
		})
	}
	// 按销售额排序
	sort.Slice(cityList, func(i, j int) bool {
		return cityList[i].Value > cityList[j].Value
	})

	// 统计前10个城市和其他
	for i, city := range cityList {
		if city.Value < 0 {
			continue
		}
		if i < 10 && city.Name != "其他" {
			*salesAreaStat = append(*salesAreaStat, data_analysis.SalesAreaStat{
				Name:  city.Name,
				Value: city.Value / vars.PriceMult,
			})
		} else {
			*cityOtherAmount += city.Value
		}
	}
	// 添加"其他"类别
	if *cityOtherAmount > 0 {
		*salesAreaStat = append(*salesAreaStat, data_analysis.SalesAreaStat{
			Name:  "其他",
			Value: *cityOtherAmount / vars.PriceMult,
		})
	}
}

func getNewOldCustomerRatio(chartTotalSaleAmount float64, chartNewCustomerSales, chartOldCustomerSales map[uint64]float64) (newCustomerRatio, oldCustomerRatio string) {
	// 计算新老客户占比
	var newCustomerAmount, oldCustomerAmount float64
	for _, amount := range chartNewCustomerSales {
		newCustomerAmount += amount
	}
	for _, amount := range chartOldCustomerSales {
		oldCustomerAmount += amount
	}
	// 计算新老客户占比
	if chartTotalSaleAmount > 0 {
		newCustomerRatio = fmt.Sprintf("%.2f%%", newCustomerAmount/chartTotalSaleAmount*100)
		oldCustomerRatio = fmt.Sprintf("%.2f%%", oldCustomerAmount/chartTotalSaleAmount*100)
	} else {
		newCustomerRatio = "0%"
		oldCustomerRatio = "0%"
	}
	return
}

func mergeSameTypeIdSaleRatio(top10ProductCategoryRatio *[]data_analysis.ProductCategoryRatio) {
	for i := 0; i < len(*top10ProductCategoryRatio); i++ {
		for j := i + 1; j < len(*top10ProductCategoryRatio); j++ {
			if (*top10ProductCategoryRatio)[i].TypeId == (*top10ProductCategoryRatio)[j].TypeId {
				// 去掉%号并转成float
				ratio1Str := strings.TrimRight((*top10ProductCategoryRatio)[i].SaleRatio, "%")
				ratio2Str := strings.TrimRight((*top10ProductCategoryRatio)[j].SaleRatio, "%")
				ratio1, _ := strconv.ParseFloat(ratio1Str, 64)
				ratio2, _ := strconv.ParseFloat(ratio2Str, 64)
				// 合并比率
				(*top10ProductCategoryRatio)[i].SaleRatio = fmt.Sprintf("%.2f%%", ratio1+ratio2)
				// 删除重复的元素
				(*top10ProductCategoryRatio) = append((*top10ProductCategoryRatio)[:j], (*top10ProductCategoryRatio)[j+1:]...)
				j-- // 因为删除了一个元素,需要回退一位
			}
		}
	}
}

func (r *DataAnalysisRepo) GethistoryCustomerIds(ctx context.Context, historyDetails []data_analysis.CustomerFirstOrder, customerRatioTime time.Time) (historyCustomerIds map[uint64]struct{}) {
	historyCustomerIds = make(map[uint64]struct{})
	for _, d := range historyDetails {
		if d.FirstOrderTime.Before(customerRatioTime) {
			historyCustomerIds[d.CustomerId] = struct{}{}
		}
	}
	return
}

// 统一处理产品首页返回返回数据
func (r *DataAnalysisRepo) GetUnifiedHomeAnalysisRes(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetUnifiedAnalysisRes, err error) {
	homeData, err := r.GetAnalysisHomeData(ctx, req)
	if err != nil {
		return
	}
	res.HomeData = homeData
	sourceType := req.Type
	req.Type = 1
	res.ProAnalysisTrendDataDay, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	req.Type = 2
	res.ProAnalysisTrendDataMonth, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	if req.Type == sourceType {
		res.TopCustomerData, err = r.GetTopCustomerData(ctx, req)
	} else {
		res.TopProductData, err = r.GetTopProductData(ctx, req)
	}
	if err != nil {
		return
	}
	return
}

// 统一处理产品首页返回返回数据
func (r *DataAnalysisRepo) GetUnifiedProductDeatilAnalysisRes(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetUnifiedProductDetailAnalysisRes, err error) {
	productData, err := r.GetProAnalysisProductData(ctx, req)
	if err != nil {
		return
	}
	res.ProductAnalysisData = productData
	req.Type = 1
	res.ProAnalysisTrendDataDay, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	req.Type = 2
	res.ProAnalysisTrendDataMonth, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	res.TopColorData, err = r.GetTopColorData(ctx, req)
	if err != nil {
		return
	}
	return
}

// 统一处理产品点击颜色详情返回返回数据
func (r *DataAnalysisRepo) GetUnifiedCustomerDeatilAnalysisRes(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetUnifiedCustomerDetailAnalysisRes, err error) {
	customerData, err := r.GetProAnalysisCustomerData(ctx, req)
	if err != nil {
		return
	}
	res.CustomerAnalysisData = customerData
	req.Type = 1
	res.ProAnalysisTrendDataDay, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	req.Type = 2
	res.ProAnalysisTrendDataMonth, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	res.TopCustomerData, err = r.GetTopCustomerData(ctx, req)
	if err != nil {
		return
	}
	return
}

// 统一处理客户点击客户详情返回返回数据
func (r *DataAnalysisRepo) GetUnifiedCustomerDeatilCustomerAnalysisRes(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetUnifiedCustomerDetailCustomerAnalysisRes, err error) {
	customerData, err := r.GetCusAnalysisCustomerData(ctx, req)
	if err != nil {
		return
	}
	res.CustomerAnalysisData = customerData
	req.Type = 1
	res.ProAnalysisTrendDataDay, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	req.Type = 2
	res.ProAnalysisTrendDataMonth, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	res.TopProductData, err = r.GetTopProductData(ctx, req)
	if err != nil {
		return
	}
	return
}

func (r *DataAnalysisRepo) GetUnifiedProductDeatilCustomerAnalysisRes(ctx context.Context, req *data_analysis.GetAnalysisHomeReq) (res data_analysis.GetUnifiedProductDetailCustomerAnalysisRes, err error) {
	customerData, err := r.GetCusAnalysisProductData(ctx, req)
	if err != nil {
		return
	}
	res.CustomerAnalysisData = customerData
	req.Type = 1
	res.ProAnalysisTrendDataDay, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	req.Type = 2
	res.ProAnalysisTrendDataMonth, err = r.GetProAnalysisTrendData(ctx, req)
	if err != nil {
		return
	}
	res.TopColorData, err = r.GetTopColorData(ctx, req)
	if err != nil {
		return
	}
	return
}

// GetReceivableOrderList 获取指定客户的应收单列表
func (repo *DataAnalysisRepo) GetReceivableOrderList(ctx context.Context, req *data_analysis.GetReceivableOrderListReq) (res data_analysis.GetReceivableOrderListRes, err error) {
	var (
		fpmArrangeOrderSvc = product2.NewArrangeOrderClient()
		fpmArrangeOrders   product2.ArrangeOrderResList
	)
	// 构建查询条件
	query := &structure_should_collect_order.GetShouldCollectOrderDetailListQuery{
		ListQuery:      req.ListQuery,
		CustomerId:     req.CustomerId,
		StartOrderTime: req.StartTime,
		EndOrderTime:   req.EndTime,
	}

	// 查询应收单详情记录
	orderDetails, count, err := mysql.SearchShouldCollectOrderDetail(repo.tx, query)
	if err != nil {
		return
	}

	// 获取所有应收单ID
	orderIds := orderDetails.GetShouldCollectOrderIds()

	// 查询应收单信息
	var orderList model_should_collect_order.ShouldCollectOrderList
	if len(orderIds) > 0 {
		orderList, err = mysql.FindShouldCollectOrderByIDs(repo.tx, orderIds)
		if err != nil {
			return
		}
		fpmArrangeOrderIds := mysql_base.GetUInt64List(orderList, "src_id")
		fpmArrangeOrders, err = fpmArrangeOrderSvc.GetArrangeOrderList(ctx, product2.ArrangeOrderReq{IDs: fpmArrangeOrderIds})
	}

	// 初始化列表
	res.List = make([]data_analysis.ReceivableOrderItem, 0, len(orderDetails))
	for _, detail := range orderDetails {
		// 从应收单中获取对应记录
		order := orderList.Pick(detail.ShouldCollectOrderId)

		// 计算未收金额
		uncollectedAmount := order.TotalSettleMoney - order.CollectedMoney

		// 转换为响应结构
		item := data_analysis.ReceivableOrderItem{
			// 基础信息
			OrderId:   order.Id,
			OrderNo:   order.OrderNo,
			OrderTime: order.OrderTime,

			// 商品信息
			ProductCode:      detail.Code,
			ProductName:      detail.Name,             // 成品名称
			ProductColorCode: detail.ProductColorCode, // 色号
			ProductColorName: detail.ProductColorName, // 颜色

			// 数量信息
			Roll:         detail.Roll,         // 匹数
			SettleWeight: detail.SettleWeight, // 结算数量

			SalePrice: detail.SalePrice,
			// 价格信息
			OffsetSalePrice: detail.StandardSalePrice - detail.SalePrice, // 优惠幅度

			// 汇总金额
			SettleAmount:      order.TotalSettleMoney, // 结算金额
			CollectedAmount:   order.CollectedMoney,   // 已收金额
			UncollectedAmount: uncollectedAmount,      // 未收金额

			// 状态信息
			CollectType:       order.CollectType, // 单据类型
			CollectTypeName:   order.CollectType.String(),
			SaleMode:          order.SaleMode, // 销售模式
			SaleModeName:      order.SaleMode.String(),
			AuditStatus:       order.AuditStatus, // 审核状态
			AuditStatusName:   order.AuditStatus.String(),
			CollectStatus:     order.CollectStatus, // 应收状态
			CollectStatusName: order.CollectStatus.String(),

			// 送货单创建日期
			CreateTime: order.CreateTime,
			// 销售单号
			SaleOrderID:       order.SaleOrderID,
			SaleOrderNo:       fpmArrangeOrders.PickByOrderID(order.SrcId).SrcOrderNo,
			DyelotNumber:      detail.DyelotNumber,
			SettleErrorWeight: detail.SettleErrorWeight,
		}

		res.List = append(res.List, item)
	}

	// 设置总数量
	res.Total = int64(count)

	return res, nil
}

func ExponentialSmoothingPredict(data []int64, alpha float64) float64 {
	// 使用数组中最近的实际值作为计算基准
	// 切片的长度最多为5个月的数据
	maxMonths := 6
	if len(data) > maxMonths {
		// 仅使用最近的5个月的数据
		data = data[len(data)-maxMonths:]
	}
	// 第一个月份不进行预测
	prevActual := float64(data[0]) // 第一个月的实际值
	forecast := prevActual         // 初始预测值与第一个实际值相同
	// 逐步计算每个时间点的预测值，从第二个数据点开始
	for i := 1; i < len(data); i++ {
		actual := float64(data[i]) // 当前月的实际值
		// 公式：当月预测 = α × 当月实际值 + (1-α) × 上月预测值
		newForecast := alpha*actual + (1-alpha)*forecast
		fmt.Printf("第%d步计算: 实际值=%d, 前期预测=%.2f, 新预测=%.2f\n",
			i, data[i], forecast, newForecast)

		// 更新预测值，用于下一次计算
		forecast = newForecast
	}
	return forecast
}

func GetEstimateData(tx *mysql_base.Tx, targetMonthStart time.Time, targetMonthEnd time.Time, req *data_analysis.GetAnalysisHomeReq) (lastYearRoll, beforeYearRoll int64, lastYearWeight, lastYearAmount, beforeYearWeight, beforeYearAmount float64) {
	req.TrendStartTime = tools.QueryTime(targetMonthStart.AddDate(-1, 0, 0).Format("2006-01-02"))
	req.TrendEndTime = tools.QueryTime(targetMonthEnd.AddDate(-1, 0, 0).Format("2006-01-02"))
	// 如果是当前未结束的月份，获取上一年和前年同期的数据用于计算预估值
	lastYearDetails, err := mysql.GetProductAnalysisData(tx, req, 1, true)
	if err != nil {
		return
	}
	req.TrendStartTime = tools.QueryTime(targetMonthStart.AddDate(-2, 0, 0).Format("2006-01-02"))
	req.TrendEndTime = tools.QueryTime(targetMonthEnd.AddDate(-2, 0, 0).Format("2006-01-02"))
	beforeYearDetails, err := mysql.GetProductAnalysisData(tx, req, 1, true)
	if err != nil {
		return
	}

	// 计算上一年同期数据
	for _, d := range lastYearDetails {
		lastYearRoll += d.TotalRoll
		lastYearAmount += d.SaleAmount
		lastYearWeight += d.TotalSettleWeight
	}

	// 计算前年同期数据
	for _, d := range beforeYearDetails {
		beforeYearRoll += d.TotalRoll
		beforeYearAmount += d.SaleAmount
		beforeYearWeight += d.TotalSettleWeight
	}
	return
}
