package msg_consume

import (
	"context"
	"encoding/json"
	"hcscm/server/system"
	svc "hcscm/service/sale_price"
	structure "hcscm/structure/sale_price"
	"hcscm/tools/metadata"
	"strconv"
)

func ConsumeAdjustSalePriceEffective(message []byte) (err error) {
	// 根据token获取登录态
	var (
		ctx = context.Background()
		msg structure.AdjustSalePriceEffective
		svc = svc.NewSalePriceAdjustOrderService()
	)
	err = json.Unmarshal(message, &msg)
	if err != nil {
		return
	}
	tenantMangementId, _ := strconv.ParseUint(msg.TenantManagementId, 10, 64)
	info := &system.WashSystemUser{TenantManagementId: tenantMangementId, TenantManagement: system.TenantManagement{DatabaseName: msg.DbName}}
	// ctx = metadata.SetMDToIncoming(ctx, "user_name", system.SystemOperator.GetUserName())
	// ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(system.SystemOperator.GetUserId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(tenantMangementId, 10))
	ctx = context.WithValue(ctx, metadata.LoginInfo, info)
	err = svc.AdjustPriceOrderEffective(ctx, msg)
	return
}

func ConsumeAdjustSalePriceWait(message []byte) (err error) {
	// 根据token获取登录态
	var (
		ctx = context.Background()
		msg structure.AdjustSalePriceEffective
		svc = svc.NewSalePriceAdjustOrderService()
	)
	err = json.Unmarshal(message, &msg)
	if err != nil {
		return
	}
	ctx = metadata.SetMDToIncoming(ctx, "user_name", system.SystemOperator.GetUserName())
	ctx = metadata.SetMDToIncoming(ctx, "user_id", strconv.FormatUint(system.SystemOperator.GetUserId(), 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(system.SystemOperator.GetTenantManagementId(), 10))
	ctx = context.WithValue(ctx, metadata.LoginInfo, system.SystemOperator)
	err = svc.WaitAdjustPriceOrderEffective(ctx, msg)
	return
}
