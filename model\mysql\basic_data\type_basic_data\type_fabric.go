package type_basic_data

import (
	common "hcscm/common/basic_data"
	"hcscm/common/errors"
	"hcscm/middleware"
	. "hcscm/model/mysql/basic_data/type_basic_data/dao"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/basic_data/type_basic_data"
	"strings"
)

func MustCreateTypeFabric(tx *mysql_base.Tx, r TypeFabric) (o TypeFabric, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateTypeFabric(tx *mysql_base.Tx, r TypeFabric) (o TypeFabric, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteTypeFabric(tx *mysql_base.Tx, r TypeFabric) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstTypeFabricByID(tx *mysql_base.Tx, id uint64) (r TypeFabric, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstTypeFabricByID(tx *mysql_base.Tx, id uint64) (r TypeFabric, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FirstTypeFabric(tx *mysql_base.Tx, code, name string) (r TypeFabric, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	if code != "" {
		cond.AddEqual("code", strings.TrimSpace(code))
	} else {
		if strings.TrimSpace(name) != "" {
			cond.AddEqual("name", strings.TrimSpace(name))
		}
	}
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	return
}

func FirstEnableTypeFabric(tx *mysql_base.Tx) (r TypeFabric, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddEqual("status", common.StatusEnable)
	cond.AddSort("code")
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	return
}

func FindTypeFabricByTypeFabricID(tx *mysql_base.Tx, objects ...interface{}) (o TypeFabricList, err error) {
	ids := GetTypeFabricIdList(objects)
	var (
		r    TypeFabric
		cond = mysql_base.NewCondition()
		list []TypeFabric
	)

	cond.AddContainMatch("id", ids)
	cond.AddSort("length(code)", "code")
	err = mysql_base.FindByCondSort(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindTypeFabricByIDs(tx *mysql_base.Tx, ids []uint64) (o TypeFabricList, err error) {
	var (
		r    TypeFabric
		cond = mysql_base.NewCondition()
		list []TypeFabric
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddContainMatch("id", ids)
	cond.AddSort("length(code)", "code")
	err = mysql_base.FindByCondSort(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindTypeFabricByParentID(tx *mysql_base.Tx, id uint64) (o TypeFabricList, err error) {
	var (
		r    TypeFabric
		cond = mysql_base.NewCondition()
		list []TypeFabric
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "parent_id", id)
	cond.AddSort("length(code)", "code")
	err = mysql_base.FindByCondSort(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 获取子类型,isIncludeParent 是否包含父节点
func FindTypeFabricSubByParentID(tx *mysql_base.Tx, id uint64, isIncludeParent bool) (o TypeFabricList, err error) {
	var (
		list []TypeFabric
		sql  string
	)
	if isIncludeParent {
		sql = `WITH recursive temp AS (SELECT * FROM type_fabric p WHERE id = ? UNION ALL SELECT t.* FROM type_fabric t INNER JOIN temp t2 ON t2.id = t.parent_id) SELECT * FROM temp where temp.delete_time = '0000-00-00 00:00:00' `
		err = mysql_base.GetSlaveConn(tx).Raw(sql, id).Scan(&list).Error
	} else {
		sql = `WITH recursive temp AS (SELECT * FROM type_fabric p WHERE id = ? UNION ALL SELECT t.* FROM type_fabric t INNER JOIN temp t2 ON t2.id = t.parent_id) SELECT * FROM temp where temp.delete_time = '0000-00-00 00:00:00' and temp.id != ? `
		err = mysql_base.GetSlaveConn(tx).Raw(sql, id, id).Scan(&list).Error
	}
	if err != nil {
		err = middleware.ErrorLog(errors.NewError(errors.ErrCodeMysqlRetrieve))
		return
	}
	o = list
	return
}

// 查找父节点及其本身
func FindParentTypeFabric(tx *mysql_base.Tx, objects ...interface{}) (o TypeFabricList, err error) {
	var (
		list []TypeFabric
		ids  []uint64
	)
	ids = GetTypeFabricIdList(objects)

	sql := `WITH recursive temp AS (SELECT * FROM type_fabric p WHERE p.id in (?) UNION ALL SELECT t.* FROM temp,type_fabric t WHERE temp.parent_id = t.id ) SELECT * FROM temp where temp.delete_time = '0000-00-00 00:00:00' group by temp.id ORDER BY temp.parent_id,length(temp.code),temp.code`
	err = mysql_base.GetSlaveConn(tx).Raw(sql, ids).Scan(&list).Error
	if err != nil {
		err = middleware.ErrorLog(errors.NewError(errors.ErrCodeMysqlRetrieve))
		return
	}
	o = list
	return
}

func SearchTypeFabric(tx *mysql_base.Tx, q *structure.GetTypeFabricListQuery) (o TypeFabricList, count int, err error) {
	var (
		r           TypeFabric
		cond        = mysql_base.NewCondition()
		list        []TypeFabric
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.Name != "" {
		cond.AddTableFuzzyMatch(r, "name", q.Name)
	}
	if q.Status != 0 {
		cond.AddTableEqual(r, "status", q.Status)
	}
	cond.AddSort("length(code)", "code")
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchTypeFabricNoPage(tx *mysql_base.Tx, q *structure.GetTypeFabricListQuery) (o TypeFabricList, err error) {
	var (
		r           TypeFabric
		cond        = mysql_base.NewCondition()
		list        []TypeFabric
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)

	if len(q.Code) > 0 {
		cond.AddTableFuzzyMatch(r, "code", q.Code)
	}
	if len(q.Name) > 0 {
		cond.AddTableFuzzyMatch(r, "name", q.Name)
	}
	// cond.AddTableEqual(r, "status", iconst.StatusEnable)
	cond.AddSort("length(code)", "code")
	groupFields = []string{}
	err = mysql_base.SearchListGroup(tx, &r, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchTypeFabricTree(tx *mysql_base.Tx, q *structure.GetTypeFabricTreeListQuery) (o TypeFabricList, count int, err error) {
	var (
		r           TypeFabric
		cond        = mysql_base.NewCondition()
		list        []TypeFabric
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.Code != "" {
		cond.AddTableMultiFieldLikeMatch(r, []string{"code", "name"}, q.Code)
	}
	if q.Status != 0 {
		cond.AddTableEqual(r, "status", q.Status)
	}
	// cond.AddTableEqual(r, "parent_id", 0)
	cond.AddSort("parent_id", "length(code)", "code")
	groupFields = []string{}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据编号和名称查询
func FindTypeFabricByCodeOrName(tx *mysql_base.Tx, code, name []string) (o TypeFabricList, err error) {
	var (
		r    TypeFabric
		cond = mysql_base.NewCondition()
		list []TypeFabric
	)

	cond.AddContainMatchToOR("code", code)
	cond.AddContainMatchToOR("name", name)

	err = mysql_base.FindByCond(tx, &r, &list, cond)
	o = list
	return
}
