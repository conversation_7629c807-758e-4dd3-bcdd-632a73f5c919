package system

import "hcscm/server/system"

func InitDistrictArea(routerGroup *system.RouterGroup) {
	// 获取行政地区列表
	{
		dictionary := routerGroup.Group("district")
		dictionary.GET("获取行政地区列表", "list", system.GetDistrictList)
		dictionary.GET("获取全部级别地区列表", "enum_list", system.GetDistrictEnumList)
	}
}
func MPInitDistrictArea(routerGroup *system.RouterGroup) {
	// 获取行政地区列表
	{
		dictionary := routerGroup.Group("district")
		dictionary.GET("获取行政地区列表", "list", system.GetDistrictList)
		dictionary.GET("获取全部级别地区列表", "enum_list", system.GetDistrictEnumList)
	}
}
func H5InitDistrictArea(routerGroup *system.RouterGroup) {
	// 获取行政地区列表
	{
		dictionary := routerGroup.Group("district")
		dictionary.GET("获取行政地区列表", "list", system.GetDistrictList)
		dictionary.GET("获取全部级别地区列表", "enum_list", system.GetDistrictEnumList)
	}
}
