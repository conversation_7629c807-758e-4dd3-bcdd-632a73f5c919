package sms

import (
	"encoding/json"
	"fmt"
	openapi "github.com/alibabacloud-go/darabonba-openapi/v2/client"
	dysmsapi "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	dysmsapi20170525 "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"hcscm/common/errors"
	"hcscm/extern/aliyun"
	"hcscm/middleware"
	"hcscm/tools"
)

func getClient() (client *dysmsapi20170525.Client, err error) {

	// 创建短信发送服务客户端,建立短链
	client = &dysmsapi20170525.Client{}
	client, err = dysmsapi20170525.NewClient(&openapi.Config{
		AccessKeyId:     tea.String(aliyun.AccessKey),
		AccessKeySecret: tea.String(aliyun.AccessSecret),
		Endpoint:        tea.String("dysmsapi.aliyuncs.com"),
	})
	if err != nil {
		fmt.Println("Failed to create client:", err)
		return
	}
	return
}

// 发送验证码
func SendVerificationCode(phoneNumber string) (verificationCode string, err error) {
	var client *dysmsapi.Client
	client, err = getClient()
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeSystemError, ",建立链接失败!"))
		return
	}
	// 生成随机验证码
	verificationCode = tools.GetRandomNumber(6)

	// 构建短信参数
	params := map[string]interface{}{
		"code": verificationCode,
	}

	// 发送短信
	response, err := sendSMS(client, aliyun.SmsSignName, aliyun.SmsTemplateCode, phoneNumber, params)
	if err != nil {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeSystemError, "，发送短信失败!"), response)
		return
	}

	// 打印发送结果
	fmt.Println("SMS sent successfully!")
	fmt.Println("RequestId:", response.Body.RequestId)
	fmt.Println("statusCode:", response.StatusCode)
	fmt.Println("BizId:", response.Body.BizId)
	fmt.Println("body:", response.Body)
	return
}

// 发送短信
func sendSMS(client *dysmsapi.Client, signName, templateCode, phoneNumber string, templateParam map[string]interface{}) (*dysmsapi.SendSmsResponse, error) {
	var (
		sendSmsRes     = &dysmsapi.SendSmsResponse{}
		sendSmsRequest = &dysmsapi.SendSmsRequest{}
	)
	sendSmsRequest.SignName = tea.String(signName)
	sendSmsRequest.TemplateCode = tea.String(templateCode)
	sendSmsRequest.PhoneNumbers = tea.String(phoneNumber)

	jsonBytes, err := json.Marshal(templateParam)
	if err != nil {
		return nil, err
	}
	// 构建模板参数
	// for key, value := range templateParam {
	//	request.TemplateParam[key] = fmt.Sprintf("%v", value)
	// }
	sendSmsRequest.TemplateParam = tea.String(string(jsonBytes))

	// 复制代码运行请自行打印 API 的返回值
	sendSmsRes, err = client.SendSms(sendSmsRequest)
	if err != nil {
		return nil, err
	}

	return sendSmsRes, nil
}
