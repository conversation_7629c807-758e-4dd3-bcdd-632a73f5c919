package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	common_product "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	dic_pb "hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	stock_product "hcscm/extern/pb/product"
	purchase_pb "hcscm/extern/pb/purchase"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	should_collect_pb "hcscm/extern/pb/should_collect_order"
	user_pb "hcscm/extern/pb/user"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strconv"
	"strings"
	"time"
)

type FpmArrangeOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmArrangeOrderRepo(tx *mysql_base.Tx) *FpmArrangeOrderRepo {
	return &FpmArrangeOrderRepo{tx: tx}
}

func (r *FpmArrangeOrderRepo) Add(ctx context.Context, req *structure.AddFpmArrangeOrderParam) (id uint64, salePlanOrderItemIds []uint64, err error) {
	var (
		info                  = metadata.GetLoginInfo(ctx)
		sale_sys_svc          = sale_sys_pb.NewSaleSystemClient()
		saleSysData           = sale_sys_pb.Res{}
		_salePlanOrderItemIds = make([]uint64, 0)
		orderPrefix           mysqlSystem.OrderPrefix
		prefix, dateFormat    string
		numLength             int
		exist                 bool
	)

	fpmArrangeOrder := model.NewFpmArrangeOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	err = fpmArrangeOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeWait
	fpmArrangeOrder.DepartmentId = info.GetDepartmentId()

	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})

	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmArrangeOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmArrangeOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}

	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmArrangeOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmArrangeOrder.OrderNo = orderNo
	fpmArrangeOrder.Number = int(number)

	fpmArrangeOrder.TotalLength, fpmArrangeOrder.TotalWeight, fpmArrangeOrder.TotalRoll = req.GetTotalLWR()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmArrangeOrder.UnitId = item.UnitId
			break
		}
	}

	fpmArrangeOrder, err = mysql.MustCreateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmArrangeOrderItem := model.NewFpmArrangeOrderItem(ctx, &item)
		fpmArrangeOrderItem.ParentId = fpmArrangeOrder.Id
		fpmArrangeOrderItem.ParentOrderNo = fpmArrangeOrder.OrderNo
		fpmArrangeOrderItem, err = mysql.MustCreateFpmArrangeOrderItem(r.tx, fpmArrangeOrderItem)
		if err != nil {
			return
		}
		if item.SalePlanOrderItemId > 0 && fpmArrangeOrder.SrcType == common_product.ArrangeOrderFromSaleOrder {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
	}

	if fpmArrangeOrder.AuditStatus == common_system.OrderStatusAudited {
		salePlanOrderItemIds = _salePlanOrderItemIds
	}

	id = fpmArrangeOrder.Id
	return
}

func (r *FpmArrangeOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmArrangeOrderParam) (data structure.UpdateFpmArrangeOrderData, stockIds map[uint64]bool, err error) {
	var (
		fpmArrangeOrder model.FpmArrangeOrder
		// itemModel       model.FpmArrangeOrderItem
		fcModel       model.FpmArrangeOrderItemFc
		itemList      model.FpmArrangeOrderItemList
		fcList        model.FpmArrangeOrderItemFcList
		isAlready     bool = true
		isHasFc       bool = false
		oldStockIdMap      = set.NewUint64Set()
		newStockIdMap      = set.NewUint64Set()
	)

	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断是否是可以编辑的状态
	if fpmArrangeOrder.BusinessStatus > common_product.BusinessStatusArrangeAllready {
		err = errors.NewCustomError(errors.ErrCodeBusinessStatusFalse, "当前业务状态无法编辑单据")
		return
	}

	fpmArrangeOrder.UpdateFpmArrangeOrder(ctx, req)

	fpmArrangeOrder.TotalLength, fpmArrangeOrder.TotalWeight, fpmArrangeOrder.TotalRoll = req.GetTotalLWR()

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmArrangeOrder.UnitId = item.UnitId
			break
		}
	}

	// 如果配布但为已拒绝，则修改为待审核
	if fpmArrangeOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmArrangeOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}
	itemIds := itemList.GetIds()
	// addItemIDs := req.ItemData.GetIds()

	// 删除不符合的数据（提取id）
	// diffItemIds := tools.GetDifferentUint64(itemIds, addItemIDs)
	fcList, err = mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, itemIds)
	if err != nil {
		return
	}

	// 旧的库存id保存起来
	for _, fc := range fcList {
		oldStockIdMap.Add(fc.StockId)
	}

	if len(itemIds) > 0 {
		// if len(diffItemIds) > 0 {
		//	err = mysql.MustDeleteByIds(r.tx, diffItemIds, itemModel)
		//	if err != nil {
		//		return
		//	}
		// }

		if len(fcList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, fcModel, "parent_id")
			if err != nil {
				return
			}
		}
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		totalFCRoll := 0
		fpmArrangeOrderItem := itemList.Pick(item.Id)

		// 出仓数量 码单空差 码单数量 结算空差 结算数量 长度
		countFc := item.GetCountFc()
		if item.Id == 0 {

			fpmArrangeOrderItem = model.NewFpmArrangeOrderItem(ctx, &item)
			fpmArrangeOrderItem.ParentId = fpmArrangeOrder.Id
			fpmArrangeOrderItem.ParentOrderNo = fpmArrangeOrder.OrderNo

			fpmArrangeOrderItem.ArrangeWeight = countFc[0]
			fpmArrangeOrderItem.WeightError = countFc[1]
			fpmArrangeOrderItem.ActuallyWeight = countFc[2]
			fpmArrangeOrderItem.SettleErrorWeight = countFc[3]
			fpmArrangeOrderItem.SettleWeight = countFc[4]
			fpmArrangeOrderItem.ArrangeLength = countFc[5]
			fpmArrangeOrderItem.ArrangeRoll = countFc[6]

			fpmArrangeOrderItem, err = mysql.MustCreateFpmArrangeOrderItem(r.tx, fpmArrangeOrderItem)
		} else {
			fpmArrangeOrderItem.UpdateFpmArrangeOrderItem(ctx, &item)

			fpmArrangeOrderItem.ArrangeWeight = countFc[0]
			fpmArrangeOrderItem.WeightError = countFc[1]
			fpmArrangeOrderItem.ActuallyWeight = countFc[2]
			fpmArrangeOrderItem.SettleErrorWeight = countFc[3]
			fpmArrangeOrderItem.SettleWeight = countFc[4]
			fpmArrangeOrderItem.ArrangeLength = countFc[5]
			fpmArrangeOrderItem.ArrangeRoll = countFc[6]

			fpmArrangeOrderItem, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, fpmArrangeOrderItem)
		}
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			totalFCRoll += fineCode.Roll
			itemFc := model.NewFpmArrangeOrderItemFc(ctx, &fineCode)
			itemFc.ParentId = fpmArrangeOrderItem.Id
			itemFc.ArrangeOrderNo = fpmArrangeOrder.OrderNo
			itemFc.ActuallyWeight = itemFc.BaseUnitWeight - itemFc.WeightError
			itemFc.SettleWeight = itemFc.ActuallyWeight - itemFc.SettleErrorWeight
			itemFc, err = mysql.MustCreateFpmArrangeOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
			isHasFc = true
			// 如果新的库存id在旧的里面，则删除旧的，新的添加
			if oldStockIdMap.In(itemFc.StockId) {
				oldStockIdMap.Delete(itemFc.StockId)
			} else {
				newStockIdMap.Add(itemFc.StockId)
			}
		}
		if totalFCRoll < item.PushRoll {
			isAlready = false
		}
	}

	// 还未配置细码：待配布
	// 细码条数与外面分录行不一致且已经配了布：配布中
	// 细码条数与外面分录行一致：已配布
	if isAlready {
		fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeAllready
	} else if isHasFc {
		fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeInProgress
	} else {
		fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeWait
	}
	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	data.Id = fpmArrangeOrder.Id

	// 处理占用与释放库存id,true的为占用，false的为释放
	stockIds = make(map[uint64]bool)
	// 旧的库存id释放
	for _, id := range oldStockIdMap.List() {
		stockIds[id] = false
	}
	// 新的库存id占用
	for _, id := range newStockIdMap.List() {
		stockIds[id] = true
	}

	// 判断是否已经审核,用于上层执行对应的逻辑
	if fpmArrangeOrder.AuditStatus == common_system.OrderStatusAudited {
		data.IsAudited = true
	}
	return
}

// pda扫码啊配布使用
func (r *FpmArrangeOrderRepo) UpdateFpmArrangeOrderFc(ctx context.Context, req *structure.UpdateArrangeOrderFcParam, stock structure.GetStockProductDetailDropdownData) (data structure.UpdateFpmArrangeOrderData, err error) {
	var (
		fpmArrangeOrder model.FpmArrangeOrder
		itemList        model.FpmArrangeOrderItemList
		fcList          model.FpmArrangeOrderItemFcList
		item            model.FpmArrangeOrderItem
		isAlready       = true
	)
	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断配布单的业务状态是否是已经确认出仓
	if fpmArrangeOrder.BusinessStatus >= common_product.BusinessStatusArrangeWaitOut {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，配布单当前状态不能修改"))
		return
	}

	// 判断配布单仓库是否与库存的一致
	if fpmArrangeOrder.WarehouseId != stock.WarehouseId {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，该条码与配布单仓库不一致"))
		return
	}

	itemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	if req.ArrangeType == common_product.ArrangeTypeIn {
		var exist bool
		_, exist, err = mysql.FirstFpmArrangeOrderItemFcByStockID(r.tx, itemList.GetIds(), stock.Id)
		if err != nil {
			return
		}
		if exist {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，请勿重复录入"))
			return
		}
	}

	// 判断是否是可以编辑的状态
	// if fpmArrangeOrder.BusinessStatus > common_product.BusinessStatusArrangeAllready {
	//	return errors.NewCustomError(errors.ErrCodeBusinessStatusFalse, "当前业务状态无法编辑单据")
	// }

	// 获取所有配布单项目的ID
	itemIds := itemList.GetIds()

	// 检查是否需要进行同色同缸检查
	// 如果需要同色同缸检查
	if fpmArrangeOrder.SameColorSameDyeLot {
		// 同色同缸验证：同一产品ID和同一颜色ID下的缸号必须一致
		var dyelotMap = make(map[uint64]string) // 映射产品颜色ID到缸号

		// 遍历所有已有的细码，收集每个产品颜色ID对应的缸号
		fcList, err = mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, itemIds)
		if err != nil {
			return
		}

		for _, fc := range fcList {
			if fc.DyeFactoryDyelotNumber != "" {
				dyelotMap[fc.SumStockId] = fc.DyeFactoryDyelotNumber
			}
		}

		// 检查当前扫码的产品颜色ID是否已有缸号记录
		if existingDyelot, exists := dyelotMap[stock.StockProductId]; exists {
			// 如果已有记录，且缸号不一致，则报错
			if existingDyelot != stock.DyelotNumber {
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，同色同缸要求：同一产品颜色的缸号必须一致。"))
				return
			}
		}

		// 如果缸号匹配或还没有设置缸号，则查找匹配的项目
		item = itemList.PickByProductColorIdAndDyelotNumber(stock.StockProductId, stock.DyelotNumber)
		if item.Id == 0 {
			item = itemList.PickByProductColorIdAndDyelotNumber(stock.StockProductId, "")
			if item.Id == 0 {
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，未找到匹配的条数。"))
				return
			}
		}
	} else {
		// 不需要同色同缸检查，按照原来的逻辑处理
		item = itemList.PickByProductColorIdAndDyelotNumber(stock.StockProductId, stock.DyelotNumber)
		if item.Id == 0 {
			item = itemList.PickByProductColorIdAndDyelotNumber(stock.StockProductId, "")
			if item.Id == 0 {
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，未找到匹配的条数。"))
				return
			}
		}
	}
	// 删除不符合的数据（提取id）
	fcList, err = mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, itemIds)

	// 驳回变更为待审核
	if fpmArrangeOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmArrangeOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	data.StockDetailIds = []uint64{stock.Id}
	// 配布细码、占用细码库存
	if req.ArrangeType == common_product.ArrangeTypeIn {
		var itemFc = model.FpmArrangeOrderItemFc{}
		itemFc.Id = vars.Snowflake.GenerateId().UInt64()
		itemFc.Roll = stock.Roll
		itemFc.WarehouseBinId = stock.WarehouseBinId
		itemFc.VolumeNumber = stock.VolumeNumber
		itemFc.WarehouseOutType = fpmArrangeOrder.OutOrderType
		itemFc.WarehouseOutOrderId = stock.WarehouseOutOrderId
		itemFc.WarehouseOutOrderNo = stock.WarehouseOutOrderNo
		itemFc.WarehouseInType = stock.WarehouseInType
		itemFc.WarehouseInOrderId = stock.WarehouseInOrderId
		itemFc.WarehouseInOrderNo = stock.WarehouseInOrderNo
		itemFc.ArrangeOrderNo = fpmArrangeOrder.OrderNo
		itemFc.StockId = stock.Id
		itemFc.SumStockId = stock.StockProductId
		itemFc.BaseUnitWeight = stock.Weight
		itemFc.PaperTubeWeight = stock.PaperTubeWeight
		itemFc.WeightError = stock.WeightError
		itemFc.UnitId = stock.MeasurementUnitId
		itemFc.Length = stock.Length
		itemFc.SettleWeight = stock.Weight
		itemFc.DigitalCode = stock.DigitalCode
		itemFc.ShelfNo = stock.ShelfNo
		itemFc.ContractNumber = stock.ContractNumber
		itemFc.CustomerPoNum = stock.CustomerPoNum
		itemFc.AccountNum = stock.CustomerAccountNum
		itemFc.DyeFactoryColorCode = stock.DyeFactoryColorCode
		itemFc.DyeFactoryDyelotNumber = stock.DyelotNumber
		itemFc.ProductWidth = stock.ProductWidth
		itemFc.ProductGramWeight = stock.ProductGramWeight
		itemFc.StockRemark = stock.Remark
		itemFc.InternalRemark = stock.InternalRemark
		itemFc.ScanUserId = metadata.GetUserId(ctx)
		itemFc.ScanUserName = metadata.GetUserName(ctx)
		// itemFc.SettleErrorWeight = stock.WeightError
		itemFc.ScanTime = time.Now()
		itemFc.ParentId = item.Id
		itemFc.ArrangeOrderNo = fpmArrangeOrder.OrderNo
		itemFc.ActuallyWeight = itemFc.BaseUnitWeight - itemFc.WeightError
		itemFc.SettleWeight = itemFc.ActuallyWeight - itemFc.SettleErrorWeight
		itemFc, err = mysql.MustCreateFpmArrangeOrderItemFc(r.tx, itemFc)
		if err != nil {
			return
		}

		item.ArrangeRoll += itemFc.Roll
		item.ArrangeWeight += itemFc.BaseUnitWeight
		item.WeightError += itemFc.WeightError
		item.ActuallyWeight += itemFc.ActuallyWeight
		item.SettleErrorWeight += itemFc.SettleErrorWeight
		item.SettleWeight += itemFc.SettleWeight
		item.ArrangeLength += itemFc.Length
		// 判断配布匹数是否有超出下推匹数，下推匹数不为0时判断
		if item.PushRoll != 0 {
			if item.PushRoll < item.ArrangeRoll {
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOperate, "，配布匹数已超出。"))
				return
			}
		}
		item, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, item)
		if err != nil {
			return
		}

		fpmArrangeOrder.TotalRoll += itemFc.Roll
		fpmArrangeOrder.TotalWeight += itemFc.BaseUnitWeight
		fpmArrangeOrder.TotalLength += itemFc.Length
	}
	// 整缸录入、占用整缸细码库存
	if req.ArrangeType == common_product.ArrangeTypeDyeIn {
		// todo:整缸录入待完善
		data.StockDetailIds = []uint64{}
	}
	// 取消配布细码、取消细码占用
	if req.ArrangeType == common_product.ArrangeTypeDel {
		itemFc := fcList.PickByStockId(stock.Id)
		// 检查是否找到了有效的细码
		if itemFc.Id == 0 {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，未找到对应的细码记录。"))
			return
		}

		err = mysql.MustDeleteFpmArrangeOrderItemFc(r.tx, itemFc)
		if err != nil {
			return
		}

		item.ArrangeRoll -= itemFc.Roll
		item.ArrangeWeight -= itemFc.BaseUnitWeight
		item.WeightError -= itemFc.WeightError
		item.ActuallyWeight -= itemFc.ActuallyWeight
		item.SettleErrorWeight -= itemFc.SettleErrorWeight
		item.SettleWeight -= itemFc.SettleWeight
		item.ArrangeLength -= itemFc.Length
		item, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, item)
		if err != nil {
			return
		}

		fpmArrangeOrder.TotalRoll -= itemFc.Roll
		fpmArrangeOrder.TotalWeight -= itemFc.BaseUnitWeight
		fpmArrangeOrder.TotalLength -= itemFc.Length
	}

	var (
		totalFcWeight int
		totalWeight   int
	)
	for _, orderItem := range itemList {
		totalFcWeight = 0
		_fcList := fcList.PickList(orderItem.Id)
		for _, fc := range _fcList {
			totalFcWeight += fc.SettleWeight
			totalWeight += fc.SettleWeight
		}
		if totalFcWeight < orderItem.PushWeight {
			isAlready = false
		}
	}

	// 还未配置细码：待配布
	// 细码条数与外面分录行不一致且已经配了布：配布中
	// 细码条数与外面分录行一致：已配布
	if totalWeight != 0 {
		if isAlready {
			fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeAllready
		} else {
			fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeInProgress
		}
	} else {
		fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeWait
	}
	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	// 判断是否已经审核,用于上层执行对应的逻辑
	if fpmArrangeOrder.AuditStatus == common_system.OrderStatusAudited {
		data.IsAudited = true
	}
	return
}

func (r *FpmArrangeOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmArrangeOrderBusinessCloseParam) (data structure.UpdateFpmArrangeOrderData, err error) {
	var (
		fpmArrangeOrder model.FpmArrangeOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, v)
		if err != nil {
			return
		}
		err = fpmArrangeOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmArrangeOrderRepo) UpdateBusinessStatus(ctx context.Context, status common_product.BusinessStatus, id uint64) (
	data structure.UpdateFpmArrangeOrderData, err error) {

	var (
		model = model.FpmArrangeOrder{}
	)
	model, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}

	model.BusinessStatus = status
	model, err = mysql.MustUpdateFpmArrangeOrder(r.tx, model)
	if err != nil {
		return
	}
	data.SrcType = model.SrcType
	return
}

func (r *FpmArrangeOrderRepo) FindItemByOrderId(ctx context.Context, orderId uint64) (data structure.GetFpmArrangeOrderItemDataList, err error) {
	var (
		_data = make(structure.GetFpmArrangeOrderItemDataList, 0)
	)
	if orderId == 0 {
		return
	}

	itemList, err := mysql.FindFpmArrangeOrderItemByParenTID(r.tx, orderId)
	if err != nil {
		return
	}
	for _, item := range itemList {
		_data = append(_data, item.BuildResp())
	}
	data = _data
	return
}

func (r *FpmArrangeOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (
	bookItemsReq structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	stockIds []uint64,
	err error) {
	var (
		fpmArrangeOrder           model.FpmArrangeOrder
		fpmArrangeOrderItemList   model.FpmArrangeOrderItemList
		fpmArrangeOrderItemFcList model.FpmArrangeOrderItemFcList
		_bookItemsReq             = make(structure.UpdateStockProductDetailParamList, 0)
		_salePlanOrderItemIds     = make([]uint64, 0)
		newStockIdMap             = set.NewUint64Set()
	)

	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}
	fpmArrangeOrderItemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	fpmArrangeOrderItemFcList, err = mysql.FindFpmArrangeOrderItemFcByParentID(r.tx, fpmArrangeOrderItemList.GetIds())
	if err != nil {
		return
	}

	// r.judgeAuditPass(id, fpmArrangeOrder)

	// 占用库存
	for _, item := range fpmArrangeOrderItemList {
		if item.SalePlanOrderItemId > 0 && fpmArrangeOrder.SrcType == common_product.ArrangeOrderFromSaleOrder {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
		_bookItemsReq = append(_bookItemsReq, item.ToUpdateStockProductDetailParam(true))
	}
	for _, fpmArrangeOrderItemFc := range fpmArrangeOrderItemFcList {
		newStockIdMap.Add(fpmArrangeOrderItemFc.StockId)
	}
	// 审核
	err = fpmArrangeOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}
	bookItemsReq = _bookItemsReq
	salePlanOrderItemIds = _salePlanOrderItemIds
	stockIds = newStockIdMap.List()
	return
}

func (r *FpmArrangeOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (
	data structure.UpdateFpmArrangeOrderStatusData,
	bookItemsReq structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	stockIds []uint64,
	err error) {
	var (
		fpmArrangeOrder           model.FpmArrangeOrder
		fpmArrangeOrderItemList   model.FpmArrangeOrderItemList
		fpmArrangeOrderItemFcList model.FpmArrangeOrderItemFcList
		_bookItemsReq             = make(structure.UpdateStockProductDetailParamList, 0)
		_salePlanOrderItemIds     = make([]uint64, 0)
		newStockIdMap             = set.NewUint64Set()
		exit                      bool
		isNoCancel                bool
	)

	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}
	fpmArrangeOrderItemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	fpmArrangeOrderItemFcList, err = mysql.FindFpmArrangeOrderItemFcByParentID(r.tx, fpmArrangeOrderItemList.GetIds())
	if err != nil {
		return
	}

	// 检查送货单是否已作废
	if fpmArrangeOrder.SrcType == common_product.ArrangeOrderFromSaleOrder {
		isNoCancel, err = should_collect_pb.NewShouldCollectOrderClient().ShouldCollectOrderIsNoCancel(ctx, fpmArrangeOrder.Id)
		if err != nil {
			return
		}
		if isNoCancel {
			err = errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, " 销售送货单未作废")
			return
		}
	}

	// 业务状态不对
	if !(fpmArrangeOrder.BusinessStatus < common_product.BusinessStatusArrangeWaitOut || fpmArrangeOrder.BusinessStatus == common_product.BusinessStatusArrangeCancel) {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessStatusFalse, "已产生下游单据"))
		return
	}

	// 判断是否被被引用使用
	exit, _, err = r.JudgeIsQuoted(id)
	if err != nil {
		return
	}
	if exit {
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeTheOrderIsQuoted))
		return
	}
	// 占用库存
	for _, item := range fpmArrangeOrderItemList {
		if item.SalePlanOrderItemId > 0 && fpmArrangeOrder.SrcType == common_product.ArrangeOrderFromSaleOrder {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
		_bookItemsReq = append(_bookItemsReq, item.ToUpdateStockProductDetailParam(false))
	}
	for _, fpmArrangeOrderItemFc := range fpmArrangeOrderItemFcList {
		newStockIdMap.Add(fpmArrangeOrderItemFc.StockId)
	}
	// 消审
	err = fpmArrangeOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	bookItemsReq = _bookItemsReq
	salePlanOrderItemIds = _salePlanOrderItemIds
	stockIds = newStockIdMap.List()
	return
}

func (r *FpmArrangeOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmArrangeOrderStatusData, err error) {
	var (
		fpmArrangeOrder model.FpmArrangeOrder
	)

	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 驳回
	err = fpmArrangeOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmArrangeOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmArrangeOrderStatusData, err error) {
	var (
		fpmArrangeOrder model.FpmArrangeOrder
	)

	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 作废
	err = fpmArrangeOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmArrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeCancel
	fpmArrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, fpmArrangeOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmArrangeOrderRepo) ExistOrder(ctx context.Context, q *structure.ExistOrderQuery) (exist bool, err error) {
	_, exist, err = mysql.FirstFpmArrangeOrderByQuery(r.tx, q)
	if err != nil {
		return
	}

	return
}

func (r *FpmArrangeOrderRepo) Get(ctx context.Context, req *structure.GetFpmArrangeOrderQuery) (data structure.GetFpmArrangeOrderData, err error) {
	var (
		fpmArrangeOrder model.FpmArrangeOrder
		itemDatas       model.FpmArrangeOrderItemList
		fineCodeList    model.FpmArrangeOrderItemFcList
		detailStockList model.StockProductDetailList
		warehousePB     = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB          = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dicSvc          = dic_pb.NewDictionaryClient()
	)
	if req.Id != 0 {
		fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, req.Id)
		if err != nil {
			return
		}
	} else {
		fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByOrderNo(r.tx, req.OrderNo)
		if err != nil {
			return
		}
	}

	o := structure.GetFpmArrangeOrderData{}
	r.swapListModel2Data(fpmArrangeOrder, &o, ctx)
	o.OrderTypeName = fmt.Sprintf("%s-%s", o.OutOrderTypeName, o.SendProductType.String())

	// 只需要主单数据
	if req.IsWithoutDetail {
		return o, nil
	}

	itemDatas, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, fpmArrangeOrder.Id)
	if err != nil {
		return
	}

	// 统计每个单位的总重量
	var (
		unitWeightMap = make(map[uint64]int)    // 已配重量
		unitPushMap   = make(map[uint64]int)    // 配布重量
		unitNameCache = make(map[uint64]string) // 缓存单位名称
	)

	for _, itemData := range itemDatas {
		// 统计重量
		isMainUnit := itemData.UnitId == itemData.AuxiliaryUnitId
		targetUnitId := itemData.AuxiliaryUnitId
		if isMainUnit {
			targetUnitId = itemData.UnitId
			unitPushMap[targetUnitId] += itemData.PushWeight
			unitWeightMap[targetUnitId] += itemData.ArrangeWeight
		} else {
			unitWeightMap[targetUnitId] += itemData.ArrangeLength
			unitPushMap[targetUnitId] += itemData.PushLength
		}
	}
	// 获取单位名称的函数
	getUnitName := func(unitId uint64) string {
		if name, exists := unitNameCache[unitId]; exists {
			return name
		}
		name, _ := unitPB.GetInfoBaseMeasurementUnitNameById(ctx, unitId)
		unitNameCache[unitId] = name
		return name
	}
	// 汇总重量的函数
	formatWeightMap := func(weightMap map[uint64]int) string {
		var results []string
		for unitId, weight := range weightMap {
			if weight > 0 {
				results = append(results,
					strconv.Itoa(weight/vars.Weight)+getUnitName(unitId))
			}
		}
		return strings.Join(results, "、")
	}
	// 判断订单类型
	if o.PushRoll != 0 {
		// 生成汇总字符串
		if o.TotalRoll != 0 {
			o.MergeArrangeWeight = fmt.Sprintf("%v%s%v", strconv.Itoa(o.PushRoll/vars.Roll), "匹 ", formatWeightMap(unitWeightMap)) // 已配数量
		}
		o.MergePushWeight = fmt.Sprintf("%v%s%v", strconv.Itoa(o.PushRoll/vars.Roll), "匹 ", formatWeightMap(unitPushMap))
	} else {
		// 生成汇总字符串
		o.MergeArrangeWeight = formatWeightMap(unitWeightMap)
		o.MergePushWeight = formatWeightMap(unitPushMap)
	}

	// 细码信息
	fineCodeList, err = mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	mUnitFcIds := mysql_base.GetUInt64List(fineCodeList, "measurement_unit_id")
	unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, mUnitFcIds)
	wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
	binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
	stockIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
	detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds)
	if err != nil {
		return
	}
	dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmArrangeOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmArrangeOrder.BusinessStatus <= common_product.BusinessStatusArrangeWaitOut {
			if itemData.DyeFactoryDyelotNumber != "" {
				stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
					StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
				for _, v := range stockList {
					// 库存信息,2023-12-20 需求1001412改为获取可用数量和匹数
					itemGetData.SumStockRoll = v.AvailableRoll
					itemGetData.SumStockWeight = v.AvailableWeight
					itemGetData.SumStockLength = v.Length
				}
			}
			// if itemData.DyeFactoryDyelotNumber == "" {
			// 	stock, _err := mysql.MustFirstStockProductByID(r.tx, itemData.SumStockId)
			// 	if _err != nil {
			// 		err = _err
			// 		return
			// 	}
			// 	itemGetData.SumStockRoll = stock.StockRoll - stock.BookRoll
			// 	itemGetData.SumStockWeight = stock.Weight - stock.BookWeight
			// 	itemGetData.SumStockLength = stock.Length
			// }
		}
		// 已扫数量的单位
		if itemData.AuxiliaryUnitId != itemData.UnitId {
			itemGetData.AuxiliaryUnitName = unitNameMap[itemData.AuxiliaryUnitId]
		}
		for _, fineCode := range fineCodeList.PickList(itemData.Id) {
			fineCodeGetData := structure.GetFpmArrangeOrderItemFcData{}
			fineCodeGetData.Id = fineCode.Id
			fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
			fineCodeGetData.CreatorId = fineCode.CreatorId
			fineCodeGetData.CreatorName = fineCode.CreatorName
			fineCodeGetData.UpdaterId = fineCode.UpdaterId
			fineCodeGetData.UpdateUserName = fineCode.UpdaterName
			fineCodeGetData.ParentId = fineCode.ParentId
			fineCodeGetData.Roll = fineCode.Roll
			fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
			fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
			fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
			fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
			fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
			fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
			fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
			fineCodeGetData.StockId = fineCode.StockId
			fineCodeGetData.SumStockId = fineCode.SumStockId
			fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
			fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
			fineCodeGetData.WeightError = fineCode.WeightError
			fineCodeGetData.Length = fineCode.Length
			fineCodeGetData.SettleWeight = fineCode.SettleWeight
			fineCodeGetData.DigitalCode = fineCode.DigitalCode
			fineCodeGetData.ShelfNo = fineCode.ShelfNo
			fineCodeGetData.ContractNumber = fineCode.ContractNumber
			fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
			fineCodeGetData.AccountNum = fineCode.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
			fineCodeGetData.StockRemark = fineCode.StockRemark
			fineCodeGetData.Remark = fineCode.Remark
			fineCodeGetData.InternalRemark = fineCode.InternalRemark
			fineCodeGetData.ScanUserId = fineCode.ScanUserId
			fineCodeGetData.ScanUserName = fineCode.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
			fineCodeGetData.WarehouseId = fineCode.WarehouseId
			fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight
			fineCodeGetData.AuxiliaryUnitId = itemData.AuxiliaryUnitId
			fineCodeGetData.AuxiliaryUnitName = unitNameMap[itemData.AuxiliaryUnitId]
			fineCodeGetData.UnitId = fineCode.UnitId
			fineCodeGetData.UnitName = unitNameMap[fineCode.UnitId]
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fineCode.WarehouseBinId]
			detailStock := detailStockList.Pick(fineCode.StockId)

			fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
				dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)
			o.TotalRoll += fineCode.Roll
			o.TotalWeight += fineCode.BaseUnitWeight
			o.TotalLength += fineCode.Length
			// r.swapFcModel2Data(fineCode, &fineCodeGetData, ctx)
			itemGetData.ItemFCData = append(itemGetData.ItemFCData, fineCodeGetData)
		}
		o.PushRoll += itemData.PushRoll
		o.PushWeight += itemData.PushWeight
		o.PushLength += itemData.PushLength
		if len(itemGetData.ItemFCData) == 0 {
			itemGetData.ItemFCData = make(structure.GetFpmArrangeOrderItemFcDataList, 0)
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}
	data = o
	return
}

// 获取销售送货单结构体
func (r *FpmArrangeOrderRepo) GetProductSaleShouldCollectOrderParam(ctx context.Context, id uint64) (addShouldCollectOrderItem *shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam, err error) {
	var (
		fpmArrangeOrder           model.FpmArrangeOrder
		fpmArrangeOrderItems      model.FpmArrangeOrderItemList
		fpmArrangeOrderItemFcList model.FpmArrangeOrderItemFcList
	)
	fpmArrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}

	fpmArrangeOrderItems, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}

	fpmArrangeOrderItemFcList, err = mysql.FindFpmArrangeOrderItemFcByParentID(r.tx, fpmArrangeOrderItems.GetIds())
	if err != nil {
		return
	}

	// 生成送货单参数,查找详情
	addRes := fpmArrangeOrder.ToAddProductSaleShouldCollectOrderParam(fpmArrangeOrderItems, fpmArrangeOrderItemFcList)

	addShouldCollectOrderItem = addRes
	return
}

func (r *FpmArrangeOrderRepo) GetList(ctx context.Context, req *structure.GetFpmArrangeOrderListQuery) (list structure.GetFpmArrangeOrderDataList, total int, err error) {
	var (
		orders    model.FpmArrangeOrderList
		itemDatas model.FpmArrangeOrderItemList
		// fineCodeList model.FpmArrangeOrderItemFcList
		bizPB       = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		emplPB      = empl_pb.NewClientEmployeeService()
	)
	orders, total, err = mysql.SearchFpmArrangeOrder(r.tx, req)
	if err != nil {
		return
	}

	itemDatas, err = mysql.FindFpmArrangeOrderItemByParenTIDs(r.tx, orders.GetIds())
	if err != nil {
		return
	}

	// fineCodeList, err = mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, itemDatas.GetIds())
	// if err != nil {
	// 	return
	// }

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orders, "unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		warehouseOutIds := mysql_base.GetUInt64List(orders, "arrange_to_warehouse_id")
		wareIds := tools.MergeSlicesUint64(warehouseIds, warehouseOutIds)
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, wareIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	// 获取单位名称的函数
	getUnitName := func(unitId uint64) string {
		name, _ := unitPB.GetInfoBaseMeasurementUnitNameById(ctx, unitId)
		if name == "" {
			return ""
		}
		return name
	}
	// 汇总重量的函数
	formatWeightMap := func(weightMap map[uint64]int) string {
		var results []string
		for unitId, weight := range weightMap {
			if weight > 0 {
				results = append(results,
					strconv.Itoa(weight/vars.Weight)+getUnitName(unitId))
			}
		}
		return strings.Join(results, "、")
	}
	list = make(structure.GetFpmArrangeOrderDataList, 0)
	for _, src := range orders.List() {
		var (
			colorMap   = make(map[uint64]int) // 统计色号
			ProductMap = make(map[uint64]int) // 统计面料
			dst        = structure.GetFpmArrangeOrderData{}
		)
		// 获取配布单详情
		arrangeOrderData, err := r.Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: src.Id})
		if err != nil {
			continue
		}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.SrcType = src.SrcType
		dst.SrcId = src.SrcId
		dst.SrcOrderNo = src.SrcOrderNo
		dst.OutOrderType = src.OutOrderType
		dst.SaleSystemId = src.SaleSystemId
		dst.ArrangeTime = tools.MyTime(src.ArrangeTime)
		dst.ArrangeToWarehouseId = src.ArrangeToWarehouseId
		dst.WarehouseId = src.WarehouseId
		dst.BizUnitId = src.BizUnitId
		dst.ProcessFactoryId = src.ProcessFactoryId
		dst.ReceiveAddr = src.ReceiveAddr
		dst.ReceivePhone = src.ReceivePhone
		dst.ReceiveTag = src.ReceiveTag
		dst.DriverId = src.DriverId
		dst.SaleUserId = src.SaleUserId
		dst.SaleFollowerId = src.SaleFollowerId
		dst.StoreKeeperId = src.StoreKeeperId
		dst.LogisticsCompanyId = src.LogisticsCompanyId
		dst.InternalRemark = src.InternalRemark
		dst.SaleRemark = src.SaleRemark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.BusinessStatus = src.BusinessStatus
		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.BusinessStatusName = src.BusinessStatus.String()
		dst.UnitName = unitNameMap[src.UnitId]
		dst.OutOrderTypeName = src.OutOrderType.String()
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.ArrangeToWarehouseName = wareNameMap[src.ArrangeToWarehouseId]
		dst.BizUnitName = bizNameMap[src.BizUnitId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.SaleModeName = src.SaleMode.String()
		dst.PickUpGoodsInOrder = src.PickUpGoodsInOrder
		// 统计每个单位的重量
		unitWeightMap := make(map[uint64]int) // 已配重量
		unitPushMap := make(map[uint64]int)   // 配布重量
		// 获取成品信息 统计面料
		for _, item := range arrangeOrderData.ItemData {
			_, ok := colorMap[item.ProductColorId]
			if !ok {
				// 不存在，添加
				colorMap[item.ProductColorId]++
			}
			_, exist := ProductMap[item.ProductId]
			if !exist {
				// 不存在，添加
				ProductMap[item.ProductId]++
			}
		}
		dst.SendProductType = src.SendProductType
		dst.ItemInfo = fmt.Sprintf("%d种面料,%d个颜色", len(ProductMap), len(colorMap))
		// 添加ItemData字段
		dst.ItemData = arrangeOrderData.ItemData

		for _, item := range itemDatas.PickByParentID(src.Id) {
			dst.PushRoll += item.PushRoll
			dst.PushWeight += item.PushWeight
			dst.PushLength += item.PushLength
			isMainUnit := item.UnitId == item.AuxiliaryUnitId
			targetUnitId := item.AuxiliaryUnitId
			if isMainUnit {
				targetUnitId = item.UnitId
				unitPushMap[targetUnitId] += item.PushWeight
				unitWeightMap[targetUnitId] += item.ArrangeWeight
			} else {
				unitWeightMap[targetUnitId] += item.ArrangeLength
				unitPushMap[targetUnitId] += item.PushLength
			}
		}
		// 判断订单类型
		if dst.PushRoll != 0 {
			// 生成汇总字符串
			if dst.TotalRoll != 0 {
				dst.MergeArrangeWeight = fmt.Sprintf("%v%s%v", strconv.Itoa(dst.PushRoll/vars.Roll), "匹 ", formatWeightMap(unitWeightMap)) // 已配数量
			}
			dst.MergePushWeight = fmt.Sprintf("%v%s%v", strconv.Itoa(dst.PushRoll/vars.Roll), "匹 ", formatWeightMap(unitPushMap))
		} else {
			// 生成汇总字符串
			dst.MergeArrangeWeight = formatWeightMap(unitWeightMap)
			dst.MergePushWeight = formatWeightMap(unitPushMap)
		}
		list = append(list, dst)
	}
	return
}

func (r *FpmArrangeOrderRepo) swapListModel2Data(src model.FpmArrangeOrder, dst *structure.GetFpmArrangeOrderData, ctx context.Context) {
	var (
		bizService  = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		userName    = make(map[uint64]string)
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		driverName  string
	)

	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId, src.ProcessFactoryId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleMap, err2 := saleSysPB.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	driverIds := tools.String2UintArray(src.DriverId, ",")
	if len(driverIds) != 0 {
		driverNameMap, _ := emplPB.GetEmployeeNameByIds(r.tx.Context, driverIds)
		driverName = tools.GetMapValAppend2String(driverNameMap, ",")
	}
	userName, _ = emplPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId, src.SaleUserId, src.SaleFollowerId})

	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseNameMap, _ := warehousePB.GetPhysicalWarehouseByIds(r.tx.Context, []uint64{src.WarehouseId, src.ArrangeToWarehouseId})

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.SrcType = src.SrcType
	dst.SrcId = src.SrcId
	dst.SrcOrderNo = src.SrcOrderNo
	dst.OutOrderType = src.OutOrderType
	dst.SaleSystemId = src.SaleSystemId
	dst.ArrangeTime = tools.MyTime(src.ArrangeTime)
	dst.ArrangeToWarehouseId = src.ArrangeToWarehouseId
	dst.WarehouseId = src.WarehouseId
	dst.BizUnitId = src.BizUnitId
	dst.ProcessFactoryId = src.ProcessFactoryId
	dst.ReceiveAddr = src.ReceiveAddr
	dst.ReceivePhone = src.ReceivePhone
	dst.ReceiveTag = src.ReceiveTag
	dst.DriverId = src.DriverId
	dst.SaleUserId = src.SaleUserId
	dst.SaleFollowerId = src.SaleFollowerId
	dst.StoreKeeperId = src.StoreKeeperId
	dst.LogisticsCompanyId = src.LogisticsCompanyId
	dst.InternalRemark = src.InternalRemark
	dst.SaleRemark = src.SaleRemark
	dst.SaleMode = src.SaleMode
	dst.SaleModeName = src.SaleMode.String()
	// dst.TotalRoll = src.TotalRoll
	// dst.TotalWeight = src.TotalWeight
	// dst.TotalLength = src.TotalLength
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.BusinessStatus = src.BusinessStatus
	dst.SameColorSameDyeLot = src.SameColorSameDyeLot
	dst.PickUpGoodsInOrder = src.PickUpGoodsInOrder
	if dst.SrcType == common_product.ArrangeOrderFromReservationOrder {
		if val, ok := userName[src.StoreKeeperId]; ok {
			dst.StoreKeeperName = val
			dst.SaleUserName = val
			dst.SaleFollowerName = val
		}
	} else {
		if val, ok := userName[src.StoreKeeperId]; ok {
			dst.StoreKeeperName = val
		}
		if val, ok := userName[src.SaleUserId]; ok {
			dst.SaleUserName = val
		}
		if val, ok := userName[src.SaleFollowerId]; ok {
			dst.SaleFollowerName = val
		}
	}

	// 销售用的
	dst.SendProductType = src.SendProductType
	dst.SaleGroupId = src.SaleGroupId
	dst.SettleType = src.SettleType
	dst.LogisticsArea = src.LogisticsArea
	dst.PostageItems = src.PostageItems
	dst.SendProductRemark = src.SendProductRemark
	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.BusinessStatusName = src.BusinessStatus.String()
	dst.UnitName = unitName
	dst.OutOrderTypeName = src.OutOrderType.String()
	dst.DriverName = driverName
	dst.LogisticsCompany = src.LogisticsCompany
	dst.ProcessFactory = src.ProcessFactory
	if val, ok := saleMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := warehouseNameMap[src.WarehouseId]; ok {
		dst.WarehouseName = val
	}
	if val, ok := warehouseNameMap[src.ArrangeToWarehouseId]; ok {
		dst.ArrangeToWarehouseName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.BizUnitName = val
	}
}

func (r *FpmArrangeOrderRepo) swapItemModel2Data(src model.FpmArrangeOrderItem, dst *structure.GetFpmArrangeOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
		productSvc = product.NewProductClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)
	getProduct, _, _ := mysql.FirstFinishProductByID(r.tx, src.ProductId)
	productMap, _ := productSvc.GetProductMapByIds(ctx, []uint64{src.ProductId})
	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.ProductId = src.ProductId
	dst.ProductColorId = src.ProductColorId
	dst.CustomerId = src.CustomerId
	dst.ProductLevelId = src.ProductLevelId
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ArrangeRoll = src.ArrangeRoll
	dst.SumStockId = src.SumStockId
	dst.ArrangeWeight = src.ArrangeWeight
	dst.WeightError = src.WeightError
	dst.ActuallyWeight = src.ActuallyWeight
	dst.PaperTubeWeight = src.PaperTubeWeight
	dst.SettleErrorWeight = src.SettleErrorWeight
	dst.SettleWeight = src.SettleWeight
	dst.UnitId = src.UnitId
	dst.AuxiliaryUnitId = src.AuxiliaryUnitId
	dst.ArrangeLength = src.ArrangeLength
	dst.Remark = src.Remark
	dst.QuoteOrderItemId = src.QuoteOrderItemId
	dst.QuoteOrderNo = src.QuoteOrderNo

	// dst.ProductIngredient = src.ProductIngredient
	// dst.ProductCraft = src.ProductCraft
	dst.ProductRemark = src.ProductRemark
	dst.SumStockRoll = src.SumStockRoll
	dst.SumStockLength = src.SumStockLength
	dst.SumStockWeight = src.SumStockWeight
	// dst.ProductGramWeight = src.ProductGramWeight
	// dst.ProductWidth = src.ProductWidth

	// 转义
	dst.UnitName = unitName
	if val, ok := customerMap[src.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductWidth = getProduct.FinishProductWidth
	dst.ProductGramWeight = getProduct.FinishProductGramWeight
	dst.ProductCraft = getProduct.FinishProductCraft
	dst.ProductIngredient = getProduct.FinishProductIngredient
	dst.ProductCode = getProduct.FinishProductCode
	dst.ProductName = getProduct.FinishProductName
	if product, ok := productMap[src.ProductId]; ok {
		dst.FinishProductWidthAndWightUnit.FinishProductWidthUnitId = product.FinishProductWidthUnitId
		dst.FinishProductWidthAndWightUnit.FinishProductWidthUnitName = product.FinishProductWidthUnitName
		dst.FinishProductWidthAndWightUnit.FinishProductWidthAndUnitName = product.FinishProductWidthAndUnitName
		dst.FinishProductWidthAndWightUnit.ProductGramWeight = product.ProductGramWeight
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitId = product.FinishProductGramWeightUnitId
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightAndUnitName = product.FinishProductGramWeightAndUnitName
		dst.FinishProductWidthAndWightUnit.ProductWidth = product.ProductWidth
		dst.FinishProductWidthAndWightUnit.FinishProductGramWeightUnitName = product.FinishProductGramWeightUnitName
	}
	dst.ProductColorCode = getColor.ProductColorCode
	dst.ProductColorName = getColor.ProductColorName
	dst.SalePlanOrderItemId = src.SalePlanOrderItemId

	// 下推信息
	dst.PushRoll = src.PushRoll
	dst.PushWeight = src.PushWeight
	dst.PushLength = src.PushLength
	dst.LengthUnitPrice = src.LengthCutSalePrice
}

func (r *FpmArrangeOrderRepo) swapFcModel2Data(fineCode model.FpmArrangeOrderItemFc, fineCodeGetData *structure.GetFpmArrangeOrderItemFcData, ctx context.Context) {

	var (
		warehousePB          = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB               = base_info_pb.NewInfoBaseMeasurementUnitClient()
		dictionaryDetailsSvc = dic_pb.NewDictionaryClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, fineCode.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, fineCode.UnitId)
	detailStockList, _ := mysql.FindStockProductDetailByIDs(r.tx, []uint64{fineCode.StockId})
	dicIds := mysql_base.GetUInt64List(detailStockList, "dictionary_detail_id")
	dicNameMap, _ := dictionaryDetailsSvc.GetDictionaryNameByIds(ctx, dicIds)

	fineCodeGetData.Id = fineCode.Id
	fineCodeGetData.CreateTime = tools.MyTime(fineCode.CreateTime)
	fineCodeGetData.UpdateTime = tools.MyTime(fineCode.UpdateTime)
	fineCodeGetData.CreatorId = fineCode.CreatorId
	fineCodeGetData.CreatorName = fineCode.CreatorName
	fineCodeGetData.UpdaterId = fineCode.UpdaterId
	fineCodeGetData.UpdateUserName = fineCode.UpdaterName
	fineCodeGetData.ParentId = fineCode.ParentId
	fineCodeGetData.Roll = fineCode.Roll
	fineCodeGetData.WarehouseBinId = fineCode.WarehouseBinId
	fineCodeGetData.VolumeNumber = fineCode.VolumeNumber
	fineCodeGetData.WarehouseOutType = fineCode.WarehouseOutType
	fineCodeGetData.WarehouseOutOrderId = fineCode.WarehouseOutOrderId
	fineCodeGetData.WarehouseOutOrderNo = fineCode.WarehouseOutOrderNo
	fineCodeGetData.WarehouseInType = fineCode.WarehouseInType
	fineCodeGetData.WarehouseInOrderId = fineCode.WarehouseInOrderId
	fineCodeGetData.WarehouseInOrderNo = fineCode.WarehouseInOrderNo
	fineCodeGetData.ArrangeOrderNo = fineCode.ArrangeOrderNo
	fineCodeGetData.StockId = fineCode.StockId
	fineCodeGetData.SumStockId = fineCode.SumStockId
	fineCodeGetData.BaseUnitWeight = fineCode.BaseUnitWeight
	fineCodeGetData.PaperTubeWeight = fineCode.PaperTubeWeight
	fineCodeGetData.WeightError = fineCode.WeightError
	fineCodeGetData.UnitId = fineCode.UnitId
	fineCodeGetData.Length = fineCode.Length
	fineCodeGetData.SettleWeight = fineCode.SettleWeight
	fineCodeGetData.DigitalCode = fineCode.DigitalCode
	fineCodeGetData.ShelfNo = fineCode.ShelfNo
	fineCodeGetData.ContractNumber = fineCode.ContractNumber
	fineCodeGetData.CustomerPoNum = fineCode.CustomerPoNum
	fineCodeGetData.AccountNum = fineCode.AccountNum
	fineCodeGetData.DyeFactoryColorCode = fineCode.DyeFactoryColorCode
	fineCodeGetData.DyeFactoryDyelotNumber = fineCode.DyeFactoryDyelotNumber
	detailStock := detailStockList.Pick(fineCode.StockId)
	fineCodeGetData.BuildFPResp(fineCode.ProductWidth, fineCode.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
		dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)
	fineCodeGetData.StockRemark = fineCode.StockRemark
	fineCodeGetData.Remark = fineCode.Remark
	fineCodeGetData.InternalRemark = fineCode.InternalRemark
	fineCodeGetData.ScanUserId = fineCode.ScanUserId
	fineCodeGetData.ScanUserName = fineCode.ScanUserName
	fineCodeGetData.ScanTime = tools.MyTime(fineCode.ScanTime)
	fineCodeGetData.WarehouseId = fineCode.WarehouseId
	fineCodeGetData.ActuallyWeight = fineCode.ActuallyWeight
	fineCodeGetData.SettleErrorWeight = fineCode.SettleErrorWeight

	// 转义
	fineCodeGetData.WarehouseBinName = binName
	fineCodeGetData.UnitName = unitName
}

func (r *FpmArrangeOrderRepo) ArrangeOutStockSwap(ctx context.Context, req structure.GetFpmArrangeOrderData) (data interface{}, err error) {

	if req.AuditStatus != common_system.OrderStatusAudited && req.BusinessStatus != common_product.BusinessStatusArrangeAllready {
		err = errors.ErrCodeOutStatusNoPassOrNoReady
		return
	}
	// 转义creat_id -> emp_id
	empID, _ := user_pb.NewUserClient().GetUserEmpIdById(ctx, req.CreatorId)

	switch req.OutOrderType {
	case common_product.WarehouseGoodOutTypeInternalAllocate: // 内部调拨出仓
		return r.swap2AllocateOutOrderParam(req), nil
	case common_product.WarehouseGoodOutTypeSaleAllocate: // 销售调拨出仓
		return r.swap2AddSaleAllocateOutParam(req), nil
	case common_product.WarehouseGoodOutTypeSale: // 销售出仓
		return r.swap2AddSaleOutParam(req, empID), nil
	case common_product.WarehouseGoodOutTypePurchaseReturn: // 采购退货出仓
		return r.swap2AddOutParam(req), nil
	case common_product.WarehouseGoodOutTypeProcess, common_product.WarehouseGoodOutTypeTypeRepair: // 加工出仓
		return r.swap2AddProcessOutParam(req), nil
	case common_product.WarehouseGoodOutTypeOther: // 其他出仓
		return r.swap2AddOutParam(req), nil
	case common_product.WarehouseGoodOutTypeDeduction: // 扣款出仓
		return r.swap2AddOutParam(req), nil
	}
	return
}

// 采购退货出仓单新增转换 // 其他出仓
func (r *FpmArrangeOrderRepo) swap2AddOutParam(req structure.GetFpmArrangeOrderData) structure.AddFpmOutOrderParam {
	var (
		param        = structure.AddFpmOutOrderParam{}
		srcIds       = make([]uint64, 0)
		priceMap     = make(map[uint64]int)
		orderPrefix  mysqlSystem.OrderPrefix
		exist        bool
		err          error
		sale_sys_svc = sale_sys_pb.NewSaleSystemClient()
		saleSysData  = sale_sys_pb.Res{}
	)
	// 在营销体系中获取一部分前缀
	param.Swap2AddPRTParam(req)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(context.Background(), sale_sys_pb.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return param
	}

	switch req.OutOrderType {
	case common_product.WarehouseGoodOutTypeDeduction:
		if !exist {
			param.OrderNoPre = vars.FpmDeductionOutOrderPrefix
			if vars.UseSaleSystem {
				param.OrderNoPre = fmt.Sprintf("%s-%s", param.OrderNoPre, saleSysData.Code)
			}
		} else {
			param.OrderNoPre = orderPrefix.FpmDeductionOutOrder
			if orderPrefix.UseSaleSystem {
				param.OrderNoPre = fmt.Sprintf("%s-%s", param.OrderNoPre, saleSysData.Code)
			}
		}
	case common_product.WarehouseGoodOutTypePurchaseReturn:
		if !exist {
			param.OrderNoPre = vars.FpmPurchaseReturnOutOrderPrefix
			if vars.UseSaleSystem {
				param.OrderNoPre = fmt.Sprintf("%s-%s", param.OrderNoPre, saleSysData.Code)
			}
		} else {
			param.OrderNoPre = orderPrefix.FpmPurchaseReturnOutOrder
			if orderPrefix.UseSaleSystem {
				param.OrderNoPre = fmt.Sprintf("%s-%s", param.OrderNoPre, saleSysData.Code)
			}
		}
		for _, item := range req.ItemData {
			srcIds = append(srcIds, item.QuoteOrderItemId)
		}
		// 找到那个出货单
		if len(srcIds) > 0 {
			srcItemData, _ := purchase_pb.NewPurchaseProductReturnOrderClient().GetPurchaseProductReturnItemByIds(context.Background(), srcIds)
			for _, srcItem := range srcItemData {
				priceMap[srcItem.Id] = srcItem.Price
			}

		}
	case common_product.WarehouseGoodOutTypeOther:
		if !exist {
			param.OrderNoPre = vars.FpmOtherOutOrderPrefix
			if vars.UseSaleSystem {
				param.OrderNoPre = fmt.Sprintf("%s-%s", param.OrderNoPre, saleSysData.Code)
			}
		} else {
			param.OrderNoPre = orderPrefix.FpmOtherOutOrder
			if orderPrefix.UseSaleSystem {
				param.OrderNoPre = fmt.Sprintf("%s-%s", param.OrderNoPre, saleSysData.Code)
			}
		}

	}

	for _, item := range req.ItemData {
		itemParam := structure.AddFpmOutOrderItemParam{}
		itemParam.Swap2AddPRTItemParam(item)
		for _, itemFc := range item.ItemFCData {
			itemFcParam := structure.AddFpmOutOrderItemFcParam{}
			itemFcParam.Swap2AddPRTItemFcParam(itemFc)
			itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
		}
		switch req.OutOrderType {
		case common_product.WarehouseGoodOutTypePurchaseReturn:
			itemParam.UnitPrice = priceMap[item.QuoteOrderItemId]
		}
		param.ItemData = append(param.ItemData, itemParam)
	}
	return param
}

// 销售出仓单新增转换
func (r *FpmArrangeOrderRepo) swap2AddSaleOutParam(req structure.GetFpmArrangeOrderData, arrangeUserId uint64) structure.AddFpmSaleOutOrderParam {
	param := structure.AddFpmSaleOutOrderParam{}
	param.Swap2AddSaleOutParam(req)
	param.ArrangeUserId = arrangeUserId
	// for _, item := range req.ItemData {
	// 	itemParam := structure.AddFpmOutOrderItemParam{}
	// 	itemParam.Swap2AddPRTItemParam(item)
	// 	for _, itemFc := range item.ItemFCData {
	// 		itemFcParam := structure.AddFpmOutOrderItemFcParam{}
	// 		itemFcParam.Swap2AddPRTItemFcParam(itemFc)
	// 		itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
	// 	}
	// 	param.ItemData = append(param.ItemData, itemParam)
	// }
	for _, item := range req.ItemData {
		dyelotNumberMap := make(map[string]structure.GetFpmArrangeOrderItemFcDataList, 0)
		for _, itemFc := range item.ItemFCData {
			dyelotNumberMap[itemFc.DyeFactoryDyelotNumber] = append(dyelotNumberMap[itemFc.DyeFactoryDyelotNumber], itemFc)
		}
		for dyelotNumber, fcList := range dyelotNumberMap {
			itemParam := structure.AddFpmOutOrderItemParam{}
			itemParam.Swap2AddPRTItemParam(item)
			itemParam.DyeFactoryDyelotNumber = dyelotNumber
			for _, fc := range fcList {
				itemFcParam := structure.AddFpmOutOrderItemFcParam{}
				itemFcParam.Swap2AddPRTItemFcParam(fc)
				itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
			}
			itemParam.OutRoll = len(itemParam.ItemFCData) * vars.Roll
			param.ItemData = append(param.ItemData, itemParam)
		}
	}
	return param
}

// 销售调拨出仓单新增转换
func (r *FpmArrangeOrderRepo) swap2AddSaleAllocateOutParam(req structure.GetFpmArrangeOrderData) structure.AddFpmSaleAllocateOutOrderParam {
	var (
		lcQuery = base_info_pb.GetInfoSaleLogisticsCompanyQuery{}
		lcData  = base_info_pb.GetInfoSaleLogisticsCompanyData{}
		client  = base_info_pb.NewInfoSaleLogisticsCompanyClient()
	)
	param := structure.AddFpmSaleAllocateOutOrderParam{}
	param.Swap2AddSaleAlocateOutParam(req)
	lcQuery.Id = param.LogisticsCompanyId
	lcData, _ = client.GetInfoSaleLogisticsCompanyById(r.tx.Context, &lcQuery)
	param.LogisticsCompanyArea = lcData.LogisticsArea
	// for _, item := range req.ItemData {
	// 	itemParam := structure.AddFpmOutOrderItemParam{}
	// 	itemParam.Swap2AddPRTItemParam(item)
	// 	for _, itemFc := range item.ItemFCData {
	// 		itemFcParam := structure.AddFpmOutOrderItemFcParam{}
	// 		itemFcParam.Swap2AddPRTItemFcParam(itemFc)
	// 		itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
	// 	}
	// 	param.ItemData = append(param.ItemData, itemParam)
	// }
	for _, item := range req.ItemData {
		dyelotNumberMap := make(map[string]structure.GetFpmArrangeOrderItemFcDataList, 0)
		for _, itemFc := range item.ItemFCData {
			dyelotNumberMap[itemFc.DyeFactoryDyelotNumber] = append(dyelotNumberMap[itemFc.DyeFactoryDyelotNumber], itemFc)
		}
		for dyelotNumber, fcList := range dyelotNumberMap {
			itemParam := structure.AddFpmOutOrderItemParam{}
			itemParam.Swap2AddPRTItemParam(item)
			itemParam.DyeFactoryDyelotNumber = dyelotNumber
			for _, fc := range fcList {
				itemFcParam := structure.AddFpmOutOrderItemFcParam{}
				itemFcParam.Swap2AddPRTItemFcParam(fc)
				itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
			}
			itemParam.OutRoll = len(itemParam.ItemFCData) * vars.Roll
			param.ItemData = append(param.ItemData, itemParam)
		}
	}
	// reflect.TypeOf(param)
	return param
}

// 内部调拨出仓单新增转换
func (r *FpmArrangeOrderRepo) swap2AllocateOutOrderParam(req structure.GetFpmArrangeOrderData) structure.AddFpmInternalAllocateOutOrderParam {
	param := structure.AddFpmInternalAllocateOutOrderParam{}
	param.Swap2AllocateOutOrderParam(req)
	for _, item := range req.ItemData {
		itemParam := structure.AddFpmOutOrderItemParam{}
		itemParam.Swap2AddPRTItemParam(item)
		for _, itemFc := range item.ItemFCData {
			itemFcParam := structure.AddFpmOutOrderItemFcParam{}
			itemFcParam.Swap2AddPRTItemFcParam(itemFc)
			itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
		}
		param.ItemData = append(param.ItemData, itemParam)
	}
	return param
}

// 加工出仓单赋值
func (r *FpmArrangeOrderRepo) swap2AddProcessOutParam(req structure.GetFpmArrangeOrderData) structure.AddFpmProcessOutOrderParam {
	param := structure.AddFpmProcessOutOrderParam{}
	param.Swap2ProcessOutOrderParam(req)
	for _, item := range req.ItemData {
		itemParam := structure.AddFpmProcessOutOrderItemParam{}
		itemParam.Swap2AddProcessItemParam(item)
		for _, itemFc := range item.ItemFCData {
			itemFcParam := structure.AddFpmOutOrderItemFcParam{}
			itemFcParam.Swap2AddPRTItemFcParam(itemFc)
			itemParam.ItemFCData = append(itemParam.ItemFCData, itemFcParam)
		}
		param.ItemData = append(param.ItemData, itemParam)
	}
	return param
}

// 判断库存是否足够并占用库存(占用时候只需要匹数判断)
func (r *FpmArrangeOrderRepo) JudgeStockIsEnough(ctx context.Context, req structure.GetFpmArrangeOrderData) (error, map[uint64]int) {
	var (
		stockDetailList = model.StockProductDetailList{}
		stockRollMap    = make(map[uint64]int)
		stockWeightMap  = make(map[uint64]int)
		stockLengthMap  = make(map[uint64]int)
		err             error
	)
	// 判断匹数和成品信息是否匹配
	for _, item := range req.ItemData {
		totalRoll := 0
		totalLength := 0
		fcIds := item.ItemFCData.GetIds()
		for _, fc := range item.ItemFCData {
			totalRoll += fc.Roll
			totalLength += fc.Length
			stockRollMap[fc.StockId] += fc.Roll
			stockWeightMap[fc.StockId] += fc.BaseUnitWeight
			stockLengthMap[fc.StockId] += fc.Length
		}
		if totalRoll != item.ArrangeRoll {
			return errors.NewCustomError(errors.ErrCodeFCNoEqualItemRoll, "错误行:"+item.ProductName), stockRollMap
		}
		if item.ArrangeLength > 0 && totalLength > 0 && item.ArrangeLength != totalLength {
			return errors.NewCustomError(errors.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName), stockRollMap
		}
		stockDetailList, err = mysql.FindStockProductDetailByIDs(r.tx, fcIds)
		if err != nil {
			return err, stockRollMap
		}
		for _, detail := range stockDetailList {
			if detail.Roll != stockRollMap[detail.Id] {
				return errors.ErrCodeOverStockRoll, stockRollMap
			}
		}
	}
	// 判断库存是否足够并占用
	// stockIds := tools.GetUint64MapKey(stockRollMap)
	return nil, stockRollMap
}

// 判断是否被被引用使用
func (r *FpmArrangeOrderRepo) JudgeIsQuoted(id uint64) (bool, structure.GetCreateByArrangeOrderSOutOrderData, error) {

	order, err := mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return false, structure.GetCreateByArrangeOrderSOutOrderData{}, err
	}

	switch order.OutOrderType {
	case common_product.WarehouseGoodOutTypeInternalAllocate: // 内部调拨出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	case common_product.WarehouseGoodOutTypeSaleAllocate: // 销售调拨出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	case common_product.WarehouseGoodOutTypeSale: // 销售出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	case common_product.WarehouseGoodOutTypePurchaseReturn: // 采购退货出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	case common_product.WarehouseGoodOutTypeProcess: // 加工出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	case common_product.WarehouseGoodOutTypeOther: // 其他出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	case common_product.WarehouseGoodOutTypeDeduction: // 扣款出仓
		return mysql.FindOutOrderByArrangeOrderId(r.tx, order.Id)
	}
	return false, structure.GetCreateByArrangeOrderSOutOrderData{}, nil
}

func (r *FpmArrangeOrderRepo) UpdateChangeData(ctx context.Context, order structure.GetFpmChangeArrangeOrderData) (
	err error,
	sumBookMap map[uint64]*structure.BookData,
	detailBookMap map[uint64]uint64,
) {
	var (
		arrangeOrder model.FpmArrangeOrder
		// arrangeOrderItem model.FpmArrangeOrderItem
		arrItemList model.FpmArrangeOrderItemList
		fcModelMap  = make(map[uint64]model.FpmArrangeOrderItemFc)
		// detailStockModel model.StockProductDetail
		sumBookMap2         = make(map[uint64]*structure.BookData)
		detailBookMap2      = make(map[uint64]uint64)
		isAlready      bool = true
		isHasFc        bool = false
	)
	arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, order.ArrangeOrderId)
	arrangeOrder.OutOrderType = order.AfterOutOrderType
	arrangeOrder.ArrangeToWarehouseId = order.AfterArrangeToWarehouseId
	arrangeOrder.BizUnitId = order.AfterBizUnitId

	// 成品信息以及细码操作处理
	arrItemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, order.ArrangeOrderId)
	arrItemIds := arrItemList.GetIds()

	// 删除细码
	fcList, err := mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, arrItemIds)
	if err != nil {
		return
	}
	var (
		stockDetailIdMap = set.NewUint64Set()
		stockSvc         = stock_product.NewProductStockClient()
		stockDetailMap   = make(map[uint64]stock_product.StockProductDetail)
	)
	for _, fc := range fcList {
		fcModelMap[fc.Id] = fc
		stockDetailIdMap.Add(fc.StockId)
	}

	// 找出细码对应的现有库存
	stockDetailMap, err = stockSvc.GetStockDetailByIds(ctx, stockDetailIdMap.List())
	if err != nil {
		return
	}

	// 变更成品信息
	// 更新细码（并且计算更新成品信息的值）
	for _, item := range order.ItemData {
		var (
			totalChangeRoll   = 0
			totalChangeWeight = 0
		)

		// if len(item.ItemFCData) == 0 {
		//	continue
		// }

		tempArrangeItem := arrItemList.Pick(item.ArrangeItemId)

		// 直接变更下推数量
		// if len(item.ItemFCData) == 0 {
		tempArrangeItem.PushRoll += item.ChangeRoll
		tempArrangeItem.PushWeight += item.ChangeWeight
		tempArrangeItem.PushLength += item.ChangeLength
		if tempArrangeItem.PushWeight < 0 || tempArrangeItem.PushRoll < 0 || tempArrangeItem.PushLength < 0 {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOverAbleChangeWeight, "超出可变更数量"))
			return
		}
		// 更新原item
		tempArrangeItem, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, tempArrangeItem)
		if err != nil {
			return
		}
		bookData := &structure.BookData{
			BookRoll:   item.ChangeRoll,
			BookWeight: item.ChangeWeight,
			ProductId:  item.ProductId,
			ColorId:    item.ProductColorId,
			OrderId:    order.Id,
			OrderNo:    order.OrderNo,
			OrderType:  common_system.BookOrderTypeArrangeChangePass,
		}
		sumBookMap2[item.SumStockId] = bookData
		// continue
		// }

		// 扣细码
		for _, fc := range item.ItemFCData {
			fcModel := fcModelMap[fc.SrcId]
			if fcModel.Id == 0 {
				err = errors.NewCustomError(errors.ErrCodeMysqlUpdate, "找不到对应的细码")
				return
			}
			getChangeRoll := fc.ItemFCChangeData.GetChangeRoll()
			getChengeWeight := fc.ItemFCChangeData.GetChangeWeight()
			totalChangeRoll += getChangeRoll
			totalChangeWeight += getChengeWeight
			if getChengeWeight+fcModel.BaseUnitWeight < 0 {
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOverAbleChangeWeight,
					fmt.Sprintf("变更数量 %v ,原数量 %v", float64(getChengeWeight)/vars.Weight, float64(fc.BaseUnitWeight)/vars.Weight)))
				return
			}

			for _, fcChange := range fc.ItemFCChangeData {
				// 找到原来的配布单细码
				var arrangeOrderItemFc model.FpmArrangeOrderItemFc
				arrangeOrderItemFc = fcModel
				arrangeOrderItemFc.SwapChange2ModelfcChange(ctx, fcChange)
				arrangeOrderItemFc.ActuallyWeight = arrangeOrderItemFc.BaseUnitWeight - arrangeOrderItemFc.WeightError
				arrangeOrderItemFc.SettleWeight = arrangeOrderItemFc.ActuallyWeight - arrangeOrderItemFc.SettleErrorWeight
				arrangeOrderItemFc, err = mysql.MustUpdateFpmArrangeOrderItemFc(r.tx, arrangeOrderItemFc)
				if err != nil {
					return
				}

				// 判断最终匹数和数量和长度不能超过库存里的数
				stockDetail := stockDetailMap[arrangeOrderItemFc.StockId]
				if arrangeOrderItemFc.Roll > stockDetail.StockRoll ||
					arrangeOrderItemFc.BaseUnitWeight > stockDetail.Weight ||
					arrangeOrderItemFc.Length > stockDetail.Length {
					err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOverAbleChangeWeight,
						fmt.Sprintf("%v%v", "卷号", arrangeOrderItemFc.VolumeNumber)))
				}
				// 每一条分录行生成新的一条细码
				tempArrangeItem.ArrangeRoll += fcChange.ChangeRoll
				tempArrangeItem.ArrangeWeight += fcChange.ChangeWeight
				tempArrangeItem.ArrangeLength += fcChange.ChangeLength
				tempArrangeItem.ActuallyWeight += fcChange.ChangeWeight
				tempArrangeItem.SettleWeight += fcChange.ChangeWeight
				arrangeOrder.TotalRoll += item.ChangeRoll
				arrangeOrder.TotalWeight += item.ChangeWeight
				arrangeOrder.TotalLength += item.ChangeLength
				if err != nil {
					return
				}
				// 细码数量变更为0或者选择剔除配布则取消细码库存占用
				if fcChange.ChangeWeight+fcModel.BaseUnitWeight == 0 || fcChange.IsNotOut {
					detailBookMap2[fc.StockId] = fc.Id
					// 更新原本的细码匹数和数量
					arrangeOrderItemFc.Roll = 0
					arrangeOrderItemFc.BaseUnitWeight = 0
					arrangeOrderItemFc, err = mysql.MustUpdateFpmArrangeOrderItemFc(r.tx, arrangeOrderItemFc)
					if err != nil {
						return
					}
				}
			}
		}

		// 细码总数之和不能大于详情的总数之和
		if tempArrangeItem.ArrangeRoll > item.ResultRoll {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "细码匹数与最终匹数不相等，"+
				fmt.Sprintf("变更匹数 %v ,原匹数 %v", float64(totalChangeRoll)/vars.Roll, float64(item.ChangeRoll)/vars.Roll)))
			return
		}
		// book, ok := sumBookMap2[item.SumStockId]
		// if !ok {
		// 	book = &structure.BookData{}
		// }
		// book.BookRoll += item.ChangeRoll
		// book.BookWeight += item.ChangeWeight
		// book.ProductId = item.ProductId
		// book.ColorId = item.ProductColorId
		// book.OrderId = item.ParentId
		// book.OrderNo = item.ParentOrderNo
		// sumBookMap2[item.SumStockId] = book
		// 更新原item
		tempArrangeItem, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, tempArrangeItem)
		if err != nil {
			return
		}

		// 只要其中一条成品信息的出仓匹数或者出仓数量大于0都是配布中
		if tempArrangeItem.ArrangeRoll > 0 || tempArrangeItem.ArrangeWeight > 0 {
			isHasFc = true
		}
		// 其中一条成品信息的出仓匹数小于最终下推匹数则未配布完成
		if tempArrangeItem.ArrangeRoll < item.ResultRoll {
			isAlready = false
		}
	}
	if isAlready {
		arrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeAllready
	} else if isHasFc {
		arrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeInProgress
	} else {
		arrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeWait
	}
	arrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, arrangeOrder)
	if err != nil {
		return
	}
	// }
	detailBookMap = detailBookMap2
	sumBookMap = sumBookMap2
	return
}

func (r *FpmArrangeOrderRepo) UpdateChangeDataWaitV2(ctx context.Context, order structure.GetFpmChangeArrangeOrderData) (
	err error,
	sumBookMap map[uint64]*structure.BookData,
	detailBookMap map[uint64]uint64,
) {
	var (
		arrangeOrder model.FpmArrangeOrder
		// arrangeOrderItem model.FpmArrangeOrderItem
		arrItemList model.FpmArrangeOrderItemList
		fcModelMap  = make(map[uint64]model.FpmArrangeOrderItemFc)
		// detailStockModel model.StockProductDetail
		sumBookMap2         = make(map[uint64]*structure.BookData)
		detailBookMap2      = make(map[uint64]uint64)
		isAlready      bool = true
		isHasFc        bool = false
	)
	arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, order.ArrangeOrderId)
	arrangeOrder.OutOrderType = order.AfterOutOrderType
	arrangeOrder.ArrangeToWarehouseId = order.AfterArrangeToWarehouseId
	arrangeOrder.BizUnitId = order.AfterBizUnitId

	// 成品信息以及细码操作处理
	arrItemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, order.ArrangeOrderId)
	arrItemIds := arrItemList.GetIds()

	// 删除细码
	fcList, err := mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, arrItemIds)
	if err != nil {
		return
	}
	for _, fc := range fcList {
		fcModelMap[fc.Id] = fc
	}

	// 变更成品信息
	// 更新细码（并且计算更新成品信息的值）
	for _, item := range order.ItemData {

		// if len(item.ItemFCData) == 0 {
		//	continue
		// }

		tempArrangeItem := arrItemList.Pick(item.ArrangeItemId)

		// if len(item.ItemFCData) == 0 {
		tempArrangeItem.PushRoll -= item.ChangeRoll
		tempArrangeItem.PushWeight -= item.ChangeWeight
		tempArrangeItem.PushLength -= item.ChangeLength
		if tempArrangeItem.PushWeight < 0 || tempArrangeItem.PushRoll < 0 || tempArrangeItem.PushLength < 0 {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeOverAbleChangeWeight, "超出可变更数量"))
			return
		}
		// 更新原item
		tempArrangeItem, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, tempArrangeItem)
		if err != nil {
			return
		}
		bookData := &structure.BookData{
			BookRoll:   -item.ChangeRoll,
			BookWeight: -item.ChangeWeight,
			ProductId:  -item.ProductId,
			ColorId:    item.ProductColorId,
			OrderId:    order.Id,
			OrderNo:    order.OrderNo,
			OrderType:  common_system.BookOrderTypeArrangeChangeWait,
		}
		sumBookMap2[item.SumStockId] = bookData
		// 	continue
		// }

		// 扣细码
		for _, fc := range item.ItemFCData {
			for _, fcChange := range fc.ItemFCChangeData {
				// 找到原来的配布单细码
				var arrangeOrderItemFc model.FpmArrangeOrderItemFc
				if val, ok := fcModelMap[fc.SrcId]; ok {
					arrangeOrderItemFc = val
					arrangeOrderItemFc.SwapChange2ModelfcChangeWait(ctx, fcChange)
					arrangeOrderItemFc.ActuallyWeight = arrangeOrderItemFc.BaseUnitWeight - arrangeOrderItemFc.WeightError
					arrangeOrderItemFc.SettleWeight = arrangeOrderItemFc.ActuallyWeight - arrangeOrderItemFc.SettleErrorWeight
					arrangeOrderItemFc, err = mysql.MustUpdateFpmArrangeOrderItemFc(r.tx, arrangeOrderItemFc)
					if err != nil {
						return
					}
				} else {
					detailBookMap2[fc.StockId] = fc.Id
					// 更新原本的细码匹数和数量
					arrangeOrderItemFc.Roll = fcChange.ChangeRoll
					arrangeOrderItemFc.BaseUnitWeight = fcChange.ChangeWeight
					arrangeOrderItemFc, err = mysql.MustUpdateFpmArrangeOrderItemFc(r.tx, arrangeOrderItemFc)
					if err != nil {
						return
					}
				}
				// 每一条分录行生成新的一条细码
				tempArrangeItem.ArrangeRoll -= fcChange.ChangeRoll
				tempArrangeItem.ArrangeWeight -= fcChange.ChangeWeight
				tempArrangeItem.ArrangeLength -= fcChange.ChangeLength
				tempArrangeItem.ActuallyWeight -= fcChange.ChangeWeight
				tempArrangeItem.SettleWeight -= fcChange.ChangeWeight
				arrangeOrder.TotalRoll -= item.ChangeRoll
				arrangeOrder.TotalWeight -= item.ChangeWeight
				arrangeOrder.TotalLength -= item.ChangeLength
				if err != nil {
					return
				}
			}
		}
		// book, ok := sumBookMap2[item.SumStockId]
		// if !ok {
		// 	book = &structure.BookData{}
		// }
		// book.BookRoll -= item.ChangeRoll
		// book.BookWeight -= item.ChangeWeight
		// book.ProductId = item.ProductId
		// book.ColorId = item.ProductColorId
		// book.OrderId = item.ParentId
		// book.OrderNo = item.ParentOrderNo
		// sumBookMap2[item.SumStockId] = book
		// 更新原item
		tempArrangeItem, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, tempArrangeItem)
		if err != nil {
			return
		}

		// 只要其中一条成品信息的出仓匹数或者出仓数量大于0都是配布中
		if tempArrangeItem.ArrangeRoll > 0 || tempArrangeItem.ArrangeWeight > 0 {
			isHasFc = true
		}
		// 其中一条成品信息的出仓匹数小于最终下推匹数则未配布完成
		if tempArrangeItem.ArrangeRoll < item.ResultRoll {
			isAlready = false
		}
	}
	if isAlready {
		arrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeAllready
	} else if isHasFc {
		arrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeInProgress
	} else {
		arrangeOrder.BusinessStatus = common_product.BusinessStatusArrangeWait
	}
	arrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, arrangeOrder)
	if err != nil {
		return
	}
	// }
	detailBookMap = detailBookMap2
	sumBookMap = sumBookMap2
	return
}

func (r *FpmArrangeOrderRepo) UpdateChangeDataWait(ctx context.Context, order structure.GetFpmChangeArrangeOrderData) (
	err error,
	sumBookMap map[uint64]*structure.BookData,
	detailBookMap map[uint64]int,
) {
	var (
		arrangeOrder     model.FpmArrangeOrder
		itemData         model.FpmArrangeOrderItem
		arrItemList      model.FpmArrangeOrderItemList
		fcModel          model.FpmArrangeOrderItemFc
		detailStockModel model.StockProductDetail
		isBookDetail     bool
		fcModelMap       = make(map[uint64]model.FpmArrangeOrderItemFc)
		sumBookMap2      = make(map[uint64]*structure.BookData)
		detailBookMap2   = make(map[uint64]int)
		changeFcIds      = set.NewUint64Set()

		changeFcIdArrangeFcIdMap = make(map[uint64]uint64)
	)
	// 配布单获取并恢复成变更前数据更新
	arrangeOrder, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, order.ArrangeOrderId)
	arrangeOrder.OutOrderType = order.BeforeOutOrderType
	arrangeOrder.ArrangeToWarehouseId = order.BeforeArrangeToWarehouseId
	arrangeOrder.BizUnitId = order.BeforeBizUnitId
	arrangeOrder, err = mysql.MustUpdateFpmArrangeOrder(r.tx, arrangeOrder)
	if err != nil {
		return
	}

	// 成品信息以及细码操作处理
	arrItemList, err = mysql.FindFpmArrangeOrderItemByParenTID(r.tx, order.ArrangeOrderId)
	arrItemIds := arrItemList.GetIds()

	// 删除细码
	fcList, err := mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, arrItemIds)
	if err != nil {
		return
	}

	for _, fc := range fcList {
		fcModelMap[fc.Id] = fc
	}

	// 已经点击确认出仓了
	if arrangeOrder.BusinessStatus > common_product.BusinessStatusArrangeAllready {
		isBookDetail = true
	}

	// 只变更成品信息
	if arrangeOrder.BusinessStatus == common_product.BusinessStatusArrangeWait {
		for _, item := range order.ItemData {
			itemData, err = mysql.MustFirstFpmArrangeOrderItemByID(r.tx, item.ArrangeItemId)
			if err != nil {
				return
			}
			// 将变更信息复原
			itemData.ArrangeRoll = item.ArrangeRoll
			itemData.ArrangeWeight = item.ArrangeWeight
			itemData.ArrangeWeight = item.ArrangeWeight
			itemData, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, itemData)
			if err != nil {
				return
			}
			// 减少还是增加了库存（由于ChangeRoll是负数，实际上执行的是+）
			bookList, ok := sumBookMap2[itemData.SumStockId]
			if !ok {
				bookList = &structure.BookData{}
			}
			bookList.BookRoll -= item.ChangeRoll
			bookList.BookWeight -= item.ChangeWeight
			sumBookMap2[itemData.SumStockId] = bookList
		}
	} else {
		// 更新细码（并且计算更新成品信息的值）
		for _, item := range order.ItemData {
			tempArrangeItem := arrItemList.Pick(item.ArrangeItemId)
			// 变更细码
			for _, fc := range item.ItemFCData {
				getChangeRoll := fc.ItemFCChangeData.GetChangeRoll()
				// 没有变更操作，直接生成细码
				if len(fc.ItemFCChangeData) == 0 {
					fcModel.SwapChange2Model(ctx, fc)
					fcModel, err = mysql.MustCreateFpmArrangeOrderItemFc(r.tx, fcModel)
					if err != nil {
						return
					}
				}
				// 审核时候相当于删除了这条细码（扣光了），现在需要加回去
				if getChangeRoll+fc.Roll == 0 {
					if isBookDetail {
						detailStockModel, err = mysql.MustFirstStockProductDetailByID(r.tx, fc.StockId)
						if err != nil {
							return
						}
						if getChangeRoll+detailStockModel.Roll == 0 {
							// 相当于获取到审核时更新了详细库存的状态（审核时候：配布中->入库；消审：入库->配布中）
							detailBookMap2[fc.StockId] += getChangeRoll
						}
					}
					// continue
				}

				for _, fcChange := range fc.ItemFCChangeData {
					// 找到原来的配布单细码
					var arrangeOrderItemFc model.FpmArrangeOrderItemFc
					if val, ok := fcModelMap[fc.SrcId]; ok {
						arrangeOrderItemFc = val
						arrangeOrderItemFc.SwapChange2ModelfcChangeWait(ctx, fcChange)
						arrangeOrderItemFc.ActuallyWeight = arrangeOrderItemFc.BaseUnitWeight - arrangeOrderItemFc.WeightError
						arrangeOrderItemFc.SettleWeight = arrangeOrderItemFc.ActuallyWeight - arrangeOrderItemFc.SettleErrorWeight
						fcModel, err = mysql.MustUpdateFpmArrangeOrderItemFc(r.tx, arrangeOrderItemFc)
						if err != nil {
							return
						}
					} else {
						err = errors.NewCustomError(errors.ErrCodeMysqlUpdate, "找不到对应的细码")
						return
					}
					// 每一条分录行生成新的一条细码
					tempArrangeItem.ArrangeRoll -= fcChange.ChangeRoll
					tempArrangeItem.ArrangeWeight -= fcChange.ChangeWeight
					tempArrangeItem.ArrangeLength -= fcChange.ChangeLength
					tempArrangeItem.ActuallyWeight -= fcChange.ChangeWeight
					tempArrangeItem.SettleWeight -= fcChange.ChangeWeight
					fcModel.ParentId = arrangeOrderItemFc.ParentId
					if err != nil {
						return
					}
					changeFcIds.Add(fcChange.Id)
					changeFcIdArrangeFcIdMap[fcChange.Id] = fcModel.Id
				}
			}
			// 更新原item
			tempArrangeItem, err = mysql.MustUpdateFpmArrangeOrderItem(r.tx, tempArrangeItem)
			if err != nil {
				return
			}
			// 减少还是增加了库存（由于ChangeRoll是负数，实际上执行的是+）
			book, ok := sumBookMap2[itemData.SumStockId]
			if !ok {
				book = &structure.BookData{}
			}
			book.BookRoll -= item.ChangeRoll
			book.BookWeight -= item.ChangeWeight
			book.ProductId = item.ProductId
			book.ColorId = item.ProductColorId
			book.OrderId = item.ParentId
			book.OrderNo = item.ParentOrderNo
			sumBookMap2[itemData.SumStockId] = book
		}
	}

	detailBookMap = detailBookMap2
	sumBookMap = sumBookMap2
	return
}

func (r *FpmArrangeOrderRepo) ChangeBusinessStatus(ctx context.Context, id uint64) (err error) {
	var (
		order model.FpmArrangeOrder
	)
	order, err = mysql.MustFirstFpmArrangeOrderByID(r.tx, id)
	if err != nil {
		return
	}
	order.BusinessStatus = common_product.BusinessStatusArrangeAllready
	order, err = mysql.MustUpdateFpmArrangeOrder(r.tx, order)
	if err != nil {
		return
	}
	return
}

func (r *FpmArrangeOrderRepo) IsFromPurchaseProductOrder(ctx context.Context, req structure.GetFpmArrangeOrderListQuery) error {

	req.JudgeStatus = true
	_, count, err := mysql.SearchFpmArrangeOrderCrossRvt(r.tx, &req)
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, "操作失败,请先取消对应配布单")
	}
	return nil
}

func (r *FpmArrangeOrderRepo) GetOutData(ctx context.Context, tx *mysql_base.Tx, id uint64) (
	outMap map[uint64][2]int, err error) {
	var (
		_outMap = make(map[uint64][2]int)
	)

	itemList, err := mysql.FindFpmArrangeOrderItemByParenTID(tx, id)
	if err != nil {
		return
	}
	for _, item := range itemList {
		_outMap[item.QuoteOrderItemId] = [2]int{item.ArrangeRoll, item.ArrangeWeight}
	}
	outMap = _outMap
	return
}

func (r *FpmArrangeOrderRepo) UpdateOrderByChange(ctx context.Context, arrangeOrderId uint64) (err error) {

	var (
		tureIds                             = make([]uint64, 0)
		totalRoll, totalWeight, totalLength int
	)

	changeOrderList, err := mysql.FindFpmChangeArrangeOrderByParenTID(r.tx, arrangeOrderId)
	if err != nil {
		return
	}

	for _, order := range changeOrderList {
		if order.AuditStatus != common_system.OrderStatusAudited {
			tureIds = append(tureIds, order.Id)
		}
	}

	if len(tureIds) == 0 {
		return
	}

	// 找这张单的成品信息
	arrangeItemList, err := mysql.FindFpmArrangeOrderItemByParenTID(r.tx, arrangeOrderId)
	if err != nil {
		return
	}
	arrangeItemFcList, err := mysql.FindFpmArrangeOrderItemFcByParenTIDs(r.tx, arrangeItemList.GetIds())
	if err != nil {
		return
	}

	changeItemList, err := mysql.FindFpmChangeArrangeOrderItemByParenTIDs(r.tx, tureIds)
	if err != nil {
		return
	}
	changeItemFcList, err := mysql.FindFpmChangeArrangeOrderItemFcByParentIDs(r.tx, changeItemList.GetIds())
	if err != nil {
		return
	}
	for _, order := range changeOrderList {
		if order.AuditStatus == common_system.OrderStatusAudited {
			continue
		}
		totalRoll, totalWeight, totalLength = 0, 0, 0
		tmpChangeItemList := changeItemList.PickList(order.Id)
		for _, item := range tmpChangeItemList {

			arrangeItem := arrangeItemList.Pick(item.ArrangeItemId)
			tmpChangeItemFcList := changeItemFcList.PickList(item.ParentId)

			item.ArrangeRoll = arrangeItem.ArrangeRoll
			item.SumStockId = arrangeItem.SumStockId
			item.ArrangeWeight = arrangeItem.ArrangeWeight
			item.ArrangeLength = arrangeItem.ArrangeLength
			item.WeightError = arrangeItem.WeightError
			item.ActuallyWeight = arrangeItem.ActuallyWeight
			item.PaperTubeWeight = arrangeItem.PaperTubeWeight
			item.SettleErrorWeight = arrangeItem.SettleErrorWeight
			item.SettleWeight = arrangeItem.SettleWeight
			// 存在细码为空的情况，但是仍然需要写入最终匹数和数量，所以需要注释掉
			// if len(tmpChangeItemFcList) > 0 {
			item.ResultRoll = arrangeItem.PushRoll + item.ChangeRoll
			item.ResultWeight = arrangeItem.PushWeight + item.ChangeWeight
			item.ResultLength = arrangeItem.PushLength + item.ChangeLength
			// }
			totalRoll += item.ResultRoll
			totalWeight += item.ResultWeight
			totalLength += item.ResultLength

			item, err = mysql.MustUpdateFpmChangeArrangeOrderItem(r.tx, item)
			if err != nil {
				return
			}

			for _, fc := range tmpChangeItemFcList {
				arrangeItemFc := arrangeItemFcList.Pick(fc.SrcId)
				fc.Roll = arrangeItemFc.Roll
				fc.BaseUnitWeight = arrangeItemFc.BaseUnitWeight
				fc.PaperTubeWeight = arrangeItemFc.PaperTubeWeight
				fc.WeightError = arrangeItemFc.WeightError
				fc.ActuallyWeight = arrangeItemFc.ActuallyWeight
				fc.SettleErrorWeight = arrangeItemFc.SettleErrorWeight
				fc.UnitId = arrangeItemFc.UnitId
				fc.Length = arrangeItemFc.Length
				fc.SettleWeight = arrangeItemFc.SettleWeight
				fc, err = mysql.MustUpdateFpmChangeArrangeOrderItemFc(r.tx, fc)
				if err != nil {
					return
				}
			}
		}
		order.TotalRoll = totalRoll
		order.TotalWeight = totalWeight
		order.TotalLength = totalLength
		order, err = mysql.MustUpdateFpmChangeArrangeOrder(r.tx, order)
		if err != nil {
			return
		}
	}
	return
}
