package product

import (
	"hcscm/server/finish_product_order"
	web "hcscm/server/product"
	"hcscm/server/system"
)

func InitFinishProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		finishProduct := product.Group("finishProduct")
		finishProduct.POSTNeedAuth("新增", "addFinishProduct", web.AddFinishProduct)
		finishProduct.PUTNeedAuth("更新", "updateFinishProduct", web.UpdateFinishProduct)
		finishProduct.PUTNeedAuth("更新状态", "updateFinishProductStatus", web.UpdateFinishProductStatus)
		finishProduct.DELETENeedAuth("删除", "deleteFinishProduct", web.DeleteFinishProduct)
		finishProduct.GET("获取", "getFinishProduct", web.GetFinishProduct)
		finishProduct.GET("获取", "getFinishProductEnum", web.GetFinishProductEnum)
		finishProduct.GET("获取列表", "getFinishProductList", web.GetFinishProductList)
		finishProduct.GET("获取列表(只获取一些字段,填编号时候用)", "searchForSomeProductField", web.SearchForSomeProductField)
		finishProduct.GET("获取下拉列表(可用)", "getFinishProductDropdownList", web.GetFinishProductDropdownList)
		finishProduct.GET("获取种类成品可用下拉列表(成品颜色列表使用)", "getKindAndProductList", web.GetKindAndProductList)
		finishProduct.GET("搜索相似图片", "search_image", web.SearchImageByUrl)
	}
	{
		finishProductColor := product.Group("finishProductColor")
		finishProductColor.POSTNeedAuth("新增", "addFinishProductColor", web.AddFinishProductColor)
		finishProductColor.POSTNeedAuth("快捷添加染厂颜色", "addQuickProductDyeColor", web.AddQuickProductDyeColor)
		finishProductColor.PUTNeedAuth("更新", "updateFinishProductColor", web.UpdateFinishProductColor)
		finishProductColor.PUTNeedAuth("更新状态", "updateFinishProductColorStatus", web.UpdateFinishProductColorStatus)
		finishProductColor.DELETENeedAuth("删除", "deleteFinishProductColor", web.DeleteFinishProductColor)
		finishProductColor.GET("获取", "getFinishProductColor", web.GetFinishProductColor)
		finishProductColor.GET("获取列表", "getFinishProductColorList", web.GetFinishProductColorList)
		finishProductColor.GET("获取下拉列表(可用)", "getFinishProductColorDropdownList", web.GetFinishProductColorDropdownList)
		finishProductColor.GET("根据成品颜色id获取复合布信息", "getProductCompositeDetails", web.GetProductCompositeDetails)
		finishProductColor.GET("根据成品颜色id获取染厂颜色信息", "getProductDyeingColorDetails", web.GetFinishProductDyeingColorDetails)
		finishProductColor.GET("根据成品id获取染厂颜色信息", "getDyeingColorDetailsByProduct", web.GetDyeingColorDetailsByProduct)
		finishProductColor.GET("染厂颜色枚举", "getDyeingColorDetailsByProductListEnum", web.GetDyeingColorDetailsByProductListEnum)
		finishProductColor.GET("根据成品id获取颜色类别列表", "getProductColorKindByProductId", web.GetProductColorKindByProductId)
		finishProductColor.GET("获取下拉列表(填编号的时候用)", "searchSomeFinishProductColorField", web.SearchSomeFinishProductColorField)
	}
}

func MPInitFinishProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		finishProduct := product.Group("finishProduct")
		finishProduct.POST("新增", "addFinishProduct", web.AddQuickFinishProduct)
		finishProduct.GET("获取下拉列表(可用)", "getFinishProductDropdownList", web.MPGetFinishProductDropdownList)
		finishProduct.GET("获取下拉列表(可用)", "getMPFinishProductDropdownList", web.GetFinishProductDropdownList)
		finishProduct.GET("获取", "getFinishProduct", web.GetFinishProduct)
		finishProduct.GET("成品采购订单项列表", "/finish_product/item_list", finish_product_order.GetFinishProductPurchaseOrderItemList)
		finishProduct.GET("搜索相似图片", "search_image", web.SearchImageByUrl)
	}
	{
		finishProductColor := product.Group("finishProductColor")
		finishProductColor.POST("快捷添加颜色", "addQuickProductColor", web.AddQuickFinishProductColor)
		finishProductColor.GET("获取下拉列表(可用)", "getFinishProductColorDropdownList", web.MPGetFinishProductColorDropdownList)
	}
}

func H5InitFinishProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		finishProduct := product.Group("finishProduct")
		finishProduct.POST("新增", "addFinishProduct", web.AddQuickFinishProduct)
		finishProduct.GET("获取下拉列表(可用)", "getFinishProductDropdownList", web.MPGetFinishProductDropdownList)
		finishProduct.GET("获取下拉列表(可用)", "getMPFinishProductDropdownList", web.GetFinishProductDropdownList)
		finishProduct.GET("获取", "getFinishProduct", web.GetFinishProduct)
		finishProduct.GET("成品采购订单项列表", "/finish_product/item_list", finish_product_order.GetFinishProductPurchaseOrderItemList)
		finishProduct.GET("搜索相似图片", "search_image", web.SearchImageByUrl)
	}
	{
		finishProductColor := product.Group("finishProductColor")
		finishProductColor.POST("快捷添加颜色", "addQuickProductColor", web.AddQuickFinishProductColor)
		finishProductColor.GET("获取下拉列表(可用)", "getFinishProductColorDropdownList", web.MPGetFinishProductColorDropdownList)
		finishProductColor.GET("获取下拉列表(填编号的时候用)", "searchSomeFinishProductColorField", web.SearchSomeFinishProductColorField)
	}
}

func ThirdPartyInitFinishProduct(routerGroup *system.RouterGroup) {
	product := routerGroup.Group("product")
	{
		finishProduct := product.Group("finishProduct")
		finishProduct.GET("搜索相似图片", "search_image", web.SearchImageByUrl)
	}
}
