package msg_consume

import (
	"encoding/json"
	"fmt"
	common_system "hcscm/common/system_consts"
	"hcscm/config"
	"hcscm/extern/rabbitmq"
	"hcscm/tools"
	"log"
	"sync"
)

var (
	consumeOncePool     sync.Once
	instanceConsumePool *rabbitmq.RabbitPool
)

func Init() {
	_port, _ := tools.String2Int(config.Conf.RabbitMQ.Port)
	// 初始化消费者池
	consumeOncePool.Do(func() {
		instanceConsumePool = rabbitmq.NewConsumePool()
		// instanceConsumePool.SetMaxConsumeChannel(100)
		err := instanceConsumePool.Connect(config.Conf.RabbitMQ.Host, _port, config.Conf.RabbitMQ.User, config.Conf.RabbitMQ.Password)
		if err != nil {
			fmt.Println(err)
		}
	})

	go getMqMessage()
	go getDelayMqMessage()
}

// 推送消息结构体（如需修改请同步修改rabbitmq内的MqMessage结构体,rabbitmq.MqMessage)
type MqMessage struct {
	MqMessageType common_system.MqMessageType
	// json
	MessageJson []byte

	CycleTime string
}

// json反序列化
func translateMessage(body []byte) (unit MqMessage) {
	unit = MqMessage{}
	err := json.Unmarshal(body, &unit)
	if err != nil {
		return
	}
	return
}

// 队列
func getMqMessage() {
	nomrl := &rabbitmq.ConsumeReceive{
		ExchangeName: rabbitmq.GetExchangeKey(rabbitmq.RoutingKey), // 队列名称
		ExchangeType: rabbitmq.EXCHANGE_TYPE_DIRECT,
		Route:        rabbitmq.RoutingKey,
		QueueName:    rabbitmq.GetQueueKey(rabbitmq.RoutingKey),
		IsTry:        true,  // 是否重试
		IsAutoAck:    false, // 自动消息确认
		MaxReTry:     5,     // 最大重试次数
		EventFail: func(code int, e error, data []byte) {
			fmt.Printf("error:%s", e)
		},
		EventSuccess: consumer,
	}
	instanceConsumePool.RegisterConsumeReceive(nomrl)
	err := instanceConsumePool.RunConsume()
	if err != nil {
		rabbitmq.ErrorHanding(err, "初始化消费者失败")
	}
}

// 延迟队列
func getDelayMqMessage() {
	nomrl := &rabbitmq.ConsumeReceive{
		ExchangeName: rabbitmq.GetExchangeKey(rabbitmq.DelayRoutingKey), // 队列名称
		ExchangeType: rabbitmq.EXCHANGE_TYPE_DELAY,
		Route:        rabbitmq.DelayRoutingKey,
		QueueName:    rabbitmq.GetQueueKey(rabbitmq.DelayRoutingKey),
		IsTry:        true,  // 是否重试
		IsAutoAck:    false, // 自动消息确认
		MaxReTry:     5,     // 最大重试次数
		EventFail: func(code int, e error, data []byte) {
			fmt.Printf("error:%s", e)
		},
		EventSuccess: delayConsume,
	}
	instanceConsumePool.RegisterConsumeReceive(nomrl)
	err := instanceConsumePool.RunConsume()
	if err != nil {
		rabbitmq.ErrorHanding(err, "初始化延时消费者失败")
	}
}

func consumer(data []byte, header map[string]interface{}, retryClient rabbitmq.RetryClientInterface) bool {
	var err error
	_ = retryClient.Ack()

	// json反序列化
	mqMessage := translateMessage(data)
	if mqMessage.MqMessageType == 0 {
		log.Println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
		log.Println("~~~~~~~~~~~~~~~~~~~~~~TEST~SUCCESS~!!!~~~~~~~~~~~~~~~~~~~~~~~~")
		log.Println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeSalePlanOrder {
		// 更新销售计划单
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeModifyProductStock {
		// 更新成品库存
		err = ConsumeModifyProductStock(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeUserAccess {
		// 用户权限
		err = ConsumeUpdateUserAccess(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	// 账套套餐作废
	if mqMessage.MqMessageType == common_system.MqMessageTypeTenantPackageCancel {
		err = ConsumeCancelTenantManagementPackageRel(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	// 搜索图片上传/删除
	if mqMessage.MqMessageType == common_system.MqMessageTypeSearchImageUpload {
		err = ConsumeSearchImageUpload(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	return true
}

func delayConsume(data []byte, header map[string]interface{}, retryClient rabbitmq.RetryClientInterface) bool {
	var err error
	_ = retryClient.Ack()
	// json反序列化
	mqMessage := translateMessage(data)
	if mqMessage.MqMessageType == 0 {
		log.Println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
		log.Println("~~~~~~~~~~~~~~~~~~~~~~TEST~SUCCESS~!!!~~~~~~~~~~~~~~~~~~~~~~~~")
		log.Println("~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~")
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeSalePriceAdjustOrder {
		// 销售调价单生效
		err = ConsumeAdjustSalePriceEffective(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeSalePriceAdjustOrderWait {
		err = ConsumeAdjustSalePriceWait(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeAliPay {
		// 用户权限
		err = ConsumeUpdatePayRecord(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	if mqMessage.MqMessageType == common_system.MqMessageTypeTenantManagementExpire {
		err = ConsumeUpdateTenantManagement(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	// 账套套餐过期
	if mqMessage.MqMessageType == common_system.MqMessageTypeTenantPackageExpire {
		err = ConsumeUpdateTenantManagementPackageRel(mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	// 新建账套通知
	if mqMessage.MqMessageType == common_system.MqMessageTypeNewTenantNotify {
		err = PushNewTenantManagement(common_system.MqMessageTypeNewTenantNotify, "markdown", mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	// 客户反馈
	if mqMessage.MqMessageType == common_system.MqMessageTypeCustomerFeedback {
		err = PushNewTenantManagement(common_system.MqMessageTypeCustomerFeedback, "markdown", mqMessage.MessageJson)
		if err != nil {
			return false
		}
		return true
	}
	return true
}
