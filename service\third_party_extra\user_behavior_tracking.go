package third_party_extra

import (
	"hcscm/aggs/third_party_extra"
	"hcscm/model/mysql/mysql_base"
	"hcscm/structure/third_party_extra_structure"
	"time"
)

type UserBehaviorTrackingService struct {
}

func NewUserBehaviorTrackingService() *UserBehaviorTrackingService {
	return &UserBehaviorTrackingService{}
}

// CreateUserBehaviorTracking 创建用户行为跟踪记录
func (s *UserBehaviorTrackingService) CreateUserBehaviorTracking(req third_party_extra_structure.CreateUserBehaviorTrackingRequest) (err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewUserBehaviorTrackingRepo(tx)
	err = repo.CreateUserBehaviorTracking(req)
	if err != nil {
		return
	}

	err = tx.Commit().Error
	return
}

// GetUserBehaviorTrackingList 获取用户行为跟踪列表
func (s *UserBehaviorTrackingService) GetUserBehaviorTrackingList(req third_party_extra_structure.GetUserBehaviorTrackingListRequest) (response third_party_extra_structure.GetUserBehaviorTrackingListResponse, err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewUserBehaviorTrackingRepo(tx)
	response, err = repo.GetUserBehaviorTrackingList(req)
	return
}

// GetUserBehaviorStatistics 获取用户行为统计
func (s *UserBehaviorTrackingService) GetUserBehaviorStatistics(req third_party_extra_structure.GetUserBehaviorStatisticsRequest) (response third_party_extra_structure.GetUserBehaviorStatisticsResponse, err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewUserBehaviorTrackingRepo(tx)
	response, err = repo.GetUserBehaviorStatistics(req)
	return
}

// TrackUserLogin 跟踪用户登录行为
func (s *UserBehaviorTrackingService) TrackUserLogin(userId uint64, ipAddress, userAgent, sessionId string) (err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewUserBehaviorTrackingRepo(tx)
	err = repo.TrackUserLogin(userId, ipAddress, userAgent, sessionId)
	if err != nil {
		return
	}

	err = tx.Commit().Error
	return
}

// TrackUserLogout 跟踪用户登出行为
func (s *UserBehaviorTrackingService) TrackUserLogout(userId uint64, ipAddress, userAgent, sessionId string, duration int64) (err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	repo := third_party_extra.NewUserBehaviorTrackingRepo(tx)
	err = repo.TrackUserLogout(userId, ipAddress, userAgent, sessionId, duration)
	if err != nil {
		return
	}

	err = tx.Commit().Error
	return
}

// CleanupOldBehaviorData 清理旧的行为数据（定时任务使用）
func (s *UserBehaviorTrackingService) CleanupOldBehaviorData(days int) (err error) {
	tx := mysql_base.GetTx()
	defer mysql_base.PutTx(tx)

	beforeTime := time.Now().AddDate(0, 0, -days)
	repo := third_party_extra.NewUserBehaviorTrackingRepo(tx)
	err = repo.CleanupOldBehaviorData(beforeTime)
	if err != nil {
		return
	}

	err = tx.Commit().Error
	return
}

// TrackPageView 跟踪页面访问
func (s *UserBehaviorTrackingService) TrackPageView(userId uint64, pageName, ipAddress, userAgent, sessionId string) (err error) {
	req := third_party_extra_structure.CreateUserBehaviorTrackingRequest{
		UserId:       userId,
		ActionType:   "view",
		ActionTarget: pageName,
		ActionData:   "{\"page\":\"" + pageName + "\",\"timestamp\":\"" + time.Now().Format(time.RFC3339) + "\"}",
		IpAddress:    ipAddress,
		UserAgent:    userAgent,
		SessionId:    sessionId,
	}
	return s.CreateUserBehaviorTracking(req)
}

// TrackButtonClick 跟踪按钮点击
func (s *UserBehaviorTrackingService) TrackButtonClick(userId uint64, buttonName, pageName, ipAddress, userAgent, sessionId string) (err error) {
	req := third_party_extra_structure.CreateUserBehaviorTrackingRequest{
		UserId:       userId,
		ActionType:   "click",
		ActionTarget: buttonName,
		ActionData:   "{\"button\":\"" + buttonName + "\",\"page\":\"" + pageName + "\",\"timestamp\":\"" + time.Now().Format(time.RFC3339) + "\"}",
		IpAddress:    ipAddress,
		UserAgent:    userAgent,
		SessionId:    sessionId,
	}
	return s.CreateUserBehaviorTracking(req)
}
