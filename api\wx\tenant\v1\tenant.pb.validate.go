// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wx/tenant/tenant.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTenantRequestMultiError, or nil if none found.
func (m *CreateTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantManagementId

	// no validation rules for TenantManagementName

	// no validation rules for TenantManagementDeadline

	// no validation rules for DatabaseName

	// no validation rules for TobeDevelopedAppId

	// no validation rules for Phone

	// no validation rules for Contact

	if len(errors) > 0 {
		return CreateTenantRequestMultiError(errors)
	}

	return nil
}

// CreateTenantRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTenantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTenantRequestMultiError) AllErrors() []error { return m }

// CreateTenantRequestValidationError is the validation error returned by
// CreateTenantRequest.Validate if the designated constraints aren't met.
type CreateTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTenantRequestValidationError) ErrorName() string {
	return "CreateTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTenantRequestValidationError{}

// Validate checks the field values on CreateTenantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateTenantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTenantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTenantReplyMultiError, or nil if none found.
func (m *CreateTenantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTenantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateTenantReplyMultiError(errors)
	}

	return nil
}

// CreateTenantReplyMultiError is an error wrapping multiple validation errors
// returned by CreateTenantReply.ValidateAll() if the designated constraints
// aren't met.
type CreateTenantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTenantReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTenantReplyMultiError) AllErrors() []error { return m }

// CreateTenantReplyValidationError is the validation error returned by
// CreateTenantReply.Validate if the designated constraints aren't met.
type CreateTenantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTenantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTenantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTenantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTenantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTenantReplyValidationError) ErrorName() string {
	return "CreateTenantReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTenantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTenantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTenantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTenantReplyValidationError{}

// Validate checks the field values on UpdateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTenantRequestMultiError, or nil if none found.
func (m *UpdateTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantManagementId

	// no validation rules for TenantManagementName

	// no validation rules for TenantManagementDeadline

	// no validation rules for DatabaseName

	// no validation rules for TobeDevelopedAppId

	// no validation rules for Id

	// no validation rules for Phone

	// no validation rules for Contact

	if len(errors) > 0 {
		return UpdateTenantRequestMultiError(errors)
	}

	return nil
}

// UpdateTenantRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTenantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTenantRequestMultiError) AllErrors() []error { return m }

// UpdateTenantRequestValidationError is the validation error returned by
// UpdateTenantRequest.Validate if the designated constraints aren't met.
type UpdateTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTenantRequestValidationError) ErrorName() string {
	return "UpdateTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTenantRequestValidationError{}

// Validate checks the field values on UpdateTenantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateTenantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTenantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTenantReplyMultiError, or nil if none found.
func (m *UpdateTenantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTenantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateTenantReplyMultiError(errors)
	}

	return nil
}

// UpdateTenantReplyMultiError is an error wrapping multiple validation errors
// returned by UpdateTenantReply.ValidateAll() if the designated constraints
// aren't met.
type UpdateTenantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTenantReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTenantReplyMultiError) AllErrors() []error { return m }

// UpdateTenantReplyValidationError is the validation error returned by
// UpdateTenantReply.Validate if the designated constraints aren't met.
type UpdateTenantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTenantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTenantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTenantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTenantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTenantReplyValidationError) ErrorName() string {
	return "UpdateTenantReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTenantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTenantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTenantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTenantReplyValidationError{}

// Validate checks the field values on DeleteTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTenantRequestMultiError, or nil if none found.
func (m *DeleteTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantManagementId

	if len(errors) > 0 {
		return DeleteTenantRequestMultiError(errors)
	}

	return nil
}

// DeleteTenantRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteTenantRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTenantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTenantRequestMultiError) AllErrors() []error { return m }

// DeleteTenantRequestValidationError is the validation error returned by
// DeleteTenantRequest.Validate if the designated constraints aren't met.
type DeleteTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTenantRequestValidationError) ErrorName() string {
	return "DeleteTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTenantRequestValidationError{}

// Validate checks the field values on DeleteTenantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteTenantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTenantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTenantReplyMultiError, or nil if none found.
func (m *DeleteTenantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTenantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteTenantReplyMultiError(errors)
	}

	return nil
}

// DeleteTenantReplyMultiError is an error wrapping multiple validation errors
// returned by DeleteTenantReply.ValidateAll() if the designated constraints
// aren't met.
type DeleteTenantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTenantReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTenantReplyMultiError) AllErrors() []error { return m }

// DeleteTenantReplyValidationError is the validation error returned by
// DeleteTenantReply.Validate if the designated constraints aren't met.
type DeleteTenantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTenantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTenantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTenantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTenantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTenantReplyValidationError) ErrorName() string {
	return "DeleteTenantReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTenantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTenantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTenantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTenantReplyValidationError{}

// Validate checks the field values on GetTenantRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTenantRequestMultiError, or nil if none found.
func (m *GetTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetTenantRequestMultiError(errors)
	}

	return nil
}

// GetTenantRequestMultiError is an error wrapping multiple validation errors
// returned by GetTenantRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTenantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTenantRequestMultiError) AllErrors() []error { return m }

// GetTenantRequestValidationError is the validation error returned by
// GetTenantRequest.Validate if the designated constraints aren't met.
type GetTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTenantRequestValidationError) ErrorName() string { return "GetTenantRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTenantRequestValidationError{}

// Validate checks the field values on GetTenantReply with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetTenantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTenantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetTenantReplyMultiError,
// or nil if none found.
func (m *GetTenantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTenantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreateTime

	// no validation rules for CreatorId

	// no validation rules for CreatorName

	// no validation rules for UpdateTime

	// no validation rules for UpdaterId

	// no validation rules for UpdaterName

	// no validation rules for TenantManagementId

	// no validation rules for TenantManagementName

	// no validation rules for TobeDevelopedAppId

	// no validation rules for Phone

	// no validation rules for Contact

	if len(errors) > 0 {
		return GetTenantReplyMultiError(errors)
	}

	return nil
}

// GetTenantReplyMultiError is an error wrapping multiple validation errors
// returned by GetTenantReply.ValidateAll() if the designated constraints
// aren't met.
type GetTenantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTenantReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTenantReplyMultiError) AllErrors() []error { return m }

// GetTenantReplyValidationError is the validation error returned by
// GetTenantReply.Validate if the designated constraints aren't met.
type GetTenantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTenantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTenantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTenantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTenantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTenantReplyValidationError) ErrorName() string { return "GetTenantReplyValidationError" }

// Error satisfies the builtin error interface
func (e GetTenantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTenantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTenantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTenantReplyValidationError{}

// Validate checks the field values on ListTenantRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTenantRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTenantRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTenantRequestMultiError, or nil if none found.
func (m *ListTenantRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTenantRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPage() < 1 {
		err := ListTenantRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := ListTenantRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Ids

	// no validation rules for Name

	// no validation rules for Phone

	// no validation rules for Contact

	// no validation rules for TobeDevelopedAppName

	if len(errors) > 0 {
		return ListTenantRequestMultiError(errors)
	}

	return nil
}

// ListTenantRequestMultiError is an error wrapping multiple validation errors
// returned by ListTenantRequest.ValidateAll() if the designated constraints
// aren't met.
type ListTenantRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTenantRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTenantRequestMultiError) AllErrors() []error { return m }

// ListTenantRequestValidationError is the validation error returned by
// ListTenantRequest.Validate if the designated constraints aren't met.
type ListTenantRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTenantRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTenantRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTenantRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTenantRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTenantRequestValidationError) ErrorName() string {
	return "ListTenantRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTenantRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTenantRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTenantRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTenantRequestValidationError{}

// Validate checks the field values on ListTenantReply with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListTenantReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTenantReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTenantReplyMultiError, or nil if none found.
func (m *ListTenantReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTenantReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTenantReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTenantReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTenantReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListTenantReplyMultiError(errors)
	}

	return nil
}

// ListTenantReplyMultiError is an error wrapping multiple validation errors
// returned by ListTenantReply.ValidateAll() if the designated constraints
// aren't met.
type ListTenantReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTenantReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTenantReplyMultiError) AllErrors() []error { return m }

// ListTenantReplyValidationError is the validation error returned by
// ListTenantReply.Validate if the designated constraints aren't met.
type ListTenantReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTenantReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTenantReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTenantReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTenantReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTenantReplyValidationError) ErrorName() string { return "ListTenantReplyValidationError" }

// Error satisfies the builtin error interface
func (e ListTenantReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTenantReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTenantReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTenantReplyValidationError{}

// Validate checks the field values on ListTenantReply_Tenant with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTenantReply_Tenant) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTenantReply_Tenant with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTenantReply_TenantMultiError, or nil if none found.
func (m *ListTenantReply_Tenant) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTenantReply_Tenant) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreateTime

	// no validation rules for CreatorId

	// no validation rules for CreatorName

	// no validation rules for UpdateTime

	// no validation rules for UpdaterId

	// no validation rules for UpdaterName

	// no validation rules for TenantManagementId

	// no validation rules for TenantManagementName

	// no validation rules for TobeDevelopedAppId

	// no validation rules for Phone

	// no validation rules for Contact

	if len(errors) > 0 {
		return ListTenantReply_TenantMultiError(errors)
	}

	return nil
}

// ListTenantReply_TenantMultiError is an error wrapping multiple validation
// errors returned by ListTenantReply_Tenant.ValidateAll() if the designated
// constraints aren't met.
type ListTenantReply_TenantMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTenantReply_TenantMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTenantReply_TenantMultiError) AllErrors() []error { return m }

// ListTenantReply_TenantValidationError is the validation error returned by
// ListTenantReply_Tenant.Validate if the designated constraints aren't met.
type ListTenantReply_TenantValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTenantReply_TenantValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTenantReply_TenantValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTenantReply_TenantValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTenantReply_TenantValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTenantReply_TenantValidationError) ErrorName() string {
	return "ListTenantReply_TenantValidationError"
}

// Error satisfies the builtin error interface
func (e ListTenantReply_TenantValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTenantReply_Tenant.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTenantReply_TenantValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTenantReply_TenantValidationError{}
