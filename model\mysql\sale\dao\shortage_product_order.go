package dao

import (
	"context"
	"fmt"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/sale"
	"hcscm/model/redis"
	structure "hcscm/structure/sale"
)

type IShortageProductOrder interface {
	MustCreate(tx *mysql_base.Tx, r mysql.ShortageProductOrder) (o mysql.ShortageProductOrder, err error)
	MustUpdate(tx *mysql_base.Tx, r mysql.ShortageProductOrder) (o mysql.ShortageProductOrder, err error)
	MustDelete(tx *mysql_base.Tx, r mysql.ShortageProductOrder) (err error)
	MustFirstByID(tx *mysql_base.Tx, id uint64) (r mysql.ShortageProductOrder, err error)
	FirstByID(tx *mysql_base.Tx, id uint64) (r mysql.ShortageProductOrder, exist bool, err error)
	FirstByQuery(tx *mysql_base.Tx, q *structure.ExistOrderQuery) (r mysql.ShortageProductOrder, exist bool, err error)
	FindByShortageProductOrderID(tx *mysql_base.Tx, objects ...interface{}) (o mysql.ShortageProductOrderList, err error)
	FindByIDs(tx *mysql_base.Tx, ids []uint64) (o mysql.ShortageProductOrderList, err error)
	FindByParentID(tx *mysql_base.Tx, pid uint64) (o mysql.ShortageProductOrderList, err error)
	Search(tx *mysql_base.Tx, q *structure.GetShortageProductOrderListQuery) (o mysql.ShortageProductOrderList, count int, err error)
}

type shortageProductOrderDao struct {
	mysqlFn   mysql_base.IMysqlFn
	redisFn   redis.IRedisFn
	prefixKey string
	isCache   bool
}

func NewShortageProductOrderDao(ctx context.Context, isCache bool) IShortageProductOrder {
	return &shortageProductOrderDao{
		mysqlFn:   mysql_base.NewMysqlFn(),
		redisFn:   redis.NewNode(redis.GetClient(ctx), errors.ErrCodeRedisError, 28800),
		prefixKey: redis.GenerateRedisCacheKey(ctx, "shortage_product_order:"),
		isCache:   isCache,
	}
}

func (dao *shortageProductOrderDao) MustCreate(tx *mysql_base.Tx, r mysql.ShortageProductOrder) (o mysql.ShortageProductOrder, err error) {
	err = dao.mysqlFn.MustCreateModel(tx, &r)
	o = r
	return
}

func (dao *shortageProductOrderDao) MustUpdate(tx *mysql_base.Tx, r mysql.ShortageProductOrder) (o mysql.ShortageProductOrder, err error) {
	err = dao.mysqlFn.MustUpdateModel(tx, &r)
	if dao.isCache {
		_ = dao.redisFn.Del(fmt.Sprintf("%s%d", dao.prefixKey, r.Id))
	}
	o = r
	return
}

func (dao *shortageProductOrderDao) MustDelete(tx *mysql_base.Tx, r mysql.ShortageProductOrder) (err error) {
	err = dao.mysqlFn.MustDeleteModel(tx, &r)
	if dao.isCache {
		_ = dao.redisFn.Del(fmt.Sprintf("%s%d", dao.prefixKey, r.Id))
	}
	return
}

func (dao *shortageProductOrderDao) MustFirstByID(tx *mysql_base.Tx, id uint64) (r mysql.ShortageProductOrder, err error) {
	if !dao.isCache {
		var (
			cond = mysql_base.NewCondition()
		)
		err = dao.mysqlFn.MustFirst(tx, &r, id, cond)
		if err != nil {
			return
		}
		return
	}
	err = dao.redisFn.Take(&r, fmt.Sprintf("%s%d", dao.prefixKey, id), func(v interface{}) error {
		var cond = mysql_base.NewCondition()
		return dao.mysqlFn.MustFirst(tx, &r, id, cond)
	})
	if err != nil {
		return
	}
	return
}

func (dao *shortageProductOrderDao) FirstByID(tx *mysql_base.Tx, id uint64) (r mysql.ShortageProductOrder, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = dao.mysqlFn.First(tx, &r, id, cond)

	return
}

func (dao *shortageProductOrderDao) FirstByQuery(tx *mysql_base.Tx, q *structure.ExistOrderQuery) (r mysql.ShortageProductOrder, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	if q.SaleProductOrderId != 0 {
		cond.AddEqual("sale_product_order_id", q.SaleProductOrderId)
	}
	cond.AddNotEqual("audit_status", common_system.OrderStatusVoided)
	exist, err = dao.mysqlFn.FirstByCond(tx, &r, cond)

	return
}

func (dao *shortageProductOrderDao) FindByShortageProductOrderID(tx *mysql_base.Tx, objects ...interface{}) (o mysql.ShortageProductOrderList, err error) {
	ids := mysql.GetShortageProductOrderIdList(objects)
	var (
		r    mysql.ShortageProductOrder
		cond = mysql_base.NewCondition()
		list []mysql.ShortageProductOrder
	)

	err = dao.mysqlFn.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (dao *shortageProductOrderDao) FindByIDs(tx *mysql_base.Tx, ids []uint64) (o mysql.ShortageProductOrderList, err error) {
	var (
		r    mysql.ShortageProductOrder
		cond = mysql_base.NewCondition()
		list []mysql.ShortageProductOrder
	)
	r.BuildReadCond(tx.Context, cond)

	err = dao.mysqlFn.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据上级id获取
func (dao *shortageProductOrderDao) FindByParentID(tx *mysql_base.Tx, pid uint64) (o mysql.ShortageProductOrderList, err error) {
	var (
		r    mysql.ShortageProductOrder
		cond = mysql_base.NewCondition()
		list []mysql.ShortageProductOrder
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "id", pid)

	err = dao.mysqlFn.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func (dao *shortageProductOrderDao) Search(tx *mysql_base.Tx, q *structure.GetShortageProductOrderListQuery) (o mysql.ShortageProductOrderList, count int, err error) {
	var (
		r           mysql.ShortageProductOrder
		cond        = mysql_base.NewCondition()
		list        []mysql.ShortageProductOrder
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.SaleSystemId != 0 {
		cond.AddTableEqual(r, "sale_system_id", q.SaleSystemId)
	}
	if q.VoucherNumber != "" {
		cond.AddTableFuzzyMatch(r, "voucher_number", q.VoucherNumber)
	}
	if !q.StartOrderTime.IsYMDZero() && !q.EndOrderTime.IsYMDZero() {
		cond.AddTableBetween(r, "order_time", q.StartOrderTime.StringYMD(), q.EndOrderTime.StringYMD2DayListTimeYMDHMS())
	}
	if q.CustomerId != 0 {
		cond.AddTableEqual(r, "customer_id", q.CustomerId)
	}
	if q.SaleUserId != 0 {
		cond.AddTableEqual(r, "sale_user_id", q.SaleUserId)
	}
	if q.OrderNo != "" {
		cond.AddTableFuzzyMatch(r, "order_no", q.OrderNo)
	}
	if !q.AuditStatus.IsNil() {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToUint64())
	} else {
		cond.AddNotEqual("audit_status", common_system.OrderStatusVoided)
	}
	if q.AuditorId != 0 {
		cond.AddTableEqual(r, "auditor_id", q.AuditorId)
	}
	if q.UpdateUserId != 0 {
		cond.AddTableEqual(r, "updater_id", q.UpdateUserId)
	}
	if !q.CreateTimeStart.IsYMDZero() && !q.CreateTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "create_time", q.CreateTimeStart.StringYMD(), q.CreateTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.AuditTimeStart.IsYMDZero() && !q.AuditTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "audit_date", q.AuditTimeStart.StringYMD(), q.AuditTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.UpdateTimeStart.IsYMDZero() && !q.UpdateTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "update_time", q.UpdateTimeStart.StringYMD(), q.UpdateTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.OrderTimeStart.IsYMDZero() && !q.OrderTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "order_time", q.OrderTimeStart.StringYMD(), q.OrderTimeEnd.StringYMD2DayListTimeYMDHMS())
	}

	groupFields = []string{}
	count, err = dao.mysqlFn.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}
