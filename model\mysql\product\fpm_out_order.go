package product

import (
	"context"
	"gorm.io/gorm"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	common "hcscm/common/sale"
	"hcscm/model/mysql/mysql_base"
	mysql_system "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/vars"
	"time"
)

func GetFpmOutOrderIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_out_order_id")
}

type FpmOutOrderList []FpmOutOrder

func (r FpmOutOrderList) List() []FpmOutOrder {
	return r
}

func (r FpmOutOrderList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmOutOrderList) GetWarehouseIDs() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.WarehouseId)
	}
	return o
}

func (r FpmOutOrderList) GetProcessUnitIDs() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.BizUnitId)
	}
	return o
}

func (r FpmOutOrderList) One() FpmOutOrder {
	return r[0]
}

func (r FpmOutOrderList) Pick(id uint64) (o FpmOutOrder) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

// FpmOutOrder 出仓表
type FpmOutOrder struct {
	mysql_base.Order
	Id               uint64                         `gorm:"column:id;primaryKey"`
	OutOrderType     cus_const.WarehouseGoodOutType `gorm:"column:out_order_type"`                                       // 出仓类型
	SrcOrderId       uint64                         `gorm:"column:src_order_id"`                                         // 源单id
	SrcOrderNo       string                         `gorm:"column:src_order_no"`                                         // 源单单号
	ArrangeOrderId   uint64                         `gorm:"column:arrange_order_id"`                                     // 配布id
	ArrangeOrderNo   string                         `gorm:"column:arrange_order_no"`                                     // 配布单号
	SaleSystemId     uint64                         `gorm:"column:sale_system_id" relate:"sale_system_id"`               // 营销体系id
	BizUnitId        uint64                         `gorm:"column:biz_unit_id" relate:"biz_unit_id"`                     // 供应商id(加工单位,客户)
	WarehouseId      uint64                         `gorm:"column:warehouse_id" relate:"warehouse_id"`                   // 仓库id
	WarehouseOutTime time.Time                      `gorm:"column:warehouse_out_time"`                                   // 出仓时间
	StoreKeeperId    uint64                         `gorm:"column:store_keeper_id" relate:"employee_id,store_keeper_id"` // 仓管员id（关联user.id）
	Remark           string                         `gorm:"column:remark"`                                               // 备注
	TotalRoll        int                            `gorm:"column:total_roll"`                                           // 匹数总计
	TotalWeight      int                            `gorm:"column:total_weight"`                                         // 数量总计
	TotalLength      int                            `gorm:"column:total_length"`                                         // 长度总计
	TotalPrice       int                            `gorm:"column:total_price"`                                          // 单据金额
	UnitId           uint64                         `gorm:"column:unit_id" relate:"measurement_unit_id,unit_id"`         // 单位id
	DepartmentId     uint64                         `gorm:"column:department_id" relate:"department_id"`                 // 下单用户所属部门
	OrderNo          string                         `gorm:"column:order_no"`                                             // 单据编号
	Number           int                            `gorm:"column:number"`                                               // 编号流水：每日重新更新
	VoucherNumber    string                         `gorm:"column:voucher_number"`                                       // 凭证号
	TextureUrl       string                         `gorm:"column:texture_url"`                                          // 凭证图片URL
	SaleMode         common.SaleOrderType           `gorm:"column:sale_mode"`                                            // 订单类型 1大货 2剪板 3客订大货 4客订剪板
	// 内部调拨出仓单
	InWarehouseId      uint64 `gorm:"column:in_warehouse_id" relate:"warehouse_id"`              // 调入仓库id
	DriverId           string `gorm:"column:driver_id"`                                          // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId uint64 `gorm:"column:logistics_company_id" relate:"logistics_company_id"` // 物流公司id
	// 成品销售调拨出仓单
	WarehouseInId        uint64 `gorm:"column:warehouse_in_id" relate:"warehouse_id"`      // 调入仓库id
	SaleUserId           uint64 `gorm:"column:sale_user_id" relate:"sale_user_id"`         // 销售员id
	SaleFollowerId       uint64 `gorm:"column:sale_follower_id" relate:"sale_follower_id"` // 销售跟单员id
	LogisticsCompanyArea string `gorm:"column:logistics_company_area"`                     // 物流公司区域
	// 成品销售出仓单
	SaleAlloInOrderId    uint64 `gorm:"column:sale_allo_in_order_id"`                          // 销售调拨进仓单id
	ProcessFactoryId     uint64 `gorm:"column:process_factory_id" relate:"process_factory_id"` // 加工厂id
	ReceiveName          string `gorm:"column:receive_name"`                                   // 收货人
	ReceiveAddr          string `gorm:"column:receive_addr"`                                   // 收货地址
	ReceivePhone         string `gorm:"column:receive_phone"`                                  // 收货电话
	ReceiveTag           string `gorm:"column:receive_tag"`                                    // 收货标签
	ArrangeUserId        uint64 `gorm:"column:arrange_user_id" relate:"employee_id"`           // 配布员id（关联employee.id）
	LogisticsCompanyName string `gorm:"column:logistics_company_name"`                         // 物流公司名称
	InternalRemark       string `gorm:"column:internal_remark"`                                // 内部备注
	SaleRemark           string `gorm:"column:sale_remark"`                                    // 销售备注
}

// 查询后的钩子
func (r *FpmOutOrder) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmOutOrder) GetId() uint64 {
	return r.Id
}

// TableName FpmOutOrder 表名
func (FpmOutOrder) TableName() string {
	return "fpm_out_order"
}

func (r FpmOutOrder) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmOutOrder) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

// ErrCodeFpmOutOrderAlreadyExist     ErrCode = 51XX1 // 出仓表已存在
// ErrCodeFpmOutOrderNotExist         ErrCode = 51XX2 // 出仓表不存在
func (FpmOutOrder) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmOutOrderNotExist
}

func (FpmOutOrder) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmOutOrderAlreadyExist
}

func (r FpmOutOrder) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	mysql_system.CommonDataSeparate(ctx, r, cond)
}

func (r FpmOutOrder) GetOtherFields() []string {
	return []string{
		"creator_id", // 创建人
		"updater_id", // 更新人
	}
}

func NewFpmOutOrder(
	ctx context.Context,
	p *structure.AddFpmOutOrderParam,
) (r FpmOutOrder) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.SrcOrderId = p.SrcOrderId
	r.SrcOrderNo = p.SrcOrderNo
	r.ArrangeOrderId = p.ArrangeOrderId
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.SaleSystemId = p.SaleSystemId
	r.BizUnitId = p.BizUnitId
	r.WarehouseId = p.WarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.StoreKeeperId = p.StoreKeeperId
	r.Remark = p.Remark
	r.TextureUrl = p.TextureUrl
	r.VoucherNumber = p.VoucherNumber
	r.SaleMode = p.SaleMode
	if p.OutOrderType > 0 {
		r.OutOrderType = p.OutOrderType
	}
	return
}

func (r *FpmOutOrder) UpdateFpmOutOrder(
	ctx context.Context,
	p *structure.UpdateFpmOutOrderParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.BizUnitId = p.BizUnitId
	r.WarehouseId = p.WarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.StoreKeeperId = p.StoreKeeperId
	r.Remark = p.Remark
	r.VoucherNumber = p.VoucherNumber
	r.TextureUrl = p.TextureUrl
	r.SaleMode = p.SaleMode
}

func NewFpmInternalAllocateOutOrder(
	ctx context.Context,
	p *structure.AddFpmInternalAllocateOutOrderParam,
) (r FpmOutOrder) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.OutOrderType = p.OutOrderType
	if p.OutOrderType == 0 {
		r.OutOrderType = cus_const.WarehouseGoodOutTypeInternalAllocate
	}
	r.ArrangeOrderId = p.ArrangeOrderId
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.SaleSystemId = p.SaleSystemId
	r.WarehouseId = p.OutWarehouseId
	r.InWarehouseId = p.InWarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.StoreKeeperId = p.StoreKeeperId
	r.DriverId = p.DriverId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.Remark = p.Remark
	return
}

func (r *FpmOutOrder) UpdateFpmInternalAllocateOutOrder(
	ctx context.Context,
	p *structure.UpdateFpmInternalAllocateOutOrderParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.WarehouseId = p.OutWarehouseId
	r.InWarehouseId = p.InWarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.StoreKeeperId = p.StoreKeeperId
	r.DriverId = p.DriverId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.Remark = p.Remark
}

func NewFpmSaleAllocateOutOrder(
	ctx context.Context,
	p *structure.AddFpmSaleAllocateOutOrderParam,
) (r FpmOutOrder) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.OutOrderType = p.OutOrderType
	if p.OutOrderType == 0 {
		r.OutOrderType = cus_const.WarehouseGoodOutTypeSaleAllocate
	}
	r.ArrangeOrderId = p.ArrangeOrderId
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.SaleSystemId = p.SaleSystemId
	r.WarehouseId = p.WarehouseId
	r.WarehouseInId = p.WarehouseInId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.BizUnitId = p.CustomerId
	r.StoreKeeperId = p.StoreKeeperId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.DriverId = p.DriverId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.LogisticsCompanyArea = p.LogisticsCompanyArea
	r.LogisticsCompanyName = p.LogisticsCompany
	r.SaleMode = p.SaleMode
	r.Remark = p.Remark
	return
}

func (r *FpmOutOrder) UpdateFpmSaleAllocateOutOrder(
	ctx context.Context,
	p *structure.UpdateFpmSaleAllocateOutOrderParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.WarehouseId = p.WarehouseId
	r.WarehouseInId = p.WarehouseInId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.BizUnitId = p.CustomerId
	r.StoreKeeperId = p.StoreKeeperId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.DriverId = p.DriverId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.LogisticsCompanyArea = p.LogisticsCompanyArea
	r.Remark = p.Remark
}

func NewFpmSaleOutOrder(
	ctx context.Context,
	p *structure.AddFpmSaleOutOrderParam,
) (r FpmOutOrder) {
	// 后台跟易布用的一个增加的接口，前端在后台要传配布单id，易布不要传
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.OutOrderType = p.OutOrderType
	if p.OutOrderType == 0 {
		r.OutOrderType = cus_const.WarehouseGoodOutTypeSale
	}
	r.SaleAlloInOrderId = p.SaleAlloInOrderId
	r.ArrangeOrderId = p.ArrangeOrderId
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.SaleSystemId = p.SaleSystemId
	r.BizUnitId = p.CustomerId
	r.WarehouseId = p.WarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.ProcessFactoryId = p.ProcessFactoryId
	r.ReceiveName = p.ReceiveName
	r.ReceiveAddr = p.ReceiveAddr
	r.ReceivePhone = p.ReceivePhone
	r.ReceiveTag = p.ReceiveTag
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.StoreKeeperId = p.StoreKeeperId
	r.ArrangeUserId = p.ArrangeUserId
	r.DriverId = p.DriverId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.LogisticsCompanyName = p.LogisticsCompanyName
	r.LogisticsCompanyArea = p.LogisticsCompanyArea
	r.InternalRemark = p.InternalRemark
	r.SaleRemark = p.SaleRemark
	r.TextureUrl = p.TextureUrl
	r.SrcOrderId = p.SrcOrderID
	r.SrcOrderNo = p.SrcOrderNo
	r.SaleMode = p.SaleMode
	return
}

func (r *FpmOutOrder) UpdateFpmSaleOutOrder(
	ctx context.Context,
	p *structure.UpdateFpmSaleOutOrderParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.BizUnitId = p.CustomerId
	r.WarehouseId = p.WarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.ProcessFactoryId = p.ProcessFactoryId
	r.ReceiveName = p.ReceiveName
	r.ReceiveAddr = p.ReceiveAddr
	r.ReceivePhone = p.ReceivePhone
	r.ReceiveTag = p.ReceiveTag
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.StoreKeeperId = p.StoreKeeperId
	r.ArrangeUserId = p.ArrangeUserId
	r.DriverId = p.DriverId
	r.LogisticsCompanyId = p.LogisticsCompanyId
	r.LogisticsCompanyName = p.LogisticsCompanyName
	r.LogisticsCompanyArea = p.LogisticsCompanyArea
	r.InternalRemark = p.InternalRemark
	r.SaleRemark = p.SaleRemark
	r.TextureUrl = p.TextureUrl
	r.SaleMode = p.SaleMode
}

func NewFpmProcessOutOrder(
	ctx context.Context,
	p *structure.AddFpmProcessOutOrderParam,
) (r FpmOutOrder) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.ArrangeOrderId = p.ArrangeOrderId
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.SaleSystemId = p.SaleSystemId
	r.BizUnitId = p.ProcessUnitId
	r.WarehouseId = p.WarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.StoreKeeperId = p.StoreKeeperId
	r.Remark = p.Remark
	r.OutOrderType = p.OutOrderType
	if p.OutOrderType == 0 {
		r.OutOrderType = cus_const.WarehouseGoodOutTypeProcess
	}
	r.VoucherNumber = p.VoucherNumber
	r.TextureUrl = p.TextureUrl
	return
}

func (r *FpmOutOrder) UpdateFpmProcessOutOrder(
	ctx context.Context,
	p *structure.UpdateFpmProcessOutOrderParam,
) {
	r.SaleSystemId = p.SaleSystemId
	r.BizUnitId = p.ProcessUnitId
	r.WarehouseId = p.WarehouseId
	r.WarehouseOutTime = p.WarehouseOutTime.ToTimeYMD()
	r.StoreKeeperId = p.StoreKeeperId
	r.Remark = p.Remark
	r.VoucherNumber = p.VoucherNumber
	r.TextureUrl = p.TextureUrl
	if p.OutOrderType != 0 {
		r.OutOrderType = p.OutOrderType
	}
}
