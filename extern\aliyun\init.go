package aliyun

import (
	"hcscm/config"
	"time"
)

func Init() {
	AccessKey = config.Conf.Alibaba.AccessKey
	AccessSecret = config.Conf.Alibaba.AccessSecret
	SmsSignName = config.Conf.Alibaba.SignName
	SmsTemplateCode = config.Conf.Alibaba.TemplateCode
	SearchImageaccesskey = config.Conf.Alibaba.SearchImageaccesskey
	SearchImageaccesssecret = config.Conf.Alibaba.SearchImageaccesssecret
	SearchImageInstanceName = config.Conf.Alibaba.SearchImageInstanceName

	// 初始化图片搜索API限流间隔，默认为1秒
	if config.Conf.Alibaba.ImageSearchRateLimitMs > 0 {
		ImageSearchRateLimitInterval = time.Duration(config.Conf.Alibaba.ImageSearchRateLimitMs) * time.Millisecond
	} else {
		ImageSearchRateLimitInterval = time.Second // 默认1秒
	}
}

var (
	// 设置阿里云的AccessKey和AccessSecret
	AccessKey                    string
	AccessSecret                 string
	SmsSignName                  string
	SmsTemplateCode              string
	SearchImageaccesskey         string
	SearchImageaccesssecret      string
	SearchImageInstanceName      string        // 搜索图片的实例名称
	ImageSearchRateLimitInterval time.Duration // 图片搜索API限流间隔
)
