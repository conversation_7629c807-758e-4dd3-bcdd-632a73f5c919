package msg_publish

import (
	"context"
	"encoding/json"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/rabbitmq"
)

type SearchImageUpload struct {
	TenantManagementId      uint64
	SearchImageInstanceName string
	ProductCode             string
	Url                     string
	Type                    int // 1为新增 2为更新 3为删除
}

type SearchImageUploadList []SearchImageUpload

func PushSearchImageUpload(ctx context.Context, req SearchImageUploadList) (err error) {
	for _, searchImageUpload := range req {
		var (
			mqMessage rabbitmq.MqMessage
		)
		mqMessage.MqMessageType = common_system.MqMessageTypeSearchImageUpload
		mqMessage.MessageJson, err = json.Marshal(searchImageUpload)
		if err != nil {
			return
		}
		publishMessage(mqMessage)
	}
	return
}
