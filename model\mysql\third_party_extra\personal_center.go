package third_party_extra

import (
	"hcscm/common/errors"
	"hcscm/model/mysql/mysql_base"
	"time"
)

// PersonalCenter 个人中心
type PersonalCenter struct {
	mysql_base.Model
	Id          uint64     `gorm:"column:id"`
	UserId      uint64     `gorm:"column:user_id"`     // 用户ID
	Nickname    string     `gorm:"column:nickname"`    // 昵称
	Avatar      string     `gorm:"column:avatar"`      // 头像
	Phone       string     `gorm:"column:phone"`       // 手机号
	Email       string     `gorm:"column:email"`       // 邮箱
	Gender      int        `gorm:"column:gender"`      // 性别 1:男 2:女 0:未知
	Birthday    *time.Time `gorm:"column:birthday"`    // 生日
	Address     string     `gorm:"column:address"`     // 地址
	Description string     `gorm:"column:description"` // 个人描述
	Status      int        `gorm:"column:status"`      // 状态 1:正常 0:禁用
}

func (p *PersonalCenter) GetId() uint64 {
	return p.Id
}

// TableName PersonalCenter 表名
func (PersonalCenter) TableName() string {
	return "personal_center"
}

func (p PersonalCenter) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (PersonalCenter) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (PersonalCenter) GetNotExistError() errors.ErrCode {
	return errors.ErrCodePersonalCenterNotExist
}

func (PersonalCenter) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodePersonalCenterAlreadyExist
}

type PersonalCenterList []PersonalCenter
