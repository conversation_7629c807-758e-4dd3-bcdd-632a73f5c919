package tenant_management

import (
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	aggs "hcscm/aggs/tenant_management"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/tenant_management"
	"hcscm/tools"
)

type ICodeListOrcManagementLogic interface {
	SearchList(ctx context.Context, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error)
	GetDetailInfo(ctx context.Context, query structure.GetCodeListOrcManagementQuery) (data structure.GetCodeListOrcManagementData, err error)
	GetMPDetailInfo(ctx context.Context, query structure.GetCodeListOrcManagementQuery) (data structure.GetMPCodeListOrcManagementData, err error)
	UpdateCodeListName(ctx context.Context, param structure.UpdateCodeListOrcManagementNameParam) (data structure.ResTenantManagementIDData, err error)

	UpdateStatusEnable(ctx context.Context, param structure.EnableCodeListOrcManagementParam) (err error)
	UpdateStatusDisable(ctx context.Context, param structure.DisableCodeListOrcManagementParam) (err error)

	UpdateStatusEnableEleColorCard(ctx context.Context, param structure.EnableCodeListOrcManagementParam) (err error)
	UpdateStatusDisableEleColorCard(ctx context.Context, param structure.DisableCodeListOrcManagementParam) (err error)

	UpdateStatusSearchImage(ctx context.Context, param structure.EnableSearchImageParam) (err error)

	Recharge(ctx context.Context, param structure.RechargeParam) (data structure.RechargeData, err error)
	EleColorCardServerRecharge(ctx context.Context, param structure.RechargeParam) (data structure.RechargeData, err error)
	GetRechargeHistorys(ctx context.Context, query structure.RechargeHistoryListQuery) (list structure.GetRechargeHistoryListDataList, total int, err error)
	IsExpired(ctx context.Context, expireType common.RechargeType, tenantManagementId uint64) (isExpired bool, err error)
	IsRecharged(ctx context.Context, tenantManagementId uint64) (isRecharged bool, err error)
}

func NewCodeListOrcManagementLogic(ginCtx *gin.Context) ICodeListOrcManagementLogic {
	return &codeListOrcManagementLogic{
		codeListOrcManagementRepo: aggs.NewCodeListOrcManagementRepo(),
		singleFlight:              tools.Single,
		ginCtx:                    ginCtx,
	}
}

type codeListOrcManagementLogic struct {
	tx           *mysql_base.Tx
	singleFlight tools.SingleFlight
	ginCtx       *gin.Context
	// tenantManagementRepo aggs.ITenantManagementRepo
	codeListOrcManagementRepo aggs.ICodeListOrcManagementRepo
}

func (logic *codeListOrcManagementLogic) SearchList(ctx context.Context, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	return logic.codeListOrcManagementRepo.SearchList(ctx, tx, query)
}

func (logic *codeListOrcManagementLogic) GetDetailInfo(ctx context.Context, query structure.GetCodeListOrcManagementQuery) (data structure.GetCodeListOrcManagementData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return logic.codeListOrcManagementRepo.GetDetailInfo(ctx, tx, query)
}

func (logic *codeListOrcManagementLogic) GetMPDetailInfo(ctx context.Context, query structure.GetCodeListOrcManagementQuery) (data structure.GetMPCodeListOrcManagementData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	return logic.codeListOrcManagementRepo.GetMPDetailInfo(ctx, tx, query)
}

func (logic *codeListOrcManagementLogic) UpdateCodeListName(ctx context.Context, param structure.UpdateCodeListOrcManagementNameParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()
		return logic.codeListOrcManagementRepo.UpdateCompanyName(ctx, tx, param)

	})
	if err != nil {
		return
	}

	return val.(structure.ResTenantManagementIDData), nil
}

// 启用状态
func (logic *codeListOrcManagementLogic) UpdateStatusEnable(ctx context.Context, param structure.EnableCodeListOrcManagementParam) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	return logic.codeListOrcManagementRepo.UpdateStatusEnable(ctx, tx, param)
}

// 禁用状态
func (logic *codeListOrcManagementLogic) UpdateStatusDisable(ctx context.Context, param structure.DisableCodeListOrcManagementParam) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	return logic.codeListOrcManagementRepo.UpdateStatusDisable(ctx, tx, param)
}

// 启用电子色卡状态
func (logic *codeListOrcManagementLogic) UpdateStatusEnableEleColorCard(ctx context.Context, param structure.EnableCodeListOrcManagementParam) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	return logic.codeListOrcManagementRepo.UpdateStatusEnableEleColorCard(ctx, tx, param)
}

// 禁用电子色卡状态
func (logic *codeListOrcManagementLogic) UpdateStatusDisableEleColorCard(ctx context.Context, param structure.DisableCodeListOrcManagementParam) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	return logic.codeListOrcManagementRepo.UpdateStatusDisableEleColorCard(ctx, tx, param)
}

// 启用禁用搜索图片状态
func (logic *codeListOrcManagementLogic) UpdateStatusSearchImage(ctx context.Context, param structure.EnableSearchImageParam) (err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	return logic.codeListOrcManagementRepo.UpdateStatusSearchImage(ctx, tx, param)
}

// 充值
func (logic *codeListOrcManagementLogic) Recharge(ctx context.Context, param structure.RechargeParam) (data structure.RechargeData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()

		data, err = logic.codeListOrcManagementRepo.Recharge(ctx, tx, param)
		if err != nil {
			return
		}

		return data, nil
	})

	if err != nil {
		return
	}

	return val.(structure.RechargeData), nil
}

// 电子色卡充值
func (logic *codeListOrcManagementLogic) EleColorCardServerRecharge(ctx context.Context, param structure.RechargeParam) (data structure.RechargeData, err error) {
	var (
		paramByte []byte
		val       interface{}
	)
	paramByte, err = json.Marshal(param)
	if err != nil {
		return
	}

	val, err = logic.singleFlight.Do(tools.GetRequestKey(logic.ginCtx, paramByte), func() (res interface{}, err error) {
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()

		// 调用相同的充值方法，后续可根据需要扩展电子色卡特有的充值逻辑
		data, err = logic.codeListOrcManagementRepo.EleColorCardServerRecharge(ctx, tx, param)
		if err != nil {
			return
		}

		return data, nil
	})

	if err != nil {
		return
	}

	return val.(structure.RechargeData), nil
}

// 充值记录
func (logic *codeListOrcManagementLogic) GetRechargeHistorys(ctx context.Context, query structure.RechargeHistoryListQuery) (list structure.GetRechargeHistoryListDataList, total int, err error) {
	// tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	return logic.codeListOrcManagementRepo.GetRechargeHistorys(ctx, tx, query)
}

// 判断功能是否过期
func (logic *codeListOrcManagementLogic) IsExpired(ctx context.Context, expireType common.RechargeType, tenantManagementId uint64) (isExpired bool, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	// 码单识别过期判断
	if expireType == common.RechargeTypeOcr {
		isExpired, err = logic.codeListOrcManagementRepo.IsOcrExpired(ctx, tx, tenantManagementId)
		if err != nil {
			return
		}
	}
	// 电子色卡过期判断
	if expireType == common.RechargeTypeEleColorCard {
		isExpired, err = logic.codeListOrcManagementRepo.IsEleColorCardExpired(ctx, tx, tenantManagementId)
		if err != nil {
			return
		}
	}
	// 搜索图片过期判断
	if expireType == common.RechargeTypeSearchImage {
		isExpired, err = logic.codeListOrcManagementRepo.IsSearchImageExpired(ctx, tx, tenantManagementId)
		if err != nil {
			return
		}
	}
	return
}

// 判断码单是否充值过
func (logic *codeListOrcManagementLogic) IsRecharged(ctx context.Context, tenantManagementId uint64) (isRecharged bool, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	return logic.codeListOrcManagementRepo.IsRecharged(ctx, tx, tenantManagementId)
}
