package product

import (
	"fmt"
	"github.com/gin-gonic/gin"
	cus_const "hcscm/common/product"
	system_consts "hcscm/common/system_consts"
	saleSys "hcscm/extern/pb/sale_system"
	dye_stock "hcscm/extern/pb/stock"
	"hcscm/model/mysql/mysql_base"
	mysqlSystem "hcscm/model/mysql/system"
	"hcscm/model/redis"
	"hcscm/server/system"
	svc "hcscm/service/product"
	structure "hcscm/structure/product"
	dye_structure "hcscm/structure/system"
	"hcscm/vars"
	"time"
)

// @Tags		【成品加工退货进仓】
// @Summary	添加成品加工退货进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.AddFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/addFpmProcessReturnInOrder [post]
func AddFpmProcessReturnInOrder(c *gin.Context) {
	var (
		q            = &structure.AddFpmInOrderParam{}
		data         = structure.AddFpmInOrderData{}
		svc          = svc.NewFpmInOrderService()
		err          error
		orderPrefix  mysqlSystem.OrderPrefix
		exist        bool
		sale_sys_svc = saleSys.NewSaleSystemClient()
		saleSysData  = saleSys.Res{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypeProcessReturn
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: q.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(tx)
	if err != nil {
		return
	}
	if !exist {
		q.OrderNoPre = vars.FpmProcessReturnInOrderPrefix
		if vars.UseSaleSystem {
			q.OrderNoPre = fmt.Sprintf("%s-%s-", q.OrderNoPre, saleSysData.Code)
		}
	} else {
		q.OrderNoPre = orderPrefix.FpmProcessReturnInOrder
		if orderPrefix.UseSaleSystem {
			q.OrderNoPre = fmt.Sprintf("%s-%s-", q.OrderNoPre, saleSysData.Code)
		}
	}
	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	更新成品加工退货进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrder [put]
func UpdateFpmProcessReturnInOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderParam{}
		data = structure.UpdateFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	q.InOrderType = cus_const.WarehouseGoodInTypeProcessReturn
	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	更新成品加工退货进仓业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderBusinessCloseParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderBusinessClose [put]
func UpdateFpmProcessReturnInOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderBusinessCloseParam{}
		data = structure.UpdateFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	更新成品加工退货进仓状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusWait [put]
func UpdateFpmProcessReturnInOrderStatusWait(c *gin.Context) {
	var (
		q           = &structure.UpdateFpmInOrderStatusParam{}
		data        = structure.UpdateFpmInOrderStatusData{}
		inOrderSvc  = svc.NewFpmInOrderService()
		stockSvc    = svc.NewStockProductService()
		err         error
		rLocks      = make(redis.LockForRedisList, 0)
		updateItems structure.UpdateStockProductDetailParamList
		dyeStockSvc = dye_stock.NewClientStockDyeingFabric()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		data, updateItems, _, err = inOrderSvc.UpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}
		err = dyeStockSvc.RefundStockQty(ctx, tx, &dye_structure.RefundStockRequest{OrderId: id, ChangeTime: time.Now()})
		if err != nil {
			return
		}
	}
	return
}

// @Tags		【成品加工退货进仓】
// @Summary	更新成品加工退货进仓状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusPass [put]
func UpdateFpmProcessReturnInOrderStatusPass(c *gin.Context) {
	var (
		q               = &structure.UpdateFpmInOrderStatusParam{}
		data            = structure.UpdateFpmInOrderStatusData{}
		inOrderSvc      = svc.NewFpmInOrderService()
		stockSvc        = svc.NewStockProductService()
		processOutSvc   = svc.NewFpmProcessOutOrderService()
		rLocks          = make(redis.LockForRedisList, 0)
		ids             map[uint64]uint64
		sumIds          map[uint64]uint64
		addItems        structure.AddStockProductDetailParamList
		err             error
		srcTypeUseDye   cus_const.StockTypeReworkV2
		dyeStockSvc     = dye_stock.NewClientStockDyeingFabric()
		orderData       = structure.GetFpmInOrderData{}
		processOutOrder = structure.GetFpmProcessOutOrderData{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		data, addItems, _, _, err = inOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		if len(addItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, addItems, nil)
			if err != nil {
				return
			}
			ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addItems)
			if err != nil {
				return
			}

			err = inOrderSvc.UpdateDetailStockDetailId(ctx, tx, ids, sumIds, id, false)
			if err != nil {
				return
			}
		}

		orderData, _ = inOrderSvc.Get(ctx, &structure.GetFpmInOrderQuery{Id: id})
		// 进染整库存
		for _, item := range orderData.ItemData {
			processOutOrder, err = processOutSvc.GetByItemID(ctx, item.QuoteOrderItemId)
			if err != nil {
				return
			}
			// 进染整库存
			if processOutOrder.OutOrderType == cus_const.WarehouseGoodOutTypeTypeRepair {
				srcTypeUseDye = cus_const.StockTypeReworkV2RepairUnhandle
			} else {
				srcTypeUseDye = cus_const.StockTypeReworkV2ProcessingUnhandle
			}
			saveDNFData := dye_structure.SaveDyeingFabricStockQtyParams{}
			{
				saveDNFData.SrcType = uint8(cus_const.SrcTypeFabricRtnEnterStock)
				saveDNFData.SrcId = orderData.Id
				saveDNFData.StockType = system_consts.OperateStockType(srcTypeUseDye)
				saveDNFData.Info = &dye_structure.StockDyeingFabricInfo{
					UnitId:          orderData.BizUnitId,
					FinishProductId: item.ProductId,
					CustomerId:      item.CustomerId,
					ColorId:         item.ProductColorId,
				}
				saveDNFData.PieceCount = item.InRoll
				saveDNFData.Weight = item.SettleWeight
				saveDNFData.ChangeDate = orderData.WarehouseInTime.Date()
				saveDNFData.Reason = orderData.Remark
				saveDNFData.SrcItemId = item.Id
			}
			// 扣库存(退货单只扣半成品库存)
			err = dyeStockSvc.ReduceDyeingFabricStockQty(ctx, tx, &saveDNFData)
			if err != nil {
				return
			}
		}
		err = dyeStockSvc.RefundStockQty(ctx, tx, &dye_structure.RefundStockRequest{OrderId: id, ChangeTime: time.Now()})
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	更新成品加工退货进仓状态-驳回
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusReject [put]
func UpdateFpmProcessReturnInOrderStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusReject(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	更新成品加工退货进仓状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/updateFpmProcessReturnInOrderStatusCancel [put]
func UpdateFpmProcessReturnInOrderStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusCancel(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	获取成品加工退货进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/getFpmProcessReturnInOrder [get]
func GetFpmProcessReturnInOrder(c *gin.Context) {
	var (
		q    = &structure.GetFpmInOrderQuery{}
		data = structure.GetFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品加工退货进仓】
// @Summary	获取成品加工退货进仓列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmProcessReturnInOrder/getFpmProcessReturnInOrderList [get]
func GetFpmProcessReturnInOrderList(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = cus_const.WarehouseGoodInTypeProcessReturn
	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}
