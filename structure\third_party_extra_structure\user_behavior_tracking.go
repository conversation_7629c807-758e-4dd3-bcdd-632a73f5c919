package third_party_extra_structure

import (
	"hcscm/structure/base_structure"
	"time"
)

// CreateUserBehaviorTrackingRequest 创建用户行为跟踪请求
type CreateUserBehaviorTrackingRequest struct {
	UserId       uint64 `json:"user_id" binding:"required"`
	ActionType   string `json:"action_type" binding:"required"`
	ActionTarget string `json:"action_target"`
	ActionData   string `json:"action_data"`
	IpAddress    string `json:"ip_address"`
	UserAgent    string `json:"user_agent"`
	SessionId    string `json:"session_id"`
	Duration     int64  `json:"duration"`
}

// GetUserBehaviorTrackingListRequest 获取用户行为跟踪列表请求
type GetUserBehaviorTrackingListRequest struct {
	base_structure.PageRequest
	UserId       uint64    `json:"user_id" form:"user_id"`
	ActionType   string    `json:"action_type" form:"action_type"`
	ActionTarget string    `json:"action_target" form:"action_target"`
	SessionId    string    `json:"session_id" form:"session_id"`
	StartTime    time.Time `json:"start_time" form:"start_time"`
	EndTime      time.Time `json:"end_time" form:"end_time"`
}

// GetUserBehaviorTrackingListResponse 获取用户行为跟踪列表响应
type GetUserBehaviorTrackingListResponse struct {
	base_structure.PageResponse
	List []UserBehaviorTrackingResponse `json:"list"`
}

// UserBehaviorTrackingResponse 用户行为跟踪响应
type UserBehaviorTrackingResponse struct {
	Id           uint64    `json:"id"`
	UserId       uint64    `json:"user_id"`
	ActionType   string    `json:"action_type"`
	ActionTarget string    `json:"action_target"`
	ActionData   string    `json:"action_data"`
	IpAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	SessionId    string    `json:"session_id"`
	Duration     int64     `json:"duration"`
	ActionTime   time.Time `json:"action_time"`
	CreateTime   time.Time `json:"create_time"`
}

// GetUserBehaviorStatisticsRequest 获取用户行为统计请求
type GetUserBehaviorStatisticsRequest struct {
	UserId    uint64    `json:"user_id" form:"user_id" binding:"required"`
	StartTime time.Time `json:"start_time" form:"start_time"`
	EndTime   time.Time `json:"end_time" form:"end_time"`
}

// GetUserBehaviorStatisticsResponse 获取用户行为统计响应
type GetUserBehaviorStatisticsResponse struct {
	Statistics map[string]int64 `json:"statistics"`
}
