package extern

import (
	"hcscm/config"
	"hcscm/extern/aliyun"
	"hcscm/extern/html2image"
	"hcscm/extern/pb/ocr"
	"hcscm/extern/printer"
	"hcscm/extern/rabbitmq"
	"hcscm/extern/upyun"
)

func Init(conf config.Config,
	user, password, host, port, dead, routingKey string, // rabbitmq
) {
	upyun.Init()
	html2image.Init()
	rabbitmq.Init(conf, user, password, host, port, dead, routingKey)
	aliyun.Init()
	printer.Init()
	ocr.Init(conf.Ocr.Host, conf.Ocr.Port, conf.Ocr.Env)
}

func Stop() {
	// wechat.Stop()
	// kingdee.Stop()
	// html2image.Stop()
	// upyun.Stop()
}
