package should_collect_order

import (
	"context"
	"fmt"
	common "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/grey_fabric_info"
	"hcscm/extern/pb/basic_data/info_basic_data"
	info_basic_pb "hcscm/extern/pb/basic_data/info_basic_data"
	basic_product_pb "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/raw_material"
	"hcscm/extern/pb/biz_unit"
	biz_pb "hcscm/extern/pb/biz_unit"
	emp_pb "hcscm/extern/pb/employee"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/should_collect_order"
	mysql "hcscm/model/mysql/should_collect_order/dao"
	structure_grey_fabric "hcscm/structure/basic_data/grey_fabric_info"
	structure_raw_matl "hcscm/structure/basic_data/raw_material"
	structure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
)

type ReportFormsRepo struct {
	tx *mysql_base.Tx
}

func NewReportFormsRepo(tx *mysql_base.Tx) *ReportFormsRepo {
	return &ReportFormsRepo{tx: tx}
}

func (r *ReportFormsRepo) GetCustomerOweMoneyList(ctx context.Context, req *structure.GetCustomerOweMoneyListQuery) (list structure.GetCustomerOweMoneyDataList, total int, err error) {
	var (
		items             = make(structure.GetCustomerOweMoneyDataList, 0)
		bizUnits          biz_unit.ResList
		customerOweMoneys model.CustomerOweMoneyList
	)

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	customerOweMoneys, total, err = model.FindCustomerOweMoneyList(r.tx, req, false)
	if err != nil {
		return
	}

	// 查询客户
	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", customerOweMoneys)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnits, total, err = bizUnitSvc.GetCustomerByQuery(ctx, biz_unit.Req{Ids: bizUnitIds})
	if err != nil {
		return
	}

	for _, customerOweMoney := range customerOweMoneys {
		bizUnit := bizUnits.Pick(customerOweMoney.CustomerId)
		item := structure.GetCustomerOweMoneyData{}
		item.SaleSystemId = bizUnit.SaleSystemID
		item.SaleSystemName = bizUnit.SaleSystemName
		item.CustomerId = bizUnit.Id
		item.CustomerCode = bizUnit.CustomCode
		item.CustomerName = bizUnit.Name
		item.SaleUserId = bizUnit.SaleUserId
		item.SaleUserName = bizUnit.SaleUserName
		item.Period = tools.Cent(customerOweMoney.Period)
		item.ShouldCollectMoney = tools.Cent(customerOweMoney.ShouldCollectMoney)
		item.CollectPrice = tools.Cent(customerOweMoney.CollectPrice)
		item.RemoveMoney = tools.Cent(customerOweMoney.RemoveMoney)
		item.DiscountMoney = tools.Cent(customerOweMoney.DiscountMoney)
		item.ChargebackMoney = tools.Cent(customerOweMoney.ChargebackMoney)
		item.UncollectMoney = tools.Cent(customerOweMoney.UncollectMoney)
		item.WriteOffPrice = tools.Cent(customerOweMoney.WriteOffPrice)
		item.BalancePrice = tools.Cent(customerOweMoney.BalancePrice)
		item.LastAdvancePrice = tools.Cent(customerOweMoney.LastAdvancePrice)
		item.AdvancePrice = tools.Cent(customerOweMoney.AdvancePrice)
		item.AdvanceUsedPrice = tools.Cent(customerOweMoney.AdvanceUsedPrice)
		item.BalanceAdvancePrice = tools.Cent(customerOweMoney.BalanceAdvancePrice)
		item.EndPeriod = tools.Cent(customerOweMoney.BalancePrice - customerOweMoney.BalanceAdvancePrice)

		item.SettleType = common_system.SettleType(bizUnit.SettleType)
		item.SettleTypeName = bizUnit.SettleTypeName
		item.CustomCycle = bizUnit.SettleCycle
		item.CreditLimit = tools.Cent(bizUnit.CreditLimit)
		// 本期结余为0则为已收齐，反之为未收齐
		if item.BalancePrice == 0 {
			item.OweStatus = common.OweStatusNo
			item.OweStatusName = common.OweStatusNo.String()
		} else {
			item.OweStatus = common.OweStatusYes
			item.OweStatusName = common.OweStatusYes.String()
		}
		items = append(items, item)
	}
	list = items
	return
}

func (r *ReportFormsRepo) GetCustomerOweMoneySummary(ctx context.Context, req *structure.GetCustomerOweMoneyListQuery) (summary structure.GetCustomerOweMoneyData, err error) {
	var (
		customerOweMoneys model.CustomerOweMoneyList
		customerOweMoney  model.CustomerOweMoney
		count             int
	)

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	customerOweMoneys, count, err = model.FindCustomerOweMoneyList(r.tx, req, true)
	if err != nil {
		return
	}
	if count != 0 {
		customerOweMoney = customerOweMoneys.List()[0]
		summary.Period = tools.Cent(customerOweMoney.Period)
		summary.ShouldCollectMoney = tools.Cent(customerOweMoney.ShouldCollectMoney)
		summary.CollectPrice = tools.Cent(customerOweMoney.CollectPrice)
		summary.RemoveMoney = tools.Cent(customerOweMoney.RemoveMoney)
		summary.DiscountMoney = tools.Cent(customerOweMoney.DiscountMoney)
		summary.ChargebackMoney = tools.Cent(customerOweMoney.ChargebackMoney)
		summary.UncollectMoney = tools.Cent(customerOweMoney.UncollectMoney)
		summary.WriteOffPrice = tools.Cent(customerOweMoney.WriteOffPrice)
		summary.BalancePrice = tools.Cent(customerOweMoney.BalancePrice)
		summary.LastAdvancePrice = tools.Cent(customerOweMoney.LastAdvancePrice)
		summary.AdvancePrice = tools.Cent(customerOweMoney.AdvancePrice)
		summary.AdvanceUsedPrice = tools.Cent(customerOweMoney.AdvanceUsedPrice)
		summary.BalanceAdvancePrice = tools.Cent(customerOweMoney.BalanceAdvancePrice)
		summary.EndPeriod = tools.Cent(customerOweMoney.BalancePrice - customerOweMoney.BalanceAdvancePrice)
	}
	return
}

func (r *ReportFormsRepo) GetCustomerReconciliationList(ctx context.Context, req *structure.GetCustomerReconciliationListQuery) (list structure.GetCustomerReconciliationDataList, summary structure.GetCustomerReconciliationData, total int, err error) {
	var (
		items                     = make(structure.GetCustomerReconciliationDataList, 0)
		lastCustomerOweMoney      model.CustomerOweMoney
		customerReconciliations   model.CustomerReconciliationList
		balance                   tools.Cent
		actuallyCollectOrderItems model.ActuallyCollectOrderItemList
	)

	if !req.StartTime.IsYMDZero() {
		var (
			lastCustomerOweMoneys model.CustomerOweMoneyList
			count                 int
		)
		// 查询上期结余信息
		lastCustomerOweMoneys, count, err = model.FindCustomerOweMoneyList(r.tx, &structure.GetCustomerOweMoneyListQuery{CustomerId: req.CustomerId, EndTime: req.StartTime}, false)
		if err != nil {
			return
		}
		if count != 0 {
			lastCustomerOweMoney = lastCustomerOweMoneys.List()[0]
		}
		balance += tools.Cent(lastCustomerOweMoney.BalancePrice) - tools.Cent(lastCustomerOweMoney.BalanceAdvancePrice)
	}

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	customerReconciliations, total, err = model.FindCustomerReconciliationList(r.tx, req)
	if err != nil {
		return
	}

	var (
		productIdMap              = set.NewUint64Set()
		rawMaterialIdMap          = set.NewUint64Set()
		greyFabricIdMap           = set.NewUint64Set()
		productColorIdMap         = set.NewUint64Set()
		rawMaterialColorIdMap     = set.NewUint64Set()
		greyFabricColorIdMap      = set.NewUint64Set()
		measurementUnitNameIdMap  = set.NewUint64Set()
		actuallyCollectOrderIdMap = set.NewUint64Set()
		productIds                = make([]uint64, 0)
		productColorIds           = make([]uint64, 0)
		rawMaterialIds            = make([]uint64, 0)
		rawMaterialColorIds       = make([]uint64, 0)
		greyFabricIds             = make([]uint64, 0)
		greyFabricColorIds        = make([]uint64, 0)
		rawMaterials              map[uint64]*structure_raw_matl.RawMatlInfo
		rawMaterialColors         map[uint64]*raw_material.ColorInfoItem
		productItems              map[uint64]*basic_product_pb.ProductRes
		productColorItems         map[uint64]*basic_product_pb.ProductColorRes
		greyFabricItems           map[uint64]*structure_grey_fabric.GetGreyFabricInfoData
		greyFabricColors          map[uint64][2]string
		measurementUnitName       map[uint64]string
		bizNameMap                map[uint64]string
	)
	for _, customerReconciliation := range customerReconciliations {
		switch customerReconciliation.CollectType {
		case common.CollectTypeProductSale: // 销售送货单
			productIdMap.Add(customerReconciliation.MaterialID)
			productColorIdMap.Add(customerReconciliation.ColorID)
		case common.CollectTypeProductReturn: // 销售退货单
			productIdMap.Add(customerReconciliation.MaterialID)
			productColorIdMap.Add(customerReconciliation.ColorID)
		case common.CollectTypeRawMaterial: // 原料销售应收单
			rawMaterialIdMap.Add(customerReconciliation.MaterialID)
			rawMaterialColorIdMap.Add(customerReconciliation.ColorID)
		case common.CollectTypeRawMaterialReturn: // 原料销售退货应收单
			rawMaterialIdMap.Add(customerReconciliation.MaterialID)
			rawMaterialColorIdMap.Add(customerReconciliation.ColorID)
		case common.CollectTypeGreyFabric: // 坯布销售应收单
			greyFabricIdMap.Add(customerReconciliation.MaterialID)
			greyFabricColorIdMap.Add(customerReconciliation.ColorID)
		case common.CollectTypeGreyFabricReturn: // 坯布销售退货应收单
			greyFabricIdMap.Add(customerReconciliation.MaterialID)
			greyFabricColorIdMap.Add(customerReconciliation.ColorID)
		case common.CollectTypeOther: // 其他应收单
		case common.CollectTypeActual: // 实收单
		case common.CollectTypeAdvance: // 预收单
		default:

		}
		measurementUnitNameIdMap.Add(customerReconciliation.MeasurementUnitId)
		// 实收2预收3
		if customerReconciliation.BaseType == 2 {
			actuallyCollectOrderIdMap.Add(customerReconciliation.OrderId)
		}
	}

	actuallyCollectOrderItems, err = mysql.FindActuallyCollectOrderItemByParenTIDs(r.tx, actuallyCollectOrderIdMap.List())
	if err != nil {
		return
	}

	productIds = productIdMap.List()
	productColorIds = productColorIdMap.List()
	rawMaterialIds = rawMaterialIdMap.List()
	rawMaterialColorIds = rawMaterialColorIdMap.List()
	greyFabricIds = greyFabricIdMap.List()
	greyFabricColorIds = greyFabricColorIdMap.List()
	// 原料
	rawMaterialSvc := raw_material.NewClientRawMatlInfoService()
	rawMaterials, err = rawMaterialSvc.GetRawMatlNameByIds(ctx, rawMaterialIds)
	if err != nil {
		return
	}
	// 原料颜色
	rawColorListMap := raw_material.NewRawMaterialColorClient()
	rawMaterialColors, err = rawColorListMap.GetColorInfoByIds(ctx, rawMaterialColorIds)
	if err != nil {
		return
	}
	// 成品
	productSvc := basic_product_pb.NewProductClient()
	productItems, err = productSvc.GetProductMapByIds(ctx, productIds)
	if err != nil {
		return
	}
	// 成品颜色
	productColorSvc := basic_product_pb.NewProductColorClient()
	productColorItems, err = productColorSvc.GetProductColorMapByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	// 坯布
	greyFabricSvc := grey_fabric_info.NewGreyFabricInfoClient()
	greyFabricItems, err = greyFabricSvc.GetGreyFabricInfoMapList(ctx, greyFabricIds)
	if err != nil {
		return
	}
	// 坯布颜色
	greyFabricColorSvc := info_basic_data.NewInfoProductGrayFabricColorClient()
	greyFabricColors, err = greyFabricColorSvc.GetInfoProductGrayFabricColorCodeNameByIds(ctx, greyFabricColorIds)
	if err != nil {
		return
	}

	// 计量单位
	measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err = measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitNameIdMap.List())
	if err != nil {
		return
	}

	bizPB := biz_pb.NewClientBizUnitService()
	bizNameMap, _ = bizPB.GetBizUnitNameByIds(ctx, []uint64{req.CustomerId})

	// 如果页数为1才显示上期结余
	if req.Page <= 1 {
		lastItem := structure.GetCustomerReconciliationData{}
		lastItem.CollectTypeName = "上期结余"
		lastItem.Balance += balance
		items = append(items, lastItem)
		var (
			thisCustomerOweMoneys model.CustomerOweMoneyList
			thisCustomerOweMoney  model.CustomerOweMoney
			count                 int
		)
		thisCustomerOweMoneys, count, err = model.FindCustomerOweMoneyList(r.tx, &structure.GetCustomerOweMoneyListQuery{CustomerId: req.CustomerId, StartTime: req.StartTime, EndTime: req.EndTime}, true)
		if err != nil {
			return
		}
		if count != 0 {
			thisCustomerOweMoney = thisCustomerOweMoneys.List()[0]
		}
		summary.LastBalancePrice += balance
		summary.ShouldCollectMoney += tools.Cent(thisCustomerOweMoney.ShouldCollectMoney)
		summary.CollectedMoney += tools.Cent(thisCustomerOweMoney.CollectPrice + thisCustomerOweMoney.AdvancePrice - thisCustomerOweMoney.AdvanceUsedPrice) // 收款金额修改为实收+预收
		summary.Balance += tools.Cent(thisCustomerOweMoney.BalancePrice)
		summary.EndPeriod += tools.Cent(thisCustomerOweMoney.BalancePrice - thisCustomerOweMoney.BalanceAdvancePrice) // 累计欠款为实际欠款金额
		summary.CustomerName = bizNameMap[req.CustomerId]
		summary.TenantManagementName = metadata.GetLoginInfo(ctx).GetTenantManagementName()
		summary.StartTime = req.StartTime
		summary.EndTime = req.EndTime
	} else {
		// todo:上一页汇总有问题，需要修改
		var (
			customerReconciliationBalance model.FinalCustomerReconciliationBalanceList
		)
		// 如果页数不为1则需要算出目前总数，然后根据总数获取前面页数的余额总数继续累加下去
		count := (req.Page - 1) * req.Size
		req.Page = 1
		req.Size = count

		customerReconciliationBalance, _, err = model.GetCustomerReconciliationBalance(r.tx, req)
		if err != nil {
			return
		}

		balance += tools.Cent(customerReconciliationBalance[0].Balance)
	}

	// var orderId uint64 = 0
	for _, customerOweMoney := range customerReconciliations {
		item := structure.GetCustomerReconciliationData{}
		if customerOweMoney.CollectType == common.CollectTypeProductSale || customerOweMoney.CollectType == common.CollectTypeProductReturn { // 成品
			if productItem, ok := productItems[customerOweMoney.MaterialID]; ok {
				item.Code = productItem.FinishProductCode
				item.Name = productItem.FinishProductName
			}
			if productColorItem, ok := productColorItems[customerOweMoney.ColorID]; ok {
				item.ProductColorCode = productColorItem.ProductColorCode
				item.ProductColorName = productColorItem.ProductColorName
			}
		}
		if customerOweMoney.CollectType == common.CollectTypeRawMaterial || customerOweMoney.CollectType == common.CollectTypeRawMaterialReturn { // 原料
			if rawMaterial, ok := rawMaterials[customerOweMoney.MaterialID]; ok {
				item.Code = rawMaterial.Code
				item.Name = rawMaterial.Name
			}
			if rawMaterialColor, ok := rawMaterialColors[customerOweMoney.ColorID]; ok {
				item.ProductColorCode = rawMaterialColor.Code
				item.ProductColorName = rawMaterialColor.Name
			}
		}
		if customerOweMoney.CollectType == common.CollectTypeGreyFabric || customerOweMoney.CollectType == common.CollectTypeGreyFabricReturn { // 坯布
			if greyFabricItem, ok := greyFabricItems[customerOweMoney.MaterialID]; ok {
				item.Code = greyFabricItem.Code
				item.Name = greyFabricItem.Name
			}
			item.ProductColorCode = greyFabricColors[customerOweMoney.ColorID][0]
			item.ProductColorName = greyFabricColors[customerOweMoney.ColorID][1]
		}
		if customerOweMoney.CollectType == common.CollectTypeOther { // 其他应收单
			item.Code = customerOweMoney.Code
			item.Name = customerOweMoney.Name
			item.ProductColorCode = customerOweMoney.ProductColorCode
			item.ProductColorName = customerOweMoney.ProductColorName
		}
		item.OrderId = customerOweMoney.OrderId
		item.OrderNo = customerOweMoney.OrderNo
		item.OrderTime = tools.MyTime(customerOweMoney.OrderTime)
		if customerOweMoney.BaseType == 1 {
			item.CollectType = customerOweMoney.CollectType
			item.CollectTypeName = customerOweMoney.CollectType.String()
			item.SettlePrice = tools.Cent(customerOweMoney.SettlePrice)
			// 获取其他收款放到实收单显示
			// customerOweMoney获取汇总时已经变换符号，如果取单据数据此处为减法
			// if orderId != item.OrderId {
			// 	balance += tools.Cent(customerOweMoney.SettlePrice) - tools.Cent(customerOweMoney.RemoveMoney) - tools.Cent(customerOweMoney.DiscountPrice) - tools.Cent(customerOweMoney.ChargebackMoney)
			// 	orderId = item.OrderId
			// } else {
			// 	balance += tools.Cent(customerOweMoney.SettlePrice)
			// }
			balance += tools.Cent(customerOweMoney.SettlePrice)
		} else if customerOweMoney.BaseType == 2 {
			item.CollectType = common.CollectTypeActual
			item.CollectTypeName = "实收款单"
			item.ActuallyCollectPrice = tools.Cent(customerOweMoney.SettlePrice)
			// 核销汇总
			var (
				discountPrice   int
				chargebackMoney int
				offsetPrice     int
			)
			_actuallyCollectOrderItems := actuallyCollectOrderItems.PickByParentID(customerOweMoney.OrderId)
			for _, actuallyCollectOrderItem := range _actuallyCollectOrderItems {
				discountPrice += actuallyCollectOrderItem.DiscountPrice    // 折扣金额
				chargebackMoney += actuallyCollectOrderItem.DeductionPrice // 扣款金额
				offsetPrice += actuallyCollectOrderItem.OffsetPrice        // 优惠金额
			}
			item.DiscountPrice = tools.Cent(discountPrice)
			item.ChargebackMoney = tools.Cent(chargebackMoney)
			item.RemoveMoney = tools.Cent(offsetPrice)
			balance -= item.ActuallyCollectPrice + item.DiscountPrice + item.ChargebackMoney + item.RemoveMoney
		} else {
			item.CollectType = common.CollectTypeAdvance
			item.CollectTypeName = "预收款单"
			item.ActuallyCollectPrice = tools.Cent(customerOweMoney.SettlePrice)
			balance -= tools.Cent(customerOweMoney.SettlePrice)
		}
		item.DyelotNumber = customerOweMoney.DyelotNumber

		// if customerOweMoney.CollectType == common.CollectTypeRawMaterial ||
		// 	customerOweMoney.CollectType == common.CollectTypeRawMaterialReturn {
		// 	// 原料保存时候件数没有*100 返回的时候为了统一，*100
		// 	item.Roll = tools.Hundred(customerOweMoney.Roll * vars.PieceCount)
		// } else {
		// 	item.Roll = tools.Hundred(customerOweMoney.Roll)
		// }
		item.Roll = tools.Hundred(customerOweMoney.Roll)
		item.Weight = tools.TenThousand(customerOweMoney.Weight)
		if customerOweMoney.MeasurementUnitId != 0 {
			item.MeasurementUnitId = customerOweMoney.MeasurementUnitId
			item.MeasurementUnitName = measurementUnitName[customerOweMoney.MeasurementUnitId]
		} else {
			item.MeasurementUnitName = "-"
		}
		item.IsWithTaxRate = tools.YesOrNo(customerOweMoney.IsWithTaxRate)
		item.TaxRate = tools.Hundred(customerOweMoney.TaxRate)
		item.SalePrice = tools.TenThousand(customerOweMoney.SalePrice)
		item.OtherPrice = tools.Cent(customerOweMoney.OtherPrice)
		// item.RemoveMoney = tools.Cent(customerOweMoney.RemoveMoney)
		// item.DiscountPrice = tools.Cent(customerOweMoney.DiscountPrice)
		// item.ChargebackMoney = tools.Cent(customerOweMoney.ChargebackMoney)
		item.Remark = customerOweMoney.Remark
		item.Balance = balance
		// 检查是否有空字符串
		item.CheckNullString()
		items = append(items, item)
	}
	list = items
	return
}

func (r *ReportFormsRepo) MPGetCustomerReconciliationList(ctx context.Context, req *structure.GetCustomerReconciliationListQuery) (list structure.MPGetCustomerReconciliationDataList, summary structure.MPGetCustomerReconciliationData, total int, err error) {
	var (
		orderList                 = make(structure.MPGetCustomerReconciliationDataList, 0)
		lastCustomerOweMoney      model.CustomerOweMoney
		thisCustomerOweMoney      model.CustomerOweMoney
		customerReconciliations   model.CustomerReconciliationList
		shouldCollectOrderDetails model.ShouldCollectOrderDetailList
		actuallyCollectOrderItems model.ActuallyCollectOrderItemList
		balance                   int
	)

	if !req.StartTime.IsYMDZero() {
		var (
			lastCustomerOweMoneys model.CustomerOweMoneyList
			count                 int
		)
		// 查询上期结余信息
		lastCustomerOweMoneys, count, err = model.FindCustomerOweMoneyList(r.tx, &structure.GetCustomerOweMoneyListQuery{CustomerId: req.CustomerId, EndTime: req.StartTime}, true)
		if err != nil {
			return
		}
		if count != 0 {
			lastCustomerOweMoney = lastCustomerOweMoneys.List()[0]
		}
		balance += lastCustomerOweMoney.BalancePrice - lastCustomerOweMoney.BalanceAdvancePrice
	}

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	customerReconciliations, total, err = model.FindCustomerReconciliationOrderList(r.tx, req)
	if err != nil {
		return
	}

	var (
		shouldCollectOrderIds     = set.NewUint64Set()
		actuallyCollectOrderIdMap = set.NewUint64Set()
	)
	for _, customerReconciliation := range customerReconciliations {
		if customerReconciliation.BaseType == 1 {
			shouldCollectOrderIds.Add(customerReconciliation.OrderId)
		}
		// 实收2预收3
		if customerReconciliation.BaseType == 2 {
			actuallyCollectOrderIdMap.Add(customerReconciliation.OrderId)
		}
	}

	shouldCollectOrderDetails, err = mysql.FindShouldCollectOrderDetailByParentIDs(r.tx, shouldCollectOrderIds.List())
	if err != nil {
		return
	}

	// 小程序显示不参与计算，可以显示其他收款
	actuallyCollectOrderItems, err = mysql.FindActuallyCollectOrderItemBySrcOrderIDs(r.tx, shouldCollectOrderIds.List())
	// actuallyCollectOrderItems, err = mysql.FindActuallyCollectOrderItemByParenTIDs(r.tx, actuallyCollectOrderIdMap.List())
	if err != nil {
		return
	}

	var (
		materialIdMap      = set.NewUint64Set()
		materialColorIdMap = set.NewUint64Set()
		materialIds        []uint64
		materialColorIds   []uint64
		rawMaterials       map[uint64]*structure_raw_matl.RawMatlInfo
		rawMaterialColors  map[uint64]*raw_material.ColorInfoItem
		productItem        map[uint64]*basic_product_pb.ProductRes
		productColorItem   map[uint64]*basic_product_pb.ProductColorRes
		greyFabricItem     map[uint64]*structure_grey_fabric.GetGreyFabricInfoData
		bizNameMap         map[uint64]string
	)
	for _, shouldCollectOrderDetail := range shouldCollectOrderDetails {
		materialIdMap.Add(shouldCollectOrderDetail.MaterialId)
		materialColorIdMap.Add(shouldCollectOrderDetail.ProductColorId)
	}
	materialIds = materialIdMap.List()
	materialColorIds = materialColorIdMap.List()
	// 原料
	rawMaterialSvc := raw_material.NewClientRawMatlInfoService()
	rawMaterials, err = rawMaterialSvc.GetRawMatlNameByIds(ctx, materialIds)
	if err != nil {
		return
	}
	// 原料颜色
	rawColorListMap := raw_material.NewRawMaterialColorClient()
	rawMaterialColors, err = rawColorListMap.GetColorInfoByIds(ctx, materialColorIds)
	if err != nil {
		return
	}
	// 成品
	productSvc := basic_product_pb.NewProductClient()
	productItem, err = productSvc.GetProductMapByIds(ctx, materialIds)
	if err != nil {
		return
	}
	// 成品颜色
	productColorSvc := basic_product_pb.NewProductColorClient()
	productColorItem, err = productColorSvc.GetProductColorMapByIds(ctx, materialColorIds)
	if err != nil {
		return
	}
	// 坯布
	greyFabricSvc := grey_fabric_info.NewGreyFabricInfoClient()
	greyFabricItem, err = greyFabricSvc.GetGreyFabricInfoMapList(ctx, materialIds)
	if err != nil {
		return
	}
	// 坯布颜色
	greyFabricColorSvc := info_basic_data.NewInfoProductGrayFabricColorClient()
	greyFabricColor, err := greyFabricColorSvc.GetInfoProductGrayFabricColorCodeNameByIds(ctx, materialColorIds)
	if err != nil {
		return
	}

	// 计量单位
	measurementUnitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", customerReconciliations, shouldCollectOrderDetails)
	measurementUnitNameSvc := info_basic_data.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	bizPB := biz_pb.NewClientBizUnitService()
	bizNameMap, _ = bizPB.GetBizUnitNameByIds(ctx, []uint64{req.CustomerId})

	// 如果页数为1才显示上期结余
	if req.Page <= 1 {
		var (
			thisCustomerOweMoneys model.CustomerOweMoneyList
			count                 int
		)
		thisCustomerOweMoneys, count, err = model.FindCustomerOweMoneyList(r.tx, &structure.GetCustomerOweMoneyListQuery{CustomerId: req.CustomerId, StartTime: req.StartTime, EndTime: req.EndTime}, true)
		if err != nil {
			return
		}
		if count != 0 {
			thisCustomerOweMoney = thisCustomerOweMoneys.List()[0]
		}
		summary.LastBalancePrice += balance
		summary.ShouldCollectMoney += thisCustomerOweMoney.ShouldCollectMoney
		summary.CollectedMoney += thisCustomerOweMoney.CollectPrice + thisCustomerOweMoney.AdvancePrice - thisCustomerOweMoney.AdvanceUsedPrice // 收款金额修改为实收+预收
		summary.Balance += thisCustomerOweMoney.BalancePrice
		summary.EndPeriod += thisCustomerOweMoney.BalancePrice - thisCustomerOweMoney.BalanceAdvancePrice // 累计欠款为实际欠款金额
		summary.CustomerName = bizNameMap[req.CustomerId]
		summary.TenantManagementName = metadata.GetLoginInfo(ctx).GetTenantManagementName()
		summary.StartTime = req.StartTime
		summary.EndTime = req.EndTime
	} else {
		var (
			customerReconciliationBalance model.FinalCustomerReconciliationBalanceList
		)
		// 如果页数不为1则需要算出目前总数，然后根据总数获取前面页数的余额总数继续累加下去
		count := (req.Page - 1) * req.Size
		req.Page = 1
		req.Size = count

		customerReconciliationBalance, _, err = model.GetCustomerReconciliationBalance(r.tx, req)
		if err != nil {
			return
		}

		balance += customerReconciliationBalance[0].Balance
	}

	for _, customerOweMoney := range customerReconciliations {
		var itemList = make(structure.OrderDetailList, 0)
		o := structure.MPGetCustomerReconciliationData{}
		o.OrderId = customerOweMoney.OrderId
		o.OrderNo = customerOweMoney.OrderNo
		o.OrderTime = tools.MyTime(customerOweMoney.OrderTime)
		o.OtherPrice = customerOweMoney.OtherPrice
		o.Remark = customerOweMoney.Remark
		if customerOweMoney.BaseType == 1 {
			o.CollectType = customerOweMoney.CollectType
			if o.CollectType == common.CollectTypeProductSale {
				o.CollectTypeName = "成品销售单"
			}
			if o.CollectType == common.CollectTypeProductReturn {
				o.CollectTypeName = "成品退货单"
			}
			if o.CollectType == common.CollectTypeRawMaterial {
				o.CollectTypeName = "原料销售应收单"
			}
			if o.CollectType == common.CollectTypeRawMaterialReturn {
				o.CollectTypeName = "原料销售退货应收单"
			}
			if o.CollectType == common.CollectTypeGreyFabric {
				o.CollectTypeName = "坯布销售应收单"
			}
			if o.CollectType == common.CollectTypeGreyFabricReturn {
				o.CollectTypeName = "坯布销售退货应收单"
			}
			if o.CollectType == common.CollectTypeOther {
				o.CollectTypeName = "其他应收单"
			}

			// 核销汇总
			_actuallyCollectOrderItems := actuallyCollectOrderItems.PickBySrcOrderID(customerOweMoney.OrderId)
			for _, actuallyCollectOrderItem := range _actuallyCollectOrderItems {
				o.ActuallyCollectPrice += actuallyCollectOrderItem.ActuallyCollectPrice // 实收金额
				o.RemoveMoney += actuallyCollectOrderItem.OffsetPrice                   // 优惠金额
				o.DiscountPrice += actuallyCollectOrderItem.DiscountPrice               // 折扣金额
				o.ChargebackMoney += actuallyCollectOrderItem.DeductionPrice            // 扣款金额
				o.WriteOffPrice += actuallyCollectOrderItem.WriteOffPrice               // 核销金额
			}
			// 获取单据的使用减法
			o.TotalPrice = customerOweMoney.SettlePrice - o.RemoveMoney - o.DiscountPrice - o.ChargebackMoney

			_shouldCollectOrderDetails := shouldCollectOrderDetails.PickByShouldCollectOrderId(customerOweMoney.OrderId)
			for _, detail := range _shouldCollectOrderDetails {
				item := structure.OrderDetail{}
				item.DyelotNumber = detail.DyelotNumber
				item.Roll = detail.Roll
				item.RollString = fmt.Sprintf("%v%v", detail.Roll/vars.Roll, "匹")
				if o.CollectType == common.CollectTypeRawMaterial || o.CollectType == common.CollectTypeRawMaterialReturn {
					// 原料
					item.RollString = fmt.Sprintf("%v%v", detail.Roll/vars.PieceCount, "件")
					if rawMaterial, ok := rawMaterials[detail.MaterialId]; ok {
						item.Code = rawMaterial.Code
						item.Name = rawMaterial.Name
					}
					if rawMaterialColor, ok := rawMaterialColors[detail.ProductColorId]; ok {
						item.ColorCode = rawMaterialColor.Code
						item.ColorName = rawMaterialColor.Name
					}
				} else if o.CollectType == common.CollectTypeGreyFabric || o.CollectType == common.CollectTypeGreyFabricReturn {
					// 坯布
					if greyFabric, ok := greyFabricItem[detail.MaterialId]; ok {
						item.Code = greyFabric.Code
						item.Name = greyFabric.Name
					}
					item.ColorCode = greyFabricColor[detail.ProductColorId][0]
					item.ColorName = greyFabricColor[detail.ProductColorId][1]
				} else if o.CollectType == common.CollectTypeProductSale || o.CollectType == common.CollectTypeProductReturn {
					// 成品
					if product, ok := productItem[detail.MaterialId]; ok {
						item.Code = product.FinishProductCode
						item.Name = product.FinishProductName
					}
					if productColor, ok := productColorItem[detail.ProductColorId]; ok {
						item.ColorCode = productColor.ProductColorCode
						item.ColorName = productColor.ProductColorName
					}
				} else {
					item.Code = detail.Code
					item.Name = detail.Name
					item.ColorCode = detail.ProductColorCode
					item.ColorName = detail.ProductColorName
				}
				item.Weight = detail.Weight
				if detail.MeasurementUnitId != 0 {
					item.MeasurementUnitId = detail.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[detail.MeasurementUnitId]
				} else {
					item.MeasurementUnitId = 0
					item.MeasurementUnitName = "kg"
				}
				item.SalePrice = detail.SalePrice
				item.TotalPrice = detail.SettlePrice
				itemList = append(itemList, item)
			}
		} else if customerOweMoney.BaseType == 2 {
			o.TotalPrice = customerOweMoney.SettlePrice
			o.CollectType = common.CollectTypeActual
			o.CollectTypeName = "实收款单"
			// 核销汇总
			// _actuallyCollectOrderItems := actuallyCollectOrderItems.PickByParentID(customerOweMoney.OrderId)
			// for _, actuallyCollectOrderItem := range _actuallyCollectOrderItems {
			// 	o.RemoveMoney += actuallyCollectOrderItem.OffsetPrice        // 优惠金额
			// 	o.DiscountPrice += actuallyCollectOrderItem.DiscountPrice    // 折扣金额
			// 	o.ChargebackMoney += actuallyCollectOrderItem.DeductionPrice // 扣款金额
			// }
		} else {
			o.TotalPrice = customerOweMoney.SettlePrice
			o.CollectType = common.CollectTypeAdvance
			o.CollectTypeName = "预收款单"
		}
		o.ItemList = itemList
		orderList = append(orderList, o)
	}
	list = orderList
	return
}

func (r *ReportFormsRepo) FindSaleSimpleReportList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data []*structure.GetSaleSimpleReportData, total int, err error) {

	var (
		dataV2 = make([]*structure.GetSaleSimpleReportData, 0)
	)
	list, total, err := mysql.FindSaleSimpleReportListV2(r.tx, q)
	if err != nil {
		return
	}

	// 找成品
	productMap, _ := basic_product_pb.NewProductClient().
		GetProductMapByIds(ctx, mysql_base.GetUInt64List(list, "material_id"))
	// 找颜色
	colorMap, _ := basic_product_pb.NewProductColorClient().
		GetProductColorItemByIds(ctx, mysql_base.GetUInt64List(list, "product_color_id"))
	// 找计量单位
	mUnitNameMap, _ := info_basic_pb.NewInfoBaseMeasurementUnitClient().
		GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(list, "measurement_unit_id"))

	if err != nil {
		return
	}
	for _, report := range list {
		o := &structure.GetSaleSimpleReportData{}
		report.BuildResp(o)
		if product := productMap[report.MaterialId]; product != nil {
			o.MaterialCode = product.FinishProductCode
			o.MaterialName = product.FinishProductName
		}
		o.ProductColorCode = colorMap[report.ProductColorId][0]
		o.ProductColorName = colorMap[report.ProductColorId][1]
		o.MeasurementUnitName = mUnitNameMap[report.MeasurementUnitId]
		dataV2 = append(dataV2, o)
	}
	data = dataV2
	return
}

func (r *ReportFormsRepo) FindSaleSimpleReportGroupCustomerList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data []*structure.GetSaleSimpleReportGroupCustomerData, total int, err error) {

	var (
		dataV2 = make([]*structure.GetSaleSimpleReportGroupCustomerData, 0)
	)
	list, total, err := mysql.FindSaleSimpleReportGroupCustomerList(r.tx, q)
	if err != nil {
		return
	}

	bizNameMap, _ := biz_pb.NewClientBizUnitService().
		GetBizUnitNameByIds(ctx, mysql_base.GetUInt64List(list, "biz_unit_id"))

	if err != nil {
		return
	}
	for _, report := range list {
		o := &structure.GetSaleSimpleReportGroupCustomerData{}
		report.BuildResp(o)
		o.CustomerName = bizNameMap[o.CustomerID]
		dataV2 = append(dataV2, o)
	}
	data = dataV2
	return
}

func (r *ReportFormsRepo) FindSaleSimpleReportGroupMaterialList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data []*structure.GetSaleSimpleReportGroupMaterialData, total int, err error) {

	var (
		dataV2 = make([]*structure.GetSaleSimpleReportGroupMaterialData, 0)
	)

	list, total, err := mysql.FindSaleSimpleReportGroupMaterialList(r.tx, q)
	if err != nil {
		return
	}

	// 编号，名称，成品工艺，成品成分，幅宽，克重
	productMap, _, _ := basic_product_pb.NewProductClient().
		GetProductByIds(ctx, mysql_base.GetUInt64List(list, "product_id"))

	if err != nil {
		return
	}
	for _, report := range list {
		o := &structure.GetSaleSimpleReportGroupMaterialData{}
		report.BuildResp(o)
		o.MaterialCode = productMap[report.MaterialId][0]
		o.MaterialName = productMap[report.MaterialId][1]
		o.MaterialCodeAndName = o.MaterialCode + o.MaterialName
		dataV2 = append(dataV2, o)
	}
	data = dataV2
	return
}

func (r *ReportFormsRepo) FindSaleSimpleReportGroupMaterialDetailList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data []*structure.GetSaleSimpleReportGroupMaterialDetailData, total int, err error) {

	var (
		dataV2 = make([]*structure.GetSaleSimpleReportGroupMaterialDetailData, 0)
	)

	list, total, err := mysql.FindSaleSimpleReportGroupMaterialDetailList(r.tx, q)
	if err != nil {
		return
	}

	colorMap, _ := basic_product_pb.NewProductColorClient().GetProductColorItemByIds(ctx, mysql_base.GetUInt64List(list, "product_color_id"))

	if err != nil {
		return
	}
	for _, report := range list {
		o := &structure.GetSaleSimpleReportGroupMaterialDetailData{}
		report.BuildDetailResp(o)
		o.ColorCode = colorMap[report.ProductColorId][0]
		o.ColorName = colorMap[report.ProductColorId][1]
		o.ColorCodeAndName = o.ColorCode + o.ColorName
		dataV2 = append(dataV2, o)
	}
	data = dataV2
	return
}

func (r *ReportFormsRepo) FindSaleSimpleReportGroupSellerList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data []*structure.GetSaleSimpleReportGroupSellerData, total int, err error) {

	var (
		dataV2 = make([]*structure.GetSaleSimpleReportGroupSellerData, 0)
	)
	list, total, err := mysql.FindSaleSimpleReportGroupSellerList(r.tx, q)
	if err != nil {
		return
	}

	empNameMap, _ := emp_pb.NewClientEmployeeService().
		GetEmployeeNameByIds(ctx, mysql_base.GetUInt64List(list, "emp_id"))

	if err != nil {
		return
	}
	for _, report := range list {
		o := &structure.GetSaleSimpleReportGroupSellerData{}
		report.BuildResp(o)
		o.SellerName = empNameMap[o.SellerId]
		dataV2 = append(dataV2, o)
	}
	data = dataV2
	return
}

// 近期数据（Days传需要多少天）
func (r *ReportFormsRepo) FindRecentlyDayDataList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data []*structure.GetSaleSimpleReportGroupDayData, err error) {
	var (
		dataV2 = make([]*structure.GetSaleSimpleReportGroupDayData, 0)
		// dateMap   = make(map[string]string)
		reportMap = make(map[string]*structure.GetSaleSimpleReportGroupDayData)
		dataMap   = make(map[string][]uint64, 0)
	)
	list, _, err := mysql.FindRecentlyDayDataList(r.tx, q)
	if err != nil {
		return
	}

	_list, _, err := mysql.SearchAllShouldCollectOrder(r.tx, &structure.GetShouldCollectOrderListQuery{
		StartAuditDate: tools.QueryTime(q.OrderDateEnd.ToTime().AddDate(0, 0, -q.Days).Format("2006-01-02")),
		EndAuditDate:   q.OrderDateEnd,
	})

	for _, order := range _list {
		dataF := order.AuditDate.Format("2006-01-02")
		dataMap[dataF] = append(dataMap[dataF], order.CustomerId)
	}

	for _, report := range list {
		o := &structure.GetSaleSimpleReportGroupDayData{}

		// 有一些天是没有数据的，需要手动插入0数据
		report.BuildResp(o)
		reportMap[report.DayDate] = o
		// dataV2 = append(dataV2, o)
	}

	// 有一些天是没有数据的，需要手动插入0数据
	for i := q.Days; i > 0; i-- {
		dateStr := q.OrderDateEnd.ToTimeYMD().AddDate(0, 0, -i+1).Format("2006-01-02")
		if report := reportMap[dateStr]; report != nil {
			customerIds := dataMap[dateStr]
			dm := set.NewUint64Set()
			dm.AddList(customerIds)
			report.CustomerCount = len(dm.List())
			dataV2 = append(dataV2, report)
		} else {
			o := &structure.GetSaleSimpleReportGroupDayData{}
			o.DayDate = dateStr
			dataV2 = append(dataV2, o)
		}
	}

	if err != nil {
		return
	}

	data = dataV2
	return
}

func (r *ReportFormsRepo) FindRecentlyBetweenDayData(ctx context.Context, req *structure.QuerySaleSimpleReportListParam) (
	data *structure.GetSaleSimpleTotalData, err error) {
	dataV2 := &structure.GetSaleSimpleTotalData{}
	list, _, err := mysql.FindRecentlyBetweenDayData(r.tx, req)
	if err != nil {
		return
	}
	for _, v := range list {
		dataV2.Price = v.Price
		dataV2.Roll = v.Roll
		dataV2.OrderCount = v.OrderCount
		dataV2.CustomerCount = len(v.CustomerStr.ToUint64())
	}
	data = dataV2
	return
}

// 经营分析
func (r *ReportFormsRepo) GetBusinessAnalysis(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data *structure.GetBusinessAnalysisData, err error) {
	var (
		dataV2         = &structure.GetBusinessAnalysisData{}
		detailDataList = structure.GetSaleSumReportDataList{}
		totalIncome    int
		totalCost      int
		otherIncome    int
		otherCost      int
	)
	// 主营业务收入（汇总实收）
	incomeList, err := mysql.GetBusinessAnalysisMainIncome(r.tx, q)
	if err != nil {
		return
	}
	for _, detail := range incomeList {
		if detail.ActuallyCollectOrderType == common_system.ActuallyOrderTypeActually {
			totalIncome += detail.ActuallyCollectPrice // 实收金额
		} else if detail.ActuallyCollectOrderType == common_system.ActuallyOrderTypeOther {
			otherIncome += detail.ActuallyCollectPrice
		}
	}

	// 主营业务成本（汇总实付）
	payList, err := mysql.GetBusinessAnalysisMainCost(r.tx, q)
	if err != nil {
		return
	}
	for _, pay := range payList {
		if pay.ActuallyPayOrderType == common_system.ActuallyOrderTypeActually {
			totalCost += pay.ActuallyPayPrice
		} else if pay.ActuallyPayOrderType == common_system.ActuallyOrderTypeOther {
			otherCost += pay.ActuallyPayPrice
		}
	}

	dataV2.MainBusinessIncome = totalIncome                              // 业务收入
	dataV2.MainBusinessCost = totalCost                                  // 业务收入
	dataV2.OtherIncome = otherIncome                                     // 其他收入
	dataV2.OtherCost = otherCost                                         // 其他支出
	dataV2.NetProfit = totalIncome + otherIncome - totalCost - otherCost // 净利

	// 销售送货单未审核数据（创建时间筛）
	dlOrderList, count, err := mysql.SearchShouldCollectOrderV2(r.tx, &structure.GetShouldCollectOrderListQuery{
		CreateTimeStart:  q.OrderDateStart,
		CreateTimeEnd:    q.OrderDateEnd,
		SaleSystemId:     q.SaleSystemId,
		AuditStatusSimpe: common_system.OrderStatusPendingAudit,
		CollectType:      common.CollectTypeProductSale,
	})
	if count > 0 {
		for _, order := range dlOrderList {
			if dataV2.WaitAuditOrderNos == "" {
				dataV2.WaitAuditOrderNos = order.OrderNo
			} else {
				dataV2.WaitAuditOrderNos = dataV2.WaitAuditOrderNos + "，" + order.OrderNo
			}
		}
		data = dataV2
		// return
	}

	// 销售送货单已审核数据（创建时间筛）
	detailList, err := mysql.GetShouldCollectOrderDetailGroupMeasurementUnitId(r.tx, q)
	if err != nil {
		return
	}
	// 计量单位
	munitMap, _ := info_basic_pb.NewInfoBaseMeasurementUnitClient().
		GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(detailList, "measurement_unit_id"))

	for _, detail := range detailList {
		detailResp := &structure.GetSaleSumReportData{}
		detail.BuildResp(detailResp)
		detailResp.MeasurementUnitName = munitMap[detail.MeasurementUnitId]
		detailDataList = append(detailDataList, detailResp)
	}

	dataV2.ItemData = detailDataList
	data = dataV2

	return
}

// 毛利分析
// 产品销售毛利分析（物料）
func (r *ReportFormsRepo) GetGrossProfitGroupMaterialList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data *structure.GetGrossProfitGroupMaterialDataList, total int, err error) {

	// 目前系统为:什么计量单位进入就按什么计量单位出去，

	var (
		srcIds             = set.NewUint64Set()
		sumStockIds        = set.NewUint64Set()
		list               model.GrossProfitGroupMaterialList
		listSrcIds         = []model.GrossProfitFieldWithPayable{}
		listSumStockIds    = []model.GrossProfitFieldWithPayable{}
		srcIdPriceMap      = make(map[uint64]int)
		sumStockIdPriceMap = make(map[uint64]int)
		// sellerNameMap      = make(map[uint64]string)
		getDataList = structure.GetGrossProfitGroupMaterialDataList{}
	)
	list, total, err = mysql.GetGrossProfitGroupMaterialList(r.tx, q)

	for _, gp := range list {
		for _, srcId := range gp.SrcIds.ToUint64() {
			if srcId > 0 {
				srcIds.Add(srcId)
			}
		}
		for _, stockId := range gp.StockIds.ToUint64() {
			if stockId > 0 {
				sumStockIds.Add(stockId)
			}
		}
	}

	// 去找应付数据
	tools.FinishVoid(
		func() {
			listSrcIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySrcIds:  true,
				SrcIds:         srcIds.List(),
				OrderDateStart: q.OrderDateStart,
				OrderDateEnd:   q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSrcIds {
				srcIdPriceMap[v.SrcId] += v.Price
			}
		}, func() {
			listSumStockIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySumStockIds: true,
				SumStockIds:        sumStockIds.List(),
				OrderDateStart:     q.OrderDateStart,
				OrderDateEnd:       q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSumStockIds {
				sumStockIdPriceMap[v.SumStockId] = v.Price
			}
		})

	// 查单位
	mUnitMap, _ := info_basic_pb.NewInfoBaseMeasurementUnitClient().
		GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(list, "measurement_unit_id"))
	// 查成品名 // 编号，名称，成品工艺，成品成分，幅宽，克重
	productMap, _, _ := basic_product_pb.NewProductClient().
		GetProductByIds(ctx, mysql_base.GetUInt64List(list, "material_id"))

	for _, material := range list {
		getData := &structure.GetGrossProfitGroupMaterialData{}
		for _, srcId := range material.SrcIds.ToUint64() {
			getData.OutTotalCost += srcIdPriceMap[srcId]
		}
		for _, stockId := range material.StockIds.ToUint64() {
			getData.OutTotalCost += sumStockIdPriceMap[stockId]
		}
		material.BuildResp(getData)
		getData.MeasurementUnitName = mUnitMap[material.MeasurementUnitId]
		getData.MaterialCode = productMap[material.MaterialId][0]
		getData.MaterialName = productMap[material.MaterialId][1]
		getData.MaterialCodeAndName = getData.MaterialCode + getData.MaterialName
		getDataList = append(getDataList, getData)
	}
	data = &getDataList
	return
}

// 产品销售毛利分析（颜色）
func (r *ReportFormsRepo) GetGrossProfitGroupColorList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	data *structure.GetGrossProfitGroupColorDataList, total int, err error) {

	var (
		srcIds             = set.NewUint64Set()
		sumStockIds        = set.NewUint64Set()
		list               model.GrossProfitGroupColorList
		listSrcIds         = []model.GrossProfitFieldWithPayable{}
		listSumStockIds    = []model.GrossProfitFieldWithPayable{}
		srcIdPriceMap      = make(map[uint64]int)
		sumStockIdPriceMap = make(map[uint64]int)
		// sellerNameMap      = make(map[uint64]string)
		getDataList = structure.GetGrossProfitGroupColorDataList{}
	)

	//
	list, total, err = mysql.GetGrossProfitGroupColorList(r.tx, q)

	for _, gp := range list {
		for _, srcId := range gp.SrcIds.ToUint64() {
			if srcId > 0 {
				srcIds.Add(srcId)
			}
		}
		for _, stockId := range gp.StockIds.ToUint64() {
			if stockId > 0 {
				sumStockIds.Add(stockId)
			}
		}
	}

	// 查单位
	mUnitMap, _ := info_basic_pb.NewInfoBaseMeasurementUnitClient().
		GetInfoBaseMeasurementUnitNameByIds(ctx, mysql_base.GetUInt64List(list, "measurement_unit_id"))
	// 查成品名 // 编号，名称，成品工艺，成品成分，幅宽，克重
	productMap, _, _ := basic_product_pb.NewProductClient().
		GetProductByIds(ctx, mysql_base.GetUInt64List(list, "material_id"))
	// 成品颜色
	colorMap, _ := basic_product_pb.NewProductColorClient().
		GetProductColorItemByIds(ctx, mysql_base.GetUInt64List(list, "color_id"))

	// 去找应付数据
	tools.FinishVoid(
		func() {
			listSrcIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySrcIds:  true,
				SrcIds:         srcIds.List(),
				OrderDateStart: q.OrderDateStart,
				OrderDateEnd:   q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSrcIds {
				srcIdPriceMap[v.SrcId] += v.Price
			}
		}, func() {
			listSumStockIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySumStockIds: true,
				SumStockIds:        sumStockIds.List(),
				OrderDateStart:     q.OrderDateStart,
				OrderDateEnd:       q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSumStockIds {
				sumStockIdPriceMap[v.SumStockId] = v.Price
			}
		})

	for _, material := range list {
		getData := &structure.GetGrossProfitGroupColorData{}
		for _, srcId := range material.SrcIds.ToUint64() {
			getData.OutTotalCost += srcIdPriceMap[srcId]
		}
		for _, stockId := range material.StockIds.ToUint64() {
			getData.OutTotalCost += sumStockIdPriceMap[stockId]
		}

		material.BuildResp(getData)
		getData.MeasurementUnitName = mUnitMap[material.MeasurementUnitId]
		getData.MaterialCode = productMap[material.MaterialId][0]
		getData.MaterialName = productMap[material.MaterialId][1]
		getData.MaterialCodeAndName = getData.MaterialCode + getData.MaterialName
		color := colorMap[material.ColorId]
		getData.ColorCode = color[0]
		getData.ColorName = color[1]
		getData.ColorCodeAndName = getData.ColorCode + getData.ColorName
		getDataList = append(getDataList, getData)
	}
	data = &getDataList
	return
}

// 客户毛利分析
func (r *ReportFormsRepo) GetGrossProfitGroupCustomerList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	list *structure.GetGrossProfitGroupCustomerDataList, count int, err error) {
	var (
		srcIds             = set.NewUint64Set()
		sumStockIds        = set.NewUint64Set()
		bizIds             = set.NewUint64Set()
		listSrcIds         = []model.GrossProfitFieldWithPayable{}
		listSumStockIds    = []model.GrossProfitFieldWithPayable{}
		srcIdPriceMap      = make(map[uint64]int)
		sumStockIdPriceMap = make(map[uint64]int)
		bizNameMap         = make(map[uint64][2]string)
		getDataList        = structure.GetGrossProfitGroupCustomerDataList{}
	)

	// 模糊查客户ids出来
	q.CustomerIds, _ = biz_pb.NewClientBizUnitService().GetBizUnitIdsByNameLike(ctx, q.CustomerName)

	shouldCollectGPList, count, err := mysql.GetGrossProfitGroupCustomerList(r.tx, q)
	if err != nil {
		return
	}

	for _, gp := range shouldCollectGPList {
		for _, srcId := range gp.SrcIds.ToUint64() {
			if srcId > 0 {
				srcIds.Add(srcId)
			}
		}
		for _, stockId := range gp.StockIds.ToUint64() {
			if stockId > 0 {
				sumStockIds.Add(stockId)
			}
		}
		bizIds.Add(gp.CustomerID)
	}
	// 去找应付数据
	tools.FinishVoid(
		func() {
			listSrcIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySrcIds:  true,
				SrcIds:         srcIds.List(),
				OrderDateStart: q.OrderDateStart,
				OrderDateEnd:   q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSrcIds {
				srcIdPriceMap[v.SrcId] += v.Price
			}
		}, func() {
			listSumStockIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySumStockIds: true,
				SumStockIds:        sumStockIds.List(),
				OrderDateStart:     q.OrderDateStart,
				OrderDateEnd:       q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSumStockIds {
				sumStockIdPriceMap[v.SumStockId] = v.Price
			}
		}, func() {
			bizNameMap, _ = biz_pb.NewClientBizUnitService().GetBizUnitByIds(ctx, bizIds.List())
		})

	for _, gp := range shouldCollectGPList {
		getData := structure.GetGrossProfitGroupCustomerData{}

		getData.CustomerID = gp.CustomerID
		getData.CustomerName = bizNameMap[gp.CustomerID][1]
		// getData.OutTotalCost = gp.OutTotalCost
		getData.SaleTotalPrice = gp.StockSaleTotalPrice + gp.TransferSaleTotalPrice
		getData.GrossProfit = getData.SaleTotalPrice - getData.OutTotalCost

		for _, srcId := range gp.SrcIds.ToUint64() {
			getData.OutTotalCost += srcIdPriceMap[srcId]
		}
		for _, stockId := range gp.StockIds.ToUint64() {
			getData.OutTotalCost += sumStockIdPriceMap[stockId]
		}
		getDataList = append(getDataList, &getData)
	}
	list = &getDataList
	return
}

// 销售员毛利表
func (r *ReportFormsRepo) GetGrossProfitGroupSellerList(ctx context.Context, q *structure.QuerySaleSimpleReportListParam) (
	list *structure.GetGrossProfitGroupSellerDataList, count int, err error) {
	var (
		srcIds             = set.NewUint64Set()
		sumStockIds        = set.NewUint64Set()
		sellerIds          = set.NewUint64Set()
		listSrcIds         = []model.GrossProfitFieldWithPayable{}
		listSumStockIds    = []model.GrossProfitFieldWithPayable{}
		srcIdPriceMap      = make(map[uint64]int)
		sumStockIdPriceMap = make(map[uint64]int)
		sellerNameMap      = make(map[uint64]string)
		getDataList        = structure.GetGrossProfitGroupSellerDataList{}
	)

	// 模糊查询销售员ids
	empResp, _ := emp_pb.NewClientEmployeeService().GetEmployeeByLikeCodeOrName(ctx, q.SellerName)
	q.SellerIds = empResp.EmployeeIDs

	shouldCollectGPList, count, err := mysql.GetGrossProfitGroupSellerList(r.tx, q)
	if err != nil {
		return
	}

	for _, gp := range shouldCollectGPList {
		for _, srcId := range gp.SrcIds.ToUint64() {
			if srcId > 0 {
				srcIds.Add(srcId)
			}
		}
		for _, stockId := range gp.StockIds.ToUint64() {
			if stockId > 0 {
				sumStockIds.Add(stockId)
			}
		}
		sellerIds.Add(gp.SellerID)
	}
	// 去找应付数据
	tools.FinishVoid(
		func() {
			listSrcIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySrcIds:  true,
				SrcIds:         srcIds.List(),
				OrderDateStart: q.OrderDateStart,
				OrderDateEnd:   q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSrcIds {
				srcIdPriceMap[v.SrcId] += v.Price
			}
		}, func() {
			listSumStockIds, _, err = mysql.GetGrossProfitFieldWithPayableListFromPayable(r.tx, &structure.QuerySaleSimpleReportListParam{
				IsQuerySumStockIds: true,
				SumStockIds:        sumStockIds.List(),
				OrderDateStart:     q.OrderDateStart,
				OrderDateEnd:       q.OrderDateEnd,
			})
			if err != nil {
				return
			}
			for _, v := range listSumStockIds {
				sumStockIdPriceMap[v.SumStockId] = v.Price
			}
		}, func() {
			sellerNameMap, _ = emp_pb.NewClientEmployeeService().GetEmployeeNameByIds(ctx, sellerIds.List())
		})

	for _, gp := range shouldCollectGPList {
		getData := structure.GetGrossProfitGroupSellerData{}

		getData.SellerID = gp.SellerID
		getData.SellerName = sellerNameMap[gp.SellerID]
		// getData.OutTotalCost = gp.OutTotalCost
		getData.SaleTotalPrice = gp.StockSaleTotalPrice + gp.TransferSaleTotalPrice
		getData.GrossProfit = getData.SaleTotalPrice - getData.OutTotalCost

		for _, srcId := range gp.SrcIds.ToUint64() {
			getData.OutTotalCost += srcIdPriceMap[srcId]
		}
		for _, stockId := range gp.StockIds.ToUint64() {
			getData.OutTotalCost += sumStockIdPriceMap[stockId]
		}
		getDataList = append(getDataList, &getData)
	}
	list = &getDataList
	return
}
