package qywx

import (
	"context"
	"github.com/gin-gonic/gin"
	"hcscm/server/system"
	svc "hcscm/service/qywx"
	structure "hcscm/structure/qywx"
	system_structure "hcscm/structure/system"
	"hcscm/tools/metadata"
)

//		@Tags		【企业微信】
//		@Summary	添加代开发应用
//		@Security	ApiKeyAuth
//		@accept		application/json
//		@Produce	application/json
//		@Param		Platform	header		int	true	"终端ID"
//	 @Param		body	body		structure.CreateTobeDevelopedAppRequest{}	true	"入参"
//		@Success	200			{object}	structure.TobeDevelopedAppResponse{}
//		@Router		/hcscm/admin/v1/qywx/tobe_developed_app [post]
func CreateTobeDevelopedApp(c *gin.Context) {
	var (
		q    structure.CreateTobeDevelopedAppRequest
		data structure.TobeDevelopedAppResponse
		svc  = svc.NewQYWXService()
		err  error
	)

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.CreateTobeDevelopedApp(context.Background(), q)
	if err != nil {
		return
	}

	return
}

//			@Tags		【企业微信】
//			@Summary	代开发应用列表
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	    @Param		page				query		int		false	"page"
//	   @Param		size				query		int		false	"size"
//	   @Param		offset				query		int		false	"offset"
//	   @Param		limit				query		int		false	"limit"
//	   @Param		query_str				query		string		false	"企业名称或者id"
//			@Success	200			{object}	structure.GetTobeDevelopedAppListResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app [get]
func GetTobeDevelopedAppList(c *gin.Context) {
	var (
		q    structure.GetTobeDevelopedAppListRequest
		data structure.GetTobeDevelopedAppListResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.GetTobeDevelopedAppList(ctx, q)
	if err != nil {
		return
	}

	return
}

//		@Tags		【企业微信】
//		@Summary	账套绑定待开发应用
//		@Security	ApiKeyAuth
//		@accept		application/json
//		@Produce	application/json
//		@Param		Platform	header		int	true	"终端ID"
//	 @Param		body	body		structure.TobeDevelopedAppBindTenantRequest{}	true	"入参"
//		@Success	200			{object}	structure.TobeDevelopedAppResponse{}
//		@Router		/hcscm/admin/v1/qywx/tobe_developed_app/bind_tenant [post]
func TobeDevelopedAppBindTenant(c *gin.Context) {
	var (
		q    structure.TobeDevelopedAppBindTenantRequest
		data structure.TobeDevelopedAppResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.TobeDevelopedAppBindTenant(ctx, q)
	if err != nil {
		return
	}

	return
}

//		@Tags		【企业微信】
//		@Summary	账套取消绑定待开发应用
//		@Security	ApiKeyAuth
//		@accept		application/json
//		@Produce	application/json
//		@Param		Platform	header		int	true	"终端ID"
//	 @Param		body	body		structure.TobeDevelopedAppCancelBindTenantRequest{}	true	"入参"
//		@Success	200			{object}	structure.TenantManagementResponse{}
//		@Router		/hcscm/admin/v1/qywx/tobe_developed_app/cancel_bind_tenant [put]
func TobeDevelopedAppCancelBindTenant(c *gin.Context) {
	var (
		q    structure.TobeDevelopedAppCancelBindTenantRequest
		data structure.TenantManagementResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.TobeDevelopedAppCancelBindTenant(ctx, q)
	if err != nil {
		return
	}

	return
}

//		@Tags		【企业微信】
//		@Summary	绑定阿布机器人
//		@Security	ApiKeyAuth
//		@accept		application/json
//		@Produce	application/json
//		@Param		Platform	header		int	true	"终端ID"
//	 @Param		body	body		structure.UpdateTobeDevelopedAppRequest{}	true	"入参"
//		@Success	200			{object}	structure.TobeDevelopedAppResponse{}
//		@Router		/hcscm/admin/v1/qywx/tobe_developed_app [put]
func TobeDevelopedAppBindRobot(c *gin.Context) {
	var (
		q    structure.UpdateTobeDevelopedAppRequest
		data structure.TobeDevelopedAppResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.TobeDevelopedAppBindRobot(ctx, q)
	if err != nil {
		return
	}

	return
}

//		@Tags		【企业微信】
//		@Summary	创建群机器人
//		@Security	ApiKeyAuth
//		@accept		application/json
//		@Produce	application/json
//		@Param		Platform	header		int	true	"终端ID"
//	 @Param		body	body		structure.CreateQYWXRobotRequest{}	true	"入参"
//		@Success	200			{object}	structure.QYWXRobotResponse{}
//		@Router		/hcscm/admin/v1/qywx/tobe_developed_app/qywx_robot [post]
func CreateQYWXRobot(c *gin.Context) {
	var (
		q    structure.CreateQYWXRobotRequest
		data structure.QYWXRobotResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.CreateQYWXRobot(ctx, q)
	if err != nil {
		return
	}

	return
}

//		@Tags		【企业微信】
//		@Summary	删除群机器人
//		@Security	ApiKeyAuth
//		@accept		application/json
//		@Produce	application/json
//		@Param		Platform	header		int	true	"终端ID"
//	 @Param		body	body		structure.DeleteQYWXRobotRequest{}	true	"入参"
//		@Success	200			{object}	structure.QYWXRobotResponse{}
//		@Router		/hcscm/admin/v1/qywx/tobe_developed_app/qywx_robot [delete]
func DeleteQYWXRobot(c *gin.Context) {
	var (
		q    structure.DeleteQYWXRobotRequest
		data structure.QYWXRobotResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.DeleteQYWXRobot(ctx, q)
	if err != nil {
		return
	}

	return
}

//			@Tags		【企业微信】
//			@Summary	企微应用配置列表
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	    @Param		page				query		int		false	"page"
//	   @Param		size				query		int		false	"size"
//	   @Param		offset				query		int		false	"offset"
//	   @Param		limit				query		int		false	"limit"
//	   @Param		tenant_name				query		string		false	"账套名称"
//	   @Param		phone				query		string		false	"联系电话"
//	   @Param		contact_person				query		string		false	"联系人"
//	   @Param		tobe_developed_app_name				query		string		false	"应用名称"
//			@Success	200			{object}	structure.GetQYWXBoundListResponse{}
//			@Router		/hcscm/admin/v1/qywx [get]
func GetQYWXBoundList(c *gin.Context) {
	var (
		q    structure.GetQYWXBoundListRequest
		data structure.GetQYWXBoundListResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.GetQYWXBoundList(ctx, q)
	if err != nil {
		return
	}

	return
}

//			@Tags		【企业微信】
//			@Summary	企微应用配置列表
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	   @Param		tobe_developed_app_id				query		int		false	"代开发应用id"
//			@Success	200			{object}	structure.GetQYWXRobotResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/qywx_robot [get]
func GetQYWXRobotList(c *gin.Context) {
	var (
		q    structure.GetQYWXRobotRequest
		data structure.GetQYWXRobotResponse
		svc  = svc.NewQYWXService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.GetQYWXRobotList(ctx, q)
	if err != nil {
		return
	}

	return
}

//			@Tags		【企业微信】
//			@Summary	企微员工列表
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	    @Param		page				query		int		false	"page"
//	   @Param		size				query		int		false	"size"
//	   @Param		offset				query		int		false	"offset"
//	   @Param		limit				query		int		false	"limit"
//	   @Param		name				query		string		false	"名称"
//			@Success	200			{object}	structure.GetQYWXUsersResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/qywx_users [get]
func GetQYWXUsers(c *gin.Context) {
	var (
		q    structure.GetQYWXUsersRequest
		data structure.GetQYWXUsersResponse
		svc  = svc.NewQYWXService()
		err  error
		info metadata.IOperator
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	info, ok = system.GetLoginInfo(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, &q)
	if err != nil {
		return
	}

	data, err = svc.GetQYWXUsers(ctx, info, q)
	if err != nil {
		return
	}

	return
}

//			@Tags		【企业微信】
//			@Summary	企微客户列表
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	    @Param		page				query		int		false	"page"
//	   @Param		size				query		int		false	"size"
//	   @Param		offset				query		int		false	"offset"
//	   @Param		limit				query		int		false	"limit"
//	   @Param		name				query		string		false	"名称"
//	   @Param		all_customers				query		bool		false	"是否查找全部"
//			@Success	200			{object}	structure.QYWXGetCustomersResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/qywx_customers [get]
func GetQYWXCustomers(c *gin.Context) {
	var (
		req  structure.QYWXGetCustomersRequest
		resp structure.QYWXGetCustomersResponse
		err  error
		info metadata.IOperator
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	info, ok = system.GetLoginInfo(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, resp)
	}()

	err = system.ShouldBind(c, &req)
	if err != nil {
		return
	}

	resp, err = svc.NewQYWXService().GetCustomerByFollowUserID(ctx, info, req)
}

//			@Tags		【企业微信】
//			@Summary	企微群聊列表
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	    @Param		page				query		int		false	"page"
//	   @Param		size				query		int		false	"size"
//	   @Param		offset				query		int		false	"offset"
//	   @Param		limit				query		int		false	"limit"
//	   @Param		name				query		string		false	"名称"
//			@Success	200			{object}	structure.QYWXGetGroupChatListResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/qywx_group_chat_list [get]
func GetGroupChatList(c *gin.Context) {
	var (
		req  structure.QYWXGetGroupChatListRequest
		resp structure.QYWXGetGroupChatListResponse
		err  error
		info metadata.IOperator
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	info, ok = system.GetLoginInfo(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, resp)
	}()

	err = system.ShouldBind(c, &req)
	if err != nil {
		return
	}

	resp, err = svc.NewQYWXService().GetGroupChatList(ctx, info, req)
}

// @Tags		【企业微信】
// @Summary	获取企业微信标签列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		Platform	header		int	true	"终端ID"
// @Param		Authorization	header		string									true	"token"
// @Param		page				query		int		false	"page"
// @Param		size				query		int		false	"size"
// @Param		offset				query		int		false	"offset"
// @Param		limit				query		int		false	"limit"
// @Param		external_user_id				query		string		false	"企业微信客户ID"
// @Param		follow_user_id				query		string		false	"企业微信员工ID"
// @Success	200			{object}	structure.GetCorpTagGroupDataList{}
// @Router		/hcscm/admin/v1/qywx/tobe_developed_app/corp_tag_list [get]
// @Router		/hcscm/h5/v1/qywx/tobe_developed_app/corp_tag_list [get]
func GetCorpTagGroupList(c *gin.Context) {
	var (
		req   structure.GetCorpTagGroupListQuery
		list  = make(structure.GetCorpTagGroupDataList, 0)
		err   error
		total int
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, &req)
	if err != nil {
		return
	}

	list, total, err = svc.NewQYWXService().GetPurchaserClueCorpTagGroupDataList(ctx, req)
	if err != nil {
		return
	}
}

//			@Tags		【企业微信】
//			@Summary	添加外部群
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	   @Param		name				query		string		true	"名称"
//			@Success	200			{object}	system_structure.ResponseData{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/add_qywx_group_chat [post]
func AddGroupChat(c *gin.Context) {
	var (
		req  structure.QYWXAddGroupChatRequest
		resp system_structure.ResponseData
		err  error
		info metadata.IOperator
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	info, ok = system.GetLoginInfo(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, resp)
	}()

	err = system.ShouldBind(c, &req)
	if err != nil {
		return
	}

	resp, err = svc.NewQYWXService().AddGroupChat(ctx, info, req)
}

//			@Tags		【企业微信】
//			@Summary	获取企微客户绑定客户
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	   @Param		qywx_customer_id				query		string		false	"客户id"
//	   @Param		qywx_group_chat_id				query		string		false	"群id"
//			@Success	200			{object}	structure.QYWXCustomerBindRelResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/get_qywx_bind_rel [get]
//			@Router		/hcscm/h5/v1/qywx/tobe_developed_app/get_qywx_bind_rel [get]
func GetQYWXCustomerBindRel(c *gin.Context) {
	var (
		req  structure.QYWXCustomerBindRelRequest
		resp structure.QYWXCustomerBindRelResponse
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, resp)
	}()

	err = system.ShouldBind(c, &req)
	if err != nil {
		return
	}

	resp, err = svc.NewQYWXService().GetQYWXCustomerBindRel(ctx, req)
}

//			@Tags		【企业微信】
//			@Summary	获取企微签名
//			@Security	ApiKeyAuth
//			@accept		application/json
//			@Produce	application/json
//			@Param		Platform	header		int	true	"终端ID"
//	   @Param		nonceStr				query		string		true	"id"
//	   @Param		timestamp				query		int64		true	"id"
//	   @Param		url				query		string		true	"id"
//			@Success	200			{object}	structure.QYWXSignatureResponse{}
//			@Router		/hcscm/admin/v1/qywx/tobe_developed_app/get_qywx_signature [get]
func GetQYWXSignature(c *gin.Context) {
	var (
		req  structure.QYWXSignatureRequest
		resp structure.QYWXSignatureResponse
		err  error
		info metadata.IOperator
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	info, ok = system.GetLoginInfo(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, resp)
	}()

	err = system.ShouldBind(c, &req)
	if err != nil {
		return
	}

	resp, err = svc.NewQYWXService().GetQYWXSignature(ctx, info, req)
}
