package mysql_base

import (
	"context"
	"hcscm/model/redis"
	"hcscm/vars"
	"io/ioutil"
	"os"
	"strings"
	"time"

	"gorm.io/gorm"
)

const (
	SqlPathLocal = "docs/sql"
	SqlPathTest  = "sql/"
)

var sqlStr = ""

func SqlInit(_db *gorm.DB) {
	var (
		tplFileList []string
		err         error
	)
	ctx := context.Background()
	if vars.Env == "local" {
		tplFileList, err = GetAllTplFile(SqlPathLocal, nil)
		if err != nil {
			return
		}
	} else {
		tplFileList, err = GetAllTplFile(SqlPathTest, nil)
		if err != nil {
			return
		}
	}
	fileCount := len(tplFileList)
	if fileCount == 0 {
		return
	}
	for _, path := range tplFileList {
		// 尝试从Redis缓存获取SQL内容
		cacheKey := "sql_file:" + path
		cacheContent, err := redis.GetClient(ctx).Get(cacheKey).Result()
		if err == nil && cacheContent != "" {
			sqlStr = cacheContent
		} else {
			// 缓存不存在，读取文件
			c, ioErr := ioutil.ReadFile(path)
			if ioErr != nil {
				return
			}
			sqlStr = string(c)
			// 将内容存入Redis，设置5分钟过期时间
			redis.GetClient(ctx).Set(cacheKey, sqlStr, 5*time.Minute)
		}
		if sqlStr == "" {
			continue
		}
		splitSQLs := strings.Split(sqlStr, ";")
		for _, splitSQL := range splitSQLs {
			if splitSQL == "" || strings.Trim(splitSQL, "\r\n") == "" {
				continue
			}
			if _db != nil {
				err = _db.Exec(strings.Trim(splitSQL, "\n")).Error
			} else {
				err = getConn(nil).Exec(strings.Trim(splitSQL, "\n")).Error
			}
			if err != nil {
				continue
			}
		}
	}
	return
}

// 获取 pathName 文件夹下所有 sql 文件
func GetAllTplFile(pathName string, fileList []string) ([]string, error) {
	files, err := os.ReadDir(pathName)
	for _, fi := range files {
		if fi.IsDir() {
			fileList, err = GetAllTplFile(pathName+"/"+fi.Name(), fileList)
			if err != nil {
				return nil, err
			}
		} else {
			if strings.HasSuffix(fi.Name(), ".sql") {
				fileList = append(fileList, pathName+"/"+fi.Name())
			}
		}
	}
	return fileList, err
}
