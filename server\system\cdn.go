package system

import (
	"github.com/gin-gonic/gin"
	"hcscm/extern/upyun"
	structure "hcscm/structure/system"
)

//		@Tags		CDN
//		@Security	ApiKeyAuth
//		@Summary	获取CDN操作令牌
//		@Produce	json
//		@Param		body		body		structure.GetCDNTokenQuery{}	true	"CDN信息"
//		@Param		Platform	header		int								true	"终端ID"
//	 @Param		Authorization	header		string						true	"token"
//		@Success	200			{object}	structure.GetLoginInformationData{}
//		@Router		/hcscm/admin/v1/cdn/token [get]
//		@Router		/hcscm/mp/v1/cdn/token [get]
//		@Router		/hcscm/h5/v1/cdn/token [get]
//		@Router		/hcscm/third_party/v1/cdn/token [get]
func GetCDNToken(c *gin.Context) {
	var (
		q    = &structure.GetCDNTokenQuery{}
		data structure.GetCDNTokenData
		err  error

		token upyun.FormAPIToken
	)

	defer func() {
		BuildResponse(c, err, data)
	}()

	err = ShouldBind(c, q)
	if err != nil {
		return
	}

	// 正式环境的cdn
	if q.Status == 2 {
		token, err = upyun.GetZZUpYunFormAPIToken(q.Method, q.SaveKey)
		if err != nil {
			return
		}
	} else {
		token, err = upyun.GetUpYunFormAPIToken(q.Method, q.SaveKey)
		if err != nil {
			return
		}
	}

	data.Authorization = token.Authorization
	data.Bucket = token.Bucket
	data.Method = token.Method
	data.Policy = token.Policy
	data.Expiration = token.XUpYunExpire
	data.Status = q.Status
	if data.Status == 2 {
		// 正式环境
		data.StatusName = "正式"
	} else {
		data.StatusName = "测试"
	}

}
