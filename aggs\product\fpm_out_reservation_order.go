package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	"hcscm/common/errors"
	cus_const "hcscm/common/product"
	"hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	empl_pb "hcscm/extern/pb/employee"
	sale_sys_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/vars"
	"time"
)

type FpmOutReservationOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmOutReservationOrderRepo(tx *mysql_base.Tx) *FpmOutReservationOrderRepo {
	return &FpmOutReservationOrderRepo{tx: tx}
}

func (r *FpmOutReservationOrderRepo) Add(ctx context.Context, req *structure.AddFpmOutReservationOrderParam) (data structure.AddFpmOutReservationOrderData, err error) {

	var (
		// rLock *redis.LockForRedis
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = sale_sys_pb.NewSaleSystemClient()
		saleSysData        = sale_sys_pb.Res{}
	)

	fpmOutReservationOrder := model.NewFpmOutReservationOrder(ctx, req)
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, sale_sys_pb.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.FpmReservationOutOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.FpmReservationOutOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}

	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmOutReservationOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmOutReservationOrder.OrderNo = orderNo
	fpmOutReservationOrder.Number = int(number)

	fpmOutReservationOrder.TotalLength, fpmOutReservationOrder.TotalWeight, fpmOutReservationOrder.TotalRoll = req.GetTotalLWR()
	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutReservationOrder.UnitId = item.UnitId
		}
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutReservationOrder.UnitId = item.UnitId
			break
		}
	}

	fpmOutReservationOrder, err = mysql.MustCreateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmOutReservationOrderItem := model.NewFpmOutReservationOrderItem(ctx, &item)
		fpmOutReservationOrderItem.ParentId = fpmOutReservationOrder.Id
		fpmOutReservationOrderItem.ParentOrderNo = fpmOutReservationOrder.OrderNo
		fpmOutReservationOrderItem, err = mysql.MustCreateFpmOutReservationOrderItem(r.tx, fpmOutReservationOrderItem)
		if err != nil {
			return
		}
	}

	data.Id = fpmOutReservationOrder.Id
	return
}

func (r *FpmOutReservationOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmOutReservationOrderParam) (data structure.UpdateFpmOutReservationOrderData, err error) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
		itemModel              model.FpmOutReservationOrderItem
		itemList               model.FpmOutReservationOrderItemList
	)
	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmOutReservationOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	fpmOutReservationOrder.UpdateFpmOutReservationOrder(ctx, req)

	fpmOutReservationOrder.TotalLength, fpmOutReservationOrder.TotalWeight, fpmOutReservationOrder.TotalRoll = req.GetTotalLWR()
	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutReservationOrder.UnitId = item.UnitId
		}
	}

	if fpmOutReservationOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmOutReservationOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmOutReservationOrder.UnitId = item.UnitId
			break
		}
	}

	fpmOutReservationOrder, err = mysql.MustUpdateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmOutReservationOrderItemByParenTID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
	}

	// 新增成品信息
	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmOutReservationOrderItem := model.NewFpmOutReservationOrderItem(ctx, &item)
		fpmOutReservationOrderItem.ParentId = fpmOutReservationOrder.Id
		fpmOutReservationOrderItem.ParentOrderNo = fpmOutReservationOrder.OrderNo
		fpmOutReservationOrderItem, err = mysql.MustCreateFpmOutReservationOrderItem(r.tx, fpmOutReservationOrderItem)
		if err != nil {
			return
		}
	}
	data.Id = fpmOutReservationOrder.Id
	return
}

func (r *FpmOutReservationOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmOutReservationOrderBusinessCloseParam) (data structure.UpdateFpmOutReservationOrderData, err error) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, v)
		if err != nil {
			return
		}
		err = fpmOutReservationOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmOutReservationOrder, err = mysql.MustUpdateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmOutReservationOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (data structure.UpdateFpmOutReservationOrderStatusData, err error, stockMap map[uint64]*structure.BookData) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
	)

	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, id)
	if err != nil {
		return
	}

	err, stockMap = r.judgeAuditPass(id, fpmOutReservationOrder)
	if err != nil {
		return
	}

	// 审核
	err = fpmOutReservationOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmOutReservationOrder, err = mysql.MustUpdateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmOutReservationOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (data structure.UpdateFpmOutReservationOrderStatusData, err error, rollMap map[uint64]*structure.BookData) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
	)

	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, id)
	if err != nil {
		return
	}

	err = r.judgeIsQuoted(id, fpmOutReservationOrder)
	if err != nil {
		return
	}
	err, rollMap = r.judgeAuditWait(id, fpmOutReservationOrder)
	if err != nil {
		return
	}

	// 消审
	err = fpmOutReservationOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmOutReservationOrder, err = mysql.MustUpdateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmOutReservationOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmOutReservationOrderStatusData, err error) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
	)

	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 驳回
	err = fpmOutReservationOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmOutReservationOrder, err = mysql.MustUpdateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmOutReservationOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmOutReservationOrderStatusData, err error) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
	)

	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, id)
	if err != nil {
		return
	}

	r.judgeIsQuoted(id, fpmOutReservationOrder)

	// 作废
	err = fpmOutReservationOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmOutReservationOrder, err = mysql.MustUpdateFpmOutReservationOrder(r.tx, fpmOutReservationOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmOutReservationOrderRepo) Get(ctx context.Context, req *structure.GetFpmOutReservationOrderQuery) (data structure.GetFpmOutReservationOrderData, err error) {
	var (
		fpmOutReservationOrder model.FpmOutReservationOrder
		itemDatas              model.FpmOutReservationOrderItemList
	)
	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmOutReservationOrderData{}
	r.swapListModel2Data(fpmOutReservationOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmOutReservationOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, itemData := range itemDatas {
		itemGetData := structure.GetFpmOutReservationOrderItemData{}
		r.swapItemModel2Data(itemData, &itemGetData, ctx)
		if fpmOutReservationOrder.AuditStatus != common_system.OrderStatusAudited {
			stockList, _ := mysql.FindStockProductDyelotNumberWM(r.tx, &structure.GetWarehouseManageQuery{
				StockProductId: itemData.SumStockId, ProductColorId: itemData.ProductColorId, DyelotNumber: itemData.DyeFactoryDyelotNumber})
			for _, v := range stockList {
				// 库存信息,2023-12-20 需求1001412改为获取可用数量和匹数
				itemGetData.SumStockRoll = v.AvailableRoll
				itemGetData.SumStockWeight = v.AvailableWeight
				itemGetData.SumStockLength = v.Length
				itemGetData.AvailableRoll = v.AvailableRoll
				itemGetData.AvailableWeight = v.AvailableWeight
			}
		}
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmOutReservationOrderRepo) GetList(ctx context.Context, req *structure.GetFpmOutReservationOrderListQuery) (list structure.GetFpmOutReservationOrderDataList, total int, err error) {
	var (
		orders    model.FpmOutReservationOrderList
		bizPB     = biz_pb.NewClientBizUnitService()
		saleSysPB = sale_sys_pb.NewSaleSystemClient()
		// emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)
	orders, total, err = mysql.SearchFpmOutReservationOrder(r.tx, req)
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		// empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orders, "unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = saleSysPB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		wareIds := tools.MergeSlicesUint64(warehouseIds, warehouseIds)
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, wareIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	// g.Go(func(ctx context.Context) error {
	//	var err1 error
	//	storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
	//	empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
	//	if err1 != nil {
	//		middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
	//	}
	//	return nil
	// })

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		dst := structure.GetFpmOutReservationOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.OutOrderType = src.OutOrderType
		dst.SaleSystemId = src.SaleSystemId
		dst.BizUnitId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.OutWarehouseId = src.OutWarehouseId
		dst.InWarehouseId = src.InWarehouseId
		dst.ReservationTime = tools.MyTime(src.ReservationTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.Remark = src.Remark
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.TotalLength = src.TotalLength
		dst.UnitId = src.UnitId
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.Number = src.Number
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.VoucherNumber = src.VoucherNumber
		dst.TextureUrl = src.TextureUrl

		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.OutOrderTypeName = src.OutOrderType.String()
		dst.BizUnitName = bizNameMap[src.BizUnitId]
		if src.OutOrderType != cus_const.WarehouseGoodOutTypeDeduction && src.OutOrderType != cus_const.WarehouseGoodOutTypeOther {
			dst.InWarehouseName = wareNameMap[src.InWarehouseId]
		}
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.OutWarehouseName = wareNameMap[src.OutWarehouseId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.UnitName = unitNameMap[src.UnitId]
		list = append(list, dst)
	}
	return
}

func (r *FpmOutReservationOrderRepo) swapListModel2Data(src model.FpmOutReservationOrder, dst *structure.GetFpmOutReservationOrderData, ctx context.Context) {
	var (
		bizService  = biz_pb.NewClientBizUnitService()
		saleSysPB   = sale_sys_pb.NewSaleSystemClient()
		userPB      = empl_pb.NewClientEmployeeService()
		userName    = make(map[uint64]string)
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)

	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleSystemMap, err2 := saleSysPB.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	userName, _ = userPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseNameMap, _ := warehousePB.GetPhysicalWarehouseByIds(ctx, []uint64{src.WarehouseId, src.InWarehouseId, src.OutWarehouseId})

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.OutOrderType = src.OutOrderType
	dst.SaleSystemId = src.SaleSystemId
	dst.BizUnitId = src.BizUnitId
	dst.WarehouseId = src.WarehouseId
	dst.OutWarehouseId = src.OutWarehouseId
	dst.InWarehouseId = src.InWarehouseId
	dst.ReservationTime = tools.MyTime(src.ReservationTime)
	dst.StoreKeeperId = src.StoreKeeperId
	dst.Remark = src.Remark
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.TotalLength = src.TotalLength
	dst.UnitId = src.UnitId
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.VoucherNumber = src.VoucherNumber
	dst.TextureUrl = src.TextureUrl

	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.OutOrderTypeName = src.OutOrderType.String()
	dst.UnitName = unitName
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.BizUnitName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	if val, ok := warehouseNameMap[src.WarehouseId]; ok {
		dst.WarehouseName = val
	}
	if val, ok := warehouseNameMap[src.InWarehouseId]; ok {
		dst.InWarehouseName = val
	}
	if val, ok := warehouseNameMap[src.OutWarehouseId]; ok {
		dst.OutWarehouseName = val
	}

}

func (r *FpmOutReservationOrderRepo) swapItemModel2Data(src model.FpmOutReservationOrderItem, dst *structure.GetFpmOutReservationOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, src.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, src.ProductColorId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.ParentId = src.ParentId
	dst.ParentOrderNo = src.ParentOrderNo
	dst.ProductId = src.ProductId
	dst.ProductCode = src.ProductCode
	dst.ProductName = src.ProductName
	dst.CustomerId = src.CustomerId
	dst.ProductColorId = src.ProductColorId
	dst.ProductColorCode = src.ProductColorCode
	dst.ProductColorName = src.ProductColorName
	dst.ProductLevelId = src.ProductLevelId
	dst.ProductWidth = src.ProductWidth
	dst.DyeFactoryColorCode = src.DyeFactoryColorCode
	dst.DyeFactoryDyelotNumber = src.DyeFactoryDyelotNumber
	dst.ProductGramWeight = src.ProductGramWeight
	dst.ProductRemark = src.ProductRemark
	dst.ProductCraft = src.ProductCraft
	dst.ProductIngredient = src.ProductIngredient
	dst.ReservationRoll = src.ReservationRoll
	dst.SumStockId = src.SumStockId
	dst.ReservationWeight = src.ReservationWeight
	dst.UnitId = src.UnitId
	dst.ReservationLength = src.ReservationLength
	dst.Remark = src.Remark

	// 转义

	dst.UnitName = unitName
	if val, ok := customerMap[src.CustomerId]; ok {
		dst.CustomerName = val
	}
	dst.ProductLevelName = levelName
	dst.ProductColorName = getColor.ProductColorName

	dst.SumStockRoll = src.SumStockRoll
	dst.SumStockLength = src.SumStockLength
	dst.SumStockWeight = src.SumStockWeight
	dst.AvailableRoll = src.SumStockRoll
	dst.AvailableWeight = src.SumStockWeight
}

func (r *FpmOutReservationOrderRepo) judgeAuditPass(id uint64, order model.FpmOutReservationOrder) (error, map[uint64]*structure.BookData) {
	var (
		itemList = model.FpmOutReservationOrderItemList{}
		rollMap  = make(map[uint64]*structure.BookData)
		// sumStockProduct = model.StockProduct{}
		err error
	)
	itemList, err = mysql.FindFpmOutReservationOrderItemByParenTID(r.tx, id)
	if err != nil {
		return err, rollMap
	}
	for _, item := range itemList {
		bookList, ok := rollMap[item.SumStockId]
		if !ok {
			bookList = &structure.BookData{}
		}
		bookList.BookRoll += item.ReservationRoll
		bookList.BookWeight += item.ReservationWeight
		bookList.ProductId = item.ProductId
		bookList.ColorId = item.ProductColorId
		bookList.OrderId = item.ParentId
		bookList.OrderNo = item.ParentOrderNo
		rollMap[item.SumStockId] = bookList

		if item.ReservationRoll == 0 && item.ReservationWeight == 0 {
			err = middleware.ErrorLog(errors.NewError(errors.ErrCodeRollAndWeightCanNotAllZero))
			return err, rollMap
		}
	}

	return nil, rollMap
}

func (r *FpmOutReservationOrderRepo) judgeAuditWait(id uint64, fpmOutReservationOrder model.FpmOutReservationOrder) (error, map[uint64]*structure.BookData) {

	var (
		itemList = model.FpmOutReservationOrderItemList{}
		rollMap  = make(map[uint64]*structure.BookData)
		// sumStockProduct = model.StockProduct{}
		err error
	)
	itemList, err = mysql.FindFpmOutReservationOrderItemByParenTID(r.tx, id)
	if err != nil {
		return err, rollMap
	}
	for _, item := range itemList {
		bookList, ok := rollMap[item.SumStockId]
		if !ok {
			bookList = &structure.BookData{}
		}
		bookList.BookRoll -= item.ReservationRoll
		bookList.BookWeight -= item.ReservationWeight
		bookList.ProductId = item.ProductId
		bookList.ColorId = item.ProductColorId
		bookList.OrderId = item.ParentId
		bookList.OrderNo = item.ParentOrderNo
		rollMap[item.SumStockId] = bookList
		// sumStockProduct, err = mysql.MustFirstStockProductByID(r.tx, item.SumStockId)
		// if sumStockProduct.StockRoll-sumStockProduct.BookRoll < rollMap[sumStockProduct.Id] {
		//	return errors.ErrCodeOverStockRoll, nil
		// }
	}

	return nil, rollMap

}

func (r *FpmOutReservationOrderRepo) judgeAuditCancel(id uint64, order model.FpmOutReservationOrder) (err error) {

	return
}

func (r *FpmOutReservationOrderRepo) judgeIsQuoted(id uint64, order model.FpmOutReservationOrder) (err error) {
	var (
		count int
	)

	_, count, err = mysql.SearchFpmArrangeOrderCrossRvt(r.tx, &structure.GetFpmArrangeOrderListQuery{SrcId: order.Id, JudgeStatus: true})
	if err != nil {
		return
	}
	if count > 0 {
		return errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, " : 操作失败")
	}
	return
}

func (r *FpmOutReservationOrderRepo) SwapArrangeData(ctx context.Context, id uint64) (err error, arrangeOrder structure.AddFpmArrangeOrderParam) {
	var (
		fpmOutReservationOrder     = model.FpmOutReservationOrder{}
		fpmOutReservationOrderItem = model.FpmOutReservationOrderItemList{}
		// saleOutOrderParam = structure.AddFpmSaleOutOrderParam{}
		// saleOutOrderItemParam = structure.AddFpmSaleOutOrderItemParam{}
		arrangeItemList = structure.AddFpmArrangeOrderItemParamList{}
	)

	// 查本条数据
	fpmOutReservationOrder, err = mysql.MustFirstFpmOutReservationOrderByID(r.tx, id)
	if err != nil {
		return
	}
	// 查子数据
	fpmOutReservationOrderItem, err = mysql.FindFpmOutReservationOrderItemByParenTID(r.tx, fpmOutReservationOrder.Id)
	if err != nil {
		return
	}
	// 转换item数据
	arrangeOrder = r.swapListModel2ArrangeListModel(fpmOutReservationOrder)
	arrangeItemList = r.swapThisItem2ArrangeItemParam(fpmOutReservationOrderItem)
	arrangeOrder.ItemData = arrangeItemList

	return
}

func (r *FpmOutReservationOrderRepo) swapThisItem2ArrangeItemParam(src model.FpmOutReservationOrderItemList) (dst structure.AddFpmArrangeOrderItemParamList) {
	for _, item := range src {
		param := structure.AddFpmArrangeOrderItemParam{}
		param.ProductId = item.ProductId
		param.ProductCode = item.ProductCode
		param.ProductName = item.ProductName
		param.CustomerId = item.CustomerId
		param.ProductColorId = item.ProductColorId
		param.ProductColorCode = item.ProductColorCode
		param.ProductColorName = item.ProductColorName
		param.ProductLevelId = item.ProductLevelId
		param.DyeFactoryColorCode = item.DyeFactoryColorCode
		param.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		param.ProductWidth = item.ProductWidth
		param.ProductGramWeight = item.ProductGramWeight
		param.ProductRemark = item.ProductRemark
		param.ProductCraft = item.ProductCraft
		param.ProductIngredient = item.ProductIngredient
		// param.ArrangeRoll = item.ReservationRoll
		param.SumStockId = item.SumStockId
		// param.ArrangeWeight = item.ReservationWeight
		// param.SettleWeight = item.ReservationWeight
		param.UnitId = item.UnitId
		param.ArrangeLength = item.ReservationLength
		param.Remark = item.Remark
		param.PushRoll = item.ReservationRoll
		param.PushWeight = item.ReservationWeight
		param.PushLength = item.ReservationLength
		param.SumStockRoll = item.AvailableRoll
		param.SumStockWeight = item.AvailableWeight
		param.SumStockLength = item.AvailableLength
		dst = append(dst, param)
	}
	return
}

func (r *FpmOutReservationOrderRepo) swapListModel2ArrangeListModel(src model.FpmOutReservationOrder) (dst structure.AddFpmArrangeOrderParam) {
	dst.SrcType = cus_const.ArrangeOrderFromReservationOrder
	dst.SrcId = src.Id
	dst.SrcOrderNo = src.OrderNo
	dst.SaleMode = sale.BulkTypeOrder
	// dst.BusinessStatus = cus_const.BusinessStatusArrangeWait
	dst.OutOrderType = src.OutOrderType
	dst.SaleSystemId = src.SaleSystemId
	dst.ArrangeTime = tools.QueryTime(time.Now().Format("2006-01-02"))
	dst.ArrangeToWarehouseId = src.InWarehouseId
	if src.WarehouseId > 0 {
		dst.WarehouseId = src.WarehouseId
	}
	if src.OutWarehouseId > 0 {
		dst.WarehouseId = src.OutWarehouseId
	}
	dst.BizUnitId = src.BizUnitId
	dst.ProcessFactoryId = src.BizUnitId
	dst.StoreKeeperId = src.StoreKeeperId
	dst.InternalRemark = src.Remark
	// dst.TotalRoll = src.TotalRoll
	// dst.TotalWeight = src.TotalWeight
	// dst.Length = src.Length
	// dst.UnitId = src.UnitId
	// dst.DepartmentId = src.DepartmentId
	// dst.OrderNo = src.OrderNo
	// dst.Number = src.Number
	// dst.AuditStatus = src.AuditStatus
	// dst.AuditorId = src.AuditorId
	// dst.AuditorName = src.AuditorName
	// dst.AuditTime = src.AuditTime
	// dst.BusinessStatus = cus_const.BusinessStatusArrangeWait
	return
}
