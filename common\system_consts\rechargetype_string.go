// Code generated by "stringer -type=RechargeType --linecomment"; DO NOT EDIT.

package common

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[RechargeTypeOcr-1]
	_ = x[RechargeTypeEleColorCard-2]
	_ = x[RechargeTypeSearchImage-3]
}

const _RechargeType_name = "OCR电子色卡图片搜索"

var _RechargeType_index = [...]uint8{0, 3, 15, 27}

func (i RechargeType) String() string {
	i -= 1
	if i < 0 || i >= RechargeType(len(_RechargeType_index)-1) {
		return ""
	}
	return _RechargeType_name[_RechargeType_index[i]:_RechargeType_index[i+1]]
}
