package third_party_extra

import (
	"github.com/gin-gonic/gin"
	"hcscm/common/response"
	"hcscm/service/third_party_extra"
	"hcscm/structure/third_party_extra_structure"
	"hcscm/tools/metadata"
	"strconv"
)

// GetPersonalCenter 获取个人中心信息
// @Summary 获取个人中心信息
// @Description 根据用户ID获取个人中心信息
// @Tags 个人中心
// @Accept json
// @Produce json
// @Param user_id query uint64 true "用户ID"
// @Success 200 {object} response.Response{data=third_party_extra_structure.GetPersonalCenterResponse}
// @Router /api/third_party_extra/personal_center/get [get]
func GetPersonalCenter(c *gin.Context) {
	var req third_party_extra_structure.GetPersonalCenterRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	// 如果没有传用户ID，从上下文获取
	if req.UserId == 0 {
		userIdStr := metadata.GetUserIdFromIncoming(c)
		if userIdStr == "" {
			response.FailWithMessage(c, "用户ID不能为空")
			return
		}
		userId, err := strconv.ParseUint(userIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage(c, "用户ID格式错误")
			return
		}
		req.UserId = userId
	}

	service := third_party_extra.NewPersonalCenterService()
	result, err := service.GetPersonalCenter(req)
	if err != nil {
		response.FailWithMessage(c, "获取个人中心信息失败: "+err.Error())
		return
	}

	response.OkWithData(c, result)
}

// UpdatePersonalCenter 更新个人中心信息
// @Summary 更新个人中心信息
// @Description 更新用户的个人中心信息
// @Tags 个人中心
// @Accept json
// @Produce json
// @Param data body third_party_extra_structure.UpdatePersonalCenterRequest true "更新请求"
// @Success 200 {object} response.Response
// @Router /api/third_party_extra/personal_center/update [post]
func UpdatePersonalCenter(c *gin.Context) {
	var req third_party_extra_structure.UpdatePersonalCenterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	// 如果没有传用户ID，从上下文获取
	if req.UserId == 0 {
		userIdStr := metadata.GetUserIdFromIncoming(c)
		if userIdStr == "" {
			response.FailWithMessage(c, "用户ID不能为空")
			return
		}
		userId, err := strconv.ParseUint(userIdStr, 10, 64)
		if err != nil {
			response.FailWithMessage(c, "用户ID格式错误")
			return
		}
		req.UserId = userId
	}

	service := third_party_extra.NewPersonalCenterService()
	err := service.UpdatePersonalCenter(req)
	if err != nil {
		response.FailWithMessage(c, "更新个人中心信息失败: "+err.Error())
		return
	}

	response.OkWithMessage(c, "更新成功")
}

// GetPersonalCenterList 获取个人中心列表
// @Summary 获取个人中心列表
// @Description 获取个人中心信息列表（管理员使用）
// @Tags 个人中心
// @Accept json
// @Produce json
// @Param user_id query uint64 false "用户ID"
// @Param status query int false "状态"
// @Param keyword query string false "关键词"
// @Param page query int false "页码"
// @Param page_size query int false "每页数量"
// @Success 200 {object} response.Response{data=third_party_extra_structure.GetPersonalCenterListResponse}
// @Router /api/third_party_extra/personal_center/list [get]
func GetPersonalCenterList(c *gin.Context) {
	var req third_party_extra_structure.GetPersonalCenterListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage(c, "参数错误: "+err.Error())
		return
	}

	service := third_party_extra.NewPersonalCenterService()
	result, err := service.GetPersonalCenterList(req)
	if err != nil {
		response.FailWithMessage(c, "获取个人中心列表失败: "+err.Error())
		return
	}

	response.OkWithData(c, result)
}

// DeletePersonalCenter 删除个人中心信息
// @Summary 删除个人中心信息
// @Description 删除指定的个人中心信息（管理员使用）
// @Tags 个人中心
// @Accept json
// @Produce json
// @Param id path uint64 true "个人中心ID"
// @Success 200 {object} response.Response
// @Router /api/third_party_extra/personal_center/delete/{id} [delete]
func DeletePersonalCenter(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 64)
	if err != nil {
		response.FailWithMessage(c, "ID格式错误")
		return
	}

	service := third_party_extra.NewPersonalCenterService()
	err = service.DeletePersonalCenter(id)
	if err != nil {
		response.FailWithMessage(c, "删除个人中心信息失败: "+err.Error())
		return
	}

	response.OkWithMessage(c, "删除成功")
}
