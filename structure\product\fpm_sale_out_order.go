package product

import (
	cus_const "hcscm/common/product"
	common "hcscm/common/sale"
	should_collect_order_consts "hcscm/common/should_collect_order"
	common_system "hcscm/common/system_consts"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFpmSaleOutOrderParamList []AddFpmSaleOutOrderParam

func (r AddFpmSaleOutOrderParamList) Adjust() {

}

type AddFpmSaleOutOrderParam struct {
	structure_base.Param
	ItemData             AddFpmOutOrderItemParamList    `json:"item_data"`              // 成品信息
	OutOrderType         cus_const.WarehouseGoodOutType `json:"out_order_type"`         // 出仓类型
	SaleAlloInOrderId    uint64                         `json:"sale_allo_in_order_id"`  // 进仓单id
	ArrangeOrderId       uint64                         `json:"-"`                      // 配布id
	ArrangeOrderNo       string                         `json:"-"`                      // 配布单号
	SaleSystemId         uint64                         `json:"sale_system_id"`         // 营销体系id，必填
	CustomerId           uint64                         `json:"customer_id"`            // 客户id
	WarehouseId          uint64                         `json:"warehouse_id"`           // 仓库id
	WarehouseOutTime     tools.QueryTime                `json:"warehouse_out_time"`     // 出仓日期
	ProcessFactoryId     uint64                         `json:"process_factory_id"`     // 加工厂id
	ReceiveName          string                         `json:"receive_name"`           // 收货人
	ReceiveAddr          string                         `json:"receive_addr"`           // 收货地址
	ReceivePhone         string                         `json:"receive_phone"`          // 收货电话
	ReceiveTag           string                         `json:"receive_tag"`            // 收货标签
	SaleUserId           uint64                         `json:"sale_user_id"`           // 销售员id
	SaleFollowerId       uint64                         `json:"sale_follower_id"`       // 销售跟单员id
	StoreKeeperId        uint64                         `json:"store_keeper_id"`        // 仓管员id（关联user.id）
	ArrangeUserId        uint64                         `json:"arrange_user_id"`        // 配布员id（关联user.id）
	DriverId             string                         `json:"driver_id"`              // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId   uint64                         `json:"logistics_company_id"`   // 物流公司id
	LogisticsCompanyName string                         `json:"logistics_company_name"` // 物流公司name
	LogisticsCompanyArea string                         `json:"logistics_company_area"` // 物流公司区域
	InternalRemark       string                         `json:"internal_remark"`        // 内部备注
	SaleRemark           string                         `json:"sale_remark"`            // 销售备注
	TextureUrl           string                         `json:"texture_url"`            // 凭证图片URL
	SrcOrderID           uint64                         `json:"src_order_id"`           // 源单id
	SrcOrderNo           string                         `json:"src_order_no"`           // 源单单号
	SaleMode             common.SaleOrderType           `json:"sale_mode"`              // 订单类型 1大货 2剪板 3客订大货 4客订剪板
}

type AddFpmSaleOutOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateFpmSaleOutOrderParam struct {
	structure_base.Param
	ItemData             AddFpmOutOrderItemParamList `json:"item_data"` // 坯布信息
	Id                   uint64                      `json:"id"`
	SaleSystemId         uint64                      `json:"sale_system_id"`         // 营销体系id，必填
	ArrangeOrderId       uint64                      `json:"-"`                      // 配布id
	ArrangeOrderNo       string                      `json:"-"`                      // 配布单号
	CustomerId           uint64                      `json:"customer_id"`            // 客户id
	WarehouseId          uint64                      `json:"warehouse_id"`           // 仓库id
	WarehouseOutTime     tools.QueryTime             `json:"warehouse_out_time"`     // 出仓日期
	ProcessFactoryId     uint64                      `json:"process_factory_id"`     // 加工厂id
	ReceiveName          string                      `json:"receive_name"`           // 收货人
	ReceiveAddr          string                      `json:"receive_addr"`           // 收货地址
	ReceivePhone         string                      `json:"receive_phone"`          // 收货电话
	ReceiveTag           string                      `json:"receive_tag"`            // 收货标签
	SaleUserId           uint64                      `json:"sale_user_id"`           // 销售员id
	SaleFollowerId       uint64                      `json:"sale_follower_id"`       // 销售跟单员id
	StoreKeeperId        uint64                      `json:"store_keeper_id"`        // 仓管员id（关联user.id）
	ArrangeUserId        uint64                      `json:"-"`                      // 配布员id（关联user.id）
	DriverId             string                      `json:"driver_id"`              // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId   uint64                      `json:"logistics_company_id"`   // 物流公司id
	LogisticsCompanyName string                      `json:"logistics_company_name"` // 物流公司name
	LogisticsCompanyArea string                      `json:"logistics_company_area"` // 物流公司区域
	InternalRemark       string                      `json:"internal_remark"`        // 内部备注
	SaleRemark           string                      `json:"sale_remark"`            // 销售备注
	TextureUrl           string                      `json:"texture_url"`            // 凭证图片URL
	SaleMode             common.SaleOrderType        `json:"sale_mode"`              // 订单类型 1大货 2剪板 3客订大货 4客订剪板
}

type UpdateFpmSaleOutOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// 列表表使用
func (r AddFpmSaleOutOrderParam) GetTotalPWR() (totalWeight int, totalRoll int, totalLength int) {
	for _, v := range r.ItemData {
		tw, tl, te, tpp := 0, 0, 0, 0
		for _, v2 := range v.ItemFCData {
			tw += v2.BaseUnitWeight
			tl += v2.Length
			te += v2.WeightError
			tpp += v2.PaperTubeWeight
		}
		//
		// weightPrice := tools.DecimalDiv(float64(tw*v.UnitPrice), 10000)
		// lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), 10000)
		// tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
		// tp = tempPrice + v.OtherPrice
		// 安全相除计算并四舍五入
		// totalPrice += tp
		totalRoll += v.OutRoll
		totalLength += tl
		totalWeight += tw
	}
	return
}

func (r *AddFpmSaleOutOrderParam) Swap2AddSaleOutParam(req GetFpmArrangeOrderData) {
	r.SaleSystemId = req.SaleSystemId
	r.ArrangeOrderId = req.Id
	r.ArrangeOrderNo = req.OrderNo
	r.CustomerId = req.BizUnitId
	r.WarehouseId = req.WarehouseId
	r.WarehouseOutTime = tools.QueryTime(req.ArrangeTime.Date())
	r.ProcessFactoryId = req.ProcessFactoryId
	r.ReceiveAddr = req.ReceiveAddr
	r.ReceivePhone = req.ReceivePhone
	r.ReceiveTag = req.ReceiveTag
	r.SaleUserId = req.SaleUserId
	r.SaleFollowerId = req.SaleFollowerId
	r.StoreKeeperId = req.StoreKeeperId
	r.ArrangeUserId = req.CreatorId
	r.DriverId = req.DriverId
	r.LogisticsCompanyId = req.LogisticsCompanyId
	r.LogisticsCompanyName = req.LogisticsCompany
	// r.LogisticsCompanyArea = req.LogisticsCompanyArea
	r.InternalRemark = req.InternalRemark
	r.SaleRemark = req.SaleRemark
	r.SaleMode = req.SaleMode
}

func (r UpdateFpmSaleOutOrderParam) GetTotalPWR() (totalWeight int, totalRoll int, totalLength int) {
	for _, v := range r.ItemData {
		tw, tl, te, tpp := 0, 0, 0, 0
		for _, v2 := range v.ItemFCData {
			tw += v2.BaseUnitWeight
			tl += v2.Length
			te += v2.WeightError
			tpp += v2.PaperTubeWeight
		}
		//
		// weightPrice := tools.DecimalDiv(float64(tw*v.UnitPrice), 10000)
		// lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), 10000)
		// tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
		// tp = tempPrice + v.OtherPrice
		// 安全相除计算并四舍五入
		// totalPrice += tp
		totalRoll += v.OutRoll
		totalLength += tl
		totalWeight += tw
	}
	return
}

type UpdateFpmSaleOutOrderBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateFpmSaleOutOrderStatusParam struct {
	structure_base.Param
	Id          tools.QueryIntList        `json:"id"`
	AuditStatus common_system.OrderStatus `json:"audit_status"`
	IsAudit     bool                      `json:"is_audit"`
}
type UpdateFpmSaleOutOrderStatusData struct {
	structure_base.ResponseData
	Id               uint64       `json:"id"`
	ArrangeId        uint64       `json:"arrange_id"`
	SaleAlloOrderId  uint64       `json:"sale_allo_order_id"`
	StockDetailIds   []uint64     `json:"-"`
	WarehouseOutTime tools.MyTime `json:"warehouse_out_time"` // 出仓时间
}

type MPUpdateFpmSaleOutOrderStatusData struct {
	structure_base.ResponseData
	Id              uint64   `json:"id"`
	SaleAlloOrderId uint64   `json:"sale_allo_order_id"`
	StockDetailIds  []uint64 `json:"-"`
}

type GetFpmSaleOutOrderQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmSaleOutOrderListQuery struct {
	structure_base.ListQuery
	ArrangeOrderId        uint64             `form:"arrange_order_id"`         // 配布id
	IsDropList            int                `form:"is_drop_list"`             // 回收站
	ArrangeOrderNo        string             `form:"arrange_order_no"`         // 配布单号
	SaleSystemId          uint64             `form:"sale_system_id"`           // 营销体系id，必填
	CustomerId            uint64             `form:"customer_id"`              // 客户id
	WarehouseId           uint64             `form:"warehouse_id"`             // 仓库id
	WarehouseOutTime      tools.QueryTime    `form:"warehouse_out_time"`       // 出仓日期
	OutTimeBegin          tools.QueryTime    `form:"out_time_begin"`           // 出仓时间
	OutTimeEnd            tools.QueryTime    `form:"out_time_end"`             // 出仓时间
	ProcessFactoryId      uint64             `form:"process_factory_id"`       // 加工厂id
	ReceiveAddr           string             `form:"receive_addr"`             // 收货地址
	ReceiveName           string             `form:"receive_name"`             // 收货人
	ReceivePhone          string             `form:"receive_phone"`            // 收货电话
	ReceiveTag            string             `form:"receive_tag"`              // 收货标签
	SaleUserId            uint64             `form:"sale_user_id"`             // 销售员id
	SaleFollowerId        uint64             `form:"sale_follower_id"`         // 销售跟单员id
	StoreKeeperId         uint64             `form:"store_keeper_id"`          // 仓管员id（关联user.id）
	ArrangeUserId         uint64             `form:"arrange_user_id"`          // 配布员id（关联user.id）
	DriverId              string             `form:"driver_id"`                // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId    uint64             `form:"logistics_company_id"`     // 物流公司id
	LogisticsCompanyName  string             `form:"logistics_company_name"`   // 物流公司name
	LogisticsCompanyArea  string             `form:"logistics_company_area"`   // 物流公司区域
	InternalRemark        string             `form:"internal_remark"`          // 内部备注
	SaleRemark            string             `form:"sale_remark"`              // 销售备注
	TotalRoll             int                `form:"total_roll"`               // 匹数总计
	TotalWeight           int                `form:"total_weight"`             // 数量总计
	TotalLength           int                `form:"total_length"`             // 长度总计
	UnitId                int                `form:"unit_id"`                  // 单位id
	BusinessClose         bool               `form:"business_close"`           // 业务关闭
	BusinessCloseUserId   uint64             `form:"business_close_user_id"`   // 业务关闭操作人
	BusinessCloseUserName string             `form:"business_close_user_name"` // 业务关闭操作人名
	BusinessCloseTime     tools.QueryTime    `form:"business_close_time"`      // 业务关闭时间
	DepartmentId          uint64             `form:"department_id"`            // 下单用户所属部门
	OrderNo               string             `form:"order_no"`                 // 单据编号
	Number                int                `form:"number"`                   // 编号流水：每日重新更新
	AuditStatus           tools.QueryIntList `form:"audit_status"`             // 审核状态
	AuditorId             uint64             `form:"auditor_id"`               // 审核人ID （关联user.id）
	AuditorName           string             `form:"auditor_name"`             // 审核人名称
	AuditTime             tools.QueryTime    `form:"audit_time"`               // 审核时间
	CreateStartDate       tools.QueryTime    `form:"create_start_date"`        // 创建时间开始
	CreateEndDate         tools.QueryTime    `form:"create_end_date"`          // 创建时间结束
	AuditStartDate        tools.QueryTime    `form:"audit_start_date"`         // 审核时间开始
	AuditEndDate          tools.QueryTime    `form:"audit_end_date"`           // 审核时间结束
	VoucherNumber         string             `form:"voucher_number"`           // 凭证
	TextureUrl            string             `json:"texture_url"`              // 凭证图片URL
}

func (r GetFpmSaleOutOrderListQuery) Adjust() {

}

type GetFpmSaleOutOrderData struct {
	structure_base.RecordData
	ItemData              GetFpmOutOrderItemDataList  `json:"item_data"`                // 成品信息
	SaleAlloOrderId       uint64                      `json:"sale_allo_order_id"`       // 销调进
	ArrangeOrderId        uint64                      `json:"arrange_order_id"`         // 配布id(新建销售出仓单的时候不要传)
	ArrangeOrderNo        string                      `json:"arrange_order_no"`         // 配布单号
	SaleSystemId          uint64                      `json:"sale_system_id"`           // 营销体系id，必填
	CustomerId            uint64                      `json:"customer_id"`              // 客户id
	WarehouseId           uint64                      `json:"warehouse_id"`             // 仓库id
	WarehouseOutTime      tools.MyTime                `json:"warehouse_out_time"`       // 出仓日期
	ProcessFactoryId      uint64                      `json:"process_factory_id"`       // 加工厂id
	ReceiveName           string                      `json:"receive_name"`             // 收货人
	ReceiveAddr           string                      `json:"receive_addr"`             // 收货地址
	ReceivePhone          string                      `json:"receive_phone"`            // 收货电话
	ReceiveTag            string                      `json:"receive_tag"`              // 收货标签
	SaleUserId            uint64                      `json:"sale_user_id"`             // 销售员id
	SaleFollowerId        uint64                      `json:"sale_follower_id"`         // 销售跟单员id
	StoreKeeperId         uint64                      `json:"store_keeper_id"`          // 仓管员id（关联user.id）
	ArrangeUserId         uint64                      `json:"arrange_user_id"`          // 配布员id（关联user.id）
	DriverId              string                      `json:"driver_id"`                // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId    uint64                      `json:"logistics_company_id"`     // 物流公司id
	LogisticsCompanyName  string                      `json:"logistics_company_name"`   // 物流公司name
	LogisticsCompanyArea  string                      `json:"logistics_company_area"`   // 物流公司区域
	InternalRemark        string                      `json:"internal_remark"`          // 内部备注
	SaleRemark            string                      `json:"sale_remark"`              // 销售备注
	TotalRoll             int                         `json:"total_roll"`               // 匹数总计
	TotalWeight           int                         `json:"total_weight"`             // 数量总计
	TotalLength           int                         `json:"total_length"`             // 长度总计
	UnitId                uint64                      `json:"unit_id" relate:"unit_id"` // 单位id
	AuxiliaryUnitId       uint64                      `json:"auxiliary_unit_id"`        // 辅助单位id
	BusinessClose         common_system.BusinessClose `json:"business_close"`           // 业务关闭
	BusinessCloseUserId   uint64                      `json:"business_close_user_id"`   // 业务关闭操作人
	BusinessCloseUserName string                      `json:"business_close_user_name"` // 业务关闭操作人名
	BusinessCloseTime     tools.MyTime                `json:"business_close_time"`      // 业务关闭时间
	DepartmentId          uint64                      `json:"department_id"`            // 下单用户所属部门
	OrderNo               string                      `json:"order_no"`                 // 单据编号
	Number                int                         `json:"number"`                   // 编号流水：每日重新更新
	AuditStatus           common_system.OrderStatus   `json:"audit_status"`             // 审核状态
	AuditorId             uint64                      `json:"auditor_id"`               // 审核人ID （关联user.id）
	AuditorName           string                      `json:"auditor_name"`             // 审核人名称
	AuditTime             tools.MyTime                `json:"audit_time"`               // 审核时间
	TextureUrl            string                      `json:"texture_url"`              // 凭证图片URL
	TotalSettlePrice      int                         `json:"total_settle_price"`       // 总金额（单据）
	MergeWeightInfo       string                      `json:"merge_weight_info"`        // 拼接所在单商品信息的数量及单位
	SaleMode              common.SaleOrderType        `json:"sale_mode"    `            // 订单类型 1大货 2剪板 3客订大货 4客订剪板

	// 转义
	BusinessCloseName string `json:"business_close_name"` // 业务关闭Name
	AuditStatusName   string `json:"audit_status_name"`   // 审核状态Name
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 辅助单位name
	UnitName          string `json:"unit_name"`           // 单位name
	OutOrderTypeName  string `json:"in_order_type_name"`  // out仓类型name
	SaleSystemName    string `json:"sale_system_name"`    // 营销体系name
	CustomerName      string `json:"customer_name"`       // 客户name
	StoreKeeperName   string `json:"store_keeper_name"`   // 仓管员name
	WarehouseName     string `json:"warehouse_name"`      // 仓库name

	ProcessFactoryName string `json:"process_factory_name"` // 加工厂名称
	ArrangeUserName    string `json:"arrange_user_name"`    // 配布员名称
	SaleUserName       string `json:"sale_user_name"`       // 销售员名称
	SaleFollowerName   string `json:"sale_follower_name"`   // 销售跟单员名称
	DriverName         string `json:"driver_name"`          // 司机名称
	SaleModeName       string `json:"sale_mode_name"`       // 订单类型
}

type GetFpmSaleOutOrderDataList []GetFpmSaleOutOrderData

func (g GetFpmSaleOutOrderDataList) Adjust() {

}

// 根据销售出仓单及其项的信息转换为创建应收单所需的格式
func (p *GetFpmSaleOutOrderData) ToAddProductSaleShouldCollectOrderParam() (data *shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam) {
	var (
		items = make(shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailParamList, 0)
		// itemfcs 应该在每个销售出仓单项中处理，不需要在这里初始化
	)
	r := &shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam{}
	r.SrcId = p.Id
	r.SrcOrderNo = p.OrderNo
	r.SrcOrderType = should_collect_order_consts.SrcOrderTypeProductSaleOut
	r.SaleSystemId = p.SaleSystemId
	r.CustomerId = p.CustomerId
	r.SaleUserId = p.SaleUserId
	r.SaleFollowerId = p.SaleFollowerId
	r.SettleType = common_system.SettleType(1)
	r.OrderTime = tools.QueryTime(p.WarehouseOutTime.Date()) // 使用销售出仓单的出仓时间（来自调货单的下单时间）
	r.IsWithTaxRate = false
	r.Roll = p.TotalRoll
	r.Weight = p.TotalWeight
	r.OffsetPrice = 0
	r.DiscountPrice = 0
	r.ReducePrice = 0
	r.CollectedMoney = 0
	r.AuditStatus = common_system.OrderStatusPendingAudit
	r.TotalSettleMoney = p.TotalSettlePrice
	r.TotalShouldCollectMoney = p.TotalSettlePrice
	r.TotalUnCollectMoney = p.TotalSettlePrice
	r.OrderRemark = p.SaleRemark
	r.SaleMode = p.SaleMode // 下推订单类型

	for _, fpmSaleOutOrderItem := range p.ItemData {
		o := shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailParam{}
		o.SrcDetailId = fpmSaleOutOrderItem.Id
		o.MaterialId = fpmSaleOutOrderItem.ProductId
		o.Code = fpmSaleOutOrderItem.ProductCode
		o.Name = fpmSaleOutOrderItem.ProductName
		o.ProductColorId = fpmSaleOutOrderItem.ProductColorId
		o.ProductColorCode = fpmSaleOutOrderItem.ProductColorCode
		o.ProductColorName = fpmSaleOutOrderItem.ProductColorName
		o.DyelotNumber = fpmSaleOutOrderItem.DyeFactoryDyelotNumber
		o.Roll = fpmSaleOutOrderItem.OutRoll
		o.Weight = fpmSaleOutOrderItem.TotalWeight
		o.WeightError = fpmSaleOutOrderItem.WeightError
		o.ActuallyWeight = fpmSaleOutOrderItem.ActuallyWeight
		o.SettleErrorWeight = fpmSaleOutOrderItem.SettleErrorWeight
		o.SettleWeight = fpmSaleOutOrderItem.SettleWeight
		o.StandardSalePrice = fpmSaleOutOrderItem.UnitPrice
		o.SaleLevelId = fpmSaleOutOrderItem.ProductLevelId
		o.OffsetSalePrice = 0
		o.SettlePrice = fpmSaleOutOrderItem.TotalPrice
		o.SalePrice = fpmSaleOutOrderItem.UnitPrice
		o.StandardWeightError = 0
		o.OffsetWeightError = 0
		o.AdjustWeightError = 0
		o.Length = fpmSaleOutOrderItem.OutLength
		o.StandardLengthCutSalePrice = fpmSaleOutOrderItem.LengthUnitPrice
		o.OffsetLengthCutSalePrice = 0
		o.LengthCutSalePrice = o.StandardLengthCutSalePrice - o.OffsetLengthCutSalePrice
		o.OtherPrice = fpmSaleOutOrderItem.OtherPrice
		o.Remark = fpmSaleOutOrderItem.Remark
		o.SaleTaxRate = 0
		o.SalePlanOrderItemId = fpmSaleOutOrderItem.SalePlanOrderItemId
		o.SumStockId = fpmSaleOutOrderItem.SumStockId
		// 成品工艺、成品等级、成品成分、成品备注信息
		o.ProductCraft = fpmSaleOutOrderItem.ProductCraft
		o.ProductLevelId = fpmSaleOutOrderItem.ProductLevelId
		o.ProductIngredient = fpmSaleOutOrderItem.ProductIngredient
		o.ProductRemark = fpmSaleOutOrderItem.ProductRemark
		o.MeasurementUnitId = fpmSaleOutOrderItem.UnitId
		o.AuxiliaryUnitId = fpmSaleOutOrderItem.AuxiliaryUnitId
		if o.AuxiliaryUnitId == 0 {
			o.AuxiliaryUnitId = o.MeasurementUnitId
		}

		// 初始化 itemfcs 列表
		itemfcs := make(shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailFcParamList, 0)
		for _, fpmSaleOutOrderItemFc := range fpmSaleOutOrderItem.ItemFCData {
			q := shouldCollectOrderStructure.AddProductSaleShouldCollectOrderDetailFcParam{}
			q.Id = fpmSaleOutOrderItemFc.Id
			q.Roll = fpmSaleOutOrderItemFc.Roll
			q.WarehouseId = fpmSaleOutOrderItemFc.WarehouseId
			q.WarehouseBinId = fpmSaleOutOrderItemFc.WarehouseBinId
			q.VolumeNumber = fpmSaleOutOrderItemFc.VolumeNumber
			q.SrcDetailId = fpmSaleOutOrderItem.Id
			q.SrcDetailFcId = fpmSaleOutOrderItemFc.Id
			q.BaseUnitWeight = fpmSaleOutOrderItemFc.BaseUnitWeight
			q.PaperTubeWeight = fpmSaleOutOrderItemFc.PaperTubeWeight
			q.WeightError = fpmSaleOutOrderItemFc.WeightError
			q.ActuallyWeight = fpmSaleOutOrderItemFc.ActuallyWeight
			q.SettleErrorWeight = fpmSaleOutOrderItemFc.SettleErrorWeight
			q.SettleWeight = fpmSaleOutOrderItemFc.SettleWeight
			q.Length = fpmSaleOutOrderItemFc.Length
			q.FinishProductWidthUnitId = fpmSaleOutOrderItemFc.FinishProductWidthUnitId
			q.FinishProductGramWeightUnitId = fpmSaleOutOrderItemFc.FinishProductGramWeightUnitId
			q.StockRemark = fpmSaleOutOrderItemFc.StockRemark
			q.Remark = fpmSaleOutOrderItemFc.Remark
			q.ArrangeOrderNo = fpmSaleOutOrderItemFc.ArrangeOrderNo
			q.StockId = fpmSaleOutOrderItemFc.StockId
			q.SumStockId = fpmSaleOutOrderItemFc.SumStockId
			q.UnitId = fpmSaleOutOrderItemFc.UnitId
			q.AuxiliaryUnitId = fpmSaleOutOrderItemFc.AuxiliaryUnitId
			if q.AuxiliaryUnitId == 0 {
				q.AuxiliaryUnitId = q.UnitId
			}
			itemfcs = append(itemfcs, q)
		}
		o.ItemFcList = itemfcs
		items = append(items, o)
	}
	r.ItemData = items
	data = r
	return
}

type DeleteFpmSaleOutOrderParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmSaleOutOrderData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type SaleOutOrderParam struct {
	structure_base.Param
	Id uint64 `json:"id"`
}

type MPUpdateFpmSaleOutOrderStatusPassData struct {
	structure_base.ResponseData
	Id                   uint64                         `json:"id"`
	CreateType           cus_const.WarehouseGoodOutType `json:"create_type"` // 创建的单类型
	SrcType              cus_const.ArrangeOrderFrom
	CreateId             uint64 `json:"create_id"`               // 创建的单id
	ShouldCollectOrderId uint64 `json:"should_collect_order_id"` // 创建的应收单id(成品销售单)
}
