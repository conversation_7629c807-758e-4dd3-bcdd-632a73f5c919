package grey_fabric_manage

import (
	"context"
	"github.com/gin-gonic/gin"
	system_consts "hcscm/common/system_consts"
	"hcscm/extern/pb/payable"
	salePb "hcscm/extern/pb/sale"
	stock_pb "hcscm/extern/pb/stock"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/redis"
	"hcscm/server/system"
	svc "hcscm/service/grey_fabric_manage"
	produceSvc "hcscm/service/produce"
	saleSvc "hcscm/service/sale"
	structure "hcscm/structure/grey_fabric_manage"
	sys_structure "hcscm/structure/payable2"
	produceStructure "hcscm/structure/produce"
	saleStructure "hcscm/structure/sale"
	"hcscm/tools"
	"time"
	structure_base "hcscm/structure/system"
)

// @Tags		【坯布生产收货单】
// @Summary	添加坯布生产收货单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddGfmProduceReceiveOrderParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.AddGfmProduceReceiveOrderData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/addGfmProduceReceiveOrder [post]
func AddGfmProduceReceiveOrder(c *gin.Context) {
	var (
		q    = &structure.AddGfmProduceReceiveOrderParam{}
		data = structure.AddGfmProduceReceiveOrderData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	删除坯布生产收货单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.DeleteGfmProduceReceiveOrderParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.DeleteGfmProduceReceiveOrderData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/deleteGfmProduceReceiveOrder [delete]
func DeleteGfmProduceReceiveOrder(c *gin.Context) {
	var (
		q    = &structure.DeleteGfmProduceReceiveOrderParam{}
		data = structure.DeleteGfmProduceReceiveOrderData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Delete(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	更新坯布生产收货单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateGfmProduceReceiveOrderParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.UpdateGfmProduceReceiveOrderData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrder [put]
func UpdateGfmProduceReceiveOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateGfmProduceReceiveOrderParam{}
		data = structure.UpdateGfmProduceReceiveOrderData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	更新坯布生产收货单业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateGfmProduceReceiveOrderBusinessCloseParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.UpdateGfmProduceReceiveOrderData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderBusinessClose [put]
func UpdateGfmProduceReceiveOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateGfmProduceReceiveOrderBusinessCloseParam{}
		data = structure.UpdateGfmProduceReceiveOrderData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	更新坯布生产收货单状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateGfmProduceReceiveOrderStatusParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.UpdateGfmProduceReceiveOrderStatusParam{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusWait [put]
func UpdateGfmProduceReceiveOrderStatusWait(c *gin.Context) {
	var (
		q                    = &structure.UpdateGfmProduceReceiveOrderStatusParam{}
		data                 = structure.UpdateGfmProduceReceiveOrderStatusData{}
		pSvc                 = svc.NewGfmProduceReceiveOrderService()
		stockSvc             = svc.NewGfmWarehouseService()
		updateDetailParams   structure.UpdateGfmWarehouseParamList
		rLocks               = make(redis.LockForRedisList, 0)
		err                  error
		updateItems          produceStructure.UpdateProductionChangeOrderAuditParamList
		salePlanOrderItemIds []uint64
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, updateDetailParams, updateItems, salePlanOrderItemIds, err = pSvc.UpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateDetailParams) > 0 {
			rLocks, _, err = stockSvc.UpdateDetails(ctx, tx, rLocks, updateDetailParams)
			if err != nil {
				return
			}
		}
		if len(updateItems) != 0 {
			// 更新生产通知单
			var (
				produceSvc          = produceSvc.NewProductionNotifyOrderService()
				salePlanOrderSvc    = saleSvc.NewSaleProductPlanOrderService(ctx, false)
				updateSalePlanItems saleStructure.ProductionPlanOrderModifySalPlanOrderList
			)
			updateSalePlanItems, err = produceSvc.UpdateProductionNotifyOrderReceive(ctx, tx, rLocks, updateItems)
			if err != nil {
				return
			}
			err = salePlanOrderSvc.AuditUpdateSaleProductPlanOrderGfDetailDetails(ctx, tx, rLocks, updateSalePlanItems)
			if err != nil {
				return
			}
		}

		if len(salePlanOrderItemIds) > 0 {
			err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, system_consts.SituStatusGFProductionOrder,
				false, id, "坯布生产进仓单消审")
			if err != nil {
				return
			}
		}
	}

	// 作废应付单
	err = voidPayableProcessing(ctx, tx, q.Id.ToUint64())
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		err = stock_pb.NewClientStockRawMaterial().RefundRawMatlStock(ctx, tx, id, "坯布生产收货单消审")
		if err != nil {
			return
		}
	}
	return
}

// @Tags		【坯布生产收货单】
// @Summary	更新坯布生产收货单状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateGfmProduceReceiveOrderStatusParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.UpdateGfmProduceReceiveOrderStatusParam{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusPass [put]
func UpdateGfmProduceReceiveOrderStatusPass(c *gin.Context) {
	var (
		q                    = &structure.UpdateGfmProduceReceiveOrderStatusParam{}
		data                 = structure.UpdateGfmProduceReceiveOrderStatusData{}
		pSvc                 = svc.NewGfmProduceReceiveOrderService()
		addDetailParams      structure.AddGfmWarehouseParamList
		getItemAndFcIds      structure.GetItemAndFcIds
		rLocks               = make(redis.LockForRedisList, 0)
		stockSvc             = svc.NewGfmWarehouseService()
		detailIDsMap         = make(map[uint64]uint64)
		sumIDsMap            = make(map[uint64]uint64)
		err                  error
		orderNo              = make([]string, 0)
		updateItems          produceStructure.UpdateProductionChangeOrderAuditParamList
		salePlanOrderItemIds []uint64
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, addDetailParams, getItemAndFcIds, updateItems, salePlanOrderItemIds, err = pSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		orderNo = append(orderNo, data.OrderNo)
		if len(addDetailParams) > 0 {
			rLocks, detailIDsMap, sumIDsMap, err = stockSvc.AddDetails(ctx, tx, rLocks, addDetailParams)
			if err != nil {
				return
			}
		}
		// 更新库存id（汇总、详细）到ids
		err = pSvc.UpdateStockId(ctx, tx, id, detailIDsMap, sumIDsMap, getItemAndFcIds)
		if err != nil {
			return
		}

		if len(updateItems) != 0 {
			// 更新生产通知单
			var (
				produceSvc          = produceSvc.NewProductionNotifyOrderService()
				salePlanOrderSvc    = saleSvc.NewSaleProductPlanOrderService(ctx, false)
				updateSalePlanItems saleStructure.ProductionPlanOrderModifySalPlanOrderList
			)
			updateSalePlanItems, err = produceSvc.UpdateProductionNotifyOrderReceive(ctx, tx, rLocks, updateItems)
			if err != nil {
				return
			}
			err = salePlanOrderSvc.AuditUpdateSaleProductPlanOrderGfDetailDetails(ctx, tx, rLocks, updateSalePlanItems)
			if err != nil {
				return
			}
		}

		if len(salePlanOrderItemIds) > 0 {
			err = salePb.NewSalePlanClient(ctx).UpdateSituStatus(ctx, salePlanOrderItemIds, system_consts.SituStatusGFProductionOrPurchaseReceive,
				true, id, "坯布生产进仓单审核")
			if err != nil {
				return
			}
		}
	}
	// 创建加工费应付单
	err = addPayableProcessingOrderByRec(ctx, tx, q.Id.ToUint64())
	if err != nil {
		return
	}

	return
}

// 创建加工费应付单
func addPayableProcessingOrderByRec(ctx context.Context, tx *mysql_base.Tx, orderIds []uint64) (err error) {
	payableService := payable.NewClientPayableService()
	orderService := svc.NewGfmProduceReceiveOrderService()

	for _, id := range orderIds {
		var order structure.GetGfmProduceReceiveOrderData
		order, err = orderService.Get(ctx, &structure.GetGfmProduceReceiveOrderQuery{Id: id})
		if err != nil {
			return
		}

		req := &sys_structure.AddPayableProcessingParams{
			PayableBaseInfo: sys_structure.PayableBaseInfo{
				SrcOrderType: 3,
				SrcOrderId:   order.Id,
				SrcOrderNo:   order.OrderNo,
				SaleSystemId: order.SaleSystemId,
				SupplierId:   order.SupplierId,
				PayDate:      tools.QueryTime(time.Now().Format("2006-01-02")),
			},
			Items: nil,
		}
		for _, item := range order.ItemData {
			req.Items = append(req.Items, &sys_structure.PayableProcessingItem{
				SrcId:             item.Id,
				Weight:            item.TotalWeight,
				UnitPrice:         item.ProcessSinglePrice,
				OtherPrice:        item.OtherPrice,
				MaterialId:        item.GreyFabricId,
				ColorId:           item.GrayFabricColorId,
				MeasurementUnitId: item.UnitId,
				Width:             item.GreyFabricWidth,
				GramWeight:        item.GreyFabricGramWeight,
				WidthUnitId:       item.GreyFabricWidthUnitId,
				GramWeightUnitId:  item.GreyFabricGramWeightUnitId,
				YarnBatch:         item.YarnBatch,
			})
		}

		err = payableService.AddPayableProduce(ctx, tx, req)
		if err != nil {
			return
		}
	}
	return
}

// @Tags		【坯布生产收货单】
// @Summary	更新坯布生产收货单状态-驳回
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateGfmProduceReceiveOrderStatusParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.UpdateGfmProduceReceiveOrderStatusParam{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusReject [put]
func UpdateGfmProduceReceiveOrderStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateGfmProduceReceiveOrderStatusParam{}
		data = structure.UpdateGfmProduceReceiveOrderStatusData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateStatusReject(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	更新坯布生产收货单状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateGfmProduceReceiveOrderStatusParam{}	true	"创建GfmProduceReceiveOrder"
// @Success	200		{object}	structure.UpdateGfmProduceReceiveOrderStatusParam{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateGfmProduceReceiveOrderStatusCancel [put]
func UpdateGfmProduceReceiveOrderStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateGfmProduceReceiveOrderStatusParam{}
		data = structure.UpdateGfmProduceReceiveOrderStatusData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateStatusCancel(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	获取坯布生产收货单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetGfmProduceReceiveOrderData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrder [get]
func GetGfmProduceReceiveOrder(c *gin.Context) {
	var (
		q    = &structure.GetGfmProduceReceiveOrderQuery{}
		data = structure.GetGfmProduceReceiveOrderData{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	获取坯布生产收货单列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page		query		int	false	"page"
// @Param		size		query		int	false	"size"
// @Param		offset		query		int	false	"offset"
// @Param		limit		query		int	false	"limit"
// @Param		download	query		int	false	"download"
// @Param		Platform	header		int	true	"终端ID"
// @Success	200			{object}	structure.GetGfmProduceReceiveOrderDataList{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrderList [get]
func GetGfmProduceReceiveOrderList(c *gin.Context) {
	var (
		q     = &structure.GetGfmProduceReceiveOrderListQuery{}
		list  = make(structure.GetGfmProduceReceiveOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewGfmProduceReceiveOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		// system.BuildListResponse(c, err, list, total)
		system.BuildListResponseV2(c, q, "坯布生产收货单", err, list, nil, total)

	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	获取坯布生产收货单用料原料信息枚举列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page		query		int	false	"page"
// @Param		size		query		int	false	"size"
// @Param		offset		query		int	false	"offset"
// @Param		limit		query		int	false	"limit"
// @Param		source_id	query		int	false	"单据id"
// @Param		download	query		int	false	"download"
// @Param		Platform	header		int	true	"终端ID"
// @Success	200			{object}	structure.GetGfmProduceReceiveOrderDataList{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getGfmProduceReceiveOrderUseYarnItemListByOrderID [get]
func GetGfmProduceReceiveOrderUseYarnItemListByOrderID(c *gin.Context) {
	var (
		q     = &structure.GetGfmProduceReceiveOrderUseYarnItemListQuery{}
		list  = make(structure.GetGfmProduceReceiveOrderUseYarnItemDataList, 0)
		total int
		err   error
		svc   = svc.NewGfmProduceReceiveOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	list, total, err = svc.GetUseYarnItemListByOrderID(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	根据分录行id，获取坯布生产收货单用料原料信息枚举列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page		query		int	false	"page"
// @Param		size		query		int	false	"size"
// @Param		offset		query		int	false	"offset"
// @Param		limit		query		int	false	"limit"
// @Param		id	query		int	false	"分录行的id"
// @Param		download	query		int	false	"download"
// @Param		Platform	header		int	true	"终端ID"
// @Success	200			{object}	structure.GetPRCUseYarnDataUseShouldPay{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/getPRCUseYarnDataUseShouldPayListByItemID [get]
func GetPRCUseYarnDataUseShouldPayListByItemID(c *gin.Context) {
	var (
		q    = &structure.GetGfmProduceReceiveOrderItemQuery{}
		data = structure.GetPRCUseYarnDataUseShouldPay{}
		svc  = svc.NewGfmProduceReceiveOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.GetPRCUseYarnDataUseShouldPayListByItemID(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	手动更新原料毛重成本
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateBuoyantWeightPriceParam{}	true	"更新入参"
// @Success	200		{object}	structure_base.ResponseData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/updateBuoyantWeightPrice [put]
func UpdateBuoyantWeightPrice(c *gin.Context) {
	var (
		param structure.UpdateBuoyantWeightPriceParam
		data  structure_base.ResponseData
		svc   = svc.NewGfmProduceReceiveOrderService()
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, &data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	err = svc.UpdateBuoyantWeightPrice(ctx, param)
	if err != nil {
		return
	}

	return
}

// @Tags		【坯布生产收货单】
// @Summary	自动动更新原料毛重成本
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateBuoyantWeightPriceParam{}	true	"更新入参"
// @Success	200		{object}	structure_base.ResponseData{}
// @Router		/hcscm/admin/v1/grey_fabric_manage/gfmProduceReceiveOrder/autoUpdateBuoyantWeightPrice [put]
func AutoUpdateBuoyantWeightPrice(c *gin.Context) {
	var (
		param structure.UpdateBuoyantWeightPriceParam
		data  structure_base.ResponseData
		svc   = svc.NewGfmProduceReceiveOrderService()
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, &data)
	}()

	err = system.ShouldBind(c, &param)
	if err != nil {
		return
	}

	err = svc.AutoUpdateBuoyantWeightPrice(ctx, param)
	if err != nil {
		return
	}

	return
}
