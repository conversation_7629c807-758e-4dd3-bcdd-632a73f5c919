package product

import (
	cus_const "hcscm/common/product"
	"hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddFpmSaleAllocateInOrderParamList []AddFpmSaleAllocateInOrderParam

func (r AddFpmSaleAllocateInOrderParamList) Adjust() {

}

type AddFpmSaleAllocateInOrderParam struct {
	structure_base.Param
	ItemData               AddFpmInOrderItemParamList    `json:"item_data"`            // 成品信息
	InOrderType            cus_const.WarehouseGoodInType `json:"in_order_type"`        // 入仓类型
	SaleAllocateOutId      uint64                        `json:"-"`                    // 成品销售调拨出仓单id
	SaleAllocateOutOrderNo string                        `json:"-"`                    // 成品销售调拨出仓单单号
	SaleSystemId           uint64                        `json:"sale_system_id"`       // 营销体系id，必填
	WarehouseId            uint64                        `json:"warehouse_id"`         // 仓库id
	WarehouseOutId         uint64                        `json:"warehouse_out_id"`     // 调出仓库id
	CustomerId             uint64                        `json:"customer_id"`          // 客户id
	StoreKeeperId          uint64                        `json:"store_keeper_id"`      // 仓管员id（关联user.id）
	SaleUserId             uint64                        `json:"sale_user_id"`         // 销售员id
	SaleFollowerId         uint64                        `json:"sale_follower_id"`     // 销售跟单员id
	DriverId               string                        `json:"driver_id"`            // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId     uint64                        `json:"logistics_company_id"` // 物流公司id
	WarehouseInTime        tools.QueryTime               `json:"warehouse_in_time"`    // 进仓日期
	Remark                 string                        `json:"remark"`               // 备注
	SaleMode               sale.SaleOrderType            `json:"sale_mode"`
	ArrangeOrderId         uint64                        `json:"arrange_order_id"` //调拨出仓单对应的配布id
}

type AddFpmSaleAllocateInOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateFpmSaleAllocateInOrderParam struct {
	structure_base.Param
	ItemData           AddFpmInOrderItemParamList `json:"item_data"` // 坯布信息
	Id                 uint64                     `json:"id"`
	SaleSystemId       uint64                     `json:"sale_system_id"`       // 营销体系id，必填
	WarehouseId        uint64                     `json:"warehouse_id"`         // 仓库id
	WarehouseOutId     uint64                     `json:"warehouse_out_id"`     // 调出仓库id
	CustomerId         uint64                     `json:"customer_id"`          // 客户id
	StoreKeeperId      uint64                     `json:"store_keeper_id"`      // 仓管员id（关联user.id）
	SaleUserId         uint64                     `json:"sale_user_id"`         // 销售员id
	SaleFollowerId     uint64                     `json:"sale_follower_id"`     // 销售跟单员id
	DriverId           string                     `json:"driver_id"`            // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId uint64                     `json:"logistics_company_id"` // 物流公司id
	WarehouseInTime    tools.QueryTime            `json:"warehouse_in_time"`    // 进仓日期
	Remark             string                     `json:"remark"`               // 备注
}

type UpdateFpmSaleAllocateInOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// 列表表使用
func (r AddFpmSaleAllocateInOrderParam) GetTotalPWL() (totalWeight int, totalRoll int, totalLength int) {
	for _, v := range r.ItemData {
		tw, tl, te, tpp := 0, 0, 0, 0
		for _, v2 := range v.ItemFCData {
			tw += v2.BaseUnitWeight
			tl += v2.Length
			te += v2.WeightError
			tpp += v2.PaperTubeWeight
		}
		//
		// weightPrice := tools.DecimalDiv(float64(tw*v.UnitPrice), 10000)
		// lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), 10000)
		// tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
		// tp = tempPrice + v.OtherPrice
		// 安全相除计算并四舍五入
		// totalPrice += tp
		totalRoll += v.InRoll
		totalLength += tl
		totalWeight += tw
	}
	return
}

func (r *AddFpmSaleAllocateInOrderParam) Swap2ListParam(req GetFpmSaleAllocateOutOrderData) {
	r.SaleAllocateOutId = req.Id
	r.SaleAllocateOutOrderNo = req.OrderNo
	r.SaleSystemId = req.SaleSystemId
	r.WarehouseId = req.WarehouseInId
	r.WarehouseOutId = req.WarehouseId
	r.CustomerId = req.CustomerId
	r.StoreKeeperId = req.StoreKeeperId
	r.SaleUserId = req.SaleUserId
	r.SaleFollowerId = req.SaleFollowerId
	r.DriverId = req.DriverId
	r.LogisticsCompanyId = req.LogisticsCompanyId
	r.WarehouseInTime = tools.QueryTime(req.WarehouseOutTime.Date())
	r.Remark = req.Remark
	r.SaleMode = req.SaleMode
	r.ArrangeOrderId = req.ArrangeOrderId
}

func (param *AddFpmSaleAllocateInOrderParam) CheckVNumber() error {
	return checkItemFCDataVolumeNumbers(param.ItemData)
}

func (param *UpdateFpmSaleAllocateInOrderParam) CheckVNumber() error {
	return checkItemFCDataVolumeNumbers(param.ItemData)
}

func (r UpdateFpmSaleAllocateInOrderParam) GetTotalPWL() (totalWeight int, totalRoll int, totalLength int) {
	for _, v := range r.ItemData {
		tw, tl, te, tpp := 0, 0, 0, 0
		for _, v2 := range v.ItemFCData {
			tw += v2.BaseUnitWeight
			tl += v2.Length
			te += v2.WeightError
			tpp += v2.PaperTubeWeight
		}
		//
		// weightPrice := tools.DecimalDiv(float64(tw*v.UnitPrice), 10000)
		// lenPrice := tools.DecimalDiv(float64(tl*v.LengthUnitPrice), 10000)
		// tempPrice := tools.IntRoundHalf2One(weightPrice + lenPrice)
		// tp = tempPrice + v.OtherPrice
		// 安全相除计算并四舍五入
		// totalPrice += tp
		totalRoll += v.InRoll
		totalLength += tl
		totalWeight += tw
	}
	return
}

type UpdateFpmSaleAllocateInOrderBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateFpmSaleAllocateInOrderStatusParam struct {
	structure_base.Param
	Id          tools.QueryIntList        `json:"id"`
	AuditStatus common_system.OrderStatus `json:"audit_status"`
}

type UpdateFpmSaleAllocateInOrderStatusData struct {
	structure_base.ResponseData
	WarehouseInTime tools.MyTime `json:"warehouse_in_time"` // 进仓时间
}

type GetFpmSaleAllocateInOrderQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetFpmSaleAllocateInOrderListQuery struct {
	structure_base.ListQuery
	InOrderType            cus_const.WarehouseGoodInType `form:"in_order_type"`              // 入仓类型
	IsDropList             int                           `form:"is_drop_list"`               // 回收站
	SaleAllocateOutId      uint64                        `form:"sale_allocate_out_id"`       // 成品销售调拨出仓单id
	SaleAllocateOutOrderNo string                        `form:"sale_allocate_out_order_no"` // 成品销售调拨出仓单单号
	SaleSystemId           uint64                        `form:"sale_system_id"`             // 营销体系id，必填
	WarehouseId            uint64                        `form:"warehouse_id"`               // 仓库id
	WarehouseOutId         uint64                        `form:"warehouse_out_id"`           // 调出仓库id
	CustomerId             uint64                        `form:"customer_id"`                // 客户id
	StoreKeeperId          uint64                        `form:"store_keeper_id"`            // 仓管员id（关联user.id）
	SaleUserId             uint64                        `form:"sale_user_id"`               // 销售员id
	SaleFollowerId         uint64                        `form:"sale_follower_id"`           // 销售跟单员id
	DriverId               string                        `form:"driver_id"`                  // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId     uint64                        `form:"logistics_company_id"`       // 物流公司id
	WarehouseInTime        tools.QueryTime               `form:"warehouse_in_time"`          // 进仓日期
	Remark                 string                        `form:"remark"`                     // 备注
	TotalRoll              int                           `form:"total_roll"`                 // 匹数总计
	TotalWeight            int                           `form:"total_weight"`               // 数量总计
	TotalLength            int                           `form:"total_length"`               // 长度总计
	UnitId                 uint64                        `form:"unit_id"`                    // 单位id
	BusinessClose          bool                          `form:"business_close"`             // 业务关闭
	BusinessCloseUserId    uint64                        `form:"business_close_user_id"`     // 业务关闭操作人
	BusinessCloseUserName  string                        `form:"business_close_user_name"`   // 业务关闭操作人名
	BusinessCloseTime      tools.QueryTime               `form:"business_close_time"`        // 业务关闭时间
	DepartmentId           uint64                        `form:"department_id"`              // 下单用户所属部门
	OrderNo                string                        `form:"order_no"`                   // 单据编号
	Number                 int                           `form:"number"`                     // 编号流水：每日重新更新
	AuditStatus            tools.QueryIntList            `form:"audit_status"`               // 审核状态
	AuditorId              uint64                        `form:"auditor_id"`                 // 审核人ID （关联user.id）
	AuditorName            string                        `form:"auditor_name"`               // 审核人名称
	AuditTime              tools.QueryTime               `form:"audit_time"`                 // 审核时间
	InTimeBegin            tools.QueryTime               `form:"in_time_begin"`              // 进仓时间开始
	InTimeEnd              tools.QueryTime               `form:"in_time_end"`                // 进仓时间结束
	CreateStartDate        tools.QueryTime               `form:"create_start_date"`          // 创建时间开始
	CreateEndDate          tools.QueryTime               `form:"create_end_date"`            // 创建时间结束
	AuditStartDate         tools.QueryTime               `form:"audit_start_date"`           // 审核时间开始
	AuditEndDate           tools.QueryTime               `form:"audit_end_date"`             // 审核时间结束
	VoucherNumber          string                        `form:"voucher_number"`             // 凭证
}

func (r GetFpmSaleAllocateInOrderListQuery) Adjust() {

}

type GetFpmSaleAllocateInOrderData struct {
	structure_base.RecordData
	ItemData               GetFpmInOrderItemDataList   `json:"item_data"`                  // 成品信息
	SaleAllocateOutId      uint64                      `json:"sale_allocate_out_id"`       // 成品销售调拨出仓单id
	SaleAllocateOutOrderNo string                      `json:"sale_allocate_out_order_no"` // 成品销售调拨出仓单单号
	ArrangeOrderId         uint64                      `json:"arrange_order_id"`           // 配布单id
	ArrangeOrderNo         string                      `json:"arrange_order_no"`           // 配布单号
	SaleSystemId           uint64                      `json:"sale_system_id"`             // 营销体系id，必填
	WarehouseId            uint64                      `json:"warehouse_id"`               // 仓库id
	WarehouseOutId         uint64                      `json:"warehouse_out_id"`           // 调出仓库id
	CustomerId             uint64                      `json:"customer_id"`                // 客户id
	StoreKeeperId          uint64                      `json:"store_keeper_id"`            // 仓管员id（关联user.id）
	SaleUserId             uint64                      `json:"sale_user_id"`               // 销售员id
	SaleFollowerId         uint64                      `json:"sale_follower_id"`           // 销售跟单员id
	DriverId               string                      `json:"driver_id"`                  // 司机id，逗号分割（关联user.id）
	LogisticsCompanyId     uint64                      `json:"logistics_company_id"`       // 物流公司id
	WarehouseInTime        tools.MyTime                `json:"warehouse_in_time"`          // 进仓日期
	Remark                 string                      `json:"remark"`                     // 备注
	TotalRoll              int                         `json:"total_roll"`                 // 匹数总计
	TotalWeight            int                         `json:"total_weight"`               // 数量总计
	TotalLength            int                         `json:"total_length"`               // 长度总计
	UnitId                 uint64                      `json:"unit_id"`                    // 单位id
	BusinessClose          common_system.BusinessClose `json:"business_close"`             // 业务关闭
	BusinessCloseUserId    uint64                      `json:"business_close_user_id"`     // 业务关闭操作人
	BusinessCloseUserName  string                      `json:"business_close_user_name"`   // 业务关闭操作人名
	BusinessCloseTime      tools.MyTime                `json:"business_close_time"`        // 业务关闭时间
	DepartmentId           uint64                      `json:"department_id"`              // 下单用户所属部门
	OrderNo                string                      `json:"order_no"`                   // 单据编号
	Number                 int                         `json:"number"`                     // 编号流水：每日重新更新
	AuditStatus            common_system.OrderStatus   `json:"audit_status"`               // 审核状态
	AuditorId              uint64                      `json:"auditor_id"`                 // 审核人ID （关联user.id）
	AuditorName            string                      `json:"auditor_name"`               // 审核人名称
	AuditTime              tools.MyTime                `json:"audit_time"`                 // 审核时间

	// 转义
	BusinessCloseName    string `json:"business_close_name"`    // 业务关闭名称Name
	AuditStatusName      string `json:"audit_status_name"`      // 审核状态名称Name
	UnitName             string `json:"unit_name"`              // 单位名称name
	OutOrderTypeName     string `json:"in_order_type_name"`     // 出仓类型名称name
	SaleSystemName       string `json:"sale_system_name"`       // 营销体系名称name
	CustomerName         string `json:"customer_name"`          // 客户名称name
	StoreKeeperName      string `json:"store_keeper_name"`      // 仓管员名称name
	WarehouseName        string `json:"warehouse_name"`         // 仓库名称name
	WarehouseOutName     string `json:"warehouse_out_name"`     // 调出仓库名称name
	DriverName           string `json:"driver_name"`            // 司机名
	SaleUserName         string `json:"sale_user_name"`         // 销售员名
	SaleFollowerName     string `json:"sale_follower_name"`     // 销售跟单员名
	LogisticsCompanyName string `json:"logistics_company_name"` // 物流公司名
}

type GetFpmSaleAllocateInOrderDataList []GetFpmSaleAllocateInOrderData

func (g GetFpmSaleAllocateInOrderDataList) Adjust() {

}

type DeleteFpmSaleAllocateInOrderParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteFpmSaleAllocateInOrderData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}
