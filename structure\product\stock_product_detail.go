package product

import (
	"hcscm/common/errors"
	common "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	"hcscm/middleware"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"time"
)

// AddWeightItemId 添加库存的细码记录id(获取idMap中库存详情id用)
type AddStockProductDetailParam struct {
	structure_base.Param
	StockProductDetailId          uint64                     `json:"stock_product_detail_id"`
	AddWeightItemId               uint64                     `json:"-"`                                  // 添加库存的细码记录id(获取idMap中库存详情id用)
	StockProductId                uint64                     `json:"stock_product_id"`                   // 库存id
	OutStockProductDetailId       uint64                     `json:"out_stock_product_detail_id"`        // 出仓库存id（调拨和调整）
	WarehouseBinId                uint64                     `json:"warehouse_bin_id"`                   // 仓位id
	DyeFactoryColorCode           string                     `json:"dye_factory_color_code"`             // 染厂色号
	ProductColorId                uint64                     `json:"product_color_id"`                   // 颜色id
	ProductColorKindId            uint64                     `json:"product_color_kind_id"`              // 颜色类别id
	WarehouseId                   uint64                     `json:"warehouse_id"`                       // 仓库id
	CustomerId                    uint64                     `json:"customer_id"`                        // 所属客户id
	ProductId                     uint64                     `json:"product_id"`                         // 成品id
	FinishProductWidth            string                     `json:"finish_product_width"`               // 成品幅宽
	FinishProductGramWeight       string                     `json:"finish_product_gram_weight"`         // 成品克重
	ProductLevelId                uint64                     `json:"product_level_id"`                   // 成品等级id
	ProductKindId                 uint64                     `json:"product_kind_id"`                    // 布种类型id
	DyelotNumber                  string                     `json:"dyelot_number"`                      // 缸号
	VolumeNumber                  int                        `json:"volume_number"`                      // 卷号
	WeightError                   int                        `json:"weight_error"`                       // 空差数量(公斤)
	PaperTubeWeight               int                        `json:"paper_tube_weight"`                  // 纸筒数量(公斤)
	Weight                        int                        `json:"weight"`                             // 数量
	Length                        int                        `json:"length"`                             // 长度
	Roll                          int                        `json:"roll"`                               // 匹数
	DigitalCode                   string                     `json:"digital_code"`                       // 数字码
	ContractNumber                string                     `json:"contract_number"`                    // 合同号
	CustomerPoNum                 string                     `json:"customer_po_num"`                    // 客户po号
	CustomerAccountNum            string                     `json:"customer_account_num"`               // 客户款号
	ShelfNo                       string                     `json:"shelf_no"`                           // 货架号
	MeasurementUnitId             uint64                     `json:"measurement_unit_id"`                // 计量单位id
	WarehouseInType               common.WarehouseGoodInType `json:"warehouse_in_type"`                  // 来源类型
	WarehouseInOrderId            uint64                     `json:"warehouse_in_order_id"`              // 进仓单id
	WarehouseInOrderNo            string                     `json:"warehouse_in_order_no"`              // 进仓单号
	CheckTime                     time.Time                  `json:"check_time"`                         // 盘点时间
	CheckUserId                   uint64                     `json:"check_user_id"`                      // 盘点人id
	StockProductKey               string                     `json:"stock_product_key"`                  // 汇总库存key值(用于创建多条库存细码的时候)
	BookRoll                      int                        `json:"book_roll"`                          // 预约匹数
	BookWeight                    int                        `json:"book_weight"`                        // 预约数量
	IsNoNeedCheckStock            bool                       `json:"-"`                                  // 是否不需要校验库存是否足够（出仓消审或者进仓审核用）
	FinishProductWidthUnitId      uint64                     `json:"finish_product_width_unit_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                     `json:"finish_product_gram_weight_unit_id"` // 成品克重单位id(字典)
	BarCode                       string                     `json:"bar_code"`                           // 条码
	QrCode                        string                     `json:"qr_code"`                            // 二维码
	IsMayBePassVolumeNumber       bool                       `json:"-"`                                  // 是否可能直接更新卷号
	SrcId                         uint64                     `json:"src_id"`                             // 来源id
	SupplierId                    uint64                     `json:"supplier_id"`                        // 供应商id
	ProductRemark                 string                     `json:"product_remark"`                     // 成品备注
	Remark                        string                     `json:"remark"`                             // 库存备注
	InternalRemark                string                     `json:"internal_remark"`                    // 内部备注
	SettleErrorWeight             int                        `json:"settle_error_weight"`                // 结算空差
}

type AddStockProductDetailParamList []*AddStockProductDetailParam

func (r AddStockProductDetailParamList) Adjust() {

}

type AddStockProductDetailData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateStockProductDetailParam struct {
	structure_base.Param
	InOrderWeightItemId           uint64                     `json:"-"` // 添加库存的细码记录id(获取idMap中库存详情id用)
	Id                            uint64                     `json:"id" relate:"stock_product_detail_id"`
	StockProductId                uint64                     `json:"stock_product_id"`                   // 库存id
	AdjustStockProductId          uint64                     `json:"-"`                                  // 调整库存id
	WarehouseBinId                uint64                     `json:"warehouse_bin_id"`                   // 仓位id
	TargetWarehouseBinId          uint64                     `json:"target_warehouse_bin_id"`            // 移架目标仓位id（移架）
	WarehouseOutOrderId           uint64                     `json:"warehouse_out_order_id"`             // 出仓单id
	WarehouseOutOrderNo           string                     `json:"warehouse_out_order_no"`             // 出仓单号
	WarehouseInType               common.WarehouseGoodInType `json:"warehouse_in_type"`                  // 来源类型
	WarehouseInOrderId            uint64                     `json:"warehouse_in_order_id"`              // 进仓单id
	WarehouseInOrderNo            string                     `json:"warehouse_in_order_no"`              // 进仓单号
	DyeFactoryColorCode           string                     `json:"dye_factory_color_code"`             // 染厂色号
	ProductColorId                uint64                     `json:"product_color_id"`                   // 颜色id
	ProductColorKindId            uint64                     `json:"product_color_kind_id"`              // 颜色类别id
	WarehouseId                   uint64                     `json:"warehouse_id"`                       // 仓库id
	CustomerId                    uint64                     `json:"customer_id"`                        // 所属客户id
	ProductId                     uint64                     `json:"product_id"`                         // 成品id
	FinishProductWidth            string                     `json:"finish_product_width"`               // 成品幅宽
	FinishProductGramWeight       string                     `json:"finish_product_gram_weight"`         // 成品克重
	FinishProductWidthUnitId      uint64                     `json:"finish_product_width_unit_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                     `json:"finish_product_gram_weight_unit_id"` // 成品克重单位id(字典)
	ProductLevelId                uint64                     `json:"product_level_id"`                   // 成品等级id
	ProductKindId                 uint64                     `json:"product_kind_id"`                    // 布种类型id
	DyelotNumber                  string                     `json:"dyelot_number"`                      // 缸号
	VolumeNumber                  int                        `json:"volume_number"`                      // 卷号
	ProductRemark                 string                     `json:"product_remark"`                     // 成品备注
	WeightError                   int                        `json:"weight_error"`                       // 空差数量(公斤)
	PaperTubeWeight               int                        `json:"paper_tube_weight"`                  // 纸筒数量(公斤)
	Weight                        int                        `json:"weight"`                             // 数量
	Length                        int                        `json:"length"`                             // 长度
	Roll                          int                        `json:"roll"`                               // 匹数
	DigitalCode                   string                     `json:"digital_code"`                       // 数字码
	ContractNumber                string                     `json:"contract_number"`                    // 合同号
	CustomerPoNum                 string                     `json:"customer_po_num"`                    // 客户po号
	CustomerAccountNum            string                     `json:"customer_account_num"`               // 客户款号
	ShelfNo                       string                     `json:"shelf_no"`                           // 货架号
	MeasurementUnitId             uint64                     `json:"measurement_unit_id"`                // 计量单位id
	Remark                        string                     `json:"remark"`                             // 库存备注
	CheckTime                     time.Time                  `json:"check_time"`                         // 盘点时间
	CheckUserId                   uint64                     `json:"check_user_id"`                      // 盘点人id
	StockProductKey               string                     `json:"stock_product_key"`                  // 汇总库存key值(用于更新多条库存细码的时候)
	BookRoll                      int                        `json:"book_roll"`                          // 预约匹数
	BookWeight                    int                        `json:"book_weight"`                        // 预约数量
	Status                        common_system.StockStatus  `json:"status"`                             // 状态
	IsToWait                      bool                       `json:"-"`                                  // 判断是否是消审标志
	IsNoNeedCheckStock            bool                       `json:"-"`                                  // 是否不需要校验库存是否足够（出仓消审或者进仓审核用）
	QrCode                        string                     `json:"qr_code"`                            // 二维码
	Type                          int                        `json:"type"`                               // 修改库存类型 1出仓 2盘点 3移架 4更新汇总 5调整 6退货
	InternalRemark                string                     `json:"internal_remark"`                    // 内部备注
	// 日志预约记录
	StockType   common_system.StockType     // 全局
	OrderId     uint64                      // 单据id
	OrderNo     string                      // 单据编号
	OrderType   common_system.BookOrderType // 预约单类型
	BookLength  int                         // 预约长度
	BookOrderId uint64                      `json:"book_order_id"` // 占用单id
}

type UpdateStockProductDetailParamList []*UpdateStockProductDetailParam

func (i *UpdateStockProductDetailParamList) Adjust() {

}

type UpdateStockProductDetailData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

// 更新库存详情状态（1已入库2配布中3已出库）
type UpdateStockProductDetailStatusParam struct {
	structure_base.Param
	Id []uint64 `json:"id"`
}

type UpdateStockProductDetailStatusData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type DeleteStockProductDetailParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteStockProductDetailData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type GetStockProductDetailQuery struct {
	structure_base.Query
	Id     uint64 `form:"id"`      // id
	QrCode string `form:"qr_code"` // 二维码
}

type GetStockProductDetailListQuery struct {
	structure_base.ListQuery
	StockProductId          uint64                     `form:"stock_product_id"`                      // 库存id
	WarehouseBinId          uint64                     `form:"warehouse_bin_id"`                      // 仓位id
	WarehouseBinIds         []uint64                   `form:"-"`                                     // 仓位ids(仓位名称搜索用)
	WarehouseBinName        string                     `form:"warehouse_bin_Name"`                    // 仓位
	StartWarehouseInTime    tools.QueryTime            `form:"start_warehouse_in_time"`               // 入仓时间
	EndWarehouseInTime      tools.QueryTime            `form:"end_warehouse_in_time"`                 // 入仓时间
	WarehouseOutOrderId     uint64                     `form:"warehouse_out_order_id"`                // 出仓单id
	WarehouseOutOrderNo     string                     `form:"warehouse_out_order_no"`                // 出仓单号
	DyeFactoryColorCode     string                     `form:"dye_factory_color_code"`                // 染厂色号
	ProductColorId          uint64                     `form:"product_color_id"`                      // 颜色id
	ProductColorCode        string                     `form:"product_color_code"`                    // 颜色编号
	ProductColorName        string                     `form:"product_color_name"`                    // 颜色名称
	ProductColorKindId      uint64                     `form:"product_color_kind_id"`                 // 颜色类别id
	ProductColorKindName    string                     `form:"product_color_kind_name"`               // 颜色类别名
	WarehouseId             uint64                     `form:"warehouse_id"`                          // 仓库id
	WarehouseName           string                     `form:"warehouse_name"`                        // 仓库名称
	CustomerId              uint64                     `form:"customer_id"`                           // 所属客户id
	SaleCustomerId          uint64                     `form:"sale_customer_id" relate:"customer_id"` // 购买成品客户id
	ProductId               uint64                     `form:"product_id"`                            // 成品id
	FinishProductWidth      string                     `form:"finish_product_width"`                  // 成品幅宽
	FinishProductGramWeight string                     `form:"finish_product_gram_weight"`            // 成品克重
	ProductLevelId          uint64                     `form:"product_level_id"`                      // 成品等级id
	ProductKindId           uint64                     `form:"product_kind_id"`                       // 布种类型id
	DyelotNumber            string                     `form:"dyelot_number"`                         // 缸号
	VolumeNumber            int                        `form:"volume_number"`                         // 卷号
	ProductRemark           string                     `form:"product_remark"`                        // 成品备注
	WeightError             int                        `form:"weight_error"`                          // 空差数量(公斤)
	PaperTubeWeight         int                        `form:"paper_tube_weight"`                     // 纸筒数量(公斤)
	Weight                  int                        `form:"weight"`                                // 数量
	Length                  int                        `form:"length"`                                // 长度
	Roll                    int                        `json:"roll"`                                  // 匹数(后期需要作废)
	StockRoll               int                        `json:"stock_roll"`                            // 匹数
	DigitalCode             string                     `form:"digital_code"`                          // 数字码
	ContractNumber          string                     `form:"contract_number"`                       // 合同号
	CustomerPoNum           string                     `form:"customer_po_num"`                       // 客户po号
	CustomerAccountNum      string                     `form:"customer_account_num"`                  // 客户款号
	ShelfNo                 string                     `form:"shelf_no"`                              // 货架号
	MeasurementUnitId       uint64                     `form:"measurement_unit_id"`                   // 计量单位id
	Remark                  string                     `form:"remark"`                                // 库存备注
	WarehouseInType         common.WarehouseGoodInType `form:"warehouse_in_type"`                     // 来源类型
	WarehouseInOrderId      uint64                     `form:"warehouse_in_order_id"`                 // 进仓单id
	WarehouseInOrderNo      string                     `form:"warehouse_in_order_no"`                 // 进仓单号
	StockShowType           common.StockShowType       `form:"stock_show_type"`                       // 显示方式
	QualityCheckStatus      common.QualityCheckStatus  `form:"quality_check_status"`                  // 质检状态1未质检2已质检
	ProductCode             string                     `form:"product_code"`                          // 成品编号
	ProductName             string                     `form:"product_name"`                          // 成品名称
	ProductIds              []uint64                   `form:"-"`                                     // 成品ids
	ProductColorIds         []uint64                   `form:"-"`                                     // 成品颜色ids
	StockStatus             common_system.StockStatus  `form:"stock_status"`                          // 库存状态
	StockProductDetailId    uint64                     `form:"stock_product_detail_id"`               // 库存细码id
	BarCode                 string                     `form:"bar_code"`                              // 条码
	QrCode                  string                     `form:"qr_code"`                               // 二维码
}

func (r GetStockProductDetailListQuery) Adjust() {
}

func (r GetStockProductDetailListQuery) AdjustQrAndBarCode() (err error) {
	if r.QrCode == "" && r.BarCode == "" {
		err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeLackMustField, "缺少条码或二维码传参"))
		return err
	}
	return
}

type GetStockProductDetailData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	StockProductId          uint64                     `json:"stock_product_id"`                       // 库存id
	WarehouseBinId          uint64                     `json:"warehouse_bin_id"`                       // 仓位id
	WarehouseBinName        string                     `json:"warehouse_bin_Name" excel:"仓位,必填"`       // 仓位
	WarehouseInTime         tools.MyTime               `json:"warehouse_in_time"`                      // 入仓时间
	WarehouseOutOrderId     uint64                     `json:"warehouse_out_order_id"`                 // 出仓单id
	WarehouseOutOrderNo     string                     `json:"warehouse_out_order_no"`                 // 出仓单号
	ArrangeOrderNo          string                     `json:"arrange_order_no"`                       // 配布单号
	DyeFactoryColorCode     string                     `json:"dye_factory_color_code" excel:"染厂色号"`    // 染厂色号
	ProductColorId          uint64                     `json:"product_color_id"`                       // 颜色id
	ProductColorCode        string                     `json:"product_color_code" excel:"颜色编号,必填"`     // 颜色编号
	ProductColorName        string                     `json:"product_color_name" excel:"颜色名称"`        // 颜色名称
	ProductColorKindId      uint64                     `json:"product_color_kind_id"`                  // 颜色类别id
	ProductColorKindName    string                     `json:"product_color_kind_name" excel:"颜色类别名"`  // 颜色类别名
	WarehouseId             uint64                     `json:"warehouse_id"`                           // 仓库id
	WarehouseName           string                     `json:"warehouse_name" excel:"仓库,必填"`           // 仓库名称
	CustomerId              uint64                     `json:"customer_id"`                            // 所属客户id
	CustomerName            string                     `json:"customer_name" excel:"所属客户,必填"`          // 所属客户名称
	ProductKindId           uint64                     `json:"product_kind_id"`                        // 布种类型id
	ProductKindName         string                     `json:"product_kind_name" excel:"布种名称"`         // 布种类型名称
	ProductId               uint64                     `json:"product_id"`                             // 成品id
	ProductCode             string                     `json:"product_code" excel:"成品编号,必填"`           // 成品编号
	ProductName             string                     `json:"product_name" excel:"成品名称"`              // 成品名称
	ProductLevelId          uint64                     `json:"product_level_id"`                       // 成品等级id
	ProductLevelName        string                     `json:"product_level_name" excel:"成品等级"`        // 成品等级名称
	DyelotNumber            string                     `json:"dyelot_number" excel:"缸号,必填"`            // 缸号
	VolumeNumber            int                        `json:"volume_number" excel:"卷号,必填"`            // 卷号
	ProductRemark           string                     `json:"product_remark" excel:"成品备注"`            // 成品备注
	WeightError             int                        `json:"weight_error" excel:"空差数量"`              // 空差数量(公斤)
	PaperTubeWeight         int                        `json:"paper_tube_weight" excel:"纸筒数量"`         // 纸筒数量(公斤)
	Weight                  int                        `json:"weight" excel:"数量,必填"`                   // 数量
	Length                  int                        `json:"length" excel:"长度"`                      // 长度
	Roll                    int                        `json:"roll"`                                   // 匹数(后期需要作废)
	StockRoll               int                        `json:"stock_roll" excel:"匹数"`                  // 匹数
	AvailableRoll           int                        `json:"available_roll"`                         // 可用匹数
	AvailableWeight         int                        `json:"available_weight"`                       // 可用数量
	DigitalCode             string                     `json:"digital_code" excel:"数字码"`               // 数字码
	ContractNumber          string                     `json:"contract_number"`                        // 合同号
	CustomerPoNum           string                     `json:"customer_po_num"`                        // 客户po号
	CustomerAccountNum      string                     `json:"customer_account_num"`                   // 客户款号
	ShelfNo                 string                     `json:"shelf_no" excel:"货架号"`                   // 货架号
	MeasurementUnitId       uint64                     `json:"measurement_unit_id"`                    // 计量单位id
	MeasurementUnitName     string                     `json:"measurement_unit_name" excel:"计量单位,必填"`  // 计量单位名称
	Remark                  string                     `json:"remark" excel:"库存备注"`                    // 库存备注
	InternalRemark          string                     `json:"internal_remark" excel:"内部备注"`           // 内部备注
	WarehouseInType         common.WarehouseGoodInType `json:"warehouse_in_type"`                      // 来源类型
	WarehouseInTypeName     string                     `json:"warehouse_in_type_name"`                 // 来源类型名称
	WarehouseInOrderId      uint64                     `json:"warehouse_in_order_id"`                  // 进仓单id
	WarehouseInOrderNo      string                     `json:"warehouse_in_order_no"`                  // 进仓单号
	CheckTime               tools.MyTime               `json:"check_time"`                             // 盘点时间
	CheckUserId             uint64                     `json:"check_user_id"`                          // 盘点人id
	CheckUserName           string                     `json:"check_user_name"`                        // 盘点人名称
	BarCode                 string                     `json:"bar_code"`                               // 条码
	QrCode                  string                     `json:"qr_code"`                                // 二维码
	StockStatus             common_system.StockStatus  `json:"stock_status"`                           // 库存状态
	StockStatusName         string                     `json:"stock_status_name"`                      // 库存状态名称
	WeavingOrganizationId   uint64                     `json:"weaving_organization_id"`                // 织造组织id
	WeavingOrganizationCode string                     `json:"weaving_organization_code"`              // 织造组织编号
	WeavingOrganizationName string                     `json:"weaving_organization_name" excel:"组织"`   // 织造组织名称
	YarnCount               string                     `json:"yarn_count" excel:"纱支"`                  // 纱支
	Density                 string                     `json:"density" excel:"密度"`                     // 密度
	FinishProductIngredient string                     `json:"finish_product_ingredient" excel:"成品成分"` // 成品成分
	FinishProductCraft      string                     `json:"finish_product_craft"`                   // 成品工艺
	BleachId                uint64                     `json:"bleach_id"`                              // 漂染性id（字典）
	BleachName              string                     `json:"bleach_name"`                            // 漂染性名称
	PrintDate               string                     `json:"print_date"`                             // 打印日期
	SupplierId              uint64                     `json:"supplier_id"`                            // 供应商id
	SupplierName            string                     `json:"supplier_name" excel:"供应商"`              // 供应商名称
	BuoyantWeightPrice      int                        `json:"buoyant_weight_price"`                   // 毛重成本价
	NetWeightPrice          int                        `json:"net_weight_price"`                       // 净重成本
	FpmCostPriceId          uint64                     `json:"fpm_cost_price_id"`                      // 成本id
}

type GetStockProductDetailDataList []GetStockProductDetailData

func (g GetStockProductDetailDataList) Adjust() {

}

func (g GetStockProductDetailDataList) Pick(id uint64) (data GetStockProductDetailData) {
	for _, i := range g {
		if id == i.Id {
			data = i
			return
		}
	}
	return
}

type GetStockProductDetailDropdownData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	StockProductId          uint64                     `json:"stock_product_id"`          // 库存id
	WarehouseBinId          uint64                     `json:"warehouse_bin_id"`          // 仓位id
	WarehouseBinName        string                     `json:"warehouse_bin_Name"`        // 仓位
	WarehouseInTime         tools.MyTime               `json:"warehouse_in_time"`         // 入仓时间
	WarehouseOutOrderId     uint64                     `json:"warehouse_out_order_id"`    // 出仓单id
	WarehouseOutOrderNo     string                     `json:"warehouse_out_order_no"`    // 出仓单号
	DyeFactoryColorCode     string                     `json:"dye_factory_color_code"`    // 染厂色号
	ProductColorId          uint64                     `json:"product_color_id"`          // 颜色id
	ProductColorCode        string                     `json:"product_color_code"`        // 颜色编号
	ProductColorName        string                     `json:"product_color_name"`        // 颜色名称
	ProductColorKindId      uint64                     `json:"product_color_kind_id"`     // 颜色类别id
	ProductColorKindName    string                     `json:"product_color_kind_name"`   // 颜色类别名
	WarehouseId             uint64                     `json:"warehouse_id"`              // 仓库id
	WarehouseName           string                     `json:"warehouse_name"`            // 仓库名称
	CustomerId              uint64                     `json:"customer_id"`               // 所属客户id
	CustomerName            string                     `json:"customer_name"`             // 所属客户名称
	ProductId               uint64                     `json:"product_id"`                // 成品id
	ProductCode             string                     `json:"product_code"`              // 成品编号
	ProductName             string                     `json:"product_name"`              // 成品名称
	FinishProductCraft      string                     `json:"finish_product_craft"`      // 成品工艺
	FinishProductIngredient string                     `json:"finish_product_ingredient"` // 成品成分
	ProductLevelId          uint64                     `json:"product_level_id"`          // 成品等级id
	ProductLevelName        string                     `json:"product_level_name"`        // 成品等级名称
	ProductKindId           uint64                     `json:"product_kind_id"`           // 布种类型id
	ProductKindName         string                     `json:"product_kind_name"`         // 布种类型名称
	DyelotNumber            string                     `json:"dyelot_number"`             // 缸号
	VolumeNumber            int                        `json:"volume_number"`             // 卷号
	ProductRemark           string                     `json:"product_remark"`            // 成品备注
	WeightError             int                        `json:"weight_error"`              // 空差数量(公斤)
	PaperTubeWeight         int                        `json:"paper_tube_weight"`         // 纸筒数量(公斤)
	Weight                  int                        `json:"weight"`                    // 数量
	Length                  int                        `json:"length"`                    // 长度
	Roll                    int                        `json:"roll"`                      // 匹数(后期需要作废,请不要使用)
	StockRoll               int                        `json:"stock_roll"`                // 匹数
	DigitalCode             string                     `json:"digital_code"`              // 数字码
	ContractNumber          string                     `json:"contract_number"`           // 合同号
	CustomerPoNum           string                     `json:"customer_po_num"`           // 客户po号
	CustomerAccountNum      string                     `json:"customer_account_num"`      // 客户款号
	ShelfNo                 string                     `json:"shelf_no"`                  // 货架号
	MeasurementUnitId       uint64                     `json:"measurement_unit_id"`       // 计量单位id
	MeasurementUnitName     string                     `json:"measurement_unit_name"`     // 计量单位名称
	Remark                  string                     `json:"remark"`                    // 库存备注
	InternalRemark          string                     `json:"internal_remark"`           // 内部备注
	WarehouseInType         common.WarehouseGoodInType `json:"warehouse_in_type"`         // 来源类型
	WarehouseInTypeName     string                     `json:"warehouse_in_type_name"`    // 来源类型名称
	WarehouseInOrderId      uint64                     `json:"warehouse_in_order_id"`     // 进仓单id
	WarehouseInOrderNo      string                     `json:"warehouse_in_order_no"`     // 进仓单号
	QualityCheckStatus      common.QualityCheckStatus  `json:"quality_check_status"`      // 质检状态1未质检2已质检
	QualityCheckStatusName  string                     `json:"quality_check_status_name"` // 质检状态1未质检2已质检
	BarCode                 string                     `json:"bar_code"`                  // 条码
	QrCode                  string                     `json:"qr_code"`                   // 二维码
	CheckTime               tools.MyTime               `json:"check_time"`                // 盘点时间
	CheckUserId             uint64                     `json:"check_user_id"`             // 盘点人id
	CheckUserName           string                     `json:"check_user_name"`           // 盘点人名称
	SupplierId              uint64                     `json:"supplier_id"`               // 供应商id
	SupplierName            string                     `json:"supplier_name"`             // 供应商名称
}

type AddFCByPDAResp struct {
	Id               uint64 `json:"id"`
	ProductId        uint64 `json:"product_id"`         // 成品id
	ProductCode      string `json:"product_code"`       // 成品编号
	ProductName      string `json:"product_name"`       // 成品名称
	ProductColorId   uint64 `json:"product_color_id"`   // 颜色id
	ProductColorCode string `json:"product_color_code"` // 颜色编号
	ProductColorName string `json:"product_color_name"` // 颜色名称
	DyelotNumber     string `json:"dyelot_number"`      // 缸号
	VolumeNumber     int    `json:"volume_number"`      // 卷号
}

func (a AddFCByPDAResp) Adjust() {
}

type GetStockProductDetailDropdownDataList []GetStockProductDetailDropdownData

func (g GetStockProductDetailDropdownDataList) Adjust() {

}

type GetMaxVolumeNumberQuery struct {
	structure_base.Query
	DyelotNumber   string `form:"dyelot_number"`    // 缸号
	ProductColorId uint64 `form:"product_color_id"` // 颜色id
}

type GetMaxVolumeNumberData struct {
	structure_base.ResponseData
	VolumeNumber int `json:"volume_number"` // 卷号
}

type GetStockProductDyelotNumberDetailListQuery struct {
	structure_base.ListQuery
	StockProductId          uint64                     `form:"stock_product_id"`                      // 库存id
	WarehouseBinId          uint64                     `form:"warehouse_bin_id"`                      // 仓位id
	StartWarehouseInTime    tools.QueryTime            `form:"start_warehouse_in_time"`               // 入仓时间
	EndWarehouseInTime      tools.QueryTime            `form:"end_warehouse_in_time"`                 // 入仓时间
	WarehouseOutOrderId     uint64                     `form:"warehouse_out_order_id"`                // 出仓单id
	WarehouseOutOrderNo     string                     `form:"warehouse_out_order_no"`                // 出仓单号
	DyeFactoryColorCode     string                     `form:"dye_factory_color_code"`                // 染厂色号
	ProductColorId          uint64                     `form:"product_color_id"`                      // 颜色id
	ProductColorCode        string                     `form:"product_color_code"`                    // 颜色编号
	ProductColorName        string                     `form:"product_color_name"`                    // 颜色名称
	ProductColorKindId      uint64                     `form:"product_color_kind_id"`                 // 颜色类别id
	WarehouseId             uint64                     `form:"warehouse_id"`                          // 仓库id
	CustomerId              uint64                     `form:"customer_id"`                           // 库存所属客户id
	SupplierId              uint64                     `form:"supplier_id"`                           // 供应商id
	SaleSystemId            uint64                     `form:"sale_system_id"`                        // 营销体系Id
	SaleCustomerId          uint64                     `form:"sale_customer_id" relate:"customer_id"` // 购买成品客户id
	ProductId               uint64                     `form:"product_id"`                            // 成品id
	ProductCode             string                     `form:"product_code"`                          // 成品编号
	ProductName             string                     `form:"product_name"`                          // 成品名称
	ProductLevelId          uint64                     `form:"product_level_id"`                      // 成品等级id
	ProductKindId           uint64                     `form:"product_kind_id"`                       // 布种类型id
	DyelotNumber            string                     `form:"dyelot_number"`                         // 缸号
	ProductRemark           string                     `form:"product_remark"`                        // 成品备注
	DigitalCode             string                     `form:"digital_code"`                          // 数字码
	ContractNumber          string                     `form:"contract_number"`                       // 合同号
	CustomerPoNum           string                     `form:"customer_po_num"`                       // 客户po号
	CustomerAccountNum      string                     `form:"customer_account_num"`                  // 客户款号
	ShelfNo                 string                     `form:"shelf_no"`                              // 货架号
	MeasurementUnitId       uint64                     `form:"measurement_unit_id"`                   // 计量单位id
	Remark                  string                     `form:"remark"`                                // 库存备注
	InternalRemark          string                     `form:"internal_remark"`                       // 内部备注
	WarehouseInType         common.WarehouseGoodInType `form:"warehouse_in_type"`                     // 来源类型
	WarehouseInOrderId      uint64                     `form:"warehouse_in_order_id"`                 // 进仓单id
	WarehouseInOrderNo      string                     `form:"warehouse_in_order_no"`                 // 进仓单号
	AvailableOnly           bool                       `form:"available_only"`                        // 仅显示有可用库存
	StockShowType           common.StockShowType       `form:"stock_show_type"`                       // 显示方式
	WithPrice               bool                       `form:"with_price"`                            // 是否带上价格
	ProductIds              []uint64                   `form:"-"`                                     // 成品ids
	ProductColorIds         []uint64                   `form:"-"`                                     // 成品颜色ids
	FinishProductWidth      string                     `form:"finish_product_width"`                  // 成品幅宽
	FinishProductGramWeight string                     `form:"finish_product_gram_weight"`            // 成品克重
	IsUseByCheckReport      bool                       `form:"-"`                                     // 是否是供应商分组类型
	WarehouseIds            []uint64                   `form:"-"`                                     // 仓库ids
}

type GetStockProductDetailDyelotNumberData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	StockProductId          uint64                     `json:"stock_product_id"`                       // 库存id
	WarehouseBinId          uint64                     `json:"warehouse_bin_id"`                       // 仓位id
	WarehouseBinName        string                     `json:"warehouse_bin_Name"`                     // 仓位
	WarehouseInTime         tools.MyTime               `json:"warehouse_in_time"`                      // 入仓时间
	WarehouseOutOrderId     uint64                     `json:"warehouse_out_order_id"`                 // 出仓单id
	WarehouseOutOrderNo     string                     `json:"warehouse_out_order_no"`                 // 出仓单号
	DyeFactoryColorCode     string                     `json:"dye_factory_color_code"`                 // 染厂色号
	ProductColorId          uint64                     `json:"product_color_id"`                       // 颜色id
	ProductColorCode        string                     `json:"product_color_code"`                     // 颜色编号
	ProductColorName        string                     `json:"product_color_name"`                     // 颜色名称
	ProductColorKindId      uint64                     `json:"product_color_kind_id"`                  // 颜色类别id
	ProductColorKindName    string                     `json:"product_color_kind_name"`                // 颜色类别名
	WarehouseId             uint64                     `json:"warehouse_id"`                           // 仓库id
	WarehouseName           string                     `json:"warehouse_name"`                         // 仓库名称
	CustomerId              uint64                     `json:"customer_id"`                            // 所属客户id
	CustomerName            string                     `json:"customer_name"`                          // 所属客户名称
	ProductId               uint64                     `json:"product_id"`                             // 成品id
	ProductCode             string                     `json:"product_code"`                           // 成品编号
	ProductName             string                     `json:"product_name"`                           // 成品名称
	FinishProductCraft      string                     `json:"finish_product_craft"`                   // 成品工艺
	ProductLevelId          uint64                     `json:"product_level_id"`                       // 成品等级id
	ProductLevelName        string                     `json:"product_level_name"`                     // 成品等级名称
	ProductKindId           uint64                     `json:"product_kind_id"`                        // 布种类型id
	ProductKindName         string                     `json:"product_kind_name"`                      // 布种类型名称
	DyelotNumber            string                     `json:"dyelot_number"`                          // 缸号
	VolumeNumber            int                        `json:"volume_number"`                          // 卷号
	ProductRemark           string                     `json:"product_remark"`                         // 成品备注
	PaperTubeWeight         int                        `json:"paper_tube_weight"`                      // 纸筒数量(公斤)
	Weight                  int                        `json:"weight"`                                 // 数量
	Length                  int                        `json:"length"`                                 // 长度
	Roll                    int                        `json:"roll"`                                   // 匹数(后期需要作废)
	StockRoll               int                        `json:"stock_roll"`                             // 匹数
	BookRoll                int                        `json:"book_roll"`                              // 预约匹数
	AvailableRoll           int                        `json:"available_roll"`                         // 可用匹数
	AvailableWeight         int                        `json:"available_weight"`                       // 可用数量
	DigitalCode             string                     `json:"digital_code"`                           // 数字码
	ContractNumber          string                     `json:"contract_number"`                        // 合同号
	CustomerPoNum           string                     `json:"customer_po_num"`                        // 客户po号
	CustomerAccountNum      string                     `json:"customer_account_num"`                   // 客户款号
	ShelfNo                 string                     `json:"shelf_no"`                               // 货架号
	MeasurementUnitId       uint64                     `json:"measurement_unit_id"`                    // 计量单位id
	MeasurementUnitName     string                     `json:"measurement_unit_name"`                  // 计量单位名称
	Remark                  string                     `json:"remark"`                                 // 库存备注
	UnitPrice               int                        `json:"unit_price"`                             // 单价（kg/分） todo:
	LengthUnitPrice         int                        `json:"length_unit_price"`                      // 长度单价（xx/分） todo:
	WarehouseInType         common.WarehouseGoodInType `json:"warehouse_in_type"`                      // 来源类型
	WarehouseInTypeName     string                     `json:"warehouse_in_type_name"`                 // 来源类型名称
	WarehouseInOrderId      uint64                     `json:"warehouse_in_order_id"`                  // 进仓单id
	WarehouseInOrderNo      string                     `json:"warehouse_in_order_no"`                  // 进仓单号
	SupplierId              uint64                     `json:"supplier_id"`                            // 供应商id
	SupplierName            string                     `json:"supplier_name"`                          // 供应商名称
	WeavingOrganizationId   uint64                     `json:"weaving_organization_id"`                // 织造组织id
	WeavingOrganizationCode string                     `json:"weaving_organization_code"`              // 织造组织编号
	WeavingOrganizationName string                     `json:"weaving_organization_name" excel:"组织"`   // 织造组织名称
	YarnCount               string                     `json:"yarn_count" excel:"纱支"`                  // 纱支
	Density                 string                     `json:"density" excel:"密度"`                     // 密度
	FinishProductIngredient string                     `json:"finish_product_ingredient" excel:"成品成分"` // 成品成分
	StandardWeight          int                        `json:"standard_weight"`                        // 标准数量
	// BookRoll int `json:"book_roll"` // 预约匹数

	SalePrice
}

type GetStockProductDetailDyelotNumberDataList []GetStockProductDetailDyelotNumberData

func (r GetStockProductDetailDyelotNumberDataList) PickByProductCodeAndProductColorCode(productCode, productColorCode string) GetStockProductDetailDyelotNumberData {
	for _, v := range r {
		if v.ProductColorCode == productColorCode && v.ProductCode == productCode {
			return v
		}
	}
	return GetStockProductDetailDyelotNumberData{}
}

func (r GetStockProductDetailDyelotNumberDataList) PickByProductIDAndProductColorCode(productID uint64, productColorCode string) GetStockProductDetailDyelotNumberData {
	for _, v := range r {
		if v.ProductColorCode == productColorCode && v.ProductId == productID {
			return v
		}
	}
	return GetStockProductDetailDyelotNumberData{}
}

func (g GetStockProductDetailDyelotNumberDataList) Adjust() {

}

// 销售价格
type SalePrice struct {
	StandardSalePrice          int    `json:"standard_sale_price"`            // 标准销售报价(大货)
	StandardWeightCutSalePrice int    `json:"standard_weight_cut_sale_price"` // 标准销售报价(剪板(公斤))
	StandardLengthCutSalePrice int    `json:"standard_length_cut_sale_price"` // 标准剪板(米)销售报价
	WeightError                int    `json:"weight_error"`                   // 标准空差 /0.1g
	SaleLevelId                uint64 `json:"sale_level_id"`                  // 销售等级ID
	SaleLevelName              string `json:"sale_level_name"`                // 销售等级名称
	OffsetSalePrice            int    `json:"offset_sale_price"`              // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	OffsetLengthCutSalePrice   int    `json:"offset_length_cut_sale_price"`   // 剪板(米)优惠单价
	OffsetWeightCutSalePrice   int    `json:"offset_weight_cut_sale_price"`   // 剪板(公斤)优惠单价
	OffsetWeightError          int    `json:"offset_weight_error"`            // 优惠空差 /0.1g
	SalePrice                  int    `json:"sale_price"`                     // 销售单价(标准销售报价-优惠单价)(大货)
	WeightCutSalePrice         int    `json:"weight_cut_sale_price"`          // 剪板(公斤)销售单价(标准剪板(公斤)销售报价-剪板(公斤)优惠单价)
	LengthCutSalePrice         int    `json:"length_cut_sale_price"`          // 剪板(米)销售单价(标准剪板(米)销售价格-剪板(米)优惠单价)
	LatestSalePrice            int    `json:"latest_sale_price"`              // 大货上次价格
	LatestLengthCutSalePrice   int    `json:"latest_length_cut_sale_price"`   // 剪板上次价格
	IsDisplayPrice             bool   `json:"is_display_price"`               // 是否显示单价
}

type GetStockProductWeightDetailListQuery struct {
	structure_base.ListQuery
	WarehouseOutOrderId uint64                     `form:"warehouse_out_order_id"`                // 出仓单id
	StockProductId      uint64                     `form:"stock_product_id"`                      // 库存id
	ProductId           uint64                     `form:"product_id"`                            // 成品id
	ProductColorId      uint64                     `form:"product_color_id"`                      // 颜色id
	WarehouseBinId      uint64                     `form:"warehouse_bin_id"`                      // 仓位id
	WarehouseInType     common.WarehouseGoodInType `form:"warehouse_in_type"`                     // 来源类型
	WarehouseInOrderNo  string                     `form:"warehouse_in_order_no"`                 // 进仓单号
	DyelotNumber        string                     `form:"dyelot_number"`                         // 缸号
	SaleCustomerId      uint64                     `form:"sale_customer_id" relate:"customer_id"` // 购买成品客户id
	WithPrice           bool                       `form:"with_price"`                            // 是否带上价格
	Status              common_system.StockStatus  `form:"status"`                                // 库存状态
	WarehouseId         uint64                     `form:"warehouse_id"`                          // 仓库id
}

type GetStockProductDetailWeightData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	StockProductId          uint64                     `json:"stock_product_id"`          // 库存id
	WarehouseBinId          uint64                     `json:"warehouse_bin_id"`          // 仓位id
	WarehouseBinName        string                     `json:"warehouse_bin_Name"`        // 仓位
	WarehouseInTime         tools.MyTime               `json:"warehouse_in_time"`         // 入仓时间
	WarehouseOutOrderId     uint64                     `json:"warehouse_out_order_id"`    // 出仓单id
	WarehouseOutOrderNo     string                     `json:"warehouse_out_order_no"`    // 出仓单号
	DyeFactoryColorCode     string                     `json:"dye_factory_color_code"`    // 染厂色号
	ProductColorId          uint64                     `json:"product_color_id"`          // 颜色id
	ProductColorCode        string                     `json:"product_color_code"`        // 颜色编号
	ProductColorName        string                     `json:"product_color_name"`        // 颜色名称
	ProductColorKindId      uint64                     `json:"product_color_kind_id"`     // 颜色类别id
	ProductColorKindName    string                     `json:"product_color_kind_name"`   // 颜色类别名
	WarehouseId             uint64                     `json:"warehouse_id"`              // 仓库id
	WarehouseName           string                     `json:"warehouse_name"`            // 仓库名称
	CustomerId              uint64                     `json:"customer_id"`               // 所属客户id
	CustomerName            string                     `json:"customer_name"`             // 所属客户名称
	ProductId               uint64                     `json:"product_id"`                // 成品id
	ProductCode             string                     `json:"product_code"`              // 成品编号
	ProductName             string                     `json:"product_name"`              // 成品名称
	FinishProductCraft      string                     `json:"finish_product_craft"`      // 成品工艺
	FinishProductIngredient string                     `json:"finish_product_ingredient"` // 成品成分
	ProductLevelId          uint64                     `json:"product_level_id"`          // 成品等级id
	ProductLevelName        string                     `json:"product_level_name"`        // 成品等级名称
	ProductKindId           uint64                     `json:"product_kind_id"`           // 布种类型id
	ProductKindName         string                     `json:"product_kind_name"`         // 布种类型名称
	DyelotNumber            string                     `json:"dyelot_number"`             // 缸号
	VolumeNumber            int                        `json:"volume_number"`             // 卷号
	ProductRemark           string                     `json:"product_remark"`            // 成品备注
	WeightError             int                        `json:"weight_error"`              // 空差数量(公斤)
	PaperTubeWeight         int                        `json:"paper_tube_weight"`         // 纸筒数量(公斤)
	Weight                  int                        `json:"weight"`                    // 数量
	Length                  int                        `json:"length"`                    // 长度
	Roll                    int                        `json:"roll"`                      // 匹数(后期需要作废)
	StockRoll               int                        `json:"stock_roll"`                // 匹数
	AvailableRoll           int                        `json:"available_roll"`            // 可用匹数
	AvailableWeight         int                        `json:"available_weight"`          // 可用数量
	DigitalCode             string                     `json:"digital_code"`              // 数字码
	ContractNumber          string                     `json:"contract_number"`           // 合同号
	CustomerPoNum           string                     `json:"customer_po_num"`           // 客户po号
	CustomerAccountNum      string                     `json:"customer_account_num"`      // 客户款号
	ShelfNo                 string                     `json:"shelf_no"`                  // 货架号
	MeasurementUnitId       uint64                     `json:"measurement_unit_id"`       // 计量单位id
	MeasurementUnitName     string                     `json:"measurement_unit_name"`     // 计量单位名称
	Remark                  string                     `json:"remark"`                    // 库存备注
	InternalRemark          string                     `json:"internal_remark"`           // 内部备注
	UnitPrice               int                        `json:"unit_price"`                // 单价（kg/分） todo:
	LengthUnitPrice         int                        `json:"length_unit_price"`         // 长度单价（xx/分） todo:
	WarehouseInType         common.WarehouseGoodInType `json:"warehouse_in_type"`         // 来源类型
	WarehouseInTypeName     string                     `json:"warehouse_in_type_name"`    // 来源类型名称
	WarehouseInOrderId      uint64                     `json:"warehouse_in_order_id"`     // 进仓单id
	WarehouseInOrderNo      string                     `json:"warehouse_in_order_no"`     // 进仓单号
	ArrangeOrderId          uint64                     `json:"arrange_order_id"`          // 配布单id
	ArrangeOrderNo          string                     `json:"arrange_order_no"`          // 配布单号
	SupplierId              uint64                     `json:"supplier_id"`               // 供应商id
	SupplierName            string                     `json:"supplier_name"`             // 供应商名称
	SalePrice
	AuxiliaryUnitId   uint64 `json:"auxiliary_unit_id"`   // 辅助单位id
	AuxiliaryUnitName string `json:"auxiliary_unit_name"` // 辅助单位名称
	SettleErrorWeight int    `json:"settle_error_weight"` // 结算空差
	// 条码
	Barcode string `json:"bar_code"`
}

type GetStockProductDetailWeightDataList []GetStockProductDetailWeightData

func (g GetStockProductDetailWeightDataList) Adjust() {

}

type GetWarehouseManageQuery struct {
	structure_base.Query
	StockProductId uint64 `form:"stock_product_id"` // 库存id
	DyelotNumber   string `form:"dyelot_number"`    // 缸号
	ProductColorId uint64 `form:"product_color_id"` // 颜色id
	WarehouseId    uint64 `form:"warehouse_id"`     // 仓库id
}

type UnionOutAndInBaseListQuery struct {
	structure_base.ListQuery
	DyelotNumber   string          `form:"dyelot_number"`    // 缸号
	ProductColorId uint64          `form:"product_color_id"` // 颜色id
	StockDetailID  uint64          `form:"stock_detail_id"`  // 详细库存id
	StockProductId uint64          `form:"stock_product_id"` // 库存id
	BeginTime      tools.QueryTime `form:"begin_time"`       // 查询开始时间
	EndTime        tools.QueryTime `form:"end_time"`         // 查询结束时间
	SupplierID     uint64          `form:"supplier_id"`      // 供应商id
	WarehouseId    uint64          `form:"warehouse_id"`     // 仓库id
	Ids            []uint64        // 存储ids用
	IsNoSkipEmpty  bool
}

type (
	SplitWeightParam struct {
		StockDetailId     uint64 `json:"stock_detail_id"`     // 详细库存id
		StockSumId        uint64 `json:"stock_sum_id"`        // 汇总库存id
		SplitVolumeNumber int    `json:"split_volume_number"` // 拆布卷号
		SplitRoll         int    `json:"split_roll"`          // 拆布匹数
		SplitWeight       int    `json:"split_weight"`        // 拆布数量
		SplitLength       int    `json:"split_length"`        // 拆布长度
	}
	SplitStockDetailParam struct {
		structure_base.Param
		StockDetailId        uint64             `json:"stock_detail_id"`         // 详细库存id
		StockSumId           uint64             `json:"stock_sum_id"`            // 汇总库存id
		CheckRoll            int                `json:"check_roll"`              // 调整原库存匹数
		CheckWeight          int                `json:"check_weight"`            // 调整原库存数量
		CheckLength          int                `json:"check_length"`            // 调整原库存长度
		SplitWeightParamList []SplitWeightParam `json:"split_weight_param_list"` // 新条码信息
	}
	SplitStockDetailParamList struct {
		SplitStockDetailParamList []SplitStockDetailParam `json:"split_stock_detail_param_list"`
	}
)

func (s SplitStockDetailParam) Adjust() {

}

func (s *SplitStockDetailParam) Validate() error {
	var (
		stockDetailCount = 0
		r                = s.SplitWeightParamList[:0]
	)
	for _, param := range s.SplitWeightParamList {
		if param.SplitRoll < 0 || param.SplitWeight < 0 || param.SplitLength < 0 {
			return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 不能为负数"))
		}
		if param.StockDetailId > 0 {
			s.StockDetailId = param.StockDetailId
			s.StockSumId = param.StockSumId
			s.CheckRoll = param.SplitRoll
			s.CheckWeight = param.SplitWeight
			s.CheckLength = param.SplitLength
			stockDetailCount++
		}
		if param.StockDetailId == 0 {
			r = append(r, param)
		}
		if stockDetailCount > 1 {
			return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 详细库存id多传了"))
		}
		if param.SplitVolumeNumber == 0 {
			return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请输入卷号"))
		}
	}
	if stockDetailCount == 0 {
		return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 缺少库存id"))
	}
	if len(s.SplitWeightParamList) < 2 {
		return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请传入拆分信息"))
	}
	s.SplitWeightParamList = r
	return nil
}

func (s SplitStockDetailParamList) Adjust() {

}

// func (s SplitStockDetailParamList) Validate() error {
//	for _, param := range s.SplitStockDetailParamList {
//		if param.StockDetailId == 0 {
//			return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请传入详细库存id"))
//		}
//		if param.StockSumId == 0 {
//			return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请传入汇总库存id"))
//		}
//		if len(param.SplitWeightParamList) == 0 {
//			return middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请传入拆分信息"))
//		}
//	}
//	return nil
// }

type AIGetStockParam struct {
	structure_base.Param
	Warehouse    string `json:"warehouse"`     // 仓库
	ProductName  string `json:"product_name"`  // 面料编号
	ProductCode  string `json:"product_code"`  // 面料名称
	ColorCode    string `json:"color_code"`    // 颜色编号
	ColorName    string `json:"color_name"`    // 颜色名称
	DyelotNumber string `json:"dyelot_number"` // 缸号
}

type AIGetStockData struct {
	Condition string `json:"condition"`
	List      []AIGetStockDataItem
}

func (g AIGetStockData) Adjust() {

}

type AIGetStockDataItem struct {
	structure_base.ResponseData
	Product         string  `json:"product"`          // 面料
	Color           string  `json:"color"`            // 颜色
	DyelotNumber    string  `json:"dyelot_number"`    // 缸号
	StockRoll       float64 `json:"stock_roll"`       // 库存匹数
	StockWeight     float64 `json:"stock_weight"`     // 库存数量
	AvailableRoll   float64 `json:"available_roll"`   // 可用匹数
	AvailableWeight float64 `json:"available_weight"` // 可用数量
	Length          float64 `json:"length"`           // 长度
	Warehouse       string  `json:"warehouse"`        // 仓库
}
