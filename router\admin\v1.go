package admin

import (
	common "hcscm/common/system_consts"
	"hcscm/router/ai"
	"hcscm/router/basic_data"
	"hcscm/router/biz_unit"
	"hcscm/router/color_card"
	"hcscm/router/file"
	gfm "hcscm/router/grey_fabric_manage"
	"hcscm/router/mix_process"
	"hcscm/router/ocr"
	payRecordRouter "hcscm/router/pay_record"
	"hcscm/router/produce"
	"hcscm/router/product"
	"hcscm/router/purchase"
	"hcscm/router/qywx"
	"hcscm/router/rsa_keys"
	"hcscm/router/sale"
	"hcscm/router/sale_price"
	"hcscm/router/should_collect_order"
	systemRouter "hcscm/router/system"
	"hcscm/router/tenant_management"
	"hcscm/server/pay_record"
	qywxServer "hcscm/server/qywx"
	"hcscm/server/system"
)

func Admin(routerGroup *system.RouterGroup) {
	routerGroup.POST("支付宝支付回调", "aliPayNotify", pay_record.AliPayNotify)
	admin := routerGroup.GroupByPlatform(common.PlatformAdmin, "admin", system.DistinctRequest(), system.JustAllowAdmin)
	// 后台
	V1Admin(admin)
}

func V1Admin(admin *system.RouterGroup) {
	v1Admin := admin.Group("v1")
	// socket
	{
		adminAuthWs := v1Admin.Group("", system.AuthAdminWs)
		adminAuthWs.GET("WebSocket连接", "ws", system.AdminServerWs)
	}
	adminAuth := v1Admin.Group("", system.AuthAdmin)
	// 登录登出
	{
		v1Admin.POST("登入", "login", system.AdminLogin)
		v1Admin.GET("初始化路由和超管权限", "syncRouterToDB", system.SyncRouterToDB)
		// 手机验证码登录
		v1Admin.POST("发送验证码注册", "sendVerificationCodeEmail", system.SendVerificationCodeEmail)
		v1Admin.POST("手机验证码注册", "phoneVerificationCodeLogin", system.PhoneVerificationCodeLogin)
		v1Admin.POST("扫码登入", "scanCodeLogin", system.AdminScanCodeLogin)
		v1Admin.POST("登出", "logout", system.Logout)

		adminAuth.GET("获取用户信息", "information", system.GetLoginInformation)
		adminAuth.GET("获取菜单", "getMenus", system.GetMenus)
		// 查询登录日志
		adminAuth.GET("获取登录日志", "loginLog", system.GetLoginLogList)
		adminAuth.GET("获取登录地", "loginSite", system.GetLoginSite)
		adminAuth.GET("切换h5token", "switchH5Token", system.SwitchH5Token)
	}
	{
		systemRouter.InitDepartment(adminAuth)           // 部门
		systemRouter.InitDictionary(adminAuth)           // 字典
		systemRouter.InitSaleSystem(adminAuth)           // 营销部门
		systemRouter.InitUser(adminAuth)                 // 用户
		systemRouter.InitEnum(adminAuth)                 // 枚举
		systemRouter.InitCDN(adminAuth)                  // CDN
		systemRouter.InitEmployee(adminAuth)             // 员工
		systemRouter.InitMenu(adminAuth)                 // 菜单
		systemRouter.InitResourceTree(adminAuth)         // 资源树
		systemRouter.InitRole(adminAuth)                 // 角色
		systemRouter.InitPrintTemplate(adminAuth)        // 打印模板
		systemRouter.InitListHabits(adminAuth)           // 列表习惯
		systemRouter.InitBusinessUnit(adminAuth)         // 来往单位
		systemRouter.InitBasicDataRawMaterial(adminAuth) // 基础数据-原料
		systemRouter.InitRawMaterialOrder(adminAuth)     // 原料单
		systemRouter.InitStockRawMaterial(adminAuth)     // 原料库存
		systemRouter.InitDyeingAndFinishing(adminAuth)   // 染整
		systemRouter.InitDNFEnum(adminAuth)              // 染整枚举
		systemRouter.InitDistrictArea(v1Admin)           // 染整枚举
		systemRouter.InitGlobalConfig(adminAuth)         // 全局配置
		rsa_keys.InitRsaKeys(adminAuth)                  // rsa秘钥
		systemRouter.InitCostPriceRecord(adminAuth)      // 成本价记录
	}
	{
		basic_data.InitPhysicalWarehouse(adminAuth)    // 仓库
		basic_data.InitPhysicalWarehouseBin(adminAuth) // 仓位
		basic_data.InitBasicDataType(adminAuth)        // 通用数据类型
		basic_data.InitInfoBasicData(adminAuth)        // 通用数据资料
		basic_data.InitBasicDataTypeEnum(adminAuth)    // 基础数据模块枚举
		basic_data.InitGreyFabricInfo(adminAuth)       // 坯布资料
		basic_data.InitFabricDyeProcessInfo(adminAuth) // 布料染整工艺资料
	}

	// 采购
	{
		purchase.InitPurchaseGreyFabric(adminAuth) // 坯布采购列表
		purchase.InitPurchaseEnum(adminAuth)       // 采购里的枚举
	}

	// 坯布管理
	{
		gfm.InitGFMEnum(adminAuth)      // 坯布管理枚举
		gfm.InitGFMOrder(adminAuth)     // 列表路由
		gfm.InitGfmWarehouse(adminAuth) // 库存
	}

	// 生产管理
	{
		produce.InitProduce(adminAuth)
	}

	// 成品管理
	{
		// 成品资料
		product.InitFinishProduct(adminAuth)
		// 成品采购单
		systemRouter.InitFinishProductOrder(adminAuth)
		purchase.InitPurchaseProductReturnOrder(adminAuth) // 成品采购退货列表
		// 成品库存
		product.InitStockProduct(adminAuth)
		product.InitProductCheckOrder(adminAuth)
		product.InitProductAdjustOrder(adminAuth)
		// 成品进出库 !!!
		product.InitFpmInOrder(adminAuth)
		product.InitFpmEnum(adminAuth)
		// 质检
		product.InitQualityCheck(adminAuth)
		// 质检报告单
		product.InitFpmQualityCheckoutReport(adminAuth)
		// 成品报表
		product.InitFpmReportForms(adminAuth)
	}
	// 成品销售管理
	{
		sale.InitSale(adminAuth)
		sale.InitPmcGreyPlanOrder(adminAuth)
	}
	// 成品销售价格
	{
		sale_price.InitSaleLevel(adminAuth)
		sale_price.InitSalePrice(adminAuth)
	}
	// 应收管理
	{
		should_collect_order.InitShouldCollectOrder(adminAuth)
		should_collect_order.InitShouldCollectOrderReportForms(adminAuth)
		// 往来单位地址
		systemRouter.InitBizUnitFactoryLogistics(adminAuth)
	}
	// 应付管理
	{
		systemRouter.InitPayable(adminAuth)
		systemRouter.InitPayableReportForms(adminAuth)
	}
	// 租户管理
	{
		tenant_management.InitTenantManagement(adminAuth)
	}
	// 支付记录
	{
		payRecordRouter.InitPayRecord(adminAuth)
	}
	// feie 打印
	{
		systemRouter.InitPrint(adminAuth)
	}
	// ocr识别
	{
		ocr.InitOCR(adminAuth)
	}
	// 临时接口
	{
		temporary := adminAuth.Group("temporary")
		should_collect_order.InitTemporaryRouter(temporary)
		tenant_management.InitTemporaryRouter(temporary)
		product.InitTemporaryRouter(temporary)
		biz_unit.InitTemporaryRouter(temporary)
	}
	// 导入
	{
		file.InitFile(adminAuth)
	}
	// 企业微信
	{
		qywx.InitQYWX(adminAuth)
		v1Admin.POST("添加代开发应用", "/qywx/tobe_developed_app", qywxServer.CreateTobeDevelopedApp)
	}
	// 混合处理
	{
		mix_process.InitSettlePointRecord(adminAuth)
	}
	// AI
	{
		ai.InitAi(adminAuth)
	}
	// 单号前缀
	{
		systemRouter.InitOrderPrefix(adminAuth)
	}
	// 电子色卡
	{
		//color_card.InitColorCard(adminAuth)
		color_card.InitCarouseBanner(adminAuth)
		color_card.InitMerchantInfo(adminAuth)
	}
}
