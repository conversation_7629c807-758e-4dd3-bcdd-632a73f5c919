package payable

import (
	"context"
	"fmt"
	common "hcscm/common/payable"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/pb/basic_data/grey_fabric_info"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/basic_data/raw_material"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/payable"
	mysql "hcscm/model/mysql/payable/dao"
	model2 "hcscm/model/mysql/payable2"
	mysql2 "hcscm/model/mysql/payable2/dao"
	structure "hcscm/structure/payable2"
	should_collect_structure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strconv"
)

type ReportFormsRepo struct {
	tx *mysql_base.Tx
}

func NewReportFormsRepo(tx *mysql_base.Tx) *ReportFormsRepo {
	return &ReportFormsRepo{tx: tx}
}

func (r *ReportFormsRepo) GetSupplierOweMoneyList(ctx context.Context, req *structure.GetSupplierOweMoneyListQuery) (list structure.GetSupplierOweMoneyDataList, total int, err error) {
	var (
		bizUnitSvc        = biz_unit.NewClientBizUnitService()
		items             = make(structure.GetSupplierOweMoneyDataList, 0)
		bizUnits          biz_unit.ResList
		supplierOweMoneys model.SupplierOweMoneyList
	)

	if req.BizUnitTypeId != 0 {
		req.BizUnitIds, err = bizUnitSvc.GetBizUnitIdsByBizUnitTypeId(ctx, nil, 1, req.BizUnitTypeId)
		if err != nil {
			return
		}
		if len(req.BizUnitIds) == 0 {
			list = items
			return
		}
	}

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	supplierOweMoneys, total, err = model.FindSupplierOweMoneyList(r.tx, req, false)
	if err != nil {
		return
	}

	// 查询客户
	bizUnitIds := mysql_base.GetUInt64ListV2("biz_unit_id", supplierOweMoneys)
	bizUnits, total, err = bizUnitSvc.GetCustomerByQuery(ctx, biz_unit.Req{Ids: bizUnitIds})
	if err != nil {
		return
	}

	for _, customerOweMoney := range supplierOweMoneys {
		bizUnit := bizUnits.Pick(customerOweMoney.SupplierId)
		item := structure.GetSupplierOweMoneyData{}
		item.SaleSystemId = bizUnit.SaleSystemID
		item.SaleSystemName = bizUnit.SaleSystemName
		item.SupplierId = bizUnit.Id
		item.SupplierCode = bizUnit.CustomCode
		item.SupplierName = bizUnit.Name
		item.BizUnitTypeName = bizUnit.UnitTypeName
		item.OrderFollowerID = bizUnit.OrderFollowerID
		item.OrderFollowerName = bizUnit.OrderFollowerName
		item.Period = tools.Cent(customerOweMoney.Period)
		item.ShouldPayPrice = tools.Cent(customerOweMoney.ShouldPayPrice)
		item.PayPrice = tools.Cent(customerOweMoney.PayPrice)
		item.DiscountMoney = tools.Cent(customerOweMoney.DiscountMoney)
		item.ChargebackMoney = tools.Cent(customerOweMoney.ChargebackMoney)
		item.OffsetPrice = tools.Cent(customerOweMoney.OffsetPrice)
		item.UnpayPrice = tools.Cent(customerOweMoney.UnpayPrice)
		item.WriteOffPrice = tools.Cent(customerOweMoney.WriteOffPrice)
		item.BalancePrice = tools.Cent(customerOweMoney.BalancePrice)
		item.LastAdvancePrice = tools.Cent(customerOweMoney.LastAdvancePrice)
		item.AdvancePrice = tools.Cent(customerOweMoney.AdvancePrice)
		item.AdvanceUsedPrice = tools.Cent(customerOweMoney.AdvanceUsedPrice)
		item.BalanceAdvancePrice = tools.Cent(customerOweMoney.BalanceAdvancePrice)
		item.EndPeriod = tools.Cent(customerOweMoney.BalancePrice - customerOweMoney.BalanceAdvancePrice)
		item.SettleType = common_system.SettleType(bizUnit.SettleType)
		item.SettleTypeName = bizUnit.SettleTypeName
		item.CustomCycle = bizUnit.SettleCycle
		item.CreditLimit = tools.Cent(bizUnit.CreditLimit)
		// 本期结余为0则为已收齐，反之为未收齐
		if item.BalancePrice == 0 {
			item.PayStatus = common.PayStatusYes
			item.PayStatusName = common.PayStatusYes.String()
		} else {
			item.PayStatus = common.PayStatusNo
			item.PayStatusName = common.PayStatusNo.String()
		}
		items = append(items, item)
	}
	list = items
	return
}

func (r *ReportFormsRepo) GetSupplierOweMoneySummary(ctx context.Context, req *structure.GetSupplierOweMoneyListQuery) (summary structure.GetSupplierOweMoneyData, err error) {
	var (
		supplierOweMoneys model.SupplierOweMoneyList
		supplierOweMoney  model.SupplierOweMoney
		count             int
		bizNameMap        map[uint64]string
	)

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	supplierOweMoneys, count, err = model.FindSupplierOweMoneyList(r.tx, req, true)
	if err != nil {
		return
	}

	bizPB := biz_unit.NewClientBizUnitService()
	bizNameMap, _ = bizPB.GetBizUnitNameByIds(ctx, []uint64{req.SupplierId})

	if count != 0 {
		supplierOweMoney = supplierOweMoneys.List()[0]
		summary.Period = tools.Cent(supplierOweMoney.Period)
		summary.ShouldPayPrice = tools.Cent(supplierOweMoney.ShouldPayPrice)
		summary.PayPrice = tools.Cent(supplierOweMoney.PayPrice)
		summary.DiscountMoney = tools.Cent(supplierOweMoney.DiscountMoney)
		summary.ChargebackMoney = tools.Cent(supplierOweMoney.ChargebackMoney)
		summary.OffsetPrice = tools.Cent(supplierOweMoney.OffsetPrice)
		summary.UnpayPrice = tools.Cent(supplierOweMoney.UnpayPrice)
		summary.WriteOffPrice = tools.Cent(supplierOweMoney.WriteOffPrice)
		summary.BalancePrice = tools.Cent(supplierOweMoney.BalancePrice)
		summary.LastAdvancePrice = tools.Cent(supplierOweMoney.LastAdvancePrice)
		summary.AdvancePrice = tools.Cent(supplierOweMoney.AdvancePrice)
		summary.AdvanceUsedPrice = tools.Cent(supplierOweMoney.AdvanceUsedPrice)
		summary.BalanceAdvancePrice = tools.Cent(supplierOweMoney.BalanceAdvancePrice)
		summary.EndPeriod = tools.Cent(supplierOweMoney.BalancePrice - supplierOweMoney.BalanceAdvancePrice)
		summary.SupplierName = bizNameMap[req.SupplierId]
		summary.TenantManagementName = metadata.GetLoginInfo(ctx).GetTenantManagementName()
		summary.StartTime = req.StartTime
		summary.EndTime = req.EndTime
	}
	return
}

func (r *ReportFormsRepo) GetSupplierReconciliationList(ctx context.Context, req *structure.GetSupplierReconciliationListQuery) (
	list structure.GetSupplierReconciliationDataList, summary structure.GetSupplierReconciliationData, total int, err error) {
	var (
		payableRepo             = NewPayableRepo(r.tx)
		items                   = make(structure.GetSupplierReconciliationDataList, 0)
		lastSupplierOweMoney    model.SupplierOweMoney
		customerReconciliations model.SupplierReconciliationList
		orderItemsMap           = make(map[uint64]*model.PayableItem, 0)
		ordersMap               = make(map[uint64]*model.Payable, 0)
		balance                 tools.Cent
		bizNameMap              map[uint64]string
		actuallyPayOrderItems   = model2.ActuallyPayOrderItemList{}
	)

	if !req.StartTime.IsYMDZero() {
		var (
			lastSupplierOweMoneys model.SupplierOweMoneyList
			count                 int
		)
		// 查询上期结余信息
		lastSupplierOweMoneys, count, err = model.FindSupplierOweMoneyList(r.tx, &structure.GetSupplierOweMoneyListQuery{SupplierId: req.SupplierId, EndTime: req.StartTime}, false)
		if err != nil {
			return
		}
		if count != 0 {
			lastSupplierOweMoney = lastSupplierOweMoneys.List()[0]
		}
		balance += tools.Cent(lastSupplierOweMoney.BalancePrice - lastSupplierOweMoney.BalanceAdvancePrice)
	}

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	customerReconciliations, total, err = model.FindSupplierReconciliationList(r.tx, req)
	if err != nil {
		return
	}

	var (
		payableIDs          = set.NewUint64Set()
		payableItemIDs      = set.NewUint64Set()
		actuallyPayOrderIDs = set.NewUint64Set()
	)
	for _, customerReconciliation := range customerReconciliations {
		if customerReconciliation.BaseType == 1 {
			payableIDs.Add(customerReconciliation.OrderId)
			payableItemIDs.Add(customerReconciliation.OrderItemId)
		}
		if customerReconciliation.BaseType == 2 {
			actuallyPayOrderIDs.Add(customerReconciliation.OrderId)
		}
	}

	orders, err := payableRepo.QueryPayableByIds(ctx, payableIDs.List())
	if err != nil {
		return
	}

	orderItems, err := payableRepo.QueryPayableItemByIds(ctx, payableItemIDs.List())
	if err != nil {
		return
	}

	// 获取实付信息
	actuallyPayOrderItems, err = mysql2.FindActuallyPayOrderItemByParentIDs(r.tx, actuallyPayOrderIDs.List())
	if err != nil {
		return
	}

	for _, order := range orders {
		ordersMap[order.Id] = order
	}
	var (
		materialIds   = set.NewUint64Set()
		colorIds      = set.NewUint64Set()
		unitIds       = set.NewUint64Set()
		dictionaryIds = set.NewUint64Set()
	)
	for _, item := range orderItems {
		orderItemsMap[item.Id] = item
		materialIds.Add(item.MaterialId)
		colorIds.Add(item.ColorId)
		unitIds.Add(item.MeasurementUnitId)
		unitIds.Add(item.AuxiliaryUnitId)
		dictionaryIds.Add(item.WidthUnitId)
		dictionaryIds.Add(item.GramWeightUnitId)
	}

	// 计量单位
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}
	// 成品
	productSvc := product.NewProductClient()
	productItem, err := productSvc.GetProductMapByIds(ctx, materialIds.List())
	if err != nil {
		return
	}
	// 成品颜色
	productColorSvc := product.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorMapByIds(ctx, colorIds.List())
	if err != nil {
		return
	}
	// 坯布
	greyFabricSvc := grey_fabric_info.NewGreyFabricInfoClient()
	greyFabricItem, err := greyFabricSvc.GetGreyFabricInfoMapList(ctx, materialIds.List())
	if err != nil {
		return
	}
	// 坯布颜色
	greyFabricColorSvc := base_info_pb.NewInfoProductGrayFabricColorClient()
	greyFabricColor, err := greyFabricColorSvc.GetGrayFabricColorMapByIds(ctx, colorIds.List())
	if err != nil {
		return
	}
	// 原料
	rawMaterialSvc := raw_material.NewClientRawMatlInfoService()
	rawMaterials, err := rawMaterialSvc.GetRawMatlNameByIds(ctx, materialIds.List())
	if err != nil {
		return
	}
	// 原料颜色
	rawMaterialColorSvc := raw_material.NewRawMaterialColorClient()
	rawColorListMap, err := rawMaterialColorSvc.GetColorInfoByIds(ctx, colorIds.List())
	if err != nil {
		return
	}

	dicSvc := dictionary.NewDictionaryClient()
	dicNameMap, err := dicSvc.GetDictionaryNameByIds(ctx, dictionaryIds.List())
	if err != nil {
		return
	}

	bizPB := biz_unit.NewClientBizUnitService()
	bizNameMap, _ = bizPB.GetBizUnitNameByIds(ctx, []uint64{req.SupplierId})

	// 如果页数为1才显示上期结余
	if req.Page <= 1 {
		lastItem := structure.GetSupplierReconciliationData{}
		lastItem.OrderTypeName = "上期结余"
		lastItem.Balance += balance
		items = append(items, lastItem)
		var (
			thisSupplierOweMoneys model.SupplierOweMoneyList
			thisSupplierOweMoney  model.SupplierOweMoney
			count                 int
		)
		// 查询上期结余信息
		thisSupplierOweMoneys, count, err = model.FindSupplierOweMoneyList(r.tx, &structure.GetSupplierOweMoneyListQuery{SupplierId: req.SupplierId, StartTime: req.StartTime, EndTime: req.EndTime}, true)
		if err != nil {
			return
		}
		if count != 0 {
			thisSupplierOweMoney = thisSupplierOweMoneys.List()[0]
		}
		summary.LastBalancePrice += balance
		summary.ShouldPayMoney += tools.Cent(thisSupplierOweMoney.TotalPrice)
		summary.PayedMoney += tools.Cent(thisSupplierOweMoney.PayPrice + thisSupplierOweMoney.AdvancePrice - thisSupplierOweMoney.AdvanceUsedPrice)
		summary.Balance += tools.Cent(thisSupplierOweMoney.BalancePrice)
		summary.EndPeriod += tools.Cent(thisSupplierOweMoney.BalancePrice - thisSupplierOweMoney.BalanceAdvancePrice)
		summary.SupplierName = bizNameMap[req.SupplierId]
		summary.TenantManagementName = metadata.GetLoginInfo(ctx).GetTenantManagementName()
		summary.StartTime = req.StartTime
		summary.EndTime = req.EndTime
	} else {
		// todo:上一页汇总有问题，需要修改
		var (
			customerReconciliationBalance model.FinalSupplierReconciliationBalanceList
		)
		// 如果页数不为1则需要算出目前总数，然后根据总数获取前面页数的余额总数继续累加下去
		count := (req.Page - 1) * req.Size
		req.Page = 1
		req.Size = count

		customerReconciliationBalance, _, err = model.GetSupplierReconciliationBalance(r.tx, req)
		if err != nil {
			return
		}

		balance += tools.Cent(customerReconciliationBalance[0].Balance)
	}

	var orderId uint64 = 0
	for _, customerReconciliation := range customerReconciliations {
		item := structure.GetSupplierReconciliationData{}
		item.OrderId = customerReconciliation.OrderId
		item.OrderNo = customerReconciliation.OrderNo
		item.OrderTime = customerReconciliation.OrderTime.Format(vars.DateOnly)
		item.Remark = customerReconciliation.Remark
		if customerReconciliation.BaseType == 1 {
			if customerReconciliation.OrderType == common.OrderTypeDNF {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "染整费应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					if productItem[orderItem.MaterialId] != nil {
						item.Code = productItem[orderItem.MaterialId].FinishProductCode
						item.Name = productItem[orderItem.MaterialId].FinishProductName
					}
					if productColorItem[orderItem.ColorId] != nil {
						item.ProductColorCode = productColorItem[orderItem.ColorId].ProductColorCode
						item.ProductColorName = productColorItem[orderItem.ColorId].ProductColorName
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
				}
				item.FinishingUnitPrice = tools.Cent(customerReconciliation.FinishingUnitPrice)
				item.PaperTubeUnitPrice = tools.Cent(customerReconciliation.PaperTubeUnitPrice)
				item.PlasticBagUnitPrice = tools.Cent(customerReconciliation.PlasticBagUnitPrice)
			}
			if customerReconciliation.OrderType == common.OrderTypeProcessing {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "加工费应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					if greyFabricItem[orderItem.MaterialId] != nil {
						item.Code = greyFabricItem[orderItem.MaterialId].Code
						item.Name = greyFabricItem[orderItem.MaterialId].Name
					}
					if greyFabricColor[orderItem.ColorId] != nil {
						item.ProductColorCode = greyFabricColor[orderItem.ColorId].Code
						item.ProductColorName = greyFabricColor[orderItem.ColorId].Name
					}
					item.YarnBatch = orderItem.YarnBatch
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.BuildFPResp(orderItem.Width, orderItem.GramWeight, dicNameMap[orderItem.WidthUnitId][1], dicNameMap[orderItem.GramWeightUnitId][1],
						orderItem.WidthUnitId, orderItem.GramWeightUnitId)
				}
			}
			// 原料采购
			if customerReconciliation.OrderType == common.OrderTypeRawMatlPur {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "原料采购应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					if rawMaterial, ok := rawMaterials[orderItem.MaterialId]; ok {
						item.Code = rawMaterial.Code
						item.Name = rawMaterial.Name
					}
					if rawColorListMap[orderItem.ColorId] != nil {
						item.ProductColorCode = rawColorListMap[orderItem.ColorId].Code
						item.ProductColorName = rawColorListMap[orderItem.ColorId].Name
					}
					item.Brand = orderItem.Brand
					item.BatchNum = orderItem.BatchNum
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
				}
			}
			// 坯布采购
			if customerReconciliation.OrderType == common.OrderTypeGreyFabricPur {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "坯布采购应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					if greyFabricItem[orderItem.MaterialId] != nil {
						item.Code = greyFabricItem[orderItem.MaterialId].Code
						item.Name = greyFabricItem[orderItem.MaterialId].Name
					}
					if greyFabricColor[orderItem.ColorId] != nil {
						item.ProductColorCode = greyFabricColor[orderItem.ColorId].Code
						item.ProductColorName = greyFabricColor[orderItem.ColorId].Name
					}
					item.YarnBatch = orderItem.YarnBatch
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.BuildFPResp(orderItem.Width, orderItem.GramWeight, dicNameMap[orderItem.WidthUnitId][1], dicNameMap[orderItem.GramWeightUnitId][1],
						orderItem.WidthUnitId, orderItem.GramWeightUnitId)
				}
			}
			// 成品采购
			if customerReconciliation.OrderType == common.OrderTypeProductPur {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "成品采购应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					if productItem[orderItem.MaterialId] != nil {
						item.Code = productItem[orderItem.MaterialId].FinishProductCode
						item.Name = productItem[orderItem.MaterialId].FinishProductName
					}
					if productColorItem[orderItem.ColorId] != nil {
						item.ProductColorCode = productColorItem[orderItem.ColorId].ProductColorCode
						item.ProductColorName = productColorItem[orderItem.ColorId].ProductColorName
					}
					item.YarnBatch = orderItem.YarnBatch
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.BuildFPResp(orderItem.Width, orderItem.GramWeight, dicNameMap[orderItem.WidthUnitId][1], dicNameMap[orderItem.GramWeightUnitId][1],
						orderItem.WidthUnitId, orderItem.GramWeightUnitId)
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeOther {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "其他应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					item.Code = orderItem.ProjectNo
					item.Name = orderItem.ProjectName
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeRawDNF {
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "原料染整费应付账"
				if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
					if rawMaterial, ok := rawMaterials[orderItem.MaterialId]; ok {
						item.Code = rawMaterial.Code
						item.Name = rawMaterial.Name
					}
					if rawColorListMap[orderItem.ColorId] != nil {
						item.ProductColorCode = rawColorListMap[orderItem.ColorId].Code
						item.ProductColorName = rawColorListMap[orderItem.ColorId].Name
					}
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeRawProcessing {
				// todo
				item.OrderType = customerReconciliation.OrderType
				item.OrderTypeName = "原料加工费应付账"
				// if orderItem, ok := orderItemsMap[customerReconciliation.OrderItemId]; ok {
				//	item.Code = productItem[orderItem.MaterialId].FinishProductCode
				//	item.Name = productItem[orderItem.MaterialId].FinishProductName
				//	item.ProductColorCode = productColorItem[orderItem.ColorId].ProductColorCode
				//	item.ProductColorName = productColorItem[orderItem.ColorId].ProductColorName
				//	item.MeasurementUnitId = orderItem.MeasurementUnitId
				//	item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
				// item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
				// item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
				// }
			}
			item.Roll = tools.Hundred(customerReconciliation.Roll)
			item.Length = tools.Hundred(customerReconciliation.Length)
			item.Weight = tools.TenThousand(customerReconciliation.Weight)
			item.SalePrice = tools.TenThousand(customerReconciliation.SalePrice)
			item.OtherPrice = tools.Cent(customerReconciliation.OtherPrice)
			item.TotalPrice = tools.Cent(customerReconciliation.TotalPrice) // 总金额
			item.OrderTotalPrice = tools.Cent(customerReconciliation.SettlePrice)
			// item.DiscountPrice = tools.Cent(customerReconciliation.DiscountPrice)
			// item.ChargebackMoney = tools.Cent(customerReconciliation.ChargebackMoney)
			// item.OffsetPrice = tools.Cent(customerReconciliation.OffsetPrice)
			if orderId != item.OrderId {
				// 结余金额=上一行结余金额 + 该行汇总金额 - 该行的折扣金额 - 该行的扣款金额 - 该行的实付/预付金额
				balance += tools.Cent(item.SettlePrice) + tools.Cent(item.OrderTotalPrice) - tools.Cent(item.DiscountPrice) - tools.Cent(item.ChargebackMoney) - tools.Cent(item.OffsetPrice)
				orderId = item.OrderId
			} else {
				balance += item.SettlePrice
			}
		} else if customerReconciliation.BaseType == 2 {
			item.OrderType = common.OrderTypeActually
			item.SettlePrice = tools.Cent(customerReconciliation.SettlePrice)
			item.OrderTypeName = "实付款单"
			item.OrderTime = customerReconciliation.OrderTime.Format("2006-01-02")
			// 核销汇总
			var (
				discountPrice   int
				chargebackMoney int
				offsetPrice     int
			)
			_actuallyPayOrderItems := actuallyPayOrderItems.PickByParentID(customerReconciliation.OrderId)
			for _, actuallyPayOrderItem := range _actuallyPayOrderItems {
				discountPrice += actuallyPayOrderItem.DiscountPrice    // 折扣金额
				chargebackMoney += actuallyPayOrderItem.DeductionPrice // 扣款金额
				offsetPrice += actuallyPayOrderItem.OffsetPrice        // 优惠金额
			}
			item.DiscountPrice = tools.Cent(discountPrice)
			item.ChargebackMoney = tools.Cent(chargebackMoney)
			item.OffsetPrice = tools.Cent(offsetPrice)
			balance -= item.SettlePrice + item.DiscountPrice + item.ChargebackMoney + item.OffsetPrice
		} else {
			item.OrderType = common.OrderTypeAdvance
			item.SettlePrice = tools.Cent(customerReconciliation.SettlePrice)
			item.OrderTypeName = "预付款单"
			item.OrderTime = customerReconciliation.OrderTime.Format("2006-01-02")
			balance -= item.SettlePrice
		}
		item.Balance = balance
		items = append(items, item)
	}
	list = items
	return
}

func (r *ReportFormsRepo) MPGetSupplierReconciliationList(ctx context.Context, req *structure.GetSupplierReconciliationListQuery) (
	list structure.MPGetSupplierReconciliationDataList,
	summary *structure.MPGetSupplierReconciliationData,
	total int,
	err error,
) {
	var (
		payableRepo             = NewPayableRepo(r.tx)
		dataList                = make(structure.MPGetSupplierReconciliationDataList, 0)
		lastSupplierOweMoney    model.SupplierOweMoney
		thisSupplierOweMoney    model.SupplierOweMoney
		customerReconciliations model.SupplierReconciliationList
		orderItemsMap           = make(map[uint64][]*model.PayableItem, 0)
		ordersMap               = make(map[uint64]*model.Payable, 0)
		actuallyPayOrderItems   = model2.ActuallyPayOrderItemList{}
		balance                 int
		bizNameMap              map[uint64]string
	)
	summary = &structure.MPGetSupplierReconciliationData{}

	if !req.StartTime.IsYMDZero() {
		var (
			lastSupplierOweMoneys model.SupplierOweMoneyList
			count                 int
		)
		// 查询上期结余信息
		lastSupplierOweMoneys, count, err = model.FindSupplierOweMoneyList(r.tx, &structure.GetSupplierOweMoneyListQuery{SupplierId: req.SupplierId, EndTime: req.StartTime}, true)
		if err != nil {
			return
		}
		if count != 0 {
			lastSupplierOweMoney = lastSupplierOweMoneys.List()[0]
		}
		balance += lastSupplierOweMoney.BalancePrice - lastSupplierOweMoney.BalanceAdvancePrice
	}

	// 查询欠款信息(客户更新时间倒序,数据隔离)
	customerReconciliations, total, err = model.MPFindSupplierReconciliationList(r.tx, req)
	if err != nil {
		return
	}

	var (
		payableIDsMap       = set.NewUint64Set()
		payableItemIDsMap   = set.NewUint64Set()
		actuallyPayOrderIDs = set.NewUint64Set()
	)
	for _, customerReconciliation := range customerReconciliations {
		if customerReconciliation.BaseType == 1 {
			payableIDsMap.Add(customerReconciliation.OrderId)
			payableItemIDsMap.Add(customerReconciliation.OrderItemId)
		}
		if customerReconciliation.BaseType == 2 {
			actuallyPayOrderIDs.Add(customerReconciliation.OrderId)
		}
	}
	payableIDs := payableIDsMap.List()

	orders, err := payableRepo.QueryPayableByIds(ctx, payableIDs)
	if err != nil {
		return
	}

	orderItems, err := payableRepo.QueryPayableItemsByOrderIds(ctx, payableIDs)
	if err != nil {
		return
	}
	for _, order := range orders {
		ordersMap[order.Id] = order
	}

	actuallyPayOrderItems, err = mysql2.FindActuallyPayOrderItemBySrcOrderIDs(r.tx, payableIDs)
	// actuallyPayOrderItems, err = mysql2.FindActuallyPayOrderItemByParentIDs(r.tx, actuallyPayOrderIDs.List())
	if err != nil {
		return
	}

	var (
		materialIds = set.NewUint64Set()
		colorIds    = set.NewUint64Set()
		unitIds     = set.NewUint64Set()
	)
	for _, item := range orderItems {
		orderItemsMap[item.OrderId] = append(orderItemsMap[item.OrderId], item)
		materialIds.Add(item.MaterialId)
		colorIds.Add(item.ColorId)
		unitIds.Add(item.MeasurementUnitId)
		unitIds.Add(item.AuxiliaryUnitId)
	}

	// 计量单位
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds.List())
	if err != nil {
		return
	}
	// 成品
	productSvc := product.NewProductClient()
	productItemMap, err := productSvc.GetProductMapByIds(ctx, materialIds.List())
	if err != nil {
		return
	}
	// 成品颜色
	productColorSvc := product.NewProductColorClient()
	productColorItemMap, err := productColorSvc.GetProductColorMapByIds(ctx, colorIds.List())
	if err != nil {
		return
	}
	// 坯布
	greyFabricSvc := grey_fabric_info.NewGreyFabricInfoClient()
	greyFabricItemMap, err := greyFabricSvc.GetGreyFabricInfoMapList(ctx, materialIds.List())
	if err != nil {
		return
	}
	// 坯布颜色
	greyFabricColorSvc := base_info_pb.NewInfoProductGrayFabricColorClient()
	greyFabricColorMap, err := greyFabricColorSvc.GetGrayFabricColorMapByIds(ctx, colorIds.List())
	if err != nil {
		return
	}
	// 原料
	rawMaterialSvc := raw_material.NewClientRawMatlInfoService()
	rawMaterialsMap, err := rawMaterialSvc.GetRawMatlNameByIds(ctx, materialIds.List())
	if err != nil {
		return
	}
	// 原料颜色
	rawMaterialColorSvc := raw_material.NewRawMaterialColorClient()
	rawColorListMap, err := rawMaterialColorSvc.GetColorInfoByIds(ctx, colorIds.List())
	if err != nil {
		return
	}

	bizPB := biz_unit.NewClientBizUnitService()
	bizNameMap, _ = bizPB.GetBizUnitNameByIds(ctx, []uint64{req.SupplierId})

	// 如果页数为1才显示上期结余
	if req.Page <= 1 {
		var (
			thisSupplierOweMoneys model.SupplierOweMoneyList
			count                 int
		)
		// 查询上期结余信息
		thisSupplierOweMoneys, count, err = model.FindSupplierOweMoneyList(r.tx, &structure.GetSupplierOweMoneyListQuery{SupplierId: req.SupplierId, StartTime: req.StartTime, EndTime: req.EndTime}, true)
		if err != nil {
			return
		}
		if count != 0 {
			thisSupplierOweMoney = thisSupplierOweMoneys.List()[0]
		}
		summary.LastBalancePrice += balance
		summary.ShouldPayMoney += thisSupplierOweMoney.TotalPrice
		summary.PayedMoney += thisSupplierOweMoney.PayPrice + thisSupplierOweMoney.AdvancePrice - thisSupplierOweMoney.AdvanceUsedPrice
		summary.Balance += thisSupplierOweMoney.BalancePrice
		summary.EndPeriod += thisSupplierOweMoney.BalancePrice - thisSupplierOweMoney.BalanceAdvancePrice
		summary.SupplierName = bizNameMap[req.SupplierId]
		summary.TenantManagementName = metadata.GetLoginInfo(ctx).GetTenantManagementName()
		summary.StartTime = req.StartTime
		summary.EndTime = req.EndTime
	} else {
		var (
			customerReconciliationBalance model.FinalSupplierReconciliationBalanceList
		)
		// 如果页数不为1则需要算出目前总数，然后根据总数获取前面页数的余额总数继续累加下去
		count := (req.Page - 1) * req.Size
		req.Page = 1
		req.Size = count

		customerReconciliationBalance, _, err = model.GetSupplierReconciliationBalance(r.tx, req)
		if err != nil {
			return
		}

		balance += customerReconciliationBalance[0].Balance
	}

	for _, customerReconciliation := range customerReconciliations {
		var (
			items = make([]structure.Item, 0)
		)
		o := structure.MPGetSupplierReconciliationData{}
		o.OrderId = customerReconciliation.OrderId
		o.OrderNo = customerReconciliation.OrderNo
		o.OrderTime = tools.MyTime(customerReconciliation.OrderTime)
		o.Remark = customerReconciliation.Remark
		if customerReconciliation.BaseType == 1 {
			if customerReconciliation.OrderType == common.OrderTypeDNF {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "染整费应付账"
				o.FinishingUnitPrice = customerReconciliation.FinishingUnitPrice
				o.PaperTubeUnitPrice = customerReconciliation.PaperTubeUnitPrice
				o.PlasticBagUnitPrice = customerReconciliation.PlasticBagUnitPrice
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if productItem, ok := productItemMap[orderItem.MaterialId]; ok {
						item.Code = productItem.FinishProductCode
						item.Name = productItem.FinishProductName
					}
					if productColorItem, ok := productColorItemMap[orderItem.ColorId]; ok {
						item.ProductColorCode = productColorItem.ProductColorCode
						item.ProductColorName = productColorItem.ProductColorName
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.Roll, "匹")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeProcessing {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "加工费应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if productItem, ok := productItemMap[orderItem.MaterialId]; ok {
						item.Code = productItem.FinishProductCode
						item.Name = productItem.FinishProductName
					}
					if productColorItem, ok := productColorItemMap[orderItem.ColorId]; ok {
						item.ProductColorCode = productColorItem.ProductColorCode
						item.ProductColorName = productColorItem.ProductColorName
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.Roll, "匹")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			// 原料采购
			if customerReconciliation.OrderType == common.OrderTypeRawMatlPur {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "原料采购应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if rawMaterial, ok := rawMaterialsMap[orderItem.MaterialId]; ok {
						item.Code = rawMaterial.Code
						item.Name = rawMaterial.Name
					}
					if rawColor, ok := rawColorListMap[orderItem.ColorId]; ok {
						item.ProductColorCode = rawColor.Code
						item.ProductColorName = rawColor.Name
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.PieceCount, "件")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			// 坯布采购
			if customerReconciliation.OrderType == common.OrderTypeGreyFabricPur {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "坯布采购应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if greyFabricItem, ok := greyFabricItemMap[orderItem.MaterialId]; ok {
						item.Code = greyFabricItem.Code
						item.Name = greyFabricItem.Name
					}
					if greyFabricColor, ok := greyFabricColorMap[orderItem.ColorId]; ok {
						item.ProductColorCode = greyFabricColor.Code
						item.ProductColorName = greyFabricColor.Name
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.Roll, "匹")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			// 成品采购
			if customerReconciliation.OrderType == common.OrderTypeProductPur {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "成品采购应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if productItem, ok := productItemMap[orderItem.MaterialId]; ok {
						item.Code = productItem.FinishProductCode
						item.Name = productItem.FinishProductName
					}
					if productColorItem, ok := productColorItemMap[orderItem.ColorId]; ok {
						item.ProductColorCode = productColorItem.ProductColorCode
						item.ProductColorName = productColorItem.ProductColorName
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.Roll, "匹")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeOther {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "其他应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					item.Code = orderItem.ProjectNo
					item.Name = orderItem.ProjectName
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.Roll, "匹")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeRawDNF {
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "原料染整费应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if rawMaterial, ok := rawMaterialsMap[orderItem.MaterialId]; ok {
						item.Code = rawMaterial.Code
						item.Name = rawMaterial.Name
					}
					if rawColor, ok := rawColorListMap[orderItem.ColorId]; ok {
						item.ProductColorCode = rawColor.Code
						item.ProductColorName = rawColor.Name
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.PieceCount, "件")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			if customerReconciliation.OrderType == common.OrderTypeRawProcessing {
				// todo:待完善
				o.OrderType = customerReconciliation.OrderType
				o.OrderTypeName = "原料加工费应付账"
				for _, orderItem := range orderItemsMap[customerReconciliation.OrderId] {
					item := structure.Item{}
					if rawMaterial, ok := rawMaterialsMap[orderItem.MaterialId]; ok {
						item.Code = rawMaterial.Code
						item.Name = rawMaterial.Name
					}
					if rawColor, ok := rawColorListMap[orderItem.ColorId]; ok {
						item.ProductColorCode = rawColor.Code
						item.ProductColorName = rawColor.Name
					}
					item.DyeColorCode = orderItem.DyeFactoryColorCode
					item.DyeColorName = orderItem.DyeFactoryColorCode
					item.DyelotNumber = orderItem.DyelotNumber
					item.MeasurementUnitId = orderItem.MeasurementUnitId
					item.MeasurementUnitName = measurementUnitName[orderItem.MeasurementUnitId]
					item.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
					item.AuxiliaryUnitName = measurementUnitName[orderItem.AuxiliaryUnitId]
					item.Roll = orderItem.Roll
					item.RollString = fmt.Sprintf("%v%v", orderItem.Roll/vars.PieceCount, "件")
					item.Weight = orderItem.Weight
					var (
						countSaleWeightMap = make(map[uint64]int)
					)
					if orderItem.AuxiliaryUnitId != 0 {
						if orderItem.AuxiliaryUnitId == orderItem.MeasurementUnitId {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Weight
						} else {
							countSaleWeightMap[orderItem.AuxiliaryUnitId] += orderItem.Length
						}
					} else {
						countSaleWeightMap[orderItem.MeasurementUnitId] += orderItem.Weight
					}
					item.WeightString = func() (str string) {
						for k, v := range countSaleWeightMap {
							fmtRound := tools.GetRound(v, 2)
							if str == "" {
								str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							} else {
								str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + measurementUnitName[k]
							}
						}
						return
					}()
					item.SalePrice = orderItem.UnitPrice
					item.OtherPrice = orderItem.OtherPrice
					item.TotalPrice = orderItem.Price
					items = append(items, item)
				}
			}
			o.ItemList = items
			o.OrderTotalPrice = customerReconciliation.SettlePrice
			// 核销汇总
			_actuallyPayOrderItems := actuallyPayOrderItems.PickBySrcOrderID(customerReconciliation.OrderId)
			for _, actuallyPayOrderItem := range _actuallyPayOrderItems {
				o.SettlePrice += actuallyPayOrderItem.PayPrice           // 实收金额
				o.DiscountPrice += actuallyPayOrderItem.DiscountPrice    // 折扣金额
				o.ChargebackMoney += actuallyPayOrderItem.DeductionPrice // 扣款金额
				o.OffsetPrice += actuallyPayOrderItem.OffsetPrice        // 优惠金额
				o.TotalPrice += actuallyPayOrderItem.WriteOffPrice       // 核销金额
			}
		} else if customerReconciliation.BaseType == 2 {
			o.SettlePrice = customerReconciliation.SettlePrice
			o.OrderType = common.OrderTypeActually
			o.OrderTypeName = "实付款单"
			// 核销汇总
			// _actuallyPayOrderItems := actuallyPayOrderItems.PickByParentID(customerReconciliation.OrderId)
			// for _, actuallyPayOrderItem := range _actuallyPayOrderItems {
			// 	o.DiscountPrice += actuallyPayOrderItem.DiscountPrice    // 折扣金额
			// 	o.ChargebackMoney += actuallyPayOrderItem.DeductionPrice // 扣款金额
			// 	o.OffsetPrice += actuallyPayOrderItem.OffsetPrice        // 优惠金额
			// }
		} else {
			o.SettlePrice = customerReconciliation.SettlePrice
			o.OrderType = common.OrderTypeAdvance
			o.OrderTypeName = "预付款单"
		}
		// 结余金额=上一行结余金额 + 该行汇总金额 - 该行的折扣金额 - 该行的扣款金额 - 该行的实付/预付金额
		balance += o.SettlePrice + o.OrderTotalPrice - o.DiscountPrice - o.ChargebackMoney - o.OffsetPrice
		// balance += o.SettlePrice + o.OrderTotalPrice - customerReconciliation.DiscountPrice - customerReconciliation.ChargebackMoney - customerReconciliation.OffsetPrice
		o.Balance = balance
		dataList = append(dataList, o)
	}
	list = dataList
	return
}

func (r *ReportFormsRepo) FindSaleSimpleReportGroupSupplierList(ctx context.Context, q *should_collect_structure.QuerySaleSimpleReportListParam) (
	data []*should_collect_structure.GetSaleSimpleReportGroupSupplierData, total int, err error) {

	var (
		dataV2 = make([]*should_collect_structure.GetSaleSimpleReportGroupSupplierData, 0)
	)
	list, total, err := mysql.FindSaleSimpleReportGroupSupplierList(r.tx, q)
	if err != nil {
		return
	}

	bizNameMap, _ := biz_unit.NewClientBizUnitService().
		GetBizUnitNameByIds(ctx, mysql_base.GetUInt64List(list, "biz_unit_id"))

	if err != nil {
		return
	}
	for _, report := range list {
		o := &should_collect_structure.GetSaleSimpleReportGroupSupplierData{}
		report.BuildResp(o)
		o.SupplierName = bizNameMap[o.SupplierID]
		dataV2 = append(dataV2, o)
	}
	data = dataV2
	return
}
