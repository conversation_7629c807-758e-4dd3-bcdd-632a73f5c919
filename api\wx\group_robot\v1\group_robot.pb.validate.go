// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: api/wx/group_robot/group_robot.proto

package v1

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateGroupRobotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateGroupRobotRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateGroupRobotRequestMultiError, or nil if none found.
func (m *CreateGroupRobotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateGroupRobotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TobeDevelopedAppInfoId

	// no validation rules for Name

	// no validation rules for Url

	if len(errors) > 0 {
		return CreateGroupRobotRequestMultiError(errors)
	}

	return nil
}

// CreateGroupRobotRequestMultiError is an error wrapping multiple validation
// errors returned by CreateGroupRobotRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateGroupRobotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateGroupRobotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateGroupRobotRequestMultiError) AllErrors() []error { return m }

// CreateGroupRobotRequestValidationError is the validation error returned by
// CreateGroupRobotRequest.Validate if the designated constraints aren't met.
type CreateGroupRobotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateGroupRobotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateGroupRobotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateGroupRobotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateGroupRobotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateGroupRobotRequestValidationError) ErrorName() string {
	return "CreateGroupRobotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateGroupRobotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateGroupRobotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateGroupRobotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateGroupRobotRequestValidationError{}

// Validate checks the field values on CreateGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateGroupRobotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateGroupRobotReplyMultiError, or nil if none found.
func (m *CreateGroupRobotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateGroupRobotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return CreateGroupRobotReplyMultiError(errors)
	}

	return nil
}

// CreateGroupRobotReplyMultiError is an error wrapping multiple validation
// errors returned by CreateGroupRobotReply.ValidateAll() if the designated
// constraints aren't met.
type CreateGroupRobotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateGroupRobotReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateGroupRobotReplyMultiError) AllErrors() []error { return m }

// CreateGroupRobotReplyValidationError is the validation error returned by
// CreateGroupRobotReply.Validate if the designated constraints aren't met.
type CreateGroupRobotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateGroupRobotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateGroupRobotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateGroupRobotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateGroupRobotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateGroupRobotReplyValidationError) ErrorName() string {
	return "CreateGroupRobotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e CreateGroupRobotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateGroupRobotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateGroupRobotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateGroupRobotReplyValidationError{}

// Validate checks the field values on UpdateGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateGroupRobotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateGroupRobotRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateGroupRobotRequestMultiError, or nil if none found.
func (m *UpdateGroupRobotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateGroupRobotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TobeDevelopedAppInfoId

	// no validation rules for Name

	// no validation rules for Url

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateGroupRobotRequestMultiError(errors)
	}

	return nil
}

// UpdateGroupRobotRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateGroupRobotRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateGroupRobotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateGroupRobotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateGroupRobotRequestMultiError) AllErrors() []error { return m }

// UpdateGroupRobotRequestValidationError is the validation error returned by
// UpdateGroupRobotRequest.Validate if the designated constraints aren't met.
type UpdateGroupRobotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateGroupRobotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateGroupRobotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateGroupRobotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateGroupRobotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateGroupRobotRequestValidationError) ErrorName() string {
	return "UpdateGroupRobotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateGroupRobotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateGroupRobotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateGroupRobotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateGroupRobotRequestValidationError{}

// Validate checks the field values on UpdateGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateGroupRobotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateGroupRobotReplyMultiError, or nil if none found.
func (m *UpdateGroupRobotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateGroupRobotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return UpdateGroupRobotReplyMultiError(errors)
	}

	return nil
}

// UpdateGroupRobotReplyMultiError is an error wrapping multiple validation
// errors returned by UpdateGroupRobotReply.ValidateAll() if the designated
// constraints aren't met.
type UpdateGroupRobotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateGroupRobotReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateGroupRobotReplyMultiError) AllErrors() []error { return m }

// UpdateGroupRobotReplyValidationError is the validation error returned by
// UpdateGroupRobotReply.Validate if the designated constraints aren't met.
type UpdateGroupRobotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateGroupRobotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateGroupRobotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateGroupRobotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateGroupRobotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateGroupRobotReplyValidationError) ErrorName() string {
	return "UpdateGroupRobotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateGroupRobotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateGroupRobotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateGroupRobotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateGroupRobotReplyValidationError{}

// Validate checks the field values on DeleteGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteGroupRobotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteGroupRobotRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteGroupRobotRequestMultiError, or nil if none found.
func (m *DeleteGroupRobotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteGroupRobotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return DeleteGroupRobotRequestMultiError(errors)
	}

	return nil
}

// DeleteGroupRobotRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteGroupRobotRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteGroupRobotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteGroupRobotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteGroupRobotRequestMultiError) AllErrors() []error { return m }

// DeleteGroupRobotRequestValidationError is the validation error returned by
// DeleteGroupRobotRequest.Validate if the designated constraints aren't met.
type DeleteGroupRobotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteGroupRobotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteGroupRobotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteGroupRobotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteGroupRobotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteGroupRobotRequestValidationError) ErrorName() string {
	return "DeleteGroupRobotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteGroupRobotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteGroupRobotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteGroupRobotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteGroupRobotRequestValidationError{}

// Validate checks the field values on DeleteGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteGroupRobotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteGroupRobotReplyMultiError, or nil if none found.
func (m *DeleteGroupRobotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteGroupRobotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return DeleteGroupRobotReplyMultiError(errors)
	}

	return nil
}

// DeleteGroupRobotReplyMultiError is an error wrapping multiple validation
// errors returned by DeleteGroupRobotReply.ValidateAll() if the designated
// constraints aren't met.
type DeleteGroupRobotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteGroupRobotReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteGroupRobotReplyMultiError) AllErrors() []error { return m }

// DeleteGroupRobotReplyValidationError is the validation error returned by
// DeleteGroupRobotReply.Validate if the designated constraints aren't met.
type DeleteGroupRobotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteGroupRobotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteGroupRobotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteGroupRobotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteGroupRobotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteGroupRobotReplyValidationError) ErrorName() string {
	return "DeleteGroupRobotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteGroupRobotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteGroupRobotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteGroupRobotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteGroupRobotReplyValidationError{}

// Validate checks the field values on GetGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGroupRobotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGroupRobotRequestMultiError, or nil if none found.
func (m *GetGroupRobotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroupRobotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if len(errors) > 0 {
		return GetGroupRobotRequestMultiError(errors)
	}

	return nil
}

// GetGroupRobotRequestMultiError is an error wrapping multiple validation
// errors returned by GetGroupRobotRequest.ValidateAll() if the designated
// constraints aren't met.
type GetGroupRobotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroupRobotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroupRobotRequestMultiError) AllErrors() []error { return m }

// GetGroupRobotRequestValidationError is the validation error returned by
// GetGroupRobotRequest.Validate if the designated constraints aren't met.
type GetGroupRobotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroupRobotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroupRobotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroupRobotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroupRobotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroupRobotRequestValidationError) ErrorName() string {
	return "GetGroupRobotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetGroupRobotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroupRobotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroupRobotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroupRobotRequestValidationError{}

// Validate checks the field values on GetGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetGroupRobotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetGroupRobotReplyMultiError, or nil if none found.
func (m *GetGroupRobotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *GetGroupRobotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreateTime

	// no validation rules for CreatorId

	// no validation rules for CreatorName

	// no validation rules for UpdateTime

	// no validation rules for UpdaterId

	// no validation rules for UpdaterName

	// no validation rules for TobeDevelopedAppInfoId

	// no validation rules for Name

	// no validation rules for Url

	if len(errors) > 0 {
		return GetGroupRobotReplyMultiError(errors)
	}

	return nil
}

// GetGroupRobotReplyMultiError is an error wrapping multiple validation errors
// returned by GetGroupRobotReply.ValidateAll() if the designated constraints
// aren't met.
type GetGroupRobotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetGroupRobotReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetGroupRobotReplyMultiError) AllErrors() []error { return m }

// GetGroupRobotReplyValidationError is the validation error returned by
// GetGroupRobotReply.Validate if the designated constraints aren't met.
type GetGroupRobotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetGroupRobotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetGroupRobotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetGroupRobotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetGroupRobotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetGroupRobotReplyValidationError) ErrorName() string {
	return "GetGroupRobotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e GetGroupRobotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetGroupRobotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetGroupRobotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetGroupRobotReplyValidationError{}

// Validate checks the field values on ListGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListGroupRobotRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListGroupRobotRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListGroupRobotRequestMultiError, or nil if none found.
func (m *ListGroupRobotRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListGroupRobotRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetPage() < 1 {
		err := ListGroupRobotRequestValidationError{
			field:  "Page",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetSize() < 1 {
		err := ListGroupRobotRequestValidationError{
			field:  "Size",
			reason: "value must be greater than or equal to 1",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for TobeDevelopedAppInfoId

	// no validation rules for Name

	if len(errors) > 0 {
		return ListGroupRobotRequestMultiError(errors)
	}

	return nil
}

// ListGroupRobotRequestMultiError is an error wrapping multiple validation
// errors returned by ListGroupRobotRequest.ValidateAll() if the designated
// constraints aren't met.
type ListGroupRobotRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListGroupRobotRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListGroupRobotRequestMultiError) AllErrors() []error { return m }

// ListGroupRobotRequestValidationError is the validation error returned by
// ListGroupRobotRequest.Validate if the designated constraints aren't met.
type ListGroupRobotRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListGroupRobotRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListGroupRobotRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListGroupRobotRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListGroupRobotRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListGroupRobotRequestValidationError) ErrorName() string {
	return "ListGroupRobotRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListGroupRobotRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListGroupRobotRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListGroupRobotRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListGroupRobotRequestValidationError{}

// Validate checks the field values on ListGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListGroupRobotReply) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListGroupRobotReply with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListGroupRobotReplyMultiError, or nil if none found.
func (m *ListGroupRobotReply) ValidateAll() error {
	return m.validate(true)
}

func (m *ListGroupRobotReply) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Total

	for idx, item := range m.GetList() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListGroupRobotReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListGroupRobotReplyValidationError{
						field:  fmt.Sprintf("List[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListGroupRobotReplyValidationError{
					field:  fmt.Sprintf("List[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ListGroupRobotReplyMultiError(errors)
	}

	return nil
}

// ListGroupRobotReplyMultiError is an error wrapping multiple validation
// errors returned by ListGroupRobotReply.ValidateAll() if the designated
// constraints aren't met.
type ListGroupRobotReplyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListGroupRobotReplyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListGroupRobotReplyMultiError) AllErrors() []error { return m }

// ListGroupRobotReplyValidationError is the validation error returned by
// ListGroupRobotReply.Validate if the designated constraints aren't met.
type ListGroupRobotReplyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListGroupRobotReplyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListGroupRobotReplyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListGroupRobotReplyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListGroupRobotReplyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListGroupRobotReplyValidationError) ErrorName() string {
	return "ListGroupRobotReplyValidationError"
}

// Error satisfies the builtin error interface
func (e ListGroupRobotReplyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListGroupRobotReply.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListGroupRobotReplyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListGroupRobotReplyValidationError{}

// Validate checks the field values on ListGroupRobotReply_GroupRobot with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListGroupRobotReply_GroupRobot) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListGroupRobotReply_GroupRobot with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListGroupRobotReply_GroupRobotMultiError, or nil if none found.
func (m *ListGroupRobotReply_GroupRobot) ValidateAll() error {
	return m.validate(true)
}

func (m *ListGroupRobotReply_GroupRobot) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for CreateTime

	// no validation rules for CreatorId

	// no validation rules for CreatorName

	// no validation rules for UpdateTime

	// no validation rules for UpdaterId

	// no validation rules for UpdaterName

	// no validation rules for TobeDevelopedAppInfoId

	// no validation rules for Name

	// no validation rules for Url

	if len(errors) > 0 {
		return ListGroupRobotReply_GroupRobotMultiError(errors)
	}

	return nil
}

// ListGroupRobotReply_GroupRobotMultiError is an error wrapping multiple
// validation errors returned by ListGroupRobotReply_GroupRobot.ValidateAll()
// if the designated constraints aren't met.
type ListGroupRobotReply_GroupRobotMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListGroupRobotReply_GroupRobotMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListGroupRobotReply_GroupRobotMultiError) AllErrors() []error { return m }

// ListGroupRobotReply_GroupRobotValidationError is the validation error
// returned by ListGroupRobotReply_GroupRobot.Validate if the designated
// constraints aren't met.
type ListGroupRobotReply_GroupRobotValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListGroupRobotReply_GroupRobotValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListGroupRobotReply_GroupRobotValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListGroupRobotReply_GroupRobotValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListGroupRobotReply_GroupRobotValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListGroupRobotReply_GroupRobotValidationError) ErrorName() string {
	return "ListGroupRobotReply_GroupRobotValidationError"
}

// Error satisfies the builtin error interface
func (e ListGroupRobotReply_GroupRobotValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListGroupRobotReply_GroupRobot.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListGroupRobotReply_GroupRobotValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListGroupRobotReply_GroupRobotValidationError{}
