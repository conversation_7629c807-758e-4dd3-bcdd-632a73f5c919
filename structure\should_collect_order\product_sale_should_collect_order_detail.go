package should_collect_order

import (
	common "hcscm/common/should_collect_order"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddProductSaleShouldCollectOrderDetailParam struct {
	structure_base.Param
	OtherPrice                 int                                               `json:"other_price"`                                              // 其他金额，其他应退金额(成退)，折扣金额(其他)(手填)
	SaleTaxRate                int                                               `json:"sale_tax_rate"`                                            // 销售税率(手填)
	Remark                     string                                            `json:"remark"`                                                   // 备注(手填)
	Id                         uint64                                            `json:"id" relate:"should_collect_order_detail_id"`               // 应收单详情id
	ShouldCollectOrderId       uint64                                            `json:"should_collect_order_id" relate:"should_collect_order_id"` // 应收单id
	SrcDetailId                uint64                                            `json:"src_detail_id"`                                            // 来源ID
	MaterialId                 uint64                                            `json:"material_id"`                                              // 物料ID(原料id,坯布id,成品颜色id)
	VoucherNumber              string                                            `json:"voucher_number"`                                           // 凭证单号
	OrderTime                  tools.QueryTime                                   `json:"order_time"`                                               // 单据日期
	Code                       string                                            `json:"code"`                                                     // 物料编号
	Name                       string                                            `json:"name"`                                                     // 物料名称
	ProductColorId             uint64                                            `json:"product_color_id"`                                         // 成品颜色id
	ProductColorCode           string                                            `json:"product_color_code"`                                       // 成品颜色编号
	ProductColorName           string                                            `json:"product_color_name"`                                       // 成品颜色名称
	DyelotNumber               string                                            `json:"dyelot_number"`                                            // 染厂缸号
	Roll                       int                                               `json:"roll"`                                                     // 匹数(件)，乘100存
	Weight                     int                                               `json:"weight"`                                                   // 数量(公斤)，乘10000存
	WeightError                int                                               `json:"weight_error"`                                             // 码单空差数量(公斤)，乘10000存
	ActuallyWeight             int                                               `json:"actually_weight"`                                          // 码单数量(公斤)，乘10000存
	SettleErrorWeight          int                                               `json:"settle_error_weight"`                                      // 结算空差数量(公斤)，乘10000存
	SettleWeight               int                                               `json:"settle_weight"`                                            // 结算数量(公斤)，乘10000存
	StandardSalePrice          int                                               `json:"standard_sale_price"`                                      // 标准销售报价(大货 散剪)
	SaleLevelId                uint64                                            `json:"sale_level_id"`                                            // 销售等级ID
	OffsetSalePrice            int                                               `json:"offset_sale_price"`                                        // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                  int                                               `json:"sale_price"`                                               // 销售单价(销售报价-优惠单价)(大货 散剪)
	StandardWeightError        int                                               `json:"standard_weight_error"`                                    // 标准空差 /0.1g
	OffsetWeightError          int                                               `json:"offset_weight_error"`                                      // 优惠空差 /0.1g
	AdjustWeightError          int                                               `json:"adjust_weight_error"`                                      // 调整空差 /0.1g
	Length                     int                                               `json:"length"`                                                   // 长度，乘100存
	StandardLengthCutSalePrice int                                               `json:"standard_length_cut_sale_price"`                           // 剪板销售价格
	OffsetLengthCutSalePrice   int                                               `json:"offset_length_cut_sale_price"`                             // 剪版优惠单价
	LengthCutSalePrice         int                                               `json:"length_cut_sale_price"`                                    // 剪版销售单价(剪板销售价格-剪版优惠单价)
	SettlePrice                int                                               `json:"settle_price"`                                             // 结算金额，应退金额(成退)，实收金额(其他)
	MeasurementUnitId          uint64                                            `json:"measurement_unit_id"`                                      // 计量单位id
	AuxiliaryUnitId            uint64                                            `json:"auxiliary_unit_id"`                                        // 辅助单位id
	SumStockId                 uint64                                            `json:"sum_stock_id"`                                             // 汇总库存id
	SalePlanOrderItemId        uint64                                            `json:"sale_plan_order_item_id"`                                  // 成品销售计划单子项信息id
	SalePlanOrderItemNo        string                                            `json:"sale_plan_order_item_no"`                                  // 成品销售计划单子项单号
	ItemFcList                 AddProductSaleShouldCollectOrderDetailFcParamList `json:"fc_data_list"`                                             // 细码列表
	ProductCraft               string                                            `json:"product_craft"`                                            // 成品工艺
	ProductRemark              string                                            `json:"product_remark"`                                           // 成品备注
	ProductIngredient          string                                            `json:"product_ingredient"`                                       // 成品成分
	ProductLevelId             uint64                                            `json:"product_level_id"`                                         // 成品等级ID
}

type AddProductSaleShouldCollectOrderDetailParamList []AddProductSaleShouldCollectOrderDetailParam

// 更改码单空差和结算空差
type AddProductSaleShouldCollectOrderDetailFcParam struct {
	Id                            uint64             `json:"id"`                                                      // 应收单详情细码id
	OrderDetailFcId               uint64             `json:"order_detail_fc_id"`                                      // 应收细码id
	OrderDetailId                 uint64             `json:"order_detail_id" relate:"should_collect_order_detail_id"` // 应收单详情id
	SrcDetailId                   uint64             `json:"src_detail_id"`                                           // 来源ID
	SrcDetailFcId                 uint64             `json:"src_detail_fc_id"`                                        // 来源细码ID
	WeightError                   int                `json:"weight_error"`                                            // 码单空差数量(公斤)，乘10000存
	SettleErrorWeight             int                `json:"settle_error_weight"`                                     // 结算空差数量(公斤)，乘10000存
	ShouldCollectOrderId          uint64             `json:"-"`                                                       // 应收单id
	CollectType                   common.CollectType `json:"-"`                                                       // 应收类型
	Roll                          int                `json:"-"`                                                       // 匹数
	WarehouseId                   uint64             `json:"-"`                                                       // 仓库id
	ActuallyWeight                int                `json:"actually_weight"`                                         // 码单数量
	SettleWeight                  int                `json:"settle_weight"`                                           // 结算空差
	FinishProductWidthUnitId      uint64             `json:"finish_product_width_unit_id"`                            // 成品幅宽
	FinishProductGramWeightUnitId uint64             `json:"finish_product_gram_weight_unit_id"`                      // 成品克重
	StockRemark                   string             `json:"stock_remark"`                                            // 内部备注
	Remark                        string             `json:"remark"`                                                  // 备注
	WarehouseBinId                uint64             `json:"-"`                                                       // 仓位id
	VolumeNumber                  int                `json:"-"`                                                       // 卷号
	ArrangeOrderNo                string             `json:"-"`                                                       // 配布单号
	StockId                       uint64             `json:"-" relate:"stock_id"`                                     // 详细库存id
	SumStockId                    uint64             `json:"-"`                                                       // 汇总库存id
	BaseUnitWeight                int                `json:"-"`                                                       // 基本单位数量
	PaperTubeWeight               int                `json:"-"`                                                       // 纸筒数量
	UnitId                        uint64             `json:"-"`                                                       // 单位id
	Length                        int                `json:"-"`                                                       // 长度
	AuxiliaryUnitId               uint64             `json:"auxiliary_unit_id"`                                       // 辅助单位id
}

type AddProductSaleShouldCollectOrderDetailFcParamList []AddProductSaleShouldCollectOrderDetailFcParam

func (r AddProductSaleShouldCollectOrderDetailFcParamList) GetTotal() (weightError, settleErrorWeight int) {
	for _, detailFc := range r {
		weightError += detailFc.WeightError
		settleErrorWeight += detailFc.SettleErrorWeight
	}
	return
}

type GetProductSaleShouldCollectOrderDetailData struct {
	structure_base.RecordData
	ShouldCollectOrderId uint64             `json:"should_collect_order_id"` // 应收单id
	SrcDetailId          uint64             `json:"src_detail_id"`           // 来源ID(配布成品信息id)
	CollectType          common.CollectType `json:"collect_type"`            // 应收单类型 1成品 2成品退款 3原料 4坯布 5其他
	CollectTypeName      string             `json:"collect_type_name"`       // 应收单类型名称
	SaleOrderID          uint64             `json:"sale_order_id"`           // 销售单id
	SaleOrderNo          string             `json:"sale_order_no"`           // 销售单号
	FpmSaleOutOrderID    uint64             `json:"fpm_sale_out_order_id"`   // 成品销售出仓单id
	FpmSaleOutOrderNo    string             `json:"fpm_sale_out_order_no"`   // 成品销售出仓单号
	ArrangeOrderID       uint64             `json:"arrange_order_id"`        // 配布单id
	ArrangeOrderNo       string             `json:"arrange_order_no"`        // 配布单号
	WarehouseId          uint64             `json:"warehouse_id"`            // 仓库id
	WarehouseName        string             `json:"warehouse_name"`          // 仓库名称
	ProductId            uint64             `json:"product_id"`              // 成品id
	ProductCode          string             `json:"product_code"`            // 成品编号
	ProductName          string             `json:"product_name"`            // 成品名称
	CustomerId           uint64             `json:"customer_id"`             // 所属客户id
	CustomerCode         string             `json:"customer_code"`           // 所属客户编号
	CustomerName         string             `json:"customer_name"`           // 所属客户名称
	CustomerFullName     string             `json:"customer_full_name"`      // 所属客户全称
	ProductColorId       uint64             `json:"product_color_id"`        // 成品颜色id
	ProductColorCode     string             `json:"product_color_code"`      // 成品颜色名称
	ProductColorName     string             `json:"product_color_name"`      // 成品颜色名称
	DyelotNumber         string             `json:"dyelot_number"`           // 染厂缸号
	ProductCraft         string             `json:"product_craft"`           // 成品工艺
	ProductLevelId       uint64             `json:"product_level_id"`        // 成品等级
	ProductLevelName     string             `json:"product_level_name"`      // 成品等级名称
	ProductIngredient    string             `json:"product_ingredient"`      // 成品成分
	ProductRemark        string             `json:"product_remark"`          // 成品备注

	MeasurementUnitId   uint64 `json:"measurement_unit_id"`   // 单位id
	MeasurementUnitName string `json:"measurement_unit_name"` // 单位名称
	Roll                int    `json:"roll"`                  // 匹数(件)，乘100存
	Weight              int    `json:"weight"`                // 数量(公斤)，乘10000存
	StandardWeightError int    `json:"standard_weight_error"` // 标准空差 /0.1g
	OffsetWeightError   int    `json:"offset_weight_error"`   // 优惠空差 /0.1g
	AdjustWeightError   int    `json:"adjust_weight_error"`   // 调整空差 /0.1g
	WeightError         int    `json:"weight_error"`          // 空差数量(公斤)，乘10000存
	ActuallyWeight      int    `json:"actually_weight"`       // 码单数量
	SettleErrorWeight   int    `json:"settle_error_weight"`   // 结算空差数量
	SettleWeight        int    `json:"settle_weight"`         // 结算数量(公斤)，乘10000存
	Length              int    `json:"length"`                // 长度，乘100存
	WarehouseOutRemark  string `json:"warehouse_out_remark"`  // 出仓备注

	StandardSalePrice          int    `json:"standard_sale_price"`            // 标准销售报价(大货 散剪)
	SaleLevelId                uint64 `json:"sale_level_id"`                  // 销售等级ID
	SaleLevelName              string `json:"sale_level_name"`                // 销售等级名称
	OffsetSalePrice            int    `json:"offset_sale_price"`              // 优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))
	SalePrice                  int    `json:"sale_price"`                     // 销售单价(销售报价-优惠单价)(大货 散剪)
	StandardLengthCutSalePrice int    `json:"standard_length_cut_sale_price"` // 剪板销售价格
	OffsetLengthCutSalePrice   int    `json:"offset_length_cut_sale_price"`   // 剪版优惠单价
	LengthCutSalePrice         int    `json:"length_cut_sale_price"`          // 剪版销售单价(剪板销售价格-剪版优惠单价)
	SaleTaxRate                int    `json:"sale_tax_rate"`                  // 销售税率(手填)
	OtherPrice                 int    `json:"other_price"`                    // 其他金额(手填)
	SettlePrice                int    `json:"settle_price"`                   // 结算金额
	Remark                     string `json:"remark"`                         // 备注(手填)

	ReturnRoll        int                                      `json:"return_roll"`         // 退货匹数
	ReturnWeight      int                                      `json:"return_weight"`       // 退货数量
	ReturnLength      int                                      `json:"return_length"`       // 退货长度，乘100存
	SumStockId        uint64                                   `json:"sum_stock_id"`        // 汇总库存id
	FcDataList        GetProductSaleArrangeOrderItemFcDataList `json:"fc_data_list"`        // 配布细码
	AuxiliaryUnitId   uint64                                   `json:"auxiliary_unit_id"`   // 辅助单位id
	AuxiliaryUnitName string                                   `json:"auxiliary_unit_name"` // 辅助单位名称
}

type GetProductSaleShouldCollectOrderDetailDataList []GetProductSaleShouldCollectOrderDetailData

func (g GetProductSaleShouldCollectOrderDetailDataList) Adjust() {

}
