// Code generated by "stringer -type=BookOrderType --linecomment"; DO NOT EDIT.

package common

func _() {
	// An "invalid array index" compiler error signifies that the constant values have changed.
	// Re-run the stringer command to generate them again.
	var x [1]struct{}
	_ = x[BookOrderTypeReservationPass-1]
	_ = x[BookOrderTypeReservationWait-2]
	_ = x[BookOrderTypeArrangeChangePass-3]
	_ = x[BookOrderTypeArrangeChangeWait-4]
	_ = x[BookOrderTypePurchaseReturnPass-5]
	_ = x[BookOrderTypePurchaseReturnWait-6]
	_ = x[BookOrderTypePMCPush-7]
	_ = x[BookOrderTypeProductSalePass-8]
	_ = x[BookOrderTypeProductSaleWait-9]
	_ = x[BookOrderTypeProductSaleOutPass-10]
	_ = x[BookOrderTypeProductSaleOutWait-11]
	_ = x[BookOrderTypeProductIntAlloOutPass-12]
	_ = x[BookOrderTypeProductIntAlloOutWait-13]
	_ = x[BookOrderTypeProductPrtOutPass-14]
	_ = x[BookOrderTypeProductPrtOutWait-15]
	_ = x[BookOrderTypeProductOtherOutPass-16]
	_ = x[BookOrderTypeProductOtherOutWait-17]
	_ = x[BookOrderTypeProductDeductionOutPass-18]
	_ = x[BookOrderTypeProductDeductionOutWait-19]
	_ = x[BookOrderTypeProductCheckOutPass-20]
	_ = x[BookOrderTypeProductCheckOutWait-21]
	_ = x[BookOrderTypeProductProcessOutPass-22]
	_ = x[BookOrderTypeProductProcessOutWait-23]
	_ = x[BookOrderTypeProductSaleAlloOutPass-24]
	_ = x[BookOrderTypeProductSaleAlloOutWait-25]
	_ = x[BookOrderTypeProductAdjustOutPass-26]
	_ = x[BookOrderTypeProductAdjustOutWait-27]
	_ = x[BookOrderTypeProductSaleAlloInPass-28]
	_ = x[BookOrderTypeProductSaleAlloInWait-29]
}

const _BookOrderType_name = "成品预约出仓单审核成品预约出仓单消审成品配布变更单审核成品配布变更单消审成品采购退货单审核成品采购退货单消审物料计划单下推销售单自动审核成品销售单审核成品销售单消审成品销售出仓单审核成品销售出仓单消审成品内部调拨出仓单审核成品内部调拨出仓单消审成品采购退货出仓单审核成品采购退货出仓单消审成品其他出仓单审核成品其他出仓单消审成品扣款出仓单审核成品扣款出仓单消审成品盘点出仓单审核成品盘点出仓单消审成品加工出仓单审核成品加工出仓单消审成品销售调拨出仓单审核成品销售调拨出仓单消审成品调整出仓单审核成品调整出仓单消审成品销售调拨进仓单审核成品销售调拨进仓单消审"

var _BookOrderType_index = [...]uint16{0, 27, 54, 81, 108, 135, 162, 204, 225, 246, 273, 300, 333, 366, 399, 432, 459, 486, 513, 540, 567, 594, 621, 648, 681, 714, 741, 768, 801, 834}

func (i BookOrderType) String() string {
	i -= 1
	if i < 0 || i >= BookOrderType(len(_BookOrderType_index)-1) {
		return ""
	}
	return _BookOrderType_name[_BookOrderType_index[i]:_BookOrderType_index[i+1]]
}
