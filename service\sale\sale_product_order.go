package sale

import (
	"context"
	aggs "hcscm/aggs/sale"
	system_consts "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	productStructure "hcscm/structure/product"
	stock_structure "hcscm/structure/product"
	saleStructure "hcscm/structure/sale"
	structure "hcscm/structure/sale"
)

type ISaleProductOrderService interface {
	Add(ctx context.Context, tx *mysql_base.Tx, req *structure.AddSaleProductOrderParam) (id uint64, order string, err error)
	MPAdd(ctx context.Context, tx *mysql_base.Tx, req *structure.AddSaleProductOrderParamV2) (id uint64, err error)
	Update(ctx context.Context, req *structure.UpdateSaleProductOrderParam) (data structure.UpdateSaleProductOrderData, err error)
	MPUpdate(ctx context.Context, req *structure.UpdateSaleProductOrderParamV2) (data structure.UpdateSaleProductOrderData, err error)
	UpdateBusinessClose(ctx context.Context, req *structure.UpdateSaleProductOrderBusinessCloseParam) (data structure.UpdateSaleProductOrderBusinessCloseData, err error)
	UpdateStatusPass(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateSaleProductOrderAuditStatusParam) (bookItemsReq productStructure.UpdateStockProductDetailParamList, arrangeItemsReq productStructure.AddFpmArrangeOrderParamList, shortageItemReq *saleStructure.AddShortageProductOrderParam, pmcPlanSummaryReq *structure.AuditUpdatePushed, err error)
	UpdateStatusWait(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateSaleProductOrderAuditStatusParam) (bookItemsReq productStructure.UpdateStockProductDetailParamList, pmcPlanSummaryReq *structure.AuditUpdatePushed, err error)
	UpdateStatusCancel(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, err error)
	UpdateStatusReject(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, err error)
	Delete(ctx context.Context, req *structure.DeleteSaleProductOrderParam) (data structure.DeleteSaleProductOrderData, err error)
	Exist(ctx context.Context, getDetail bool, ids, summaryIds []uint64) (list structure.PushedRecordList, orderNos []string, exist bool, err error)
	ExistBySaleProductPlanOrderId(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error)
	Get(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderData, err error)
	MPGet(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderDataV2, err error)
	GetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataList, total int, err error)
	MPGetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataListV2, total int, err error)
	GetDropdownList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDropdownDataList, total int, err error)
	GetSaleOrderStatusNum(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (data structure.GetSaleOrderStatusNumData, err error)
	GetLastSalePrice(ctx context.Context, q *structure.GetLastSalePriceQuery) (structure.GetLastSalePriceDataList, error)
	GetHistorySaleOrderList(ctx context.Context, req *structure.GetHistorySaleOrderListQuery) (list structure.GetHistorySaleOrderDataList, total int, err error)
	AutoOcr(ctx context.Context, req *structure.GetProductByAutoOrcParam) (data stock_structure.GetStockProductDropdownDataList, err error)
	GetCustomerLastMoneyInfo(ctx context.Context, q *structure.GetCustomerLastMoneyInfoReq) (structure.GetCustomerLastMoneyInfoRes, error)
}

func NewSaleProductOrderService(ctx context.Context, isCache bool) ISaleProductOrderService {
	return &SaleProductOrderService{
		isCache: isCache,
	}
}

type SaleProductOrderService struct {
	isCache bool
}

func (u SaleProductOrderService) GetSaleOrderStatusNum(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (data structure.GetSaleOrderStatusNumData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.GetSaleOrderStatusNum(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) MPGet(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderDataV2, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.MPGet(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) AutoOcr(ctx context.Context, req *structure.GetProductByAutoOrcParam) (data stock_structure.GetStockProductDropdownDataList, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.AutoOcr(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) Add(ctx context.Context, tx *mysql_base.Tx, req *structure.AddSaleProductOrderParam) (id uint64, orderNo string, err error) {
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	id, orderNo, err = repo.Add(ctx, req)
	if err != nil {
		return
	}
	err = repo.AddSaleProductOrderDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	return
}
func (u SaleProductOrderService) MPAdd(ctx context.Context, tx *mysql_base.Tx, req *structure.AddSaleProductOrderParamV2) (id uint64, err error) {
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	id, err = repo.MPAdd(ctx, req)
	if err != nil {
		return
	}
	err = repo.AddMPSaleProductOrderDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	return
}

func (u SaleProductOrderService) Update(ctx context.Context, req *structure.UpdateSaleProductOrderParam) (data structure.UpdateSaleProductOrderData, err error) {
	var (
		id uint64
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	id, err = repo.Update(ctx, req)
	if err != nil {
		return data, err
	}
	err = repo.UpdateSaleProductOrderDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	data.Id = id
	return
}
func (u SaleProductOrderService) MPUpdate(ctx context.Context, req *structure.UpdateSaleProductOrderParamV2) (data structure.UpdateSaleProductOrderData, err error) {
	var (
		id uint64
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	id, err = repo.MPUpdate(ctx, req)
	if err != nil {
		return data, err
	}
	err = repo.MPUpdateSaleProductOrderDetailDetails(ctx, req, id)
	if err != nil {
		return
	}
	data.Id = id
	return
}

func (u SaleProductOrderService) UpdateBusinessClose(ctx context.Context, req *structure.UpdateSaleProductOrderBusinessCloseParam) (data structure.UpdateSaleProductOrderBusinessCloseData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.UpdateBusinessClose(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) UpdateStatusPass(
	ctx context.Context,
	tx *mysql_base.Tx,
	req *structure.UpdateSaleProductOrderAuditStatusParam,
) (
	bookItemsReq productStructure.UpdateStockProductDetailParamList,
	arrangeItemsReq productStructure.AddFpmArrangeOrderParamList,
	shortageItemReq *saleStructure.AddShortageProductOrderParam,
	pmcPlanSummaryReq *structure.AuditUpdatePushed,
	err error,
) {
	salePlanOrderItemIds := make([]uint64, 0)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	planRepo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	bookItemsReq, arrangeItemsReq, shortageItemReq, pmcPlanSummaryReq, salePlanOrderItemIds, err = repo.UpdateStatusPass(ctx, req)
	if err != nil {
		return
	}
	// 更新销售计划单详细成品状态
	if len(salePlanOrderItemIds) > 0 {
		err = planRepo.UpdateSituStatus(ctx, &structure.UpdateSaleProductPlanOrderProductDetailSituStatusParam{
			Ids:        salePlanOrderItemIds,
			SituStatus: system_consts.SituStatusProductSaleOrder,
			OrderId:    req.Id,
			IsPass:     true,
			Reason:     "销售单审核",
		})
		if err != nil {
			return
		}
	}
	return
}

func (u SaleProductOrderService) UpdateStatusWait(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateSaleProductOrderAuditStatusParam) (
	bookItemsReq productStructure.UpdateStockProductDetailParamList,
	pmcPlanSummaryReq *structure.AuditUpdatePushed,
	err error) {

	salePlanOrderItemIds := make([]uint64, 0)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	planRepo := aggs.NewSaleProductPlanOrderRepo(ctx, tx, u.isCache)
	bookItemsReq, pmcPlanSummaryReq, salePlanOrderItemIds, err = repo.UpdateStatusWait(ctx, req)
	if err != nil {
		return
	}
	// 更新销售计划单详细成品状态
	if len(salePlanOrderItemIds) > 0 {
		err = planRepo.UpdateSituStatus(ctx, &structure.UpdateSaleProductPlanOrderProductDetailSituStatusParam{
			Ids:        salePlanOrderItemIds,
			SituStatus: system_consts.SituStatusPmcOrder,
			OrderId:    req.Id,
			IsPass:     false,
			Reason:     "销售单消审",
		})
		if err != nil {
			return
		}
	}
	return
}

func (u SaleProductOrderService) UpdateStatusCancel(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, err error) {
	// var (
	// BackPlanDetail map[uint64][2]int
	// )
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	// planRepo := aggs.NewSaleProductPlanOrderRepo(tx)
	defer func() {
		err = commit(err, recover())
	}()
	data, _, err = repo.UpdateStatusCancel(ctx, req)
	if err != nil {
		return data, err
	}

	// err = planRepo.UpdateDetail(ctx, BackPlanDetail)
	if err != nil {
		return
	}

	return
}

func (u SaleProductOrderService) UpdateStatusReject(ctx context.Context, req *structure.UpdateSaleProductOrderAuditStatusParam) (data structure.UpdateSaleProductOrderAuditStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.UpdateStatusReject(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) Delete(ctx context.Context, req *structure.DeleteSaleProductOrderParam) (data structure.DeleteSaleProductOrderData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.Delete(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) Exist(ctx context.Context, getDetail bool, ids, summaryIds []uint64) (list structure.PushedRecordList, orderNos []string, exist bool, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	list, orderNos, exist, err = repo.Exist(ctx, getDetail, ids, summaryIds)
	if err != nil {
		return
	}
	return
}
func (u SaleProductOrderService) ExistBySaleProductPlanOrderId(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	orderNos, exist, err = repo.ExistBySaleProductPlanOrderId(ctx, ids)
	if err != nil {
		return
	}
	return
}

func (u SaleProductOrderService) Get(ctx context.Context, req *structure.GetSaleProductOrderQuery) (data structure.GetSaleProductOrderData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	data, err = repo.Get(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u SaleProductOrderService) GetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}
func (u SaleProductOrderService) MPGetList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDataListV2, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.MPGetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u SaleProductOrderService) GetDropdownList(ctx context.Context, req *structure.GetSaleProductOrderListQuery) (list structure.GetSaleProductOrderDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u *SaleProductOrderService) GetLastSalePrice(ctx context.Context, req *structure.GetLastSalePriceQuery) (res structure.GetLastSalePriceDataList, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, true)
	res, err = repo.GetLastSalePrice(ctx, req)
	if err != nil {
		return nil, err
	}
	return
}

func (u *SaleProductOrderService) GetHistorySaleOrderList(ctx context.Context, req *structure.GetHistorySaleOrderListQuery) (list structure.GetHistorySaleOrderDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, u.isCache)
	list, total, err = repo.GetHistorySaleOrderList(ctx, req)
	if err != nil {
		return nil, 0, err
	}
	return
}

// GetCustomerLastMoneyInfo 获取客户上次销售信息
func (s *SaleProductOrderService) GetCustomerLastMoneyInfo(ctx context.Context, q *structure.GetCustomerLastMoneyInfoReq) (data structure.GetCustomerLastMoneyInfoRes, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewSaleProductOrderRepo(ctx, tx, s.isCache)
	data, err = repo.GetCustomerLastMoneyInfo(ctx, q)
	return
}
