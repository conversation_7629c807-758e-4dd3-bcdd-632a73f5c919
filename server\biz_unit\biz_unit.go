package biz_unit

import (
	"context"
	"hcscm/common/errors"
	"hcscm/domain/biz_unit/entity"
	"hcscm/extern/pb/basic_data/type_basic_data"
	"hcscm/extern/pb/employee"
	sale_system_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	"hcscm/server/system"
	"hcscm/service/biz_unit"
	structure "hcscm/structure/system"

	"github.com/gin-gonic/gin"
)

// GetCreditLevelList 信用等级列表
//
//	@Tags		往来单位管理
//	@Security	ApiKeyAuth
//	@Summary	信用等级列表
//	@Produce	json
//	@Param		Platform		header	int		true	"终端ID"
//	@Param		Authorization	header	string	true	"token"
//	@Router		/hcscm/admin/v1/business_unit/credit_level/list [get]
func GetCreditLevelList(c *gin.Context) {
	system.BuildMapResponseV2(c, entity.CreditLevelMap)
	return
}

// GetCreditLevelList 信用等级列表
//
//	@Tags		往来单位管理
//	@Security	ApiKeyAuth
//	@Summary	数据清洗
//	@Produce	json
//	@Param		Platform		header	int		true	"终端ID"
//	@Param		Authorization	header	string	true	"token"
//	@Router		/hcscm/admin/v1/business_unit/update/sale_system/rel [put]
func UpdateBizUnitAndSaleSystemRel(c *gin.Context) {
	var (
		svc  = biz_unit.NewBizUnitQueryService()
		data = &structure.AddAndUpdateResponse{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, data)
	}()
	svc.UpdateBizUnitAndSaleSystemRel(ctx)
	return
}

// @Router		/hcscm/admin/v1/business_unit/update/pin_yin [get]
func WashBizUnitPinYin(c *gin.Context) {
	var (
		svc = biz_unit.NewBizUnitService()
		err error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, nil)
	}()
	svc.WashBizUnitPinYin(ctx)
	return
}

// GetBizUnitList 往来单位列表（枚举）
//
//	@Tags		往来单位管理
//	@Summary	往来单位列表
//	@Security	ApiKeyAuth
//	@Param		page			query		int		false	"page"
//	@Param		size			query		int		false	"size"
//	@Param		offset			query		int		false	"offset"
//	@Param		limit			query		int		false	"limit"
//	@Param		download		query		int		false	"是否导出excel"
//	@Param		sale_system_id	query		int		false	"营销系统ID"
//	@Param		name			query		string	false	"名称"
//	@Param		unit_type_id	query		int		false	"类型id"
//	@Param		or_unit_type_id	query		string	false	"类型ids 配合 or_category用"
//	@Param		or_category		query		int		false	"or单位分类 1供应商 2客户"
//	@Param		seller_id		query		int		false	"销售员id"
//	@Param		sale_area_id	query		int		false	"销售区域id"
//	@Param		sale_group_id	query		int		false	"销售群体id"
//	@Param		phone			query		string	false	"电话"
//	@Param		status			query		int		false	"状态"
//	@Param		address			query		string	false	"地址"
//	@Param		nop				query		string	false	"名称或电话"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.GetBizUnitListResponse{}
//	@Router		/hcscm/admin/v1/business_unit/list [get]
func GetBizUnitList(c *gin.Context) {
	var (
		q     = &structure.GetBizUnitListParams{}
		data  = &structure.GetBizUnitListResponse{}
		err   error
		total int
		svc   = biz_unit.NewBizUnitQueryService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildListResponse(c, err, data, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 默认启用
	if q.Status == 0 {
		q.Status = 1
	}

	// 往来单位类型新增类型枚举(供给客户自定义多种叫法)
	if q.BizUnitType != "" || q.OrBizUnitType != "" {
		err = GetUnitTypeIds(ctx, q)
		if err != nil {
			return
		}
	}

	// 销售员ids
	if q.CustomerOrSaleUserName != "" || q.SellerName != "" {
		var name string
		name = q.CustomerOrSaleUserName
		if name != "" {
			q.Name = name
		}
		if q.SellerName != "" {
			name = q.SellerName
		}
		var (
			employeeSvc = employee.NewClientEmployeeService()
			employeeRes = employee.Response{}
		)
		employeeRes, err = employeeSvc.GetEmployeeByLikeCodeOrName(ctx, name)
		if err != nil {
			return
		}
		q.SellerIDs = employeeRes.EmployeeIDs
	}

	// 营销体系名称
	if q.SaleSystemName != "" {
		q.SaleSystemIDs, err = sale_system_pb.NewSaleSystemClient().GetSaleSystemIdsByLikeName(ctx, q.SaleSystemName)
		if err != nil {
			return
		}
	}

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	*data, total, err = svc.QueryBizUnitList(ctx, tx, q)
	if err != nil {
		return
	}

	return
}

// GetBizUnitList 往来单位列表（枚举）
//
//	@Tags		往来单位管理
//	@Summary	往来单位列表(填编号的时候用的)
//	@Security	ApiKeyAuth
//	@Param		code			query		string	false	"编号"
//	@Param		category		query		int		false	"单位分类 1供应商 2客户"
//	@Param		Platform		header		int		true	"终端ID"
//	@Param		Authorization	header		string	true	"token"
//	@Success	200				{object}	structure.GetSomeBizUnitFieldDataList{}
//	@Router		/hcscm/admin/v1/business_unit/getSomeBizUnitList [get]
func GetSomeBizUnitList(c *gin.Context) {
	var (
		q     = &structure.GetBizUnitListParams{}
		data  = &structure.GetSomeBizUnitFieldDataList{}
		err   error
		total int
		svc   = biz_unit.NewBizUnitQueryService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildListResponse(c, err, data, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	if q.Code == "" {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请输入编号"))
		return
	}
	if q.Category == 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeLackMustInfo, " 请输入往来单位分类"))
		return
	}

	// 默认给page size
	if q.Page == 0 && q.Size == 0 {
		q.Page = 1
		q.Size = 5
	}

	*data, err = svc.QuerySomeBizUnitFieldList(ctx, q)
	if err != nil {
		return
	}
	total = len(*data)
	return
}

// 根据往来单位类型枚举获取往来单位类型基础资料ids
func GetUnitTypeIds(ctx context.Context, q *structure.GetBizUnitListParams) (err error) {
	var (
		unitTypeIds []uint64
	)
	typeBasicDataSvc := type_basic_data.NewTypeIntercourseUnitsClient()
	if !q.BizUnitType.IsNil() {
		unitTypeIds, err = typeBasicDataSvc.GetTypeIntercourseUnitsIDByBizUnitType(ctx, q.BizUnitType.ToUint64())
		q.UnitTypeId = unitTypeIds
	} else if !q.OrBizUnitType.IsNil() {
		unitTypeIds, err = typeBasicDataSvc.GetTypeIntercourseUnitsIDByBizUnitType(ctx, q.OrBizUnitType.ToUint64())
		q.OrUnitTypeId = unitTypeIds
		if err != nil {
			return
		}
	}
	return
}

// GetDnfChargingMethodEnum 染费收费方式枚举
//
//	@Tags		往来单位管理
//	@Security	ApiKeyAuth
//	@Summary	染费收费方式枚举
//	@Produce	json
//	@Param		Platform		header	int		true	"终端ID"
//	@Param		Authorization	header	string	true	"token"
//	@Router		/hcscm/admin/v1/business_unit/dnf_charging_method/enum [get]
func GetDnfChargingMethodEnum(c *gin.Context) {
	system.BuildMapResponseV2(c, entity.DnfChargingMethodMap)
	return
}

func BizUnitAddSaleGroup(c *gin.Context) {
	var (
		// q    = &structure.BizUnitAddSaleGroupParams{}
		data = &structure.BizUnitAddSaleGroupRes{}
		err  error
		svc  = biz_unit.NewBizUnitQueryService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, data)
	}()

	// err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	*data, err = svc.BizUnitAddSaleGroup(ctx)
	if err != nil {
		return
	}
	return
}

// BizUnitLocationWash 清洗客户地区数据
//
//	@Tags		往来单位管理
//	@Security	ApiKeyAuth
//	@Summary	清洗客户地址数据
//	@Description	从客户地址中提取省市区信息并更新到数据库
//	@Produce	json
//	@Param		Platform		header	int		true	"终端ID"
//	@Param		Authorization	header	string	true	"token"
//	@Router		/hcscm/admin/v1/temporary/bizUnit/BizUnitLocationWash [post]
func BizUnitLocationWash(c *gin.Context) {
	var (
		res structure.ResponseData
		err error
		svc = biz_unit.NewBizUnitQueryService()
		q   structure.BizUnitLocationWashQuery
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, &res)
	}()

	if err = c.ShouldBind(&q); err != nil {
		return
	}
	res, err = svc.BizUnitLocationWash(ctx, &q)
	if err != nil {
		return
	}
	return
}

// BizUnitDefaultAddressWash 清洗客户默认地址
//
//	@Tags		往来单位管理
//	@Security	ApiKeyAuth
//	@Summary	清洗客户默认地址
//	@Description	从客户地址中提取新建一个默认地址,如果客户已经存在一个默认地址则跳过
//	@Produce	json
//	@Param		Platform		header	int		true	"终端ID"
//	@Param		Authorization	header	string	true	"token"
//	@Router		/hcscm/admin/v1/temporary/biz_unit/bizUnitDefaultAddressWash [post]
func BizUnitDefaultAddressWash(c *gin.Context) {
	var (
		res structure.ResponseData
		err error
		svc = biz_unit.NewBizUnitQueryService()
		q   structure.BizUnitLocationWashQuery
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, &res)
	}()

	if err = c.ShouldBind(&q); err != nil {
		return
	}
	res, err = svc.BizUnitDefaultAddressWash(ctx, &q)
	if err != nil {
		return
	}
	return
}
