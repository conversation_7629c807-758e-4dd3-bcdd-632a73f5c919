package system

import (
	"github.com/gin-gonic/gin"
	"hcscm/common/errors"
	common_system "hcscm/common/system_consts"
	info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	product_pb "hcscm/extern/pb/basic_data/product"
	"hcscm/extern/pb/ocr"
	"hcscm/extern/pb/ocr/pb_ocr_cloth"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/ocr"
	mysql "hcscm/model/mysql/ocr/dao"
	product_model "hcscm/model/mysql/product"
	product_mysql "hcscm/model/mysql/product/dao"
	"hcscm/server/system"
	tenant_management_svc "hcscm/service/tenant_management"
	structure "hcscm/structure/ocr"
	structure_base "hcscm/structure/system"
	"hcscm/tools/metadata"
	"hcscm/vars"
)

// @Tags		【ocr识别】
// @Security	ApiKeyAuth
// @Summary	通用识别布匹
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Param   body   body   structure.OcrCommonClothParam{}  true "创建Printer"
// @Success	200				{object}	structure.OcrCommonClothData{}
// @Router		/hcscm/admin/v1/ocr/cloth [post]
// @Tags		【ocr识别】
// @Security	ApiKeyAuth
// @Summary	通用识别布匹
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Param   body   body   structure.OcrCommonClothParam{}  true "创建Printer"
// @Success	200				{object}	structure.OcrCommonClothData{}
// @Router		/hcscm/mp/v1/ocr/cloth [post]
func OcrClothCommon(c *gin.Context) {

	var (
		p    = &structure.OcrCommonClothParam{}
		data = structure.OcrCommonClothData{}
		res  = &pb_ocr_cloth.OcrCommonClothData{}
		err  error
		tx   *mysql_base.Tx
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	if metadata.GetUserId(ctx) != vars.AdminUserID || vars.Env != "test" {
		if vars.Env != "local" {
			// 判断码单是否充值过
			_, err = tenant_management_svc.NewCodeListOrcManagementLogic(c).IsRecharged(ctx, metadata.GetTenantManagementId(ctx))
			if err != nil {
				return
			}

			// 判断码单是否过期
			_, err = tenant_management_svc.NewCodeListOrcManagementLogic(c).IsExpired(ctx, common_system.RechargeTypeOcr, metadata.GetTenantManagementId(ctx))
			if err != nil {
				return
			}
		}
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	if ocr.GetClient() == nil {
		// err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "ocr服务错误"))
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeOCRBusy))
		return
	}
	res, err = ocr.GetClient().TcloudCommonCloth(ctx, p.ToPbStruct())
	if err != nil {
		// err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "ocr服务错误"))
		err = middleware.WarnLog(errors.NewError(errors.ErrCodeOCRBusy))
		return
	}
	var (
		codes         = make([]string, 0)
		colorCodes    = make([]string, 0)
		units         = make([]string, 0)
		products      product_model.FinishProductList
		productColors product_model.FinishProductColorList
		unitNames     info_pb.GetInfoBaseMeasurementUnitDataList
	)
	for _, cloth := range res.List {
		for _, xima := range cloth.XimaList {
			codes = append(codes, xima.ProductCode)
			colorCodes = append(colorCodes, xima.ProductColorCode)
			units = append(units, xima.Unit)
		}
	}

	products, err = product_mysql.FindFinishProductByCode(tx, codes)
	if err != nil {
		return
	}
	productColors, err = product_mysql.FindFinishProductColorByCode(tx, colorCodes)
	if err != nil {
		return
	}

	unitNames, err = info_pb.NewInfoBaseMeasurementUnitClient().GetinfoBaseMeasurementUnitByCodeOrName(ctx, nil, units)
	if err != nil {
		return
	}

	// 获取启用的第一个计量单位id
	unit, _ := info_pb.NewInfoBaseMeasurementUnitClient().GetFirstinfoBaseMeasurementUnit(ctx)

	var cloths = make(structure.OcrCommonClothList, 0)
	for _, cloth := range res.List {
		var ximaList = make([]*structure.Xima, 0)
		for _, xima := range cloth.XimaList {
			product := products.PickByCode(xima.ProductCode)
			productColor := productColors.PickByCode(product.Id, xima.ProductColorCode)
			if product.Id == 0 {
				product.Id = vars.Snowflake.GenerateId().UInt64()
				product.FinishProductName = xima.ProductName
				product.FinishProductCode = xima.ProductCode
				product.FinishProductFullName = xima.ProductName
				product.MeasurementUnitId = unit.Id
				product.Status = common_system.StatusEnable
				err = product_pb.NewProductClient().MustCreateProduct(ctx, product)
				if err != nil {
					return
				}
				products = append(products, product)
			}
			if productColor.Id == 0 {
				productColor.Id = vars.Snowflake.GenerateId().UInt64()
				productColor.ProductColorName = xima.ProductColorName
				productColor.ProductColorCode = xima.ProductColorCode
				productColor.FinishProductId = product.Id
				productColor.Status = common_system.StatusEnable
				err = product_pb.NewProductColorClient().MustCreateProductColor(ctx, productColor)
				if err != nil {
					return
				}
				productColors = append(productColors, productColor)
			}
			ximaList = append(ximaList, &structure.Xima{
				ProductId:               product.Id,
				ProductCode:             xima.ProductCode,
				ProductName:             xima.ProductName,
				ProductCodeAndName:      xima.ProductCodeAndName,
				ProductColorId:          productColor.Id,
				ProductColorCode:        xima.ProductColorCode,
				ProductColorName:        xima.ProductColorName,
				ProductColorCodeAndName: xima.ProductColorCodeAndName,
				DyelotNumber:            xima.DyelotNumber,
				Weight:                  int(xima.Weight),
				WeightError:             int(xima.WeightError),
				SettleWeight:            int(xima.SettleWeight),
				Price:                   int(xima.Price),
				Width:                   xima.Width,
				WeightGram:              xima.WeightGram,
				RowRoll:                 int(xima.RowRoll),
				Unit:                    xima.Unit,
				RowTotalWeight:          int(xima.RowTotalWeight),
				RowSettleWeight:         int(xima.RowSettleWeight),
				RowWeightError:          int(xima.RowWeightError),
				RowMoney:                int(xima.RowMoney),
				RowRemark:               xima.RowRemark,
				Md5:                     xima.Md5,
				UUID:                    xima.Uuid,
				Error:                   xima.Error,
			})
		}
		product := products.PickByCode(cloth.ProductCode)
		productColor := productColors.PickByCode(product.Id, cloth.ProductColorCode)
		unitName := unitNames.PickByCodeOrName("", cloth.Unit)
		var ocrCommonCloth = structure.OcrCommonCloth{}
		ocrCommonCloth = structure.OcrCommonCloth{
			SplitType:               int(cloth.SplitType),
			ProductId:               product.Id,
			ProductCode:             cloth.ProductCode,
			ProductName:             product.FinishProductName,
			ProductCodeAndName:      cloth.ProductCodeAndName,
			ProductColorId:          productColor.Id,
			ProductColorCode:        cloth.ProductColorCode,
			ProductColorName:        productColor.ProductColorName,
			ProductColorCodeAndName: cloth.ProductColorCodeAndName,
			DyelotNumber:            cloth.DyelotNumber,
			Price:                   int(cloth.Price),
			UnitId:                  unitName.Id,
			Unit:                    cloth.Unit,
			Roll:                    int(cloth.Total) * vars.Roll,
			Total:                   int(cloth.Total),
			TotalWeight:             int(cloth.TotalWeight),
			TotalWeightError:        int(cloth.TotalWeightError),
			TotalSettleWeight:       int(cloth.TotalSettleWeight),
			OrderNo:                 cloth.OrderNo,
			XimaList:                ximaList,
			// Content:                 cloth.Content,
			Header: cloth.Header,
			// More:                    cloth.More,
		}
		if ocrCommonCloth.UnitId == 0 {
			ocrCommonCloth.UnitId = product.MeasurementUnitId
			ocrCommonCloth.Unit = unit.Name
		}
		ocrRecord := model.NewOcrRecord(ctx, ocrCommonCloth)

		ocrRecordDao := mysql.NewIOcrRecordDao(ctx, false)
		ocrRecord, err = ocrRecordDao.MustCreate(ctx, tx, ocrRecord)
		if err != nil {
			return
		}

		ocrCommonCloth.Id = ocrRecord.Id

		// header:=ocrCommonCloth.Header
		// ocrCommonCloth.SupplierId=
		// ocrCommonCloth.SupplierName=header

		cloths = append(cloths, ocrCommonCloth)
	}
	data = structure.OcrCommonClothData{
		List:  cloths,
		Total: int(res.Total),
	}
	return
}

// @Tags		【ocr识别】
// @Security	ApiKeyAuth
// @Summary	更新识别结果内容
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Param   body   body   structure.OcrCommonClothParam{}  true "创建Printer"
// @Success	200				{object}	structure.OcrCommonClothData{}
// @Router		/hcscm/admin/v1/ocr/cloth/update [post]
// @Tags		【ocr识别】
// @Security	ApiKeyAuth
// @Summary	更新识别结果内容
// @Produce	json
// @Param		Platform		header		int							true	"终端ID"
// @Param		Authorization	header		string						true	"token"
// @Param   body   body   structure.OcrCommonClothParam{}  true "创建Printer"
// @Success	200				{object}	structure.OcrCommonClothData{}
// @Router		/hcscm/mp/v1/ocr/cloth/update [post]
func UpdateOcrClothCommon(c *gin.Context) {

	var (
		p    = &structure.UpdateTcloudCommonClothParam{}
		data = structure_base.AddAndUpdateResponse{}
		err  error
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, p)
	if err != nil {
		return
	}

	for _, cloth := range p.List {
		if cloth.Id == 0 {
			continue
		}
		tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
		defer func() {
			err = commit(err, recover())
		}()
		var (
			ocrRecord    model.OcrRecord
			ocrRecordDao = mysql.NewIOcrRecordDao(ctx, false)
		)
		ocrRecord, err = ocrRecordDao.MustFirstById(ctx, tx, cloth.Id)
		if err != nil {
			return
		}
		ocrRecord.UpdateOcrRecord(ctx, cloth)

		ocrRecord, err = ocrRecordDao.MustUpdate(ctx, tx, ocrRecord)
		if err != nil {
			return
		}
	}

	return
}
