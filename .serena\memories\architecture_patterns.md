# HCSCM 架构和设计模式

## 整体架构

### 分层架构
HCSCM采用经典的分层架构模式：

```
HTTP请求 -> Router -> Server -> Service -> Aggs -> Model -> Database
```

1. **Router层** (`router/`)
   - 路由定义和HTTP请求分发
   - 中间件应用
   - 请求参数验证

2. **Server层** (`server/`)
   - HTTP处理器实现
   - 请求响应格式化
   - 业务逻辑调用

3. **Service层** (`service/`)
   - 核心业务逻辑
   - 事务管理
   - 业务规则验证

4. **Aggs层** (`aggs/`)
   - 业务聚合逻辑
   - 领域模型组合
   - 复杂业务流程编排

5. **Model层** (`model/`)
   - 数据访问对象(DAO)
   - 数据库操作
   - 缓存管理

## 设计模式

### 1. 依赖注入模式
- 通过初始化函数注入依赖
- 配置统一管理
- 便于测试和模块替换

### 2. 仓储模式 (Repository Pattern)
- 数据访问逻辑封装
- 业务逻辑与数据存储分离
- 支持多种数据源

### 3. 工厂模式
- 对象创建逻辑封装
- 配置驱动的对象创建
- 便于扩展和维护

### 4. 中间件模式
- 横切关注点处理
- 认证、授权、日志、监控
- 可插拔的功能模块

### 5. 事件驱动模式
- 异步消息处理
- RabbitMQ消息队列
- 系统解耦

## 模块化设计

### 业务模块划分
- **基础数据** (`basic_data`): 基础信息管理
- **销售管理** (`sale`): 销售订单、计划等
- **采购管理** (`purchase`): 采购订单、供应商等
- **库存管理** (`stock`): 库存、出入库等
- **生产管理** (`produce`): 生产计划、工艺等
- **财务管理** (`payable`): 应付账款、成本等
- **员工管理** (`employee`): 人员信息、权限等
- **系统管理** (`system`): 配置、日志等

### 跨模块通信
- 通过Service层接口调用
- 事件消息传递
- 共享数据模型

## 数据访问模式

### 主从数据库
- 读写分离
- 主库写入，从库读取
- 自动故障转移

### 缓存策略
- Redis分布式缓存
- 本地内存缓存
- 多级缓存架构

### 事务管理
- GORM事务支持
- 分布式事务处理
- 事务回滚机制

## 安全设计

### 认证授权
- JWT Token认证
- 基于角色的访问控制(RBAC)
- API权限验证

### 数据安全
- 敏感数据加密
- SQL注入防护
- 输入参数验证

## 性能优化

### 数据库优化
- 索引优化
- 查询优化
- 连接池管理

### 缓存优化
- 热点数据缓存
- 缓存预热
- 缓存失效策略

### 并发处理
- Goroutine池
- 异步处理
- 限流控制

## 监控和运维

### 日志系统
- 结构化日志
- 日志级别控制
- 日志聚合分析

### 性能监控
- pprof性能分析
- 指标收集
- 健康检查

### 部署策略
- Docker容器化
- 配置外部化
- 滚动更新