package employee

import "hcscm/model/mysql/mysql_base"

// EmployeeSaleSystemRel 员工所属营销体系
type EmployeeSaleSystemRel struct {
	mysql_base.Model
	Id           uint64 `gorm:"primary_key;AUTO_INCREMENT;column:id"`
	EmployeeID   uint64 `gorm:"column:employee_id"`    // 员工id
	SaleSystemID uint64 `gorm:"column:sale_system_id"` // 营销体系id
}

func (r *EmployeeSaleSystemRel) GetId() uint64 {
	return r.Id
}

func (*EmployeeSaleSystemRel) TableName() string {
	return "employee_sale_system_rel"
}

func GetSaleSystemIDSet(l []*EmployeeSaleSystemRel) []uint64 {
	m := make(map[uint64]struct{})
	for _, item := range l {
		m[item.SaleSystemID] = struct{}{}
	}
	ids := make([]uint64, 0, len(m))
	for id, _ := range m {
		ids = append(ids, id)
	}
	return ids
}

func GetEmployeeSaleSystemMap(l []*EmployeeSaleSystemRel) map[uint64][]uint64 {
	m := make(map[uint64][]uint64)
	for _, item := range l {
		_, ok := m[item.EmployeeID]
		if !ok {
			m[item.EmployeeID] = make([]uint64, 0)
		}
		m[item.EmployeeID] = append(m[item.EmployeeID], item.SaleSystemID)
	}
	return m
}
