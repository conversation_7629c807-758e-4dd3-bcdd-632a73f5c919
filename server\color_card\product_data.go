package color_card

import (
	"github.com/gin-gonic/gin"
	"hcscm/common/errors"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	"hcscm/server/carouselBanner"
	"hcscm/server/system"
	"hcscm/service/basic_data"
	svc_color_card "hcscm/service/color_card"
	color_card_struct "hcscm/structure/color_card"
	structure "hcscm/structure/product"
)

// GetTypeFabricInfo 获取成品颜色列表
// @Summary 获取成品颜色列表
// @Description 获取所有成品颜色信息
// @Tags 电子色卡分享页-成品列表获取
// @Accept json
// @Produce json
// @Security ApiKeyAuth
// @Param     body      body     structure.GetFinishProductListQuery{}  true  "信息"
// @Param     Platform  header    int                                      true  "终端ID"
// @Success 200 {object} interface{}
// @Router /hcscm/third_party/v1/color_card/getTypeFabricInfo [get]
func GetFinishProductColorList(c *gin.Context) {
	var (
		q                      = &structure.GetFinishProductListQuery{}
		list                   = make(structure.GetFinishProductDropdownDataList, 0)
		processedColorCardList = make(color_card_struct.GetFinishProductColorDataList, 0)
		total                  int
		err                    error
		svc                    = basic_data.NewFinishProductService()
		svcColorCard           = svc_color_card.NewColorCardService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildListResponse(c, err, processedColorCardList, total)
	}()
	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	q.FromColorCard = true
	// q.IsPage = true // GetFinishProductDropdownList
	list, total, err = svc.GetDropdownList(ctx, q)
	processedColorCardList, err = svcColorCard.HandlerProductData(ctx, list)
	if err != nil {
		return
	}
	return
}

// GetProductDetail 获取商品详情
// @Summary 获取商品详情
// @Description 获取单个商品的详细信息
// @Tags 电子色卡分享页-成品详情获取
// @Accept json
// @Produce json
// @Param id query uint64 true "商品ID"
// @Security ApiKeyAuth
// @Param     body      body     color_card_struct.GetProductDetailQuery{}  true  "信息"
// @Param     Platform  header    int                                      true  "终端ID"
// @Success 200 {object} color_card_struct.GetProductDetailData
// @Router /hcscm/third_party/v1/color_card/getProductDetail [get]
func GetProductDetail(c *gin.Context) {
	var (
		q            = &color_card_struct.GetProductDetailQuery{}
		detail       *color_card_struct.GetProductDetailData
		err          error
		svcColorCard = svc_color_card.NewColorCardService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, detail)
	}()
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	// 绑定查询参数
	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 检查成品ID是否有效
	if q.FinishProductId == 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "成品ID不能为空"))
		return
	}

	// 获取商品详情
	detail, err = svcColorCard.GetProductDetail(ctx, tx, q)
	if err != nil {
		return
	}

	return
}

// @Tags      【后台】
// @Security  ApiKeyAuth
// @Summary   获取轮播图
// @Produce   json
// @Param     body      body      carouselBanner.GetCarouselBannersQuery{}  true  "信息"
// @Param     Platform  header    int                                      true  "终端ID"
// @Success   200       {object}  carouselBanner.GetCarouselBannerDetailData{}
// @Router    /hcscm/third_party/v1/color_card/carouselBanner [get]
func GetCarouselBanner(c *gin.Context) {
	carouselBanner.GetCarouselBanner(c)

}

// GetProductDetailColorInfo 获取成品详情的颜色信息
// @Summary 获取成品详情的颜色信息
// @Description 获取单个商品的详细颜色信息
// @Tags 电子色卡分享页-成品颜色信息获取
// @Accept json
// @Produce json
// @Param id query uint64 true "商品ID"
// @Security ApiKeyAuth
// @Param     body      body     color_card_struct.GetProductDetailColorInfoQuery{}  true  "信息"
// @Param     Platform  header    int                                      true  "终端ID"
// @Success 200 {object} color_card_struct.GetProductDetailColorInfoData
// @Router /hcscm/third_party/v1/color_card/getProductDetailColorInfo [get]
func GetProductDetailColorInfo(c *gin.Context) {
	var (
		q            = &color_card_struct.GetProductDetailColorInfoQuery{}
		detail       *color_card_struct.GetProductDetailColorInfoData
		err          error
		svcColorCard = svc_color_card.NewColorCardService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}
	defer func() {
		system.BuildResponse(c, err, detail)
	}()
	// tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	// 绑定查询参数
	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	// 检查成品ID是否有效
	if q.FinishProductId == 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "成品ID不能为空"))
		return
	}

	// 获取商品颜色详情
	detail, err = svcColorCard.GetProductDetailColorInfo(ctx, q)
	if err != nil {
		return
	}

	return
}
