package third_party

import (
	common "hcscm/common/system_consts"
	"hcscm/router/color_card"
	"hcscm/router/product"
	systemRouter "hcscm/router/system"
	"hcscm/server/system"
)

func ThirdParty(routerGroup *system.RouterGroup) {
	thirdParty := routerGroup.GroupByPlatform(common.PlatformThirdParty, "third_party", system.JustAllowThirdParty)
	// ERP
	V1ThirdParty(thirdParty)
}

func V1ThirdParty(thirdParty *system.RouterGroup) {
	v1ThirdParty := thirdParty.Group("v1")
	thirdPartyAuth := v1ThirdParty.Group("", system.AuthThirdParty)
	// 登录
	{
		v1ThirdParty.POST("访客登录", "login", system.LoginByThirdParty)
		// 手机验证码登录
		thirdPartyAuth.POST("发送手机验证码", "sendVerificationCodeEmail", system.ThirdPartySendVerificationCodeEmail)
		thirdPartyAuth.POST("手机验证码注册或者登录", "phoneVerificationCodeLogin", system.ThirdPartyPhoneVerificationCodeLogin)
		thirdPartyAuth.POST("登出", "logout", system.Logout)

		thirdPartyAuth.GET("获取用户信息", "information", system.GetLoginInformation)
	}

	{
		systemRouter.InitCDN(thirdPartyAuth) // CDN
	}

	{
		color_card.InitColorCard(thirdPartyAuth)
	}
	// 通过URL搜索相似图片，需要登录后才能使用 todo: 登录鉴权出来后需要修改thirdPartyAuth,租户不可使用，客户登录后可以使用
	{
		product.ThirdPartyInitFinishProduct(thirdPartyAuth)
	}
}
