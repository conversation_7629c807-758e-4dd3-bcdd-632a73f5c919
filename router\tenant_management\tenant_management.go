package tenant_management

import (
	tenant "hcscm/server/basic_data"
	"hcscm/server/color_card"
	"hcscm/server/system"
	"hcscm/server/tenant_management"
)

func InitTenantManagement(routerGroup *system.RouterGroup) {
	tenantManagement := routerGroup.Group("tenantManagement")
	tenantManagement.POSTNeedAuth("添加租户管理", "addTenantManagement", tenant_management.AddTenantManagement)
	tenantManagement.PUTNeedAuth("禁用租户管理", "disableTenantManagement", tenant_management.DisableTenantManagement)
	tenantManagement.PUTNeedAuth("启用租户管理", "enableTenantManagement", tenant_management.EnableTenantManagement)
	tenantManagement.PUTNeedAuth("更新租户管理公司名称", "updateTenantManagementCompanyName", tenant_management.UpdateTenantManagementCompanyName)
	tenantManagement.GET("获取租户管理列表", "getTenantManagementList", tenant_management.GetTenantManagementList)
	tenantManagement.GET("获取租户管理详情", "getTenantManagementDetailInfo", tenant_management.GetTenantManagementDetailInfo)
	tenantManagement.GET("获取微信用户线索", "getWechatUserPuleList", tenant_management.GetWechatUserPuleList)
	tenantManagement.PUTNeedAuth("账套清洗", "cleanTenantManagement", tenant_management.CleanTenantManagements)
	tenantManagement.GET("同步主库数据结构", "syncMainDB", system.SyncMainDB)

	{
		enum := tenantManagement.Group("enum")
		enum.GET("获取租户管理状态枚举", "getTenantManagementStatusEnum", tenant_management.GetTenantManagementStatusEnum)
	}
	{
		tenantPackage := tenantManagement.Group("tenantPackage")
		tenantPackage.POSTNeedAuth("添加租户套餐", "addTenantPackage", tenant_management.AddTenantPackage)
		tenantPackage.PUTNeedAuth("更新租户套餐", "updateTenantPackage", tenant_management.UpdateTenantPackage)
		tenantPackage.GET("获取租户套餐列表", "getTenantPackageList", tenant_management.GetTenantPackageList)
		tenantPackage.GET("获取租户套餐", "getTenantPackage", tenant_management.GetTenantPackage)
		tenantPackage.PUTNeedAuth("启用租户套餐", "updateStatusEnable", tenant_management.UpdateStatusEnable)
		tenantPackage.PUTNeedAuth("禁用租户套餐", "updateStatusDisable", tenant_management.UpdateStatusDisable)
		tenantPackage.POSTNeedAuth("同步套餐内容到账套", "syncTenantPackageToUser", tenant_management.SyncTenantPackageToUser)
	}

	{
		tenantManagement.GET("手动执行即将过期租户推送", "getExpiringSoonTenantManagementPushQueue", tenant_management.GetExpiringSoonTenantManagementPushQueue)
	}

	{
		tenantReceiveAddr := tenantManagement.Group("tenantReceiveAddr")
		tenantReceiveAddr.POSTNeedAuth("新增", "", tenant.AddTenantReceiveAddr)
		tenantReceiveAddr.PUTNeedAuth("更新", "", tenant.UpdateTenantReceiveAddr)
		tenantReceiveAddr.PUTNeedAuth("更新状态", "status", tenant.UpdateTenantReceiveAddrStatus)
		tenantReceiveAddr.DELETENeedAuth("删除", "", tenant.DeleteTenantReceiveAddr)
		tenantReceiveAddr.GET("获取", "", tenant.GetTenantReceiveAddr)
		tenantReceiveAddr.GET("获取列表", "list", tenant.GetTenantReceiveAddrList)
		tenantReceiveAddr.GET("获取下拉列表", "list_enum", tenant.GetTenantReceiveAddrDropdownList)
	}

	// 码单
	{
		codeListOrcManagement := tenantManagement.Group("codeListOrcManagement")
		codeListOrcManagement.PUTNeedAuth("更新码单管理账套名称", "updateCodeListOrcManagementCodeListName", tenant_management.UpdateCodeListOrcManagementCodeListName)
		codeListOrcManagement.PUTNeedAuth("启用码单管理状态", "updateCodeListOrcStatusEnable", tenant_management.UpdateCodeListOrcStatusEnable)
		codeListOrcManagement.PUTNeedAuth("禁用码单管理状态", "updateCodeListOrcStatusDisable", tenant_management.UpdateCodeListOrcStatusDisable)
		codeListOrcManagement.GET("获取码单管理列表", "getCodeListOrcManagementList", tenant_management.GetCodeListOrcManagementList)
		// 充值码单
		codeListOrcManagement.POSTNeedAuth("充值码单", "recharge", tenant_management.Recharge)
		// 充值记录
		codeListOrcManagement.GET("获取充值记录列表", "getRechargeHistoryList", tenant_management.GetRechargeHistoryList)
	}

	// 电子色卡
	{
		// 电子色卡
		electronicColorCard := tenantManagement.Group("electronicColorCard")
		electronicColorCard.PUTNeedAuth("启用电子色卡状态", "updateElectronicColorCardStatusEnable", tenant_management.UpdateStatusEnableEleColorCard)
		electronicColorCard.PUTNeedAuth("禁用电子色卡状态", "updateElectronicColorCardStatusDisable", tenant_management.UpdateStatusDisableEleColorCard)
		electronicColorCard.GET("获取电子色卡列表", "getElectronicColorCardList", tenant_management.GetEleColorCardManagementList) // 获取接口和码单ocr列表接口一样

		// 电子色卡充值
		electronicColorCard.POSTNeedAuth("充值电子色卡", "rechargeEleColorCard", tenant_management.EleColorCardServerRecharge)
		// 电子色卡充值记录
		electronicColorCard.GET("获取电子色卡充值记录列表", "getEleColorCardRechargeHistoryList", tenant_management.GetEleColorCardHistoryList)
		// 生成加密的账套id给前端
		electronicColorCard.GET("生成加密id", "generateAccountSetId", color_card.GenerateAccountSetId)

		electronicColorCard.PUTNeedAuth("启用搜索图片状态", "enable_search_image", tenant_management.UpdateStatusEnableSearchImage)
		electronicColorCard.PUTNeedAuth("禁用搜索图片状态", "disable_search_image", tenant_management.UpdateStatusDisableSearchImage)
	}

	{
		qywx := tenantManagement.Group("qywx_search")
		qywx.GET("企微配置页面获取未绑定代开发应用的租户列表", "", tenant_management.QYWXSearchUnBindList)
	}
}

func MPInitTenantManagement(routerGroup *system.RouterGroup) {
	tenantManagement := routerGroup.Group("tenantManagement")
	tenantManagement.POST("新增账套用户", "addSubTenant", tenant_management.AddSubTenant)
	tenantManagement.PUT("更新租户管理公司名称", "updateTenantManagementCompanyName", tenant_management.UpdateTenantManagementCompanyName)
	tenantManagement.PUT("更新账套用户", "updateSubTenant", tenant_management.UpdateSubTenant)
	tenantManagement.PUT("更新账套用户密码", "updateTenantLoginPassword", tenant_management.UpdateTenantLoginPassword)
	tenantManagement.GET("获取小程序租户管理详情", "getTenantManagementDetailInfo", tenant_management.GetMPTenantManagementDetailInfo)
	tenantManagement.GET("获取用户所有的关联租户", "getTenantManagementList", tenant_management.GetMPTenantManagementList)
	tenantManagement.PUT("用户反馈", "feedback", tenant_management.TenantManagementFeedback)
}

func InitTemporaryRouter(routerGroup *system.RouterGroup) {
	tenantManagement := routerGroup.Group("tenantManagement")
	tenantManagement.GET("清洗租户关系", "rel", tenant_management.WashTenantManagementRel)
	tenantManagement.GET("手动更新租户表结构", "sql_executor", tenant_management.SqlExecutor)
	// todo
	// 同步主服务
	// tenantManagement.POST("同步数据至主服务", "syncData", tenant_management.SyncData)
	// 填充域名字段
	tenantManagement.POST("填充域名字段", "fillDomain", tenant_management.FillDomain)
	// 兼容旧账套电子色卡服务
	tenantManagement.POST("兼容旧账套电子色卡服务", "compatibleOldTenantManagement", tenant_management.CompatibleOldTenantManagement)
	// tenantManagement.POST("接收同步主服务", "receiveSyncData", tenant_management.ReceiveSyncData)
}
