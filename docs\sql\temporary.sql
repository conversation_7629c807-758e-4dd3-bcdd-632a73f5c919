ALTER TABLE `fpm_out_order`
    ADD COLUMN `src_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '源单id',
    ADD COLUMN `src_order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '源单单号';
ALTER TABLE `fpm_out_order_item_fc`
    ADD COLUMN `finish_product_width_unit_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '成品幅宽单位id(字典)',
    ADD COLUMN `finish_product_gram_weight_unit_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '成品克重单位id(字典)';

-- 数量可以填负数,去除只能为正数的限制
ALTER TABLE `ocr_record`
    MODIFY COLUMN `weight` bigint(20) NOT NULL DEFAULT 0 COMMENT '重量 除以10000',
    MODIFY COLUMN `weight_error` bigint(20) NOT NULL DEFAULT 0 COMMENT '空差重量 除以10000',
    MODIFY COLUMN `settle_weight` bigint(20) NOT NULL DEFAULT 0 COMMENT '结算重量 除以10000';
-- 销售出仓单成品
ALTER TABLE `fpm_out_order_item`
    ADD COLUMN `settle_length` int(11) unsigned DEFAULT '0' NOT NULL COMMENT '结算长度(米)，乘100存';
-- 销售出仓单细码信息
ALTER TABLE `fpm_out_order_item_fc`
    ADD COLUMN `settle_length` int(11) unsigned DEFAULT '0' NOT NULL COMMENT '结算长度(米)，乘100存',
    ADD COLUMN `auxiliary_unit_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '辅助单位id';
-- 销售出仓单
ALTER TABLE `fpm_sale_out_order`
    ADD COLUMN `texture_url` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '凭证图片URL';
ALTER TABLE `fpm_sale_out_order`
ADD COLUMN `auxiliary_unit_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '辅助单位id';
-- 销售退货进仓单成品信息
ALTER TABLE `fpm_sale_return_in_order_item`
    ADD COLUMN `auxiliary_unit_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '辅助单位id',
    ADD COLUMN `return_price`   int(11) unsigned DEFAULT 0 NOT NULL COMMENT '退货单价',
    ADD COLUMN `length_cut_return_price`   int(11) unsigned DEFAULT 0 NOT NULL COMMENT '剪板退货单价',
    ADD COLUMN `settle_price`   int(11) unsigned DEFAULT 0 NOT NULL COMMENT '成品销售退货进仓单成品金额';

-- 调货单审核流程需求
ALTER TABLE `fpm_sale_out_order`
    ADD COLUMN `src_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '源单id',
    ADD COLUMN `src_order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '源单单号';
ALTER TABLE `fpm_sale_return_in_order`
    ADD COLUMN `src_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '源单id',
    ADD COLUMN `src_order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '源单单号';

-- 去除用户表职责&职责名称
ALTER TABLE `user`
DROP
COLUMN `duty`,
    DROP
COLUMN `duty_name`;

-- 增加租户管理表请求域名字段和请求域名前缀的字段
ALTER TABLE `tenant_management`
    ADD COLUMN `request_domain` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '请求域名',
    ADD COLUMN `request_domain_prefix` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '请求域名前缀';

ALTER TABLE fpm_quality_checkout_report
    ADD COLUMN hand_feel_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '手感名称',
    ADD COLUMN hair_effect_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '抓毛效果名称',
    ADD COLUMN scouring_effect_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '磨毛效果名称',
    ADD COLUMN hair_head_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '布面毛头名称',
    ADD COLUMN fabric_tilt_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '布纹斜度名称',
    ADD COLUMN color_light_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '对色光源名称',
    ADD COLUMN batch_difference_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '疋差名称',
    ADD COLUMN positive_and_negative_name VARCHAR(255) NOT NULL DEFAULT '' COMMENT '阴阳名称',
    ADD COLUMN quality_check_report_ids TEXT NOT NULL DEFAULT '' COMMENT '质检报表Ids';

ALTER TABLE fpm_quality_checkout_report
    ADD COLUMN total_sample_count INT(11) NOT NULL DEFAULT 0 COMMENT '总抽检匹数',
    ADD COLUMN total_sample_weight INT(11) NOT NULL DEFAULT 0 COMMENT '总抽检数量';

ALTER TABLE fpm_quality_checkout_report
    DROP COLUMN sample_weight;

ALTER TABLE fpm_quality_checkout_report
    ADD COLUMN remark VARCHAR(255) NOT NULL DEFAULT '' COMMENT '备注';

-- 新增字典
INSERT INTO `dictionary` (`id`, `name`, `type`, `desc`, `status`)
VALUES (10014, '手感', 'hand_feel', '手感', 1),
       (10015, '抓毛效果', 'hair_effect', '抓毛效果', 1),
       (10016, '磨毛效果', 'scouring_effect', '磨毛效果', 1),
       (10017, '布面毛头', 'hair_head', '布面毛头', 1),
       (10018, '布纹斜度', 'fabric_tilt', '布纹斜度', 1),
       (10019, '对色光源', 'color_light', '对色光源', 1),
       (10020, '疋差', 'batch_difference', '疋差', 1),
       (10021, '阴阳', 'positive_and_negative', '阴阳', 1),
       (10022, '缩水', 'shrinkage_rate_name', '缩水率', 1);
ALTER TABLE `fpm_quality_checkout_report`
    CHANGE COLUMN `shrinkage_rate` `shrinkage_rate_id` BIGINT(20) unsigned NOT NULL DEFAULT 0 COMMENT '缩水率id';
ALTER TABLE `fpm_quality_checkout_report`
    ADD COLUMN `shrinkage_rate_name` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '缩水率';
ALTER TABLE `fpm_quality_checkout_report`
    ADD COLUMN `smell` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '是否有异味';
ALTER TABLE `fpm_quality_checkout_report`
    CHANGE fabric_pattern_slope fabricTilt_id BIGINT(20);

ALTER TABLE `fpm_quality_checkout_report`
DROP
COLUMN water_absorption;
ALTER TABLE `fpm_quality_checkout_report`
    ADD COLUMN water_absorption VARCHAR(255) NOT NULL DEFAULT '' COMMENT '吸水性能';
ALTER TABLE `fpm_quality_checkout_report`
DROP
COLUMN color_fastness;

ALTER TABLE `fpm_quality_checkout_report`
    ADD COLUMN color_fastness VARCHAR(255) NOT NULL DEFAULT '' COMMENT '色牢度';
ALTER TABLE `fpm_arrange_order`
    ADD COLUMN `tax_rate` int(11) NULL DEFAULT 0 COMMENT '税率';
ALTER TABLE `fpm_quality_check`
    ADD COLUMN `volume_number` int(11) NULL DEFAULT 0 COMMENT '匹号(卷号)';

ALTER TABLE `product_adjust_order_item`
    ADD COLUMN `adjust_product_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '调整成品id';
ALTER TABLE `product_adjust_order_weight_item`
    ADD COLUMN `adjust_product_color_id` bigint(20) unsigned NOT NULL default '0' COMMENT '调整颜色id(父级获取)';
ALTER TABLE `product_adjust_order_weight_item`
    ADD COLUMN `adjust_product_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '调整成品id(父级获取)';
ALTER TABLE `fpm_in_order_item_fc`
    ADD COLUMN `is_update_stock` tinyint (1) DEFAULT 0 NOT NULL COMMENT '是否是更新库存（新增库存消审时需要把新增的库存删除）';

ALTER TABLE `fpm_quality_checkout_report`
    ADD COLUMN `attachments_url` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '附件URL';

ALTER TABLE fpm_quality_check
    ADD COLUMN is_generate_report tinyint(2) NOT NULL DEFAULT 0 COMMENT '是否生成质检报告：1为是，0为否';

ALTER TABLE `fpm_in_order_item_fc`
    ADD COLUMN `return_stock_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '退货库存成品id';

-- 应收单详情表新增来源单ID
ALTER TABLE `should_collect_order_detail`
    ADD COLUMN `src_order_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '来源单ID';

ALTER TABLE `should_collect_order_detail`
    MODIFY COLUMN `src_detail_id` bigint(20) UNSIGNED DEFAULT 0 NOT NULL COMMENT '来源单详情ID';

UPDATE `should_collect_order_detail`
    JOIN fpm_arrange_order_item on fpm_arrange_order_item.id=should_collect_order_detail.src_detail_id
    SET should_collect_order_detail.src_order_id= fpm_arrange_order_item.parent_id;

-- 增加finish_product_purchase_order_item表的辅助单位id
ALTER TABLE finish_product_purchase_order_item
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE purchase_product_return_order_item
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE sale_product_order_detail
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE fpm_arrange_order_item
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE `fpm_in_order_item_fc`
    ADD COLUMN `return_stock_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '退货库存成品id';


ALTER TABLE `biz_unit_factory_logistics`
    ADD COLUMN `logistics_area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流区域',
    ADD COLUMN `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司',
    ADD COLUMN `process_factory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加工厂名称';

ALTER TABLE `should_collect_order_detail`
    ADD COLUMN `product_craft` varchar(255) NOT NULL DEFAULT '' COMMENT '成品工艺',
    ADD COLUMN `product_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '成品备注',
    ADD COLUMN `product_ingredient` varchar(255) NOT NULL DEFAULT '' COMMENT '成品成分',
    ADD COLUMN `product_level_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '成品等级ID';

-- 应收单详情表新增来源单ID
ALTER TABLE `should_collect_order_detail`
    ADD COLUMN `src_order_id` bigint(20) unsigned NOT NULL COMMENT '来源单ID';

ALTER TABLE `should_collect_order_detail`
    MODIFY COLUMN `src_detail_id` bigint(20) UNSIGNED NOT NULL COMMENT '来源单详情ID';

ALTER TABLE `sale_shipment_type`
    ADD COLUMN `order_type` TEXT  DEFAULT '' NOT NULL COMMENT '订单类型';
-- 销售退货进仓单详情统一到进仓单详情
ALTER TABLE fpm_in_order_item
    ADD COLUMN `warehouse_in_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '进仓类型',
    ADD COLUMN `in_weight` int (11) DEFAULT 0 NOT NULL COMMENT '进仓数量，乘100存',
    ADD COLUMN `return_price` int (11) DEFAULT 0 NOT NULL COMMENT '退货单价',
    ADD COLUMN `length_cut_return_price` int (11) DEFAULT 0 NOT NULL COMMENT '剪板退货单价',
    ADD COLUMN `settle_price` int (11) DEFAULT 0 NOT NULL COMMENT '成品销售退货进仓单成品金额';

-- 成品加工进仓单详情统一到进仓单详情
ALTER TABLE fpm_in_order_item
    ADD COLUMN `quote_order_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '引用数据单id（染整）',
    ADD COLUMN `quote_order_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '单据类型',
    ADD COLUMN `dyeing_situ_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '染整进度情况id',
    ADD COLUMN `customer_account_num`  VARCHAR(255) DEFAULT '' NOT NULL COMMENT '客户款号',
    ADD COLUMN `contract_number`  VARCHAR(255) DEFAULT '' NOT NULL COMMENT '合同号',
    ADD COLUMN `dye_delivery_order_id` BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '染厂送货单号id',
    ADD COLUMN `dye_delivery_order_no`  VARCHAR(255) DEFAULT '' NOT NULL COMMENT '染厂送货单号',
    ADD COLUMN `use_gf_roll` int (11) DEFAULT 0 NOT NULL COMMENT '用坯匹数，乘100存',
    ADD COLUMN `use_gf_weight` bigint (20) DEFAULT 0 NOT NULL COMMENT '用坯数量，乘10000存',
    ADD COLUMN `dye_delivery_order_weight` bigint (20) DEFAULT 0 NOT NULL COMMENT '染厂单据数量，乘100存';

UPDATE `fpm_in_order_item`
    JOIN fpm_in_order on fpm_in_order.id=fpm_in_order_item.parent_id
    SET fpm_in_order_item.warehouse_in_type= fpm_in_order.in_order_type;

-- 成品加工出仓单详情统一到出仓单详情
ALTER TABLE `fpm_out_order_item`
    ADD COLUMN `warehouse_out_type` tinyint(2) NOT NULL DEFAULT 0 COMMENT '出仓类型',
    ADD COLUMN `return_roll` int (11) DEFAULT 0 NOT NULL COMMENT '退货匹数，乘100存',
    ADD COLUMN `return_weight` bigint (20) DEFAULT 0 NOT NULL COMMENT '退货数量，乘10000存',
    ADD COLUMN `return_length` bigint (20) DEFAULT 0 NOT NULL COMMENT '退货长度，乘10000存',
    ADD COLUMN `dye_roll` int (11) DEFAULT 0 NOT NULL COMMENT '已排染匹数，乘100存',
    ADD COLUMN `dye_weight` bigint (20) DEFAULT 0 NOT NULL COMMENT '已排染数量，乘10000存';

UPDATE `fpm_out_order_item`
    JOIN fpm_out_order on fpm_out_order.id=fpm_out_order_item.parent_id
    SET fpm_out_order_item.warehouse_out_type= fpm_out_order.out_order_type;

-- 坯布调整单补充仓管员id
ALTER TABLE `gfm_allocate_order`
    ADD COLUMN `store_keeper_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '仓管员id';

-- 坯布库存记录补充凭证单号
ALTER TABLE `gfm_stock_record`
    ADD COLUMN `voucher_number`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '凭证单号';

ALTER TABLE `gfm_stock_record`
    ADD COLUMN `grey_fabric_cost` int(11)  DEFAULT 0 NOT NULL COMMENT '坯布成本';

--
ALTER TABLE `gfm_stock_adjust_order_item_fine_code`
    ADD COLUMN `grey_fabric_width_unit_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '坯布幅宽单位id(字典)',
    ADD COLUMN `grey_fabric_gram_weight_unit_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '坯布克重单位id(字典)';
--
ALTER TABLE `gfm_stock_adjust_order_item`
DROP COLUMN `grey_fabric_width`,
     DROP COLUMN `grey_fabric_gram_weight`;

-- 坯布盘点单删除仓管员名称
ALTER TABLE `gfm_stock_check_order`
DROP COLUMN `store_keeper_name`;
-- 增加finish_product_purchase_order_item表的辅助单位id
ALTER TABLE finish_product_purchase_order_item
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE purchase_product_return_order_item
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE sale_product_order_detail
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE fpm_arrange_order_item
    ADD COLUMN auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id';

ALTER TABLE `fpm_in_order`
    MODIFY COLUMN `total_length` bigint(20) NOT NULL DEFAULT 0 COMMENT '长度总计',
    MODIFY COLUMN `total_price` bigint(20) NOT NULL DEFAULT 0 COMMENT '单据金额';

-- 坯布盘点单添加单据类型
ALTER TABLE `gfm_stock_check_order`
    ADD COLUMN `order_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '单据类型';
ALTER TABLE `gfm_stock_check_order_item`
    ADD COLUMN `order_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '单据类型';

ALTER TABLE `dyeing_and_finishing_notice_order_item_child`
    ADD COLUMN `warehouse_sum_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '库存汇总id';

ALTER TABLE `should_collect_order_detail`
    ADD COLUMN `product_craft` varchar(255) NOT NULL DEFAULT '' COMMENT '成品工艺',
    ADD COLUMN `product_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '成品备注',
    ADD COLUMN `product_ingredient` varchar(255) NOT NULL DEFAULT '' COMMENT '成品成分',
    ADD COLUMN `product_level_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '成品等级ID';

-- 清洗顺序-》接口单据清洗同步-》执行sql删除多余库存和单据-》接口库存清洗—》单据时间处理sql
ALTER TABLE `fpm_in_order`
  ADD COLUMN  `sale_allocate_out_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '成品销售调拨出仓单id',
  ADD COLUMN  `sale_allocate_out_order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品销售调拨出仓单单号',
  ADD COLUMN  `warehouse_out_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调出仓库id',
  ADD COLUMN  `sale_user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售员id',
  ADD COLUMN  `sale_follower_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售跟单员id',
  ADD COLUMN  `driver_id`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '司机id，逗号分割（关联user.id）',
  ADD COLUMN  `logistics_company_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '物流公司id',
  ADD COLUMN  `return_remark`text NOT NULL DEFAULT '' COMMENT '退货备注';

ALTER TABLE `fpm_out_order`
   ADD COLUMN  `in_warehouse_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调入仓库id',
   ADD COLUMN  `driver_id`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '司机id，逗号分割（关联user.id）',
   ADD COLUMN  `logistics_company_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '物流公司id',
   ADD COLUMN  `warehouse_in_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调入仓库id',
   ADD COLUMN  `sale_user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售员id',
   ADD COLUMN  `sale_follower_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售跟单员id',
   ADD COLUMN  `logistics_company_area` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '物流公司区域',
   ADD COLUMN  `sale_allo_in_order_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售调拨进仓单id',
  ADD COLUMN  `process_factory_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '加工厂id',
  ADD COLUMN  `receive_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '收货人',
  ADD COLUMN  `receive_addr` text DEFAULT '' NOT NULL COMMENT '收货地址',
  ADD COLUMN  `receive_phone`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货电话',
  ADD COLUMN  `receive_tag`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货标签',
  ADD COLUMN  `arrange_user_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '配布员id（关联user.id）',
  ADD COLUMN  `logistics_company_name` VARCHAR(255) NOT NULL DEFAULT 0 COMMENT '物流公司name',
  ADD COLUMN  `internal_remark` text NOT NULL DEFAULT '' COMMENT '内部备注',
  ADD COLUMN  `sale_remark`text NOT NULL DEFAULT '' COMMENT '销售备注';

ALTER TABLE `product_check_order_weight_item`
ADD INDEX `warehouse_id`(`warehouse_id`) USING BTREE,
ADD INDEX `product_id`(`product_id`) USING BTREE,
ADD INDEX `product_color_id`(`product_color_id`) USING BTREE,
ADD INDEX `dyelot_number`(`dyelot_number`) USING BTREE,
ADD INDEX `customer_id`(`customer_id`) USING BTREE,
ADD INDEX `check_time`(`check_time`) USING BTREE;

ALTER TABLE `fpm_in_order_item_fc`
ADD INDEX `product_id`(`product_id`) USING BTREE,
ADD INDEX `product_color_id`(`product_color_id`) USING BTREE,
ADD INDEX `dye_factory_dyelot_number`(`dye_factory_dyelot_number`) USING BTREE,
ADD INDEX `order_time`(`order_time`) USING BTREE,
ADD INDEX `warehouse_id`(`warehouse_id`) USING BTREE;

ALTER TABLE `fpm_out_order_item_fc`
ADD INDEX `dye_factory_dyelot_number`(`dye_factory_dyelot_number`) USING BTREE,
ADD INDEX `order_time`(`order_time`) USING BTREE,
ADD INDEX `warehouse_id`(`warehouse_id`) USING BTREE;

ALTER TABLE `fpm_in_order_item`
ADD INDEX `product_id`(`product_id`) USING BTREE,
ADD INDEX `product_level_id`(`product_level_id`) USING BTREE,
ADD INDEX `product_color_id`(`product_color_id`) USING BTREE,
ADD INDEX `dye_factory_dyelot_number`(`dye_factory_dyelot_number`) USING BTREE,
ADD INDEX `product_remark`(`product_remark`) USING BTREE;

ALTER TABLE `fpm_out_order_item`
ADD INDEX `product_id`(`product_id`) USING BTREE,
ADD INDEX `product_level_id`(`product_level_id`) USING BTREE,
ADD INDEX `product_color_id`(`product_color_id`) USING BTREE,
ADD INDEX `dye_factory_dyelot_number`(`dye_factory_dyelot_number`) USING BTREE,
ADD INDEX `product_remark`(`product_remark`) USING BTREE;

ALTER TABLE `fpm_in_order_item_fc`
    ADD COLUMN `return_stock_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '退货库存成品id';


ALTER TABLE `biz_unit_factory_logistics`
    ADD COLUMN `logistics_area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流区域',
    ADD COLUMN `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司',
    ADD COLUMN `process_factory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加工厂名称';


ALTER TABLE `sale_system`
    ADD COLUMN `spot_stocks` BIGINT(20)  DEFAULT 0 NOT NULL COMMENT '现货库存',
    ADD COLUMN `default_last_sale_price` tinyint(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '默认勾选上次销售价格 0禁止 1启用';


ALTER TABLE `physical_warehouse`
    ADD COLUMN `large_cargo_warehouse_status` tinyint(1) UNSIGNED DEFAULT 1 NOT NULL COMMENT '是否启用大货仓库 1启用 2禁用 3未激活',
    ADD COLUMN `sub_scribe_warehouse_status` tinyint(1) UNSIGNED DEFAULT 1 NOT NULL COMMENT '是否启用预约仓库 1启用 2禁用 3未激活',
    ADD COLUMN `warn_warehouse_status` tinyint(1) UNSIGNED DEFAULT 1 NOT NULL COMMENT '是否启用预警仓库 1启用 2禁用 3未激活';

ALTER TABLE `sale_product_order`
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板',
    ADD COLUMN `pick_up_goods_in_order` tinyint(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '齐单提货 0禁止 1启用 ',
    ADD COLUMN `same_color_same_dye_lot` tinyint(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '同色同缸 0禁止 1启用',
    ADD COLUMN `process_factory` VARCHAR(255) NOT NULL DEFAULT '' NOT NULL COMMENT '加工厂',
    ADD COLUMN `logistics_company` VARCHAR(255) NOT NULL DEFAULT '' NOT NULL COMMENT '物流公司',
    ADD COLUMN `sale_shipment_name` VARCHAR(255) NOT NULL DEFAULT '' NOT NULL COMMENT '发货类型名称',
    ADD COLUMN `receipt_address_detail` VARCHAR(255) NOT NULL DEFAULT '' NOT NULL COMMENT '详细收货地址';

ALTER TABLE `biz_unit_factory_logistics`
    ADD COLUMN `logistics_area` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流区域',
    ADD COLUMN `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司',
    ADD COLUMN `process_factory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加工厂名称';

ALTER TABLE `biz_unit`
    ADD COLUMN `location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '省市区';

ALTER TABLE `sale_system`
    ADD COLUMN `low_sale_price_limit` int(11) UNSIGNED DEFAULT 0 NOT NULL COMMENT '最低销售单价极限',
    ADD COLUMN `high_sale_price_limit` int(11) UNSIGNED DEFAULT 0 NOT NULL COMMENT '最高销售单价极限';

ALTER TABLE `sale_shipment_type`
    ADD COLUMN `order_type` TEXT  DEFAULT '' NOT NULL COMMENT '订单类型';

ALTER TABLE `fpm_arrange_order`
    ADD COLUMN `process_factory` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '加工厂',
    ADD COLUMN `logistics_company` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci NOT NULL DEFAULT '' COMMENT '物流公司';

ALTER TABLE `sale_product_order`
    DROP COLUMN `order_progress`;

UPDATE biz_unit_factory_logistics b SET b.name = b.process_factory;

ALTER TABLE `biz_unit_factory_logistics`
    DROP COLUMN `process_factory`;

-- -- -- 系统全局补充订单类 --
ALTER TABLE `finish_product_purchase_order` -- 成品采购单--
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `fpm_in_order` -- 成品采购进仓单，成品其他进仓单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `purchase_product_return_order` -- 成品采购退货单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `fpm_out_order` -- 成品采购退货出仓单，成品其他出仓单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `fpm_sale_out_order` -- 成品销售出仓单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `fpm_sale_return_in_order` -- 成品销售退货进仓单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `should_collect_order` -- 销售送货单,销售退货单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `fpm_arrange_order` -- 现货配布单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `sale_product_order` -- 成品销售单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `sale_transfer_order` -- 销售调货单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `payable` -- 应付单 --
    ADD COLUMN `sale_mode` tinyint(3) UNSIGNED DEFAULT 0 NOT NULL COMMENT '销售订单类型 1大货 2剪板 3客订大货 4客订剪板';
ALTER TABLE `fpm_quality_checkout_report`
    MODIFY COLUMN `strength_horizontal` varchar(255) NOT NULL DEFAULT '' COMMENT '强力横' AFTER `order_no`,
    MODIFY COLUMN `strength_vertical` varchar(255) NOT NULL DEFAULT '' COMMENT '强力直' AFTER `strength_horizontal`;

ALTER TABLE `fpm_in_order`
    MODIFY COLUMN `total_length` bigint(20) NOT NULL DEFAULT 0 COMMENT '长度总计',
    MODIFY COLUMN `total_price` bigint(20) NOT NULL DEFAULT 0 COMMENT '单据金额';

ALTER TABLE `employee`
    ADD COLUMN `qywx_user_name` VARCHAR(255)  DEFAULT '' NOT NULL COMMENT '企微员工名称';
ALTER TABLE `employee`
    ADD COLUMN `qywx_user_id` VARCHAR(255)  DEFAULT '' NOT NULL COMMENT '企微员工id';

-- 坯布盘点单添加单据类型
ALTER TABLE `gfm_stock_check_order`
    ADD COLUMN `order_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '单据类型';
ALTER TABLE `gfm_stock_check_order_item`
    ADD COLUMN `order_type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '单据类型';

ALTER TABLE `dyeing_and_finishing_notice_order_item_child`
    ADD COLUMN `warehouse_sum_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '库存汇总id';

-- 清洗顺序-》接口单据清洗同步-》执行sql删除多余库存和单据-》接口库存清洗—》单据时间处理sql
ALTER TABLE `fpm_in_order`
  ADD COLUMN  `sale_allocate_out_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '成品销售调拨出仓单id',
  ADD COLUMN  `sale_allocate_out_order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品销售调拨出仓单单号',
  ADD COLUMN  `warehouse_out_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调出仓库id',
  ADD COLUMN  `sale_user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售员id',
  ADD COLUMN  `sale_follower_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售跟单员id',
  ADD COLUMN  `driver_id`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '司机id，逗号分割（关联user.id）',
  ADD COLUMN  `logistics_company_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '物流公司id',
  ADD COLUMN  `return_remark`text NOT NULL DEFAULT '' COMMENT '退货备注';

ALTER TABLE `fpm_out_order`
   ADD COLUMN  `in_warehouse_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调入仓库id',
   ADD COLUMN  `driver_id`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '司机id，逗号分割（关联user.id）',
   ADD COLUMN  `logistics_company_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '物流公司id',
   ADD COLUMN  `warehouse_in_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调入仓库id',
   ADD COLUMN  `sale_user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售员id',
   ADD COLUMN  `sale_follower_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售跟单员id',
   ADD COLUMN  `logistics_company_area` VARCHAR(255) NOT NULL DEFAULT '' COMMENT '物流公司区域',
   ADD COLUMN  `sale_allo_in_order_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售调拨进仓单id',
  ADD COLUMN  `process_factory_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '加工厂id',
  ADD COLUMN  `receive_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '收货人',
  ADD COLUMN  `receive_addr` text DEFAULT '' NOT NULL COMMENT '收货地址',
  ADD COLUMN  `receive_phone`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货电话',
  ADD COLUMN  `receive_tag`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货标签',
  ADD COLUMN  `arrange_user_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '配布员id（关联user.id）',
  ADD COLUMN  `logistics_company_name` VARCHAR(255) NOT NULL DEFAULT 0 COMMENT '物流公司name',
  ADD COLUMN  `internal_remark` text NOT NULL DEFAULT '' COMMENT '内部备注',
  ADD COLUMN  `sale_remark`text NOT NULL DEFAULT '' COMMENT '销售备注';

UPDATE `should_collect_order` SET `sale_mode` = 1 WHERE `sale_mode` = 0;

ALTER TABLE `raw_material_sale_order_item` -- 原料销售单详情 --
    ADD COLUMN `sale_plan_order_item_id` bigint(20) DEFAULT 0 NOT NULL COMMENT '成品销售计划单子项信息id',
    ADD COLUMN `sale_plan_order_item_no` varchar(255) DEFAULT '' NOT NULL COMMENT '成品销售计划单子项单号';


ALTER TABLE `raw_material_sale_order_item` -- 原料销售单详情 --
    ADD COLUMN `sale_plan_order_item_id` bigint(20) DEFAULT 0 NOT NULL COMMENT '成品销售计划单子项信息id',
    ADD COLUMN `sale_plan_order_item_no` varchar(255) DEFAULT '' NOT NULL COMMENT '成品销售计划单子项单号';

ALTER TABLE `fpm_in_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE `fpm_out_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE `product_adjust_order` -- 成品调整单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `product_check_order` -- 成品盘点单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `shortage_product_order` -- 成品欠货单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_product_order` -- 成品销售单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_product_plan_order` -- 成品销售计划单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_product_plan_change_order` -- 销售计划变更单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `pmc_grey_plan_order` -- pmc布料计划单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_change_order` -- 生产变更单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_notify_order` -- 生产通知单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_plan_order` -- 生产计划单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_plan_order` -- 生产计划单 --
    CHANGE COLUMN `status` `audit_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';

ALTER TABLE `production_shortage_order`
    CHANGE COLUMN `status` `audit_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';

ALTER TABLE `production_change_order`
    CHANGE COLUMN `status` `audit_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';

ALTER TABLE `production_shortage_order` -- 生产欠重单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `actually_collect_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE `advance_collect_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE `should_collect_order` -- 应收单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_transfer_order_weight` -- 销售调货单细码 --
    ADD COLUMN `stock_id`        bigint(20)          NOT NULL DEFAULT '0' COMMENT '库存成品id(退货判断是否退回原库存使用)',
    ADD COLUMN `sum_stock_id`        bigint(20)          NOT NULL DEFAULT '0' COMMENT '汇总库存id(退货判断是否退回原库存使用)';

-- 坯布调拨单
ALTER TABLE `gfm_allocate_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_allocate_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_allocate_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布扣款出货单
ALTER TABLE `gfm_deduction_delivery_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_deduction_delivery_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_deduction_delivery_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布其他出货单
ALTER TABLE `gfm_other_delivery_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_other_delivery_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_other_delivery_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布其他收货单
ALTER TABLE `gfm_other_receive_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_other_receive_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_other_receive_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布生产收货单
ALTER TABLE `gfm_produce_receive_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_produce_receive_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_produce_receive_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布生产退货单
ALTER TABLE `gfm_produce_return_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_produce_return_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_produce_return_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布采购收货单
ALTER TABLE `gfm_purchase_receive`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_purchase_receive`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_purchase_receive`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布采购退货单
ALTER TABLE `gfm_purchase_return`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_purchase_return`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_purchase_return`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布销售出货单
ALTER TABLE `gfm_sale_delivery_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_sale_delivery_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_sale_delivery_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布销售退货单
ALTER TABLE `gfm_sale_return_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_sale_return_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_sale_return_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布库存调整单
ALTER TABLE `gfm_stock_adjust_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_stock_adjust_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_stock_adjust_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布库存盘点单
ALTER TABLE `gfm_stock_check_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `gfm_stock_check_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `gfm_stock_check_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 坯布采购单
ALTER TABLE `purchase_grey_fabric`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `purchase_grey_fabric`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `purchase_grey_fabric`
    CHANGE COLUMN `audite_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 成品采购退货单
ALTER TABLE `purchase_product_return_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `purchase_product_return_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `purchase_product_return_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 成品采购单
ALTER TABLE `finish_product_purchase_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `finish_product_purchase_order`
    CHANGE COLUMN `auditer_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `finish_product_purchase_order`
    CHANGE COLUMN `auditer_name` `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称';
ALTER TABLE `finish_product_purchase_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';
ALTER TABLE `finish_product_purchase_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

-- 原料采购单
ALTER TABLE `raw_material_purchase_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_purchase_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_purchase_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料采购变更单
ALTER TABLE `raw_material_purchase_change_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_purchase_change_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_purchase_change_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料采购收货单
ALTER TABLE `raw_material_purchase_receive_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_purchase_receive_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_purchase_receive_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料调整单
ALTER TABLE `raw_material_adjust_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_adjust_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_adjust_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料调拨单
ALTER TABLE `raw_material_allocate_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_allocate_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_allocate_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料采购退货单
ALTER TABLE `raw_material_purchase_return_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_purchase_return_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_purchase_return_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料销售单
ALTER TABLE `raw_material_sale_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_sale_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_sale_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料销售退货单
ALTER TABLE `raw_material_sale_return_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_sale_return_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_sale_return_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料盘点单
ALTER TABLE `raw_material_stock_check_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_stock_check_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_stock_check_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料染整报价单
ALTER TABLE `raw_material_quote_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `raw_material_quote_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `raw_material_quote_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料管理-加工收货、加工出货、加工退货收货、加工扣款出货单
ALTER TABLE `rmm_process_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 原料染整通知单
ALTER TABLE `rmm_dye_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 应付单
ALTER TABLE `payable`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `payable`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `payable`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 实付款单
ALTER TABLE `actually_pay_order` CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE `advance_pay_order` CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 染整通知单
ALTER TABLE `dyeing_and_finishing_notice_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `dyeing_and_finishing_notice_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `dyeing_and_finishing_notice_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 染整报价单
ALTER TABLE `dyeing_and_finishing_quote_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `dyeing_and_finishing_quote_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `dyeing_and_finishing_quote_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 染整变更单
ALTER TABLE `dyeing_and_finishing_change_order`
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';
ALTER TABLE `dyeing_and_finishing_change_order`
    CHANGE COLUMN `status` `audit_status`  tinyint(1) NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';
ALTER TABLE `dyeing_and_finishing_change_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

-- 成品配布单
ALTER TABLE `fpm_arrange_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';
-- 成品配布变更单
ALTER TABLE `fpm_change_arrange_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';
-- 出仓预约单
ALTER TABLE `fpm_out_reservation_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';
-- 质检报告
ALTER TABLE `fpm_quality_checkout_report`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE fpm_in_order
    ADD COLUMN `arrange_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调拨出仓单对应的配布id';

ALTER TABLE fpm_in_order
    ADD COLUMN `arrange_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调拨出仓单对应的配布id';

UPDATE fpm_in_order AS in_order
    JOIN fpm_out_order AS out_order
ON in_order.sale_allocate_out_id = out_order.id
    SET in_order.arrange_order_id = out_order.arrange_order_id;



ALTER TABLE `shortage_product_order` -- 成品欠货单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_product_order` -- 成品销售单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_product_plan_order` -- 成品销售计划单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `sale_product_plan_change_order` -- 销售计划变更单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `pmc_grey_plan_order` -- pmc布料计划单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_change_order` -- 生产变更单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_notify_order` -- 生产通知单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_plan_order` -- 生产计划单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `production_shortage_order`
    CHANGE COLUMN `status` `audit_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';

ALTER TABLE `production_change_order`
    CHANGE COLUMN `status` `audit_status` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '订单状态 1待审核 2已审核 3已驳回 4已作废';

ALTER TABLE `production_shortage_order` -- 生产欠重单 --
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE `actually_collect_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';

ALTER TABLE `advance_collect_order`
    CHANGE COLUMN `audit_time` `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间';
ALTER TABLE `sale_transfer_order`
    CHANGE COLUMN `audit_user_id` `auditor_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '审核人ID （关联user.id）';
ALTER TABLE `sale_transfer_order`
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名',
    ADD COLUMN `business_close` bool not null DEFAULT '0' COMMENT '业务关闭',
    ADD COLUMN `business_close_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '业务关闭操作人',
    ADD COLUMN `business_close_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '业务关闭时间';
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名';

ALTER TABLE fpm_arrange_order
    ADD COLUMN same_color_same_dye_lot TINYINT(1) UNSIGNED NOT NULL DEFAULT 0 COMMENT '同色同缸 0禁止 1启用';

ALTER TABLE `fpm_arrange_order`
    ADD COLUMN `pick_up_goods_in_order` tinyint(1) unsigned NOT NULL DEFAULT 0 COMMENT '齐单提货 0禁止 1启用' AFTER `same_color_same_dye_lot`;

-- （针对齐单）送货单审核前的配布单状态
ALTER TABLE `fpm_arrange_order`
    ADD COLUMN `pre_business_status` tinyint(2) DEFAULT 0 COMMENT '业务前置状态(送货单审核专用)';


ALTER TABLE stock_product_book_log
    ADD COLUMN `book_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '占用单id（单号id）';

-- 销售调价单
ALTER TABLE `sale_price_adjust_order`
    ADD COLUMN `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    ADD COLUMN `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名',
    ADD COLUMN `business_close` bool not null DEFAULT '0' COMMENT '业务关闭',
    ADD COLUMN `business_close_user_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '业务关闭操作人',
    ADD COLUMN `business_close_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '业务关闭时间';

ALTER TABLE `sale_system`
    ADD COLUMN `default_supplier_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '默认供应商',
    ADD COLUMN `default_last_cost_price` tinyint(1) UNSIGNED DEFAULT 0 NOT NULL COMMENT '默认最后一次成本报价 0禁止 1启用';

-- 为 fpm_out_reservation_order_item 表添加可用匹数、可用数量、可用长度
ALTER TABLE fpm_out_reservation_order_item
    ADD COLUMN available_roll INT(11) COMMENT '可用匹数',
    ADD COLUMN available_weight INT(11) COMMENT '可用数量',
    ADD COLUMN available_length INT(11) COMMENT '库存辅助数量（长度）';


-- 【ERP】-坯布细码补充“条码”字段及逻辑优化
ALTER TABLE `gfm_allocate_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_other_delivery_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_other_receive_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_produce_receive_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_produce_return_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_purchase_receive_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_purchase_return_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_sale_delivery_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_sale_return_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_stock_adjust_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_stock_check_order_item_fine_code`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_warehouse`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';

-- 【ERP】-坯布进仓表、坯布出仓表
ALTER TABLE `biz_unit`
    ADD COLUMN `pin_yin` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '拼音';
ALTER TABLE `gfm_stock_record`
    ADD COLUMN `receive_unit_id`  bigint(20) unsigned DEFAULT 0  NOT NULL COMMENT '接收单位id';
ALTER TABLE `gfm_stock_record`
    ADD COLUMN `resource_unit_id`  bigint(20) unsigned DEFAULT 0  NOT NULL COMMENT '货源单位id';
ALTER TABLE `gfm_stock_record`
    ADD COLUMN `fabric_piece_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '条码';
ALTER TABLE `gfm_stock_record`
    ADD COLUMN `is_dyeing` boolean DEFAULT false NOT NULL COMMENT '是否是在染坯布库存进出仓记录';
-- 【ERP】-成品其他进仓单、成品其他出仓单会生成应付账优化
ALTER TABLE `should_collect_order_detail`
    ADD COLUMN `order_type` varchar(255) NOT NULL DEFAULT '' COMMENT '单据类型';
-- 【ERP】各种单据前缀优化
CREATE TABLE IF NOT EXISTS `order_prefix`(
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) DEFAULT '0' NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) DEFAULT '0' NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`   bigint(20) DEFAULT '0' NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT 'ID',
    `fpm_purchase_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品采购单',
    `rm_purchase_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料采购单',
    `gfm_purchase_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布采购单',
    `product_plan_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '生产计划单',
    `pmc_grey_plan_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '物料计划单',
    `sale_plan_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '销售计划单',
    `production_notify_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '生产通知单',
    `production_shortage_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '生产欠重单',
    `production_change_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '生产变更单',
    `rm_purchase_receive_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料采购收货单',
    `gfm_purchase_receive_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布采购收货单',
    `gfm_produce_receive_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布生产收货单',
    `gfm_other_receive_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布其他收货单',
    `rm_sale_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料销售退货单',
    `gfm_sale_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布销售退货单',
    `fpm_sale_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售退货单',
    `rm_purchase_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料采购退货单',
    `gfm_purchase_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布采购退货单',
    `fpm_purchase_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品采购退货单',
    `gfm_produce_return_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布生产退货单',
    `rm_sale_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料销售出货单',
    `gfm_sale_delivery_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布销售出货单',
    `fpm_sale_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售单',
    `fpm_shortage_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品欠货单',
    `sale_transfer_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '调货销售单',
    `sale_plan_change_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '销售计划变更单',
    `fpm_sale_delivery_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售送货单',
    `gfm_should_collect_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布销售应收单',
    `rm_should_collect_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料销售应收单',
    `other_should_collect_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '其他应收账',
    `rm_purchase_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料采购应付账',
    `gfm_purchase_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布采购应付账',
    `fpm_purchase_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品采购应付账',
    `process_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '加工费应付账',
    `daf_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '染整费应付账',
    `other_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '其他应付账',
    `fpm_sale_return_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售退货进仓单',
    `fpm_sale_allocate_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售调拨进仓单',
    `fpm_purchase_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品采购进仓单',
    `fpm_process_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品加工进仓单',
    `fpm_process_return_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品加工退货进仓单',
    `fpm_other_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品其他进仓单',
    `fpm_internal_allocate_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品内部调拨进仓单',
    `fpm_sale_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售出仓单',
    `fpm_sale_allocate_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品销售调拨出仓单',
    `fpm_purchase_return_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品采购退货出仓单',
    `fpm_process_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品加工出仓单',
    `fpm_deduction_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品扣款出仓单',
    `fpm_other_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品其他出仓单',
    `fpm_reservation_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品出仓预约单',
    `fpm_internal_allocateOut_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品内部调拨出仓单',
    `rm_stock_check_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料库存盘点单',
    `gfm_stock_check_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布库存盘点单',
    `fpm_stock_check_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品库存盘点单',
    `fpm_arrange_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '现货配布单',
    `fpm_arrange_change_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '销售配布变更单',
    `daf_notice_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '染整通知单',
    `daf_change_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '染整变更单',
    `finish_notice_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '后整通知单',
    `redye_notice_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '复色通知单',
    `rm_dye_notice_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料染整通知单',
    `rm_process_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料加工出货单',
    `rm_process_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料加工收货单',
    `dye_quote_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '染费报价单',
    `finishing_quote_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '后整报价单',
    `rm_dye_quote_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料染整报价单',
    `gfm_allocate_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布调拨单',
    `rm_purchase_change_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料采购变更单',
    `rm_adjust_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料库存调整单',
    `rm_allocate_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '原料库存调拨单',
    `gfm_deduction_delivery_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布出货扣款单',
    `gfm_other_delivery_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布其他出货单',
    `gfm_stock_adjust_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '坯布库存调整单',
    `actually_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '实付单',
    `advance_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '预付单',
    `other_actually_payable_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '其他支出单',
    `fpm_adjust_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品调整单',
    `fpm_adjust_out_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品调整出仓单',
    `fpm_adjust_in_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品调整进仓单',
    `fpm_quality_checkout_report` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '成品质检报告',
    `sale_price_adjust_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '销售价调整单',
    `actually_collect_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '实收单',
    `advance_collect_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '预收单',
    `other_actually_collect_order` VARCHAR(15) DEFAULT '' NOT NULL COMMENT '其他实收单',
    `date_format` VARCHAR(50) DEFAULT '' NOT NULL COMMENT '日期格式',
    `num_length` int DEFAULT 0 NOT NULL COMMENT '流水号长度',
    `use_sale_system` boolean DEFAULT 0 NOT NULL COMMENT '是否启用营销体系',
    PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='单号前缀表'


ALTER TABLE `sale_product_plan_order`
    ADD COLUMN `process_factory_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '加工厂id',
    ADD COLUMN `process_factory` varchar(255) NOT NULL DEFAULT '' COMMENT '加工厂',
    ADD COLUMN `contact_name` varchar(64) NOT NULL DEFAULT '' COMMENT '联系人名称',
    ADD COLUMN `print_tag` varchar(64) NOT NULL DEFAULT '' COMMENT '打印标签(出货标签)',
    ADD COLUMN `logistics_company` varchar(255) NOT NULL DEFAULT '' COMMENT '物流公司',
    ADD COLUMN `logistics_area` varchar(255) NOT NULL DEFAULT '' COMMENT '物流区域',
    ADD COLUMN `location` varchar(255) NOT NULL DEFAULT '' COMMENT '省市区',
    ADD COLUMN `sale_shipment_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '发货类型id',
    ADD COLUMN `sale_shipment_name` VARCHAR(255) NOT NULL DEFAULT '' NOT NULL COMMENT '发货类型名称';

UPDATE `sale_product_plan_order` JOIN `biz_unit` ON `biz_unit`.`id` = `sale_product_plan_order`.`customer_id`
SET sale_product_plan_order.`contact_name`=biz_unit.contact_name where sale_product_plan_order.contact_name='';

--【ERP】成本成本核算（布匹、坯布、原料）
DROP TABLE fpm_cost_price;
CREATE TABLE IF NOT EXISTS `fpm_cost_price` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
    `product_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '成品编号',
    `colour_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '色号颜色',
    `dyelot_number`   VARCHAR(255) DEFAULT '' NOT NULL COMMENT '缸号',
    `warehouse_good_in_type`   int DEFAULT 0 NOT NULL COMMENT '进仓类型',
    `warehouse_good_in_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '进仓单id',
    `src_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '原成本id',
    `cost_calculate_date`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '成本计算日期',
    `net_weight_price`   int DEFAULT 0 NOT NULL COMMENT '净重成本单价',
    `buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '毛重成本单价',
    `net_weight`   bigint(20) DEFAULT 0 NOT NULL COMMENT '净重',
    `buoyant_weight`   bigint(20) DEFAULT 0 NOT NULL COMMENT '毛重',
    `unit`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '成品主单位',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='成品成本表';
ALTER TABLE `payable_item`
    ADD COLUMN `buoyant_weight_price` int DEFAULT 0 NOT NULL COMMENT '毛重成本';
ALTER TABLE `payable_item`
    ADD COLUMN `net_weight_price` int DEFAULT 0 NOT NULL COMMENT '净重成本';
ALTER TABLE `payable_item`
    ADD COLUMN `receive_weight` int DEFAULT 0 NOT NULL COMMENT '本次收货数量(公斤)，乘10000存';
ALTER TABLE `payable_item`
    ADD COLUMN `pt_weight_and_weight_error` int DEFAULT 0 NOT NULL COMMENT '供方纸筒空差(公斤)，乘10000存';
ALTER TABLE `payable_item`
    ADD COLUMN `should_pay_weight` int DEFAULT 0 NOT NULL COMMENT '应付数量(公斤)，乘10000存';
ALTER TABLE `fpm_in_order_item`
    ADD COLUMN `receive_weight` int DEFAULT 0 NOT NULL COMMENT '本次收货数量(公斤)，乘10000存';
ALTER TABLE `fpm_in_order_item`
    ADD COLUMN `pt_weight_and_weight_error` int DEFAULT 0 NOT NULL COMMENT '供方纸筒空差(公斤)，乘10000存';
ALTER TABLE `fpm_in_order_item`
    ADD COLUMN `should_pay_weight` int DEFAULT 0 NOT NULL COMMENT '应付数量(公斤)，乘10000存';
CREATE TABLE IF NOT EXISTS `rm_cost_price` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
    `rm_stock_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '原料库存id',
    `cost_calculate_date`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '成本计算日期',
    `net_weight_price`   int DEFAULT 0 NOT NULL COMMENT '净重成本单价',
    `buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '毛重成本单价',
    `net_weight`   bigint(20) DEFAULT 0 NOT NULL COMMENT '净重',
    `buoyant_weight`   bigint(20) DEFAULT 0 NOT NULL COMMENT '毛重',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='原料成本表';
CREATE TABLE IF NOT EXISTS `gfm_cost_price` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
    `warehouse_sum_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '库存id',
    `cost_calculate_date`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '成本计算日期',
    `net_weight_price`   bigint(20) DEFAULT 0 NOT NULL COMMENT '净重成本单价',
    `buoyant_weight_price`   bigint(20) DEFAULT 0 NOT NULL COMMENT '毛重成本单价',
    `net_weight`   int DEFAULT 0 NOT NULL COMMENT '净重',
    `buoyant_weight`   int DEFAULT 0 NOT NULL COMMENT '毛重',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='坯布成本表';
ALTER TABLE `gfm_cost_price` DROP COLUMN  `src_order_item_id`;
ALTER TABLE `gfm_cost_price` DROP COLUMN  `src_order_id`;
ALTER TABLE `gfm_cost_price` DROP COLUMN  `grey_fabric_id`;
ALTER TABLE `gfm_cost_price` DROP COLUMN  `grey_fabric_color_id`;
ALTER TABLE `gfm_cost_price` ADD COLUMN  `warehouse_sum_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '库存id';
CREATE TABLE IF NOT EXISTS `cost_price_record` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `src_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '源id(对应成本表id)',
    `type` int DEFAULT 0 NOT NULL COMMENT '类型',
    `old_net_weight_price`   int DEFAULT 0 NOT NULL COMMENT '旧净重成本单价',
    `old_buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '旧毛重成本单价',
    `new_net_weight_price`   int DEFAULT 0 NOT NULL COMMENT '新净重成本单价',
    `new_buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '新毛重成本单价',
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE = utf8mb4_general_ci ROW_FORMAT=DYNAMIC COMMENT='成本修改记录';
ALTER TABLE `gfm_produce_receive_order_use_yarn_item`
    ADD COLUMN  `buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '毛重成本单价';
ALTER TABLE `rm_cost_price` MODIFY buoyant_weight BIGINT(20);
ALTER TABLE `rm_cost_price` MODIFY net_weight BIGINT(20);
ALTER TABLE `gfm_cost_price` MODIFY net_weight BIGINT(20);
ALTER TABLE `gfm_cost_price` MODIFY net_weight BIGINT(20);
ALTER TABLE `fpm_cost_price` MODIFY net_weight BIGINT(20);
ALTER TABLE `fpm_cost_price` MODIFY net_weight BIGINT(20);
ALTER TABLE `raw_material_allocate_order_item`
    ADD COLUMN  `allocate_in_stock_id`   bigint(20) DEFAULT 0 NOT NULL COMMENT '调入库存id';
ALTER TABLE `raw_material_allocate_order_item`
    ADD COLUMN  `buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '毛重成本单价';
ALTER TABLE `raw_material_adjust_order_item`
    ADD COLUMN  `adjust_stock_id`   bigint(20) DEFAULT 0 NOT NULL COMMENT '调整库存id';
ALTER TABLE `raw_material_adjust_order_item`
    ADD COLUMN  `buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '毛重成本单价';
ALTER TABLE `fpm_in_order_item_fc`
    ADD COLUMN `arrange_item_fc` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '配布单细码id';
ALTER TABLE `fpm_process_in_order_item_bum`
    ADD COLUMN  `buoyant_weight_price`   int DEFAULT 0 NOT NULL COMMENT '毛重成本单价';
ALTER TABLE `fpm_cost_price`
    MODIFY `dyelot_number` VARCHAR(255) COLLATE = utf8mb4_general_ci;

ALTER TABLE `tenant_management`
    ADD COLUMN `electronic_color_card_status` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '电子色卡状态 1启用 2禁用 3未激活',
ADD COLUMN `electronic_color_card_dead_line` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '电子色卡有效期';

ALTER TABLE `recharge_history`
    ADD COLUMN `type` tinyint(4) NOT NULL DEFAULT 0 COMMENT '充值类型';
UPDATE `recharge_history`
SET `type` = 1
WHERE `type` = 0;


CREATE TABLE IF NOT EXISTS carousel_banners (
    `id` bigint(20) PRIMARY KEY AUTO_INCREMENT,
    `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '创建人ID （关联user.id）',
    `creator_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
    `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '更新人ID （关联user.id）',
    `updater_name` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
    `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '删除人ID （关联user.id）',
    `deleter_name` varchar(255) NOT NULL DEFAULT '' COMMENT '删除人名称',
    `prev_view_url` varchar(128) NOT NULL COMMENT '预览图',
    `title` varchar(255) NOT NULL COMMENT '标题',
    `link` longtext NOT NULL COMMENT '链接',
    `target_id` bigint(20) NOT NULL COMMENT '目标ID',
    `jump_type` INT NOT NULL COMMENT '跳转类型',
    `sort` INT NOT NULL DEFAULT 0 COMMENT '排序',
    `remark` text COMMENT '备注',
    `status` tinyint(2) NOT NULL DEFAULT 1 COMMENT '状态', -- 此处添加逗号
    `video_url` longtext NOT NULL COMMENT '视频',
    `img_url` longtext NOT NULL COMMENT '图片',
    INDEX idx_delete_time (delete_time) -- 修正字段名
    ) ENGINE = InnoDB
    DEFAULT CHARSET = utf8mb4
    COMMENT '轮播图信息表';



CREATE TABLE `merchant_info` (
                                 `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键ID',
                                 `tenant_management_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '租户管理ID',
                                 `merchant_name` varchar(255) NOT NULL COMMENT '商家名称',
                                 `merchant_addr` varchar(255) DEFAULT NULL COMMENT '商家地址',
                                 `main_products` varchar(255) DEFAULT NULL COMMENT '主营产品',
                                 `logo_url` varchar(255) DEFAULT NULL COMMENT 'Logo图片URL',
                                 `business_type` varchar(255) DEFAULT NULL COMMENT '企业类型标签',
                                 `service_expire` datetime DEFAULT NULL COMMENT '服务有效期至',
                                 `phone_contacts` varchar(255) NOT NULL COMMENT '电话号码和联系人',
                                 `create_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
                                 `creator_id` bigint(20) unsigned NOT NULL COMMENT '创建人ID ',
                                 `creator_name` varchar(255) NOT NULL DEFAULT '' COMMENT '创建人名称',
                                 `update_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
                                 `updater_id` bigint(20) unsigned NOT NULL COMMENT '更新人ID ',
                                 `updater_name` varchar(255) NOT NULL DEFAULT '' COMMENT '更新人名称',
                                 `delete_time` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
                                 `deleter_id` bigint(20) unsigned NOT NULL COMMENT '删除人ID ',
                                 `deleter_name` varchar(255) NOT NULL DEFAULT '' COMMENT '删除人名称',
                                 `company_url` longtext COMMENT '企业URL链接',
                                 PRIMARY KEY (`id`)
) ENGINE=InnoDB AUTO_INCREMENT=1880615159266945 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_uca1400_ai_ci COMMENT='商家信息表';

ALTER TABLE merchant_info
    MODIFY COLUMN business_type varchar(255) DEFAULT NULL COMMENT '企业类型标签',
    MODIFY COLUMN main_products longtext DEFAULT NULL COMMENT '企业类型标签',
    MODIFY COLUMN merchant_name longtext DEFAULT NULL COMMENT '商家名称',
    MODIFY COLUMN merchant_addr longtext DEFAULT NULL COMMENT '商家地址',
    MODIFY COLUMN phone_contacts longtext DEFAULT NULL COMMENT '电话号码和联系人';

ALTER TABLE `tenant_management`
    ADD COLUMN `search_image_status` tinyint(3) unsigned NOT NULL DEFAULT 0 COMMENT '搜索图片状态',
    ADD COLUMN `search_image_dead_line` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '搜索图片截止日期';

ALTER TABLE `recharge_history`
    DROP COLUMN `ele_card_deadline`;