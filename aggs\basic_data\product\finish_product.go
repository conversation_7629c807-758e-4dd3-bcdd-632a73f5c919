package product

import (
	"context"
	common_system "hcscm/common/system_consts"
	"hcscm/extern/aliyun/image_search"
	"hcscm/extern/pb/basic_data/grey_fabric_info"
	"hcscm/extern/pb/basic_data/info_basic_data"
	"hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	"hcscm/extern/pb/employee"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	"hcscm/msg/msg_publish"
	structure_grey_fabric "hcscm/structure/basic_data/grey_fabric_info"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"strings"
)

type FinishProductRepo struct {
	tx *mysql_base.Tx
}

func NewFinishProductRepo(tx *mysql_base.Tx) *FinishProductRepo {
	return &FinishProductRepo{tx: tx}
}

func (r *FinishProductRepo) Add(ctx context.Context, req *structure.AddFinishProductParam) (data structure.AddFinishProductData, err error) {
	finishProduct := model.NewFinishProduct(ctx, req)

	finishProduct, err = mysql.MustCreateFinishProduct(r.tx, finishProduct)
	if err != nil {
		return
	}
	data.Id = finishProduct.Id

	data.SearchImageUploadList = func() (list msg_publish.SearchImageUploadList) {
		tenantManagementId := metadata.GetTenantManagementId(ctx)
		for _, imageUrl := range finishProduct.TextureURL.ToString() {
			o := msg_publish.SearchImageUpload{}
			o.TenantManagementId = tenantManagementId
			o.SearchImageInstanceName = ""
			o.ProductCode = finishProduct.FinishProductCode
			o.Url = imageUrl
			o.Type = 1
			list = append(list, o)
		}
		return
	}()
	return
}

func (r *FinishProductRepo) Update(ctx context.Context, req *structure.UpdateFinishProductParam) (data structure.UpdateFinishProductData, err error) {
	var (
		oldFinishProduct, finishProduct model.FinishProduct
	)
	oldFinishProduct, err = mysql.MustFirstFinishProductByID(r.tx, req.Id)
	if err != nil {
		return
	}
	finishProduct = oldFinishProduct

	finishProduct.UpdateFinishProduct(ctx, req)

	finishProduct, err = mysql.MustUpdateFinishProduct(r.tx, finishProduct)
	if err != nil {
		return
	}
	data.Id = finishProduct.Id

	data.SearchImageUploadList = func() (list msg_publish.SearchImageUploadList) {
		tenantManagementId := metadata.GetTenantManagementId(ctx)
		var imageUrlMap = make(map[string]struct{})
		// 把旧的图片地址添加进map
		for _, oldImageUrl := range oldFinishProduct.TextureURL.ToString() {
			imageUrlMap[oldImageUrl] = struct{}{}
		}
		// 判断新的图片地址是否在旧的图片地址中，如果在则删除旧的图片地址，最后剩下的都是要删除的图片地址
		for _, imageUrl := range finishProduct.TextureURL.ToString() {
			if _, ok := imageUrlMap[imageUrl]; ok {
				delete(imageUrlMap, imageUrl)
			}
			// 新图全部直接新增上传
			o := msg_publish.SearchImageUpload{}
			o.TenantManagementId = tenantManagementId
			o.SearchImageInstanceName = ""
			o.ProductCode = finishProduct.FinishProductCode
			o.Url = imageUrl
			o.Type = 1
			list = append(list, o)
		}
		for imageUrl := range imageUrlMap {
			o := msg_publish.SearchImageUpload{}
			o.TenantManagementId = tenantManagementId
			o.SearchImageInstanceName = ""
			o.ProductCode = finishProduct.FinishProductCode
			o.Url = imageUrl
			o.Type = 3
			list = append(list, o)
		}
		return
	}()
	return
}

func (r *FinishProductRepo) UpdateStatus(ctx context.Context, req *structure.UpdateFinishProductStatusParam) (data structure.UpdateFinishProductStatusData, err error) {
	var (
		finishProducts model.FinishProductList
	)

	finishProducts, err = mysql.FindFinishProductByIDs(r.tx, req.Id.ToUint64())
	for _, finishProduct := range finishProducts {
		finishProduct.Status = req.Status
		finishProduct, err = mysql.MustUpdateFinishProduct(r.tx, finishProduct)
		if err != nil {
			return
		}
	}
	// 如果是禁用，则需要把搜索图片的对应图片删除
	if req.Status == common_system.StatusDisable {
		data.SearchImageUploadList = func() (list msg_publish.SearchImageUploadList) {
			tenantManagementId := metadata.GetTenantManagementId(ctx)
			for _, finishProduct := range finishProducts {
				o := msg_publish.SearchImageUpload{}
				o.TenantManagementId = tenantManagementId
				o.SearchImageInstanceName = ""
				o.ProductCode = finishProduct.FinishProductCode
				o.Url = ""
				o.Type = 3
				list = append(list, o)
			}
			return
		}()
	}
	// 如果是启用，则需要把搜索图片的对应图片新增回去
	if req.Status == common_system.StatusEnable {
		data.SearchImageUploadList = func() (list msg_publish.SearchImageUploadList) {
			tenantManagementId := metadata.GetTenantManagementId(ctx)
			for _, finishProduct := range finishProducts {
				for _, imageUrl := range finishProduct.TextureURL.ToString() {
					o := msg_publish.SearchImageUpload{}
					o.TenantManagementId = tenantManagementId
					o.SearchImageInstanceName = ""
					o.ProductCode = finishProduct.FinishProductCode
					o.Url = imageUrl
					o.Type = 1
					list = append(list, o)
				}
			}
			return
		}()
	}
	return
}

func (r *FinishProductRepo) Delete(ctx context.Context, req *structure.DeleteFinishProductParam) (data structure.DeleteFinishProductData, err error) {
	var (
		products model.FinishProductList
	)

	products, err = mysql.FindFinishProductByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	// 先更新删除原因，后执行软删除
	for _, product := range products {
		// 删除
		err = mysql.MustDeleteFinishProduct(r.tx, product)
		if err != nil {
			return
		}
		data.Id = append(data.Id, product.Id)
	}
	// 如果是删除，需要把搜索图片的对应图片删除
	data.SearchImageUploadList = func() (list msg_publish.SearchImageUploadList) {
		tenantManagementId := metadata.GetTenantManagementId(ctx)
		for _, product := range products {
			o := msg_publish.SearchImageUpload{}
			o.TenantManagementId = tenantManagementId
			o.SearchImageInstanceName = ""
			o.ProductCode = product.FinishProductCode
			o.Url = ""
			o.Type = 3
			list = append(list, o)
		}
		return
	}()
	return
}

func (r *FinishProductRepo) Get(ctx context.Context, req *structure.GetFinishProductQuery) (data structure.GetFinishProductData, err error) {
	var (
		finishProduct model.FinishProduct
	)
	finishProduct, err = mysql.MustFirstFinishProductByID(r.tx, req.Id)
	if err != nil {
		return
	}

	var (
		employeeIds           = set.NewUint64Set()
		productLevelIds       = set.NewUint64Set()
		bizUnitIds            = set.NewUint64Set()
		dictionaryDetailIds   = set.NewUint64Set()
		greyFabricIds         = set.NewUint64Set()
		measurementUnitIds    = set.NewUint64Set()
		employeeSvc           = employee.NewClientEmployeeService()
		productLevelSvc       = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		bizUnitSvc            = biz_unit.NewClientBizUnitService()
		dictionaryDetailsSvc  = dictionary.NewDictionaryClient()
		greyFabricSvc         = grey_fabric_info.NewGreyFabricInfoClient()
		measurementUnitSvc    = info_basic_data.NewInfoBaseMeasurementUnitClient()
		employeeName          map[uint64]string
		productLevel          map[uint64]string
		bizUnitName           map[uint64]string
		dictionaryDetailsName map[uint64][2]string
		greyFabricItem        map[uint64]*structure_grey_fabric.GetGreyFabricInfoData
		measurementUnitName   map[uint64]string
	)

	employeeIds.Add(finishProduct.DyeFactoryOrderFollowerId)
	productLevelIds.Add(finishProduct.FinishProductLevelId)
	bizUnitIds.AddList(finishProduct.SupplierId.ToInt())
	dictionaryDetailIds.Add(finishProduct.FinishProductWidthUnitId)
	dictionaryDetailIds.Add(finishProduct.FinishProductGramWeightUnitId)
	dictionaryDetailIds.Add(finishProduct.WeavingOrganizationId)
	dictionaryDetailIds.Add(finishProduct.BleachId)
	greyFabricIds.Add(finishProduct.GreyFabricId)
	measurementUnitIds.Add(finishProduct.MeasurementUnitId)
	measurementUnitIds.Add(finishProduct.SupplierMeasurementUnitId)

	err = errgroup.Finish(ctx, 0,
		// 员工信息
		func(ctx context.Context) error {
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds.List())
			return nil
		},
		// 成品等级
		func(ctx context.Context) error {
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds.List())
			return nil
		},
		// 往来单位
		func(ctx context.Context) error {
			bizUnitName, _ = bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds.List())
			return nil
		},
		// 字典详情
		func(ctx context.Context) error {
			dictionaryDetailsName, _ = dictionaryDetailsSvc.GetDictionaryNameByIds(ctx, dictionaryDetailIds.List())
			return nil
		},
		// 坯布信息
		func(ctx context.Context) error {
			greyFabricItem, _ = greyFabricSvc.GetGreyFabricInfoMapList(ctx, greyFabricIds.List())
			return nil
		},
		// 计量单位
		func(ctx context.Context) error {
			measurementUnitName, _ = measurementUnitSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds.List())
			return nil
		},
	)

	o := structure.GetFinishProductData{}
	o.Id = finishProduct.Id
	o.CreateTime = tools.MyTime(finishProduct.CreateTime)
	o.UpdateTime = tools.MyTime(finishProduct.UpdateTime)
	o.CreatorId = finishProduct.CreatorId
	o.CreatorName = finishProduct.CreatorName
	o.UpdaterId = finishProduct.UpdaterId
	o.UpdateUserName = finishProduct.UpdaterName
	o.FinishProductCode = finishProduct.FinishProductCode
	o.FinishProductName = finishProduct.FinishProductName
	o.FinishProductFullName = finishProduct.FinishProductFullName
	o.TypeGreyFabricId = finishProduct.TypeGreyFabricId
	o.TypeGreyFabricCode = finishProduct.TypeGreyFabricCode
	o.TypeGreyFabricName = finishProduct.TypeGreyFabricName
	o.FinishProductIngredient = finishProduct.FinishProductIngredient
	o.MeasurementUnitId = finishProduct.MeasurementUnitId
	o.MeasurementUnitName = measurementUnitName[finishProduct.MeasurementUnitId]
	o.SupplierMeasurementUnitId = finishProduct.SupplierMeasurementUnitId
	o.SupplierMeasurementUnitName = measurementUnitName[finishProduct.SupplierMeasurementUnitId]
	o.GreyFabricId = finishProduct.GreyFabricId
	if val, ok := greyFabricItem[finishProduct.GreyFabricId]; ok {
		o.GreyFabricCode = val.Code
		o.GreyFabricName = val.Name
		o.GreyFabricColorId = val.GrayFabricColorId
		o.GreyFabricColorName = val.GrayFabricColorName
	}
	o.IsColorCard = finishProduct.IsColorCard
	o.WarehouseId = finishProduct.WarehouseId
	o.WarehouseName = finishProduct.WarehouseName
	o.StorageArea = finishProduct.StorageArea
	o.DyeFactoryOrderFollowerId = finishProduct.DyeFactoryOrderFollowerId
	o.DyeFactoryOrderFollowerName = employeeName[finishProduct.DyeFactoryOrderFollowerId]
	o.Remark = finishProduct.Remark
	o.LengthToWeightRate = finishProduct.LengthToWeightRate
	o.StandardWeight = finishProduct.StandardWeight
	o.PaperTubeWeight = finishProduct.PaperTubeWeight
	o.WeightError = finishProduct.WeightError
	o.FinishProductCraft = finishProduct.FinishProductCraft
	o.Status = finishProduct.Status
	o.StatusName = finishProduct.Status.String()
	o.DyeingCraft = finishProduct.DyeingCraft
	o.FinishProductLevelId = finishProduct.FinishProductLevelId
	o.FinishProductLevelName = productLevel[finishProduct.FinishProductLevelId]
	o.TouchStyle = finishProduct.TouchStyle
	o.DyeingLoss = finishProduct.DyeingLoss
	o.Center = finishProduct.Center
	o.Density = finishProduct.Density
	o.Size = finishProduct.Size
	o.TextureURL = finishProduct.TextureURL
	o.CoverTextureURL = finishProduct.CoverTextureURL
	o.SupplierId = finishProduct.SupplierId
	supplierName := make([]string, 0, len(finishProduct.SupplierId))
	for _, supplierId := range finishProduct.SupplierId.ToInt() {
		supplierName = append(supplierName, bizUnitName[supplierId])
	}
	o.SupplierName = strings.Join(supplierName, ",")
	o.YarnCount = finishProduct.YarnCount

	o.BuildFPResp(finishProduct.FinishProductWidth, finishProduct.FinishProductGramWeight, dictionaryDetailsName[finishProduct.FinishProductWidthUnitId][1],
		dictionaryDetailsName[finishProduct.FinishProductGramWeightUnitId][1], finishProduct.FinishProductWidthUnitId, finishProduct.FinishProductGramWeightUnitId)

	o.WeavingOrganizationId = finishProduct.WeavingOrganizationId
	o.WeavingOrganizationName = dictionaryDetailsName[finishProduct.WeavingOrganizationId][1]
	o.BleachId = finishProduct.BleachId
	o.BleachName = dictionaryDetailsName[finishProduct.BleachId][1]
	o.ShrinkageWarp = finishProduct.ShrinkageWarp
	data = o
	return
}

func (r *FinishProductRepo) GetList(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetFinishProductDataList, total int, err error) {
	var (
		finishProducts model.FinishProductList
	)

	// 赋值suppliers
	if req.FieldSearch != "" {
		// 供应商名称查询权限
		if metadata.GetDataAccessScope(ctx).IsSupplierName() {
			req.SupplierIds, err = biz_unit.NewClientBizUnitService().GetBizUnitIdsByNameLike(ctx, req.FieldSearch)
			if err != nil {
				return
			}
		}
	}

	finishProducts, total, err = mysql.SearchFinishProduct(r.tx, req, true)
	if err != nil {
		return
	}

	var (
		employeeIds           = set.NewUint64Set()
		productLevelIds       = set.NewUint64Set()
		bizUnitIds            = set.NewUint64Set()
		dictionaryDetailIds   = set.NewUint64Set()
		greyFabricIds         = set.NewUint64Set()
		measurementUnitIds    = set.NewUint64Set()
		employeeSvc           = employee.NewClientEmployeeService()
		productLevelSvc       = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		bizUnitSvc            = biz_unit.NewClientBizUnitService()
		dictionaryDetailsSvc  = dictionary.NewDictionaryClient()
		greyFabricSvc         = grey_fabric_info.NewGreyFabricInfoClient()
		measurementUnitSvc    = info_basic_data.NewInfoBaseMeasurementUnitClient()
		employeeName          map[uint64]string
		productLevel          map[uint64]string
		bizUnitName           map[uint64]string
		dictionaryDetailsName map[uint64][2]string
		greyFabricItem        map[uint64]*structure_grey_fabric.GetGreyFabricInfoData
		measurementUnitName   map[uint64]string
	)

	for _, finishProduct := range finishProducts {
		employeeIds.Add(finishProduct.DyeFactoryOrderFollowerId)
		productLevelIds.Add(finishProduct.FinishProductLevelId)
		bizUnitIds.AddList(finishProduct.SupplierId.ToInt())
		dictionaryDetailIds.Add(finishProduct.FinishProductWidthUnitId)
		dictionaryDetailIds.Add(finishProduct.FinishProductGramWeightUnitId)
		dictionaryDetailIds.Add(finishProduct.WeavingOrganizationId)
		dictionaryDetailIds.Add(finishProduct.BleachId)
		greyFabricIds.Add(finishProduct.GreyFabricId)
		measurementUnitIds.Add(finishProduct.MeasurementUnitId)
		measurementUnitIds.Add(finishProduct.SupplierMeasurementUnitId)
	}

	err = errgroup.Finish(ctx, 0,
		// 员工信息
		func(ctx context.Context) error {
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds.List())
			return nil
		},
		// 成品等级
		func(ctx context.Context) error {
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds.List())
			return nil
		},
		// 往来单位
		func(ctx context.Context) error {
			bizUnitName, _ = bizUnitSvc.GetBizUnitNameByIds(ctx, bizUnitIds.List())
			return nil
		},
		// 字典详情
		func(ctx context.Context) error {
			dictionaryDetailsName, _ = dictionaryDetailsSvc.GetDictionaryNameByIds(ctx, dictionaryDetailIds.List())
			return nil
		},
		// 坯布信息
		func(ctx context.Context) error {
			greyFabricItem, _ = greyFabricSvc.GetGreyFabricInfoMapList(ctx, greyFabricIds.List())
			return nil
		},
		// 计量单位
		func(ctx context.Context) error {
			measurementUnitName, _ = measurementUnitSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds.List())
			return nil
		},
	)

	for _, finishProduct := range finishProducts.List() {
		var (
			o            = structure.GetFinishProductData{}
			supplierName []string
		)
		o.Id = finishProduct.Id
		o.CreateTime = tools.MyTime(finishProduct.CreateTime)
		o.UpdateTime = tools.MyTime(finishProduct.UpdateTime)
		o.CreatorId = finishProduct.CreatorId
		o.CreatorName = finishProduct.CreatorName
		o.UpdaterId = finishProduct.UpdaterId
		o.UpdateUserName = finishProduct.UpdaterName
		o.FinishProductCode = finishProduct.FinishProductCode
		o.FinishProductName = finishProduct.FinishProductName
		o.FinishProductFullName = finishProduct.FinishProductFullName
		o.TypeGreyFabricId = finishProduct.TypeGreyFabricId
		o.TypeGreyFabricCode = finishProduct.TypeGreyFabricCode
		o.TypeGreyFabricName = finishProduct.TypeGreyFabricName
		o.FinishProductIngredient = finishProduct.FinishProductIngredient
		o.MeasurementUnitId = finishProduct.MeasurementUnitId
		o.MeasurementUnitName = measurementUnitName[finishProduct.MeasurementUnitId]
		o.SupplierMeasurementUnitId = finishProduct.SupplierMeasurementUnitId
		o.SupplierMeasurementUnitName = measurementUnitName[finishProduct.SupplierMeasurementUnitId]
		o.GreyFabricId = finishProduct.GreyFabricId
		if greyFabric, ok := greyFabricItem[finishProduct.GreyFabricId]; ok {
			o.GreyFabricCode = greyFabric.Code
			o.GreyFabricName = greyFabric.Name
		}
		o.IsColorCard = finishProduct.IsColorCard
		o.WarehouseId = finishProduct.WarehouseId
		o.WarehouseName = finishProduct.WarehouseName
		o.StorageArea = finishProduct.StorageArea
		o.DyeFactoryOrderFollowerId = finishProduct.DyeFactoryOrderFollowerId
		o.DyeFactoryOrderFollowerName = employeeName[finishProduct.DyeFactoryOrderFollowerId]
		o.Remark = finishProduct.Remark
		o.LengthToWeightRate = finishProduct.LengthToWeightRate
		o.StandardWeight = finishProduct.StandardWeight
		o.PaperTubeWeight = finishProduct.PaperTubeWeight
		o.WeightError = finishProduct.WeightError
		o.FinishProductCraft = finishProduct.FinishProductCraft
		o.Status = finishProduct.Status
		o.StatusName = finishProduct.Status.String()
		o.DyeingCraft = finishProduct.DyeingCraft
		o.FinishProductLevelId = finishProduct.FinishProductLevelId
		o.FinishProductLevelName = productLevel[finishProduct.FinishProductLevelId]
		o.TouchStyle = finishProduct.TouchStyle
		o.DyeingLoss = finishProduct.DyeingLoss
		o.Center = finishProduct.Center
		o.Density = finishProduct.Density
		o.Size = finishProduct.Size
		// o.TextureURL = finishProduct.TextureURL
		o.CoverTextureURL = finishProduct.CoverTextureURL
		if len(finishProduct.TextureURL) > 0 {
			o.MainTextureUrl = finishProduct.TextureURL[0]
		}
		o.SupplierId = finishProduct.SupplierId
		for _, supplierId := range finishProduct.SupplierId.ToInt() {
			supplierName = append(supplierName, bizUnitName[supplierId])
		}
		o.SupplierName = strings.Join(supplierName, ",")
		o.YarnCount = finishProduct.YarnCount

		o.BuildFPResp(finishProduct.FinishProductWidth, finishProduct.FinishProductGramWeight, dictionaryDetailsName[finishProduct.FinishProductWidthUnitId][1],
			dictionaryDetailsName[finishProduct.FinishProductGramWeightUnitId][1], finishProduct.FinishProductWidthUnitId, finishProduct.FinishProductGramWeightUnitId)

		o.WeavingOrganizationId = finishProduct.WeavingOrganizationId
		o.WeavingOrganizationCode = dictionaryDetailsName[finishProduct.WeavingOrganizationId][0]
		o.WeavingOrganizationName = dictionaryDetailsName[finishProduct.WeavingOrganizationId][1]
		o.BleachId = finishProduct.BleachId
		o.BleachName = dictionaryDetailsName[finishProduct.BleachId][1]
		o.ShrinkageWarp = finishProduct.ShrinkageWarp
		list = append(list, o)
	}
	return
}

func (r *FinishProductRepo) GetDropdownList(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetFinishProductDropdownDataList, total int, err error) {
	var (
		finishProducts      model.FinishProductList
		finishProductColors model.FinishProductColorList
	)
	req.Status = common_system.StatusEnable
	finishProducts, total, err = mysql.SearchFinishProduct(r.tx, req, true)
	if err != nil {
		return
	}

	finishProductColors, err = mysql.FindFinishProductColorByProductIDs(r.tx, finishProducts.GetIds())
	if err != nil {
		return
	}

	var (
		employeeIds           = set.NewUint64Set()
		productLevelIds       = set.NewUint64Set()
		dictionaryDetailIds   = set.NewUint64Set()
		greyFabricIds         = set.NewUint64Set()
		measurementUnitIds    = set.NewUint64Set()
		employeeSvc           = employee.NewClientEmployeeService()
		productLevelSvc       = info_basic_data.NewInfoBaseFinishedProductLevelClient()
		dictionaryDetailsSvc  = dictionary.NewDictionaryClient()
		greyFabricSvc         = grey_fabric_info.NewGreyFabricInfoClient()
		measurementUnitSvc    = info_basic_data.NewInfoBaseMeasurementUnitClient()
		employeeName          map[uint64]string
		productLevel          map[uint64]string
		dictionaryDetailsName map[uint64][2]string
		greyFabricItem        map[uint64]*structure_grey_fabric.GetGreyFabricInfoData
		measurementUnitName   map[uint64]string
	)

	for _, finishProduct := range finishProducts {
		employeeIds.Add(finishProduct.DyeFactoryOrderFollowerId)
		productLevelIds.Add(finishProduct.FinishProductLevelId)
		dictionaryDetailIds.Add(finishProduct.FinishProductWidthUnitId)
		dictionaryDetailIds.Add(finishProduct.FinishProductGramWeightUnitId)
		dictionaryDetailIds.Add(finishProduct.WeavingOrganizationId)
		dictionaryDetailIds.Add(finishProduct.BleachId)
		greyFabricIds.Add(finishProduct.GreyFabricId)
		measurementUnitIds.Add(finishProduct.MeasurementUnitId)
		measurementUnitIds.Add(finishProduct.SupplierMeasurementUnitId)
	}

	err = errgroup.Finish(ctx, 0,
		// 员工信息
		func(ctx context.Context) error {
			employeeName, _ = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds.List())
			return nil
		},
		// 成品等级
		func(ctx context.Context) error {
			productLevel, _ = productLevelSvc.GetInfoBaseFinishedProductLevelNameByIds(ctx, productLevelIds.List())
			return nil
		},
		// 字典详情
		func(ctx context.Context) error {
			dictionaryDetailsName, _ = dictionaryDetailsSvc.GetDictionaryNameByIds(ctx, dictionaryDetailIds.List())
			return nil
		},
		// 坯布信息
		func(ctx context.Context) error {
			greyFabricItem, _ = greyFabricSvc.GetGreyFabricInfoMapList(ctx, greyFabricIds.List())
			return nil
		},
		// 计量单位
		func(ctx context.Context) error {
			measurementUnitName, _ = measurementUnitSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds.List())
			return nil
		},
	)

	for _, finishProduct := range finishProducts.List() {
		_finishProductColors := finishProductColors.PickByProductID(finishProduct.Id)
		o := structure.GetFinishProductDropdownData{}
		o.Id = finishProduct.Id
		o.CreateTime = tools.MyTime(finishProduct.CreateTime)
		o.UpdateTime = tools.MyTime(finishProduct.UpdateTime)
		o.CreatorId = finishProduct.CreatorId
		o.CreatorName = finishProduct.CreatorName
		o.UpdaterId = finishProduct.UpdaterId
		o.UpdateUserName = finishProduct.UpdaterName
		o.FinishProductCode = finishProduct.FinishProductCode
		o.FinishProductName = finishProduct.FinishProductName
		o.FinishProductFullName = finishProduct.FinishProductFullName
		o.TypeGreyFabricId = finishProduct.TypeGreyFabricId
		o.TypeGreyFabricCode = finishProduct.TypeGreyFabricCode
		o.TypeGreyFabricName = finishProduct.TypeGreyFabricName
		o.FinishProductIngredient = finishProduct.FinishProductIngredient
		o.MeasurementUnitId = finishProduct.MeasurementUnitId
		o.MeasurementUnitName = measurementUnitName[finishProduct.MeasurementUnitId]
		o.SupplierMeasurementUnitId = finishProduct.SupplierMeasurementUnitId
		o.SupplierMeasurementUnitName = measurementUnitName[finishProduct.SupplierMeasurementUnitId]
		o.GreyFabricId = finishProduct.GreyFabricId
		if greyFabric, ok := greyFabricItem[finishProduct.GreyFabricId]; ok {
			o.GreyFabricCode = greyFabric.Code
			o.GreyFabricName = greyFabric.Name
		}
		o.IsColorCard = finishProduct.IsColorCard
		o.WarehouseId = finishProduct.WarehouseId
		o.WarehouseName = finishProduct.WarehouseName
		o.StorageArea = finishProduct.StorageArea
		o.DyeFactoryOrderFollowerId = finishProduct.DyeFactoryOrderFollowerId
		o.DyeFactoryOrderFollowerName = employeeName[finishProduct.DyeFactoryOrderFollowerId]
		o.Remark = finishProduct.Remark
		o.LengthToWeightRate = finishProduct.LengthToWeightRate
		o.StandardWeight = finishProduct.StandardWeight
		o.PaperTubeWeight = finishProduct.PaperTubeWeight
		o.WeightError = finishProduct.WeightError
		o.FinishProductCraft = finishProduct.FinishProductCraft
		o.DyeingCraft = finishProduct.DyeingCraft
		o.FinishProductLevelId = finishProduct.FinishProductLevelId
		o.FinishProductLevelName = productLevel[finishProduct.FinishProductLevelId]
		o.TouchStyle = finishProduct.TouchStyle
		o.DyeingLoss = finishProduct.DyeingLoss
		o.Center = finishProduct.Center
		o.Density = finishProduct.Density
		o.Size = finishProduct.Size
		o.YarnCount = finishProduct.YarnCount
		o.TextureURL = finishProduct.TextureURL
		o.CoverTextureURL = finishProduct.CoverTextureURL

		o.BuildFPResp(finishProduct.FinishProductWidth, finishProduct.FinishProductGramWeight, dictionaryDetailsName[finishProduct.FinishProductWidthUnitId][1],
			dictionaryDetailsName[finishProduct.FinishProductGramWeightUnitId][1], finishProduct.FinishProductWidthUnitId, finishProduct.FinishProductGramWeightUnitId)

		o.WeavingOrganizationId = finishProduct.WeavingOrganizationId
		o.WeavingOrganizationCode = dictionaryDetailsName[finishProduct.WeavingOrganizationId][0]
		o.WeavingOrganizationName = dictionaryDetailsName[finishProduct.WeavingOrganizationId][1]
		o.BleachId = finishProduct.BleachId
		o.BleachName = dictionaryDetailsName[finishProduct.BleachId][1]
		o.ShrinkageWarp = finishProduct.ShrinkageWarp
		o.SumColors = len(_finishProductColors)
		list = append(list, o)
	}
	return
}

func (r *FinishProductRepo) GetKindAndProductList(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetKindAndProductDropdownDataList, total int, err error) {
	var (
		finishProducts model.FinishProductList
	)
	req.Status = common_system.StatusEnable
	finishProducts, total, err = mysql.SearchFinishProduct(r.tx, req, true)
	if err != nil {
		return
	}

	for _, finishProduct := range finishProducts.List() {
		o := structure.GetKindAndProductDropdownData{}
		o.Id = finishProduct.Id
		o.FinishProductCode = finishProduct.FinishProductCode
		o.FinishProductName = finishProduct.FinishProductName
		o.TypeGreyFabricId = finishProduct.TypeGreyFabricId
		o.TypeGreyFabricCode = finishProduct.TypeGreyFabricCode
		o.TypeGreyFabricName = finishProduct.TypeGreyFabricName
		list = append(list, o)
	}
	return
}

// 只获取成品的几个字段
func (r *FinishProductRepo) SearchForSomeProductField(ctx context.Context, q *structure.GetFinishProductListQuery) (data structure.GetSomeProductFieldDataList, err error) {
	var (
		list  []model.SomeProductField
		_data = make(structure.GetSomeProductFieldDataList, 0)
	)
	list, err = mysql.SearchForSomeProductField(r.tx, q)
	if err != nil {
		return
	}

	for _, product := range list {
		_data = append(_data, product.BuildResp())
	}

	data = _data
	return
}

// 只获取成品的几个字段
func (r *FinishProductRepo) SearchImageByUrl(ctx context.Context, req *structure.SearchImageListQuery) (list structure.SearchImageDataList, total int, err error) {
	var (
		results        image_search.SearchImageByUrlDataList
		productCodeMap = set.NewStringSet()
		products       model.FinishProductList
	)
	list = make(structure.SearchImageDataList, 0)
	results, total, err = image_search.SearchImageByUrl(ctx, "", req.ImageUrl, req.GetOffset(), req.GetLimit())
	if err != nil {
		return
	}

	for _, result := range results {
		productCodeMap.Add(result.ProductCode)
	}

	products, err = mysql.FindFinishProductByCode(r.tx, productCodeMap.List())
	if err != nil {
		return
	}

	for _, result := range results {
		product := products.PickByCode(result.ProductCode)
		data := structure.SearchImageData{
			ProductId:   product.Id,
			ProductCode: product.FinishProductCode,
			ProductName: product.FinishProductName,
			Score:       result.Score,
			Url:         result.Url,
		}
		list = append(list, data)
	}
	return
}
