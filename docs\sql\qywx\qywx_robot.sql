CREATE TABLE IF NOT EXISTS `qywx_robot` (
    `id` bigint(20) unsigned NOT NULL COMMENT 'id',
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT '0' NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT '0' NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`   bigint(20) unsigned  DEFAULT '0' NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
    `name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '机器人名称',
    `url` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '机器人地址',
    `tobe_developed_app_id`   bigint(20) unsigned  DEFAULT '0' NOT NULL COMMENT '代开发应用id'
    PRIMARY KEY (`id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='企业微信机器人';