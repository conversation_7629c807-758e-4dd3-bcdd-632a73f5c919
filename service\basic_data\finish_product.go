package basic_data

import (
	"context"
	aggs "hcscm/aggs/basic_data/product"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
)

func NewFinishProductService() *FinishProductService {
	return &FinishProductService{}
}

type FinishProductService struct {
}

func (u FinishProductService) Add(ctx context.Context, req *structure.AddFinishProductParam) (data structure.AddFinishProductData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFinishProductRepo(tx)
	data, err = repo.Add(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FinishProductService) Update(ctx context.Context, req *structure.UpdateFinishProductParam) (data structure.UpdateFinishProductData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFinishProductRepo(tx)
	data, err = repo.Update(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FinishProductService) UpdateStatus(ctx context.Context, req *structure.UpdateFinishProductStatusParam) (data structure.UpdateFinishProductStatusData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFinishProductRepo(tx)
	data, err = repo.UpdateStatus(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FinishProductService) Delete(ctx context.Context, req *structure.DeleteFinishProductParam) (data structure.DeleteFinishProductData, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewFinishProductRepo(tx)
	data, err = repo.Delete(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FinishProductService) Get(ctx context.Context, req *structure.GetFinishProductQuery) (data structure.GetFinishProductData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFinishProductRepo(tx)
	data, err = repo.Get(ctx, req)
	if err != nil {
		return data, err
	}
	return
}

func (u FinishProductService) GetList(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetFinishProductDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFinishProductRepo(tx)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u FinishProductService) SearchForSomeProductField(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetSomeProductFieldDataList, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFinishProductRepo(tx)
	return repo.SearchForSomeProductField(ctx, req)
}

func (u FinishProductService) GetDropdownList(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetFinishProductDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFinishProductRepo(tx)
	list, total, err = repo.GetDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

func (u FinishProductService) GetKindAndProductList(ctx context.Context, req *structure.GetFinishProductListQuery) (list structure.GetKindAndProductDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFinishProductRepo(tx)
	list, total, err = repo.GetKindAndProductList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 搜索相似图片
func (u FinishProductService) SearchImageByUrl(ctx context.Context, req *structure.SearchImageListQuery) (list structure.SearchImageDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewFinishProductRepo(tx)
	list, total, err = repo.SearchImageByUrl(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}
