package tenant_management

import (
	"context"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/tenant_management"
	structure "hcscm/structure/tenant_management"
	"time"
)

type ITenantManagementDao interface {
	MustCreate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.TenantManagement) (_tenantManagement mysql.TenantManagement, err error)
	MustUpdate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.TenantManagement) (_tenantManagement mysql.TenantManagement, err error)
	MustFirst(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, err error)
	FirstByAssignPort(ctx context.Context, tx *mysql_base.Tx, assignPort int) (tenantManagement mysql.TenantManagement, isExist bool, err error)
	FirstByID(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, isExist bool, err error)
	FirstByPhone(ctx context.Context, tx *mysql_base.Tx, phone string) (tenantManagement mysql.TenantManagement, isExist bool, err error)
	FirstBySecret(ctx context.Context, tx *mysql_base.Tx, secret string) (tenantManagement mysql.TenantManagement, isExist bool, err error)
	FindByIDsOfReflect(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (tenantManagements mysql.TenantManagementList, err error)
	FindByIDs(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (tenantManagements mysql.TenantManagementList, err error)
	SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (tenantManagements mysql.TenantManagementList, total int, err error)
	GetExpiringSoonList(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error)
	FindAll(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error)
	UpdateDomain(ctx context.Context, tx *mysql_base.Tx, q *structure.FillDomainParam) error
	GetUnbindTenantManagementList(ctx context.Context, tx *mysql_base.Tx, query structure.QYWXGetUnBindTenantManagementListQuery) (list mysql.TenantManagementList, total int, err error)
}

func NewTenantManagementDao() ITenantManagementDao {
	return &tenantManagementDao{}
}

type tenantManagementDao struct {
}

func (dao *tenantManagementDao) MustCreate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.TenantManagement) (_tenantManagement mysql.TenantManagement, err error) {
	err = mysql_base.MustCreateModel(tx, &tenantManagement)
	if err != nil {
		return
	}
	_tenantManagement = tenantManagement
	return
}

func (dao *tenantManagementDao) MustUpdate(ctx context.Context, tx *mysql_base.Tx, tenantManagement mysql.TenantManagement) (_tenantManagement mysql.TenantManagement, err error) {
	err = mysql_base.MustUpdateModel(tx, &tenantManagement)
	if err != nil {
		return
	}
	_tenantManagement = tenantManagement
	return
}

func (dao *tenantManagementDao) MustFirst(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, err error) {
	var cond = mysql_base.NewCondition()
	err = mysql_base.MustFirst(tx, &tenantManagement, tenantManagementID, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) FirstByAssignPort(ctx context.Context, tx *mysql_base.Tx, assignPort int) (tenantManagement mysql.TenantManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("assign_port", assignPort)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) FirstByID(ctx context.Context, tx *mysql_base.Tx, tenantManagementID uint64) (tenantManagement mysql.TenantManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("id", tenantManagementID)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}
func (dao *tenantManagementDao) FirstByPhone(ctx context.Context, tx *mysql_base.Tx, phone string) (tenantManagement mysql.TenantManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("phone", phone)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) FirstBySecret(ctx context.Context, tx *mysql_base.Tx, secret string) (tenantManagement mysql.TenantManagement, isExist bool, err error) {
	var cond = mysql_base.NewCondition()
	cond.AddEqual("secret", secret)
	isExist, err = mysql_base.FirstByCond(tx, &tenantManagement, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) FindByIDs(ctx context.Context, tx *mysql_base.Tx, ids []uint64) (tenantManagements mysql.TenantManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.TenantManagement
	)
	cond.AddContainMatch("id", ids)
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) FindByIDsOfReflect(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (tenantManagements mysql.TenantManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.TenantManagement
	)
	cond.AddContainMatch("id", mysql_base.GetUInt64ListV2("tenant_management_id", objects))
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (tenantManagements mysql.TenantManagementList, total int, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.TenantManagement
	)
	if !query.CreateStartTime.IsYMDZero() && !query.CreateEndTime.IsYMDZero() {
		cond.Between("create_time", query.CreateStartTime.StringYMD(), query.CreateEndTime.StringYMD2DayListTimeYMDHMS())
	}
	if query.IDOrName != "" {
		cond.AddMultiFieldLikeMatch([]string{"id", "tenant_company_name"}, query.IDOrName)
	}
	if query.TenantManagementStatus != 0 {
		cond.AddEqual("tenant_management_status", query.TenantManagementStatus)
	}
	if query.Phone != "" {
		cond.AddFuzzyMatch("tenant_phone_number", query.Phone)
	}
	total, err = mysql_base.SearchListGroupForPaging(tx, &tenantManagement, query, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) GetExpiringSoonList(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.TenantManagement
		initTime, _      = time.Parse("2006-01-02 15:04:05", "2023-11-01 00:00:00")
	)
	cond.AddContainMatch("tenant_management_status", []common.TenantManagementStatus{common.TenantManagementStatusNormal, common.TenantManagementStatusDisable})
	cond.Between("deadline", initTime, time.Now().AddDate(0, 0, 1))
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) FindAll(ctx context.Context, tx *mysql_base.Tx) (tenantManagements mysql.TenantManagementList, err error) {
	var (
		cond             = mysql_base.NewCondition()
		tenantManagement mysql.TenantManagement
	)
	err = mysql_base.SearchListGroup(tx, &tenantManagement, &tenantManagements, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) GetById(tx *mysql_base.Tx, id uint64) (tenantManagementList mysql.TenantManagement, err error) {
	var cond = mysql_base.NewCondition()
	var tenantManagement mysql.TenantManagement

	err = mysql_base.MustFirst(tx, &tenantManagement, id, cond)
	if err != nil {
		return
	}
	return
}

func (dao *tenantManagementDao) UpdateDomain(ctx context.Context, tx *mysql_base.Tx, q *structure.FillDomainParam) error {
	var cond = mysql_base.NewCondition()

	// 构建更新数据
	updates := map[string]interface{}{
		"request_domain":        q.RequestDomain,
		"request_domain_prefix": q.RequestDomainPrefix,
	}
	// 批量更新所有记录的domain字段
	err := mysql_base.MustUpdateModelByCond(tx, &mysql.TenantManagement{}, cond, updates)
	if err != nil {
		return err
	}
	return nil
}

func (dao *tenantManagementDao) GetUnbindTenantManagementList(ctx context.Context, tx *mysql_base.Tx, query structure.QYWXGetUnBindTenantManagementListQuery) (list mysql.TenantManagementList, total int, err error) {
	cond := mysql_base.NewCondition()

	if query.QueryStr != "" {
		cond.AddMultiFieldLikeMatch([]string{"tenant_company_name", "tenant_phone_number"}, query.QueryStr)
	}

	cond.AddNotContainMatch("id", query.BoundTenantManagementIds.ToInt())
	total, err = mysql_base.SearchListGroupForPaging(tx, &mysql.TenantManagement{}, query, &list, cond)
	return
}
