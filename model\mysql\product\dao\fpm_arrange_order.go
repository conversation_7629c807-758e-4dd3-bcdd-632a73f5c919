package dao

import (
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	. "hcscm/model/mysql/product"
	structure "hcscm/structure/product"
)

func MustCreateFpmArrangeOrder(tx *mysql_base.Tx, r FpmArrangeOrder) (o FpmArrangeOrder, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateFpmArrangeOrder(tx *mysql_base.Tx, r FpmArrangeOrder) (o FpmArrangeOrder, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteFpmArrangeOrder(tx *mysql_base.Tx, r FpmArrangeOrder) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstFpmArrangeOrderByID(tx *mysql_base.Tx, id uint64) (r FpmArrangeOrder, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func MustFirstFpmArrangeOrderByOrderNo(tx *mysql_base.Tx, orderNo string) (r FpmArrangeOrder, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	cond.AddEqual("order_no", orderNo)
	err = mysql_base.MustFirstByCond(tx, &r, cond)

	return
}

func FirstFpmArrangeOrderByID(tx *mysql_base.Tx, id uint64) (r FpmArrangeOrder, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FirstFpmArrangeOrderByQuery(tx *mysql_base.Tx, q *structure.ExistOrderQuery) (r FpmArrangeOrder, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	if q.SrcId != 0 {
		cond.AddEqual("src_id", q.SrcId)
	}
	cond.AddNotEqual("audit_status", common_system.OrderStatusVoided)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)

	return
}

func FindFpmArrangeOrderByFpmArrangeOrderID(tx *mysql_base.Tx, objects ...interface{}) (o FpmArrangeOrderList, err error) {
	ids := GetFpmArrangeOrderIdList(objects)
	var (
		r    FpmArrangeOrder
		cond = mysql_base.NewCondition()
		list []FpmArrangeOrder
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据源单获取配布单
func FindFpmArrangeOrderBySrcOrderNo(tx *mysql_base.Tx, srcOrderNo string) (o FpmArrangeOrderList, err error) {
	var (
		r    FpmArrangeOrder
		cond = mysql_base.NewCondition()
		list []FpmArrangeOrder
	)
	cond.AddFuzzyMatch("src_order_no", srcOrderNo)
	err = mysql_base.FindByCond(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindFpmArrangeOrderByIDs(tx *mysql_base.Tx, ids []uint64) (o FpmArrangeOrderList, err error) {
	var (
		r    FpmArrangeOrder
		cond = mysql_base.NewCondition()
		list []FpmArrangeOrder
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchFpmArrangeOrder(tx *mysql_base.Tx, q *structure.GetFpmArrangeOrderListQuery) (o FpmArrangeOrderList, count int, err error) {
	var (
		r           FpmArrangeOrder
		cond        = mysql_base.NewCondition()
		list        []FpmArrangeOrder
		groupFields []string
	)
	r.BuildReadCond(tx.Context, cond)
	if q.OrderNo != "" {
		cond.AddFuzzyMatch("order_no", q.OrderNo)
	}
	if q.SrcOrderNo != "" {
		cond.AddFuzzyMatch("src_order_no", q.SrcOrderNo)
	}
	if q.WarehouseId != 0 {
		cond.AddEqual("warehouse_id", q.WarehouseId)
	}
	if q.OutOrderType != 0 {
		cond.AddEqual("out_order_type", q.OutOrderType)
	}
	if !q.ArrangeTimeBegin.IsYMDZero() && !q.ArrangeTimeEnd.IsYMDZero() {
		cond.AddTableBetween(r, "arrange_time", q.ArrangeTimeBegin.StringYMD(), q.ArrangeTimeEnd.StringYMD2DayListTimeYMDHMS())
	}
	if !q.AuditStatus.IsNil() {
		cond.AddTableContainMatch(r, "audit_status", q.AuditStatus.ToUint64())
	} else {
		cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)
	}
	if q.BusinessStatus != 0 {
		cond.AddEqual("business_status", q.BusinessStatus)
	}
	if !q.BusinessStatusIds.IsNil() {
		cond.AddContainMatch("business_status", q.BusinessStatusIds.ToInt())
	}
	if q.VoucherNumber != "" {
		cond.AddFuzzyMatch("voucher_number", q.VoucherNumber)
	}
	if q.SrcId != 0 {
		cond.AddEqual("src_id", q.SrcId)
	}
	groupFields = []string{}
	if q.IsNotPage {
		err = mysql_base.SearchListGroup(tx, &r, &list, cond, groupFields...)
	} else {
		count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	}
	if err != nil {
		return
	}
	o = list
	return
}

// 获取预约生成的配单
func SearchFpmArrangeOrderCrossRvt(tx *mysql_base.Tx, q *structure.GetFpmArrangeOrderListQuery) (o FpmArrangeOrderList, count int, err error) {
	var (
		r           FpmArrangeOrder
		cond        = mysql_base.NewCondition()
		list        []FpmArrangeOrder
		groupFields []string
	)
	groupFields = []string{}
	if q.SrcId > 0 {
		cond.AddTableEqual(r, "src_id", q.SrcId)
	}
	if q.SrcType > 0 {
		cond.AddTableEqual(r, "src_type", q.SrcType)
	}
	if q.JudgeStatus == true {
		cond.AddNotContainMatch("audit_status", common_system.OrderStatusVoided)
	}
	count, err = mysql_base.SearchListGroupForPaging(tx, &r, q, &list, cond, groupFields...)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据源单id列表获取配布单
func FindFpmArrangeOrderBySrcIds(tx *mysql_base.Tx, srcIds []uint64) (o FpmArrangeOrderList, err error) {
	var (
		r    FpmArrangeOrder
		cond = mysql_base.NewCondition()
		list []FpmArrangeOrder
	)

	// 添加src_id条件，查询所有以srcIds为来源的配布单
	cond.AddTableContainMatch(r, "src_id", srcIds)

	// 排除作废的配布单
	cond.AddTableNotEqual(r, "audit_status", common_system.OrderStatusVoided)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}

	o = list
	return
}
