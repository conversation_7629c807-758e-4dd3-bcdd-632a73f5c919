package purchase

import (
	structure_base "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/vars"
)

type AddPurchaseProductReturnOrderItemParamList []AddPurchaseProductReturnOrderItemParam

func (r AddPurchaseProductReturnOrderItemParamList) Adjust() {

}

type AddPurchaseProductReturnOrderItemParam struct {
	structure_base.Param
	ProductPurchaseOrderId uint64 `json:"product_purchase_order_id"` // 成品采购单id
	ProductPurchaseOrderNo string `json:"product_purchase_order_no"` // 成品采购单编号
	ProductId              uint64 `json:"product_id"`                // 成品id
	ProductCode            string `json:"product_code"`              // 成品编号
	ProductName            string `json:"product_name"`              // 成品名称
	CustomerId             uint64 `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64 `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string `json:"product_color_code"`        // 成品色号
	ProductColorName       string `json:"product_color_name"`        // 成品色名
	DyeFactoryDyelotNumber string `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductCraft           string `json:"product_craft"`             // 成品工艺
	ProductLevelId         uint64 `json:"product_level_id"`          // 成品等级
	ProductIngredient      string `json:"product_ingredient"`        // 成品成分
	ProductRemark          string `json:"product_remark"`            // 成品备注
	Remark                 string `json:"remark"`                    // 备注
	ReturnRoll             int    `json:"return_roll"`               // 退货数
	AvgWeight              int    `json:"avg_weight"`                // 均重
	ReturnWeight           int    `json:"return_weight"`             // 退重
	MeasurementUnitId      uint64 `json:"measurement_unit_id"`       // 计量单位
	AuxiliaryUnitId        uint64 `json:"auxiliary_unit_id"`         // 辅助计量单位
	WeightUnitPrice        int    `json:"weight_unit_price"`         // 数量单价（数量单价）
	ReturnLength           int    `json:"return_length"`             // 长度（退货长度）
	LengthUnitPrice        int    `json:"length_unit_price"`         // 长度单价
	ReturnPrice            int    `json:"-"`                         // 退货金额
	SumStockId             uint64 `json:"sum_stock_id"`              // 汇总库存id
}

func (p *AddPurchaseProductReturnOrderItemParam) GetCountWP() {
	// 均重*条数/100
	// p.ReturnWeight = int(tools.DecimalDiv(tools.DecimalMul(float64(p.AvgWeight), float64(p.ReturnRoll)), 100))
	// 单价*数量+长度单价*长度
	p.ReturnPrice = int(tools.DecimalDiv(tools.DecimalMul(float64(p.WeightUnitPrice), float64(p.ReturnWeight)), vars.WeightUnitPriceMult) +
		tools.DecimalDiv(tools.DecimalMul(float64(p.ReturnLength), float64(p.LengthUnitPrice)), vars.LengthUnitPriceMult))
}

type AddPurchaseProductReturnOrderItemData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdatePurchaseProductReturnOrderItemStatusData struct {
	structure_base.ResponseData
}

type GetPurchaseProductReturnOrderItemQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetPurchaseProductReturnOrderItemListQuery struct {
	structure_base.ListQuery
	IsDropList             int             `form:"is_drop_list"`              // 回收站
	ParentId               uint64          `form:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string          `form:"parent_order_no"`           // 父单号(对应的单据号)(对应的单据号)
	ProductPurchaseOrderId uint64          `form:"product_purchase_order_id"` // 成品采购单id
	ProductPurchaseOrderNo string          `form:"product_purchase_order_no"` // 成品采购单编号
	ProductId              uint64          `form:"product_id"`                // 成品id
	ProductCode            string          `form:"product_code"`              // 成品编号
	ProductName            string          `form:"product_name"`              // 成品名称
	CustomerId             uint64          `form:"customer_id"`               // 所属客户id
	ProductColorId         uint64          `form:"product_color_id"`          // 成品颜色id
	ProductColorCode       string          `form:"product_color_code"`        // 成品色号
	ProductColorName       string          `form:"product_color_name"`        // 成品色名
	DyeFactoryDyelotNumber string          `form:"dye_factory_dyelot_number"` // 染厂缸号
	ProductCraft           string          `form:"product_craft"`             // 成品工艺
	ProductLevelId         uint64          `form:"product_level_id"`          // 成品等级
	ProductIngredient      string          `form:"product_ingredient"`        // 成品成分
	ProductRemark          string          `form:"product_remark"`            // 成品备注
	Remark                 string          `form:"remark"`                    // 备注
	ReturnRoll             int             `form:"return_roll"`               // 退货数
	AvgWeight              int             `form:"avg_weight"`                // 均重
	ReturnWeight           int             `form:"return_weight"`             // 退重
	MeasurementUnitId      uint64          `form:"measurement_unit_id"`       // 计量单位
	WeightUnitPrice        int             `form:"weight_unit_price"`         // 数量单价（数量单价）
	ReturnLength           int             `form:"return_length"`             // 长度（退货长度）
	LengthUnitPrice        int             `form:"length_unit_price"`         // 长度单价
	ReturnPrice            int             `form:"return_price"`              // 退货金额
	SumStockId             uint64          `form:"sum_stock_id"`              // 汇总库存id
	IsOutFinish            bool            `form:"is_out_finish"`             // 是否出仓完毕 true false
	BeginTime              tools.QueryTime `form:"begin_time"`                // 开始时间
	EndTime                tools.QueryTime `form:"end_time"`                  // 结束时间
	SaleSystemId           uint64          `form:"sale_system_id"`            // 营销体系id
	SupplierId             uint64          `form:"supplier_id"`               // 供应商id
	IsUseDrop              bool
	ListIds                []uint64
}

func (r GetPurchaseProductReturnOrderItemListQuery) Adjust() {

}

type GetPurchaseProductReturnOrderItemData struct {
	structure_base.RecordData
	ParentId               uint64 `json:"parent_id"`                 // 父id（单号id）
	ParentOrderNo          string `json:"parent_order_no"`           // 父单号(对应的单据号)(对应的单据号)
	ProductPurchaseOrderId uint64 `json:"product_purchase_order_id"` // 成品采购单id
	ProductPurchaseOrderNo string `json:"product_purchase_order_no"` // 成品采购单编号
	ProductId              uint64 `json:"product_id"`                // 成品id
	ProductCode            string `json:"product_code"`              // 成品编号
	ProductName            string `json:"product_name"`              // 成品名称
	CustomerId             uint64 `json:"customer_id"`               // 所属客户id
	ProductColorId         uint64 `json:"product_color_id"`          // 成品颜色id
	ProductColorCode       string `json:"product_color_code"`        // 成品色号
	ProductColorName       string `json:"product_color_name"`        // 成品色名
	DyeFactoryDyelotNumber string `json:"dye_factory_dyelot_number"` // 染厂缸号
	ProductCraft           string `json:"product_craft"`             // 成品工艺
	ProductLevelId         uint64 `json:"product_level_id"`          // 成品等级id
	ProductIngredient      string `json:"product_ingredient"`        // 成品成分
	ProductRemark          string `json:"product_remark"`            // 成品备注
	Remark                 string `json:"remark"`                    // 备注
	ReturnRoll             int    `json:"return_roll"`               // 退货数
	AvgWeight              int    `json:"avg_weight"`                // 均重
	ReturnWeight           int    `json:"return_weight"`             // 退重
	MeasurementUnitId      uint64 `json:"measurement_unit_id"`       // 计量单位
	AuxiliaryUnitId        uint64 `json:"auxiliary_unit_id"`         // 辅助单位
	WeightUnitPrice        int    `json:"weight_unit_price"`         // 数量单价（数量单价）
	ReturnLength           int    `json:"return_length"`             // 长度（退货长度）
	LengthUnitPrice        int    `json:"length_unit_price"`         // 长度单价
	ReturnPrice            int    `json:"return_price"`              // 退货金额
	SumStockId             uint64 `json:"sum_stock_id"`              // 汇总库存id
	OutRoll                int    `json:"out_roll"`                  // 出仓匹数
	OutWeight              int    `json:"out_weight"`                // 出仓数量
	OutLength              int    `json:"out_length"`                // 出仓长度
	IsOutFinish            bool   `json:"is_out_finish"`             // 是否出仓完毕 true false
	AvailableRoll          int    `json:"available_roll"`            // 可用匹数
	AvailableWeight        int    `json:"available_weight"`          // 可用数量

	// 转义
	CustomerName        string `json:"customer_name"`         // 所属客户名
	ProductLevelName    string `json:"product_level_name"`    // 成品等级名
	MeasurementUnitName string `json:"measurement_unit_name"` // 计量单位名
	AuxiliaryUnitName   string `json:"auxiliary_unit_name"`   // 辅助单位名
	StockRoll           int    `json:"stock_roll"`            // 库存数
	EnableOutRoll       int    `json:"enable_out_roll"`       // 可出仓匹数
	EnableOutWeight     int    `json:"enable_out_weight"`     // 可出仓数量
	EnableOutLength     int    `json:"enable_out_length"`     // 可出仓长度

}

type GetPurchaseProductReturnOrderItemDataList []GetPurchaseProductReturnOrderItemData

func (g GetPurchaseProductReturnOrderItemDataList) Adjust() {

}
