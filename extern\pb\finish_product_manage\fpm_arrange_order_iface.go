package finish_product_manage

import (
	"context"
	aggs_product "hcscm/aggs/product"
	product_const "hcscm/common/product"
	"hcscm/extern/pb/product"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	service_product "hcscm/service/product"
	structure_product "hcscm/structure/product"
)

func NewFpmArrangeOrderClient() *fpmArrangeOrderClient {
	return &fpmArrangeOrderClient{}
}

type fpmArrangeOrderClient struct {
}

type FpmArrangeOrderClient interface {
	// 获取
	// 获取汇总缸号库存的那条数据
	IsFromPurchaseProductOrder(ctx context.Context, req product.GetCADRWLParam) (rwl [3]int, err error)
	AddArrangeOrder(ctx context.Context, addOrderParam *structure_product.AddFpmArrangeOrderParam)

	// 根据采购单id获取单号
	GetOrderNoByPurchaseOrderIds(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error)
	// 根据来源ID获取安排单列表
	GetArrangeOrderListBySrcId(ctx context.Context, srcId uint64) (list structure_product.GetFpmArrangeOrderDataList, total int, err error)
}

func (this *fpmArrangeOrderClient) IsFromPurchaseProductOrder(ctx context.Context, req ArrangeOrderQuery) (err error) {
	var (
		arrReq structure_product.GetFpmArrangeOrderListQuery
	)
	arrReq.SrcType = product_const.ArrangeOrderFrom(req.SrcType)
	arrReq.SrcId = req.SrcId
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	arrRepo := aggs_product.NewFpmArrangeOrderRepo(tx)
	err = arrRepo.IsFromPurchaseProductOrder(ctx, arrReq)
	if err != nil {
		return err
	}
	return
}

func (this *fpmArrangeOrderClient) AddArrangeOrder(ctx context.Context, addOrderParam *structure_product.AddFpmArrangeOrderParam) (id uint64, err error) {
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	arrRepo := aggs_product.NewFpmArrangeOrderRepo(tx)
	id, _, err = arrRepo.Add(ctx, addOrderParam)
	if err != nil {
		return
	}
	return
}

// 新增采购收货单
func (this *fpmArrangeOrderClient) AddFPPurchaseReceiveOrder(ctx context.Context, req *structure_product.AddFpmInOrderParam) (id uint64, err error) {
	var svc = service_product.NewFpmInOrderService()
	data, err := svc.Add(ctx, req)
	if err != nil {
		return
	}
	id = data.Id
	return
}

// 根据来源ID获取安排单列表
func (this *fpmArrangeOrderClient) GetArrangeOrderListBySrcId(ctx context.Context, srcId uint64) (list structure_product.GetFpmArrangeOrderDataList, total int, err error) {
	var (
		arrReq           structure_product.GetFpmArrangeOrderListQuery
		arrangeOrderList model.FpmArrangeOrderList
		count            int
	)

	// 设置查询参数
	// 源ID必填，不需要指定源类型
	arrReq.SrcId = srcId

	// 使用从库事务
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)

	// 直接调用DAO层的SearchFpmArrangeOrderCrossRvt方法获取安排单列表
	arrangeOrderList, count, err = mysql.SearchFpmArrangeOrderCrossRvt(tx, &arrReq)
	if err != nil {
		return
	}

	// 将模型数据转换为返回结构
	list = make(structure_product.GetFpmArrangeOrderDataList, 0, len(arrangeOrderList))
	for _, arrangeOrder := range arrangeOrderList {
		var data structure_product.GetFpmArrangeOrderData

		// 基本字段赋值
		data.Id = arrangeOrder.Id
		data.OrderNo = arrangeOrder.OrderNo
		data.SrcType = arrangeOrder.SrcType
		data.SrcId = arrangeOrder.SrcId
		data.SrcOrderNo = arrangeOrder.SrcOrderNo
		data.OutOrderType = arrangeOrder.OutOrderType
		data.BusinessStatus = arrangeOrder.BusinessStatus
		data.SaleSystemId = arrangeOrder.SaleSystemId
		data.WarehouseId = arrangeOrder.WarehouseId
		data.ArrangeToWarehouseId = arrangeOrder.ArrangeToWarehouseId
		data.BizUnitId = arrangeOrder.BizUnitId
		data.ProcessFactoryId = arrangeOrder.ProcessFactoryId
		data.TotalRoll = arrangeOrder.TotalRoll
		data.TotalWeight = arrangeOrder.TotalWeight
		data.TotalLength = arrangeOrder.TotalLength
		data.UnitId = arrangeOrder.UnitId
		data.AuditStatus = arrangeOrder.AuditStatus

		// 如果需要获取详细信息，可以在这里补充其他字段的赋值
		// 比如获取安排单详情、相关的单位名称等

		list = append(list, data)
	}

	total = count
	return
}

// 根据采购单id获取单号
func (this *fpmArrangeOrderClient) GetOrderNoByPurchaseOrderIds(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error) {
	purchaseReceiveSvc := service_product.NewFpmInOrderService()
	orderNos, exist, err = purchaseReceiveSvc.Exist(ctx, ids)
	if err != nil {
		return
	}
	return
}
