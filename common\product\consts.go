package product

// 出仓类型  回修出仓（生成的为加工出仓）
//
//go:generate stringer -type=WarehouseGoodOutType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodOutType
type WarehouseGoodOutType int

const (
	WarehouseGoodOutTypeInternalAllocate WarehouseGoodOutType = 1  // 内部调拨出仓
	WarehouseGoodOutTypeSaleAllocate     WarehouseGoodOutType = 2  // 销售调拨出仓
	WarehouseGoodOutTypeSale             WarehouseGoodOutType = 3  // 销售出仓
	WarehouseGoodOutTypePurchaseReturn   WarehouseGoodOutType = 4  // 采购退货出仓
	WarehouseGoodOutTypeProcess          WarehouseGoodOutType = 5  // 正常加工出仓
	WarehouseGoodOutTypeOther            WarehouseGoodOutType = 6  // 其他出仓
	WarehouseGoodOutTypeDeduction        WarehouseGoodOutType = 7  // 扣款出仓
	WarehouseGoodOutTypeTypeRepair       WarehouseGoodOutType = 8  // 回修加工出仓
	WarehouseGoodOutTypeCheck            WarehouseGoodOutType = 9  // 盘点出仓
	WarehouseGoodOutTypeAdjust           WarehouseGoodOutType = 10 // 调整出仓
)

// 出仓类型  回修出仓（生成的为加工出仓）(报表用)
//
//go:generate stringer -type=WarehouseGoodForReportOutType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodForReportOutType
type WarehouseGoodForReportOutType int

const (
	WarehouseGoodForReportOutTypeInternalAllocate WarehouseGoodForReportOutType = 1  // 内部调拨
	WarehouseGoodForReportOutTypeSaleAllocate     WarehouseGoodForReportOutType = 2  // 销售调拨
	WarehouseGoodForReportOutTypeSale             WarehouseGoodForReportOutType = 3  // 销售
	WarehouseGoodForReportOutTypePurchaseReturn   WarehouseGoodForReportOutType = 4  // 采购退货
	WarehouseGoodForReportOutTypeProcess          WarehouseGoodForReportOutType = 5  // 加工
	WarehouseGoodForReportOutTypeOther            WarehouseGoodForReportOutType = 6  // 其他
	WarehouseGoodForReportOutTypeDeduction        WarehouseGoodForReportOutType = 7  // 扣款
	WarehouseGoodForReportOutTypeTypeRepair       WarehouseGoodForReportOutType = 8  // 回修
	WarehouseGoodForReportOutTypeCheck            WarehouseGoodForReportOutType = 9  // 盘点
	WarehouseGoodForReportOutTypeAdjust           WarehouseGoodForReportOutType = 10 // 调整
)

// 进仓类型
//
//go:generate stringer -type=WarehouseGoodInType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodInType
type WarehouseGoodInType int

const (
	WarehouseGoodInTypeInternalAllocate WarehouseGoodInType = 1  // 内部调拨进仓单
	WarehouseGoodInTypeSaleAllocate     WarehouseGoodInType = 2  // 销售调拨进仓单
	WarehouseGoodInTypeSaleReturn       WarehouseGoodInType = 3  // 销售退货进仓单
	WarehouseGoodInTypePurchase         WarehouseGoodInType = 4  // 采购进仓单
	WarehouseGoodInTypeProcess          WarehouseGoodInType = 5  // 加工进仓单
	WarehouseGoodInTypeOther            WarehouseGoodInType = 6  // 其他进仓单
	WarehouseGoodInTypeCheck            WarehouseGoodInType = 7  // 盘点进仓单
	WarehouseGoodInTypeAdjust           WarehouseGoodInType = 8  // 调整进仓单
	WarehouseGoodInTypeProcessReturn    WarehouseGoodInType = 9  // 加工退货进仓单
	WarehouseGoodInTypeExport           WarehouseGoodInType = 10 // 期初导入进仓单
)

// 进仓类型(报表用)
//
//go:generate stringer -type=WarehouseGoodForReportInType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodForReportInType
type WarehouseGoodForReportInType int

const (
	WarehouseGoodForReportInTypeInternalAllocate WarehouseGoodForReportInType = 1  // 内部调拨
	WarehouseGoodForReportInTypeSaleAllocate     WarehouseGoodForReportInType = 2  // 销售调拨
	WarehouseGoodForReportInTypeSaleReturn       WarehouseGoodForReportInType = 3  // 销售退货
	WarehouseGoodForReportInTypePurchase         WarehouseGoodForReportInType = 4  // 采购
	WarehouseGoodForReportInTypeProcess          WarehouseGoodForReportInType = 5  // 加工
	WarehouseGoodForReportInTypeOther            WarehouseGoodForReportInType = 6  // 其他
	WarehouseGoodForReportInTypeCheck            WarehouseGoodForReportInType = 7  // 盘点
	WarehouseGoodForReportInTypeAdjust           WarehouseGoodForReportInType = 8  // 调整
	WarehouseGoodForReportInTypeProcessReturn    WarehouseGoodForReportInType = 9  // 加工退货
	WarehouseGoodForReportInTypeExport           WarehouseGoodForReportInType = 10 // 期初导入
)

// 订单状态
//
//go:generate stringer -type=AuditStatus --linecomment
//go:generate generate --file=consts.go  --type=AuditStatus
type AuditStatus int

const (
	AuditStatusWaiting AuditStatus = 1 // 待审核
	AuditStatusPass    AuditStatus = 2 // 已审核
	AuditStatusReject  AuditStatus = 3 // 驳回
	AuditStatusCancel  AuditStatus = 4 // 作废
)

// 业务关闭
//
//go:generate stringer -type=BusinessClose --linecomment
//go:generate generate --file=consts.go  --type=BusinessClose
type BusinessClose int

const (
	BusinessCloseNo  BusinessClose = 1 // 开启
	BusinessCloseYes BusinessClose = 2 // 关闭
)

// 配布单生成来源
//
//go:generate stringer -type=ArrangeOrderFrom --linecomment
//go:generate generate --file=consts.go  --type=ArrangeOrderFrom
type ArrangeOrderFrom int

const (
	ArrangeOrderFromReservationOrder ArrangeOrderFrom = 1 // 预约单
	ArrangeOrderFromSaleOrder        ArrangeOrderFrom = 2 // 成品销售单
	ArrangeOrderFromPurchaseReturn   ArrangeOrderFrom = 3 // 成品销售退货单
)

// 业务状态
//
//go:generate stringer -type=BusinessStatus --linecomment
//go:generate generate --file=consts.go  --type=BusinessStatus
type BusinessStatus int

const (
	BusinessStatusArrangeWait           BusinessStatus = 1 // 待配布
	BusinessStatusArrangeInProgress     BusinessStatus = 2 // 配布中
	BusinessStatusArrangeAllready       BusinessStatus = 3 // 已配布
	BusinessStatusArrangeWaitOut        BusinessStatus = 4 // 待出仓
	BusinessStatusArrangeOuting         BusinessStatus = 5 // 可出仓
	BusinessStatusArrangeOuted          BusinessStatus = 6 // 已出仓
	BusinessStatusArrangeCancel         BusinessStatus = 7 // 已作废
	BusinessStatusArrangeSaleAlloFinish BusinessStatus = 8 // 已销调
)

// 出仓类型枚举使用
//
//go:generate stringer -type=WarehouseGoodOutEnumType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodOutEnumType
type WarehouseGoodOutEnumType int

const (
	WarehouseGoodOutEnumTypeInternalAllocate WarehouseGoodOutEnumType = 1 // 内部调拨出仓
	WarehouseGoodOutEnumTypeProcess          WarehouseGoodOutEnumType = 5 // 加工出仓
	WarehouseGoodOutEnumTypeOther            WarehouseGoodOutEnumType = 6 // 其他出仓
	WarehouseGoodOutEnumTypeDeduction        WarehouseGoodOutEnumType = 7 // 扣款出仓
	WarehouseGoodOutEnumTypeRepair           WarehouseGoodOutEnumType = 8 // 回修出仓
)

// 显示方式
//
//go:generate stringer -type=StockShowType --linecomment
//go:generate generate --file=consts.go  --type=StockShowType
type StockShowType int

const (
	StockShowTypeAll    StockShowType = 1 // 全部
	StockShowTypeRoll   StockShowType = 2 // 匹数和数量库存不为0
	StockShowTypeWeight StockShowType = 3 // 匹数为0数量不为0
	StockShowTypeZero   StockShowType = 4 // 匹数为0数量为0
)

// 变更单用的出仓类型枚举
//
//go:generate stringer -type=WarehouseGoodOutChangeType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodOutChangeType
type WarehouseGoodOutChangeType int

const (
	WarehouseGoodOutChangeTypeOutSaleAllocate WarehouseGoodOutChangeType = 2 // 销售调拨出仓
	WarehouseGoodOutChangeTypeOutSale         WarehouseGoodOutChangeType = 3 // 销售出仓
)

// 进出仓大类型枚举
//
//go:generate stringer -type=WarehouseGoodType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseGoodType
type WarehouseGoodType int

const (
	WarehouseGoodTypeIn    WarehouseGoodType = 1 // 进仓
	WarehouseGoodTypeOut   WarehouseGoodType = 2 // 出仓
	WarehouseGoodTypeCheck WarehouseGoodType = 3 // 盘点
)

// // 库存类型
// //
// //go:generate stringer -type=StockType --linecomment
// //go:generate generate --file=consts.go  --type=StockType
// type StockType int
//
// const (
//	StockTypeDyeing StockType = 1 // 在染半成品库存
//	StockTypeRework StockType = 2 // 返工布库存
// )

// 库存类型
//
//go:generate stringer -type=StockTypeDyeingV2 --linecomment
//go:generate generate --file=consts.go  --type=StockTypeDyeingV2
type StockTypeDyeingV2 int

const (
	StockTypeDyeingV2ingDnf StockTypeDyeingV2 = 1 // 在染-染整
	StockTypeDyeingV2Redye  StockTypeDyeingV2 = 2 // 在染-复色
)

// 返工布类型
//
//go:generate stringer -type=StockTypeReworkV2 --linecomment
//go:generate generate --file=consts.go  --type=StockTypeReworkV2
type StockTypeReworkV2 int

const (
	StockTypeReworkV2ProcessingUnhandle StockTypeReworkV2 = 3 // 返工布-加工-未处理
	StockTypeReworkV2ProcessingHandling StockTypeReworkV2 = 4 // 返工布-加工-在处理
	StockTypeReworkV2ProcessingRedye    StockTypeReworkV2 = 5 // 返工布-加工-复色
	StockTypeReworkV2RepairUnhandle     StockTypeReworkV2 = 6 // 返工布-回修-未处理
	StockTypeReworkV2RepairHandling     StockTypeReworkV2 = 7 // 返工布-回修-在处理
	StockTypeReworkV2RepairRedye        StockTypeReworkV2 = 8 // 返工布-回修-复色
)

// 加工出仓类型
//
//go:generate stringer -type=ProcessOutType --linecomment
//go:generate generate --file=consts.go  --type=ProcessOutType
type ProcessOutType int

const (
	ProcessOutTypeOrdinaryProcess   ProcessOutType = 5 // 正常加工出仓
	ProcessOutTypeTypeRepairProcess ProcessOutType = 8 // 回修加工出仓
)

// 加工出仓类型(染整那边的定常)
//
//go:generate stringer -type=SrcTypeFabric --linecomment
//go:generate generate --file=consts.go  --type=SrcTypeFabric
type SrcTypeFabric int

const (
	SrcTypeFabricEnterStock    SrcTypeFabric = 11 // 成品加工进仓单
	SrcTypeFabricOutStock      SrcTypeFabric = 12 // 成品加工出仓单
	SrcTypeFabricRtnEnterStock SrcTypeFabric = 13 // 成品加工退货进仓单
)

// 染整通知单类型
//
//go:generate stringer -type=DyeOrderType --linecomment
//go:generate generate --file=consts.go  --type=DyeOrderType
type DyeOrderType int

const (
	DyeOrderTypeDNF       DyeOrderType = 1 // 染整
	DyeOrderTypeRedye     DyeOrderType = 2 // 复色
	DyeOrderTypeFinishing DyeOrderType = 3 // 后整
)

// 染整通知单类型
//
//go:generate stringer -type=DnfType --linecomment
//go:generate generate --file=consts.go  --type=DnfType
type DnfType int

const (
	DnfTypeGreyDnf           DnfType = 1 // 坯布染整
	DnfTypeProductProcessing DnfType = 2 // 成品加工
	DnfTypeProductRepair     DnfType = 3 // 成品回修
)

// 库存状态
//
//go:generate stringer -type=QualityCheckStatus --linecomment
//go:generate generate --file=consts.go  --type=QualityCheckStatus
type QualityCheckStatus int

const (
	QualityCheckStatusWait QualityCheckStatus = 1 // 未质检
	QualityCheckStatusDone QualityCheckStatus = 2 // 已质检
)

// 操作判断类型 1录入 2整缸录入 3删除
//
//go:generate stringer -type=ArrangeType --linecomment
//go:generate generate --file=consts.go  --type=ArrangeType
type ArrangeType int

const (
	ArrangeTypeIn    ArrangeType = 1 // 录入
	ArrangeTypeDyeIn ArrangeType = 2 // 整缸录入
	ArrangeTypeDel   ArrangeType = 3 // 删除
)

// 移架类型 1单匹移架 2整缸移架 3整架移架
//
//go:generate stringer -type=MoveType --linecomment
//go:generate generate --file=consts.go  --type=MoveType
type MoveType int

const (
	MoveTypeOne MoveType = 1 // 单匹
	MoveTypeDye MoveType = 2 // 整缸
	MoveTypeBin MoveType = 3 // 整架
)

// 库存移架类型 1手动移架 2盘点移架 3盘点回滚移架
//
//go:generate stringer -type=WarehouseBinMoveLogType --linecomment
//go:generate generate --file=consts.go  --type=WarehouseBinMoveLogType
type WarehouseBinMoveLogType int

const (
	WarehouseBinMoveLogTypeManual        WarehouseBinMoveLogType = 1 // 手动移架
	WarehouseBinMoveLogTypeCheck         WarehouseBinMoveLogType = 2 // 盘点移架
	WarehouseBinMoveLogTypeCheckRollBack WarehouseBinMoveLogType = 3 // 盘点回滚移架
)

// 盘点状态（盈亏平）
//
//go:generate stringer -type=CheckStatus --linecomment
//go:generate generate --file=consts.go  --type=CheckStatus
type CheckStatus int

const (
	CheckStatusProfit CheckStatus = 1 // 盈
	CheckStatusLose   CheckStatus = 2 // 亏
	CheckStatusFlat   CheckStatus = 3 // 平
)

// 布匹码状态
//
//go:generate stringer -type=FabricPieceCodeStatus --linecomment
//go:generate generate --file=consts.go  --type=FabricPieceCodeStatus
type FabricPieceCodeStatus int

const (
	FabricPieceCodeStatusEnable       FabricPieceCodeStatus = 0 // 生效
	FabricPieceCodeStatusReturnCancel FabricPieceCodeStatus = 1 // 退货换标作废
)

// 是否生成质检报告
//
//go:generate stringer -type=GenerateReportStatus --linecomment
//go:generate generate --file=consts.go  --type=GenerateReportStatus
type GenerateReportStatus int

const (
	GenerateReportStatusNotGen GenerateReportStatus = 0 // 未生成
	GenerateReportStatusIsGen  GenerateReportStatus = 1 // 已生成
)

// 报表类型
//
//go:generate stringer -type=FinishReportReqType --linecomment
//go:generate generate --file=consts.go  --type=FinishReportReqType
type FinishReportReqType int

const (
	FinishReportReqTypeSummaryReport      FinishReportReqType = 1 // 成品布种汇总
	FinishReportReqTypeDetailReport       FinishReportReqType = 2 // 成品颜色汇总
	FinishReportReqTypeDyelotNumberReport FinishReportReqType = 3 // 成品缸号汇总
	FinishReportReqTypeItemFcReport       FinishReportReqType = 4 // 成品细码报表

	FinishReportReqTypeMonthDetail       FinishReportReqType = 11 // 月报表明细
	FinishReportReqTypeMonthDyelotNumber FinishReportReqType = 12 // 月报表缸号
	FinishReportReqTypeMonthProductColor FinishReportReqType = 13 // 月报表色号
	FinishReportReqTypeMonthProduct      FinishReportReqType = 14 // 月报表面料
)

// 配布订单
//
//go:generate stringer -type=ArrangeOrderStatus --linecomment
//go:generate generate --file=consts.go  --type=ArrangeOrderStatus
type ArrangeOrderStatus int

const (
	OutStockArrangeOrderStatus ArrangeOrderStatus = 1 // 出库配布单-出货
)

// 月报表类型
//
//go:generate stringer -type=StockSettlePointType --linecomment
//go:generate generate --file=consts.go  --type=StockSettlePointType
type StockSettlePointType int

const (
	StockSettlePointTypeDetail       StockSettlePointType = 1 // 明细
	StockSettlePointTypeDyelotNumber StockSettlePointType = 2 // 缸号
	StockSettlePointTypeProductColor StockSettlePointType = 3 // 色号
	StockSettlePointTypeProduct      StockSettlePointType = 4 // 面料
)

//go:generate stringer -type=OrderType --linecomment
//go:generate generate --file=consts.go  --type=OrderType
type OrderType int

const (
	OrderTypeInInternalAllocate  OrderType = 1  // 内部调拨进仓单
	OrderTypeInSaleAllocate      OrderType = 2  // 销售调拨进仓单
	OrderTypeInSaleReturn        OrderType = 3  // 销售退货进仓单
	OrderTypeInPurchase          OrderType = 4  // 采购进仓单
	OrderTypeInProcess           OrderType = 5  // 加工进仓单
	OrderTypeInOther             OrderType = 6  // 其他进仓单
	OrderTypeInProcessReturn     OrderType = 7  // 加工退货进仓单
	OrderTypeOutInternalAllocate OrderType = 8  // 内部调拨出仓单
	OrderTypeOutSaleAllocate     OrderType = 9  // 销售调拨出仓单
	OrderTypeOutSale             OrderType = 10 // 销售出仓单
	OrderTypeOutPurchaseReturn   OrderType = 11 // 采购退货出仓单
	OrderTypeOutProcess          OrderType = 12 // 加工出仓单
	OrderTypeOutOther            OrderType = 13 // 其他出仓单
	OrderTypeOutDeduction        OrderType = 14 // 扣款出仓单
	OrderTypeOutTypeRepair       OrderType = 15 // 回修出仓单
	OrderTypeCheck               OrderType = 16 // 盘点单
	OrderTypeAdjust              OrderType = 17 // 调整单
)
