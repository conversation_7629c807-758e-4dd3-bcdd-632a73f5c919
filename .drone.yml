kind: pipeline
type: docker
name: 测试环境

workspace:
  path: mrcuix

# 挂载 host 缓存目录
volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache
  - name: docker_sock
    host:
      path: /var/run/docker.sock

clone:
  depth: 20
  disable: false # 启用代码拉取


steps:

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules
        - ./go/pkg/mod

  - name: build
    image: golang:1.23-alpine
    pull: if-not-exists
    environment:
      DRONE_CPU_QUOTA: "50000" # 设置CPU配额为50000微秒每周期
    commands:
      - export GOPATH=/drone/src/mrcuix/go
      - go env -w GOPROXY=https://goproxy.cn,direct
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - echo $(date)
      - GOOS=linux GOARCH=amd64 go build -ldflags "-X 'main.Branch=${DRONE_BRANCH}' -X 'main.Tag=${DRONE_TAG}' -X 'main.CommitSha=${DRONE_COMMIT_SHA:0:8}' -X 'main.BuildTime=`date`'" --tags netgo -o hcscm main.go
      - chmod +x upx
      - ./upx -9 hcscm

  - name: build-image
    image: hub.zzfzyc.com/library/plugins/docker:v1.1-self
    pull: if-not-exists
    volumes:
      - name: docker_sock
        path: /var/run/docker.sock
    settings:
      dockerfile: Dockerfile
      context: .
      repo: hub.zzfzyc.com/hcscm/hcscm-${DRONE_BRANCH}
      tags: ${DRONE_COMMIT_SHA:0:8}
      registry: hub.zzfzyc.com
      username:
        from_secret: HARBOR_USERNAME
      password:
        from_secret: HARBOR_PASSWORD
      purge: true

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules
        - ./go/pkg/mod


  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: TEST_TARGET_PATH

      source:
        - hcscm
        - ./docs/sql
      overwrite: true

  - name: deploy
    image: appleboy/drone-ssh:v1.0
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: TEST_TARGET_PATH
      script:
        - cd /data/docker_file/hcscm/
        - chmod +x hcscm
        - cp hcscm hcscm.bak
        - docker cp config.yaml hcscm:/data/.
        - docker cp docs/sql hcscm:/data/.
        - rm docs/sql -r
        - docker cp hcscm hcscm:/data/.
        - docker stop hcscm
        - docker start hcscm

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: be93bb71-e5a3-4e7a-a4d7-26358c0b44da
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}} 
        {{- $commit_sort := substr 0 7 .Build.Commit -}}
        {{- if eq .Build.Branch "test"}}
        {{ $env = list "后端测试环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "main"}}
        {{ $env = list "后端正式环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "pre"}}
        {{ $env = list "后端预发布环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "kdyb"}}
        {{ $env = list "后端科顿易布环境" .Build.Branch $commit_sort }}
        {{end}}

        {{- if ne .Build.Tag ""}}
        {{ $env = list "后端生产环境" .Build.Tag }}
        {{end}}

        {{if eq .Build.Status "success"}}
        {{ $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{ $env }} - <font color="red">**[失败]**</font>
        {{- end}}   

        仓库:`{{ .Repo.Namespace }}/{{ .Repo.Name }}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        标签: [{{ .Build.Tag }}-{{ .Build.Commit | substr 0 7 }}]({{.Build.Link}})
        {{if eq .Build.Tag ""}}
        构建人: {{ .Build.AuthorName }}({{ .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{ .Build.Author }}({{ .Build.AuthorEmail }})
        {{- end -}}
        {{ $cst_zone := (now | dateModify "8h")}}
        发布时间: <font color="comment">{{ dateInZone "2006-01-02 15:04:05" ($cst_zone) "UTC"}}</font>
        耗时: {{ .Build.Created | sub .Build.Finished | duration }}
        执行编号: {{ .Build.Number }}

        > <font color="comment">{{ .Build.Message | replace "'" "" }}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - test
  event:
    - push

---

kind: pipeline
type: docker
name: 预发布环境

workspace:
  path: mrcuix

# 挂载 host 缓存目录
volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache
  - name: docker_sock
    host:
      path: /var/run/docker.sock

clone:
  depth: 20
  disable: false # 启用代码拉取


steps:

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules
        - ./go/pkg/mod

  - name: build
    image: golang:1.23-alpine
    pull: if-not-exists
    environment:
      DRONE_CPU_QUOTA: "50000" # 设置CPU配额为50000微秒每周期
    commands:
      - export GOPATH=/drone/src/mrcuix/go
      - go env -w GOPROXY=https://goproxy.cn,direct
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - echo $(date)
      - GOOS=linux GOARCH=amd64 go build -ldflags "-X 'main.Branch=${DRONE_BRANCH}' -X 'main.Tag=${DRONE_TAG}' -X 'main.CommitSha=${DRONE_COMMIT_SHA:0:8}' -X 'main.BuildTime=`date`'" --tags netgo -o hcscm main.go
      - chmod +x upx
      - ./upx -9 hcscm

  - name: build-image
    image: hub.zzfzyc.com/library/plugins/docker:v1.1-self
    pull: if-not-exists
    volumes:
      - name: docker_sock
        path: /var/run/docker.sock
    settings:
      dockerfile: Dockerfile
      context: .
      repo: hub.zzfzyc.com/hcscm/hcscm-${DRONE_BRANCH}
      tags: ${DRONE_COMMIT_SHA:0:8}
      registry: hub.zzfzyc.com
      username:
        from_secret: HARBOR_USERNAME
      password:
        from_secret: HARBOR_PASSWORD

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules
        - ./go/pkg/mod


  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: PRE_TARGET_PATH

      source:
        - hcscm
        - ./docs/sql
      overwrite: true

  - name: deploy
    image: appleboy/drone-ssh:v1.0
    settings:
      host:
        from_secret: TEST_HOST
      username:
        from_secret: TEST_USERNAME
      password:
        from_secret: TEST_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: PRE_TARGET_PATH
      script:
        - |
          cd /data/docker_file/hcscm_pre/
          chmod +x hcscm
          cp hcscm hcscm.bak
          docker cp config.yaml hcscm_pre:/data/.
          docker cp docs/sql hcscm_pre:/data/.
          rm docs/sql -r
          docker cp hcscm hcscm_pre:/data/.
          docker stop hcscm_pre
          docker start hcscm_pre

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: be93bb71-e5a3-4e7a-a4d7-26358c0b44da
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}} 
        {{- $commit_sort := substr 0 7 .Build.Commit -}}
        {{- if eq .Build.Branch "test"}}
        {{ $env = list "后端测试环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "main"}}
        {{ $env = list "后端正式环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "pre"}}
        {{ $env = list "后端预发布环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "kdyb"}}
        {{ $env = list "后端科顿易布环境" .Build.Branch $commit_sort }}
        {{end}}

        {{- if ne .Build.Tag ""}}
        {{ $env = list "后端生产环境" .Build.Tag }}
        {{end}}

        {{if eq .Build.Status "success"}}
        {{ $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{ $env }} - <font color="red">**[失败]**</font>
        {{- end}}   

        仓库:`{{ .Repo.Namespace }}/{{ .Repo.Name }}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        标签: [{{ .Build.Tag }}-{{ .Build.Commit | substr 0 7 }}]({{.Build.Link}})
        {{if eq .Build.Tag ""}}
        构建人: {{ .Build.AuthorName }}({{ .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{ .Build.Author }}({{ .Build.AuthorEmail }})
        {{- end -}}
        {{ $cst_zone := (now | dateModify "8h")}}
        发布时间: <font color="comment">{{ dateInZone "2006-01-02 15:04:05" ($cst_zone) "UTC"}}</font>
        耗时: {{ .Build.Created | sub .Build.Finished | duration }}
        执行编号: {{ .Build.Number }}

        > <font color="comment">{{ .Build.Message | replace "'" "" }}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - pre
  event:
    - push
---

kind: pipeline
type: docker
name: 科顿易布环境

workspace:
  path: mrcuix

# 挂载 host 缓存目录
volumes:
  - name: go_cache
    host:
      path: /data/drone_cache/go_cache
  - name: docker_sock
    host:
      path: /var/run/docker.sock

clone:
  depth: 20
  disable: false # 启用代码拉取


steps:

  - name: restore-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      restore: true
      mount:
        - ./.npm-cache
        - ./node_modules
        - ./go/pkg/mod

  - name: build
    image: golang:1.23-alpine
    pull: if-not-exists
    environment:
      DRONE_CPU_QUOTA: "50000" # 设置CPU配额为50000微秒每周期
    commands:
      - export GOPATH=/drone/src/mrcuix/go
      - go env -w GOPROXY=https://goproxy.cn,direct
      - echo ${DRONE_BRANCH}
      - echo ${DRONE_TAG}
      - echo ${DRONE_COMMIT}
      - echo ${DRONE_COMMIT:0-7}
      - echo $(date)
      - GOOS=linux GOARCH=amd64 go build -ldflags "-X 'main.Branch=${DRONE_BRANCH}' -X 'main.Tag=${DRONE_TAG}' -X 'main.CommitSha=${DRONE_COMMIT_SHA:0:8}' -X 'main.BuildTime=`date`'" --tags netgo -o hcscm main.go
      - chmod +x upx
      - ./upx -9 hcscm

  - name: build-image
    image: hub.zzfzyc.com/library/plugins/docker:v1.1-self
    pull: if-not-exists
    volumes:
      - name: docker_sock
        path: /var/run/docker.sock
    settings:
      dockerfile: Dockerfile
      context: .
      repo: hub.zzfzyc.com/hcscm/hcscm-${DRONE_BRANCH}
      tags: ${DRONE_COMMIT_SHA:0:8}
      registry: hub.zzfzyc.com
      username:
        from_secret: HARBOR_USERNAME
      password:
        from_secret: HARBOR_PASSWORD

  - name: rebuild-cache
    image: drillster/drone-volume-cache:v1.0
    volumes:
      - name: go_cache
        path: /cache
    settings:
      rebuild: true
      mount:
        - ./.npm-cache
        - ./node_modules
        - ./go/pkg/mod


  - name: scp files
    image: appleboy/drone-scp
    pull: if-not-exists
    settings:
      host:
        from_secret: KDYB_HOST
      username:
        from_secret: KDYB_USERNAME
      password:
        from_secret: KDYB_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: KDYB_TARGET_PATH

      source:
        - hcscm
        - ./docs/sql
      overwrite: true

  - name: deploy
    image: appleboy/drone-ssh:v1.0
    settings:
      host:
        from_secret: KDYB_HOST
      username:
        from_secret: KDYB_USERNAME
      password:
        from_secret: KDYB_PASSWORD
      port:
        from_secret: SSH_PORT
      target:
        from_secret: KDYB_TARGET_PATH
      script:
        - |
          cd /data/docker_file/hcscm/
          chmod +x hcscm
          cp hcscm hcscm.bak
          docker cp config.yaml hcscm_test:/data/.
          docker cp docs/sql hcscm_test:/data/.
          rm docs/sql -r
          docker cp hcscm hcscm_test:/data/.
          docker stop hcscm_test
          docker start hcscm_test

  - name: 企业微信通知
    image: lazytim/drone-wechat-robot:v1.0
    settings:
      key: be93bb71-e5a3-4e7a-a4d7-26358c0b44da
      msgtype: markdown.template
      raw: true
      content: |
        {{- $env :="" -}} 
        {{- $commit_sort := substr 0 7 .Build.Commit -}}
        {{- if eq .Build.Branch "test"}}
        {{ $env = list "后端测试环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "main"}}
        {{ $env = list "后端正式环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "pre"}}
        {{ $env = list "后端预发布环境" .Build.Branch $commit_sort }}
        {{else if eq .Build.Branch "kdyb"}}
        {{ $env = list "后端科顿易布环境" .Build.Branch $commit_sort }}
        {{end}}

        {{- if ne .Build.Tag ""}}
        {{ $env = list "后端生产环境" .Build.Tag }}
        {{end}}

        {{if eq .Build.Status "success"}}
        {{ $env }} - <font color="info">**[成功]**</font>
        {{- else -}}
        {{ $env }} - <font color="red">**[失败]**</font>
        {{- end}}   

        仓库:`{{ .Repo.Namespace }}/{{ .Repo.Name }}`
        分支: <font color="comment">{{.Build.Branch}}</font>
        标签: [{{ .Build.Tag }}-{{ .Build.Commit | substr 0 7 }}]({{.Build.Link}})
        {{if eq .Build.Tag ""}}
        构建人: {{ .Build.AuthorName }}({{ .Build.AuthorEmail }})
        {{- else -}}
        构建人: {{ .Build.Author }}({{ .Build.AuthorEmail }})
        {{- end -}}
        {{ $cst_zone := (now | dateModify "8h")}}
        发布时间: <font color="comment">{{ dateInZone "2006-01-02 15:04:05" ($cst_zone) "UTC"}}</font>
        耗时: {{ .Build.Created | sub .Build.Finished | duration }}
        执行编号: {{ .Build.Number }}

        > <font color="comment">{{ .Build.Message | replace "'" "" }}</font>
    when:
      status:
        - success
        - failure

trigger:
  branch:
    - kdyb
  event:
    - push

