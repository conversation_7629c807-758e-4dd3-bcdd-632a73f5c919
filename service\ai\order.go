package ai

import (
	"context"
	"encoding/json"
	errors2 "errors"
	aggsInfoBasic "hcscm/aggs/basic_data/info_basic_data"
	aggsBizUnit "hcscm/aggs/biz_unit"
	aggsEmployee "hcscm/aggs/employee"
	aggsProduct "hcscm/aggs/product"
	aggsSale "hcscm/aggs/sale"
	aggsShouldCollectOrder "hcscm/aggs/should_collect_order"
	"hcscm/common/errors"
	commonSale "hcscm/common/sale"
	"hcscm/config"
	entityEmployee "hcscm/domain/employee/entity"
	pbProduct "hcscm/extern/pb/basic_data/product"
	mysqlBizUnit "hcscm/model/mysql/biz_unit"
	"hcscm/model/mysql/mysql_base"
	product "hcscm/model/mysql/product"
	mysqlShouldCollectOrder "hcscm/model/mysql/should_collect_order"
	mysqlSystem "hcscm/model/mysql/system"
	ai "hcscm/structure/ai"
	structureInfoBasic "hcscm/structure/basic_data/info_basic_data"
	structureProduct "hcscm/structure/product"
	structureSale "hcscm/structure/sale"
	structureShouldCollectOrder "hcscm/structure/should_collect_order"
	structureBase "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/set"
	"strconv"
	"time"
)

type AiService struct {
}

func NewAiService() *AiService {
	return &AiService{}
}

// AiOrderHandler ai智能下单处理
func (s *AiService) AiOrderHandler(content string) (order ai.AiOrder, err error) {
	err = json.Unmarshal([]byte(content), &order)
	if err != nil {
		return
	}
	return
}

// AiGetSaleProductDetails ai获取成品销售单详情
func (s *AiService) AiGetSaleProductDetails(ctx context.Context, req ai.AiRequest) (resp ai.AiGetSaleProductOrderDetailResponse, err error) {
	var (
		aiChatResp     ai.AiChatResponse
		order          ai.AiOrder
		customers      []*mysqlBizUnit.BizUnit
		saleSystemRels mysqlBizUnit.BizUnitSaleSystemRelList
		bizUnitSale    = new(mysqlBizUnit.BizUnitSale)
		saleGroup      = new(mysqlBizUnit.SaleGroup)
		orderFollower  = new(entityEmployee.Employee)
		seller         = new(entityEmployee.Employee)
		saleSystem     mysqlSystem.SaleSystem
		saleShipments  structureInfoBasic.GetSaleShipmentDataList
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	aiChatResp, err = s.FastGptChat(config.Conf.FastGpt.AiOrderAppKey, ai.AiChatRequest{Content: req.Content}, nil)
	if err != nil {
		return
	}
	order, err = s.AiOrderHandler(aiChatResp.Content)
	if err != nil {
		err = errors2.New(aiChatResp.Content)
		return
	}
	resp.InternalRemark = req.Content
	if order.CustomerName != "" {
		bizUnitRepo := aggsBizUnit.NewRepo(tx)
		customers, err = bizUnitRepo.QueryMultiBizUnitByName(ctx, []string{order.CustomerName})
		if err != nil {
			return
		}
		if len(customers) == 0 {
			err = errors.NewError(errors.ErrCodeCustomerNotExist)
			return
		}
		customer := customers[0]
		saleSystemRels, err = bizUnitRepo.QueryBizUnitSaleSystem(ctx, customer.Id)
		if err != nil {
			return
		}
		bizUnitSale, err = bizUnitRepo.QueryBizUnitSale(ctx, customer.Id)
		if err != nil {
			return
		}
		if bizUnitSale != nil {
			saleGroup, err = bizUnitRepo.QuerySaleGroup(ctx, bizUnitSale.SaleGroupID)
			if err != nil {
				return
			}
			employeeRepo := aggsEmployee.NewRepo(tx)
			seller, err = employeeRepo.GetEmployee(ctx, bizUnitSale.SellerID)
			if err != nil {
				return
			}
			orderFollower, err = employeeRepo.GetEmployee(ctx, bizUnitSale.OrderFollowerID)
			if err != nil {
				return
			}
		}
		if len(saleSystemRels) > 0 {
			saleSystem, _, err = mysqlSystem.FirstSaleSystemByID(tx, saleSystemRels[0].SaleSystemId)
			if err != nil {
				return
			}
		}
		resp.CustomerId = customer.Id
		resp.CustomerCode = customer.CustomCode
		resp.CustomerName = customer.Name
		if saleGroup != nil {
			resp.SaleGroupId = saleGroup.Id
			resp.SaleGroupName = saleGroup.Name
		}
		if seller != nil {
			resp.SaleUserId = seller.Id
			resp.SaleUserName = seller.Name
		}
		if orderFollower != nil {
			resp.SaleFollowerId = orderFollower.Id
			resp.SaleFollowerName = orderFollower.Name
		}
		resp.SaleGroupName = saleGroup.Name
		resp.SettleType = commonSale.SettleType(customer.SettleType)
		resp.SettleTypeName = commonSale.SettleType(customer.SettleType).String()
		resp.SaleSystemId = saleSystem.Id
		resp.SaleSystemName = saleSystem.Name
		resp.SendProductType = commonSale.SendProductTypeDelivery
		resp.SendProductTypeName = resp.SendProductType.String()
	}
	saleShipmentRepo := aggsInfoBasic.NewInfoSaleShipmentTypeRepo(ctx, tx, false)
	saleShipments, _, err = saleShipmentRepo.GetList(ctx, &structureInfoBasic.GetSaleShipmentTypeListQuery{
		ListQuery: structureBase.ListQuery{Size: 1}, SaleSystemId: tools.QueryIntList(strconv.FormatUint(resp.SaleSystemId, 10))})
	if err != nil {
		return
	}
	if len(saleShipments) > 0 {
		resp.SaleShipmentCode = saleShipments[0].Code
		resp.SaleShipmentName = saleShipments[0].Name
	}
	switch order.Type {
	case commonSale.BulkTypeOrder.String():
		resp.SaleMode = commonSale.BulkTypeOrder
	case commonSale.ShearPlateTypeOrder.String():
		resp.SaleMode = commonSale.ShearPlateTypeOrder
	case commonSale.CustomerBulkTypeOrder.String():
		resp.SaleMode = commonSale.CustomerBulkTypeOrder
	case commonSale.CustomerShearPlateTypeOrder.String():
		resp.SaleMode = commonSale.CustomerShearPlateTypeOrder
	}
	resp.SaleModeName = resp.SaleMode.String()
	resp.List, resp.LostColors, err = saleProductOrderDetailHandler(ctx, tx, order)
	return
}

// saleProductOrderDetailHandler ai下单指令生成销售单详情处理器
func saleProductOrderDetailHandler(ctx context.Context, tx *mysql_base.Tx, order ai.AiOrder) (list []ai.AiSaleProductOrderDetail, lostColors int, err error) {
	var (
		num                 float64
		stockProductList    structureProduct.GetStockProductDetailDyelotNumberDataList
		productColorResList pbProduct.ProductColorResList
		productRes          pbProduct.ProductRes
		measurementUnitList structureInfoBasic.GetInfoBaseMeasurementUnitEnumDataList
	)
	stockProductRepo := aggsProduct.NewStockProductRepo(ctx, tx, make(map[uint64]product.StockProduct), make(map[uint64]product.StockProductDetail), set.NewConcurrentMap[string, uint64]())
	productClient := pbProduct.NewProductClient()
	productColorClient := pbProduct.NewProductColorClient()
	measurementUnitRepo := aggsInfoBasic.NewInfoBaseMeasurementUnitRepo(ctx, tx, false)
	measurementUnitList, _, err = measurementUnitRepo.GetEnumList(ctx, &structureInfoBasic.GetInfoBaseMeasurementUnitListQuery{})
	for _, detail := range order.Details {
		stockProductList, _, err = stockProductRepo.GetDyelotNumberDetailList(ctx, &structureProduct.GetStockProductDyelotNumberDetailListQuery{ProductCode: detail.ProductCode, WithPrice: true})
		if err != nil {
			return
		}
		productRes, err = productClient.GetProductByCode(ctx, pbProduct.ProductReq{ProductCode: detail.ProductCode})
		if err != nil {
			return
		}
		productColorResList, err = productColorClient.GetProductColorByIds(ctx, mysql_base.GetUInt64List(stockProductList, "product_color_id"))
		if err != nil {
			return
		}
		for _, color := range detail.Colors {
			stockProduct := stockProductList.PickByProductCodeAndProductColorCode(detail.ProductCode, color.ColorCode)
			if stockProduct.ProductColorId == 0 {
				lostColors++
				continue
			}
			num, err = strconv.ParseFloat(color.Num, 64)
			if err != nil {
				return
			}
			aiOrderDetail := ai.AiSaleProductOrderDetail{
				StockProductId:             stockProduct.Id,
				ProductColorId:             stockProduct.ProductColorId,
				ProductColorCode:           stockProduct.ProductColorCode,
				ProductColorName:           stockProduct.ProductColorName,
				ProductColorKindId:         stockProduct.ProductColorKindId,
				ProductColorKindName:       stockProduct.ProductColorKindName,
				CustomerId:                 stockProduct.CustomerId,
				ProductId:                  stockProduct.ProductId,
				ProductCode:                stockProduct.ProductCode,
				ProductName:                stockProduct.ProductName,
				ProductLevelId:             stockProduct.ProductLevelId,
				DyelotNumber:               stockProduct.DyelotNumber,
				ProductRemark:              stockProduct.ProductRemark,
				MeasurementUnitId:          stockProduct.MeasurementUnitId,
				MeasurementUnitName:        stockProduct.MeasurementUnitName,
				CustomerAccountNum:         stockProduct.CustomerAccountNum,
				Remark:                     stockProduct.Remark,
				StandardSalePrice:          stockProduct.StandardSalePrice,
				SaleLevelId:                stockProduct.SaleLevelId,
				OffsetSalePrice:            stockProduct.OffsetSalePrice,
				SalePrice:                  stockProduct.SalePrice.SalePrice,
				WeightError:                stockProduct.WeightError,
				OffsetWeightError:          stockProduct.OffsetWeightError,
				PaperTubeWeight:            stockProduct.PaperTubeWeight,
				StandardLengthCutSalePrice: stockProduct.StandardLengthCutSalePrice,
				StandardWeightCutSalePrice: stockProduct.StandardWeightCutSalePrice,
				OffsetLengthCutSalePrice:   stockProduct.OffsetLengthCutSalePrice,
				LengthCutSalePrice:         stockProduct.LengthCutSalePrice,
				WarehouseId:                stockProduct.WarehouseId,
				WarehouseName:              stockProduct.WarehouseName,
				StockRoll:                  stockProduct.AvailableRoll,
				StockWeight:                stockProduct.AvailableWeight,
				LatestSalePrice:            stockProduct.LatestSalePrice,
				LatestLengthCutSalePrice:   stockProduct.LatestLengthCutSalePrice,
				CustomerID:                 stockProduct.CustomerId,
				CustomerName:               stockProduct.CustomerName,
			}
			if colorInfo := productColorResList.PickByProductColorId(stockProduct.ProductColorId); len(colorInfo.TextureURL) > 0 {
				aiOrderDetail.TextureUrl = colorInfo.TextureURL[0]
			}
			if order.Type == commonSale.BulkTypeOrder.String() || order.Type == commonSale.CustomerBulkTypeOrder.String() {
				aiOrderDetail.Roll = int(num * 100)
				// 没有多余的可用匹数时，预约匹数=订单匹数-可用匹数
				if aiOrderDetail.Roll-aiOrderDetail.StockRoll > 0 {
					aiOrderDetail.BookRoll = aiOrderDetail.StockRoll
					aiOrderDetail.ShortageRoll = aiOrderDetail.Roll - aiOrderDetail.StockRoll
				} else {
					aiOrderDetail.BookRoll = aiOrderDetail.Roll
				}
				aiOrderDetail.AuxiliaryUnitId = aiOrderDetail.MeasurementUnitId
				aiOrderDetail.AuxiliaryUnitName = aiOrderDetail.MeasurementUnitName
				// 大货带入条数后，自动为“主数量”赋值“条数*成品资料的标准数量”
				aiOrderDetail.Weight = aiOrderDetail.Roll * productRes.StandardWeight / 100
			} else {
				unit := measurementUnitList.PickByName(color.Unit)
				// 如果客户有输入单位且与主单位不一致时，则输入数量带入到“辅助数量”，否则，剪板订单输入的数量默认带入到“主数量”
				if unit.Id != 0 && unit.Id != aiOrderDetail.MeasurementUnitId {
					aiOrderDetail.AuxiliaryUnitId = unit.Id
					aiOrderDetail.AuxiliaryUnitName = unit.Name
					aiOrderDetail.Length = int(num*100) * 10000 / 100
				} else {
					aiOrderDetail.AuxiliaryUnitId = aiOrderDetail.MeasurementUnitId
					aiOrderDetail.AuxiliaryUnitName = aiOrderDetail.MeasurementUnitName
					aiOrderDetail.Weight = int(num*100) * 10000 / 100
				}
			}
			list = append(list, aiOrderDetail)
		}
	}
	return
}

// AiAddSaleProductOrder ai下单
func (s *AiService) AiAddSaleProductOrder(ctx context.Context, req ai.AiRequest) (resp ai.AiAddSaleProductOrderData, err error) {
	var (
		id                         uint64
		addReq                     structureSale.AddSaleProductOrderParam
		aiChatResp                 ai.AiChatResponse
		order                      ai.AiOrder
		customers                  []*mysqlBizUnit.BizUnit
		bizUnitSale                *mysqlBizUnit.BizUnitSale
		saleProductOrderDetailList []ai.AiSaleProductOrderDetail
		saleShipments              structureInfoBasic.GetSaleShipmentDataList
		customerOweMoneys          mysqlShouldCollectOrder.CustomerOweMoneyList
		actuallyCollectOrder       mysqlShouldCollectOrder.ActuallyCollectOrder
		saleSystemRels             mysqlBizUnit.BizUnitSaleSystemRelList
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	aiChatResp, err = s.FastGptChat(config.Conf.FastGpt.AiOrderAppKey, ai.AiChatRequest{Content: req.Content},
		map[string]interface{}{"customer_require": true, "type_require": true})
	if err != nil {
		return
	}
	order, err = s.AiOrderHandler(aiChatResp.Content)
	if err != nil {
		err = nil
		resp.Content = aiChatResp.Content
		return
	}
	bizUnitRepo := aggsBizUnit.NewRepo(tx)
	customers, err = bizUnitRepo.QueryMultiBizUnitByName(ctx, []string{order.CustomerName})
	if err != nil {
		return
	}
	if len(customers) == 0 {
		err = errors.NewError(errors.ErrCodeCustomerNotExist)
		return
	}
	customer := customers[0]
	saleSystemRels, err = bizUnitRepo.QueryBizUnitSaleSystem(ctx, customer.Id)
	if err != nil {
		return
	}
	bizUnitSale, err = bizUnitRepo.QueryBizUnitSale(ctx, customer.Id)
	if err != nil {
		return
	}
	saleProductOrderDetailList, resp.LostColors, err = saleProductOrderDetailHandler(ctx, tx, order)
	if err != nil {
		return
	}
	addReq.InternalRemark = req.Content
	addReq.ItemData = wrapAiSaleProductOrderDetails(customer.Id, saleProductOrderDetailList)
	addReq.CustomerId = customer.Id
	addReq.OrderTime = tools.QueryTime(time.Now().Format(time.DateOnly))
	if len(saleSystemRels) > 0 {
		addReq.SaleSystemId = saleSystemRels[0].SaleSystemId
	}
	saleShipmentRepo := aggsInfoBasic.NewInfoSaleShipmentTypeRepo(ctx, tx, false)
	saleShipments, _, err = saleShipmentRepo.GetList(ctx, &structureInfoBasic.GetSaleShipmentTypeListQuery{
		ListQuery: structureBase.ListQuery{Size: 1}, SaleSystemId: tools.QueryIntList(strconv.FormatUint(addReq.SaleSystemId, 10))})
	if err != nil {
		return
	}
	if bizUnitSale != nil {
		addReq.SaleGroupId = bizUnitSale.SaleGroupID
		addReq.SaleFollowerId = bizUnitSale.OrderFollowerID
		addReq.SaleUserId = bizUnitSale.SellerID
	}
	addReq.SendProductType = 1
	// 订单默认齐单提货
	addReq.PickUpGoodsInOrder = true
	if len(saleShipments) > 0 {
		addReq.SaleShipmentName = saleShipments[0].Name
	}
	switch order.Type {
	case commonSale.BulkTypeOrder.String():
		addReq.SaleMode = commonSale.BulkTypeOrder
	case commonSale.ShearPlateTypeOrder.String():
		addReq.SaleMode = commonSale.ShearPlateTypeOrder
	case commonSale.CustomerBulkTypeOrder.String():
		addReq.SaleMode = commonSale.CustomerBulkTypeOrder
	case commonSale.CustomerShearPlateTypeOrder.String():
		addReq.SaleMode = commonSale.CustomerShearPlateTypeOrder
	}
	saleProductRepo := aggsSale.NewSaleProductOrderRepo(ctx, tx, false)
	id, _, err = saleProductRepo.Add(ctx, &addReq)
	if err != nil {
		return
	}
	resp.ID = id
	err = saleProductRepo.AddSaleProductOrderDetailDetails(ctx, &addReq, id)
	// 查询客户欠款
	customerOweMoneys, _, err = mysqlShouldCollectOrder.FindCustomerOweMoneyList(tx, &structureShouldCollectOrder.GetCustomerOweMoneyListQuery{CustomerId: customer.Id}, true)
	if len(customerOweMoneys) != 0 {
		if customerOweMoneys[0].BalancePrice > 0 {
			resp.OweMoney = tools.Cent(customerOweMoneys[0].BalancePrice)
			// 查询欠款天数 欠款天数=当前日期-上次实付款日期
			actuallyCollectOrderRepo := aggsShouldCollectOrder.NewActuallyCollectOrderRepo(tx)
			actuallyCollectOrder, err = actuallyCollectOrderRepo.GetLastCollectedActuallyCollectOrderByCustomerID(ctx, customer.Id)
			if err != nil {
				return
			}
			if actuallyCollectOrder.Id != 0 {
				resp.OweDays = int(time.Now().Sub(actuallyCollectOrder.ReceiveCollectDate).Hours()) / 24
			}
		}
	}
	return
}

func wrapAiSaleProductOrderDetails(customerID uint64, saleProductOrderDetailList []ai.AiSaleProductOrderDetail) (saleProductOrderDetails structureSale.AddSaleProductOrderDetailParamList) {
	for _, saleProductOrderDetail := range saleProductOrderDetailList {
		saleProductOrderDetails = append(saleProductOrderDetails, structureSale.AddSaleProductOrderDetailParam{
			StockProductId:                  saleProductOrderDetail.StockProductId,
			SaleProductOrderId:              saleProductOrderDetail.SaleProductOrderId,
			ProductColorId:                  saleProductOrderDetail.ProductColorId,
			ProductColorKindId:              saleProductOrderDetail.ProductColorKindId,
			CustomerId:                      customerID,
			ProductId:                       saleProductOrderDetail.ProductId,
			ProductLevelId:                  saleProductOrderDetail.ProductLevelId,
			ProductKindId:                   saleProductOrderDetail.ProductKindId,
			DyelotNumber:                    saleProductOrderDetail.DyelotNumber,
			ProductRemark:                   saleProductOrderDetail.ProductRemark,
			AuxiliaryUnitId:                 saleProductOrderDetail.AuxiliaryUnitId,
			MeasurementUnitId:               saleProductOrderDetail.MeasurementUnitId,
			MeasurementUnitName:             saleProductOrderDetail.MeasurementUnitName,
			Weight:                          saleProductOrderDetail.Weight,
			Roll:                            saleProductOrderDetail.Roll,
			CustomerAccountNum:              saleProductOrderDetail.CustomerAccountNum,
			Remark:                          saleProductOrderDetail.Remark,
			StandardSalePrice:               saleProductOrderDetail.StandardSalePrice,
			SaleLevelId:                     saleProductOrderDetail.SaleLevelId,
			OffsetSalePrice:                 saleProductOrderDetail.OffsetSalePrice,
			SalePrice:                       saleProductOrderDetail.SalePrice,
			WeightError:                     saleProductOrderDetail.WeightError,
			OffsetWeightError:               saleProductOrderDetail.OffsetWeightError,
			AdjustWeightError:               saleProductOrderDetail.AdjustWeightError,
			SettleWeightError:               saleProductOrderDetail.SettleWeightError,
			PaperTubeWeight:                 saleProductOrderDetail.PaperTubeWeight,
			Length:                          saleProductOrderDetail.Length,
			StandardLengthCutSalePrice:      saleProductOrderDetail.StandardLengthCutSalePrice,
			OffsetLengthCutSalePrice:        saleProductOrderDetail.OffsetLengthCutSalePrice,
			LengthCutSalePrice:              saleProductOrderDetail.LengthCutSalePrice,
			OtherPrice:                      saleProductOrderDetail.OtherPrice,
			WarehouseId:                     saleProductOrderDetail.WarehouseId,
			StockRoll:                       saleProductOrderDetail.StockRoll,
			StockWeight:                     saleProductOrderDetail.StockWeight,
			BookRoll:                        saleProductOrderDetail.BookRoll,
			PurchaseRoll:                    saleProductOrderDetail.PurchaseRoll,
			PurchaseWeight:                  saleProductOrderDetail.PurchaseWeight,
			PurchaseLength:                  saleProductOrderDetail.PurchaseLength,
			ShortageRoll:                    saleProductOrderDetail.ShortageRoll,
			ShortageWeight:                  saleProductOrderDetail.ShortageWeight,
			ShortageLength:                  saleProductOrderDetail.ShortageLength,
			IsDisplayPrice:                  saleProductOrderDetail.IsDisplayPrice,
			PmcGreyPlanOrderSummaryDetailId: saleProductOrderDetail.PmcGreyPlanOrderSummaryDetailId,
			PlanDetailId:                    saleProductOrderDetail.PlanDetailId,
		})
	}
	return
}
