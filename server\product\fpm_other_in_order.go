package product

import (
	"context"
	"fmt"
	"github.com/gin-gonic/gin"
	common "hcscm/common/basic_data"
	"hcscm/common/errors"
	common_payable "hcscm/common/payable"
	common_product "hcscm/common/product"
	"hcscm/extern/pb/payable"
	saleSys "hcscm/extern/pb/sale_system"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	"hcscm/model/redis"
	"hcscm/server/system"
	serveicePayable "hcscm/service/payable"
	svc "hcscm/service/product"
	sys_structure "hcscm/structure/payable2"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"log"
	"time"
)

// @Tags		【成品其他进仓】
// @Summary	添加成品其他进仓(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmInOrderParam{}	true	"创建FpmInOrder"
// @Suc ess	200		{object}	structure.AddFpmInOrderData{}
// @Param		Platform		header		int		true	"终端ID"
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/addFpmOtherInOrder [post]
// @Tags		【成品其他进仓】
// @Summary	添加成品其他进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.AddFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/addFpmOtherInOrder [post]
func AddFpmOtherInOrder(c *gin.Context) {
	var (
		q            = &structure.AddFpmInOrderParam{}
		data         = structure.AddFpmInOrderData{}
		svc          = svc.NewFpmInOrderService()
		err          error
		orderPrefix  mysqlSystem.OrderPrefix
		exist        bool
		sale_sys_svc = saleSys.NewSaleSystemClient()
		saleSysData  = saleSys.Res{}
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()
	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)
	if err = q.CheckVNumber(); err != nil {
		return
	}
	q.InOrderType = common_product.WarehouseGoodInTypeOther
	// product receive in
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: q.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(tx)
	if err != nil {
		return
	}
	if !exist {
		q.OrderNoPre = vars.FpmOtherInOrderPrefix
		if vars.UseSaleSystem {
			q.OrderNoPre = fmt.Sprintf("%s-%s-", q.OrderNoPre, saleSysData.Code)
		}
	} else {
		q.OrderNoPre = orderPrefix.FpmOtherInOrder
		if orderPrefix.UseSaleSystem {
			q.OrderNoPre = fmt.Sprintf("%s-%s-", q.OrderNoPre, saleSysData.Code)
		}
	}
	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrder [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrder [put]
func UpdateFpmOtherInOrder(c *gin.Context) {
	var (
		q                = &structure.UpdateFpmInOrderParam{}
		data             = structure.UpdateFpmInOrderData{}
		svc              = svc.NewFpmInOrderService()
		err              error
		payableOtherNums int
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	payableOtherSvc := serveicePayable.NewPayableOtherQueryService()
	_, payableOtherNums, err = payableOtherSvc.QueryPayableOtherList(ctx, tx, &sys_structure.GetPayableListParams{SrcOrderIds: []uint64{q.Id}})
	if err != nil {
		return
	}
	if payableOtherNums > 0 {
		err = errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, "操作失败,请先取消其他应付账")
		return
	}
	if err = q.CheckVNumber(); err != nil {
		return
	}
	q.InOrderType = common_product.WarehouseGoodInTypeOther
	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderBusinessCloseParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderBusinessClose [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓业务状态(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderBusinessCloseParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderData{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrderBusinessClose [put]
func UpdateFpmOtherInOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderBusinessCloseParam{}
		data = structure.UpdateFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusWait [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-消审(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusWait [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-审核(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusPass [put]
func UpdateFpmOtherInOrderStatusWait(c *gin.Context) {
	var (
		q                = &structure.UpdateFpmInOrderStatusParam{}
		data             = structure.UpdateFpmInOrderStatusData{}
		inOrderSvc       = svc.NewFpmInOrderService()
		stockSvc         = svc.NewStockProductService()
		err              error
		rLocks           = make(redis.LockForRedisList, 0)
		updateItems      structure.UpdateStockProductDetailParamList
		payableOtherNums int
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	payableOtherSvc := serveicePayable.NewPayableOtherQueryService()
	_, payableOtherNums, err = payableOtherSvc.QueryPayableOtherList(ctx, tx, &sys_structure.GetPayableListParams{SrcOrderIds: q.Id.ToUint64()})
	if err != nil {
		return
	}
	if payableOtherNums > 0 {
		err = errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, "操作失败,请先取消其他应付账")
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, updateItems, _, err = inOrderSvc.UpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}

		// 成品采购供应商
		if data.MainBizUnitType == common.BizUnitTypeProductSupplier {
			voidPayableProductPur(ctx, tx, []uint64{id})
		}
		// 染整厂
		if data.MainBizUnitType == common.BizUnitTypeDNF {
			voidPayableDNF(ctx, tx, []uint64{id})
		}
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusPass [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-审核(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusPass [put]
func UpdateFpmOtherInOrderStatusPass(c *gin.Context) {
	var (
		q                = &structure.UpdateFpmInOrderStatusParam{}
		data             = structure.UpdateFpmInOrderStatusData{}
		inOrderSvc       = svc.NewFpmInOrderService()
		stockSvc         = svc.NewStockProductService()
		rLocks           = make(redis.LockForRedisList, 0)
		ids              map[uint64]uint64
		sumIds           map[uint64]uint64
		addItems         structure.AddStockProductDetailParamList
		err              error
		payableOtherNums int
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	payableOtherSvc := serveicePayable.NewPayableOtherQueryService()
	_, payableOtherNums, err = payableOtherSvc.QueryPayableOtherList(ctx, tx, &sys_structure.GetPayableListParams{SrcOrderIds: q.Id.ToUint64()})
	if err != nil {
		return
	}
	if payableOtherNums > 0 {
		err = errors.NewCustomError(errors.ErrCodeTheOrderIsQuoted, "操作失败,请先取消其他应付账")
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, addItems, _, _, err = inOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		if len(addItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, addItems, nil)
			if err != nil {
				return
			}
			ids, sumIds, err = stockSvc.AddDetails(ctx, tx, addItems)
			if err != nil {
				return
			}

			err = inOrderSvc.UpdateDetailStockDetailId(ctx, tx, ids, sumIds, id, false)
			if err != nil {
				return
			}
		}
		// // 成品采购供应商
		// if data.MainBizUnitType == common.BizUnitTypeProductSupplier {
		// 	orderData := structure.GetFpmInOrderData{}
		// 	orderData, err = inOrderSvc.GetByTx(ctx, tx, id)
		// 	if err != nil {
		// 		return
		// 	}
		// 	addPayableProductPurOrderByOtherIn(ctx, tx, orderData, false)
		// }
		// // 染整厂
		// if data.MainBizUnitType == common.BizUnitTypeDNF {
		// 	orderData := structure.GetFpmInOrderData{}
		// 	orderData, err = inOrderSvc.GetByTx(ctx, tx, id)
		// 	if err != nil {
		// 		return
		// 	}
		// 	// 新增染整应付单
		// 	addPayableDNFOrderByOtherIn(ctx, tx, orderData, false)
		// }
		orderData := structure.GetFpmInOrderData{}
		orderData, err = inOrderSvc.GetByTx(ctx, tx, id)
		if err != nil {
			return
		}
		addPayableOtherOrderByOtherIn(ctx, tx, orderData, false)
	}

	return
}

// 创建成品采购应付单
func addPayableProductPurOrderByOtherIn(ctx context.Context, tx *mysql_base.Tx, order structure.GetFpmInOrderData, isAudit bool) {
	payableService := payable.NewClientPayableService()

	req := &sys_structure.AddPayableProductPurParams{
		PayableBaseInfo: sys_structure.PayableBaseInfo{
			SrcOrderType: common_payable.SrcOrderTypeFpmOtherInOrder,
			SrcOrderId:   order.Id,
			SrcOrderNo:   order.OrderNo,
			SaleSystemId: order.SaleSystemId,
			SupplierId:   order.BizUnitId,
			PayDate:      tools.QueryTime(time.Now().Format("2006-01-02")),
			SaleMode:     order.SaleMode, // 下推订单类型
		},
	}
	req.Items = make([]*sys_structure.PayableProductPurItem, 0)
	for _, item := range order.ItemData {
		if item.AuxiliaryUnitId == 0 {
			item.AuxiliaryUnitId = item.UnitId
		}
		req.Items = append(req.Items, &sys_structure.PayableProductPurItem{
			SrcId:               item.Id,
			Roll:                item.InRoll,
			Weight:              item.SettleWeight,
			WeightUnitPrice:     item.UnitPrice,
			Length:              item.InLength,
			LengthUnitPrice:     item.LengthUnitPrice,
			OtherPrice:          item.OtherPrice,
			SumStockId:          item.SumStockId,
			MaterialId:          item.ProductId,
			ColorId:             item.ProductColorId,
			DyeFactoryColorCode: item.DyeFactoryColorCode,
			DyelotNumber:        item.DyeFactoryDyelotNumber,
			MeasurementUnitId:   item.UnitId,
			AuxiliaryUnitId:     item.AuxiliaryUnitId,
			Width:               item.ProductWidth,
			GramWeight:          item.ProductGramWeight,
			WidthUnitId:         item.FinishProductWidthUnitId,
			GramWeightUnitId:    item.FinishProductGramWeightUnitId,
			FcDataList:          convertToPayableFCDataList(item.ItemFCData),
		})
	}
	id, err := payableService.AddPayableProductPur(ctx, tx, req)
	if err != nil {
		log.Default().Println(ctx, "addPayableProductPurOrder err: ", err)
		// 失败继续执行
	}
	// 是否自动审核
	if isAudit {
		auditReq := &sys_structure.GetPayableOtherRequest{
			Id: id,
		}
		id, err = payableService.AuditPayableProductPur(ctx, tx, auditReq)
		if err != nil {
			return
		}
	}
}
func convertToPayableFCDataList(srcList structure.GetFpmInOrderItemFcDataList) []sys_structure.AddPayableItemFcData {
	if len(srcList) == 0 {
		return nil
	}

	result := make([]sys_structure.AddPayableItemFcData, 0, len(srcList))
	for _, src := range srcList {
		result = append(result, sys_structure.AddPayableItemFcData{
			Id:                src.Id,
			WarehouseId:       src.WarehouseId,
			Roll:              src.Roll,
			WarehouseBinId:    src.WarehouseBinId,
			VolumeNumber:      src.VolumeNumber,
			BaseUnitWeight:    src.BaseUnitWeight,
			WeightError:       src.WeightError,
			PaperTubeWeight:   src.PaperTubeWeight,
			ActuallyWeight:    src.ActuallyWeight,
			SettleWeight:      src.SettleWeight,
			Length:            src.Length,
			UnitId:            src.UnitId,
			StockId:           src.StockId,
			SumStockId:        src.SumStockId,
			SettleErrorWeight: src.SettleErrorWeight,
		})
	}

	return result
}

func addPayableDNFOrderByOtherIn(ctx context.Context, tx *mysql_base.Tx, order structure.GetFpmInOrderData, isAudit bool) {
	payableService := payable.NewClientPayableService()

	req := &sys_structure.AddPayableDNFParams{
		PayableBaseInfo: sys_structure.PayableBaseInfo{
			SrcOrderType: common_payable.SrcOrderTypeFpmOtherInOrder,
			SrcOrderId:   order.Id,
			SrcOrderNo:   order.OrderNo,
			SaleSystemId: order.SaleSystemId,
			SupplierId:   order.BizUnitId,
			PayDate:      tools.QueryTime(time.Now().Format("2006-01-02")),
			SaleMode:     order.SaleMode,
		},
	}
	req.Items = make([]*sys_structure.PayableDNFItem, 0)
	for _, item := range order.ItemData {
		if item.AuxiliaryUnitId == 0 {
			item.AuxiliaryUnitId = item.UnitId
		}
		req.Items = append(req.Items, &sys_structure.PayableDNFItem{
			SrcId:               item.Id,
			PieceCount:          item.InRoll,
			Weight:              item.SettleWeight,
			DNFUnitPrice:        item.UnitPrice,
			Length:              item.InLength,
			FinishingUnitPrice:  item.LengthUnitPrice,
			OtherPrice:          item.OtherPrice,
			SumStockId:          item.SumStockId,
			MaterialId:          item.ProductId,
			ColorId:             item.ProductColorId,
			DyeFactoryColorCode: item.DyeFactoryColorCode,
			DyelotNumber:        item.DyeFactoryDyelotNumber,
			MeasurementUnitId:   item.UnitId,
			AuxiliaryUnitId:     item.AuxiliaryUnitId,
			Width:               item.ProductWidth,
			GramWeight:          item.ProductGramWeight,
			WidthUnitId:         item.FinishProductWidthUnitId,
			GramWeightUnitId:    item.FinishProductGramWeightUnitId,
			FcDataList:          convertToPayableFCDataList(item.ItemFCData),
		})
	}

	id, err := payableService.AddPayableDNF(ctx, tx, req)
	if err != nil {
		log.Default().Println(ctx, "addPayableProductPurOrder err: ", err)
		// 失败继续执行
	}
	// 是否自动审核
	if isAudit {
		auditReq := &sys_structure.GetPayableOtherRequest{
			Id: id,
		}
		id, err = payableService.AuditPayableProductPur(ctx, tx, auditReq)
		if err != nil {
			return
		}
	}
}

func addPayableOtherOrderByOtherIn(ctx context.Context, tx *mysql_base.Tx, order structure.GetFpmInOrderData, isAudit bool) {
	svc := serveicePayable.NewPayableOtherService(ctx, false)

	req := &sys_structure.AddPayableOtherParams{
		PayableBaseInfo: sys_structure.PayableBaseInfo{
			SrcOrderType: common_payable.SrcOrderTypeFpmOtherInOrder,
			SrcOrderId:   order.Id,
			SrcOrderNo:   order.OrderNo,
			SaleSystemId: order.SaleSystemId,
			SupplierId:   order.BizUnitId,
			HandlerId:    order.StoreKeeperId,
			PayDate:      tools.QueryTime(order.WarehouseInTime.Date()),
			SaleMode:     order.SaleMode,
			VoucherNum:   order.VoucherNumber,
			Remark:       order.Remark,
		},
	}
	req.Items = make([]*sys_structure.PayableOtherItem, 0)
	for _, item := range order.ItemData {
		var unitPrice, weight int
		if item.AuxiliaryUnitId == 0 {
			item.AuxiliaryUnitId = item.UnitId
		}
		if item.UnitPrice != 0 {
			unitPrice = item.UnitPrice
		} else if item.LengthUnitPrice != 0 {
			unitPrice = item.LengthUnitPrice
		}
		// 当单价不为0是，生成其他应付账
		if unitPrice == 0 {
			continue
		}
		// 若单位和结算单位一致时，则取“进仓数量 - 空差”；不一致时，则取“进仓辅助数量”
		if item.UnitId == item.AuxiliaryUnitId {
			weight = item.TotalWeight - item.WeightError
		} else {
			weight = item.InLength
		}
		req.Items = append(req.Items, &sys_structure.PayableOtherItem{
			SrcId:       item.Id,
			VoucherNum:  order.OrderNo, // 下推时凭证单号即来源单号
			Date:        order.WarehouseInTime.Date(),
			OrderType:   order.InOrderType.String(),
			ProjectNo:   fmt.Sprintf("%s#%s", item.ProductCode, item.ProductName),
			ProjectName: fmt.Sprintf("%s#%s", item.ProductColorCode, item.ProductColorName),
			Weight:      weight,
			UnitPrice:   unitPrice,
			OtherPrice:  item.OtherPrice,
			Remark:      item.Remark,
		})
	}

	if len(req.Items) == 0 {
		return
	}

	id, err := svc.AddOrder(ctx, req)
	if err != nil {
		log.Default().Println(ctx, "addPayableProductPurOrder err: ", err)
		// 失败继续执行
	}
	// 是否自动审核
	if isAudit {
		err = svc.PassOrder(ctx, id)
		if err != nil {
			return
		}
	}
}

// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-驳回
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusReject [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-驳回(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusReject [put]
func UpdateFpmOtherInOrderStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusReject(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusCancel [put]
// @Tags		【成品其他进仓】
// @Summary	更新成品其他进仓状态-作废(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmInOrderStatusParam{}	true	"创建FpmInOrder"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200		{object}	structure.UpdateFpmInOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/updateFpmOtherInOrderStatusCancel [put]
func UpdateFpmOtherInOrderStatusCancel(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmInOrderStatusParam{}
		data = structure.UpdateFpmInOrderStatusData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusCancel(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	获取成品其他进仓
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetFpmInOrderData{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/getFpmOtherInOrder [get]
// @Tags		【成品其他进仓】
// @Summary	获取成品其他进仓(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200	{object}	structure.GetFpmInOrderData{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/getFpmOtherInOrder [get]
func GetFpmOtherInOrder(c *gin.Context) {
	var (
		q    = &structure.GetFpmInOrderQuery{}
		data = structure.GetFpmInOrderData{}
		svc  = svc.NewFpmInOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	获取成品其他进仓列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/getFpmOtherInOrderList [get]
// @Tags		【成品其他进仓】
// @Summary	获取成品其他进仓列表(小程序)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		query_str		query		string	false	"发货单位和订单编号模糊匹配"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/getFpmOtherInOrderList [get]
func GetFpmOtherInOrderList(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = common_product.WarehouseGoodInTypeOther
	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	获取成品其他进仓列表(及详情)
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		product_code_or_name	query		string	false	"成品编号或名称"
// @Param		product_color_code_or_name	query		string	false	"成品颜色编号或名称"
// @Param		dyelot_number	query		string	false	"缸号"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmOtherInOrder/getFpmOtherInOrderListAndDetails [get]
func GetFpmOtherInOrderListAndDetails(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = common_product.WarehouseGoodInTypeOther
	list, total, err = svc.GetListAndDetail(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	获取成品其他进仓列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		supplier_id		query		int		false	"供方id"
// @Param		biz_unit_name	query		string	false	"供方name"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"入仓开始时间"
// @Param		in_time_end		query		string	false	"入仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization	header		string	true	"token"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/mp/v1/product/fpmOtherInOrder/GetMPFpmInOrderList [get]
func GetMPFpmInOrderList(c *gin.Context) {
	var (
		q     = &structure.GetMPFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	q.InOrderType = common_product.WarehouseGoodInTypeOther
	list, total, err = svc.GetMPList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品其他进仓】
// @Summary	清洗库存进出仓记录
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		warehouse_in_time	query		string	false	"进仓时间"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Param		Authorization	header		string	true	"token"
// @Success	200				{object}	structure.GetFpmInOrderDataList{}
// @Router		/hcscm/admin/v1/temporary/product/wash_in_order_for_import [get]
func WashInOrderForImport(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmInOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}
	info := metadata.GetLoginInfo(ctx)
	_ctx := context.Background()
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.UserName, info.GetUserName())
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.UserId, tools.UInt642String(info.GetUserId()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.DepartmentId, tools.UInt642String(info.GetDepartmentId()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.SaleSystemId, tools.UInt642String(info.GetDefaultSaleSystemId()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.IsAllowUpdateOrder, tools.BooleanToIntString(info.IsAllowUpdateOrder()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.IsAllowCancelOther, tools.BooleanToIntString(info.IsAllowCancelOther()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.IsAllowAuditSelf, tools.BooleanToIntString(info.IsAllowAuditSelf()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.TenantManagementId, tools.UInt642String(info.GetTenantManagementId()))
	_ctx = metadata.SetMDToIncoming(_ctx, metadata.TenantManagementDbName, info.GetTenantManagementDbName())
	_ctx = context.WithValue(_ctx, metadata.LoginInfo, info)
	_ctx = context.WithValue(_ctx, metadata.DataAccessScope, info.GetDataAccessScope())
	// 更新使用判断
	_ctx = context.WithValue(_ctx, metadata.DataSeparate, info.GetDataSeparate())
	go func() {
		tx, commit := mysql_base.TransactionMainEx(nil, _ctx, false)
		defer func() {
			err = commit(err, recover())
		}()

		err = svc.WashInOrderForImport(_ctx, tx, q)
		if err != nil {
			return
		}
	}()
}

// /hcscm/admin/v1/temporary/product/wash_in_order_item_together [get]
func WashInOrderItemTogether(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	// 销售退货进仓单
	var (
		fpmSaleReturnInOrderItemList model.FpmSaleReturnInOrderItemList
	)
	fpmSaleReturnInOrderItemList, err = mysql.FindAllFpmSaleReturnInOrderItem(tx)
	if err != nil {
		return
	}
	for _, fpmSaleReturnInOrderItem := range fpmSaleReturnInOrderItemList {
		fpmInOrderItem := model.FpmInOrderItem{}
		fpmInOrderItem.Id = fpmSaleReturnInOrderItem.Id
		fpmInOrderItem.ParentId = fpmSaleReturnInOrderItem.ParentId
		fpmInOrderItem.WarehouseInType = common_product.WarehouseGoodInTypeSaleReturn
		fpmInOrderItem.SumStockId = fpmSaleReturnInOrderItem.SumStockId
		fpmInOrderItem.ParentOrderNo = fpmSaleReturnInOrderItem.ParentOrderNo
		fpmInOrderItem.QuoteOrderNo = fpmSaleReturnInOrderItem.QuoteOrderNo
		fpmInOrderItem.QuoteOrderItemId = fpmSaleReturnInOrderItem.QuoteOrderItemId
		fpmInOrderItem.ProductId = fpmSaleReturnInOrderItem.ProductId
		fpmInOrderItem.ProductCode = fpmSaleReturnInOrderItem.ProductCode
		fpmInOrderItem.ProductName = fpmSaleReturnInOrderItem.ProductName
		fpmInOrderItem.CustomerId = fpmSaleReturnInOrderItem.CustomerId
		fpmInOrderItem.ProductColorId = fpmSaleReturnInOrderItem.ProductColorId
		fpmInOrderItem.ProductColorCode = fpmSaleReturnInOrderItem.ProductColorCode
		fpmInOrderItem.ProductColorName = fpmSaleReturnInOrderItem.ProductColorName
		fpmInOrderItem.DyeFactoryColorCode = fpmSaleReturnInOrderItem.DyeFactoryColorCode
		fpmInOrderItem.DyeFactoryDyelotNumber = fpmSaleReturnInOrderItem.DyeFactoryDyelotNumber
		fpmInOrderItem.ProductWidth = fpmSaleReturnInOrderItem.ProductWidth
		fpmInOrderItem.ProductGramWeight = fpmSaleReturnInOrderItem.ProductGramWeight
		fpmInOrderItem.ProductLevelId = fpmSaleReturnInOrderItem.ProductLevelId
		fpmInOrderItem.ProductRemark = fpmSaleReturnInOrderItem.ProductRemark
		fpmInOrderItem.ProductCraft = fpmSaleReturnInOrderItem.ProductCraft
		fpmInOrderItem.ProductIngredient = fpmSaleReturnInOrderItem.ProductIngredient
		fpmInOrderItem.InRoll = fpmSaleReturnInOrderItem.InRoll
		fpmInOrderItem.InWeight = fpmSaleReturnInOrderItem.InWeight
		fpmInOrderItem.WeightError = fpmSaleReturnInOrderItem.WeightError
		fpmInOrderItem.PaperTubeWeight = fpmSaleReturnInOrderItem.PaperTubeWeight
		fpmInOrderItem.SettleWeight = fpmSaleReturnInOrderItem.SettleWeight
		fpmInOrderItem.ActuallyWeight = fpmSaleReturnInOrderItem.ActuallyWeight
		fpmInOrderItem.SettleErrorWeight = fpmSaleReturnInOrderItem.SettleErrorWeight
		fpmInOrderItem.UnitId = fpmSaleReturnInOrderItem.UnitId
		fpmInOrderItem.AuxiliaryUnitId = fpmSaleReturnInOrderItem.AuxiliaryUnitId
		fpmInOrderItem.InLength = fpmSaleReturnInOrderItem.InLength
		fpmInOrderItem.Remark = fpmSaleReturnInOrderItem.Remark
		fpmInOrderItem.ReturnRoll = fpmSaleReturnInOrderItem.ReturnRoll
		fpmInOrderItem.ReturnWeight = fpmSaleReturnInOrderItem.ReturnWeight
		fpmInOrderItem.ReturnLength = fpmSaleReturnInOrderItem.ReturnLength
		fpmInOrderItem.FinishProductWidthUnitId = fpmSaleReturnInOrderItem.FinishProductWidthUnitId
		fpmInOrderItem.FinishProductGramWeightUnitId = fpmSaleReturnInOrderItem.FinishProductGramWeightUnitId
		fpmInOrderItem.ReturnPrice = fpmSaleReturnInOrderItem.ReturnPrice
		fpmInOrderItem.LengthCutReturnPrice = fpmSaleReturnInOrderItem.LengthCutReturnPrice
		fpmInOrderItem.SettlePrice = fpmSaleReturnInOrderItem.SettlePrice

		fpmInOrderItem, err = mysql.MustCreateFpmInOrderItem(tx, fpmInOrderItem)
		if err != nil {
			return
		}
	}

	// 加工进仓单
	var (
		fpmProcessInOrderItemList model.FpmProcessInOrderItemList
	)
	fpmProcessInOrderItemList, err = mysql.FindAllFpmProcessInOrderItem(tx)
	if err != nil {
		return
	}
	for _, fpmProcessInOrderItem := range fpmProcessInOrderItemList {
		fpmInOrderItem := model.FpmInOrderItem{}
		fpmInOrderItem.Id = fpmProcessInOrderItem.Id
		fpmInOrderItem.ParentId = fpmProcessInOrderItem.ParentId
		fpmInOrderItem.SumStockId = fpmProcessInOrderItem.SumStockId
		fpmInOrderItem.ParentOrderNo = fpmProcessInOrderItem.ParentOrderNo
		fpmInOrderItem.QuoteOrderId = fpmProcessInOrderItem.QuoteOrderId
		fpmInOrderItem.DyeingSituId = fpmProcessInOrderItem.DyeingSituId
		fpmInOrderItem.QuoteOrderNo = fpmProcessInOrderItem.QuoteOrderNo
		fpmInOrderItem.QuoteOrderItemId = fpmProcessInOrderItem.QuoteOrderItemId
		fpmInOrderItem.QuoteOrderType = fpmProcessInOrderItem.QuoteOrderType
		fpmInOrderItem.ProductId = fpmProcessInOrderItem.ProductId
		fpmInOrderItem.ProductCode = fpmProcessInOrderItem.ProductCode
		fpmInOrderItem.ProductName = fpmProcessInOrderItem.ProductName
		fpmInOrderItem.CustomerId = fpmProcessInOrderItem.CustomerId
		fpmInOrderItem.ProductColorId = fpmProcessInOrderItem.ProductColorId
		fpmInOrderItem.ProductColorCode = fpmProcessInOrderItem.ProductColorCode
		fpmInOrderItem.ProductColorName = fpmProcessInOrderItem.ProductColorName
		fpmInOrderItem.DyeFactoryColorCode = fpmProcessInOrderItem.DyeFactoryColorCode
		fpmInOrderItem.DyeFactoryDyelotNumber = fpmProcessInOrderItem.DyeFactoryDyelotNumber
		fpmInOrderItem.ProductWidth = fpmProcessInOrderItem.ProductWidth
		fpmInOrderItem.ProductGramWeight = fpmProcessInOrderItem.ProductGramWeight
		fpmInOrderItem.ProductLevelId = fpmProcessInOrderItem.ProductLevelId
		fpmInOrderItem.ProductRemark = fpmProcessInOrderItem.ProductRemark
		fpmInOrderItem.ProductCraft = fpmProcessInOrderItem.ProductCraft
		fpmInOrderItem.ProductIngredient = fpmProcessInOrderItem.ProductIngredient
		fpmInOrderItem.InRoll = fpmProcessInOrderItem.InRoll
		fpmInOrderItem.CustomerAccountNum = fpmProcessInOrderItem.CustomerAccountNum
		fpmInOrderItem.ContractNumber = fpmProcessInOrderItem.ContractNumber
		fpmInOrderItem.DyeDeliveryOrderId = fpmProcessInOrderItem.DyeDeliveryOrderId
		fpmInOrderItem.DyeDeliveryOrderNo = fpmProcessInOrderItem.DyeDeliveryOrderNo
		fpmInOrderItem.UseGfRoll = fpmProcessInOrderItem.UseGfRoll
		fpmInOrderItem.UseGfWeight = fpmProcessInOrderItem.UseGfWeight
		fpmInOrderItem.InWeight = fpmProcessInOrderItem.InWeight
		fpmInOrderItem.WeightError = fpmProcessInOrderItem.WeightError
		fpmInOrderItem.SettleWeight = fpmProcessInOrderItem.SettleWeight
		fpmInOrderItem.UnitId = fpmProcessInOrderItem.UnitId
		fpmInOrderItem.DyeDeliveryOrderWeight = fpmProcessInOrderItem.DyeDeliveryOrderWeight
		fpmInOrderItem.InLength = fpmProcessInOrderItem.InLength
		fpmInOrderItem.Remark = fpmProcessInOrderItem.Remark
		fpmInOrderItem.FinishProductWidthUnitId = fpmProcessInOrderItem.FinishProductWidthUnitId
		fpmInOrderItem.FinishProductGramWeightUnitId = fpmProcessInOrderItem.FinishProductGramWeightUnitId
		fpmInOrderItem.SalePlanOrderItemId = fpmProcessInOrderItem.SalePlanOrderItemId
		fpmInOrderItem.SalePlanOrderItemNo = fpmProcessInOrderItem.SalePlanOrderItemNo

		fpmInOrderItem, err = mysql.MustCreateFpmInOrderItem(tx, fpmInOrderItem)
		if err != nil {
			return
		}
	}
}

// /hcscm/admin/v1/temporary/product/wash_in_order_together [get]
func WashInOrderTogether(c *gin.Context) {
	var (
		q     = &structure.GetFpmInOrderListQuery{}
		list  = make(structure.GetFpmInOrderDataList, 0)
		total int
		err   error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	// 销售退货进仓单
	var (
		fpmSaleReturnInOrderList model.FpmSaleReturnInOrderList
	)
	fpmSaleReturnInOrderList, err = mysql.FindAllFpmSaleReturnInOrder(tx)
	if err != nil {
		return
	}
	for _, fpmSaleReturnInOrder := range fpmSaleReturnInOrderList {
		fpmInOrder := model.FpmInOrder{}
		fpmInOrder.Id = fpmSaleReturnInOrder.Id
		fpmInOrder.SaleSystemId = fpmSaleReturnInOrder.SaleSystemId
		fpmInOrder.InOrderType = common_product.WarehouseGoodInTypeSaleReturn
		fpmInOrder.BizUnitId = fpmSaleReturnInOrder.CustomerId
		fpmInOrder.WarehouseId = fpmSaleReturnInOrder.WarehouseId
		fpmInOrder.WarehouseInTime = fpmSaleReturnInOrder.WarehouseInTime
		fpmInOrder.StoreKeeperId = fpmSaleReturnInOrder.StoreKeeperId
		fpmInOrder.ReturnRemark = fpmSaleReturnInOrder.ReturnRemark
		fpmInOrder.Remark = fpmSaleReturnInOrder.Remark
		fpmInOrder.TotalRoll = fpmSaleReturnInOrder.TotalRoll
		fpmInOrder.TotalWeight = fpmSaleReturnInOrder.TotalWeight
		fpmInOrder.TotalLength = fpmSaleReturnInOrder.TotalLength
		fpmInOrder.UnitId = fpmSaleReturnInOrder.UnitId
		fpmInOrder.BusinessClose = fpmSaleReturnInOrder.BusinessClose
		fpmInOrder.BusinessCloseUserId = fpmSaleReturnInOrder.BusinessCloseUserId
		fpmInOrder.BusinessCloseUserName = fpmSaleReturnInOrder.BusinessCloseUserName
		fpmInOrder.BusinessCloseTime = fpmSaleReturnInOrder.BusinessCloseTime
		fpmInOrder.DepartmentId = fpmSaleReturnInOrder.DepartmentId
		fpmInOrder.OrderNo = fpmSaleReturnInOrder.OrderNo
		fpmInOrder.Number = fpmSaleReturnInOrder.Number
		fpmInOrder.AuditStatus = fpmSaleReturnInOrder.AuditStatus
		fpmInOrder.AuditorId = fpmSaleReturnInOrder.AuditorId
		fpmInOrder.AuditorName = fpmSaleReturnInOrder.AuditorName
		fpmInOrder.AuditDate = fpmSaleReturnInOrder.AuditTime
		fpmInOrder.VoucherNumber = fpmSaleReturnInOrder.VoucherNumber
		fpmInOrder.TextureUrl = fpmSaleReturnInOrder.TextureUrl
		fpmInOrder.SrcId = fpmSaleReturnInOrder.SrcOrderID
		fpmInOrder.SrcOrderNo = fpmSaleReturnInOrder.SrcOrderNo
		fpmInOrder, err = mysql.MustCreateFpmInOrder(tx, fpmInOrder)
		if err != nil {
			return
		}
	}

	// 成品销售调拨进仓单
	var (
		fpmSaleAllocateInOrderList model.FpmSaleAllocateInOrderList
	)
	fpmSaleAllocateInOrderList, err = mysql.FindAllFpmSaleAllocateInOrder(tx)
	if err != nil {
		return
	}
	for _, fpmSaleAllocateInOrder := range fpmSaleAllocateInOrderList {
		fpmInOrder := model.FpmInOrder{}
		fpmInOrder.Id = fpmSaleAllocateInOrder.Id
		fpmInOrder.InOrderType = common_product.WarehouseGoodInTypeSaleAllocate
		fpmInOrder.SaleAllocateOutId = fpmSaleAllocateInOrder.SaleAllocateOutId
		fpmInOrder.SaleAllocateOutOrderNo = fpmSaleAllocateInOrder.SaleAllocateOutOrderNo
		fpmInOrder.SaleSystemId = fpmSaleAllocateInOrder.SaleSystemId
		fpmInOrder.WarehouseId = fpmSaleAllocateInOrder.WarehouseId
		fpmInOrder.WarehouseOutId = fpmSaleAllocateInOrder.WarehouseOutId
		fpmInOrder.BizUnitId = fpmSaleAllocateInOrder.CustomerId
		fpmInOrder.StoreKeeperId = fpmSaleAllocateInOrder.StoreKeeperId
		fpmInOrder.SaleUserId = fpmSaleAllocateInOrder.SaleUserId
		fpmInOrder.SaleFollowerId = fpmSaleAllocateInOrder.SaleFollowerId
		fpmInOrder.DriverId = fpmSaleAllocateInOrder.DriverId
		fpmInOrder.LogisticsCompanyId = fpmSaleAllocateInOrder.LogisticsCompanyId
		fpmInOrder.WarehouseInTime = fpmSaleAllocateInOrder.WarehouseInTime
		fpmInOrder.Remark = fpmSaleAllocateInOrder.Remark
		fpmInOrder.TotalRoll = fpmSaleAllocateInOrder.TotalRoll
		fpmInOrder.TotalWeight = fpmSaleAllocateInOrder.TotalWeight
		fpmInOrder.TotalLength = fpmSaleAllocateInOrder.TotalLength
		fpmInOrder.UnitId = fpmSaleAllocateInOrder.UnitId
		fpmInOrder.BusinessClose = fpmSaleAllocateInOrder.BusinessClose
		fpmInOrder.BusinessCloseUserId = fpmSaleAllocateInOrder.BusinessCloseUserId
		fpmInOrder.BusinessCloseUserName = fpmSaleAllocateInOrder.BusinessCloseUserName
		fpmInOrder.BusinessCloseTime = fpmSaleAllocateInOrder.BusinessCloseTime
		fpmInOrder.DepartmentId = fpmSaleAllocateInOrder.DepartmentId
		fpmInOrder.OrderNo = fpmSaleAllocateInOrder.OrderNo
		fpmInOrder.Number = fpmSaleAllocateInOrder.Number
		fpmInOrder.AuditStatus = fpmSaleAllocateInOrder.AuditStatus
		fpmInOrder.AuditorId = fpmSaleAllocateInOrder.AuditorId
		fpmInOrder.AuditorName = fpmSaleAllocateInOrder.AuditorName
		fpmInOrder.AuditDate = fpmSaleAllocateInOrder.AuditTime
		fpmInOrder, err = mysql.MustCreateFpmInOrder(tx, fpmInOrder)
		if err != nil {
			return
		}
	}

	// 加工进仓单
	var (
		fpmProcessInOrderList model.FpmProcessInOrderList
	)
	fpmProcessInOrderList, err = mysql.FindAllFpmProcessInOrder(tx)
	if err != nil {
		return
	}
	for _, fpmProcessInOrder := range fpmProcessInOrderList {
		fpmInOrder := model.FpmInOrder{}
		fpmInOrder.Id = fpmProcessInOrder.Id
		fpmInOrder.InOrderType = common_product.WarehouseGoodInTypeProcess
		fpmInOrder.SaleSystemId = fpmProcessInOrder.SaleSystemId
		fpmInOrder.BizUnitId = fpmProcessInOrder.ProcessUnitId
		fpmInOrder.WarehouseId = fpmProcessInOrder.WarehouseId
		fpmInOrder.WarehouseInTime = fpmProcessInOrder.WarehouseInTime
		fpmInOrder.StoreKeeperId = fpmProcessInOrder.StoreKeeperId
		fpmInOrder.Remark = fpmProcessInOrder.Remark
		fpmInOrder.TotalRoll = fpmProcessInOrder.TotalRoll
		fpmInOrder.TotalWeight = fpmProcessInOrder.TotalWeight
		fpmInOrder.TotalLength = fpmProcessInOrder.TotalLength
		fpmInOrder.TotalPrice = fpmProcessInOrder.TotalPrice
		fpmInOrder.UnitId = fpmProcessInOrder.UnitId
		fpmInOrder.BusinessClose = fpmProcessInOrder.BusinessClose
		fpmInOrder.BusinessCloseUserId = fpmProcessInOrder.BusinessCloseUserId
		fpmInOrder.BusinessCloseUserName = fpmProcessInOrder.BusinessCloseUserName
		fpmInOrder.BusinessCloseTime = fpmProcessInOrder.BusinessCloseTime
		fpmInOrder.DepartmentId = fpmProcessInOrder.DepartmentId
		fpmInOrder.OrderNo = fpmProcessInOrder.OrderNo
		fpmInOrder.Number = fpmProcessInOrder.Number
		fpmInOrder.AuditStatus = fpmProcessInOrder.AuditStatus
		fpmInOrder.AuditorId = fpmProcessInOrder.AuditorId
		fpmInOrder.AuditorName = fpmProcessInOrder.AuditorName
		fpmInOrder.AuditDate = fpmProcessInOrder.AuditTime
		fpmInOrder.VoucherNumber = fpmProcessInOrder.VoucherNumber
		fpmInOrder.TextureUrl = fpmProcessInOrder.TextureUrl
		fpmInOrder, err = mysql.MustCreateFpmInOrder(tx, fpmInOrder)
		if err != nil {
			return
		}
	}

}
