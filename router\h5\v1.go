package h5

import (
	common "hcscm/common/system_consts"
	"hcscm/router/ai"
	"hcscm/router/basic_data"
	"hcscm/router/data_analysis"
	"hcscm/router/product"
	"hcscm/router/qywx"
	saleRouter "hcscm/router/sale"
	"hcscm/router/sale_price"
	"hcscm/router/should_collect_order"
	systemRouter "hcscm/router/system"
	"hcscm/server/system"
)

func H5(routerGroup *system.RouterGroup) {
	h5 := routerGroup.GroupByPlatform(common.PlatformH5, "h5", system.DistinctRequest(), system.JustAllowH5)
	// 后台
	V1H5(h5)
}

func V1H5(h5 *system.RouterGroup) {
	v1H5 := h5.Group("v1")
	h5Auth := v1H5.Group("", system.AuthH5)
	// 登录登出
	{
		v1H5.POST("登入", "login", system.H5Login)
		v1H5.POST("扫码登入", "scanCodeLogin", system.H5ScanCodeLogin)
		v1H5.POST("登出", "logout", system.Logout)

		h5Auth.GET("获取用户信息", "information", system.GetLoginInformation)
		h5Auth.GET("切换后台token", "switchAdminToken", system.SwitchAdminToken)
		h5Auth.GET("返回加密的账套id", "generateAccountSetId", system.GenerateAccountSetId)
	}

	{
		systemRouter.InitDepartment(h5Auth)             // 部门
		systemRouter.InitDictionary(h5Auth)             // 字典
		systemRouter.H5InitSaleSystem(h5Auth)           // 营销部门
		systemRouter.InitUser(h5Auth)                   // 用户
		systemRouter.InitEnum(h5Auth)                   // 枚举
		systemRouter.InitCDN(h5Auth)                    // CDN
		systemRouter.InitEmployee(h5Auth)               // 员工
		systemRouter.InitMenu(h5Auth)                   // 菜单
		systemRouter.InitResourceTree(h5Auth)           // 资源树
		systemRouter.InitRole(h5Auth)                   // 角色
		systemRouter.InitPrintTemplate(h5Auth)          // 打印模板
		systemRouter.InitListHabits(h5Auth)             // 列表习惯
		systemRouter.H5InitBusinessUnit(h5Auth)         // 来往单位
		systemRouter.H5InitBasicDataRawMaterial(h5Auth) // 易布原料资料
		basic_data.H5InitPhysicalWarehouse(h5Auth)      // 仓库
		basic_data.H5InitInfoBasicData(h5Auth)          // 基础数据
		basic_data.H5InitBasicDataType(h5Auth)          // 基础数据类型
		basic_data.InitBasicDataTypeEnum(h5Auth)        // 基础数据模块枚举
		basic_data.H5InitGreyFabricInfo(h5Auth)         // 坯布资料
		basic_data.InitFabricDyeProcessInfo(h5Auth)     // 布料染整工艺资料
		systemRouter.H5InitDistrictArea(h5Auth)         // 染整枚举
		systemRouter.H5InitBizUnitFactoryLogistics(h5Auth)
	}
	// 成品
	{
		product.H5InitFinishProduct(h5Auth)
	}
	// 销售管理
	{
		saleRouter.H5InitSale(h5Auth)
	}
	// 成品销售价格
	{
		sale_price.InitSalePrice(h5Auth)
	}
	// 库存管理
	{
		product.H5InitStockProduct(h5Auth)
	}

	// 成品管理
	{
		product.H5InitFpmInOrder(h5Auth)
	}

	// 应收管理
	{
		should_collect_order.InitShouldCollectOrderReportForms(h5Auth)
	}
	// 数据分析
	{
		data_analysis.InitDataAnalysis(h5Auth)
	}
	// 企业微信
	{
		qywx.InitQYWX(h5Auth)
	}
	// ai
	{
		ai.InitAi(h5Auth)
	}
	// 线下拜访(客户跟进记录)
	{
		qywx.InitCustomerFollowRecord(h5Auth)
	}
}
