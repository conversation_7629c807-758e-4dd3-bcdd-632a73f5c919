// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v5.29.1
// source: api/wx/group_robot/group_robot_service.proto

package v1

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GroupRobot_CreateGroupRobot_FullMethodName = "/wx.api.wx.group_robot.v1.GroupRobot/CreateGroupRobot"
	GroupRobot_UpdateGroupRobot_FullMethodName = "/wx.api.wx.group_robot.v1.GroupRobot/UpdateGroupRobot"
	GroupRobot_DeleteGroupRobot_FullMethodName = "/wx.api.wx.group_robot.v1.GroupRobot/DeleteGroupRobot"
	GroupRobot_GetGroupRobot_FullMethodName    = "/wx.api.wx.group_robot.v1.GroupRobot/GetGroupRobot"
	GroupRobot_ListGroupRobot_FullMethodName   = "/wx.api.wx.group_robot.v1.GroupRobot/ListGroupRobot"
)

// GroupRobotClient is the client API for GroupRobot service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GroupRobotClient interface {
	// CreateGroupRobot 创建群机器人信息
	CreateGroupRobot(ctx context.Context, in *CreateGroupRobotRequest, opts ...grpc.CallOption) (*CreateGroupRobotReply, error)
	// UpdateGroupRobot 更新群机器人信息
	UpdateGroupRobot(ctx context.Context, in *UpdateGroupRobotRequest, opts ...grpc.CallOption) (*UpdateGroupRobotReply, error)
	// DeleteGroupRobot 删除群机器人信息
	DeleteGroupRobot(ctx context.Context, in *DeleteGroupRobotRequest, opts ...grpc.CallOption) (*DeleteGroupRobotReply, error)
	// GetGroupRobot 获取指定的群机器人信息
	GetGroupRobot(ctx context.Context, in *GetGroupRobotRequest, opts ...grpc.CallOption) (*GetGroupRobotReply, error)
	// ListGroupRobot 获取群机器人信息列表
	ListGroupRobot(ctx context.Context, in *ListGroupRobotRequest, opts ...grpc.CallOption) (*ListGroupRobotReply, error)
}

type groupRobotClient struct {
	cc grpc.ClientConnInterface
}

func NewGroupRobotClient(cc grpc.ClientConnInterface) GroupRobotClient {
	return &groupRobotClient{cc}
}

func (c *groupRobotClient) CreateGroupRobot(ctx context.Context, in *CreateGroupRobotRequest, opts ...grpc.CallOption) (*CreateGroupRobotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateGroupRobotReply)
	err := c.cc.Invoke(ctx, GroupRobot_CreateGroupRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupRobotClient) UpdateGroupRobot(ctx context.Context, in *UpdateGroupRobotRequest, opts ...grpc.CallOption) (*UpdateGroupRobotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateGroupRobotReply)
	err := c.cc.Invoke(ctx, GroupRobot_UpdateGroupRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupRobotClient) DeleteGroupRobot(ctx context.Context, in *DeleteGroupRobotRequest, opts ...grpc.CallOption) (*DeleteGroupRobotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteGroupRobotReply)
	err := c.cc.Invoke(ctx, GroupRobot_DeleteGroupRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupRobotClient) GetGroupRobot(ctx context.Context, in *GetGroupRobotRequest, opts ...grpc.CallOption) (*GetGroupRobotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetGroupRobotReply)
	err := c.cc.Invoke(ctx, GroupRobot_GetGroupRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *groupRobotClient) ListGroupRobot(ctx context.Context, in *ListGroupRobotRequest, opts ...grpc.CallOption) (*ListGroupRobotReply, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ListGroupRobotReply)
	err := c.cc.Invoke(ctx, GroupRobot_ListGroupRobot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GroupRobotServer is the server API for GroupRobot service.
// All implementations must embed UnimplementedGroupRobotServer
// for forward compatibility.
type GroupRobotServer interface {
	// CreateGroupRobot 创建群机器人信息
	CreateGroupRobot(context.Context, *CreateGroupRobotRequest) (*CreateGroupRobotReply, error)
	// UpdateGroupRobot 更新群机器人信息
	UpdateGroupRobot(context.Context, *UpdateGroupRobotRequest) (*UpdateGroupRobotReply, error)
	// DeleteGroupRobot 删除群机器人信息
	DeleteGroupRobot(context.Context, *DeleteGroupRobotRequest) (*DeleteGroupRobotReply, error)
	// GetGroupRobot 获取指定的群机器人信息
	GetGroupRobot(context.Context, *GetGroupRobotRequest) (*GetGroupRobotReply, error)
	// ListGroupRobot 获取群机器人信息列表
	ListGroupRobot(context.Context, *ListGroupRobotRequest) (*ListGroupRobotReply, error)
	mustEmbedUnimplementedGroupRobotServer()
}

// UnimplementedGroupRobotServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGroupRobotServer struct{}

func (UnimplementedGroupRobotServer) CreateGroupRobot(context.Context, *CreateGroupRobotRequest) (*CreateGroupRobotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateGroupRobot not implemented")
}
func (UnimplementedGroupRobotServer) UpdateGroupRobot(context.Context, *UpdateGroupRobotRequest) (*UpdateGroupRobotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateGroupRobot not implemented")
}
func (UnimplementedGroupRobotServer) DeleteGroupRobot(context.Context, *DeleteGroupRobotRequest) (*DeleteGroupRobotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteGroupRobot not implemented")
}
func (UnimplementedGroupRobotServer) GetGroupRobot(context.Context, *GetGroupRobotRequest) (*GetGroupRobotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetGroupRobot not implemented")
}
func (UnimplementedGroupRobotServer) ListGroupRobot(context.Context, *ListGroupRobotRequest) (*ListGroupRobotReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListGroupRobot not implemented")
}
func (UnimplementedGroupRobotServer) mustEmbedUnimplementedGroupRobotServer() {}
func (UnimplementedGroupRobotServer) testEmbeddedByValue()                    {}

// UnsafeGroupRobotServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GroupRobotServer will
// result in compilation errors.
type UnsafeGroupRobotServer interface {
	mustEmbedUnimplementedGroupRobotServer()
}

func RegisterGroupRobotServer(s grpc.ServiceRegistrar, srv GroupRobotServer) {
	// If the following call pancis, it indicates UnimplementedGroupRobotServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GroupRobot_ServiceDesc, srv)
}

func _GroupRobot_CreateGroupRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateGroupRobotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupRobotServer).CreateGroupRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroupRobot_CreateGroupRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupRobotServer).CreateGroupRobot(ctx, req.(*CreateGroupRobotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupRobot_UpdateGroupRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateGroupRobotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupRobotServer).UpdateGroupRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroupRobot_UpdateGroupRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupRobotServer).UpdateGroupRobot(ctx, req.(*UpdateGroupRobotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupRobot_DeleteGroupRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteGroupRobotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupRobotServer).DeleteGroupRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroupRobot_DeleteGroupRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupRobotServer).DeleteGroupRobot(ctx, req.(*DeleteGroupRobotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupRobot_GetGroupRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetGroupRobotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupRobotServer).GetGroupRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroupRobot_GetGroupRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupRobotServer).GetGroupRobot(ctx, req.(*GetGroupRobotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GroupRobot_ListGroupRobot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListGroupRobotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GroupRobotServer).ListGroupRobot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GroupRobot_ListGroupRobot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GroupRobotServer).ListGroupRobot(ctx, req.(*ListGroupRobotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GroupRobot_ServiceDesc is the grpc.ServiceDesc for GroupRobot service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GroupRobot_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "wx.api.wx.group_robot.v1.GroupRobot",
	HandlerType: (*GroupRobotServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateGroupRobot",
			Handler:    _GroupRobot_CreateGroupRobot_Handler,
		},
		{
			MethodName: "UpdateGroupRobot",
			Handler:    _GroupRobot_UpdateGroupRobot_Handler,
		},
		{
			MethodName: "DeleteGroupRobot",
			Handler:    _GroupRobot_DeleteGroupRobot_Handler,
		},
		{
			MethodName: "GetGroupRobot",
			Handler:    _GroupRobot_GetGroupRobot_Handler,
		},
		{
			MethodName: "ListGroupRobot",
			Handler:    _GroupRobot_ListGroupRobot_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "api/wx/group_robot/group_robot_service.proto",
}
