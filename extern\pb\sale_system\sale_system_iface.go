package sale_system

import (
	"context"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/system"
)

type Client interface {
	GetSaleSystemById(ctx context.Context, id uint64) (name map[uint64]string, err error)
	GetSaleSystemDetailById(ctx context.Context, id uint64) (data Res, err error)
	GetSaleSystemByIds(ctx context.Context, ids []uint64) (map[uint64]string, error)
	GetSaleSystemByQuery(ctx context.Context, req Req) (data Res, err error)
	GetSaleSystemByQueryList(ctx context.Context, req Req) (list ResList, err error)

	// 根据ids获取营销体系map
	GetSaleSystemListMapByIds(ctx context.Context, ids []uint64) (res map[uint64]*Res, err error)
	GetSaleSystemIdsByLikeName(ctx context.Context, name string) (ids []uint64, err error)
	GetAllSaleSystemIds(ctx context.Context, status common.Status) (ids []uint64, err error)
}

func NewSaleSystemClient() *salSystem {
	return &salSystem{}
}

type salSystem struct {
}

func (s salSystem) GetSaleSystemById(ctx context.Context, id uint64) (name map[uint64]string, err error) {
	var (
		saleSystem mysql.SaleSystem
		m          = make(map[uint64]string)
	)
	if id == 0 {
		return m, nil
	}
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystem, _, err = mysql.FirstSaleSystemByID(tx, id)
	if err != nil {
		return
	}
	m[id] = saleSystem.Name
	name = m
	return
}

func (s salSystem) GetSaleSystemByIds(ctx context.Context, ids []uint64) (map[uint64]string, error) {
	var (
		saleSystems mysql.SaleSystemList
		m           = make(map[uint64]string)
		err         error
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystems, err = mysql.FindSaleSystemByID(tx, ids)
	if err != nil {
		return m, err
	}
	for _, saleSystem := range saleSystems {
		m[saleSystem.Id] = saleSystem.Name
	}

	return m, err
}

func (s salSystem) GetSaleSystemByQueryList(ctx context.Context, req Req) (list ResList, err error) {
	var (
		saleSystems mysql.SaleSystemList
		m           = make(ResList, 0)
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystems, err = mysql.FindSaleSystemBySaleSystemID(tx, req)
	if err != nil {
		return
	}

	for _, saleSystem := range saleSystems {
		r := &Res{}
		buildSaleSystem(ctx, r, saleSystem)
		m = append(m, *r)
	}
	list = m
	return
}

func (s salSystem) GetSaleSystemByQuery(ctx context.Context, req Req) (data Res, err error) {
	var (
		saleSystem mysql.SaleSystem
		exist      bool
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystem, exist, err = mysql.FirstSaleSystemByID(tx, req.Id)
	if err != nil {
		return
	}
	if !exist {
		return
	}

	r := &Res{}
	buildSaleSystem(ctx, r, saleSystem)
	data = *r
	return
}

func (s salSystem) GetSaleSystemName(ctx context.Context, id uint64) (string, error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystem, _, err := mysql.FirstSaleSystemByID(tx, id)
	if err != nil {
		return "", err
	}
	return saleSystem.Name, nil
}

// 名称 电话 地址
func (s salSystem) GetSaleSystemInfoById(ctx context.Context, id uint64) (name map[uint64][4]string, err error) {
	var (
		saleSystem mysql.SaleSystem
		m          = make(map[uint64][4]string)
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystem, _, err = mysql.FirstSaleSystemByID(tx, id)
	if err != nil {
		return
	}
	m[id] = [4]string{saleSystem.Name, saleSystem.Phone, saleSystem.AddressDetail, saleSystem.Contacts}
	name = m
	return
}

// 根据编号或名称获取
func (s salSystem) GetSaleSystemByCodeOrName(ctx context.Context, code, name []string) (res ResList, err error) {
	if len(code) == 0 && len(name) == 0 {
		return
	}
	var (
		saleSystems mysql.SaleSystemList
		list        = make(ResList, 0)
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystems, err = mysql.FindTypeIntercourseUnitsByCodeOrName(tx, code, name)
	if err != nil {
		return
	}

	for _, saleSystem := range saleSystems {
		r := &Res{}
		buildSaleSystem(ctx, r, saleSystem)
		list = append(list, *r)
	}
	res = list
	return
}

// 根据ids获取营销体系map
func (s salSystem) GetSaleSystemListMapByIds(ctx context.Context, ids []uint64) (res map[uint64]*Res, err error) {
	if len(ids) == 0 {
		return
	}
	var (
		saleSystems mysql.SaleSystemList
		m           = make(map[uint64]*Res, 0)
	)
	res = m
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystems, err = mysql.FindSaleSystemByID(tx, ids)
	if err != nil {
		return
	}

	for _, saleSystem := range saleSystems {
		r := &Res{}
		buildSaleSystem(ctx, r, saleSystem)
		res[r.Id] = r
	}
	return
}

func buildSaleSystem(
	ctx context.Context,
	r *Res,
	saleSystem mysql.SaleSystem,
) {
	r.Id = saleSystem.Id
	r.Code = saleSystem.Code
	r.Name = saleSystem.Name
	r.AddressDetail = saleSystem.AddressDetail
	r.Contacts = saleSystem.Contacts
	r.Phone = saleSystem.Phone
	r.FaxNumber = saleSystem.FaxNumber
	r.Status = saleSystem.Status
	r.Email = saleSystem.Email
	r.Remark = saleSystem.Remark
	r.SaleRadixPointSign = saleSystem.SaleRadixPointSign
	r.SaleTotalRadixPointSign = saleSystem.SaleTotalRadixPointSign
	r.SaleTaxRateDefinition = saleSystem.SaleTaxRateDefinition
	r.LowSaleWeightLimit = saleSystem.LowSaleWeightLimit
	r.HighSaleWeightLimit = saleSystem.HighSaleWeightLimit
	r.FinProPurRadixPointSign = saleSystem.FinProPurRadixPointSign
	r.FinProPurTotalRadixPointSign = saleSystem.FinProPurTotalRadixPointSign
	r.DefaultPhysicalWarehouse = saleSystem.DefaultPhysicalWarehouse
	r.DefaultCustomerId = saleSystem.DefaultCustomerId
	r.LowClothSaleWeightLimit = saleSystem.LowClothSaleWeightLimit
	r.HighClothSaleWeightLimit = saleSystem.HighClothSaleWeightLimit
	r.IsSettleLimit = saleSystem.IsSettleLimit
	r.SettleType = saleSystem.SettleType
	r.SettleCycle = saleSystem.SettleCycle
	r.CreditLimit = saleSystem.CreditLimit
	r.SaleOrderPrefix = saleSystem.SaleOrderPrefix
	r.ReturnOrderPrefix = saleSystem.ReturnOrderPrefix
	r.ArrangeOrderPrefix = saleSystem.ArrangeOrderPrefix
	r.PurchaseOrderPrefix = saleSystem.PurchaseOrderPrefix
	r.AllocateOrderPrefix = saleSystem.AllocateOrderPrefix
	r.ProduceOrderPrefix = saleSystem.ProduceOrderPrefix
	r.DyeingOrderPrefix = saleSystem.DyeingOrderPrefix
	r.DeliveryOrderPrefix = saleSystem.DeliveryOrderPrefix
}

func (s salSystem) GetSaleSystemIdsByLikeName(ctx context.Context, name string) (ids []uint64, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	list, err := mysql.FindSaleSystemByLikeName(tx, name)
	if err != nil {
		return
	}
	for _, saleSystem := range list {
		ids = append(ids, saleSystem.Id)
	}
	return
}

func (s salSystem) GetAllSaleSystemIds(ctx context.Context, status common.Status) (ids []uint64, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	list, err := mysql.FindAllSaleSystem(tx, status)
	if err != nil {
		return
	}
	for _, saleSystem := range list {
		ids = append(ids, saleSystem.Id)
	}
	return
}

func (s salSystem) GetSaleSystemDetailById(ctx context.Context, id uint64) (data Res, err error) {
	var (
		saleSystem mysql.SaleSystem
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	saleSystem, _, err = mysql.FirstSaleSystemByID(tx, id)
	if err != nil {
		return
	}
	data.Id = saleSystem.Id
	data.Name = saleSystem.Name
	data.Phone = saleSystem.Phone
	data.Contacts = saleSystem.Contacts
	data.FaxNumber = saleSystem.FaxNumber
	data.AddressDetail = saleSystem.AddressDetail
	data.DefaultLastSalePrice = saleSystem.DefaultLastSalePrice
	data.DefaultCustomerId = saleSystem.DefaultCustomerId
	data.DefaultSupplierId = saleSystem.DefaultSupplierId
	data.DefaultLastSalePrice = saleSystem.DefaultLastSalePrice
	data.DefaultLastCostPrice = saleSystem.DefaultLastCostPrice
	return
}
