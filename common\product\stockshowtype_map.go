package product

func GetStockShowTypeMap() (r map[StockShowType]string) {
	l := []StockShowType{StockShowTypeAll, StockShowTypeRoll, StockShowTypeWeight, StockShowTypeZero}
	r = make(map[StockShowType]string)
	for _, k := range l {
		r[k] = k.String()
	}
	return r
}
func GetStockShowTypeReverseMap() (r map[string]StockShowType) {
	l := []StockShowType{StockShowTypeAll, StockShowTypeRoll, StockShowTypeWeight, StockShowTypeZero}
	r = make(map[string]StockShowType)
	for _, k := range l {
		r[k.String()] = k
	}
	return r
}
func GetStockShowTypeReverseIntMap() (r map[string]int) {
	l := []StockShowType{StockShowTypeAll, StockShowTypeRoll, StockShowTypeWeight, StockShowTypeZero}
	r = make(map[string]int)
	for _, k := range l {
		r[k.String()] = int(k)
	}
	return r
}

func (t StockShowType) Check() bool {
	l := []StockShowType{StockShowTypeAll, StockShowTypeRoll, StockShowTypeWeight, StockShowTypeZero}
	for i := range l {
		if l[i] == t {
			return true
		}
	}
	return false
}
