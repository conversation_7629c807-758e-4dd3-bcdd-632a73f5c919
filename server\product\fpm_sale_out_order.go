package product

import (
	"context"
	"github.com/gin-gonic/gin"
	common_product "hcscm/common/product"
	shouldCollectOrderPb "hcscm/extern/pb/should_collect_order"
	"hcscm/model/mysql/mysql_base"
	"hcscm/model/redis"
	"hcscm/server/system"
	svc "hcscm/service/product"
	shouldCollectOrderSvc "hcscm/service/should_collect_order"
	structure "hcscm/structure/product"
	shouldCollectOrderStructure "hcscm/structure/should_collect_order"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

// @Tags		【成品销售出仓单】
// @Summary	添加成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmSaleOutOrderParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.AddFpmSaleOutOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/addFpmSaleOutOrder [post]
// @Tags		【成品销售出仓单】
// @Summary	添加成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmSaleOutOrderParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.AddFpmSaleOutOrderData{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/addFpmSaleOutOrder [post]
func AddFpmSaleOutOrder(c *gin.Context) {
	var (
		q    = &structure.AddFpmSaleOutOrderParam{}
		data = structure.AddFpmSaleOutOrderData{}
		svc  = svc.NewFpmSaleOutOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	q.OutOrderType = common_product.WarehouseGoodOutTypeSale
	data, err = svc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrder [put]
// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderData{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrder [put]
func UpdateFpmSaleOutOrder(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleOutOrderParam{}
		data = structure.UpdateFpmSaleOutOrderData{}
		svc  = svc.NewFpmSaleOutOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Update(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderBusinessCloseParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderBusinessClose [put]
// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单业务状态
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderBusinessCloseParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderData{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/UpdateFpmSaleOutOrderBusinessClose [put]
func UpdateFpmSaleOutOrderBusinessClose(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleOutOrderBusinessCloseParam{}
		data = structure.UpdateFpmSaleOutOrderData{}
		svc  = svc.NewFpmSaleOutOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.UpdateBusinessClose(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusWait [put]
func UpdateFpmSaleOutOrderStatusWait(c *gin.Context) {
	var (
		q           = &structure.UpdateFpmSaleOutOrderStatusParam{}
		data        = structure.UpdateFpmSaleOutOrderStatusData{}
		outOrderSvc = svc.NewFpmSaleOutOrderService()
		stockSvc    = svc.NewStockProductService()
		err         error
		rLocks      = make(redis.LockForRedisList, 0)
		updateItems structure.UpdateStockProductDetailParamList
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		data, updateItems, err = outOrderSvc.UpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}
	}

	// 判断是否齐单并更新配布单出仓状态
	err = outOrderSvc.CheckAndUpdateArrangeOrderOutStatus(ctx, tx, q.Id.ToUint64())
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单状态-驳回
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusReject [put]
func UpdateFpmSaleOutOrderStatusReject(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleOutOrderStatusParam{}
		data = structure.UpdateFpmSaleOutOrderStatusData{}
		svc  = svc.NewFpmSaleOutOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	for _, id := range q.Id.ToUint64() {
		data, err = svc.UpdateStatusReject(ctx, id)
		if err != nil {
			return
		}
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusCancel [put]
// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusCancel [put]
func UpdateFpmSaleOutOrderStatusCancel(c *gin.Context) {
	var (
		q           = &structure.UpdateFpmSaleOutOrderStatusParam{}
		data        = structure.UpdateFpmSaleOutOrderStatusData{}
		outOrderSvc = svc.NewFpmSaleOutOrderService()
		// stockSvc    = svc.NewStockProductService()
		rLocks = make(redis.LockForRedisList, 0)
		err    error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		data, err = outOrderSvc.UpdateStatusCancel(ctx, tx, id)
		if err != nil {
			return
		}
		// if data.ArrangeId > 0 && len(data.StockDetailIds) > 0 {
		// 	// 变更详细库存状态为入仓中
		// 	rLocks, _, err = stockSvc.UpdateDetailStatusArrangeStatusWarehouseIn(ctx, tx, rLocks, data.StockDetailIds)
		// 	if err != nil {
		// 		return
		// 	}
		// }
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	获取成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetFpmSaleOutOrderData{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/getFpmSaleOutOrder [get]
// @Tags		【成品销售出仓单】
// @Summary	获取成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		id	query		int	true	"id"
// @Success	200	{object}	structure.GetFpmSaleOutOrderData{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/getFpmSaleOutOrder [get]
func GetFpmSaleOutOrder(c *gin.Context) {
	var (
		q    = &structure.GetFpmSaleOutOrderQuery{}
		data = structure.GetFpmSaleOutOrderData{}
		svc  = svc.NewFpmSaleOutOrderService()
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = svc.Get(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	获取成品销售出仓单列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		arrange_order_no		query		string	false	"配布单号"
// @Param		customer		query		int		false	"客户id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		in_time_begin	query		string	false	"进仓开始时间"
// @Param		in_time_end		query		string	false	"进仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmSaleOutOrderDataList{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/getFpmSaleOutOrderList [get]
// @Tags		【成品销售出仓单】
// @Summary	获取成品销售出仓单列表
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		page			query		int		false	"page"
// @Param		size			query		int		false	"size"
// @Param		offset			query		int		false	"offset"
// @Param		limit			query		int		false	"limit"
// @Param		order_no		query		string	false	"单号"
// @Param		arrange_order_no		query		string	false	"配布单号"
// @Param		customer		query		int		false	"客户id"
// @Param		warehouse_id	query		int		false	"仓库id"
// @Param		out_time_begin	query		string	false	"出仓开始时间"
// @Param		out_time_end		query		string	false	"出仓结束时间"
// @Param		audit_status	query		string	false	"状态"
// @Param		download		query		int		false	"download"
// @Param		Platform		header		int		true	"终端ID"
// @Success	200				{object}	structure.GetFpmSaleOutOrderDataList{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/getFpmSaleOutOrderList [get]
func GetFpmSaleOutOrderList(c *gin.Context) {
	var (
		q     = &structure.GetFpmOutOrderListQuery{}
		list  = make(structure.GetFpmSaleOutOrderDataList, 0)
		total int
		err   error
		svc   = svc.NewFpmSaleOutOrderService()
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildListResponse(c, err, list, total)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	q.OutOrderType = common_product.WarehouseGoodOutTypeSale
	list, total, err = svc.GetList(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	添加成品销售出仓单
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.AddFpmSaleOutOrderParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.AddFpmSaleOutOrderData{}
// @Router		/hcscm/pda/v1/product/fpmSaleOutOrder/addFpmSaleOutOrder [post]
func PDAAddFpmSaleOutOrder(c *gin.Context) {
	var (
		q                  = &structure.AddFpmSaleOutOrderParam{}
		data               = structure.AddFpmSaleOutOrderData{}
		fpmSaleOutOrderSvc = svc.NewFpmSaleOutOrderService()
		err                error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	data, err = fpmSaleOutOrderSvc.Add(ctx, q)
	if err != nil {
		return
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单状态-消审
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/mpupdateFpmSaleOutOrderStatusWait [put]
func MPUpdateFpmSaleOutOrderStatusWait(c *gin.Context) {
	var (
		q                       = &structure.UpdateFpmSaleOutOrderStatusParam{}
		data                    = structure.MPUpdateFpmSaleOutOrderStatusData{}
		outOrderSvc             = svc.NewFpmSaleOutOrderService()
		stockSvc                = svc.NewStockProductService()
		err                     error
		rLocks                  = make(redis.LockForRedisList, 0)
		updateItems             structure.UpdateStockProductDetailParamList
		shouldCollectOrderPbSvc = shouldCollectOrderPb.NewShouldCollectOrderClient()
	)

	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		// 判断是否有对应的应收单
		err = shouldCollectOrderPbSvc.IsShouldCollectOrderCancel(ctx, id)
		if err != nil {
			return
		}

		data, updateItems, err = outOrderSvc.MPUpdateStatusWait(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}
	}

	return
}

// @Tags		【成品销售出仓单】
// @Summary	更新成品销售出仓单状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/admin/v1/product/fpmSaleOutOrder/updateFpmSaleOutOrderStatusPass [put]
// @Tags		【易布 成品销售出仓单】
// @Summary	更新成品销售出仓单状态-审核
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/mpupdateFpmSaleOutOrderStatusPass [put]
func UpdateFpmSaleOutOrderStatusPass(c *gin.Context) {
	var (
		q    = &structure.UpdateFpmSaleOutOrderStatusParam{}
		data = structure.UpdateFpmSaleOutOrderStatusData{}
		err  error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()

	data, err = ServiceUpdateFpmSaleOutOrderStatusPass(ctx, q.Id.ToUint64(), tx)
	return
}

func ServiceUpdateFpmSaleOutOrderStatusPass(ctx context.Context, fpmSaleOutOrderIDs []uint64, tx *mysql_base.Tx) (data structure.UpdateFpmSaleOutOrderStatusData, err error) {
	var (
		outOrderSvc = svc.NewFpmSaleOutOrderService()
		stockSvc    = svc.NewStockProductService()
		rLocks      = make(redis.LockForRedisList, 0)
		updateItems structure.UpdateStockProductDetailParamList
	)
	defer func() {
		rLocks.Unlock()
	}()

	for _, id := range fpmSaleOutOrderIDs {
		// 更新销售出仓单状态为已审核
		data, updateItems, err = outOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			rLocks, err = stockSvc.StockProductLock(ctx, tx, rLocks, nil, updateItems)
			if err != nil {
				return
			}
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}
		//updateBookOrderByFpmOutOrder(ctx, tx, id, updateItems)
		// 创建一个新的 SaleOutOrderParam 实例
		saleOutOrderParam := &structure.SaleOutOrderParam{
			Param: structure_base.Param{},
			Id:    id, // 将 q.Id 的值赋给 saleOutOrderParam.Id
		}
		// 调用 addShouldCollectOrderBySaleOutOrder 函数，并传递 saleOutOrderParam 和 q.IsAudit
		addShouldCollectOrderBySaleOutOrder(ctx, tx, saleOutOrderParam, true)
	}
	return
}

// 根据出仓单ID查找对应的销售单，并更新库存占用日志
//func updateBookOrderByFpmOutOrder(ctx context.Context, tx *mysql_base.Tx, outOrderId uint64, updateItems structure.UpdateStockProductDetailParamList) {
//	var (
//		out_svc       = svc.NewFpmOutOrderService()
//		outOrder      structure.GetFpmOutOrderData
//		arrangeOrder  structure.GetFpmArrangeOrderData
//		arrangeRepo   = aggs.NewFpmArrangeOrderRepo(tx)
//		err           error
//		saleOrderType sale.SendProductType
//	)
//
//	outOrder, err = out_svc.Get(ctx, tx, &structure.GetFpmOutOrderQuery{Id: outOrderId})
//	if err != nil {
//		return
//	}
//
//	if outOrder.ArrangeOrderId <= 0 {
//		return
//	}
//
//	arrangeOrder, err = arrangeRepo.Get(ctx, &structure.GetFpmArrangeOrderQuery{Id: outOrder.ArrangeOrderId})
//	if err != nil {
//		return
//	}
//
//	// 3. 通过配布单srcid找到对应的成品销售单
//	if arrangeOrder.SrcId <= 0 {
//		return
//	}
//
//	// 4. 判断销售单类型是否为调拨
//	saleOrderType = arrangeOrder.SendProductType
//	if saleOrderType != sale.SendProductTypeSaleAllocate { // 2表示销调类型
//		return
//	}
//
//	// 5. 是调拨类型，更新库存占用日志
//	// 查询对应的库存占用日志
//	bookLog, err := dao.FindStockProductBookLogByOrderId(tx, arrangeOrder.SrcId, common_system.BookOrderTypeProductSaleOutPass)
//	if err != nil {
//		return
//	}
//	if bookLog.Id != 0 {
//		bookLog.BookOrderId = arrangeOrder.SrcId
//		// 保存更新
//		_, err = dao.NewStockProductBookLogDao(tx).MustUpdateStockProductBookLog(bookLog)
//		if err != nil {
//			return
//		}
//	}
//
//}

// 审核销售出仓单 生成应收单
func ServiceUpdateFpmSaleOutOrderStatusPassV2(ctx context.Context, rLocks redis.LockForRedisList, fpmSaleOutOrderIDs []uint64, tx *mysql_base.Tx, stockSvc *svc.StockProductService) (
	data structure.UpdateFpmSaleOutOrderStatusData, locks redis.LockForRedisList, err error) {
	var (
		outOrderSvc = svc.NewFpmSaleOutOrderService()
		updateItems structure.UpdateStockProductDetailParamList
	)
	locks = rLocks
	for _, id := range fpmSaleOutOrderIDs {
		// 更新销售出仓单状态为已审核
		data, updateItems, err = outOrderSvc.UpdateStatusPass(ctx, tx, id)
		if err != nil {
			return
		}
		if len(updateItems) != 0 {
			// 同一个方法内，不需要重新获取锁
			// locks, err = stockSvc.StockProductLock(ctx, tx, locks, nil, updateItems)
			// if err != nil {
			// 	return
			// }
			// 更新相关库存信息的盘点信息
			_, err = stockSvc.UpdateDetails(ctx, tx, updateItems)
			if err != nil {
				return
			}
		}
		// 创建一个新的 SaleOutOrderParam 实例
		saleOutOrderParam := &structure.SaleOutOrderParam{
			Param: structure_base.Param{},
			Id:    id, // 将 q.Id 的值赋给 saleOutOrderParam.Id
		}
		// 调用 addShouldCollectOrderBySaleOutOrder 函数，并传递 saleOutOrderParam 和 q.IsAudit
		addShouldCollectOrderBySaleOutOrder(ctx, tx, saleOutOrderParam, true)
	}
	return
}

// 创建销售送货单（应收单）
func addShouldCollectOrderBySaleOutOrder(ctx context.Context, tx *mysql_base.Tx, q *structure.SaleOutOrderParam, isAudit bool) {
	var (
		data                       = structure.MPUpdateFpmSaleOutOrderStatusPassData{}
		outOrderSvc                = svc.NewFpmSaleOutOrderService()
		err                        error
		shouldCollectOrderIdMap    = make(map[uint64]uint64)
		shouldCollectOrderSvc      = shouldCollectOrderSvc.NewProductSaleShouldCollectOrderService(nil)
		addShouldCollectOrderItems shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParamList
		addShouldCollectOrderItem  *shouldCollectOrderStructure.AddProductSaleShouldCollectOrderParam
	)

	tx, commit := mysql_base.TransactionMainEx(tx, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	// 获取应收单信息
	data, addShouldCollectOrderItem, err = outOrderSvc.GetroveSaleOutOrderParam(ctx, q, tx)
	if addShouldCollectOrderItem != nil {
		addShouldCollectOrderItems = append(addShouldCollectOrderItems, addShouldCollectOrderItem)
	}
	if err != nil {
		return
	}

	// 将应收单信息添加进数据库
	if len(addShouldCollectOrderItems) != 0 {
		shouldCollectOrderIdMap, err = shouldCollectOrderSvc.Add(ctx, tx, addShouldCollectOrderItems)
		if err != nil {
			return
		}
		data.ShouldCollectOrderId = shouldCollectOrderIdMap[data.Id]
	}

	// 是否自动审核
	if isAudit {
		var shouldCollectOrderIds = make([]uint64, 0)
		for _, shouldCollectOrderId := range shouldCollectOrderIdMap {
			shouldCollectOrderIds = append(shouldCollectOrderIds, shouldCollectOrderId)
		}
		req := &shouldCollectOrderStructure.UpdateProductSaleShouldCollectOrderAuditStatusParam{
			Id: tools.QueryIntList(tools.UInt64s2String(shouldCollectOrderIds)),
		}
		_, _, err = shouldCollectOrderSvc.UpdateStatusPass(ctx, tx, req)
		if err != nil {
			return
		}
	}
	return

}

// @Summary	更新成品销售出仓单状态-作废
// @Security	ApiKeyAuth
// @accept		application/json
// @Produce	application/json
// @Param		body	body		structure.UpdateFpmSaleOutOrderStatusParam{}	true	"创建FpmSaleOutOrder"
// @Success	200		{object}	structure.UpdateFpmSaleOutOrderStatusParam{}
// @Router		/hcscm/mp/v1/product/fpmSaleOutOrder/mpupdateFpmSaleOutOrderStatusCancel [put]
func MPUpdateFpmSaleOutOrderStatusCancel(c *gin.Context) {
	var (
		q           = &structure.UpdateFpmSaleOutOrderStatusParam{}
		data        = structure.UpdateFpmSaleOutOrderStatusData{}
		outOrderSvc = svc.NewFpmSaleOutOrderService()
		// stockSvc    = svc.NewStockProductService()
		rLocks = make(redis.LockForRedisList, 0)
		err    error
	)
	ctx, ok := system.GetLoginInfoCtx(c)
	if !ok {
		return
	}

	defer func() {
		system.BuildResponse(c, err, data)
	}()

	err = system.ShouldBind(c, q)
	if err != nil {
		return
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
		rLocks.Unlock()
	}()

	for _, id := range q.Id.ToUint64() {
		data, err = outOrderSvc.MPUpdateStatusCancel(ctx, tx, id)
		if err != nil {
			return
		}
		// if data.ArrangeId > 0 && len(data.StockDetailIds) > 0 {
		// 	// 变更详细库存状态为入仓中
		// 	rLocks, _, err = stockSvc.UpdateDetailStatusArrangeStatusWarehouseIn(ctx, tx, rLocks, data.StockDetailIds)
		// 	if err != nil {
		// 		return
		// 	}
		// }
	}

	return
}
