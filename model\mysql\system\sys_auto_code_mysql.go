package mysql

import (
	"fmt"
	"hcscm/model/mysql/mysql_base"
	"hcscm/structure/system"
)

var GetSql = new(getSql)

type getSql struct{}

// GetDB 获取数据库的所有数据库名
func (s *getSql) GetDB(businessDB string) (data []system.Db, err error) {
	var entities []system.Db
	sql := "SELECT SCHEMA_NAME AS `database` FROM INFORMATION_SCHEMA.SCHEMATA;"
	if businessDB == "" {
		err = mysql_base.GetConn(nil).Raw(sql).Scan(&entities).Error
	}
	return entities, err
}

// GetTables 获取数据库的所有表名
func (s *getSql) GetTables(businessDB string, dbName string) (data []system.Table, err error) {
	var entities []system.Table
	sql := `select table_name as table_name from information_schema.tables where table_schema = ?`
	if businessDB == "" {
		err = mysql_base.GetConn(nil).Raw(sql, dbName).Scan(&entities).Error
	}

	return entities, err
}

// GetColumn 获取指定数据库和指定数据表的所有字段名,类型值等
func (s *getSql) GetColumn(businessDB string, tableName string, dbName string) (data []system.Column, err error) {
	var entities []system.Column
	sql := `
	SELECT COLUMN_NAME        column_name,
--        DATA_TYPE          data_type,
       COLUMN_TYPE column_type,
       COLUMN_DEFAULT column_default,
--        CASE DATA_TYPE
--            WHEN 'longtext' THEN c.CHARACTER_MAXIMUM_LENGTH
--            WHEN 'varchar' THEN c.CHARACTER_MAXIMUM_LENGTH
--            WHEN 'double' THEN CONCAT_WS(',', c.NUMERIC_PRECISION, c.NUMERIC_SCALE)
--            WHEN 'decimal' THEN CONCAT_WS(',', c.NUMERIC_PRECISION, c.NUMERIC_SCALE)
--            WHEN 'int' THEN c.NUMERIC_PRECISION
--            WHEN 'bigint' THEN c.NUMERIC_PRECISION
--            ELSE '' END AS data_type_long,
       COLUMN_COMMENT     column_comment
	FROM INFORMATION_SCHEMA.COLUMNS c
	WHERE table_name = ?
	  AND table_schema = ?
	`
	if businessDB == "" {
		err = mysql_base.GetConn(nil).Raw(sql, tableName, dbName).Scan(&entities).Error
	}

	return entities, err
}

// GetTableCreateSql 获取数据库的表的创建语句
func (s *getSql) GetTableCreateSql(dbName, tableName string) (data system.TableCreateSql, err error) {
	row := mysql_base.GetConn(nil).Raw(fmt.Sprintf("SHOW CREATE TABLE `%s`.`%s`;", dbName, tableName)).Row()
	err = row.Scan(&data.Table, &data.CreateTable)
	return
}

func (s *getSql) ExecSql(sql string) error {
	return mysql_base.GetConn(nil).Exec(sql).Error
}
