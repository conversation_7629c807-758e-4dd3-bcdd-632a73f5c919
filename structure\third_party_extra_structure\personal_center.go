package third_party_extra_structure

import (
	"hcscm/structure/base_structure"
	"time"
)

// GetPersonalCenterRequest 获取个人中心信息请求
type GetPersonalCenterRequest struct {
	UserId uint64 `json:"user_id" form:"user_id" binding:"required"`
}

// GetPersonalCenterResponse 获取个人中心信息响应
type GetPersonalCenterResponse struct {
	Id          uint64     `json:"id"`
	UserId      uint64     `json:"user_id"`
	Nickname    string     `json:"nickname"`
	Avatar      string     `json:"avatar"`
	Phone       string     `json:"phone"`
	Email       string     `json:"email"`
	Gender      int        `json:"gender"`
	Birthday    *time.Time `json:"birthday"`
	Address     string     `json:"address"`
	Description string     `json:"description"`
	Status      int        `json:"status"`
	CreateTime  time.Time  `json:"create_time"`
	UpdateTime  time.Time  `json:"update_time"`
}

// UpdatePersonalCenterRequest 更新个人中心信息请求
type UpdatePersonalCenterRequest struct {
	UserId      uint64     `json:"user_id" binding:"required"`
	Nickname    string     `json:"nickname"`
	Avatar      string     `json:"avatar"`
	Phone       string     `json:"phone"`
	Email       string     `json:"email"`
	Gender      int        `json:"gender"`
	Birthday    *time.Time `json:"birthday"`
	Address     string     `json:"address"`
	Description string     `json:"description"`
}

// GetPersonalCenterListRequest 获取个人中心列表请求
type GetPersonalCenterListRequest struct {
	base_structure.PageRequest
	UserId  uint64 `json:"user_id" form:"user_id"`
	Status  *int   `json:"status" form:"status"`
	Keyword string `json:"keyword" form:"keyword"`
}

// GetPersonalCenterListResponse 获取个人中心列表响应
type GetPersonalCenterListResponse struct {
	base_structure.PageResponse
	List []GetPersonalCenterResponse `json:"list"`
}
