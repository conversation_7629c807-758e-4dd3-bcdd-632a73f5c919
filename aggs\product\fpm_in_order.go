package product

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	common "hcscm/common/basic_data"
	"hcscm/common/errors"
	consts "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	base_product_pb "hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	empl_pb "hcscm/extern/pb/employee"
	purchase_pb "hcscm/extern/pb/purchase"
	sale_system_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	mysql "hcscm/model/mysql/product/dao"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/metadata"
	"hcscm/tools/set"
	"hcscm/vars"
	"strconv"
	"time"
)

type IFpmInOrderRepo interface {
	Add(ctx context.Context, req *structure.AddFpmInOrderParam) (data structure.AddFpmInOrderData, err error)
	Update(ctx context.Context, req *structure.UpdateFpmInOrderParam) (data structure.UpdateFpmInOrderData, err error)
	UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmInOrderBusinessCloseParam) (data structure.UpdateFpmInOrderData, err error)
	UpdateStatusPass(ctx context.Context, id uint64) (
		data structure.UpdateFpmInOrderStatusData,
		addItems structure.AddStockProductDetailParamList,
		updateItems structure.UpdateStockProductDetailParamList,
		salePlanOrderItemIds []uint64,
		err error,
	)
	UpdateStatusWait(ctx context.Context, id uint64) (
		data structure.UpdateFpmInOrderStatusData,
		updateItems structure.UpdateStockProductDetailParamList,
		salePlanOrderItemIds []uint64,
		err error)
	UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmInOrderStatusData, err error)
	UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmInOrderStatusData, err error)
	Exist(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error)
	Get(ctx context.Context, req *structure.GetFpmInOrderQuery) (data structure.GetFpmInOrderData, err error)
	GetList(ctx context.Context, req *structure.GetFpmInOrderListQuery) (list structure.GetFpmInOrderDataList, total int, err error)
	GetMPList(ctx context.Context, req *structure.GetMPFpmInOrderListQuery) (list structure.GetFpmInOrderDataList, total int, err error)
	GetListAndDetail(ctx context.Context, req *structure.GetFpmInOrderListQuery) (list structure.GetFpmInOrderDataList, total int, err error)
	GetFcList(ctx context.Context, req *structure.GetFpmInOrderItemFcListQuery) (list structure.GetFpmInOrderItemFcDataList, err error)
	swapListModel2Data(src model.FpmInOrder, dst *structure.GetFpmInOrderData, ctx context.Context)
	swapItemModel2Data(item model.FpmInOrderItem, itemGetData *structure.GetFpmInOrderItemData, ctx context.Context)
	swapFcModel2Data(fc model.FpmInOrderItemFc, fineCodeGetData *structure.GetFpmInOrderItemFcData, ctx context.Context)
	judgeAuditPass(id uint64, order model.FpmInOrder, ctx context.Context) (
		addItems structure.AddStockProductDetailParamList,
		updateItems structure.UpdateStockProductDetailParamList, salePlanOrderItemIds []uint64, err error)
	judgeAuditWait(id uint64, fpmInOrder model.FpmInOrder, ctx context.Context) (
		updateItems structure.UpdateStockProductDetailParamList,
		salePlanOrderItemIds []uint64,
		err error)
	GetFpmPrcInOrderDetailsWithFc(ctx context.Context, req *structure.GetFpmInOrderListQuery) (
		list structure.GetFpmInOrderItemDropdownDataList, total int, err error)
	UpdateReturnItem(ctx context.Context, req structure.ModifyFpmInOrder) (err error)
	GetIDsBySrcID(ctx context.Context, srcIDs []uint64) (ids []uint64, err error)
	// fpm_in_order_item_fc文件中获取成品进仓报表
	GetProductInReport(ctx context.Context, req *structure.GetProductInOrderReportQuery) (data structure.GetProductInOrderProductReportDataList, count int, err error)
	WashInOrderForImport(ctx context.Context, req *structure.GetFpmInOrderListQuery) (err error)
}

type FpmInOrderRepo struct {
	tx *mysql_base.Tx
}

func NewFpmInOrderRepo(tx *mysql_base.Tx) IFpmInOrderRepo {
	return &FpmInOrderRepo{tx: tx}
}

func (r *FpmInOrderRepo) Add(ctx context.Context, req *structure.AddFpmInOrderParam) (data structure.AddFpmInOrderData, err error) {

	// var (
	// rLock *redis.LockForRedis
	// )
	var (
		orderPrefix mysqlSystem.OrderPrefix
		dateFormat  string
		numLength   int
		exist       bool
	)

	fpmInOrder := model.NewFpmInOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	fpmInOrder.AuditStatus = common_system.OrderStatusPendingAudit
	fpmInOrder.BusinessClose = common_system.BusinessCloseNo
	fpmInOrder.DepartmentId = metadata.GetDepartmentId(ctx)
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
	} else {
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
	}
	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "product", fpmInOrder, req.OrderNoPre, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	fpmInOrder.OrderNo = orderNo
	fpmInOrder.Number = int(number)

	price, weight, roll, length := req.GetTotalPWR(req.InOrderType)
	fpmInOrder.TotalPrice = price
	fpmInOrder.TotalWeight = weight
	fpmInOrder.TotalRoll = roll
	fpmInOrder.TotalLength = length

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmInOrder.UnitId = item.UnitId
			break
		}
	}
	fpmInOrder.InOrderType = req.InOrderType
	fpmInOrder, err = mysql.MustCreateFpmInOrder(r.tx, fpmInOrder)
	if err != nil {
		return
	}

	// 3. 新增成品信息
	for _, item := range req.ItemData {
		fpmInOrderItem := model.NewFpmInOrderItem(ctx, &item)
		fpmInOrderItem.ParentId = fpmInOrder.Id
		fpmInOrderItem.ParentOrderNo = fpmInOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(req.InOrderType)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmInOrderItem.TotalWeight = tw
		fpmInOrderItem.TotalPrice = tp
		fpmInOrderItem.InLength = tl
		fpmInOrderItem.WeightError = weightError
		fpmInOrderItem.SettleErrorWeight = settleErrorWeight
		fpmInOrderItem.PaperTubeWeight = tpp
		fpmInOrderItem.SettleWeight = tsw
		fpmInOrderItem.ActuallyWeight = taw
		fpmInOrderItem.WarehouseInType = fpmInOrder.InOrderType
		if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeAdjust {
			// 调整的进仓单的库存汇总id需要重新获取
			fpmInOrderItem.SumStockId = 0
		}
		// 前端没有传入所属客户id，尝试取营销体系的默认客户id
		if fpmInOrderItem.CustomerId == 0 {
			saleSystem, _ := sale_system_pb.NewSaleSystemClient().GetSaleSystemDetailById(ctx, fpmInOrder.SaleSystemId)
			fpmInOrderItem.CustomerId = saleSystem.DefaultCustomerId
		}
		fpmInOrderItem, err = mysql.MustCreateFpmInOrderItem(r.tx, fpmInOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			if req.InOrderType != consts.WarehouseGoodInTypeProcessReturn && req.InOrderType != consts.WarehouseGoodInTypeAdjust {
				fineCode.SumStockId = item.SumStockId
				fineCode.ProductWidth = item.ProductWidth
				fineCode.ProductGramWeight = item.ProductGramWeight
				fineCode.FinishProductWidthUnitId = item.FinishProductWidthUnitId
				fineCode.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
				// fineCode.DyeFactoryColorCode = fpmInOrderItem.DyeFactoryColorCode
				// fineCode.DyeFactoryDyelotNumber = fpmInOrderItem.DyeFactoryDyelotNumber
			}
			if req.InOrderType == consts.WarehouseGoodInTypeAdjust {
				// 调整的进仓单的库存汇总id需要重新获取
				fineCode.SumStockId = 0
			}
			itemFc := model.NewFpmInOrderItemFc(ctx, &fineCode)
			itemFc.DyeFactoryColorCode = item.DyeFactoryColorCode
			itemFc.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
			itemFc.ParentId = fpmInOrderItem.Id
			itemFc.WarehouseInType = fpmInOrder.InOrderType
			itemFc.WarehouseInOrderId = fpmInOrder.Id
			itemFc.WarehouseInOrderNo = fpmInOrder.OrderNo
			itemFc.OrderTime = fpmInOrder.WarehouseInTime
			itemFc.WarehouseId = fpmInOrder.WarehouseId
			// 补充打码暂时使用，2024-1-11处理完去除
			// if req.InOrderType == consts.WarehouseGoodInTypeOther {
			//	itemFc.GenBarQrCode(ctx, item.ProductCode, item.ProductColorCode)
			// }
			itemFc, err = mysql.MustCreateFpmInOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmInOrder.Id
	return
}

func (r *FpmInOrderRepo) Update(ctx context.Context, req *structure.UpdateFpmInOrderParam) (data structure.UpdateFpmInOrderData, err error) {
	var (
		fpmInOrder    model.FpmInOrder
		itemModel     model.FpmInOrderItem
		findCodeModel model.FpmInOrderItemFc
		itemList      model.FpmInOrderItemList
	)
	fpmInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := fpmInOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，当前单据状态不能更新。"))
		return
	}

	fpmInOrder.UpdateFpmInOrder(ctx, req)

	price, weight, roll, length := req.GetTotalPWR(req.InOrderType)
	fpmInOrder.TotalPrice = price
	fpmInOrder.TotalWeight = weight
	fpmInOrder.TotalRoll = roll
	fpmInOrder.TotalLength = length

	for _, item := range req.ItemData {
		if item.UnitId != 0 {
			fpmInOrder.UnitId = item.UnitId
			break
		}
	}

	// 把单据状态从已驳回改回待审核
	if fpmInOrder.AuditStatus == common_system.OrderStatusRejected {
		fpmInOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	fpmInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmInOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.Id)
	itemIds := itemList.GetIds()
	fcList, _ := mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemIds)
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		if len(fcList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "parent_id")
		}
		if err != nil {
			return
		}
	}

	// 新增成品信息
	for _, item := range req.ItemData {
		fpmInOrderItem := model.NewFpmInOrderItem(ctx, &item)
		fpmInOrderItem.ParentId = fpmInOrder.Id
		fpmInOrderItem.ParentOrderNo = fpmInOrder.OrderNo
		tw, tp, tl, weightError, settleErrorWeight, _, tpp, tsw, taw := item.GetTotalWPL(req.InOrderType)
		// 总数量，总价，总长，码单空差，结算空差，总空差，总纸筒重，总结算数量，总码单数量
		fpmInOrderItem.TotalWeight = tw
		fpmInOrderItem.TotalPrice = tp
		fpmInOrderItem.InLength = tl
		fpmInOrderItem.WeightError = weightError
		fpmInOrderItem.SettleErrorWeight = settleErrorWeight
		fpmInOrderItem.PaperTubeWeight = tpp
		fpmInOrderItem.SettleWeight = tsw
		fpmInOrderItem.ActuallyWeight = taw
		fpmInOrderItem.WarehouseInType = fpmInOrder.InOrderType
		// 前端没有传入所属客户id，尝试取营销体系的默认客户id
		if fpmInOrderItem.CustomerId == 0 {
			saleSystem, _ := sale_system_pb.NewSaleSystemClient().GetSaleSystemDetailById(ctx, fpmInOrder.SaleSystemId)
			fpmInOrderItem.CustomerId = saleSystem.DefaultCustomerId
		}
		fpmInOrderItem, err = mysql.MustCreateFpmInOrderItem(r.tx, fpmInOrderItem)
		if err != nil {
			return
		}
		// 补充打码暂时使用，2024-1-11处理完去除，2024-2-11可删除
		// oldItem := itemList.Pick(item.Id)
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			// 补充打码暂时使用，2024-1-11处理完去除，2024-2-11可删除
			// oldFc := fcList.Pick(fineCode.Id)
			// if req.InOrderType != consts.WarehouseGoodInTypeProcessReturn {
			// 	fineCode.DyeFactoryColorCode = fpmInOrderItem.DyeFactoryColorCode
			// 	fineCode.DyeFactoryDyelotNumber = fpmInOrderItem.DyeFactoryDyelotNumber
			// }
			itemFc := model.NewFpmInOrderItemFc(ctx, &fineCode)
			itemFc.DyeFactoryColorCode = item.DyeFactoryColorCode
			itemFc.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
			itemFc.ParentId = fpmInOrderItem.Id
			itemFc.WarehouseInType = fpmInOrder.InOrderType
			itemFc.WarehouseInOrderId = fpmInOrder.Id
			itemFc.WarehouseInOrderNo = fpmInOrder.OrderNo
			itemFc.OrderTime = fpmInOrder.WarehouseInTime
			itemFc.WarehouseId = fpmInOrder.WarehouseId
			if req.InOrderType != consts.WarehouseGoodInTypeProcessReturn {
				itemFc.SumStockId = item.SumStockId
				itemFc.ProductWidth = item.ProductWidth
				itemFc.ProductGramWeight = item.ProductGramWeight
				itemFc.FinishProductWidthUnitId = item.FinishProductWidthUnitId
				itemFc.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId
			}
			// 补充打码暂时使用，2024-1-11处理完去除
			// if req.InOrderType == consts.WarehouseGoodInTypeOther {
			// 	itemFc.GenBarQrCodeV2(ctx, item.ProductCode, item.ProductColorCode, item.ProductId, item.ProductColorId, oldItem.ProductId, oldItem.ProductColorId, oldFc)
			// }
			itemFc, err = mysql.MustCreateFpmInOrderItemFc(r.tx, itemFc)
			if err != nil {
				return
			}
		}
	}

	data.Id = fpmInOrder.Id
	return
}

func (r *FpmInOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateFpmInOrderBusinessCloseParam) (data structure.UpdateFpmInOrderData, err error) {
	var (
		fpmInOrders model.FpmInOrderList
	)
	fpmInOrders, err = mysql.FindFpmInOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, fpmInOrder := range fpmInOrders {
		// 更新业务状态
		err = fpmInOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		fpmInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmInOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *FpmInOrderRepo) UpdateStatusPass(ctx context.Context, id uint64) (
	data structure.UpdateFpmInOrderStatusData,
	addItems structure.AddStockProductDetailParamList,
	updateItems structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	err error,
) {
	var (
		fpmInOrder model.FpmInOrder
		outOrder   model.FpmOutOrder
	)

	fpmInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 必选条件判断
	if fpmInOrder.WarehouseId == 0 {
		err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "请先选择仓库"))
		return
	}
	addItems, updateItems, salePlanOrderItemIds, err = r.judgeAuditPass(id, fpmInOrder, ctx)
	if err != nil {
		return
	}

	// 出仓单未审核，进仓单不可以审核
	if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeInternalAllocate {
		outOrder, err = mysql.MustFirstFpmOutOrderByID(r.tx, fpmInOrder.SrcId)
		if err != nil {
			return
		}
		if outOrder.AuditStatus != common_system.OrderStatusAudited {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeSrcOrderNoPassFalse, "操作失败，请先审核出仓单"))
			return
		}
	}

	// 审核
	err = fpmInOrder.Audit(ctx)
	if err != nil {
		return
	}
	fpmInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmInOrder)
	if err != nil {
		return
	}
	// 更新已经入仓库
	err = mysql.MustUpdateIsInWarehouseByOrderId(r.tx, fpmInOrder.Id, true)
	if err != nil {
		return
	}

	// 获取BizUnitId的主要往来单位类型
	if fpmInOrder.BizUnitId != 0 {
		var bizUnitData biz_pb.Res
		bizUnitData, err = biz_pb.NewClientBizUnitService().GetBizUnitDetailByID(ctx, biz_pb.Req{Id: fpmInOrder.BizUnitId})
		if err != nil {
			return
		}
		data.MainUnitTypeId = bizUnitData.MainUnitTypeId
		data.MainUnitTypeName = bizUnitData.MainUnitTypeName
		data.MainBizUnitType = common.BizUnitType(bizUnitData.MainBizUnitType)
	}

	data.WarehouseInTime = tools.MyTime(fpmInOrder.WarehouseInTime)
	data.OrderNo = fpmInOrder.OrderNo
	return
}

func (r *FpmInOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (
	data structure.UpdateFpmInOrderStatusData,
	updateItems structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		fpmInOrder model.FpmInOrder
	)

	fpmInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	updateItems, salePlanOrderItemIds, err = r.judgeAuditWait(id, fpmInOrder, ctx)
	if err != nil {
		return
	}

	// 消审
	err = fpmInOrder.Wait(ctx)
	if err != nil {
		return
	}
	fpmInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmInOrder)
	if err != nil {
		return
	}

	// 撤销已经入仓库
	err = mysql.MustUpdateIsInWarehouseByOrderId(r.tx, fpmInOrder.Id, false)

	// 获取BizUnitId的主要往来单位类型
	if fpmInOrder.BizUnitId != 0 {
		var bizUnitData biz_pb.Res
		bizUnitData, err = biz_pb.NewClientBizUnitService().GetBizUnitDetailByID(ctx, biz_pb.Req{Id: fpmInOrder.BizUnitId})
		if err != nil {
			return
		}

		data.MainUnitTypeId = bizUnitData.MainUnitTypeId
		data.MainUnitTypeName = bizUnitData.MainUnitTypeName
		data.MainBizUnitType = common.BizUnitType(bizUnitData.MainBizUnitType)
	}

	data.WarehouseInTime = tools.MyTime(fpmInOrder.WarehouseInTime)
	data.OrderNo = fpmInOrder.OrderNo
	return
}

func (r *FpmInOrderRepo) UpdateStatusReject(ctx context.Context, id uint64) (data structure.UpdateFpmInOrderStatusData, err error) {
	var (
		fpmInOrder model.FpmInOrder
	)

	fpmInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 拒绝/驳回
	err = fpmInOrder.Reject(ctx)
	if err != nil {
		return
	}
	fpmInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmInOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmInOrderRepo) UpdateStatusCancel(ctx context.Context, id uint64) (data structure.UpdateFpmInOrderStatusData, err error) {
	var (
		fpmInOrder model.FpmInOrder
	)

	fpmInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, id)
	if err != nil {
		return
	}

	// 作废
	err = fpmInOrder.Cancel(ctx)
	if err != nil {
		return
	}
	fpmInOrder, err = mysql.MustUpdateFpmInOrder(r.tx, fpmInOrder)
	if err != nil {
		return
	}

	return
}

func (r *FpmInOrderRepo) Exist(ctx context.Context, ids []uint64) (orderNos []string, exist bool, err error) {
	var (
		fpmInOrders model.FpmInOrderList
	)
	fpmInOrders, err = mysql.FindFpmInOrderByPurchaseOrderIDs(r.tx, ids)
	if err != nil {
		return
	}
	for _, fpmInOrder := range fpmInOrders {
		orderNos = append(orderNos, fpmInOrder.OrderNo)
	}
	if len(fpmInOrders) != 0 {
		exist = true
	}
	return
}

func (r *FpmInOrderRepo) Get(ctx context.Context, req *structure.GetFpmInOrderQuery) (data structure.GetFpmInOrderData, err error) {
	var (
		fpmInOrder             model.FpmInOrder
		itemDatas              model.FpmInOrderItemList
		fineCodeList           model.FpmInOrderItemFcList
		detailStockList        model.StockProductDetailList
		bizService             = biz_pb.NewClientBizUnitService()
		pLevelPB               = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB                 = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB            = warehouse_pb.NewPhysicalWarehouseClient()
		baseProductPB          = base_product_pb.NewProductColorClient()
		dicSvc                 = dictionary.NewDictionaryClient()
		productSvc             = base_product_pb.NewProductClient()
		purchaseProductItemMap = make(map[uint64]purchase_pb.PurchaseProductItemData)
	)
	fpmInOrder, err = mysql.MustFirstFpmInOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetFpmInOrderData{}
	r.swapListModel2Data(fpmInOrder, &o, ctx)

	itemDatas, err = mysql.FindFpmInOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}
	fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	var (
		bizIds     = set.NewUint64Set()
		levelIds   = set.NewUint64Set()
		mUnitIds   = set.NewUint64Set()
		colorIds   = set.NewUint64Set()
		quoteIds   = set.NewUint64Set()
		dicIds     = set.NewUint64Set()
		wbinIds    = set.NewUint64Set()
		stockIds   = set.NewUint64Set()
		productIds = set.NewUint64Set()
	)
	for _, itemData := range itemDatas {
		bizIds.Add(itemData.CustomerId)
		levelIds.Add(itemData.ProductLevelId)
		mUnitIds.Add(itemData.UnitId)
		mUnitIds.Add(itemData.AuxiliaryUnitId)
		colorIds.Add(itemData.ProductColorId)
		quoteIds.Add(itemData.QuoteOrderItemId)
		dicIds.Add(itemData.FinishProductWidthUnitId)
		dicIds.Add(itemData.FinishProductGramWeightUnitId)
		productIds.Add(itemData.ProductId)
	}

	for _, fineCode := range fineCodeList {
		mUnitIds.Add(fineCode.UnitId)
		dicIds.Add(fineCode.FinishProductWidthUnitId)
		dicIds.Add(fineCode.FinishProductGramWeightUnitId)
		wbinIds.Add(fineCode.WarehouseBinId)
		stockIds.Add(fineCode.StockId)
	}

	// 采购进仓单
	if fpmInOrder.InOrderType == consts.WarehouseGoodInTypePurchase {
		purchaseProductItemMap, err = purchase_pb.NewPurchaseProductOrderClient().GetItems(ctx, quoteIds.List())
		if err != nil {
			return
		}
	}

	// if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeInternalAllocate {
	detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds.List())
	if err != nil {
		return
	}
	// }
	for _, detailStock := range detailStockList {
		dicIds.Add(detailStock.FinishProductWidthUnitId)
		dicIds.Add(detailStock.FinishProductGramWeightUnitId)
	}

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, bizIds.List())
	levelNameMap, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds.List())
	unitNameMap, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, mUnitIds.List())
	ColorMap, _ := baseProductPB.GetProductColorItemByIds(ctx, colorIds.List())
	binNameMap, _ := warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds.List())
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds.List())

	processOutItemDataList, _ := mysql.FindFpmOutOrderItemByIDs(r.tx, quoteIds.List())
	allOutItemDataList, _ := mysql.FindFpmOutOrderItemByIDs(r.tx, quoteIds.List())
	productMap, _ := productSvc.GetProductMapByIds(ctx, productIds.List())

	var (
		countSaleWeightMap = make(map[uint64]int)
	)
	for _, _ordersItem := range itemDatas {
		if _ordersItem.AuxiliaryUnitId != 0 {
			if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
				countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
			} else {
				countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.InLength
			}
		} else {
			countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
		}
	}
	o.MergeWeightInfo = func() (str string) {
		for k, v := range countSaleWeightMap {
			fmtRound := tools.GetRound(v, 2)
			if str == "" {
				str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
			} else {
				str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
			}
		}
		return
	}()

	for _, item := range itemDatas {
		itemGetData := structure.GetFpmInOrderItemData{}
		itemGetData.Id = item.Id
		itemGetData.CreateTime = tools.MyTime(item.CreateTime)
		itemGetData.UpdateTime = tools.MyTime(item.UpdateTime)
		itemGetData.CreatorId = item.CreatorId
		itemGetData.CreatorName = item.CreatorName
		itemGetData.UpdaterId = item.UpdaterId
		itemGetData.UpdateUserName = item.UpdaterName
		itemGetData.ParentId = item.ParentId
		itemGetData.ParentOrderNo = item.ParentOrderNo
		itemGetData.QuoteOrderNo = item.QuoteOrderNo
		itemGetData.QuoteOrderId = item.QuoteOrderId
		itemGetData.QuoteOrderItemId = item.QuoteOrderItemId
		itemGetData.ProductId = item.ProductId
		if product, ok := productMap[item.ProductId]; ok {
			itemGetData.ProductCode = product.FinishProductCode
			itemGetData.ProductName = product.FinishProductName
			itemGetData.ProductCraft = product.FinishProductCraft
			itemGetData.ProductIngredient = product.FinishProductIngredient
		}
		itemGetData.CustomerId = item.CustomerId
		itemGetData.ProductColorId = item.ProductColorId
		itemGetData.ProductColorCode = ColorMap[item.ProductColorId][0]
		itemGetData.ProductColorName = ColorMap[item.ProductColorId][1]
		itemGetData.DyeFactoryColorCode = item.DyeFactoryColorCode
		itemGetData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		itemGetData.ProductLevelId = item.ProductLevelId
		itemGetData.ProductRemark = item.ProductRemark
		itemGetData.QuoteRoll = item.QuoteRoll
		itemGetData.QuoteTotalWeight = item.QuoteTotalWeight
		itemGetData.InRoll = item.InRoll
		itemGetData.TotalWeight = item.TotalWeight
		itemGetData.WeightError = item.WeightError
		itemGetData.PaperTubeWeight = item.PaperTubeWeight
		itemGetData.SettleWeight = item.SettleWeight
		itemGetData.SettleErrorWeight = item.SettleErrorWeight
		itemGetData.QuoteWeight = item.QuoteWeight
		itemGetData.UnitPrice = item.UnitPrice
		itemGetData.UnitId = item.UnitId
		itemGetData.AuxiliaryUnitId = item.AuxiliaryUnitId
		if item.AuxiliaryUnitId == 0 {
			itemGetData.AuxiliaryUnitId = item.UnitId
		}
		itemGetData.InLength = item.InLength
		itemGetData.QuoteLength = item.QuoteLength
		itemGetData.LengthUnitPrice = item.LengthUnitPrice
		itemGetData.OtherPrice = item.OtherPrice
		itemGetData.TotalPrice = item.TotalPrice
		itemGetData.Remark = item.Remark
		itemGetData.SumStockId = item.SumStockId
		itemGetData.ArrangeOrderItemId = item.ArrangeOrderItemId
		itemGetData.ReceiveWeight = item.ReceiveWeight
		itemGetData.PTWeightAndWeightError = item.PTWeightAndWeightError
		itemGetData.ShouldPayWeight = item.ShouldPayWeight

		itemGetData.BuildFPResp(item.ProductWidth, item.ProductGramWeight, dicNameMap[item.FinishProductWidthUnitId][1],
			dicNameMap[item.FinishProductGramWeightUnitId][1], item.FinishProductWidthUnitId, item.FinishProductGramWeightUnitId)

		// 转义
		itemGetData.UnitName = unitNameMap[item.UnitId]
		itemGetData.AuxiliaryUnitName = unitNameMap[item.AuxiliaryUnitId]
		if val, ok := customerMap[item.CustomerId]; ok {
			itemGetData.CustomerName = val
		}
		itemGetData.ProductLevelName = levelNameMap[item.ProductLevelId]
		// 加工出仓信息
		processOutItemData := processOutItemDataList.Pick(item.QuoteOrderItemId)
		itemGetData.OutRoll = processOutItemData.OutRoll
		itemGetData.OutLength = processOutItemData.OutLength
		itemGetData.OutWeight = processOutItemData.TotalWeight
		// 调拨出仓信息
		allOutItemData := allOutItemDataList.Pick(item.QuoteOrderItemId)
		itemGetData.AllocateRoll = allOutItemData.OutRoll
		itemGetData.AllocateWeight = allOutItemData.TotalWeight
		itemGetData.AllocateLength = allOutItemData.OutLength

		// 销售计划单详情
		itemGetData.SalePlanOrderItemId = item.SalePlanOrderItemId
		itemGetData.SalePlanOrderItemNo = item.SalePlanOrderItemNo
		// r.swapItemModel2Data(item, &itemGetData, ctx)
		if fpmInOrder.InOrderType == consts.WarehouseGoodInTypePurchase {
			itemGetData.TotalWaitCollectWeight = purchaseProductItemMap[item.QuoteOrderItemId].TotalWaitCollectWeight
		}

		// 添加细码信息
		fcList := fineCodeList.PickFcListByParentId(item.Id)
		var itemFCData = make(structure.GetFpmInOrderItemFcDataList, 0)
		for _, fc := range fcList {
			fineCodeGetData := structure.GetFpmInOrderItemFcData{}
			// fc :=
			// r.swapFcModel2Data(fc, &fineCodeGetData, ctx)
			fineCodeGetData.Id = fc.Id
			fineCodeGetData.SrcId = fc.SrcId
			fineCodeGetData.CreateTime = tools.MyTime(fc.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fc.UpdateTime)
			fineCodeGetData.CreatorId = fc.CreatorId
			fineCodeGetData.CreatorName = fc.CreatorName
			fineCodeGetData.UpdaterId = fc.UpdaterId
			fineCodeGetData.UpdateUserName = fc.UpdaterName
			fineCodeGetData.ParentId = fc.ParentId
			fineCodeGetData.WarehouseInType = fc.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fc.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fc.WarehouseInOrderNo
			fineCodeGetData.Roll = fc.Roll
			fineCodeGetData.WarehouseBinId = fc.WarehouseBinId
			fineCodeGetData.VolumeNumber = fc.VolumeNumber
			fineCodeGetData.BaseUnitWeight = fc.BaseUnitWeight
			fineCodeGetData.WeightError = fc.WeightError
			fineCodeGetData.UnitId = fc.UnitId
			fineCodeGetData.StockId = fc.StockId
			fineCodeGetData.SumStockId = fc.SumStockId
			fineCodeGetData.PaperTubeWeight = fc.PaperTubeWeight
			fineCodeGetData.Length = fc.Length
			fineCodeGetData.Remark = fc.Remark
			fineCodeGetData.InternalRemark = fc.InternalRemark
			fineCodeGetData.DigitalCode = fc.DigitalCode
			fineCodeGetData.ShelfNo = fc.ShelfNo
			fineCodeGetData.AccountNum = fc.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fc.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
			fineCodeGetData.DyelotNumber = fc.DyeFactoryDyelotNumber
			// 增加旧单判断，致盛旧单问题，如果重新更新才能去掉
			// if fc.DyeFactoryDyelotNumber != item.DyeFactoryDyelotNumber {
			// 	fineCodeGetData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
			// 	fineCodeGetData.DyelotNumber = item.DyeFactoryDyelotNumber
			// }
			if fc.DyeFactoryColorCode != item.DyeFactoryColorCode {
				fineCodeGetData.DyeFactoryColorCode = item.DyeFactoryColorCode
			}
			if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeProcessReturn {
				fineCodeGetData.DyeFactoryColorCode = fc.DyeFactoryColorCode
				fineCodeGetData.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
				fineCodeGetData.DyelotNumber = fc.DyeFactoryDyelotNumber
			}
			// fineCodeGetData.ProductWidth = fc.ProductWidth
			// fineCodeGetData.ProductGramWeight = fc.ProductGramWeight
			fineCodeGetData.ContractNumber = fc.ContractNumber
			fineCodeGetData.ScanUserId = fc.ScanUserId
			fineCodeGetData.ScanUserName = fc.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fc.ScanTime)
			fineCodeGetData.StockRemark = fc.StockRemark
			fineCodeGetData.ActuallyWeight = fc.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fc.SettleErrorWeight
			fineCodeGetData.SettleWeight = fc.SettleWeight
			itemGetData.SumStockId = fc.SumStockId
			fineCodeGetData.WarehouseInTypeName = fc.WarehouseInType.String()
			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fc.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fc.UnitId]
			fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
			detailStock := detailStockList.Pick(fc.StockId)
			fineCodeGetData.BarCode = detailStock.BarCode
			fineCodeGetData.QrCode = tools.String2Utf8(detailStock.QrCode)
			fineCodeGetData.DyelotNumber = detailStock.DyelotNumber
			fineCodeGetData.PrintDate = time.Now().Format("2006-01-02")
			if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeInternalAllocate {
				fineCodeGetData.BuildFPResp(detailStock.FinishProductWidth, detailStock.FinishProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
					dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)
			} else {
				fineCodeGetData.BuildFPResp(fc.ProductWidth, fc.ProductGramWeight, dicNameMap[fc.FinishProductWidthUnitId][1],
					dicNameMap[fc.FinishProductGramWeightUnitId][1], fc.FinishProductWidthUnitId, fc.FinishProductGramWeightUnitId)
			}
			if product, ok := productMap[itemGetData.ProductId]; ok {
				fineCodeGetData.ProductKindId = product.TypeGreyFabricId
				fineCodeGetData.ProductKindName = product.TypeGreyFabricName
				fineCodeGetData.FinishProductCraft = product.FinishProductCraft
				fineCodeGetData.FinishProductIngredient = product.FinishProductIngredient
				fineCodeGetData.WeavingOrganizationId = product.WeavingOrganizationId
				fineCodeGetData.WeavingOrganizationCode = product.WeavingOrganizationCode
				fineCodeGetData.WeavingOrganizationName = product.WeavingOrganizationName
				fineCodeGetData.Density = product.Density
				fineCodeGetData.YarnCount = product.YarnCount
				fineCodeGetData.BleachId = product.BleachId
				fineCodeGetData.BleachName = product.BleachName
			}
			// 补充打码暂时使用，2024-1-11处理完去除，2024-2-11可删除
			// fineCodeGetData.BarCode = fc.BarCode
			// fineCodeGetData.QrCode = tools.String2Utf8(fc.QrCode)
			itemFCData = append(itemFCData, fineCodeGetData)
		}
		itemGetData.ItemFCData = itemFCData
		o.ItemData = append(o.ItemData, itemGetData)
	}

	data = o
	return
}

func (r *FpmInOrderRepo) GetList(ctx context.Context, req *structure.GetFpmInOrderListQuery) (list structure.GetFpmInOrderDataList, total int, err error) {
	var (
		orders      model.FpmInOrderList
		ordersItems model.FpmInOrderItemList
		bizPB       = biz_pb.NewClientBizUnitService()
		salePB      = sale_system_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)

	if req.ProductCodeOrName != "" || req.ProductColorCodeOrName != "" || req.DyelotNumber != "" {
		var orderItems model.FpmInOrderItemList
		orderItems, total, err = mysql.SearchFpmInOrderDetail(r.tx, req)
		if err != nil {
			return
		}
		req.OrderIds = orderItems.GetOrderIDs()
		if err != nil {
			return
		}
	}

	orders, total, err = mysql.SearchFpmInOrder(r.tx, req)
	if err != nil {
		return
	}

	ordersItems, err = mysql.FindFpmInOrderItemByParenTIDs(r.tx, orders.GetIds())
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64ListV2("unit_id", orders, ordersItems)
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = salePB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		warehouseOutIds := mysql_base.GetUInt64List(orders, "out_warehouse_id")
		wareIds := tools.MergeSlicesUint64(warehouseIds, warehouseOutIds)
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, wareIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		var (
			countSaleWeightMap = make(map[uint64]int)
		)
		_ordersItems := ordersItems.PickByParentId(src.Id)
		for _, _ordersItem := range _ordersItems {
			if _ordersItem.AuxiliaryUnitId != 0 {
				if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
				} else {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.InLength
				}
			} else {
				countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
			}
		}
		dst := structure.GetFpmInOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.InOrderType = src.InOrderType
		dst.SaleSystemId = src.SaleSystemId
		dst.BizUnitId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.Remark = src.Remark
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.UnitId = src.UnitId
		dst.TotalLength = src.TotalLength
		dst.TotalPrice = src.TotalPrice
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.SrcId = src.SrcId
		dst.SrcOrderNo = src.SrcOrderNo
		dst.OutWarehouseId = src.OutWarehouseId
		dst.VoucherNumber = src.VoucherNumber
		dst.TextureUrl = src.TextureUrl
		dst.SaleMode = src.SaleMode

		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.InOrderTypeName = src.InOrderType.String()
		dst.UnitName = unitNameMap[src.UnitId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.BizUnitName = bizNameMap[src.BizUnitId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.OutWarehouseName = wareNameMap[src.OutWarehouseId]
		dst.SaleModeName = src.SaleMode.String()
		dst.MergeWeightInfo = func() (str string) {
			for k, v := range countSaleWeightMap {
				fmtRound := tools.GetRound(v, 2)
				if str == "" {
					str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				} else {
					str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				}
			}
			return
		}()
		if req.WithItems {
			var (
				bizService    = biz_pb.NewClientBizUnitService()
				baseProductPB = base_product_pb.NewProductColorClient()
				// pLevelPB      = base_info_pb.NewInfoBaseFinishedProductLevelClient()
				// dicSvc        = dictionary.NewDictionaryClient()
				productSvc  = base_product_pb.NewProductClient()
				bizIds      = set.NewUint64Set()
				levelIds    = set.NewUint64Set()
				mUnitIds    = set.NewUint64Set()
				colorIds    = set.NewUint64Set()
				quoteIds    = set.NewUint64Set()
				dicIds      = set.NewUint64Set()
				productIds  = set.NewUint64Set()
				funcList    []func(ctx context.Context) error
				customerMap = make(map[uint64]string, 0)
				// levelNameMap  = make(map[uint64]string, 0)
				ColorMap = make(map[uint64][2]string, 0)
				// dicNameMap    = make(map[uint64][2]string, 0)
				// processOutItemDataList model.FpmOutOrderItemList
				// allOutItemDataList     model.FpmOutOrderItemList
				productMap = make(map[uint64]*base_product_pb.ProductRes, 0)
			)
			for _, itemData := range ordersItems {
				bizIds.Add(itemData.CustomerId)
				levelIds.Add(itemData.ProductLevelId)
				mUnitIds.Add(itemData.UnitId)
				mUnitIds.Add(itemData.AuxiliaryUnitId)
				colorIds.Add(itemData.ProductColorId)
				quoteIds.Add(itemData.QuoteOrderItemId)
				dicIds.Add(itemData.FinishProductWidthUnitId)
				dicIds.Add(itemData.FinishProductGramWeightUnitId)
				productIds.Add(itemData.ProductId)
			}

			funcList = append(funcList, func(ctx context.Context) error {
				var err1 error
				customerMap, err1 = bizService.GetBizUnitNameByIds(ctx, bizIds.List())
				if err1 != nil {
					middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err "+err1.Error()))
				}
				return nil
			})

			// funcList = append(funcList, func(ctx context.Context) error {
			// 	var err1 error
			// 	levelNameMap, err1 = pLevelPB.GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds.List())
			// 	if err1 != nil {
			// 		middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, " err "+err1.Error()))
			// 	}
			// 	return nil
			// })

			funcList = append(funcList, func(ctx context.Context) error {
				var err1 error
				ColorMap, err1 = baseProductPB.GetProductColorItemByIds(ctx, colorIds.List())
				if err1 != nil {
					middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, " err "+err1.Error()))
				}
				return nil
			})
			// funcList = append(funcList, func(ctx context.Context) error {
			// 	var err1 error
			// 	dicNameMap, err1 = dicSvc.GetDictionaryNameByIds(ctx, dicIds.List())
			// 	if err1 != nil {
			// 		middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, " err "+err1.Error()))
			// 	}
			// 	return nil
			// })
			// funcList = append(funcList, func(ctx context.Context) error {
			// 	var err1 error
			// 	processOutItemDataList, err1 = mysql.FindFpmOutOrderItemByIDs(r.tx, quoteIds.List())
			// 	if err1 != nil {
			// 		middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, " err "+err1.Error()))
			// 	}
			// 	return nil
			// })
			// funcList = append(funcList, func(ctx context.Context) error {
			// 	var err1 error
			// 	allOutItemDataList, err1 = mysql.FindFpmOutOrderItemByIDs(r.tx, quoteIds.List())
			// 	if err1 != nil {
			// 		middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, " err "+err1.Error()))
			// 	}
			// 	return nil
			// })
			funcList = append(funcList, func(ctx context.Context) error {
				var err1 error
				productMap, err1 = productSvc.GetProductMapByIds(ctx, productIds.List())
				if err1 != nil {
					middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductMapByIds err "+err1.Error()))
				}
				return nil
			})
			errgroup.Finish(ctx, 0, funcList...)
			for _, item := range ordersItems.PickByParentId(src.Id) {
				itemGetData := structure.GetFpmInOrderItemData{}
				itemGetData.Id = item.Id
				itemGetData.CreateTime = tools.MyTime(item.CreateTime)
				itemGetData.UpdateTime = tools.MyTime(item.UpdateTime)
				itemGetData.CreatorId = item.CreatorId
				itemGetData.CreatorName = item.CreatorName
				itemGetData.UpdaterId = item.UpdaterId
				itemGetData.UpdateUserName = item.UpdaterName
				itemGetData.ParentId = item.ParentId
				itemGetData.ParentOrderNo = item.ParentOrderNo
				itemGetData.QuoteOrderNo = item.QuoteOrderNo
				itemGetData.QuoteOrderId = item.QuoteOrderId
				itemGetData.QuoteOrderItemId = item.QuoteOrderItemId
				itemGetData.ProductId = item.ProductId
				if product, ok := productMap[item.ProductId]; ok {
					itemGetData.ProductCode = product.FinishProductCode
					itemGetData.ProductName = product.FinishProductName
					itemGetData.ProductCraft = product.FinishProductCraft
					itemGetData.ProductIngredient = product.FinishProductIngredient
				}
				itemGetData.CustomerId = item.CustomerId
				itemGetData.ProductColorId = item.ProductColorId
				itemGetData.ProductColorCode = ColorMap[item.ProductColorId][0]
				itemGetData.ProductColorName = ColorMap[item.ProductColorId][1]
				itemGetData.DyeFactoryColorCode = item.DyeFactoryColorCode
				itemGetData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
				itemGetData.ProductLevelId = item.ProductLevelId
				itemGetData.ProductRemark = item.ProductRemark
				itemGetData.QuoteRoll = item.QuoteRoll
				itemGetData.QuoteTotalWeight = item.QuoteTotalWeight
				itemGetData.InRoll = item.InRoll
				itemGetData.TotalWeight = item.InWeight
				itemGetData.WeightError = item.WeightError
				itemGetData.PaperTubeWeight = item.PaperTubeWeight
				itemGetData.SettleWeight = item.SettleWeight
				itemGetData.SettleErrorWeight = item.SettleErrorWeight
				itemGetData.QuoteWeight = item.QuoteWeight
				itemGetData.UnitPrice = item.UnitPrice
				itemGetData.UnitId = item.UnitId
				itemGetData.AuxiliaryUnitId = item.AuxiliaryUnitId
				if item.AuxiliaryUnitId == 0 {
					itemGetData.AuxiliaryUnitId = item.UnitId
				}
				itemGetData.InLength = item.InLength
				itemGetData.QuoteLength = item.QuoteLength
				itemGetData.LengthUnitPrice = item.LengthUnitPrice
				itemGetData.OtherPrice = item.OtherPrice
				itemGetData.TotalPrice = item.TotalPrice
				itemGetData.Remark = item.Remark
				itemGetData.SumStockId = item.SumStockId
				itemGetData.ArrangeOrderItemId = item.ArrangeOrderItemId
				itemGetData.ReceiveWeight = item.ReceiveWeight
				itemGetData.PTWeightAndWeightError = item.PTWeightAndWeightError
				itemGetData.ShouldPayWeight = item.ShouldPayWeight

				// itemGetData.BuildFPResp(item.ProductWidth, item.ProductGramWeight, dicNameMap[item.FinishProductWidthUnitId][1],
				// 	dicNameMap[item.FinishProductGramWeightUnitId][1], item.FinishProductWidthUnitId, item.FinishProductGramWeightUnitId)

				// 转义
				itemGetData.UnitName = unitNameMap[item.UnitId]
				itemGetData.AuxiliaryUnitName = unitNameMap[item.AuxiliaryUnitId]
				if val, ok := customerMap[item.CustomerId]; ok {
					itemGetData.CustomerName = val
				}
				// itemGetData.ProductLevelName = levelNameMap[item.ProductLevelId]
				// 加工出仓信息
				// processOutItemData := processOutItemDataList.Pick(item.QuoteOrderItemId)
				// itemGetData.OutRoll = processOutItemData.OutRoll
				// itemGetData.OutLength = processOutItemData.OutLength
				// itemGetData.OutWeight = processOutItemData.TotalWeight
				// 调拨出仓信息
				// allOutItemData := allOutItemDataList.Pick(item.QuoteOrderItemId)
				// itemGetData.AllocateRoll = allOutItemData.OutRoll
				// itemGetData.AllocateWeight = allOutItemData.TotalWeight
				// itemGetData.AllocateLength = allOutItemData.OutLength

				// 销售计划单详情
				itemGetData.SalePlanOrderItemId = item.SalePlanOrderItemId
				itemGetData.SalePlanOrderItemNo = item.SalePlanOrderItemNo
				// r.swapItemModel2Data(item, &itemGetData, ctx)
				// 采购进仓单
				// fpmInOrder := orders.Pick(item.ParentId)
				// if fpmInOrder.InOrderType == consts.WarehouseGoodInTypePurchase {
				// 	purchaseProductItemMap := make(map[uint64]purchase_pb.PurchaseProductItemData)
				// 	purchaseProductItemMap, err = purchase_pb.NewPurchaseProductOrderClient().GetItems(ctx, quoteIds.List())
				// 	if err != nil {
				// 		return
				// 	}
				// 	itemGetData.TotalWaitCollectWeight = purchaseProductItemMap[item.QuoteOrderItemId].TotalWaitCollectWeight
				// }
				dst.ItemData = append(dst.ItemData, itemGetData)
			}
		}
		list = append(list, dst)
	}
	return
}

func (r *FpmInOrderRepo) GetMPList(ctx context.Context, req *structure.GetMPFpmInOrderListQuery) (list structure.GetFpmInOrderDataList, total int, err error) {
	var (
		orders      model.FpmInOrderList
		ordersItems model.FpmInOrderItemList
		bizPB       = biz_pb.NewClientBizUnitService()
		salePB      = sale_system_pb.NewSaleSystemClient()
		emplPB      = empl_pb.NewClientEmployeeService()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
	)

	if req.OrderNo != "" {
		req.BizUnitIds, err = bizPB.GetBizUnitIdsByNameLike(ctx, req.OrderNo)
		if err != nil {
			return
		}
	}

	orders, total, err = mysql.SearchMPFpmInOrder(r.tx, req)
	if err != nil {
		return
	}

	ordersItems, err = mysql.FindFpmInOrderItemByParenTIDs(r.tx, orders.GetIds())
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64ListV2("measurement_unit_id", orders, ordersItems)
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = salePB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		warehouseOutIds := mysql_base.GetUInt64List(orders, "out_warehouse_id")
		wareIds := tools.MergeSlicesUint64(warehouseIds, warehouseOutIds)
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, wareIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, src := range orders.List() {
		var (
			countSaleWeightMap = make(map[uint64]int)
		)
		_ordersItems := ordersItems.PickByParentId(src.Id)
		for _, _ordersItem := range _ordersItems {
			if _ordersItem.AuxiliaryUnitId != 0 {
				if _ordersItem.AuxiliaryUnitId == _ordersItem.UnitId {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.SettleWeight
				} else {
					countSaleWeightMap[_ordersItem.AuxiliaryUnitId] += _ordersItem.InLength
				}
			} else {
				countSaleWeightMap[_ordersItem.UnitId] += _ordersItem.SettleWeight
			}
		}
		dst := structure.GetFpmInOrderData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.InOrderType = src.InOrderType
		dst.SaleSystemId = src.SaleSystemId
		dst.BizUnitId = src.BizUnitId
		dst.WarehouseId = src.WarehouseId
		dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
		dst.StoreKeeperId = src.StoreKeeperId
		dst.Remark = src.Remark
		dst.BusinessClose = src.BusinessClose
		dst.BusinessCloseUserId = src.BusinessCloseUserId
		dst.BusinessCloseUserName = src.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
		dst.DepartmentId = src.DepartmentId
		dst.OrderNo = src.OrderNo
		dst.AuditStatus = src.AuditStatus
		dst.AuditorId = src.AuditorId
		dst.AuditorName = src.AuditorName
		dst.AuditTime = tools.MyTime(src.AuditDate)
		dst.UnitId = src.UnitId
		dst.TotalLength = src.TotalLength
		dst.TotalPrice = src.TotalPrice
		dst.TotalRoll = src.TotalRoll
		dst.TotalWeight = src.TotalWeight
		dst.SrcId = src.SrcId
		dst.SrcOrderNo = src.SrcOrderNo
		dst.OutWarehouseId = src.OutWarehouseId
		dst.SaleMode = src.SaleMode
		// 转义
		dst.AuditStatusName = src.AuditStatus.String()
		dst.BusinessCloseName = src.BusinessClose.String()
		dst.WarehouseName = wareNameMap[src.WarehouseId]
		dst.InOrderTypeName = src.InOrderType.String()
		dst.UnitName = unitNameMap[src.UnitId]
		dst.SaleSystemName = saleSysNameMap[src.SaleSystemId]
		dst.BizUnitName = bizNameMap[src.BizUnitId]
		dst.StoreKeeperName = empNameMap[src.StoreKeeperId]
		dst.OutWarehouseName = wareNameMap[src.OutWarehouseId]
		dst.MergeWeightInfo = func() (str string) {
			for k, v := range countSaleWeightMap {
				fmtRound := tools.GetRound(v, 2)
				if str == "" {
					str = strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				} else {
					str += ", " + strconv.FormatFloat(tools.DecimalDiv(float64(v), vars.Weight), 'f', fmtRound, 64) + "" + unitNameMap[k]
				}
			}
			return
		}()
		list = append(list, dst)
	}
	return
}

func (r *FpmInOrderRepo) GetListAndDetail(ctx context.Context, req *structure.GetFpmInOrderListQuery) (
	list structure.GetFpmInOrderDataList, total int, err error) {
	var (
		orderItems     model.FpmInOrderItemList
		orders         model.FpmInOrderList
		bizPB          = biz_pb.NewClientBizUnitService()
		salePB         = sale_system_pb.NewSaleSystemClient()
		emplPB         = empl_pb.NewClientEmployeeService()
		dictionaryPB   = dictionary.NewDictionaryClient()
		unitPB         = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB    = warehouse_pb.NewPhysicalWarehouseClient()
		productPB      = base_product_pb.NewProductClient()
		productColorPB = base_product_pb.NewProductColorClient()
		pLevelPB       = base_info_pb.NewInfoBaseFinishedProductLevelClient()
	)

	if req.ProductCodeOrName != "" {
		req.ProductIds, err = productPB.GetProductIds(ctx, "", req.ProductCodeOrName, "")
		if err != nil {
			return
		}
	}
	if req.ProductColorCodeOrName != "" {
		req.ProductColorIds, err = productColorPB.GetProductColorIds(ctx, req.ProductColorCodeOrName, req.ProductColorCodeOrName, "")
		if err != nil {
			return
		}
	}

	orderItems, total, err = mysql.SearchFpmInOrderDetail(r.tx, req)
	if err != nil {
		return
	}

	orders, err = mysql.FindFpmInOrderByIDs(r.tx, orderItems.GetOrderIDs())
	if err != nil {
		return
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap    map[uint64]string
		saleSysNameMap map[uint64]string
		wareNameMap    map[uint64]string
		bizNameMap     map[uint64]string
		empNameMap     map[uint64]string
		dicNameMap     map[uint64][2]string
		levelNameMap   map[uint64]string
		colorMap       map[uint64][2]string
		productMap     map[uint64]*base_product_pb.ProductRes
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orders, "unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		saleSysIds := mysql_base.GetUInt64List(orders, "sale_system_id")
		saleSysNameMap, err1 = salePB.GetSaleSystemByIds(ctx, saleSysIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		warehouseIds := mysql_base.GetUInt64List(orders, "warehouse_id")
		warehouseOutIds := mysql_base.GetUInt64List(orders, "out_warehouse_id")
		wareIds := tools.MergeSlicesUint64(warehouseIds, warehouseOutIds)
		wareNameMap, err1 = warehousePB.GetPhysicalWarehouseByIds(ctx, wareIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds1 := mysql_base.GetUInt64List(orders, "biz_unit_id")
		bizUnitIds2 := mysql_base.GetUInt64List(orderItems, "biz_unit_id")
		bizUnitIds := append(bizUnitIds1, bizUnitIds2...)
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		storeKeeperIds := mysql_base.GetUInt64List(orders, "store_keeper_id")
		empNameMap, err1 = emplPB.GetEmployeeNameByIds(ctx, storeKeeperIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetEmployeeNameByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		dicIds := mysql_base.GetUInt64ListV2("dictionary_detail_id", orderItems)
		dicNameMap, err1 = dictionaryPB.GetDictionaryNameByIds(ctx, dicIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetDictionaryNameByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		productIds := mysql_base.GetUInt64List(orderItems, "product_id")
		productMap, err1 = productPB.GetProductMapByIds(ctx, productIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductMapByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		productColorIds := mysql_base.GetUInt64List(orderItems, "product_color_id")
		colorMap, err1 = productColorPB.GetProductColorItemByIds(ctx, productColorIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductColorItemByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		levelIds := mysql_base.GetUInt64List(orderItems, "product_level_id")
		levelNameMap, err1 = pLevelPB.GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetInfoBaseFinishedProductLevelNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, orderItem := range orderItems.List() {
		order := orders.Pick(orderItem.ParentId)
		dst := structure.GetFpmInOrderData{}
		dst.Id = orderItem.Id
		dst.CreateTime = tools.MyTime(orderItem.CreateTime)
		dst.UpdateTime = tools.MyTime(orderItem.UpdateTime)
		dst.CreatorId = orderItem.CreatorId
		dst.CreatorName = orderItem.CreatorName
		dst.UpdaterId = orderItem.UpdaterId
		dst.UpdateUserName = orderItem.UpdaterName
		dst.InOrderType = order.InOrderType
		dst.SaleSystemId = order.SaleSystemId
		dst.BizUnitId = order.BizUnitId
		dst.WarehouseId = order.WarehouseId
		dst.WarehouseInTime = tools.MyTime(order.WarehouseInTime)
		dst.StoreKeeperId = order.StoreKeeperId
		dst.BusinessClose = order.BusinessClose
		dst.BusinessCloseUserId = order.BusinessCloseUserId
		dst.BusinessCloseUserName = order.BusinessCloseUserName
		dst.BusinessCloseTime = tools.MyTime(order.BusinessCloseTime)
		dst.DepartmentId = order.DepartmentId
		dst.OrderNo = order.OrderNo
		dst.AuditStatus = order.AuditStatus
		dst.AuditorId = order.AuditorId
		dst.AuditorName = order.AuditorName
		dst.AuditTime = tools.MyTime(order.AuditDate)
		dst.SrcId = order.SrcId
		dst.SrcOrderNo = order.SrcOrderNo
		dst.OutWarehouseId = order.OutWarehouseId
		// dst.VoucherNumber = order.VoucherNumber
		// 转义
		dst.AuditStatusName = order.AuditStatus.String()
		dst.BusinessCloseName = order.BusinessClose.String()
		dst.WarehouseName = wareNameMap[order.WarehouseId]
		dst.InOrderTypeName = order.InOrderType.String()
		dst.UnitName = unitNameMap[order.UnitId]
		dst.SaleSystemName = saleSysNameMap[order.SaleSystemId]
		dst.BizUnitName = bizNameMap[order.BizUnitId]
		dst.StoreKeeperName = empNameMap[order.StoreKeeperId]
		dst.OutWarehouseName = wareNameMap[order.OutWarehouseId]

		dst.ParentOrderNo = orderItem.ParentOrderNo
		dst.QuoteOrderNo = orderItem.QuoteOrderNo
		if product, ok := productMap[orderItem.ProductId]; ok {
			dst.ProductCode = product.FinishProductCode
			dst.ProductName = product.FinishProductName
			dst.ProductCraft = product.FinishProductCraft
			dst.ProductIngredient = product.FinishProductIngredient
		}
		dst.ProductColorCode = colorMap[orderItem.ProductColorId][0]
		dst.ProductColorName = colorMap[orderItem.ProductColorId][1]
		dst.DyeFactoryColorCode = orderItem.DyeFactoryColorCode
		dst.DyeFactoryDyelotNumber = orderItem.DyeFactoryDyelotNumber
		dst.ProductRemark = orderItem.ProductRemark
		dst.QuoteRoll = orderItem.QuoteRoll
		dst.QuoteTotalWeight = orderItem.QuoteTotalWeight
		dst.InRoll = orderItem.InRoll
		dst.WeightError = orderItem.WeightError
		dst.PaperTubeWeight = orderItem.PaperTubeWeight
		dst.SettleWeight = orderItem.SettleWeight
		dst.QuoteWeight = orderItem.QuoteWeight
		dst.UnitPrice = orderItem.UnitPrice
		dst.UnitId = orderItem.UnitId
		dst.AuxiliaryUnitId = orderItem.AuxiliaryUnitId
		if orderItem.AuxiliaryUnitId == 0 {
			dst.AuxiliaryUnitId = orderItem.UnitId
		}
		dst.InLength = orderItem.InLength
		dst.QuoteLength = orderItem.QuoteLength
		dst.LengthUnitPrice = orderItem.LengthUnitPrice
		dst.OtherPrice = orderItem.OtherPrice
		dst.Remark = orderItem.Remark

		dst.TotalLength = orderItem.InLength
		dst.TotalRoll = orderItem.InRoll
		dst.TotalWeight = orderItem.TotalWeight
		dst.TotalPrice = orderItem.TotalPrice
		dst.InLength = orderItem.InLength
		dst.WeightError = orderItem.WeightError
		dst.PaperTubeWeight = orderItem.PaperTubeWeight
		dst.SettleWeight = orderItem.SettleWeight

		dst.BuildFPResp(orderItem.ProductWidth, orderItem.ProductGramWeight, dicNameMap[orderItem.FinishProductWidthUnitId][1],
			dicNameMap[orderItem.FinishProductGramWeightUnitId][1], orderItem.FinishProductWidthUnitId, orderItem.FinishProductGramWeightUnitId)

		dst.UnitName = unitNameMap[orderItem.UnitId]
		dst.AuxiliaryUnitName = unitNameMap[orderItem.AuxiliaryUnitId]
		if val, ok := bizNameMap[orderItem.CustomerId]; ok {
			dst.CustomerName = val
		}
		dst.ProductLevelName = levelNameMap[orderItem.ProductLevelId]

		dst.SalePlanOrderItemId = orderItem.SalePlanOrderItemId
		dst.SalePlanOrderItemNo = orderItem.SalePlanOrderItemNo
		dst.QuoteOrderItemId = orderItem.QuoteOrderItemId

		// dst.ParentId = orderItem.ParentId
		// dst.QuoteOrderItemId = orderItem.QuoteOrderItemId
		// dst.CustomerId = orderItem.CustomerId
		// dst.ProductColorId = orderItem.ProductColorId
		// dst.ProductLevelId = orderItem.ProductLevelId
		// dst.ProductId = orderItem.ProductId
		// dst.SumStockId = orderItem.SumStockId
		list = append(list, dst)
	}
	return
}

func (r *FpmInOrderRepo) GetFcList(ctx context.Context, req *structure.GetFpmInOrderItemFcListQuery) (list structure.GetFpmInOrderItemFcDataList, err error) {
	var (
		orderItemFcs model.FpmInOrderItemFcList
	)

	orderItemFcs, err = mysql.FindFpmInOrderItemFcBySrcID(r.tx, req.SrcIds)
	if err != nil {
		return
	}

	for _, orderItemFc := range orderItemFcs.List() {
		dst := structure.GetFpmInOrderItemFcData{}
		dst.Id = orderItemFc.Id
		dst.CreateTime = tools.MyTime(orderItemFc.CreateTime)
		dst.UpdateTime = tools.MyTime(orderItemFc.UpdateTime)
		dst.CreatorId = orderItemFc.CreatorId
		dst.CreatorName = orderItemFc.CreatorName
		dst.UpdaterId = orderItemFc.UpdaterId
		dst.UpdateUserName = orderItemFc.UpdaterName
		dst.WarehouseId = orderItemFc.WarehouseId
		dst.ParentId = orderItemFc.ParentId
		dst.WarehouseInType = orderItemFc.WarehouseInType
		dst.WarehouseInOrderId = orderItemFc.WarehouseInOrderId
		dst.WarehouseInOrderNo = orderItemFc.WarehouseInOrderNo
		dst.Roll = orderItemFc.Roll
		dst.WarehouseBinId = orderItemFc.WarehouseBinId
		dst.VolumeNumber = orderItemFc.VolumeNumber
		dst.BaseUnitWeight = orderItemFc.BaseUnitWeight
		dst.WeightError = orderItemFc.WeightError
		dst.PaperTubeWeight = orderItemFc.PaperTubeWeight
		dst.ActuallyWeight = orderItemFc.ActuallyWeight
		dst.SettleErrorWeight = orderItemFc.SettleErrorWeight
		dst.SettleWeight = orderItemFc.SettleWeight
		dst.Length = orderItemFc.Length
		dst.UnitId = orderItemFc.UnitId
		dst.StockId = orderItemFc.StockId
		dst.ReturnStockId = orderItemFc.ReturnStockId
		dst.SumStockId = orderItemFc.SumStockId
		dst.Remark = orderItemFc.Remark
		dst.InternalRemark = orderItemFc.InternalRemark
		dst.DigitalCode = orderItemFc.DigitalCode
		dst.ShelfNo = orderItemFc.ShelfNo
		dst.AccountNum = orderItemFc.AccountNum
		dst.DyeFactoryColorCode = orderItemFc.DyeFactoryColorCode
		dst.DyeFactoryDyelotNumber = orderItemFc.DyeFactoryDyelotNumber
		dst.ContractNumber = orderItemFc.ContractNumber
		dst.StockRemark = orderItemFc.StockRemark
		dst.ScanUserId = orderItemFc.ScanUserId
		dst.ScanUserName = orderItemFc.ScanUserName
		dst.ScanTime = tools.MyTime(orderItemFc.ScanTime)
		dst.IsInWarehouse = orderItemFc.IsInWarehouse
		dst.BarCode = orderItemFc.BarCode
		dst.QrCode = orderItemFc.QrCode
		dst.DyelotNumber = orderItemFc.DyeFactoryDyelotNumber
		dst.SrcId = orderItemFc.SrcId
		list = append(list, dst)
	}
	return
}

func (r *FpmInOrderRepo) swapListModel2Data(src model.FpmInOrder, dst *structure.GetFpmInOrderData, ctx context.Context) {
	var (
		bizService       = biz_pb.NewClientBizUnitService()
		saleSysPBSerbice = sale_system_pb.NewSaleSystemClient()
		userPB           = empl_pb.NewClientEmployeeService()
		userName         = make(map[uint64]string)
		unitPB           = base_info_pb.NewInfoBaseMeasurementUnitClient()
		warehousePB      = warehouse_pb.NewPhysicalWarehouseClient()
	)
	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.BizUnitId})
	if err1 != nil {
		fmt.Println(err1)
	}
	sale_sys_pbtemMap, err2 := saleSysPBSerbice.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}
	userName, _ = userPB.GetEmployeeNameByIds(r.tx.Context, []uint64{src.StoreKeeperId})
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, src.UnitId)
	warehouseNameMap, _ := warehousePB.GetPhysicalWarehouseByIds(r.tx.Context, []uint64{src.WarehouseId, src.OutWarehouseId})

	// warehouseName, _ := warehousePB.GetPhysicalWarehouseNameById(r.tx.Context, src.WarehouseId)

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.InOrderType = src.InOrderType
	dst.SaleSystemId = src.SaleSystemId
	dst.BizUnitId = src.BizUnitId
	dst.WarehouseId = src.WarehouseId
	dst.WarehouseInTime = tools.MyTime(src.WarehouseInTime)
	dst.StoreKeeperId = src.StoreKeeperId
	dst.Remark = src.Remark
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.OrderNo = src.OrderNo
	dst.AuditStatus = src.AuditStatus
	dst.AuditorId = src.AuditorId
	dst.AuditorName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.UnitId = src.UnitId
	dst.TotalLength = src.TotalLength
	dst.TotalPrice = src.TotalPrice
	dst.TotalRoll = src.TotalRoll
	dst.TotalWeight = src.TotalWeight
	dst.SrcId = src.SrcId
	dst.SrcOrderNo = src.SrcOrderNo
	dst.OutWarehouseId = src.OutWarehouseId
	dst.VoucherNumber = src.VoucherNumber
	dst.TextureUrl = src.TextureUrl
	dst.SaleMode = src.SaleMode
	// 转义
	dst.AuditStatusName = src.AuditStatus.String()
	dst.BusinessCloseName = src.BusinessClose.String()
	dst.InOrderTypeName = src.InOrderType.String()
	dst.SaleModeName = src.SaleMode.String()
	dst.UnitName = unitName
	dst.SaleAllocateOutId = src.SaleAllocateOutId
	dst.SaleUserId = src.SaleUserId
	dst.SaleFollowerId = src.SaleFollowerId
	dst.DriverId = src.DriverId
	dst.LogisticsCompanyId = src.LogisticsCompanyId
	dst.ArrangeId = src.ArrangeOrderId
	if val, ok := sale_sys_pbtemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}
	if val, ok := bizMap[src.BizUnitId]; ok {
		dst.BizUnitName = val
	}
	if val, ok := userName[src.StoreKeeperId]; ok {
		dst.StoreKeeperName = val
	}
	if val, ok := warehouseNameMap[src.WarehouseId]; ok {
		dst.WarehouseName = val
	}
	if val, ok := warehouseNameMap[src.OutWarehouseId]; ok {
		dst.OutWarehouseName = val
	}

}

func (r *FpmInOrderRepo) swapItemModel2Data(item model.FpmInOrderItem, itemGetData *structure.GetFpmInOrderItemData, ctx context.Context) {
	var (
		// PB 相当于rpc获取数据
		bizService = biz_pb.NewClientBizUnitService()
		pLevelPB   = base_info_pb.NewInfoBaseFinishedProductLevelClient()
		unitPB     = base_info_pb.NewInfoBaseMeasurementUnitClient()
		productSvc = base_product_pb.NewProductClient()
	)

	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{item.CustomerId})
	levelName, _ := pLevelPB.GetInfoBaseFinishedProductLevelNameById(ctx, item.ProductLevelId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameByIds(r.tx.Context, []uint64{item.UnitId, item.AuxiliaryUnitId})
	getColor, _, _ := mysql.FirstFinishProductColorByID(r.tx, item.ProductColorId)
	productMap, _ := productSvc.GetProductMapByIds(ctx, []uint64{item.ProductId})

	itemGetData.Id = item.Id
	itemGetData.CreateTime = tools.MyTime(item.CreateTime)
	itemGetData.UpdateTime = tools.MyTime(item.UpdateTime)
	itemGetData.CreatorId = item.CreatorId
	itemGetData.CreatorName = item.CreatorName
	itemGetData.UpdaterId = item.UpdaterId
	itemGetData.UpdateUserName = item.UpdaterName
	itemGetData.ParentId = item.ParentId
	itemGetData.ParentOrderNo = item.ParentOrderNo
	itemGetData.QuoteOrderNo = item.QuoteOrderNo
	itemGetData.QuoteOrderId = item.QuoteOrderId
	itemGetData.QuoteOrderItemId = item.QuoteOrderItemId
	itemGetData.ProductId = item.ProductId
	if product, ok := productMap[item.ProductId]; ok {
		itemGetData.ProductCode = product.FinishProductCode
		itemGetData.ProductName = product.FinishProductName
		itemGetData.ProductCraft = product.FinishProductCraft
		itemGetData.ProductIngredient = product.FinishProductIngredient
	}
	itemGetData.CustomerId = item.CustomerId
	itemGetData.ProductColorId = item.ProductColorId
	itemGetData.ProductColorCode = getColor.ProductColorCode
	itemGetData.ProductColorName = getColor.ProductColorName
	itemGetData.DyeFactoryColorCode = item.DyeFactoryColorCode
	itemGetData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
	itemGetData.ProductWidth = item.ProductWidth
	itemGetData.ProductGramWeight = item.ProductGramWeight
	itemGetData.ProductLevelId = item.ProductLevelId
	itemGetData.ProductRemark = item.ProductRemark
	itemGetData.QuoteRoll = item.QuoteRoll
	itemGetData.QuoteTotalWeight = item.QuoteTotalWeight
	itemGetData.InRoll = item.InRoll
	itemGetData.TotalWeight = item.TotalWeight
	itemGetData.WeightError = item.WeightError
	itemGetData.PaperTubeWeight = item.PaperTubeWeight
	itemGetData.SettleWeight = item.SettleWeight
	itemGetData.QuoteWeight = item.QuoteWeight
	itemGetData.UnitPrice = item.UnitPrice
	itemGetData.UnitId = item.UnitId
	itemGetData.AuxiliaryUnitId = item.AuxiliaryUnitId
	if item.AuxiliaryUnitId == 0 {
		itemGetData.AuxiliaryUnitId = item.UnitId
	}
	itemGetData.InLength = item.InLength
	itemGetData.QuoteLength = item.QuoteLength
	itemGetData.LengthUnitPrice = item.LengthUnitPrice
	itemGetData.OtherPrice = item.OtherPrice
	itemGetData.TotalPrice = item.TotalPrice
	itemGetData.Remark = item.Remark
	itemGetData.SumStockId = item.SumStockId
	// 转义
	itemGetData.UnitName = unitName[item.UnitId]
	itemGetData.AuxiliaryUnitName = unitName[item.AuxiliaryUnitId]
	if val, ok := customerMap[item.CustomerId]; ok {
		itemGetData.CustomerName = val
	}
	itemGetData.ProductLevelName = levelName
	// 加工出仓信息
	processOutItemData, _, _ := mysql.FirstFpmOutOrderItemByID(r.tx, item.QuoteOrderItemId)
	itemGetData.OutRoll = processOutItemData.OutRoll
	itemGetData.OutLength = processOutItemData.OutLength
	itemGetData.OutWeight = processOutItemData.TotalWeight
	// 调拨出仓信息
	allOutItemData, _, _ := mysql.FirstFpmOutOrderItemByID(r.tx, item.QuoteOrderItemId)
	itemGetData.AllocateRoll = allOutItemData.OutRoll
	itemGetData.AllocateWeight = allOutItemData.TotalWeight
	itemGetData.AllocateLength = allOutItemData.OutLength

	itemGetData.SalePlanOrderItemId = item.SalePlanOrderItemId
	itemGetData.SalePlanOrderItemNo = item.SalePlanOrderItemNo
}

func (r *FpmInOrderRepo) swapFcModel2Data(fc model.FpmInOrderItemFc, fineCodeGetData *structure.GetFpmInOrderItemFcData, ctx context.Context) {
	var (
		warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		unitPB      = base_info_pb.NewInfoBaseMeasurementUnitClient()
	)
	binName, _ := warehousePB.GetPhysicalWarehouseBinNameById(ctx, fc.WarehouseBinId)
	unitName, _ := unitPB.GetInfoBaseMeasurementUnitNameById(r.tx.Context, fc.UnitId)

	fineCodeGetData.Id = fc.Id
	fineCodeGetData.CreateTime = tools.MyTime(fc.CreateTime)
	fineCodeGetData.UpdateTime = tools.MyTime(fc.UpdateTime)
	fineCodeGetData.CreatorId = fc.CreatorId
	fineCodeGetData.CreatorName = fc.CreatorName
	fineCodeGetData.UpdaterId = fc.UpdaterId
	fineCodeGetData.UpdateUserName = fc.UpdaterName
	fineCodeGetData.ParentId = fc.ParentId
	fineCodeGetData.WarehouseInType = fc.WarehouseInType
	fineCodeGetData.WarehouseInOrderId = fc.WarehouseInOrderId
	fineCodeGetData.WarehouseInOrderNo = fc.WarehouseInOrderNo
	fineCodeGetData.Roll = fc.Roll
	fineCodeGetData.WarehouseBinId = fc.WarehouseBinId
	fineCodeGetData.VolumeNumber = fc.VolumeNumber
	fineCodeGetData.BaseUnitWeight = fc.BaseUnitWeight
	fineCodeGetData.WeightError = fc.WeightError
	fineCodeGetData.UnitId = fc.UnitId
	fineCodeGetData.StockId = fc.StockId
	fineCodeGetData.SumStockId = fc.SumStockId
	fineCodeGetData.PaperTubeWeight = fc.PaperTubeWeight
	fineCodeGetData.Length = fc.Length
	fineCodeGetData.Remark = fc.Remark
	fineCodeGetData.InternalRemark = fc.InternalRemark
	fineCodeGetData.DigitalCode = fc.DigitalCode
	fineCodeGetData.ShelfNo = fc.ShelfNo
	fineCodeGetData.AccountNum = fc.AccountNum
	fineCodeGetData.DyeFactoryColorCode = fc.DyeFactoryColorCode
	fineCodeGetData.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
	fineCodeGetData.DyelotNumber = fc.DyeFactoryDyelotNumber
	fineCodeGetData.ProductWidth = fc.ProductWidth
	fineCodeGetData.ProductGramWeight = fc.ProductGramWeight
	fineCodeGetData.ContractNumber = fc.ContractNumber
	fineCodeGetData.ScanUserId = fc.ScanUserId
	fineCodeGetData.ScanUserName = fc.ScanUserName
	fineCodeGetData.ScanTime = tools.MyTime(fc.ScanTime)
	fineCodeGetData.StockRemark = fc.StockRemark
	fineCodeGetData.ActuallyWeight = fc.ActuallyWeight
	fineCodeGetData.SettleErrorWeight = fc.SettleErrorWeight
	fineCodeGetData.SettleWeight = fc.SettleWeight

	// 转义
	fineCodeGetData.WarehouseBinName = binName
	fineCodeGetData.UnitName = unitName
	fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
}

func (r *FpmInOrderRepo) judgeAuditPass(id uint64, order model.FpmInOrder, ctx context.Context) (
	addItems structure.AddStockProductDetailParamList,
	updateItems structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64, err error) {
	var (
		supplierId                uint64
		srcIds                    = set.NewUint64Set()
		stockIds                  = set.NewUint64Set()
		srcIdInIdMap              = make(map[uint64]uint64)
		fcIdSupplierIdMap         = make(map[uint64]uint64)
		itemList                  model.FpmInOrderItemList
		fineCodeList              = model.FpmInOrderItemFcList{}
		outFcList                 = model.FpmOutOrderItemFcList{}
		addWeight                 = make([]*structure.AddStockProductDetailParam, 0)
		updateWeight              = make([]*structure.UpdateStockProductDetailParam, 0)
		swap2StockFieldParam      = structure.Swap2StockFieldParam{}
		_salePlanOrderItemIds     = make([]uint64, 0)
		adjustOrderWeightItemIds  = set.NewUint64Set()
		adjustOrderWeightItemList model.ProductAdjustOrderWeightItemList
	)
	// 判断成品数量是否符合

	swap2StockFieldParam.WarehouseInType = order.InOrderType
	swap2StockFieldParam.WarehouseInOrderNo = order.OrderNo
	swap2StockFieldParam.WarehouseInOrderId = order.Id
	swap2StockFieldParam.WarehouseId = order.WarehouseId
	// 正常进仓单取该值，如果是退货进仓取库存的供应商id todo:退货逻辑待补充
	swap2StockFieldParam.SupplierId = order.BizUnitId

	itemList, err = mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindFpmInOrderItemFcByGrandParenTID(r.tx, order.Id)
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := base_product_pb.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}

	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	for _, fc := range fineCodeList {
		if order.InOrderType == consts.WarehouseGoodInTypeInternalAllocate || order.InOrderType == consts.WarehouseGoodInTypeProcessReturn {
			srcIds.Add(fc.SrcId)
			srcIdInIdMap[fc.SrcId] = fc.Id
		}
		if order.InOrderType == consts.WarehouseGoodInTypeAdjust {
			adjustOrderWeightItemIds.Add(fc.SrcId)
		}
	}
	// 找调拨出仓单
	if (order.InOrderType == consts.WarehouseGoodInTypeInternalAllocate || order.InOrderType == consts.WarehouseGoodInTypeProcessReturn) && srcIds.Size() > 0 {
		var (
			temMap          = make(map[uint64]uint64)
			stockDetailList = model.StockProductDetailList{}
		)
		outFcList, err = mysql.FindFpmOutOrderItemFcByIDs(r.tx, srcIds.List())
		if err != nil {
			return
		}
		for _, fc := range outFcList {
			stockIds.Add(fc.StockId)
			temMap[fc.StockId] = fc.Id
		}
		if stockIds.Size() > 0 {
			stockDetailList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds.List())
			if err != nil {
				return
			}
			for _, detail := range stockDetailList {
				if val, ok := temMap[detail.Id]; ok {
					// val 库存出仓细码记录id
					fcIdSupplierIdMap[srcIdInIdMap[val]] = detail.SupplierId // 当前的库存进仓id
				}
			}
		}
	}

	// 其他进仓单,获取往来单位类型判断是否下推应付单
	if order.InOrderType == consts.WarehouseGoodInTypeOther {
		bizData, _ := biz_pb.NewClientBizUnitService().GetBizUnitDetailByID(ctx, biz_pb.Req{Id: order.BizUnitId})
		if bizData.Category == 1 {
			supplierId = bizData.Id
		}
	}

	// 内部调拨类型 成品加工退货进仓单 查询库存
	stockDetailList := make(model.StockProductDetailList, 0)
	if order.InOrderType == consts.WarehouseGoodInTypeInternalAllocate ||
		order.InOrderType == consts.WarehouseGoodInTypeSaleAllocate ||
		order.InOrderType == consts.WarehouseGoodInTypeProcessReturn {
		stockDetailIds := mysql_base.GetUInt64List(fineCodeList, "stock_id")
		stockDetailList, err = mysql.FindStockProductDetailByIDs(r.tx, stockDetailIds)
		if err != nil {
			return
		}
	}

	// 销售退货进仓单
	if order.InOrderType == consts.WarehouseGoodInTypeSaleReturn {
		// todo:待从销售退货进仓单中搬过来
	}

	if order.InOrderType == consts.WarehouseGoodInTypeAdjust {
		adjustOrderWeightItemList, err = mysql.FindProductAdjustOrderWeightItemByIDs(r.tx, adjustOrderWeightItemIds.List())
		if err != nil {
			return
		}
	}

	for _, item := range itemList {
		_fineCodeList := fineCodeList.PickFcListByParentId(item.Id)
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		totalRoll := 0
		totalLength := 0

		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		swap2StockFieldParam.FinishProductWidthUnitId = item.FinishProductWidthUnitId
		swap2StockFieldParam.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId

		if item.InRoll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(errors.NewError(errors.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if len(_fineCodeList) == 0 {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.ProductName))
			return
		}

		for _, fineCode := range _fineCodeList {
			detailStock := stockDetailList.Pick(fineCode.StockId)
			totalRoll = totalRoll + fineCode.Roll
			totalLength += fineCode.Length
			// 内部调拨进仓单
			if order.InOrderType == consts.WarehouseGoodInTypeInternalAllocate {
				swap2StockFieldParam.FinishProductWidthUnitId = detailStock.FinishProductWidthUnitId
				swap2StockFieldParam.FinishProductGramWeightUnitId = detailStock.FinishProductGramWeightUnitId
				swap2StockFieldParam.OutStockProductDetailId = outFcList.Pick(fineCode.SrcId).StockId
			}
			// 加工进仓单
			if order.InOrderType == consts.WarehouseGoodInTypeProcess {
				swap2StockFieldParam.FinishProductWidthUnitId = fineCode.FinishProductWidthUnitId
				swap2StockFieldParam.FinishProductGramWeightUnitId = fineCode.FinishProductGramWeightUnitId
			}
			// 调整单
			if order.InOrderType == consts.WarehouseGoodInTypeAdjust {
				// 只需要更新旧库存信息，调整细码中旧库存必须带上库存id
				if fineCode.StockId != 0 {
					// detailStock := stockDetailList.Pick(fineCode.StockId)
					// swap2StockFieldParam.FinishProductWidthUnitId = detailStock.FinishProductWidthUnitId
					// swap2StockFieldParam.FinishProductGramWeightUnitId = detailStock.FinishProductGramWeightUnitId
					weight := fineCode.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam, false)
					weight.IsToWait = false
					weight.Type = 5
					updateWeight = append(updateWeight, weight)
				} else {
					// 需要新增库存信息
					swap2StockFieldParam.OutStockProductDetailId = adjustOrderWeightItemList.Pick(fineCode.SrcId).StockProductDetailId
					weight := fineCode.ToAddStockProductDetailParam(ctx, swap2StockFieldParam)
					addWeight = append(addWeight, weight)
				}
				continue
			}

			weight := fineCode.ToAddStockProductDetailParam(ctx, swap2StockFieldParam)
			switch order.InOrderType {
			case consts.WarehouseGoodInTypeInternalAllocate:
				weight.SupplierId = fcIdSupplierIdMap[fineCode.Id]
				weight.QrCode = detailStock.QrCode

			case consts.WarehouseGoodInTypeOther:
				weight.SupplierId = supplierId
			case consts.WarehouseGoodInTypePurchase:
				weight.SupplierId = order.BizUnitId
			case consts.WarehouseGoodInTypeProcessReturn:
				weight.SupplierId = fcIdSupplierIdMap[fineCode.Id]
				weight.QrCode = detailStock.QrCode
			case consts.WarehouseGoodInTypeSaleAllocate:
				weight.SupplierId = fcIdSupplierIdMap[fineCode.Id]
				weight.QrCode = detailStock.QrCode
			case consts.WarehouseGoodInTypeSaleReturn:
				weight.SupplierId = fcIdSupplierIdMap[fineCode.Id]
				weight.QrCode = detailStock.QrCode
			}

			addWeight = append(addWeight, weight)
		}

		if totalRoll != item.InRoll {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.ProductName))
			return
		}
		if item.InLength > 0 && totalLength > 0 && item.InLength != totalLength {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeFCNoEqualItemLength, "错误行:"+item.ProductName))
			return
		}
		if item.SalePlanOrderItemId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
	}
	addItems = addWeight
	updateItems = updateWeight
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *FpmInOrderRepo) judgeAuditWait(id uint64, fpmInOrder model.FpmInOrder, ctx context.Context) (
	updateItems structure.UpdateStockProductDetailParamList,
	salePlanOrderItemIds []uint64,
	err error) {

	var (
		updateWeight          = make([]*structure.UpdateStockProductDetailParam, 0)
		srcIds                = set.NewUint64Set()
		outFcStockMap         = make(map[uint64]uint64)
		_salePlanOrderItemIds = make([]uint64, 0)
		swap2StockFieldParam  = structure.Swap2StockFieldParam{}
	)
	// 判断成品数量是否符合
	itemList, err := mysql.FindFpmInOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	fineCodeList, err := mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, itemList.GetIds())
	if err != nil {
		return
	}

	productColorIds := mysql_base.GetUInt64List(itemList, "product_color_id")
	productColorSvc := base_product_pb.NewProductColorClient()
	productColorItem, err := productColorSvc.GetProductColorByIds(ctx, productColorIds)
	if err != nil {
		return
	}
	measurementUnitIds := mysql_base.GetUInt64List(itemList, "measurement_unit_id")
	measurementUnitNameSvc := base_info_pb.NewInfoBaseMeasurementUnitClient()
	measurementUnitName, err := measurementUnitNameSvc.GetInfoBaseMeasurementUnitNameByIds(ctx, measurementUnitIds)
	if err != nil {
		return
	}

	if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeProcessReturn {
		for _, fc := range fineCodeList {
			srcIds.Add(fc.SrcId)
		}

		outFCList, _err := mysql.FindFpmOutOrderItemFcByIDs(r.tx, srcIds.List())
		if _err != nil {
			err = _err
			return
		}

		for _, fc := range outFCList {
			outFcStockMap[fc.Id] = fc.StockId
		}
	}

	swap2StockFieldParam.WarehouseInType = fpmInOrder.InOrderType
	swap2StockFieldParam.WarehouseInOrderNo = fpmInOrder.OrderNo
	swap2StockFieldParam.WarehouseInOrderId = fpmInOrder.Id
	swap2StockFieldParam.WarehouseId = fpmInOrder.WarehouseId

	for _, item := range itemList {
		productColor := productColorItem.PickByProductColorId(item.ProductColorId)
		swap2StockFieldParam.ProductId = item.ProductId
		swap2StockFieldParam.DyeFactoryColorCode = item.DyeFactoryColorCode
		swap2StockFieldParam.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		swap2StockFieldParam.ProductColorId = item.ProductColorId
		swap2StockFieldParam.CustomerId = item.CustomerId
		swap2StockFieldParam.ProductLevelId = item.ProductLevelId
		swap2StockFieldParam.ProductColorKindId = productColor.TypeFinishedProductKindId
		swap2StockFieldParam.ItemProductRemark = item.ProductRemark
		swap2StockFieldParam.MeasurementUnitId = item.UnitId
		swap2StockFieldParam.MeasurementUnitName = measurementUnitName[item.UnitId]
		swap2StockFieldParam.FinishProductWidthUnitId = item.FinishProductWidthUnitId
		swap2StockFieldParam.FinishProductGramWeightUnitId = item.FinishProductGramWeightUnitId

		_fineCodeList := fineCodeList.PickFcListByParentId(item.Id)
		// fineCodeList, _ := mysql.FindFpmInOrderItemFcByParenTID(r.tx, item.Id)
		for _, fc := range _fineCodeList {
			// 调整单
			if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeAdjust {
				weight := fc.ToUpdateStockProductDetailParam(ctx, swap2StockFieldParam, true)
				// 需要修改细码信息，所以IsToWait为false
				// 新增的细码，直接把记录删除掉
				weight.Type = 5
				if fc.IsUpdateStock {
					weight.IsToWait = false
				} else {
					weight.IsToWait = true
				}
				updateWeight = append(updateWeight, weight)
				continue
			}
			weight := fc.ToUpdateStockProductDetailParamBack(ctx)
			// 加工退货进仓单特殊一点
			if fpmInOrder.InOrderType == consts.WarehouseGoodInTypeProcessReturn {
				if fc.StockId == outFcStockMap[fc.SrcId] {
					weight.IsToWait = false
				}
			}
			updateWeight = append(updateWeight, weight)
		}

		if item.SalePlanOrderItemId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}

	}
	updateItems = updateWeight
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

// 获取采购进仓单详情(带细码)
func (r *FpmInOrderRepo) GetFpmPrcInOrderDetailsWithFc(ctx context.Context, req *structure.GetFpmInOrderListQuery) (
	list structure.GetFpmInOrderItemDropdownDataList, total int, err error) {
	var (
		orderItems   model.FpmInOrderItemList
		orders       model.FpmInOrderList
		fineCodeList model.FpmInOrderItemFcList
		bizPB        = biz_pb.NewClientBizUnitService()
		// salePB         = sale_system_pb.NewSaleSystemClient()
		dictionaryPB = dictionary.NewDictionaryClient()
		unitPB       = base_info_pb.NewInfoBaseMeasurementUnitClient()
		// warehousePB    = warehouse_pb.NewPhysicalWarehouseClient()
		productPB       = base_product_pb.NewProductClient()
		productColorPB  = base_product_pb.NewProductColorClient()
		stockIds        = set.NewUint64Set()
		detailStockList model.StockProductDetailList
		detailStockMap  = make(map[uint64]model.StockProductDetail)
		pLevelPB        = base_info_pb.NewInfoBaseFinishedProductLevelClient()
	)

	if req.ProductCodeOrName != "" {
		req.ProductIds, err = productPB.GetProductIds(ctx, "", "", req.ProductCodeOrName)
		if err != nil {
			return
		}
	}
	if req.ProductColorCodeOrName != "" {
		req.ProductColorIds, err = productColorPB.GetProductColorIds(ctx, "", "", req.ProductColorCodeOrName)
		if err != nil {
			return
		}
	}

	req.AuditStatus = tools.QueryIntList(tools.Int2String(int(common_system.OrderStatusAudited)))
	orderItems, total, err = mysql.SearchFpmInOrderDetail(r.tx, req)
	if err != nil {
		return
	}

	orders, err = mysql.FindFpmInOrderByIDs(r.tx, orderItems.GetOrderIDs())
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindFpmInOrderItemFcByParenTIDs(r.tx, orderItems.GetIds())
	if err != nil {
		return
	}

	for _, orderItemFc := range fineCodeList {
		stockIds.Add(orderItemFc.StockId)
	}

	detailStockList, err = mysql.FindStockProductDetailByIDs(r.tx, stockIds.List())
	if err != nil {
		return
	}
	for _, detailStock := range detailStockList {
		detailStockMap[detailStock.Id] = detailStock
	}

	g := errgroup.WithCancel(ctx)
	var (
		unitNameMap map[uint64]string
		// saleSysNameMap map[uint64]string
		// wareNameMap    map[uint64]string
		bizNameMap map[uint64]string
		// empNameMap     map[uint64]string
		dicNameMap   map[uint64][2]string
		levelNameMap map[uint64]string
		colorMap     map[uint64][2]string
		productMap   map[uint64]*base_product_pb.ProductRes
		binNameMap   map[uint64]string
	)

	g.Go(func(ctx context.Context) error {
		var err1 error
		unitIds := mysql_base.GetUInt64List(orderItems, "measurement_unit_id")
		unitNameMap, err1 = unitPB.GetInfoBaseMeasurementUnitNameByIds(ctx, unitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "FindFpmInOrderByIDs err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		bizUnitIds := mysql_base.GetUInt64List(orderItems, "biz_unit_id")
		bizNameMap, err1 = bizPB.GetBizUnitNameByIds(ctx, bizUnitIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err"))
		}
		return nil
	})

	g.Go(func(ctx context.Context) error {
		var err1 error
		dicIds := mysql_base.GetUInt64ListV2("dictionary_detail_id", orderItems)
		dicNameMap, err1 = dictionaryPB.GetDictionaryNameByIds(ctx, dicIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetDictionaryNameByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		productIds := mysql_base.GetUInt64List(orderItems, "product_id")
		productMap, err1 = productPB.GetProductMapByIds(ctx, productIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductMapByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		productColorIds := mysql_base.GetUInt64List(orderItems, "product_color_id")
		colorMap, err1 = productColorPB.GetProductColorItemByIds(ctx, productColorIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductColorItemByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		levelIds := mysql_base.GetUInt64List(orderItems, "product_level_id")
		levelNameMap, err1 = pLevelPB.GetInfoBaseFinishedProductLevelNameByIds(ctx, levelIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetInfoBaseFinishedProductLevelNameByIds err"))
		}
		return nil
	})
	g.Go(func(ctx context.Context) error {
		var err1 error
		var warehousePB = warehouse_pb.NewPhysicalWarehouseClient()
		wbinIds := mysql_base.GetUInt64List(fineCodeList, "warehouse_bin_id")
		binNameMap, err1 = warehousePB.GetPhysicalWarehouseBinNameByIds(ctx, wbinIds)
		if err1 != nil {
			middleware.WarnLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseBinNameByIds err"))
		}
		return nil
	})

	if err = g.Wait(); err != nil {
		return nil, 0, err
	}

	for _, item := range orderItems.List() {
		order := orders.Pick(item.ParentId)
		dst := structure.GetFpmInOrderItemDropdownData{}
		dst.Id = item.Id
		dst.CreateTime = tools.MyTime(item.CreateTime)
		dst.UpdateTime = tools.MyTime(item.UpdateTime)
		dst.CreatorId = item.CreatorId
		dst.CreatorName = item.CreatorName
		dst.UpdaterId = item.UpdaterId
		dst.UpdateUserName = item.UpdaterName

		dst.WarehouseInTime = tools.MyTime(order.WarehouseInTime)
		dst.ParentOrderNo = item.ParentOrderNo
		dst.QuoteOrderNo = item.QuoteOrderNo
		dst.QuoteOrderItemId = item.QuoteOrderItemId
		dst.ProductId = item.ProductId
		if productItem, ok := productMap[item.ProductId]; ok {
			dst.ProductCode = productItem.FinishProductCode
			dst.ProductName = productItem.FinishProductName
			dst.ProductCraft = productItem.FinishProductCraft
			dst.ProductIngredient = productItem.FinishProductIngredient
		}
		dst.ProductColorId = item.ProductColorId
		dst.ProductColorCode = colorMap[item.ProductColorId][0]
		dst.ProductColorName = colorMap[item.ProductColorId][1]
		dst.DyeFactoryColorCode = item.DyeFactoryColorCode
		dst.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
		dst.ProductRemark = item.ProductRemark
		dst.QuoteRoll = item.QuoteRoll
		dst.QuoteTotalWeight = item.QuoteTotalWeight
		dst.InRoll = item.InRoll
		dst.WeightError = item.WeightError
		dst.PaperTubeWeight = item.PaperTubeWeight
		dst.SettleWeight = item.SettleWeight
		dst.QuoteWeight = item.QuoteWeight
		dst.UnitPrice = item.UnitPrice
		dst.UnitId = item.UnitId
		dst.AuxiliaryUnitId = item.AuxiliaryUnitId
		if item.AuxiliaryUnitId == 0 {
			dst.AuxiliaryUnitId = item.UnitId
		}
		dst.InLength = item.InLength
		dst.QuoteLength = item.QuoteLength
		dst.LengthUnitPrice = item.LengthUnitPrice
		dst.OtherPrice = item.OtherPrice
		dst.Remark = item.Remark

		dst.TotalWeight = item.TotalWeight
		dst.TotalPrice = item.TotalPrice
		dst.InLength = item.InLength
		dst.WeightError = item.WeightError
		dst.PaperTubeWeight = item.PaperTubeWeight
		dst.SettleWeight = item.SettleWeight

		dst.BuildFPResp(item.ProductWidth, item.ProductGramWeight, dicNameMap[item.FinishProductWidthUnitId][1],
			dicNameMap[item.FinishProductGramWeightUnitId][1], item.FinishProductWidthUnitId, item.FinishProductGramWeightUnitId)

		dst.UnitName = unitNameMap[item.UnitId]
		dst.AuxiliaryUnitName = unitNameMap[item.AuxiliaryUnitId]
		dst.CustomerId = item.CustomerId
		if val, ok := bizNameMap[item.CustomerId]; ok {
			dst.CustomerName = val
		}
		dst.ProductLevelId = item.ProductLevelId
		dst.ProductLevelName = levelNameMap[item.ProductLevelId]

		dst.SalePlanOrderItemId = item.SalePlanOrderItemId
		dst.SalePlanOrderItemNo = item.SalePlanOrderItemNo
		// dst.AvailableRoll = item.InRoll - item.ReturnRoll
		// dst.AvailableWeight = item.SettleWeight - item.ReturnWeight
		// dst.AvailableLength = item.InLength - item.ReturnLength
		// 添加细码信息
		fcList := fineCodeList.PickFcListByParentId(item.Id)
		var itemFCData = make(structure.GetFpmInOrderItemFcDataList, 0)
		for _, fc := range fcList {
			detailStock, ok := detailStockMap[fc.StockId]
			if !ok {
				continue
			}
			if detailStock.Status != common_system.StockStatusWarehouseIn {
				continue
			}

			dst.AvailableRoll += detailStock.Roll
			dst.AvailableWeight += detailStock.Weight
			dst.AvailableLength += detailStock.Length

			fineCodeGetData := structure.GetFpmInOrderItemFcData{}
			fineCodeGetData.Id = fc.Id
			fineCodeGetData.SrcId = fc.SrcId
			fineCodeGetData.CreateTime = tools.MyTime(fc.CreateTime)
			fineCodeGetData.UpdateTime = tools.MyTime(fc.UpdateTime)
			fineCodeGetData.CreatorId = fc.CreatorId
			fineCodeGetData.CreatorName = fc.CreatorName
			fineCodeGetData.UpdaterId = fc.UpdaterId
			fineCodeGetData.UpdateUserName = fc.UpdaterName
			fineCodeGetData.ParentId = fc.ParentId
			fineCodeGetData.WarehouseInType = fc.WarehouseInType
			fineCodeGetData.WarehouseInOrderId = fc.WarehouseInOrderId
			fineCodeGetData.WarehouseInOrderNo = fc.WarehouseInOrderNo
			fineCodeGetData.Roll = fc.Roll
			fineCodeGetData.WarehouseBinId = fc.WarehouseBinId
			fineCodeGetData.VolumeNumber = fc.VolumeNumber
			fineCodeGetData.BaseUnitWeight = fc.BaseUnitWeight
			fineCodeGetData.WeightError = fc.WeightError
			fineCodeGetData.UnitId = fc.UnitId
			fineCodeGetData.StockId = fc.StockId
			fineCodeGetData.SumStockId = fc.SumStockId
			fineCodeGetData.PaperTubeWeight = fc.PaperTubeWeight
			fineCodeGetData.Length = fc.Length
			fineCodeGetData.Remark = fc.Remark
			fineCodeGetData.InternalRemark = fc.InternalRemark
			fineCodeGetData.DigitalCode = fc.DigitalCode
			fineCodeGetData.ShelfNo = fc.ShelfNo
			fineCodeGetData.AccountNum = fc.AccountNum
			fineCodeGetData.DyeFactoryColorCode = fc.DyeFactoryColorCode
			fineCodeGetData.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
			fineCodeGetData.DyelotNumber = fc.DyeFactoryDyelotNumber
			// 增加旧单判断，致盛旧单问题，如果重新更新才能去掉
			// if fc.DyeFactoryDyelotNumber != item.DyeFactoryDyelotNumber {
			// 	fineCodeGetData.DyeFactoryDyelotNumber = item.DyeFactoryDyelotNumber
			// 	fineCodeGetData.DyelotNumber = item.DyeFactoryDyelotNumber
			// }
			if fc.DyeFactoryColorCode != item.DyeFactoryColorCode {
				fineCodeGetData.DyeFactoryColorCode = item.DyeFactoryColorCode
			}
			if order.InOrderType == consts.WarehouseGoodInTypeProcessReturn {
				fineCodeGetData.DyeFactoryColorCode = fc.DyeFactoryColorCode
				fineCodeGetData.DyeFactoryDyelotNumber = fc.DyeFactoryDyelotNumber
				fineCodeGetData.DyelotNumber = fc.DyeFactoryDyelotNumber
			}
			// fineCodeGetData.ProductWidth = fc.ProductWidth
			// fineCodeGetData.ProductGramWeight = fc.ProductGramWeight
			fineCodeGetData.ContractNumber = fc.ContractNumber
			fineCodeGetData.ScanUserId = fc.ScanUserId
			fineCodeGetData.ScanUserName = fc.ScanUserName
			fineCodeGetData.ScanTime = tools.MyTime(fc.ScanTime)
			fineCodeGetData.StockRemark = fc.StockRemark
			fineCodeGetData.ActuallyWeight = fc.ActuallyWeight
			fineCodeGetData.SettleErrorWeight = fc.SettleErrorWeight
			fineCodeGetData.SettleWeight = fc.SettleWeight
			dst.SumStockId = fc.SumStockId

			// 转义
			fineCodeGetData.WarehouseBinName = binNameMap[fc.WarehouseBinId]
			fineCodeGetData.UnitName = unitNameMap[fc.UnitId]
			fineCodeGetData.MeasurementUnitName = fineCodeGetData.UnitName
			fineCodeGetData.BarCode = detailStock.BarCode
			fineCodeGetData.QrCode = tools.String2Utf8(detailStock.QrCode)
			fineCodeGetData.DyelotNumber = detailStock.DyelotNumber
			fineCodeGetData.PrintDate = time.Now().Format("2006-01-02")
			if order.InOrderType == consts.WarehouseGoodInTypeInternalAllocate {
				fineCodeGetData.BuildFPResp(fc.ProductWidth, fc.ProductGramWeight, dicNameMap[detailStock.FinishProductWidthUnitId][1],
					dicNameMap[detailStock.FinishProductGramWeightUnitId][1], detailStock.FinishProductWidthUnitId, detailStock.FinishProductGramWeightUnitId)
			} else {
				fineCodeGetData.BuildFPResp(fc.ProductWidth, fc.ProductGramWeight, dicNameMap[fc.FinishProductWidthUnitId][1],
					dicNameMap[fc.FinishProductGramWeightUnitId][1], fc.FinishProductWidthUnitId, fc.FinishProductGramWeightUnitId)
			}
			if product, ok := productMap[item.ProductId]; ok {
				fineCodeGetData.ProductKindId = product.TypeGreyFabricId
				fineCodeGetData.ProductKindName = product.TypeGreyFabricName
				fineCodeGetData.FinishProductCraft = product.FinishProductCraft
				fineCodeGetData.FinishProductIngredient = product.FinishProductIngredient
				fineCodeGetData.WeavingOrganizationId = product.WeavingOrganizationId
				fineCodeGetData.WeavingOrganizationCode = product.WeavingOrganizationCode
				fineCodeGetData.WeavingOrganizationName = product.WeavingOrganizationName
				fineCodeGetData.Density = product.Density
				fineCodeGetData.YarnCount = product.YarnCount
				fineCodeGetData.BleachId = product.BleachId
				fineCodeGetData.BleachName = product.BleachName
			}
			// 补充打码暂时使用，2024-1-11处理完去除，2024-2-11可删除
			// fineCodeGetData.BarCode = fc.BarCode
			// fineCodeGetData.QrCode = tools.String2Utf8(fc.QrCode)
			itemFCData = append(itemFCData, fineCodeGetData)
		}
		dst.ItemFCData = itemFCData
		// if dst.AvailableRoll != 0 || dst.AvailableWeight != 0 {
		list = append(list, dst)
		// }
	}
	return
}

func (r *FpmInOrderRepo) UpdateReturnItem(ctx context.Context, req structure.ModifyFpmInOrder) (err error) {
	var (
		item model.FpmInOrderItem
	)
	// 找出该单下的信息
	item, err = mysql.MustFirstFpmInOrderItemByID(r.tx, req.Id)
	if err != nil {
		return
	}

	item.ReturnRoll += req.ReturnRoll
	item.ReturnLength += req.ReturnLength
	item.ReturnWeight += req.ReturnWeight

	item, err = mysql.MustUpdateFpmInOrderItem(r.tx, item)
	if err != nil {
		return
	}
	return
}

func (r *FpmInOrderRepo) GetIDsBySrcID(ctx context.Context, srcIDs []uint64) (ids []uint64, err error) {
	var (
		orders model.FpmInOrderList
	)
	ids = make([]uint64, 0)
	orders, err = mysql.FindFpmInOrderByPurchaseOrderIDs(r.tx, srcIDs)
	if err != nil {
		return
	}

	for _, order := range orders {
		ids = append(ids, order.Id)
	}
	return
}

func (r *FpmInOrderRepo) WashInOrderForImport(ctx context.Context, req *structure.GetFpmInOrderListQuery) (err error) {
	var (
		stockProductDetails             model.StockProductDetailList
		fpmOutOrderItemFcList           model.FpmOutOrderItemFcList
		fpmInOrderItemFcList            model.FpmInOrderItemFcList
		productCheckOrderWeightItemList model.ProductCheckOrderWeightItemList
		list                            = make(map[string]map[string]structure.AddFpmInOrderItemFcParamList, 0)
		stockProductDetailMap           = make(map[uint64]model.StockProductDetail, 0)
		// size                                      = 1000
		// total                                     = 2000
	)
	// for i := 1; i*size < total; i++ {
	// 获取出所有导入的库存
	stockProductDetails, _, err = mysql.WashStockProductDetailByType(r.tx, &mysql.WashStockProductDetailListQuery{
		ListQuery:       req.ListQuery,
		WarehouseInType: 0,
		WarehouseId:     req.WarehouseId,
	})
	if err != nil {
		return
	}

	var (
		productIDs            = set.NewUint64Set()
		stockProductDetailIDs []uint64
		productColorIDs       = set.NewUint64Set()
		productSvc            = base_product_pb.NewProductClient()
		productColorSvc       = base_product_pb.NewProductColorClient()
		productMap            map[uint64]*base_product_pb.ProductRes
		productColorMap       map[uint64]*base_product_pb.ProductColorRes
	)

	for _, stockProductDetail := range stockProductDetails {
		if stockProductDetail.WarehouseId == 0 {
			continue
		}
		stockProductDetailMap[stockProductDetail.Id] = stockProductDetail
		productIDs.Add(stockProductDetail.ProductId)
		stockProductDetailIDs = append(stockProductDetailIDs, stockProductDetail.Id)
		productColorIDs.Add(stockProductDetail.ProductColorId)
	}

	// 查询出仓单获取已进仓数量
	fpmInOrderItemFcList, err = mysql.FindFpmInOrderItemFcByStockProductDetailID(r.tx, stockProductDetailIDs)
	if err != nil {
		return
	}

	// 查询出仓单获取已出仓数量
	fpmOutOrderItemFcList, err = mysql.FindFpmOutOrderItemFcByStockProductDetailID(r.tx, stockProductDetailIDs)
	if err != nil {
		return
	}

	// 查询盘点单盈亏总数
	productCheckOrderWeightItemList, err = mysql.WashByDetailStockIds(r.tx, stockProductDetailIDs)
	if err != nil {
		return
	}

	productMap, err = productSvc.GetProductMapByIds(ctx, productIDs.List())
	if err != nil {
		return
	}

	productColorMap, err = productColorSvc.GetProductColorMapByIds(ctx, productColorIDs.List())
	if err != nil {
		return
	}

	var failName string
	// 发货单位 1676436327014656 营销体系 1648873238909184 仓库
	for _, stockProductDetail := range stockProductDetails {
		if stockProductDetail.ProductId == 0 || stockProductDetail.ProductColorId == 0 {
			continue
		}
		if productMap[stockProductDetail.ProductId] == nil {
			continue
		}
		if productColorMap[stockProductDetail.ProductColorId] == nil {
			continue
		}
		_fpmInOrderItemFcList := fpmInOrderItemFcList.PickByStockDetailId(stockProductDetail.Id)
		inTotal := _fpmInOrderItemFcList.GetTotalRWL()
		_fpmOutOrderItemFcList := fpmOutOrderItemFcList.PickByStockDetailId(stockProductDetail.Id)
		outTotal := _fpmOutOrderItemFcList.GetTotalRWL()
		_productCheckOrderWeightItemList := productCheckOrderWeightItemList.PickByStockDetailId(stockProductDetail.Id)
		checkTotal := _productCheckOrderWeightItemList.GetTotalRWL()
		// 判断库存数量与进出仓盘点的数据是否一致，一致则跳过不需要重新生成进仓单
		if inTotal[0]-outTotal[0]+checkTotal[0] == stockProductDetail.Roll &&
			inTotal[1]-outTotal[1]+checkTotal[1] == stockProductDetail.Weight &&
			inTotal[2]-outTotal[2]+checkTotal[2] == stockProductDetail.Length {
			continue
		}
		// 营销体系名称，供应商名称，仓库名称，进仓时间，仓位
		key := fmt.Sprintf("%v%v%v", stockProductDetail.SupplierId, stockProductDetail.WarehouseId, stockProductDetail.WarehouseInTime)
		// 颜色编号，颜色名称，所属客户名称，染厂色号，染厂缸号，成品幅宽，成品克重，成品幅宽单位名称，成品克重单位名称，成品等级名称，成品备注，基本单位单价，长度单位单价
		itemKey := fmt.Sprintf("%v%v%v%v%v%v%v%v%v%v%v%v",
			stockProductDetail.ProductColorId,
			stockProductDetail.CustomerId,
			stockProductDetail.DyeFactoryColorCode,
			stockProductDetail.DyelotNumber,
			stockProductDetail.FinishProductWidth,
			stockProductDetail.FinishProductGramWeight,
			stockProductDetail.FinishProductWidthUnitId,
			stockProductDetail.FinishProductGramWeightUnitId,
			stockProductDetail.ProductLevelId,
			stockProductDetail.ProductRemark,
			0,
			0,
		)
		fCode := structure.AddFpmInOrderItemFcParam{}
		fCode.WarehouseId = stockProductDetail.WarehouseId
		fCode.WarehouseBinId = stockProductDetail.WarehouseBinId
		if product, ok := productMap[stockProductDetail.ProductId]; ok {
			fCode.UnitId = product.MeasurementUnitId
			fCode.AuxiliaryUnitId = product.MeasurementUnitId
		}
		fCode.VolumeNumber = stockProductDetail.VolumeNumber
		fCode.Roll = stockProductDetail.Roll - (inTotal[0] - outTotal[0] + checkTotal[0])
		fCode.Length = stockProductDetail.Length - (inTotal[2] - outTotal[2] + checkTotal[2])
		fCode.BaseUnitWeight = stockProductDetail.Weight - (inTotal[1] - outTotal[1] + checkTotal[1])
		// 判断匹数数量或者长度为负数，则跳过该条记录(负数也进行处理，平账)
		// if fCode.Roll < 0 ||
		// 	fCode.Length < 0 ||
		// 	fCode.BaseUnitWeight < 0 {
		// failName += fmt.Sprintf("Id：%v  成品编号：%v  颜色编号：%v  缸号：%v  卷号：%v\n", stockProductDetail.Id, productMap[stockProductDetail.ProductId].FinishProductCode,
		// 	productColorMap[stockProductDetail.ProductColorId].ProductColorCode, stockProductDetail.DyelotNumber, stockProductDetail.VolumeNumber)
		// continue
		// }
		fCode.WeightError = stockProductDetail.WeightError
		fCode.PaperTubeWeight = stockProductDetail.PaperTubeWeight
		// fCode.SettleWeight = int(detailData.Weight * vars.Weight)
		fCode.DigitalCode = stockProductDetail.DigitalCode
		fCode.ShelfNo = stockProductDetail.ShelfNo
		fCode.DyeFactoryColorCode = stockProductDetail.DyeFactoryColorCode
		fCode.DyeFactoryDyelotNumber = stockProductDetail.DyelotNumber
		fCode.Remark = stockProductDetail.Remark
		fCode.InternalRemark = stockProductDetail.InternalRemark
		fCode.ProductWidth = stockProductDetail.FinishProductWidth
		fCode.ProductGramWeight = stockProductDetail.FinishProductGramWeight
		fCode.FinishProductWidthUnitId = stockProductDetail.FinishProductWidthUnitId
		fCode.FinishProductGramWeightUnitId = stockProductDetail.FinishProductGramWeightUnitId
		fCode.BarCode = stockProductDetail.BarCode
		fCode.QrCode = stockProductDetail.QrCode
		fCode.AccountNum = stockProductDetail.CustomerAccountNum
		fCode.SumStockId = stockProductDetail.StockProductId
		fCode.StockId = stockProductDetail.Id

		fCode.BizUnitID = stockProductDetail.SupplierId
		fCode.CustomerId = stockProductDetail.CustomerId
		fCode.ProductId = stockProductDetail.ProductId
		fCode.ProductColorId = stockProductDetail.ProductColorId
		fCode.ProductLevelId = stockProductDetail.ProductLevelId
		fCode.ProductRemark = stockProductDetail.ProductRemark
		fCode.WarehouseInTime = tools.MyTime(stockProductDetail.WarehouseInTime)
		if req.WarehouseInTime != "" {
			fCode.WarehouseInTime = tools.MyTime(req.WarehouseInTime.ToTimeYMD())
		}
		orderList, ok := list[key]
		if !ok {
			orderList = make(map[string]structure.AddFpmInOrderItemFcParamList, 0)
			list[key] = orderList
		}
		fCodes, ok := orderList[itemKey]
		if !ok {
			fCodes = make(structure.AddFpmInOrderItemFcParamList, 0)
		}
		if fCode.BaseUnitWeight != 0 && fCode.ProductId != 0 && fCode.ProductColorId != 0 {
			fCodes = append(fCodes, fCode)
		}
		orderList[itemKey] = fCodes
		list[key] = orderList
	}
	// }
	for _, orderList := range list {
		q := &structure.AddFpmInOrderParam{}
		q.InOrderType = consts.WarehouseGoodInTypeOther
		q.OrderNoPre = "FPOW"
		var items = make(structure.AddFpmInOrderItemParamList, 0)
		for _, itemList := range orderList {
			item := structure.AddFpmInOrderItemParam{}
			var fCodes = make(structure.AddFpmInOrderItemFcParamList, 0)
			for i, detailData := range itemList {
				if item.ProductId == 0 {
					item.ProductId = detailData.ProductId
				}
				if item.ProductColorId == 0 {
					item.ProductColorId = detailData.ProductColorId
				}
				if i == 0 {
					q.SaleSystemId = 1648873238909184
					q.BizUnitId = detailData.BizUnitID
					q.WarehouseId = detailData.WarehouseId
					if !detailData.WarehouseInTime.IsZero() {
						q.WarehouseInTime = tools.QueryTime(detailData.WarehouseInTime.Date())
						// q.GenOrderNoTime = detailData.WarehouseInTime.ToTime()
					} else {
						q.WarehouseInTime = tools.QueryTime(time.Now().Format("2006-01-02"))
					}
					if req.WarehouseInTime != "" {
						q.WarehouseInTime = req.WarehouseInTime
						// q.GenOrderNoTime = req.WarehouseInTime.ToTimeYMD()
					}
					q.Remark = "数据清洗"

					item.ProductId = detailData.ProductId
					item.CustomerId = detailData.CustomerId
					item.ProductColorId = detailData.ProductColorId
					item.DyeFactoryColorCode = detailData.DyeFactoryColorCode
					item.DyeFactoryDyelotNumber = detailData.DyeFactoryDyelotNumber
					item.ProductWidth = detailData.ProductWidth
					item.ProductGramWeight = detailData.ProductGramWeight
					item.ProductLevelId = detailData.ProductLevelId
					item.ProductRemark = detailData.ProductRemark
					item.UnitId = detailData.UnitId
					item.AuxiliaryUnitId = detailData.AuxiliaryUnitId
					item.UnitPrice = 0
					item.LengthUnitPrice = 0
					item.FinishProductWidthUnitId = detailData.FinishProductWidthUnitId
					item.FinishProductGramWeightUnitId = detailData.FinishProductGramWeightUnitId
				}
				fCodes = append(fCodes, detailData)
				item.InRoll += detailData.Roll
				item.TotalWeight += detailData.BaseUnitWeight
				item.InLength += detailData.Length
				item.OtherPrice += 0
			}
			item.ItemFCData = fCodes
			if len(fCodes) != 0 {
				items = append(items, item)
			}
		}
		if len(items) == 0 {
			continue
		}
		q.ItemData = items
		res, err1 := r.Add(ctx, q)
		if err1 != nil {
			continue
		}
		_, _, _, _, err1 = r.UpdateStatusPass(ctx, res.Id)
		if err1 != nil {
			continue
		}
	}

	fmt.Println(failName)
	return
}
