package system

import (
	"context"
	"fmt"
	"hcscm/common/errors"
	"hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/system"
	mysql "hcscm/model/mysql/system/dao"
	"hcscm/model/redis"
	structure "hcscm/structure/system"
)

type IDistrictAreaRepo interface {
	FindByIDs(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (districtAreas model.DistrictAreaList, err error)
	MustFirst(ctx context.Context, tx *mysql_base.Tx, districtAreaID uint64) (districtArea model.DistrictArea, err error)
	FindByID(ctx context.Context, tx *mysql_base.Tx, districtAreaID uint64) (mergeDistrict mysql.MergeDistrict, err error)
	FindByParentID(ctx context.Context, tx *mysql_base.Tx, parentID uint64) (list model.DistrictAreaList, err error)
	Search(ctx context.Context, tx *mysql_base.Tx, q *structure.GetProvinceListQuery) (districtAreas model.DistrictAreaList, count int, err error)
	SearchEnum(ctx context.Context, tx *mysql_base.Tx, q *structure.GetProvinceListQuery) (districtAreas model.DistrictAreaList, count int, err error)
}

func NewDistrictAreaRepo(ctx context.Context, isCache bool) IDistrictAreaRepo {
	return &districtAreaRepo{
		mysqlFn:   mysql_base.NewMysqlFn(),
		redisFn:   redis.NewNode(redis.GetClient(ctx), errors.ErrCodeRedisError, 28800),
		prefixKey: "hcscm:cache:district_area:",
		isCache:   isCache,
	}
}

type districtAreaRepo struct {
	mysqlFn   mysql_base.IMysqlFn
	redisFn   redis.IRedisFn
	prefixKey string
	isCache   bool
}

func (repo *districtAreaRepo) FindByIDs(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (districtAreas model.DistrictAreaList, err error) {
	var (
		districtArea model.DistrictArea
		cond         = mysql_base.NewCondition()
	)

	err = repo.mysqlFn.Find(tx, &districtArea, model.GetDistrictAreaIDList(objects), &districtAreas, cond)
	if err != nil {
		return
	}
	return
}

func (repo *districtAreaRepo) MustFirst(ctx context.Context, tx *mysql_base.Tx, districtAreaID uint64) (districtArea model.DistrictArea, err error) {
	if !repo.isCache {
		var cond = mysql_base.NewCondition()
		err = repo.mysqlFn.MustFirst(tx, &districtArea, districtAreaID, cond)
		if err != nil {
			return
		}
		return
	}
	err = repo.redisFn.Take(&districtArea, fmt.Sprintf("%s%d", repo.prefixKey, districtAreaID), func(v interface{}) error {
		var cond = mysql_base.NewCondition()
		return repo.mysqlFn.MustFirst(tx, &districtArea, districtAreaID, cond)
	})
	if err != nil {
		return
	}
	return
}

// 根据id获取行政区域、省份，市区、
func (repo *districtAreaRepo) FindByID(ctx context.Context, tx *mysql_base.Tx, districtAreaID uint64) (mergeDistrict mysql.MergeDistrict, err error) {

	for {
		if districtAreaID == 0 {
			break
		}
		var (
			area model.DistrictArea
		)
		err = repo.mysqlFn.MustFirst(tx, &area, districtAreaID, mysql_base.NewCondition())
		if err != nil {
			return
		}
		switch area.Level {
		case common.DistrictLevelCountry:
			mergeDistrict.Country = area
		case common.DistrictLevelProvince:
			mergeDistrict.Province = area
		case common.DistrictLevelCity:
			mergeDistrict.City = area
		case common.DistrictLevelDistrict:
			mergeDistrict.District = area
		case common.DistrictLevelStreet:
			mergeDistrict.Street = area
		}
		districtAreaID = area.ParentID
	}

	// 整理 将街道和镇合并
	if !mergeDistrict.District.IsAvailable() {
		mergeDistrict.District = mergeDistrict.Street
	}

	return
}

// 根据id获取行政区域
func (repo *districtAreaRepo) FindByParentID(ctx context.Context, tx *mysql_base.Tx, parentID uint64) (list model.DistrictAreaList, err error) {

	var (
		area     model.DistrictArea
		areaList model.DistrictAreaList
		cond     = mysql_base.NewCondition()
	)
	cond.AddEqual("parent_id", parentID)
	err = repo.mysqlFn.FindByCond(tx, &area, &areaList, cond)
	if err != nil {
		return
	}
	list = areaList
	return
}

func (repo *districtAreaRepo) Search(ctx context.Context, tx *mysql_base.Tx, q *structure.GetProvinceListQuery) (districtAreas model.DistrictAreaList, count int, err error) {
	var (
		cond         = mysql_base.NewCondition()
		districtArea model.DistrictArea
	)
	if q.DistrictLevel != 0 {
		cond.AddEqual("level", q.DistrictLevel)
	}
	if q.ParentID != 0 {
		cond.AddEqual("parent_id", q.ParentID)
	} else {
		cond.AddEqual("parent_id", 1)
	}
	if q.Name != "" {
		cond.AddEqual("name", q.Name)
	}
	count, err = repo.mysqlFn.SearchListGroupForPaging(tx, &districtArea, q, &districtAreas, cond)
	if err != nil {
		return
	}

	return
}

func (repo *districtAreaRepo) SearchEnum(ctx context.Context, tx *mysql_base.Tx, q *structure.GetProvinceListQuery) (districtAreas model.DistrictAreaList, count int, err error) {
	var (
		cond         = mysql_base.NewCondition()
		districtArea model.DistrictArea
	)
	if q.DistrictLevel != 0 {
		cond.AddEqual("level", q.DistrictLevel)
	}
	if q.ParentID != 0 {
		cond.AddEqual("parent_id", q.ParentID)
	}
	if q.Name != "" {
		cond.AddEqual("name", q.Name)
	}
	count, err = repo.mysqlFn.SearchListGroupForPaging(tx, &districtArea, q, &districtAreas, cond)
	if err != nil {
		return
	}

	return
}
