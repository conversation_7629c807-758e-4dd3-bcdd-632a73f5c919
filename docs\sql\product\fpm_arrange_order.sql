CREATE TABLE IF NOT EXISTS `fpm_arrange_order` (
    -- 业务部分
    `src_type` tinyint(1) NULL DEFAULT 0 COMMENT '来源类型（1预约，2销售）',
    `src_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '来源id，必填',
    `src_order_no`  VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '来源单号，必填',
    `out_order_type` tinyint(1) NULL DEFAULT 0 COMMENT '出仓类型',
    `business_status` tinyint(1) NULL DEFAULT 0 COMMENT '业务状态',
    `sale_system_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '营销体系id，必填',
    `arrange_time` datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '配布日期',
    `arrange_to_warehouse_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '调至仓库id',
    `warehouse_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '仓库id',
    `biz_unit_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '往来单位id',
    `process_factory_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '加工厂id',
    `receive_addr` text DEFAULT '' NOT NULL COMMENT '收货地址',
    `receive_phone`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货电话',
    `receive_tag`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '收货标签',
    `driver_id`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '司机id，逗号分割（关联user.id）',
    `sale_user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售员id',
    `sale_follower_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '销售跟单员id',
    `store_keeper_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '仓管员id（关联user.id）',
    `logistics_company_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '物流公司id',
    `internal_remark` text NOT NULL DEFAULT '' COMMENT '内部备注',
    `sale_remark`text NOT NULL DEFAULT '' COMMENT '销售备注',
    -- 统计数据
    `total_roll`  int(11) NOT NULL DEFAULT 0 COMMENT '匹数总计',
    `total_weight`  bigint(20) NOT NULL DEFAULT 0 COMMENT '数量总计',
    `total_length`  int(11) NOT NULL DEFAULT 0 COMMENT '长度总计',
    `unit_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '单位id',
    -- 销售部分,来源类型（2销售）
    `send_product_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '出货类型 1出货 2销调',
    `sale_group_id`  bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '销售群体id',
    `settle_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '结算类型',
    `logistics_area` varchar(255) NOT NULL DEFAULT '' COMMENT '物流区域',
    `postage_items` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '邮费项目 1包邮 2不包邮',
    `send_product_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '出货备注',
    `contacts` varchar(255) NOT NULL DEFAULT '' COMMENT '联系人',
    `is_with_tax_rate` tinyint(1) NOT NULL DEFAULT '0' COMMENT '单价是否含税',
    `tax_rate` int(11) NULL DEFAULT 0 COMMENT '税率',
    -- 基本创建信息
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `business_close` tinyint(1) NULL DEFAULT 0 COMMENT '业务关闭',
    `business_close_user_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '业务关闭操作人',
    `business_close_user_name` VARCHAR(255)  NOT NULL DEFAULT '' COMMENT '业务关闭操作人名',
    `business_close_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '业务关闭时间',
    `department_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '下单用户所属部门',
    `order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '单据编号',
    `number` int(4) NOT NULL DEFAULT 0 COMMENT '编号流水：每日重新更新',
    `audit_status` tinyint(4) NOT NULL DEFAULT 0 COMMENT '审核状态',
    `auditor_id` bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '审核人ID （关联user.id）',
    `auditor_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '审核人名称',
    `audit_date` datetime NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '审核时间',
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    `delete_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '删除时间',
    `deleter_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '删除人ID （关联user.id）',
    `deleter_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '删除人名称',
    PRIMARY KEY (`id`),
    KEY `idx_src_id` (`src_id`),
    KEY `idx_warehouse_id` (`warehouse_id`),
    UNIQUE KEY `order_no` (`order_no`),
    KEY `idx_sale_system_id` (`sale_system_id`),
    KEY `idx_process_factory_id` (`process_factory_id`),
    KEY `idx_department_id` (`department_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='成品管理-配布单';

CREATE TABLE IF NOT EXISTS `fpm_arrange_order_item` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `parent_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '父id（单号id）',
    `parent_order_no`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '父单号(对应的单据号)(对应的单据号)',
    `product_id`     bigint(20) unsigned  NOT NULL DEFAULT 0 COMMENT '成品id',
    `product_code`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品编号',
    `product_name`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品名称',
    `customer_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '所属客户id',
    `product_color_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '成品颜色id',
    `product_color_code`   VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品名称',
    `product_color_name`   VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品名称',
    `product_level_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '成品等级',
    `product_width`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品幅宽',
    `dye_factory_color_code` varchar(255) NOT NULL DEFAULT '' COMMENT '染厂色号',
    `dye_factory_dyelot_number` varchar(255) NOT NULL DEFAULT '' COMMENT '染厂缸号',
    `product_gram_weight`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品克重',
    `product_remark`    text NOT NULL DEFAULT '' COMMENT '成品备注',
    `product_craft`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品工艺',
    `product_ingredient`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品成分',
    `arrange_roll` int (11) DEFAULT 0 NOT NULL COMMENT '配布件数(件)，乘100存',
    `quote_order_no`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '引用数据单号',
    `quote_order_item_id`     bigint(20) unsigned  NOT NULL DEFAULT 0 COMMENT '引用数据单物料那条id',
    `sale_plan_order_item_id`     bigint(20) unsigned  NOT NULL DEFAULT 0 COMMENT '成品销售计划单子项信息id',
    -- 库存信息
    `sum_stock_id` bigint(20) unsigned  DEFAULT '0' NOT NULL COMMENT '汇总库存id',
    `sum_stock_roll` int (11) DEFAULT 0 NOT NULL COMMENT '汇总库存成品匹数',
    `sum_stock_weight` int (11) DEFAULT 0 NOT NULL COMMENT '汇总库存成品数量',
    `sum_stock_length` int (11) DEFAULT 0 NOT NULL COMMENT '汇总库存成品长度',
    -- 出仓数量信息
    `arrange_weight` int (11) DEFAULT 0 NOT NULL COMMENT '出仓总数量(公斤)，乘10000存',
    `weight_error` int (11) DEFAULT '0' NOT NULL COMMENT '码单空差数量(公斤)，乘10000存',
    `actually_weight` int (11) DEFAULT '0' NOT NULL COMMENT '码单数量(公斤)，乘10000存',
    `paper_tube_weight` int (11) DEFAULT '0' NOT NULL COMMENT '纸筒数量(公斤)，乘10000存',
    `settle_error_weight` int (11) DEFAULT '0' NOT NULL COMMENT '结算空差数量(公斤)，乘10000存',
    `settle_weight` int (11) DEFAULT '0' NOT NULL COMMENT '结算数量(公斤)，乘10000存',
    `unit_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '单位id',
    -- 出仓长度
    `arrange_length` int (11) DEFAULT 0 NOT NULL COMMENT '出仓长度，乘100存',
    `remark` text DEFAULT '' NOT NULL COMMENT '备注',
    -- 下推数量
    `push_roll` int (11) DEFAULT 0 NOT NULL COMMENT '下推匹数',
    `push_length` int (11) DEFAULT 0 NOT NULL COMMENT '下推长度',
    `push_weight` bigint(20) DEFAULT 0 NOT NULL COMMENT '下推数量',
    -- 销售价格
    `standard_sale_price` int(11) NOT NULL DEFAULT '0' COMMENT '标准销售报价(大货 散剪)',
    `sale_level_id` bigint(20) unsigned NOT NULL DEFAULT '0' COMMENT '销售等级ID',
    `offset_sale_price` int(11) NOT NULL DEFAULT '0' COMMENT '优惠单价(标准报价-对应等级的售价+大货单价优惠(元/公斤))',
    `sale_price` int(11) NOT NULL DEFAULT '0' COMMENT '销售单价(销售报价-优惠单价)(大货 散剪)',
    `standard_weight_error` int(11) NOT NULL DEFAULT '0' COMMENT '标准空差 /0.1g',
    `offset_weight_error` int(11) NOT NULL DEFAULT '0' COMMENT '优惠空差 /0.1g',
    `adjust_weight_error` int(11) NOT NULL DEFAULT '0' COMMENT '调整空差 /0.1g',
    `standard_length_cut_sale_price` int(11) NOT NULL DEFAULT '0' COMMENT '剪板销售价格',
    `offset_length_cut_sale_price` int(11) NOT NULL DEFAULT '0' COMMENT '剪版优惠单价',
    `length_cut_sale_price` int(11) NOT NULL DEFAULT '0' COMMENT '剪版销售单价(剪板销售价格-剪版优惠单价)',
    `other_price` bigint(20) DEFAULT 0 NOT NULL COMMENT '其他金额',
    `sale_tax_rate` int(11) DEFAULT 0 NOT NULL COMMENT '销售税率',
    auxiliary_unit_id BIGINT(20) UNSIGNED NOT NULL DEFAULT 0 COMMENT '辅助单位id',
    -- 基本创建信息
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    PRIMARY KEY (`id`),
    KEY `idx_sum_stock_id` (`sum_stock_id`),
    KEY `idx_customer_id` (`customer_id`),
    KEY `idx_parent_id` (`parent_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='成品管理-配布单-成品信息';


CREATE TABLE IF NOT EXISTS `fpm_arrange_order_item_fc` (
    `id` bigint(20) unsigned NOT NULL AUTO_INCREMENT,
    `parent_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '父id（成品信息行id）',
    `roll` int (11) DEFAULT 0 NOT NULL COMMENT '条数(条)，乘100存',
    `warehouse_id`  bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '仓库id',
    `warehouse_bin_id` bigint(20) unsigned  DEFAULT '0' NOT NULL COMMENT '仓位id',
    `volume_number` int (11) DEFAULT 0 NOT NULL COMMENT '卷号',
    `warehouse_out_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '出仓类型',
    `warehouse_out_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '出仓单id',
    `warehouse_out_order_no` VARCHAR(128) DEFAULT '' NOT NULL COMMENT '出仓单号',
    `warehouse_in_type` tinyint(3) unsigned NOT NULL DEFAULT '0' COMMENT '来源类型',
    `warehouse_in_order_id` bigint(20) unsigned NOT NULL DEFAULT 0 COMMENT '进仓单id',
    `warehouse_in_order_no` VARCHAR(128) DEFAULT '' NOT NULL COMMENT '进仓单号',
    `arrange_order_no`  VARCHAR(255) NOT NULL DEFAULT '' COMMENT '配布单号',
    `stock_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '库存成品id',
    `sum_stock_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '汇总库存成品id',
    `base_unit_weight` int (11) DEFAULT '0' NOT NULL COMMENT '基本单位数量(公斤)，乘10000存',
    `paper_tube_weight` int (11) DEFAULT '0' NOT NULL COMMENT '纸筒数量(公斤)，乘10000存',
    `weight_error` int (11) DEFAULT '0' NOT NULL COMMENT '空差数量(公斤)，乘10000存',
    `actually_weight` int (11) DEFAULT '0' NOT NULL COMMENT '码单数量(公斤)，乘10000存',
    `settle_error_weight` int (11) DEFAULT '0' NOT NULL COMMENT '结算空差数量(公斤)，乘10000存',
    `unit_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '单位id（kg）',
    `length` int (11) DEFAULT 0 NOT NULL COMMENT '长度，乘100存',
    `settle_weight` int (11) DEFAULT '0' NOT NULL COMMENT '结算数量(公斤)，乘10000存',
    -- 成品信息
    `digital_code` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '数字码',
    `shelf_no` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '货架号',
    `contract_number` VARCHAR(128) DEFAULT '' NOT NULL COMMENT '合同号',
    `customer_po_num` VARCHAR(128) DEFAULT '' NOT NULL COMMENT '客户po号',
    `account_num` VARCHAR(128) DEFAULT '' NOT NULL COMMENT '客户款号',
    `dye_factory_color_code` text NOT NULL DEFAULT '' COMMENT '染厂色号',
    `dye_factory_dyelot_number` text NOT NULL DEFAULT '' COMMENT '染厂缸号',
    `product_width`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品幅宽',
    `product_gram_weight`    VARCHAR(255) NOT NULL DEFAULT '' COMMENT '成品克重',
    `stock_remark` varchar(255) NOT NULL DEFAULT '' COMMENT '库存备注',
    `remark` varchar(255) NOT NULL DEFAULT '' COMMENT '备注',
    `internal_remark` text NOT NULL DEFAULT '' COMMENT '内部备注',
    -- 基本创建信息
    `scan_user_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '扫描人id',
    `scan_user_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '扫描人名称',
    `scan_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '扫描时间',
    `create_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '创建时间',
    `creator_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '创建人ID （关联user.id）',
    `creator_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '创建人名称',
    `update_time`  datetime  NOT NULL DEFAULT '0000-00-00 00:00:00' COMMENT '更新时间',
    `updater_id`   bigint(20) unsigned DEFAULT 0 NOT NULL COMMENT '更新人ID （关联user.id）',
    `updater_name` VARCHAR(255) DEFAULT '' NOT NULL COMMENT '更新人名称',
    PRIMARY KEY (`id`),
    KEY `idx_warehouse_bin_id` (`warehouse_bin_id`),
    KEY `idx_parent_id` (`parent_id`),
    KEY `idx_stock_id` (`stock_id`)
    ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 ROW_FORMAT=DYNAMIC COMMENT='成品管理-配布单-成品信息-细码';
