# 客户第三方认证系统

## 概述

客户第三方认证系统为HCSCM项目提供了客户专用的第三方登录和认证功能，允许客户通过认证密钥访问电子色卡等专用接口。

## 功能特性

- 客户第三方登录认证
- 基于认证密钥的权限验证
- 客户专用的电子色卡接口
- 后台客户认证管理
- Redis缓存支持
- 认证过期时间管理

## 数据库表结构

### customer_third_party_auth 表

| 字段名 | 类型 | 说明 |
|--------|------|------|
| id | bigint | 主键ID |
| customer_id | bigint | 客户ID（关联biz_unit表） |
| auth_key | varchar(255) | 认证密钥 |
| status | int | 状态（1:启用 2:禁用） |
| expire_time | bigint | 过期时间戳 |
| remark | varchar(500) | 备注 |
| created_at | bigint | 创建时间 |
| updated_at | bigint | 更新时间 |

## API接口

### 客户登录接口

**POST** `/api/third_party/v1/customer/login`

请求参数：
```json
{
  "customerId": 123,
  "authKey": "customer_auth_key_here"
}
```

响应数据：
```json
{
  "code": 200,
  "data": {
    "token": "customer_token_here",
    "expireTime": 1640995200,
    "customerInfo": {
      "id": 123,
      "name": "客户名称",
      "permissions": ["color_card:read"]
    }
  }
}
```

### 客户专用电子色卡接口

所有接口都需要在请求头中携带：
- `Platform: customer`
- `Authorization: Bearer {customer_token}`

**GET** `/api/third_party/v1/customer/color_card/finish_product_color_list` - 获取成品颜色列表

**GET** `/api/third_party/v1/customer/color_card/product_detail` - 获取商品详情

**GET** `/api/third_party/v1/customer/color_card/fabric_category_list` - 获取布种类别

**GET** `/api/third_party/v1/customer/color_card/carousel_banner_list` - 获取轮播图列表

**GET** `/api/third_party/v1/customer/color_card/merchant_info` - 获取商家信息

**GET** `/api/third_party/v1/customer/color_card/export_list` - 导出列表

### 后台管理接口

**GET** `/api/admin/v1/customerAuth/list` - 获取客户认证列表

**POST** `/api/admin/v1/customerAuth/create` - 创建客户认证

**PUT** `/api/admin/v1/customerAuth/update` - 更新客户认证

**DELETE** `/api/admin/v1/customerAuth/delete/:id` - 删除客户认证

## 使用流程

### 1. 后台创建客户认证

1. 登录后台管理系统
2. 进入客户认证管理页面
3. 点击"创建客户认证"
4. 选择客户，设置过期时间和备注
5. 系统自动生成认证密钥

### 2. 客户登录

1. 客户使用客户ID和认证密钥调用登录接口
2. 系统验证认证信息的有效性
3. 返回客户专用token和权限信息

### 3. 访问专用接口

1. 在请求头中携带Platform和Authorization信息
2. 系统验证token有效性和客户权限
3. 返回相应的业务数据

## 安全特性

- 认证密钥自动生成，确保唯一性
- 支持认证过期时间设置
- 支持认证状态管理（启用/禁用）
- Token存储在Redis中，支持快速验证
- 中间件层面的权限验证

## 配置说明

系统会自动创建数据库表结构，无需手动配置。认证相关的Redis键值对会自动管理。

## 注意事项

1. 客户ID必须是biz_unit表中category=2的有效客户
2. 认证密钥一旦生成不可修改，如需更换请重新创建
3. Token过期后需要重新登录获取新token
4. 建议定期检查和清理过期的认证记录
