package qywx

import (
	"context"
	errors2 "errors"
	"fmt"
	"gorm.io/gorm"
	mysqlBizUnit "hcscm/aggs/biz_unit"
	employee_repo "hcscm/aggs/employee"
	"hcscm/aggs/qywx"
	"hcscm/aggs/role"
	"hcscm/aggs/tenant_management"
	app_svc "hcscm/api/wx/app"
	app "hcscm/api/wx/app/v1"
	corp_tag_svc "hcscm/api/wx/corp_tag"
	corp_tag "hcscm/api/wx/corp_tag/v1"
	group_robot_svc "hcscm/api/wx/group_robot"
	group_robot "hcscm/api/wx/group_robot/v1"
	template_svc "hcscm/api/wx/template"
	template "hcscm/api/wx/template/v1"
	tenant_svc "hcscm/api/wx/tenant"
	tenant "hcscm/api/wx/tenant/v1"
	tobe_developed_app_info_svc "hcscm/api/wx/tobe_developed_app_info"
	tobe_developed_app_info "hcscm/api/wx/tobe_developed_app_info/v1"
	"hcscm/common/errors"
	common "hcscm/common/qywx"
	bizUnit_entity "hcscm/domain/biz_unit/entity"
	"hcscm/domain/employee/entity"
	"hcscm/extern/pb/biz_unit"
	"hcscm/log"
	employeeModel "hcscm/model/mysql/employee"
	"hcscm/model/mysql/mysql_base"
	qywxModel "hcscm/model/mysql/qywx"
	systemModel "hcscm/model/mysql/system"
	tenantManagementModel "hcscm/model/mysql/tenant_management"
	"hcscm/service/bot"
	structure "hcscm/structure/qywx"
	"hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"strconv"
	"strings"
	"time"
)

type QYWXService struct {
}

func NewQYWXService() *QYWXService {
	return &QYWXService{}
}

// GetQYWXBoundList 获取账套和企微应用绑定列表
func (s *QYWXService) GetQYWXBoundList(ctx context.Context, request structure.GetQYWXBoundListRequest) (data structure.GetQYWXBoundListResponse, err error) {
	tenantClient := tenant_svc.GetTenantClient(ctx)
	tobeDevelopedAppInfoClient := tobe_developed_app_info_svc.GetTobeDevelopedAppInfoClient(ctx)
	tenantReplay, err := tenantClient.ListTenant(ctx, &tenant.ListTenantRequest{
		Page:                 uint32(request.Page),
		Size:                 uint32(request.Size),
		Name:                 request.TenantName,
		Phone:                request.Phone,
		Contact:              request.ContactPerson,
		TobeDevelopedAppName: request.TobeDevelopedAppName,
	})
	if err != nil {
		return
	}
	b := strings.Builder{}
	for i, item := range tenantReplay.List {
		if i > 0 {
			b.WriteString(",")
		}
		b.WriteString(strconv.FormatUint(item.TobeDevelopedAppId, 10))
	}
	tobeDevelopedAppInfoReplay, err := tobeDevelopedAppInfoClient.ListTobeDevelopedAppInfo(ctx, &tobe_developed_app_info.ListTobeDevelopedAppInfoRequest{
		Page: uint32(request.Page),
		Size: uint32(request.Size),
		Ids:  b.String(),
	})
	if err != nil {
		return
	}
	tobeDevelopedAppInfoMap := make(map[uint64]*tobe_developed_app_info.ListTobeDevelopedAppInfoReply_TobeDevelopedAppInfo)
	for _, info := range tobeDevelopedAppInfoReplay.List {
		tobeDevelopedAppInfoMap[info.Id] = info
	}
	for _, tenant := range tenantReplay.List {
		t, _ := time.Parse("2006-01-02 15:04:05", tenant.UpdateTime)
		item := structure.GetQYWXBoundListResponseItem{
			TenantID:      tenant.TenantManagementId,
			TenantName:    tenant.TenantManagementName,
			Phone:         tenant.Phone,
			ContactPerson: tenant.Contact,
			Updater:       tenant.UpdaterName,
			UpdateTime:    tools.MyTime(t),
		}
		tobeDevelopedAppInfo, exist := tobeDevelopedAppInfoMap[tenant.TobeDevelopedAppId]
		if exist {
			t, _ = time.Parse("2006-01-02 15:04:05", tobeDevelopedAppInfo.RobotEffectTime)
			item.TobeDevelopedAppID = tobeDevelopedAppInfo.Id
			item.TobeDevelopedAppName = tobeDevelopedAppInfo.Name
			item.AgentID = int64(tobeDevelopedAppInfo.AgentId)
			item.RobotCode = tobeDevelopedAppInfo.RobotCode
			item.RobotEffectTime = tools.MyTime(t)
			item.Token = tobeDevelopedAppInfo.Token
			item.EncodingAesKey = tobeDevelopedAppInfo.EncodingAesKey
		}
		data.List = append(data.List, item)
	}
	data.Total = int(tenantReplay.Total)
	return
}

// GetQYWXRobotList 获取企微机器人列表
func (s *QYWXService) GetQYWXRobotList(ctx context.Context, request structure.GetQYWXRobotRequest) (data structure.GetQYWXRobotResponse, err error) {
	groupRobotClient := group_robot_svc.GetGroupRobotClient(ctx)
	reply, err := groupRobotClient.ListGroupRobot(ctx, &group_robot.ListGroupRobotRequest{
		Page:                   uint32(request.Page),
		Size:                   uint32(request.Size),
		TobeDevelopedAppInfoId: request.TobeDevelopedAppID,
	})
	if err != nil {
		return
	}
	for _, item := range reply.List {
		data.List = append(data.List, structure.QYWXRobot{
			ID:   item.Id,
			Name: item.Name,
			Url:  item.Url,
		})
	}
	data.Total = int(reply.Total)
	return
}

// CreateTobeDevelopedApp 创建代开发应用
func (s *QYWXService) CreateTobeDevelopedApp(ctx context.Context, request structure.CreateTobeDevelopedAppRequest) (data structure.TobeDevelopedAppResponse, err error) {
	var (
		authCorpID uint64
		exist      bool
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	appRepo := qywx.NewTobeDevelopedAppRepo(tx)
	authCorpRepo := qywx.NewAuthCorpRepo(tx)
	authCorpID, exist, err = authCorpRepo.FirstByCorpID(ctx, request.CorpID)
	if err != nil {
		return
	}
	if !exist {
		authCorpID, err = authCorpRepo.MustCreateAuthCorp(ctx, request)
		if err != nil {
			return
		}
	}
	data, err = appRepo.Add(ctx, authCorpID, request)
	return
}

// GetTobeDevelopedAppList 代开发应用列表
func (s *QYWXService) GetTobeDevelopedAppList(ctx context.Context, request structure.GetTobeDevelopedAppListRequest) (data structure.GetTobeDevelopedAppListResponse, err error) {
	tobeDevelopedAppInfoClient := tobe_developed_app_info_svc.GetTobeDevelopedAppInfoClient(ctx)
	reply, err := tobeDevelopedAppInfoClient.ListTobeDevelopedAppInfo(ctx, &tobe_developed_app_info.ListTobeDevelopedAppInfoRequest{
		Page:     uint32(request.Page),
		Size:     uint32(request.Size),
		CorpId:   request.QueryStr,
		CorpName: request.QueryStr,
	})
	if err != nil {
		return
	}
	for _, item := range reply.List {
		data.List = append(data.List, structure.GetTobeDevelopedAppListResponseItem{
			CorpName:           item.CorpName,
			CorpID:             item.CorpId,
			AppName:            item.Name,
			AgentID:            int64(item.AgentId),
			TobeDevelopedAppID: item.Id,
		})
	}
	data.Total = int(reply.Total)
	return
}

// TobeDevelopedAppBindTenant 账套绑定代开发应用
func (s *QYWXService) TobeDevelopedAppBindTenant(ctx context.Context, request structure.TobeDevelopedAppBindTenantRequest) (data structure.TobeDevelopedAppResponse, err error) {
	tenantClient := tenant_svc.GetTenantClient(ctx)
	tobeDevelopedAppInfoClient := tobe_developed_app_info_svc.GetTobeDevelopedAppInfoClient(ctx)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	repo := tenant_management.NewTenantManagementRepo()
	for _, tenantManagementIDStr := range request.TenantManagementIDs.ToString() {
		var (
			tenantManagementID uint64
			tenantManagement   tenantManagementModel.TenantManagement
		)
		tenantManagementID, err = strconv.ParseUint(tenantManagementIDStr, 10, 64)
		if err != nil {
			return
		}
		tenantManagement, err = repo.MustFirst(ctx, tx, tenantManagementID)
		if err != nil {
			return
		}
		_, err = tenantClient.CreateTenant(ctx, &tenant.CreateTenantRequest{
			TenantManagementId:       tenantManagementID,
			TenantManagementName:     tenantManagement.TenantCompanyName,
			TenantManagementDeadline: tenantManagement.Deadline.Format(time.DateTime),
			DatabaseName:             tenantManagement.DatabaseName,
			TobeDevelopedAppId:       request.TobeDevelopedAppID,
			Phone:                    tenantManagement.TenantPhoneNumber,
			Contact:                  tenantManagement.TenantContacts,
		})
		if err != nil {
			return
		}
		_, err = tobeDevelopedAppInfoClient.UpdateTobeDevelopedAppInfo(ctx, &tobe_developed_app_info.UpdateTobeDevelopedAppInfoRequest{
			Token:          request.Token,
			EncodingAesKey: request.EncodingAesKey,
			Id:             request.TobeDevelopedAppID,
		})
		if err != nil {
			return
		}
	}

	return
}

// TobeDevelopedAppCancelBindTenant 账套取消绑定代开发应用
func (s *QYWXService) TobeDevelopedAppCancelBindTenant(ctx context.Context, request structure.TobeDevelopedAppCancelBindTenantRequest) (data structure.TenantManagementResponse, err error) {
	tenantClient := tenant_svc.GetTenantClient(ctx)
	_, err = tenantClient.DeleteTenant(ctx, &tenant.DeleteTenantRequest{TenantManagementId: request.TenantManagementID})
	return
}

// TobeDevelopedAppBindRobot 代开发应用绑定阿布机器人
func (s *QYWXService) TobeDevelopedAppBindRobot(ctx context.Context, request structure.UpdateTobeDevelopedAppRequest) (data structure.TobeDevelopedAppResponse, err error) {
	tobeDevelopedAppInfoClient := tobe_developed_app_info_svc.GetTobeDevelopedAppInfoClient(ctx)
	_, err = tobeDevelopedAppInfoClient.UpdateTobeDevelopedAppInfoRobot(ctx, &tobe_developed_app_info.UpdateTobeDevelopedAppInfoRobotRequest{
		RobotCode:       request.RobotCode,
		RobotEffectTime: request.RobotEffectTime.Date(),
		Id:              request.TobeDevelopedAppID,
	})
	return
}

// CreateQYWXRobot 创建企微机器人
func (s *QYWXService) CreateQYWXRobot(ctx context.Context, request structure.CreateQYWXRobotRequest) (data structure.QYWXRobotResponse, err error) {
	groupRobotClient := group_robot_svc.GetGroupRobotClient(ctx)
	_, err = groupRobotClient.CreateGroupRobot(ctx, &group_robot.CreateGroupRobotRequest{
		TobeDevelopedAppInfoId: request.TobeDevelopedAppID,
		Name:                   request.Name,
		Url:                    request.Url,
	})
	return
}

// DeleteQYWXRobot 删除企微机器人
func (s *QYWXService) DeleteQYWXRobot(ctx context.Context, request structure.DeleteQYWXRobotRequest) (data structure.QYWXRobotResponse, err error) {
	groupRobotClient := group_robot_svc.GetGroupRobotClient(ctx)
	_, err = groupRobotClient.DeleteGroupRobot(ctx, &group_robot.DeleteGroupRobotRequest{Id: request.ID})
	return
}

// GetQYWXUsers 绑定企微员工列表
func (s *QYWXService) GetQYWXUsers(ctx context.Context, info metadata.IOperator, request structure.GetQYWXUsersRequest) (data structure.GetQYWXUsersResponse, err error) {
	var (
		reply *app.GetQYWXUsersReply
	)
	client := app_svc.GetAppClient(ctx)
	reply, err = client.GetQYWXUsers(ctx, &app.GetQYWXUsersRequest{
		Name: request.Name,
		Page: int64(request.Page),
		Size: int64(request.Size),
	})
	if err != nil {
		return
	}
	data.Total = reply.Total
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	employeeRepo := employee_repo.NewRepo(tx)
	qywxUserIDs := make([]string, 0, len(reply.Users))
	for _, qywxUser := range reply.Users {
		qywxUserIDs = append(qywxUserIDs, qywxUser.UserId)
	}
	employeeList := make(employeeModel.EmployeeList, 0, len(qywxUserIDs))
	employeeList, err = employeeRepo.GetEmployeeByQYWXUserIDs(ctx, qywxUserIDs)
	if err != nil {
		return
	}
	for _, user := range reply.Users {
		data.List = append(data.List, structure.GetQYWXUsersResponseItem{
			Name:   user.Name,
			UserID: user.UserId,
			Bound:  employeeList.PickByQYWXUserID(user.UserId).Id != 0,
		})
	}
	return
}

// GetUserInfo 获取扫码登录的企微员工信息
func (s *QYWXService) QYWXGetUserInfo(ctx context.Context, req structure.QYWXGetUserInfoRequest) (user systemModel.User, tenantManagementID uint64, err error) {
	var (
		appReply         = new(app.GetUserInfoReply)
		templateReply    = new(template.GetOpenCorpIDReply)
		tobeDevelopedApp qywxModel.TobeDevelopedApp
		exist            bool
		rel              qywxModel.TobeDevelopedAppRel
		employee         employeeModel.Employee
		tenantManagement tenantManagementModel.TenantManagement
		tenantDB         = new(gorm.DB)
	)
	templateClient := template_svc.GetTemplateClient(ctx)
	templateReply, err = templateClient.GetOpenCorpID(ctx, &template.GetOpenCorpIDRequest{CorpID: req.CorpID})
	if err != nil {
		return
	}
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	tobeDevelopedAppRepo := qywx.NewTobeDevelopedAppRepo(tx)
	tobeDevelopedApp, exist, err = tobeDevelopedAppRepo.GetByCorpIDAndAgentID(ctx, templateReply.OpenCorpID, req.AgentID)
	if err != nil {
		return
	}
	if !exist {
		err = errors.NewError(errors.ErrCodeCorpIDOrAgentIDErr)
		return
	}
	relRepo := qywx.NewTobeDevelopedAppRelRepo(tx)
	rel, _, err = relRepo.FirstTobeDevelopedAppRelByTobeDevelopedAppID(ctx, tobeDevelopedApp.ID)
	if err != nil {
		return
	}
	tenantManagementRepo := tenant_management.NewTenantManagementRepo()
	tenantManagement, err = tenantManagementRepo.MustFirst(ctx, tx, rel.TenantManagementID)
	if err != nil {
		return
	}
	// 设置租户id和租户 数据库到ctx中
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementId, strconv.FormatUint(tenantManagement.Id, 10))
	ctx = metadata.SetMDToIncoming(ctx, metadata.TenantManagementDbName, tenantManagement.DatabaseName)
	appClient := app_svc.GetAppClient(ctx)
	appReply, err = appClient.GetUserInfo(ctx, &app.GetUserInfoRequest{Code: req.Code})
	if err != nil {
		return
	}
	if appReply == nil {
		err = errors.NewError(errors.ErrCodeEmployeeUnbindQYWXUserErr)
		return
	}
	tenantDB = mysql_base.GetDBMap(tenantManagement.Id)
	if tenantDB == nil {
		tenantDB, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}
	tenantTx := &mysql_base.Tx{DB: tenantDB}
	employeeRepo := employee_repo.NewRepo(tenantTx)
	employeeList := make(employeeModel.EmployeeList, 0)
	employeeList, err = employeeRepo.GetEmployeeByQYWXUserIDs(ctx, []string{appReply.UserId})
	if err != nil {
		return
	}
	employee = employeeList.PickByQYWXUserID(appReply.UserId)
	userRepo := role.NewRepo(tenantTx)
	user, _, err = userRepo.GetUserByEmployeeID(ctx, employee.Id)
	tenantManagementID = rel.TenantManagementID
	return
}

// GetCustomerByFollowUserID 通过员工id获取企微客户列表
func (s *QYWXService) GetCustomerByFollowUserID(ctx context.Context, info metadata.IOperator, request structure.QYWXGetCustomersRequest) (resp structure.QYWXGetCustomersResponse, err error) {
	var (
		followUserID    string
		reply           = new(app.GetCustomersReply)
		qywxCustomerIDs []string
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	if !request.AllCustomers {
		employee := new(entity.Employee)
		employeeRepo := employee_repo.NewRepo(tx)
		employee, err = employeeRepo.GetEmployee(ctx, info.GetEmpId())
		if err != nil || employee == nil || employee.QYWXUserID == "" {
			return
		}
		followUserID = employee.QYWXUserID
		// zqx todo:权限控制
	}
	client := app_svc.GetAppClient(ctx)
	reply, err = client.GetCustomers(ctx, &app.GetCustomersRequest{
		Name:         request.Name,
		FollowUserId: followUserID,
		Page:         int64(request.Page),
		Size:         int64(request.Size),
	})
	if err != nil {
		return
	}
	resp.Total = reply.Total
	for _, customer := range reply.List {
		qywxCustomerIDs = append(qywxCustomerIDs, customer.Id)
	}
	var qywxCustomerRels = &app.GetBindCustomersReply{}
	qywxCustomerRels, _ = app_svc.GetAppClient(ctx).GetBindCustomers(ctx, &app.GetBindCustomersRequest{
		ExternalUserIds: qywxCustomerIDs,
	})
	for _, customer := range reply.List {
		var bound bool
		for _, bindCustomer := range qywxCustomerRels.BindCustomers {
			if bindCustomer.ExternalUserId == customer.Id {
				bound = true
			}
		}
		resp.List = append(resp.List, structure.QYWXCustomer{
			ID:           customer.Id,
			Name:         customer.Name,
			Avatar:       customer.Avatar,
			Type:         customer.Type,
			CorpName:     customer.CorpName,
			CorpFullName: customer.CorpFullName,
			Gender:       customer.Gender,
			Bound:        bound,
		})
	}
	return
}

// GetWechatFriends 通过员工id获取企微客户列表
func (s *QYWXService) GetWechatFriends(ctx context.Context, request structure.GetWechatFriendDataListQuery) (resp structure.WechatFriendDataList, total int, err error) {
	var (
		followUserID string
		reply        = new(app.GetCustomersWithDetailReply)
	)
	// tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	// defer func() {
	// 	err = commit(err, recover())
	// }()
	// todo:权限控制
	client := app_svc.GetAppClient(ctx)
	reply, err = client.GetCustomersWithDetail(ctx, &app.GetCustomersRequest{
		Name:         request.Name,
		FollowUserId: followUserID,
		Page:         int64(request.Page),
		Size:         int64(request.Size),
	})
	if err != nil {
		return
	}
	total = int(reply.Total)
	bizUnitSvc := biz_unit.NewClientBizUnitService()
	bizUnitMap, err := bizUnitSvc.QueryBizUnitListById(ctx, reply.BizUnitIds)
	if err != nil {
		return
	}
	for _, customer := range reply.List {
		bizUnit := bizUnitMap[customer.BizUnitId]
		resp = append(resp, structure.WechatFriendData{
			PurchaserClueId:       customer.CustomerId,
			ExternalUserId:        customer.ExternalUserId,
			Name:                  customer.Name,
			Avatar:                customer.Avatar,
			ExternalType:          common.ExternalType(customer.ExternalType),
			ExternalTypeName:      customer.ExternalTypeName,
			CorpName:              customer.CorpName,
			CorpFullName:          customer.CorpFullName,
			BizUnitId:             bizUnit.Id,
			BizUnitName:           bizUnit.Name,
			IsBind:                customer.IsBind,
			Gender:                customer.Gender,
			CorpWeChatFriendCount: int(customer.CorpWechatFriendCount),
			CorpGroupChatCount:    int(customer.CorpGroupChatCount),
			CorpWeChatFriendInfo: func() (list []structure.CorpWeChatFriendInfo) {
				for _, corpWeChatFriendInfo := range customer.CorpWechatFriendInfo {
					list = append(list, structure.CorpWeChatFriendInfo{
						AddCreateTime: corpWeChatFriendInfo.AddCreateTime,
						Name:          corpWeChatFriendInfo.Name,
					})
				}
				return
			}(),
			CorpGroupChatInfo: func() (list []structure.CorpGroupChatInfo) {
				for _, corpGroupChatInfo := range customer.CorpGroupChatInfo {
					list = append(list, structure.CorpGroupChatInfo{
						GroupChatName: corpGroupChatInfo.GroupChatName,
						JoinTime:      corpGroupChatInfo.JoinTime,
					})
				}
				return
			}(),
		})
	}
	return
}

// GetGroupChatList 获取群聊列表
func (s *QYWXService) GetGroupChatList(ctx context.Context, info metadata.IOperator, req structure.QYWXGetGroupChatListRequest) (resp structure.QYWXGetGroupChatListResponse, err error) {
	var (
		reply         = new(app.GetGroupChatListReply)
		groupChatRels qywxModel.QYWXGroupChatRelList
		groupChatIDs  []string
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	client := app_svc.GetAppClient(ctx)
	reply, err = client.GetGroupChatList(ctx, &app.GetGroupChatListRequest{
		Name: req.Name,
		Page: int64(req.Page),
		Size: int64(req.Size),
	})
	if err != nil {
		return
	}
	resp.Total = reply.Total
	groupChatRepo := qywx.NewQYWXGroupChatRelRepo(tx)
	for _, groupChat := range reply.List {
		groupChatIDs = append(groupChatIDs, groupChat.ChatId)
	}
	groupChatRels, err = groupChatRepo.GetQYWXGroupChatRelByQYWXGroupChatIDs(groupChatIDs)
	if err != nil {
		return
	}
	for _, groupChat := range reply.List {
		resp.List = append(resp.List, structure.QYWXGroupChat{
			ID:         groupChat.ChatId,
			Name:       groupChat.Name,
			Owner:      groupChat.Owner,
			CreateTime: tools.QueryTime(groupChat.CreateTime),
			Bound:      groupChatRels.PickByGroupChatID(groupChat.ChatId).ID != 0,
		})
	}
	return
}

// GetPurchaserClueCorpTagGroupDataList 获取标签分组列表
func (s *QYWXService) GetPurchaserClueCorpTagGroupDataList(ctx context.Context, req structure.GetCorpTagGroupListQuery) (resp structure.GetCorpTagGroupDataList, total int, err error) {
	var (
		reply = new(corp_tag.ListCorpTagGroupReply)
	)
	reply, err = corp_tag_svc.GetCorpTagClient(ctx).GetCorpTagGroupDataList(ctx, &corp_tag.ListCorpTagRequest{
		Page:           uint32(req.Page),
		Size:           uint32(req.Size),
		ExternalUserId: req.ExternalUserID,
		FollowUserId:   req.FollowUserID,
	})
	if err != nil {
		return
	}
	total = int(reply.Total)
	for _, tagGroup := range reply.TagGroups {
		resp = append(resp, structure.GetCorpTagGroupData{
			ID:      tagGroup.Id,
			Name:    tagGroup.Name,
			GroupID: tagGroup.GroupId,
			CorpTags: func() (list []structure.Tag) {
				for _, tag := range tagGroup.TagInfos {
					list = append(list, structure.Tag{
						ID:       tag.Id,
						TagID:    tag.TagId,
						TagName:  tag.TagName,
						IsSelect: tag.IsSelected,
					})
				}
				return
			}(),
		})
	}
	return
}

// AddGroupChat 添加群聊
func (s *QYWXService) AddGroupChat(ctx context.Context, info metadata.IOperator, req structure.QYWXAddGroupChatRequest) (resp system.ResponseData, err error) {
	type sendRawMessageData struct {
		Code    int    `json:"code"`
		Message string `json:"message"`
		Data    string `json:"data"`
	}
	data := sendRawMessageData{}
	err = tools.PostJsonReturnJson(fmt.Sprintf("https://api.worktool.ymdyes.cn/wework/sendRawMessage?robotId=%s", info.GetTobeDevelopedAppRobotCode()),
		nil, map[string]interface{}{"socketType": 2, "list": []struct {
			Type      int    `json:"type"`
			GroupName string `json:"groupName"`
		}{{Type: 206, GroupName: req.Name}}}, &data)
	if err != nil {
		return
	}
	if data.Code != 200 {
		err = errors2.New(data.Message)
		return
	}
	return
}

// BotSendImageMsg 阿布机器人发送图片消息
func (s *QYWXService) BotSendImageMsg(ctx context.Context, info metadata.IOperator, req structure.BotSendMsgRequest) (data system.ResponseData, err error) {
	err = bot.BotSendTypeSendImageMsg(info.GetTobeDevelopedAppRobotCode(), req.ChatName, "", req.FileUrl)
	if err != nil {
		return
	}
	return
}

// BotSendTextMsg 阿布机器人发送文本消息
func (s *QYWXService) BotSendTextMsg(ctx context.Context, info metadata.IOperator, req structure.BotSendMsgRequest) (data system.ResponseData, err error) {
	err = bot.BotSendTypeSendTextMsg(info.GetTobeDevelopedAppRobotCode(), req.ChatName, req.Content)
	if err != nil {
		return
	}
	return
}

// GetQYWXCustomerBindRel 获取企业微信客户绑定关系
func (s *QYWXService) GetQYWXCustomerBindRel(ctx context.Context, req structure.QYWXCustomerBindRelRequest) (resp structure.QYWXCustomerBindRelResponse, err error) {
	var (
		bizUnit           = new(bizUnit_entity.BizUnit)
		groupChatRel      qywxModel.QYWXGroupChatRel
		groupChatRelExist bool
		bizUnitId         uint64
	)
	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var qywxCustomerRel = &app.GetBindCustomerReply{}
	qywxCustomerRel, _ = app_svc.GetAppClient(ctx).GetBindCustomer(ctx, &app.GetBindCustomerRequest{
		ExternalUserId: req.QYWXCustomerID,
	})
	bizUnitId = qywxCustomerRel.BizUnitId
	groupChatRelRepo := qywx.NewQYWXGroupChatRelRepo(tx)
	if qywxCustomerRel.ExternalUserId == "" {
		groupChatRel, groupChatRelExist, err = groupChatRelRepo.FirstQYWXGroupChatRelByQYWXGroupChatID(req.QYWXGroupChatID)
		if err != nil {
			return
		}
		bizUnitId = groupChatRel.BizUnitID
		if !groupChatRelExist {
			err = log.ErrorLog(errors.NewError(errors.ErrCodeQYWXBindRelNotExistErr))
			return
		}
	}
	bizUnitRepo := mysqlBizUnit.NewRepo(tx)
	bizUnit, err = bizUnitRepo.GetBizUnit(ctx, bizUnitId)
	if err != nil {
		return
	}
	resp.CustomerID = bizUnit.Id
	resp.CustomerName = bizUnit.Name
	return
}

// GetQYWXSignature 获取企业微信签名
func (s *QYWXService) GetQYWXSignature(ctx context.Context, info metadata.IOperator, req structure.QYWXSignatureRequest) (resp structure.QYWXSignatureResponse, err error) {
	var (
		reply = new(app.GetSignatureReply)
	)
	reply, err = app_svc.GetAppClient(ctx).GetSignature(ctx, &app.GetSignatureRequest{
		NonceStr:  req.NonceStr,
		Timestamp: req.Timestamp,
		Url:       req.Url,
	})
	if err != nil {
		return
	}

	resp.CorpSignature = reply.CorpSignature
	resp.AppSignature = reply.AppSignature
	return
}
