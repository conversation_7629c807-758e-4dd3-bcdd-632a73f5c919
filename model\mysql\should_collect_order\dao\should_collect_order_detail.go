package dao

import (
	"hcscm/common/product"
	"hcscm/common/sale"
	"hcscm/common/should_collect_order"
	common "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	mysql "hcscm/model/mysql/should_collect_order"
	structure "hcscm/structure/should_collect_order"
)

func MustCreateShouldCollectOrderDetail(tx *mysql_base.Tx, r mysql.ShouldCollectOrderDetail) (o mysql.ShouldCollectOrderDetail, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateShouldCollectOrderDetail(tx *mysql_base.Tx, r mysql.ShouldCollectOrderDetail) (o mysql.ShouldCollectOrderDetail, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func MustDeleteShouldCollectOrderDetail(tx *mysql_base.Tx, r mysql.ShouldCollectOrderDetail) (err error) {
	err = mysql_base.MustDeleteModel(tx, &r)
	return
}

func MustFirstShouldCollectOrderDetailByID(tx *mysql_base.Tx, id uint64) (r mysql.ShouldCollectOrderDetail, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	err = mysql_base.MustFirst(tx, &r, id, cond)

	return
}

func FirstShouldCollectOrderDetailByID(tx *mysql_base.Tx, id uint64) (r mysql.ShouldCollectOrderDetail, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
	)
	exist, err = mysql_base.First(tx, &r, id, cond)

	return
}

func FindShouldCollectOrderDetailByShouldCollectOrderDetailID(tx *mysql_base.Tx, objects ...interface{}) (o mysql.ShouldCollectOrderDetailList, err error) {
	ids := mysql.GetShouldCollectOrderDetailIdList(objects)
	var (
		r    mysql.ShouldCollectOrderDetail
		cond = mysql_base.NewCondition()
		list []mysql.ShouldCollectOrderDetail
	)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindShouldCollectOrderDetailByIDs(tx *mysql_base.Tx, ids []uint64) (o mysql.ShouldCollectOrderDetailList, err error) {
	var (
		r    mysql.ShouldCollectOrderDetail
		cond = mysql_base.NewCondition()
		list []mysql.ShouldCollectOrderDetail
	)
	r.BuildReadCond(tx.Context, cond)

	err = mysql_base.Find(tx, &r, ids, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据上级id获取
func FindShouldCollectOrderDetailByParentID(tx *mysql_base.Tx, pid uint64) (o mysql.ShouldCollectOrderDetailList, err error) {
	var (
		r    mysql.ShouldCollectOrderDetail
		cond = mysql_base.NewCondition()
		list []mysql.ShouldCollectOrderDetail
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableEqual(r, "should_collect_order_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func FindShouldCollectOrderDetailByParentIDs(tx *mysql_base.Tx, pid []uint64) (o mysql.ShouldCollectOrderDetailList, err error) {
	var (
		r    mysql.ShouldCollectOrderDetail
		cond = mysql_base.NewCondition()
		list []mysql.ShouldCollectOrderDetail
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableContainMatch(r, "should_collect_order_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 查找合并单据的合并详情
func FindShouldCollectOrderDetailByMergeOrderIDs(tx *mysql_base.Tx, pid []uint64) (o mysql.ShouldCollectOrderDetailList, err error) {
	var (
		r    mysql.ShouldCollectOrderDetail
		cond = mysql_base.NewCondition()
		list []mysql.ShouldCollectOrderDetail
	)
	r.BuildReadCond(tx.Context, cond)

	cond.AddTableContainMatch(r, "merge_order_id", pid)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

// 根据配布单分录行ids查找
func FindShouldCollectOrderDetailByArrangeOrderIds(tx *mysql_base.Tx, arrangeOrderItemId []uint64) (o mysql.ShouldCollectOrderDetailList, err error) {
	var (
		r     mysql.ShouldCollectOrderDetail
		order mysql.ShouldCollectOrder
		cond  = mysql_base.NewCondition()
		list  []mysql.ShouldCollectOrderDetail
	)
	r.BuildReadCond(tx.Context, cond)

	// 找出非作废的那些分录行
	cond.AddTableLeftJoiner(r, order, "should_collect_order_id", "id")
	cond.AddTableContainMatch(r, "src_detail_id", arrangeOrderItemId)
	cond.AddTableNotEqual(order, "audit_status", common.OrderStatusVoided)

	err = mysql_base.SearchListGroup(tx, &r, &list, cond)
	if err != nil {
		return
	}
	o = list
	return
}

func SearchShouldCollectOrderDetail(tx *mysql_base.Tx, q *structure.GetShouldCollectOrderDetailListQuery) (o mysql.ShouldCollectOrderDetailList, count int, err error) {
	var (
		detailModel mysql.ShouldCollectOrderDetail
		orderModel  mysql.ShouldCollectOrder
		cond        = mysql_base.NewCondition()
		list        []mysql.ShouldCollectOrderDetail
	)
	var groupField []string
	// 创建联表查询
	detailModel.BuildReadCond(tx.Context, cond)
	cond.AddTableLeftJoiner(&detailModel, &orderModel, "should_collect_order_id", "id")

	// 条件过滤：客户ID
	if q.CustomerId != 0 {
		cond.AddTableEqual(&orderModel, "customer_id", q.CustomerId)
	}

	// 条件过滤：销售系统ID
	if q.SaleSystemId != 0 {
		cond.AddTableEqual(&orderModel, "sale_system_id", q.SaleSystemId)
	}

	// 条件过滤：销售员ID
	if q.SaleUserId != 0 {
		cond.AddTableEqual(&orderModel, "sale_user_id", q.SaleUserId)
	}

	// 条件过滤：订单时间范围
	if !q.StartOrderTime.IsYMDZero() && !q.EndOrderTime.IsYMDZero() {
		cond.AddTableBetween(&orderModel, "order_time", q.StartOrderTime.StringYMD(), q.EndOrderTime.StringYMD2DayListTimeYMDHMS())
	}
	// 条件过滤：销售模式
	if !q.SaleMode.IsNil() {
		cond.AddTableContainMatch(&orderModel, "sale_mode", q.SaleMode.ToUint64())
	}
	// 条件过滤：审核状态
	if !q.AuditStatus.IsNil() {
		cond.AddTableContainMatch(&orderModel, "audit_status", q.AuditStatus.ToUint64())
	}
	cond.AddTableContainMatch(&orderModel, "audit_status", []product.AuditStatus{product.AuditStatusPass, product.AuditStatusWaiting, product.AuditStatusReject})
	cond.AddTableContainMatch(&orderModel, "collect_type", []should_collect_order.CollectType{should_collect_order.CollectTypeProductSale, should_collect_order.CollectTypeProductReturn})
	cond.AddTableContainMatch(&orderModel, "sale_mode", []sale.SaleOrderType{0, sale.BulkTypeOrder, sale.ShearPlateTypeOrder, sale.CustomerBulkTypeOrder, sale.CustomerShearPlateTypeOrder})
	// 执行分页查询
	count, err = mysql_base.SearchListGroupForPaging(tx, &detailModel, q, &list, cond, groupField...)
	if err != nil {
		return
	}

	o = list
	return
}
