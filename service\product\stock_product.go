/*
传入需要入库的信息，增加库存（根据资料添加）
相同数据判断条件:所属仓库，所属客户，颜色id，成品等级id，库存备注(进仓单备注)
Id Weight++		Length++	StockRoll++
传入需要出库的库存信息，减少库存（根据库存信息添加）
Id Weight--		Length--	StockRoll--
传入需要预约的库存信息，占用库存（配布单占用）
Id BookRoll
*/
package product

import (
	"context"
	"fmt"
	aggs "hcscm/aggs/product"
	"hcscm/common/errors"
	common "hcscm/common/product"
	product2 "hcscm/extern/pb/basic_data/product"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	model "hcscm/model/mysql/product"
	"hcscm/model/redis"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/tools/set"
)

type IStockProductService interface {
	StockProductLock(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, addReq structure.AddStockProductDetailParamList, updateReq structure.UpdateStockProductDetailParamList, sumBookMap ...uint64) (locks redis.LockForRedisList, err error)
	GetMaxVolumeNumber(ctx context.Context, dyelotNumber string, productColorId uint64) (volumeNumber int, err error)
	AddBookProductStock(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req *structure.UpdateStockProductDetailParam) (locks redis.LockForRedisList, err error)
	UpdateDetailStatusArrange(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req []uint64) (locks redis.LockForRedisList, stockProductIds []uint64, err error)
	UpdateDetailStatusArrangeStatusWarehouseIn(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req []uint64) (locks redis.LockForRedisList, stockProductIds []uint64, err error)
	AddDetail(ctx context.Context, tx *mysql_base.Tx, req *structure.AddStockProductDetailParam) (id uint64, err error)
	AddDetails(ctx context.Context, tx *mysql_base.Tx, req structure.AddStockProductDetailParamList) (ids map[uint64]uint64, sumStockIds map[uint64]uint64, err error)
	UpdateDetail(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateStockProductDetailParam) (id uint64, err error)
	UpdateDetails(ctx context.Context, tx *mysql_base.Tx, req structure.UpdateStockProductDetailParamList) (ids []uint64, err error)
	UpdateWarehouseBinMove(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req *structure.UpdateWarehouseBinMoveParam) (data structure.UpdateWarehouseBinMoveData, locks redis.LockForRedisList, err error)
	UpdateDetailQCStatus(ctx context.Context, tx *mysql_base.Tx, id uint64, qcStatus common.QualityCheckStatus) (err error)
	Get(ctx context.Context, stockProductId uint64) (data structure.GetStockProductData, err error)
	GetDetailByCond(ctx context.Context, req *structure.GetStockProductDetailListQuery) (data structure.GetStockProductDetailDropdownData, err error)
	GetList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDataList, total int, err error)
	GetDetail(ctx context.Context, req *structure.GetStockProductDetailQuery) (data structure.GetStockProductDetailData, err error)
	GetDetailList(ctx context.Context, req *structure.GetStockProductDetailListQuery) (list structure.GetStockProductDetailDataList, total int, err error)
	GetDyelotNumberDetailList(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error)
	GetDropdownList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDropdownDataList, total int, err error)
	GetStockProductDyelotNumberList(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error)
	GetWeightDetailList(ctx context.Context, req *structure.GetStockProductWeightDetailListQuery) (list structure.GetStockProductDetailWeightDataList, total int, err error)
	GetDetailDropdownList(ctx context.Context, req *structure.GetStockProductDetailListQuery) (list structure.GetStockProductDetailDropdownDataList, total int, err error)
	GetSumStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, count int, err error)
	GetDyeNumberAndColorStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, count int, err error)
	GetDetailStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, count int, err error)
	GetOcrList(ctx context.Context, req *structure.GetYBStockProductListQuery) (list structure.GetStockProductDataList, total int, err error)
}

func NewStockProductService() *StockProductService {
	return &StockProductService{
		// rLocks:                rLocks,
		stockProduct:          make(map[uint64]model.StockProduct),
		stockProductDetail:    make(map[uint64]model.StockProductDetail),
		stockProductReq:       set.NewConcurrentMap[string, uint64](),
		stockProductDetailReq: set.NewConcurrentMap[string, uint64](),
	}
}

type StockProductService struct {
	rLocks                redis.LockForRedisList
	stockProduct          map[uint64]model.StockProduct
	stockProductDetail    map[uint64]model.StockProductDetail
	stockProductReq       *set.ConcurrentMap[string, uint64] // 上锁使用
	stockProductDetailReq *set.ConcurrentMap[string, uint64] // 上锁使用
}

// ResetReq 重设上锁参数
// 用途：多张单审核使用同一个StockProductService时，需要在每张单操作前进行重设
func (u *StockProductService) ResetReq() {
	u.stockProductReq = set.NewConcurrentMap[string, uint64]()
	u.stockProductDetailReq = set.NewConcurrentMap[string, uint64]()
}

// 批量移架操作（支持单匹）
func (u *StockProductService) UpdateWarehouseBinMove(
	ctx context.Context,
	tx *mysql_base.Tx,
	rLocks redis.LockForRedisList,
	req *structure.UpdateWarehouseBinMoveParam,
) (
	data structure.UpdateWarehouseBinMoveData,
	locks redis.LockForRedisList,
	err error,
) {
	locks = rLocks
	var (
		codeReq                = &structure.GetStockProductDetailListQuery{}
		stockDetail            = structure.GetStockProductDetailDropdownData{}
		updateItems            = structure.UpdateStockProductDetailParamList{}
		orderRoll, orderWeight int
	)
	codeReq.QrCode = req.QrCode
	// 目前仅用于条码和二维码查询（唯一值）
	err = codeReq.AdjustQrAndBarCode()
	if err != nil {
		return
	}
	stockDetail, err = u.GetDetailByCond(ctx, tx, codeReq)
	if err != nil {
		return
	}

	if req.Type == common.MoveTypeDye {
		// 本架整缸 todo:

	} else if req.Type == common.MoveTypeBin {
		// 整架 todo:

	} else {
		// 单匹
		var updateItem = &structure.UpdateStockProductDetailParam{}
		updateItem.StockProductId = stockDetail.StockProductId
		updateItem.Id = stockDetail.Id
		updateItem.WarehouseBinId = stockDetail.WarehouseBinId
		updateItem.TargetWarehouseBinId = req.TargetBinId
		updateItem.WarehouseId = stockDetail.WarehouseId
		updateItem.ProductId = stockDetail.ProductId
		updateItem.ProductColorId = stockDetail.ProductColorId
		updateItem.DyelotNumber = stockDetail.DyelotNumber
		updateItem.VolumeNumber = stockDetail.VolumeNumber
		updateItem.Roll = stockDetail.StockRoll
		updateItem.Weight = stockDetail.Weight
		updateItem.Type = 3
		orderRoll += stockDetail.StockRoll
		orderWeight += stockDetail.Weight
		updateItems = append(updateItems, updateItem)
	}

	// 库存上锁
	locks, err = u.StockProductLock(ctx, tx, locks, nil, updateItems)
	if err != nil {
		return
	}
	// 更新库存仓位
	_, err = u.UpdateDetails(ctx, tx, updateItems)
	if err != nil {
		return
	}

	// 移架日志
	warehouseBinMoveRepo := aggs.NewWarehouseBinMoveLogRepo(tx)
	err = warehouseBinMoveRepo.Add(ctx, req, updateItems)
	if err != nil {
		return
	}

	data.ProductId = stockDetail.ProductId
	data.ProductCode = stockDetail.ProductCode
	data.ProductName = stockDetail.ProductName
	data.Roll = stockDetail.Roll
	data.Weight = stockDetail.Weight
	data.ProductColorCode = stockDetail.ProductColorCode
	data.ProductColorName = stockDetail.ProductColorName
	data.DyelotNumber = stockDetail.DyelotNumber
	data.VolumeNumber = stockDetail.VolumeNumber
	data.OrderRoll = orderRoll
	data.OrderWeight = orderWeight
	// data.WarehouseBinRoll = stockDetail.WarehouseBinRoll
	// data.ColorRoll = stockDetail.ColorRoll
	// data.DyeRoll = stockDetail.DyeRoll
	return
}

// 汇总
// 预约库存(参数为正数)
func (u *StockProductService) AddBookProductStock(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req *structure.UpdateStockProductDetailParam) (locks redis.LockForRedisList, err error) {
	locks = rLocks
	var (
		ok bool
		// stockProductId uint64
	)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	// logRepo := aggs.NewStockProductBookLogRepo(tx)

	req.StockProductKey = fmt.Sprintf("%v", req.StockProductId)
	_, ok = u.stockProductReq.Get(req.StockProductKey)
	if !ok {
		var (
			rLock *redis.LockForRedis
		)
		// rLock, err = redis.ModifyStock(ctx, 0, 0, 0, 0, "", stockProductKey)
		rLock, err = redis.ModifyStockID(ctx, req.StockProductId, "")
		if err != nil {
			return
		}
		locks = append(locks, rLock)

		u.stockProductReq.Set(req.StockProductKey, req.StockProductId)
	}
	_, err = repo.AddOrUpdate(ctx, req.ToAddStockProductParam())
	if err != nil {
		return
	}
	// _, err = logRepo.Add(ctx, req.ToAddStockProductBookLog(ctx))
	if err != nil {
		return
	}
	return
}

// 详情
// 配布提交占用库存(返回汇总库存stockProductIds)
func (u *StockProductService) UpdateDetailStatusArrange(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req []uint64, isBook bool) (locks redis.LockForRedisList, stockProductIds []uint64, err error) {
	locks = rLocks
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	var (
		rLock *redis.LockForRedis
	)
	for _, id := range req {
		// 详情上锁
		rLock, err = redis.ModifyDetailStock(ctx, id, "")
		if err != nil {
			return
		}
		locks = append(locks, rLock)
	}
	if isBook {
		stockProductIds, err = repo.UpdateDetailStatusArrange(ctx, req)
		if err != nil {
			return
		}
	} else {
		stockProductIds, err = repo.UpdateDetailStatusRelease(ctx, req)
		if err != nil {
			return
		}
	}
	return
}

// 新增单条库存详情
func (u *StockProductService) AddDetail(ctx context.Context, tx *mysql_base.Tx, req *structure.AddStockProductDetailParam) (id uint64, err error) {
	var (
		stockProductId uint64
		ok             bool
	)

	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	// 查询汇总库存是否存在，不存在则创建
	req.StockProductKey = fmt.Sprintf("%v%v%v%v%v%v", req.WarehouseId, req.CustomerId, req.ProductColorId, req.ProductLevelId, req.ProductRemark, req.Remark)
	stockProductId, ok = u.stockProductReq.Get(req.StockProductKey)
	if !ok {
		stockProductId, _, err = repo.Exist(ctx, req.WarehouseId, req.CustomerId, req.ProductColorId, req.ProductLevelId, req.Remark)
		if err != nil {
			return
		}
	}
	u.stockProductReq.Set(req.StockProductKey, stockProductId)

	stockProductId, err = repo.AddOrUpdate(ctx, req.ToAddStockProductParam(stockProductId))
	if err != nil {
		return
	}

	id, err = repo.AddDetail(ctx, req, stockProductId)
	if err != nil {
		return
	}

	// err = repo.DelVolumeNumber(ctx, req.DyelotNumber, req.ProductColorId)
	// if err != nil {
	// 	return
	// }
	return
}

// 新增多条条库存详情(增加库存、占用库存传入参数为正数)
func (u *StockProductService) AddDetails(ctx context.Context, tx *mysql_base.Tx, req structure.AddStockProductDetailParamList) (ids map[uint64]uint64, sumStockIds map[uint64]uint64, err error) {
	var (
		repo           = aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
		stockProductId uint64
		id             uint64
		idMap          = make(map[uint64]uint64)
		sumStockIdMap  = make(map[uint64]uint64)
		ok             bool
		dyeColorVol    = make(map[string]int)
		// dyeColorMaxVol = make(map[string]int)
	)
	// todo: 并发处理待优化
	// err = errgroup.Finish(ctx, 0, func(ctx context.Context) error {
	// 检查相同缸号相同颜色的布卷号是否重复
	for _, item := range req {
		value := fmt.Sprintf("%v,%v,%v", item.DyelotNumber, item.ProductColorId, item.VolumeNumber)
		// maxValue := fmt.Sprintf("%v,%v", item.DyelotNumber, item.ProductColorId)
		if _, ok := dyeColorVol[value]; ok {
			// 补充成品种类id
			productItem, _ := product2.NewProductClient().GetProductById(ctx, item.ProductId)
			productColorItem, _ := product2.NewProductColorClient().GetProductColorItemById(ctx, item.ProductColorId)
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, fmt.Sprintf("卷号重复:%v-%v,缸号%v卷号%v", productItem[item.ProductId][0], productColorItem[item.ProductColorId][0], item.DyelotNumber, item.VolumeNumber)))
			return
		} else {
			dyeColorVol[value] = item.VolumeNumber
		}
		// maxVol := dyeColorMaxVol[maxValue]
		// if maxVol < item.VolumeNumber {
		// 	dyeColorMaxVol[maxValue] = item.VolumeNumber
		// }

		// item.StockProductKey = fmt.Sprintf("%v%v%v%v%v%v", item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId,item.ProductRemark item.Remark)
	}
	// return nil
	// },
	// 	func(ctx context.Context) error {
	for _, item := range req {
		// 上锁时创建了汇总库存，如果找不到则是错误
		stockProductId, ok = u.stockProductReq.Get(fmt.Sprintf("%v%v%v%v%v%v", item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId, item.ProductRemark, item.Remark))
		if !ok {
			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "创建汇总库存失败导致找不到汇总库存id"))
			return
		}
		stockProductId, err = repo.AddOrUpdate(ctx, item.ToAddStockProductParam(stockProductId))
		if err != nil {
			return
		}
		sumStockIdMap[item.AddWeightItemId] = stockProductId

		// 如果传进来的某些库存特征的信息不为空，则需要查询该仓库是否存在一样的库存，如果存在，则进行更新操作，不执行新增操作；如果不存在，则进行新增操作
		if item.QrCode != "" {
			var exist bool
			id, exist, err = repo.CheckStockProductDetailAndUpdate(ctx, item)
			if err != nil {
				return
			}
			if exist {
				idMap[item.AddWeightItemId] = id
				continue
			}
		}
		id, err = repo.AddDetail(ctx, item, stockProductId)
		if err != nil {
			return
		}
		idMap[item.AddWeightItemId] = id
	}
	// return nil
	// },
	// func(ctx context.Context) error {
	// 	for _, item := range req[250:] {
	// 		// 上锁时创建了汇总库存，如果找不到则是错误
	// 		stockProductId, ok = u.stockProductReq.Get(fmt.Sprintf("%v%v%v%v%v%v", item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId,item.ProductRemark item.Remark))
	// 		if !ok {
	// 			err = middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "创建汇总库存失败导致找不到汇总库存id"))
	// 			return err
	// 		}
	// 		stockProductId, err = repo.AddOrUpdate(ctx, item.ToAddStockProductParam(stockProductId))
	// 		if err != nil {
	// 			return err
	// 		}
	// 		sumStockIdMap[item.AddWeightItemId] = stockProductId
	// 		id, err = repo.AddDetail(ctx, item, stockProductId)
	// 		if err != nil {
	// 			return err
	// 		}
	// 		idMap[item.AddWeightItemId] = id
	// 	}
	// 	return nil
	// },
	// )

	// 如果是本次进仓的最大卷号，则更新缓存最大值(卷号获取缓存使用)
	// for maxValue := range dyeColorMaxVol {
	// 	var (
	// 		dyelotNumber   string
	// 		productColorId uint64
	// 	)
	// 	split := strings.Split(maxValue, ",")
	// 	if len(split) == 2 {
	// 		dyelotNumber = split[0]
	// 		productColorId, _ = tools.String2UInt64(split[1])
	// 	}
	// 	err = repo.DelVolumeNumber(ctx, dyelotNumber, productColorId)
	// 	if err != nil {
	// 		return
	// 	}
	// }

	ids = idMap
	sumStockIds = sumStockIdMap
	return
}

// FpmCostPricePassUpdate 用于坯布调拨或者调整审核通过时，变更库存成本价
func (u StockProductService) FpmCostPricePassUpdate(ctx context.Context, tx *mysql_base.Tx, req structure.AddStockProductDetailParamList) (err error) {
	var (
		outStockProductDetailList model.StockProductDetailList
		fpmCostPriceList          structure.GetFpmCostPriceDataList
	)
	outStockProductDetailIds := make([]uint64, 0, len(req))
	for _, item := range req {
		outStockProductDetailIds = append(outStockProductDetailIds, item.OutStockProductDetailId)
	}
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	outStockProductDetailList, err = repo.GetDetailsByIds(ctx, outStockProductDetailIds)
	if err != nil {
		return
	}
	productIds := make([]uint64, 0, len(outStockProductDetailList))
	productColorIds := make([]uint64, 0, len(outStockProductDetailList))
	dyelotNumbers := make([]string, 0, len(outStockProductDetailList))
	WarehouseGoodInIds := make([]uint64, 0, len(outStockProductDetailList))
	warehouseInOrderTypes := make([]common.WarehouseGoodInType, 0, len(outStockProductDetailList))
	for _, item := range outStockProductDetailList {
		productIds = append(productIds, item.ProductId)
		productColorIds = append(productColorIds, item.ProductColorId)
		dyelotNumbers = append(dyelotNumbers, item.DyelotNumber)
		warehouseInOrderTypes = append(warehouseInOrderTypes, item.WarehouseInType)
		if item.WarehouseInType != common.WarehouseGoodInTypeProcess {
			WarehouseGoodInIds = append(WarehouseGoodInIds, item.WarehouseInOrderId)
		} else {
			WarehouseGoodInIds = append(WarehouseGoodInIds, 0)
		}
	}
	fpmCostPriceRepo := aggs.NewFpmCostPriceRepo(ctx, tx, false)
	fpmCostPriceList, _, err = fpmCostPriceRepo.GetList(ctx, &structure.GetFpmCostPriceListQuery{ProductIds: productIds, ColourIds: productColorIds,
		DyelotNumbers: dyelotNumbers, WarehouseGoodInIds: WarehouseGoodInIds, WarehouseGoodInTypes: warehouseInOrderTypes})
	if err != nil {
		return
	}
	for _, item := range req {
		var (
			exist             bool
			warehouseGoodInId uint64
		)
		outStockProductDetail := outStockProductDetailList.Pick(item.OutStockProductDetailId)
		if outStockProductDetail.WarehouseInType != common.WarehouseGoodInTypeProcess {
			warehouseGoodInId = outStockProductDetail.WarehouseInOrderId
		} else {
			warehouseGoodInId = 0
		}
		outFpmCostPrice := fpmCostPriceList.Pick(outStockProductDetail.ProductId, outStockProductDetail.ProductColorId,
			warehouseGoodInId, outStockProductDetail.WarehouseInType, outStockProductDetail.DyelotNumber)
		_, exist, err = fpmCostPriceRepo.GetV2(ctx, item.ProductId, item.ProductColorId, item.WarehouseInOrderId,
			item.DyelotNumber, item.WarehouseInType)
		if !exist {
			var srcId uint64
			if outFpmCostPrice.SrcId != 0 {
				srcId = outFpmCostPrice.SrcId
			} else {
				srcId = outFpmCostPrice.Id
			}
			_, err = fpmCostPriceRepo.Add(ctx, &structure.AddFpmCostPriceParam{
				CommonFpmCostPrice: structure.CommonFpmCostPrice{
					ProductId:           item.ProductId,
					ColourId:            item.ProductColorId,
					DyelotNumber:        item.DyelotNumber,
					WarehouseGoodInType: item.WarehouseInType,
					WarehouseGoodInId:   item.WarehouseInOrderId,
					SrcId:               srcId,
					NetWeightPrice:      outFpmCostPrice.NetWeightPrice,
					BuoyantWeightPrice:  outFpmCostPrice.BuoyantWeightPrice,
					Unit:                item.MeasurementUnitId,
				},
			})
			if err != nil {
				return
			}
		}
	}
	return
}

// FpmCostPriceWaitUpdate 用于坯布调拨或者调整审核通过时，变更库存成本价
func (u StockProductService) FpmCostPriceWaitUpdate(ctx context.Context, tx *mysql_base.Tx, req structure.UpdateStockProductDetailParamList) (err error) {
	var (
		stockProductDetailList model.StockProductDetailList
		fpmCostPriceList       structure.GetFpmCostPriceDataList
	)
	productIds := make([]uint64, 0, len(req))
	productColorIds := make([]uint64, 0, len(req))
	dyelotNumbers := make([]string, 0, len(stockProductDetailList))
	WarehouseGoodInIds := make([]uint64, 0, len(req))
	warehouseInOrderTypes := make([]common.WarehouseGoodInType, 0, len(req))
	stockProductDetailIds := make([]uint64, 0, len(req))
	for _, item := range req {
		stockProductDetailIds = append(stockProductDetailIds, item.Id)
	}
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	stockProductDetailList, err = repo.GetDetailsByIds(ctx, stockProductDetailIds)
	if err != nil {
		return
	}
	for _, item := range stockProductDetailList {
		// 这个三种进仓都有应付账控制成本，所以这里消审不用处理这些进仓类型
		if item.WarehouseInType != common.WarehouseGoodInTypeProcess && item.WarehouseInType != common.WarehouseGoodInTypePurchase &&
			item.WarehouseInType != common.WarehouseGoodInTypeOther {
			productIds = append(productIds, item.ProductId)
			productColorIds = append(productColorIds, item.ProductColorId)
			dyelotNumbers = append(dyelotNumbers, item.DyelotNumber)
			WarehouseGoodInIds = append(WarehouseGoodInIds, item.WarehouseInOrderId)
			warehouseInOrderTypes = append(warehouseInOrderTypes, item.WarehouseInType)
		}
	}
	fpmCostPriceRepo := aggs.NewFpmCostPriceRepo(ctx, tx, false)
	fpmCostPriceList, _, err = fpmCostPriceRepo.GetList(ctx, &structure.GetFpmCostPriceListQuery{ProductIds: productIds, ColourIds: productColorIds,
		DyelotNumbers: dyelotNumbers, WarehouseGoodInIds: WarehouseGoodInIds, WarehouseGoodInTypes: warehouseInOrderTypes})
	if err != nil {
		return
	}
	for _, item := range fpmCostPriceList {
		if item.Id != 0 {
			err = fpmCostPriceRepo.UpdateV2(ctx, &structure.UpdateFpmCostPriceParam{Id: item.Id})
			if err != nil {
				return
			}
		}
	}
	return
}

// 更新单条库存详情(出仓，出仓退货)
// 入参：来源类型 退货进仓单id 退货进仓单号 出仓单id 出仓单号 数量 长度 匹数 数字码 合同号 客户po号 客户款号 库存备注
func (u *StockProductService) UpdateDetail(ctx context.Context, tx *mysql_base.Tx, req *structure.UpdateStockProductDetailParam) (id uint64, err error) {
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	_, err = repo.AddOrUpdate(ctx, req.ToAddStockProductParam())
	if err != nil {
		return
	}
	id, err = repo.UpdateDetail(ctx, req)
	if err != nil {
		return
	}
	return
}

// QC quality check 更新质检状态
func (u *StockProductService) UpdateDetailQCStatus(ctx context.Context, tx *mysql_base.Tx, id uint64, qcStatus common.QualityCheckStatus) (err error) {
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)

	err = repo.UpdateDetailQCStatus(ctx, id, qcStatus)
	if err != nil {
		return
	}
	return
}

// 更新多条库存详情(出仓，盘点修改)(释放占用)
// 入参：出仓单id 出仓单号 数量 长度 匹数
func (u *StockProductService) UpdateDetails(ctx context.Context, tx *mysql_base.Tx, req structure.UpdateStockProductDetailParamList) (ids []uint64, err error) {
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	for _, detailParam := range req {
		var (
			id      uint64
			stockID uint64
		)
		stockID, err = repo.AddOrUpdate(ctx, detailParam.ToAddStockProductParam())
		if err != nil {
			return
		}
		detailParam.AdjustStockProductId = stockID
		// type为4只更新汇总库存
		if detailParam.Type != 4 {
			id, err = repo.UpdateDetail(ctx, detailParam)
			if err != nil {
				return
			}
			ids = append(ids, id)
		}
	}
	return
}

// 更新多条库存详情(出仓，盘点修改)(释放占用)
// 入参：出仓单id 出仓单号 数量 长度 匹数
func (u *StockProductService) UpdateDetailsV2(ctx context.Context, tx *mysql_base.Tx, req structure.UpdateStockProductDetailParamList,
) (ids map[uint64]uint64, sumStockIds map[uint64]uint64, err error) {
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	var (
		idMap         = make(map[uint64]uint64)
		sumStockIdMap = make(map[uint64]uint64)
	)
	for _, detailParam := range req {
		var (
			id      uint64
			stockID uint64
		)
		stockID, err = repo.AddOrUpdate(ctx, detailParam.ToAddStockProductParam())
		if err != nil {
			return
		}
		detailParam.AdjustStockProductId = stockID
		sumStockIdMap[detailParam.InOrderWeightItemId] = stockID
		// type为4只更新汇总库存
		if detailParam.Type != 4 {
			id, err = repo.UpdateDetail(ctx, detailParam)
			if err != nil {
				return
			}
			idMap[detailParam.InOrderWeightItemId] = id
			// ids = append(ids, id)
		}
	}
	sumStockIds = sumStockIdMap
	ids = idMap
	return
}

// 统一上锁
func (u *StockProductService) StockProductLock(
	ctx context.Context,
	tx *mysql_base.Tx,
	rLocks redis.LockForRedisList,
	addReq structure.AddStockProductDetailParamList,
	updateReq structure.UpdateStockProductDetailParamList,
	sumBookMap ...uint64,
) (
	locks redis.LockForRedisList,
	err error,
) {
	locks = rLocks
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)

	// todo: 并发处理待优化
	// err = errgroup.Finish(ctx, 0,
	// 	func(ctx context.Context) error {
	for _, item := range addReq {
		var (
			ok             bool
			exist          bool
			stockProductId uint64
		)
		// 汇总key
		item.StockProductKey = fmt.Sprintf("%v%v%v%v%v%v", item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId, item.ProductRemark, item.Remark)
		stockProductId, ok = u.stockProductReq.Get(item.StockProductKey)
		if !ok {
			stockProductId, exist, err = repo.Exist(ctx, item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId, item.Remark)
			if err != nil {
				return
			}
			if !exist {
				stockProductId, err = repo.AddOrUpdate(ctx, item.ToAddEmptyStockProductParam(stockProductId))
				if err != nil {
					return
				}
			}
			u.stockProductReq.Set(item.StockProductKey, stockProductId)
		}
	}
	// return nil
	// },
	// func(ctx context.Context) error {
	for _, item := range updateReq {
		var (
			ok             bool
			exist          bool
			stockProductId uint64
		)
		// 汇总key
		item.StockProductKey = fmt.Sprintf("%v%v%v%v%v%v", item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId, item.ProductRemark, item.Remark)
		stockProductId, ok = u.stockProductReq.Get(item.StockProductKey)
		if !ok {
			stockProductId, exist, err = repo.Exist(ctx, item.WarehouseId, item.CustomerId, item.ProductColorId, item.ProductLevelId, item.Remark)
			if err != nil {
				return
			}
			if !exist {
				stockProductId, err = repo.AddOrUpdate(ctx, item.ToAddEmptyStockProductParam(stockProductId))
				if err != nil {
					return
				}
			}
			u.stockProductReq.Set(item.StockProductKey, stockProductId)
		}
		// 详情key
		_, ok = u.stockProductDetailReq.Get(tools.UInt642String(item.Id))
		if !ok {
			u.stockProductDetailReq.Set(tools.UInt642String(item.Id), item.Id)
		}
	}
	// return nil
	// },
	// )

	err = u.stockProductReq.Range(
		func(key string, value uint64) error {
			var (
				_err  error
				rLock *redis.LockForRedis
			)
			rLock, _err = redis.ModifyStockID(ctx, value, key)
			if _err != nil {
				return _err
			}
			locks = append(locks, rLock)
			return nil
		})
	if err != nil {
		return
	}
	err = u.stockProductDetailReq.Range(
		func(key string, value uint64) error {
			var (
				_err  error
				rLock *redis.LockForRedis
			)
			stockProductDetailKey := fmt.Sprintf("%v%v", "Detail:", value)
			rLock, _err = redis.ModifyDetailStock(ctx, 0, stockProductDetailKey)
			if _err != nil {
				return _err
			}
			locks = append(locks, rLock)
			return nil
		})
	if err != nil {
		return
	}
	return
}

// 根据缸号和颜色id获取最大卷号
func (u *StockProductService) GetMaxVolumeNumber(ctx context.Context, dyelotNumber string, productColorId uint64) (volumeNumber int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	volumeNumber, err = repo.GetMaxVolumeNumber(ctx, dyelotNumber, productColorId, 0)
	if err != nil {
		return
	}
	return
}

// 获取成品库存汇总详情
func (u *StockProductService) Get(ctx context.Context, stockProductId uint64) (data structure.GetStockProductData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	data, err = repo.Get(ctx, stockProductId)
	if err != nil {
		return
	}
	return
}

// 获取成品库存详细库存详情
func (u *StockProductService) GetDetailByCond(ctx context.Context, tx *mysql_base.Tx, req *structure.GetStockProductDetailListQuery) (data structure.GetStockProductDetailDropdownData, err error) {
	tx, commit := mysql_base.TransactionMainEx(tx, ctx, true)
	defer func() {
		err = commit(err, recover())
	}()
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	data, err = repo.GetDetailByCond(ctx, req)
	if err != nil {
		return
	}
	return
}

// 获取成品库存汇总列表
func (u *StockProductService) GetList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取成品库存汇总下拉列表
func (u *StockProductService) GetDropdownList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取成品库存汇总下拉列表(小程序端)
func (u *StockProductService) MPGetDropdownList(ctx context.Context, req *structure.GetStockProductListQuery) (list structure.GetStockProductDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.MPGetDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取单条库存详情
func (u *StockProductService) GetDetail(ctx context.Context, req *structure.GetStockProductDetailQuery) (data structure.GetStockProductDetailData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	data, err = repo.GetDetail(ctx, req.Id, req.QrCode)
	if err != nil {
		return data, err
	}
	return
}

// 获取多条库存详情
func (u *StockProductService) GetDetailList(ctx context.Context, req *structure.GetStockProductDetailListQuery) (list structure.GetStockProductDetailDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetDetailList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取多条缸号汇总库存详情
func (u *StockProductService) GetDyelotNumberDetailList(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetDyelotNumberDetailList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取成品缸号库存分组列表
func (u *StockProductService) GetStockProductDyelotNumberList(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetStockProductDyelotNumberList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取成品缸号库存分组列表
func (u *StockProductService) GetStockProductDyelotNumberListV2(ctx context.Context, req *structure.GetStockProductDyelotNumberDetailListQuery) (list structure.GetStockProductDetailDyelotNumberDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetStockProductDyelotNumberListV2(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取详情细码选择列表
func (u *StockProductService) GetWeightDetailList(ctx context.Context, req *structure.GetStockProductWeightDetailListQuery) (list structure.GetStockProductDetailWeightDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetWeightDetailList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取多条库存详情(下拉)
func (u *StockProductService) GetDetailDropdownList(ctx context.Context, req *structure.GetStockProductDetailListQuery) (list structure.GetStockProductDetailDropdownDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetDetailDropdownList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 详情
// 配布取消提交占用库存(返回汇总库存stockProductIds)（出仓单作废）
func (u *StockProductService) UpdateDetailStatusArrangeStatusWarehouseIn(ctx context.Context, tx *mysql_base.Tx, rLocks redis.LockForRedisList, req []uint64) (locks redis.LockForRedisList, stockProductIds []uint64, err error) {
	locks = rLocks
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	var (
		rLock *redis.LockForRedis
	)
	for _, id := range req {
		// 详情上锁
		rLock, err = redis.ModifyDetailStock(ctx, id, "")
		if err != nil {
			return
		}
		locks = append(locks, rLock)
	}
	stockProductIds, err = repo.UpdateDetailStatusArrangeStatusWarehouseIn(ctx, req)
	if err != nil {
		return
	}
	return
}

// 汇总库存-出入库详情
func (u *StockProductService) GetSumStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, count, err = repo.GetSumStockProductOutAndInDetailList(ctx, q)
	if err != nil {
		return
	}
	return
}

// 缸号库存-出入库详情
func (u *StockProductService) GetDyeNumberAndColorStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, count, err = repo.GetDyeNumberAndColorStockProductOutAndInDetailList(ctx, q)
	if err != nil {
		return
	}
	return
}

// 详细库存-出入库详情
func (u *StockProductService) GetDetailStockProductOutAndInDetailList(ctx context.Context, q *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, count int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, count, err = repo.GetDetailStockProductOutAndInDetailList(ctx, q)
	if err != nil {
		return
	}
	return
}

// 获取库存列表
func (u *StockProductService) GetYBList(ctx context.Context, req *structure.GetYBStockProductListQuery) (list structure.GetYBStockProductDataList, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetYBList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取色号详情列表
func (u *StockProductService) GetColorDetailList(ctx context.Context, req *structure.GetColorDetailQuery) (list structure.GetTotalData, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)

	// 获取产品 Id
	// productID, err := GetProductIdByCode(tx, req.ProductCode)
	// if err != nil {
	// 	return list, total, err
	// }

	// req.StockProductId = productID

	list, total, err = repo.GetColorDetailList(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 通过stock_id查询成品详细信息
func (u *StockProductService) GetStockProductDetailInfo(ctx context.Context, req *structure.GetStockProductDetailInfoQuery) (data structure.GetStockProductDetailInfoData, err error) {
	// 使用聚合层的方法获取成品详细信息
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	data, err = repo.GetStockProductDetailInfo(ctx, req)
	if err != nil {
		return
	}
	return
}

// 获取对应缸号的流水明细
func (u *StockProductService) GetDyelotDetail(ctx context.Context, req *structure.UnionOutAndInBaseListQuery) (list structure.GetStockProductOutAndInDetailDataListResp, total int, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	list, total, err = repo.GetDyelotDetail(ctx, req)
	if err != nil {
		return list, total, err
	}
	return
}

// 获取多条库存详情
func (u *StockProductService) AIGetStock(ctx context.Context, req structure.AIGetStockParam) (data structure.AIGetStockData, err error) {
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggs.NewStockProductRepo(ctx, tx, u.stockProduct, u.stockProductDetail, u.stockProductReq)
	data, err = repo.AIGetStock(ctx, req)
	if err != nil {
		return
	}
	return
}
