package grey_fabric_manage

import (
	"context"
	"fmt"
	"hcscm/aggs/gen_order_no"
	aggsRm "hcscm/aggs/raw_material"
	cus_error "hcscm/common/errors"
	cus_const "hcscm/common/grey_fabric_manage"
	stock_const "hcscm/common/stock"
	common_system "hcscm/common/system_consts"
	gfInfoPB "hcscm/extern/pb/basic_data/grey_fabric_info"
	base_info_pb "hcscm/extern/pb/basic_data/info_basic_data"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	produce_pb "hcscm/extern/pb/produce"
	saleSys "hcscm/extern/pb/sale_system"
	stock_pb "hcscm/extern/pb/stock"
	"hcscm/middleware"
	mysql "hcscm/model/mysql/grey_fabric_manage"
	"hcscm/model/mysql/mysql_base"
	mysqlSystem "hcscm/model/mysql/system"
	structure "hcscm/structure/grey_fabric_manage"
	produceStructure "hcscm/structure/produce"
	structureRm "hcscm/structure/raw_material"
	sys_structure "hcscm/structure/system"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"time"
)

type GfmProduceReceiveOrderRepo struct {
	tx *mysql_base.Tx
}

func NewGfmProduceReceiveOrderRepo(tx *mysql_base.Tx) *GfmProduceReceiveOrderRepo {
	return &GfmProduceReceiveOrderRepo{tx: tx}
}

func (r *GfmProduceReceiveOrderRepo) Add(ctx context.Context, req *structure.AddGfmProduceReceiveOrderParam) (data structure.AddGfmProduceReceiveOrderData, err error) {

	var (
		info               = metadata.GetLoginInfo(ctx)
		orderPrefix        mysqlSystem.OrderPrefix
		prefix, dateFormat string
		numLength          int
		exist              bool
		sale_sys_svc       = saleSys.NewSaleSystemClient()
		saleSysData        = saleSys.Res{}
		rmCostPriceList structureRm.GetRmCostPriceDataList
	)

	gfmProduceReceiveOrder := mysql.NewGfmProduceReceiveOrder(ctx, req)

	// 1. 给个默认的审核状态,初始化业务为开启
	gfmProduceReceiveOrder.AuditStatus = common_system.OrderStatusPendingAudit
	gfmProduceReceiveOrder.BusinessClose = common_system.BusinessCloseNo
	gfmProduceReceiveOrder.DepartmentId = info.GetDepartmentId()
	saleSysData, _ = sale_sys_svc.GetSaleSystemByQuery(ctx, saleSys.Req{Id: req.SaleSystemId})
	orderPrefix, exist, err = mysqlSystem.GetFirstOrderPrefix(r.tx)
	if err != nil {
		return
	}
	if !exist {
		prefix = vars.GfmProduceReceiveOrderPrefix
		numLength = vars.NumLength
		dateFormat = vars.DateFormat
		if vars.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	} else {
		prefix = orderPrefix.GfmProduceReceiveOrder
		numLength = orderPrefix.NumLength
		dateFormat = orderPrefix.DateFormat
		if orderPrefix.UseSaleSystem {
			prefix = fmt.Sprintf("%s-%s", prefix, saleSysData.Code)
		}
	}

	// 2.单号生成
	gen := gen_order_no.NewOrderNoRepo(ctx, r.tx)
	orderNo, number, err := gen.GenOrderNo(ctx, "grey_fabric_manage", gfmProduceReceiveOrder, prefix, time.Now().Format(dateFormat), numLength)
	if err != nil {
		return
	}
	gfmProduceReceiveOrder.OrderNo = orderNo
	gfmProduceReceiveOrder.Number = int(number)

	gfmProduceReceiveOrder.TotalPrice, gfmProduceReceiveOrder.TotalWeight, gfmProduceReceiveOrder.TotalRoll = req.GetTotalPWR()

	gfmProduceReceiveOrder, err = mysql.MustCreateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
	if err != nil {
		return
	}
	rmStockIds := make([]uint64, 0)
	for _, item := range req.ItemData {
		for _, yarn := range item.UseYarnData {
			for _, yarnItem := range yarn.UseYarnItemData {
				rmStockIds = append(rmStockIds, yarnItem.RmlStockId)
			}
		}
	}
	rmCostPriceRepo := aggsRm.NewRmCostPriceRepo(ctx, r.tx, false)
	rmCostPriceList, _, err = rmCostPriceRepo.GetList(ctx, &structureRm.GetRmCostPriceListQuery{RmStockIds: rmStockIds})
	if err != nil {
		return
	}
	// 3. 新增坯布信息
	for _, item := range req.ItemData {
		gfmProduceReceiveOrderItem := mysql.NewGfmProduceReceiveOrderItem(ctx, &item)
		gfmProduceReceiveOrderItem.GreyFabricDeliveryId = gfmProduceReceiveOrder.Id
		gfmProduceReceiveOrderItem.OrderNo = gfmProduceReceiveOrder.OrderNo
		gfmProduceReceiveOrderItem.TotalWeight, gfmProduceReceiveOrderItem.TotalPrice, gfmProduceReceiveOrderItem.AvgWeight = item.GetTotalWP()
		gfmProduceReceiveOrderItem, err = mysql.MustCreateGfmProduceReceiveOrderItem(r.tx, gfmProduceReceiveOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFineCode := mysql.NewGfmProduceReceiveOrderItemFineCode(ctx, &fineCode)
			itemFineCode.GfmProduceReceiveItemId = gfmProduceReceiveOrderItem.Id
			itemFineCode, err = mysql.MustCreateGfmProduceReceiveOrderItemFineCode(r.tx, itemFineCode)
			if err != nil {
				return
			}
		}

		// 4. 新增用纱信息
		for _, yarn := range item.UseYarnData {
			useYarn := mysql.NewGfmProduceReceiveOrderUseYarn(ctx, &yarn)
			useYarn.OrderId = gfmProduceReceiveOrder.Id
			useYarn.GfmProduceReceiveItemId = gfmProduceReceiveOrderItem.Id
			useYarn.ActuallyUseYarnQuantity = yarn.SumActuallyUseYarnQuantity()
			useYarn, err = mysql.MustCreateGfmProduceReceiveOrderUseYarn(r.tx, useYarn)
			if err != nil {
				return
			}
			for _, yarnItem := range yarn.UseYarnItemData {
				rmCostPrice := rmCostPriceList.PickByRmStockId(yarnItem.RmlStockId)
				yarnItemModel := mysql.NewGfmProduceReceiveOrderUseYarnItem(ctx, &yarnItem)
				yarnItemModel.BuoyantWeightPrice = rmCostPrice.BuoyantWeightPrice
				yarnItemModel.UseYarnId = useYarn.Id
				yarnItemModel, err = mysql.MustCreateGfmProduceReceiveOrderUseYarnItem(r.tx, yarnItemModel)
				if err != nil {
					return
				}
			}
		}
	}

	data.Id = gfmProduceReceiveOrder.Id
	return
}

func (r *GfmProduceReceiveOrderRepo) Update(ctx context.Context, req *structure.UpdateGfmProduceReceiveOrderParam) (data structure.UpdateGfmProduceReceiveOrderData, err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
		itemModel              mysql.GfmProduceReceiveOrderItem
		findCodeModel          mysql.GfmProduceReceiveOrderItemFineCode
		fcList                 mysql.GfmProduceReceiveOrderItemFineCodeList
		itemList               mysql.GfmProduceReceiveOrderItemList
		yarnList               mysql.GfmProduceReceiveOrderUseYarnList
		yarnModel              mysql.GfmProduceReceiveOrderUseYarn
		yarnItemList           mysql.GfmProduceReceiveOrderUseYarnItemList
		yarnItemModel          mysql.GfmProduceReceiveOrderUseYarnItem
		rmCostPriceList        structureRm.GetRmCostPriceDataList
	)
	gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	// 判断该状态是否可以更新，是否有权限更新
	_err, canUpdate := gfmProduceReceiveOrder.CanUpdate(ctx)
	if _err != nil {
		err = _err
		return
	}
	if !canUpdate {
		err = cus_error.NewCustomError(cus_error.ErrCodeBusinessParameter, "，当前单据状态不能更新。")
		return
	}

	gfmProduceReceiveOrder.UpdateGfmProduceReceiveOrder(ctx, req)

	gfmProduceReceiveOrder.TotalPrice, gfmProduceReceiveOrder.TotalWeight, gfmProduceReceiveOrder.TotalRoll = req.GetTotalPWR()

	if gfmProduceReceiveOrder.AuditStatus == common_system.OrderStatusRejected {
		gfmProduceReceiveOrder.AuditStatus = common_system.OrderStatusPendingAudit
	}

	gfmProduceReceiveOrder, err = mysql.MustUpdateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
	if err != nil {
		return
	}

	// 找出该单下的信息，并删除
	itemList, err = mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	itemIds := itemList.GetIds()
	if len(itemIds) > 0 {
		err = mysql.MustDeleteByIds(r.tx, itemIds, itemModel)
		if err != nil {
			return
		}
		fcList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParentIDs(r.tx, itemIds)
		if err != nil {
			return
		}
		if len(fcList) > 0 {
			err = mysql.MustDeleteByParentIds(r.tx, itemIds, findCodeModel, "gfm_produce_receive_item_id")
			if err != nil {
				return
			}
		}
		// 查用料信息
		yarnList, err = mysql.FindGfmProduceReceiveOrderUseYarnByParentIDs(r.tx, itemIds)
		if err != nil {
			return
		}
		yarnIds := yarnList.GetIds()
		if len(yarnIds) > 0 {
			err = mysql.MustDeleteByIds(r.tx, yarnIds, yarnModel)
			if err != nil {
				return
			}
			// 查用料信息库存
			yarnItemList, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParentIDs(r.tx, itemIds)
			if err != nil {
				return
			}
			if len(yarnItemList) > 0 {
				err = mysql.MustDeleteByParentIds(r.tx, yarnIds, yarnItemModel, "use_yarn_id")
				if err != nil {
					return
				}
			}
		}
	}

	rmStockIds := make([]uint64, 0)
	for _, item := range req.ItemData {
		for _, yarn := range item.UseYarnData {
			for _, yarnItem := range yarn.UseYarnItemData {
				rmStockIds = append(rmStockIds, yarnItem.RmlStockId)
			}
		}
	}
	rmCostPriceRepo := aggsRm.NewRmCostPriceRepo(ctx, r.tx, false)
	rmCostPriceList, _, err = rmCostPriceRepo.GetList(ctx, &structureRm.GetRmCostPriceListQuery{RmStockIds: rmStockIds})
	if err != nil {
		return
	}

	// 新增坯布信息
	for _, item := range req.ItemData {
		gfmProduceReceiveOrderItem := mysql.NewGfmProduceReceiveOrderItem(ctx, &item)
		gfmProduceReceiveOrderItem.GreyFabricDeliveryId = gfmProduceReceiveOrder.Id
		gfmProduceReceiveOrderItem.OrderNo = gfmProduceReceiveOrder.OrderNo
		gfmProduceReceiveOrderItem.TotalWeight, gfmProduceReceiveOrderItem.TotalPrice, gfmProduceReceiveOrderItem.AvgWeight = item.GetTotalWP()
		gfmProduceReceiveOrderItem, err = mysql.MustCreateGfmProduceReceiveOrderItem(r.tx, gfmProduceReceiveOrderItem)
		if err != nil {
			return
		}
		// 3. 添加细码
		for _, fineCode := range item.ItemFCData {
			itemFineCode := mysql.NewGfmProduceReceiveOrderItemFineCode(ctx, &fineCode)
			itemFineCode.GfmProduceReceiveItemId = gfmProduceReceiveOrderItem.Id
			itemFineCode, err = mysql.MustCreateGfmProduceReceiveOrderItemFineCode(r.tx, itemFineCode)
			if err != nil {
				return
			}
		}
		// 4. 新增用纱信息
		for _, yarn := range item.UseYarnData {
			useYarn := mysql.NewGfmProduceReceiveOrderUseYarn(ctx, &yarn)
			useYarn.OrderId = gfmProduceReceiveOrder.Id
			useYarn.GfmProduceReceiveItemId = gfmProduceReceiveOrderItem.Id
			useYarn.ActuallyUseYarnQuantity = yarn.SumActuallyUseYarnQuantity()
			useYarn, err = mysql.MustCreateGfmProduceReceiveOrderUseYarn(r.tx, useYarn)
			if err != nil {
				return
			}
			for _, yarnItem := range yarn.UseYarnItemData {
				rmCostPrice := rmCostPriceList.PickByRmStockId(yarnItem.RmlStockId)
				yarnItemModel = mysql.NewGfmProduceReceiveOrderUseYarnItem(ctx, &yarnItem)
				yarnItemModel.BuoyantWeightPrice = rmCostPrice.BuoyantWeightPrice
				yarnItemModel.UseYarnId = useYarn.Id
				yarnItemModel, err = mysql.MustCreateGfmProduceReceiveOrderUseYarnItem(r.tx, yarnItemModel)
				if err != nil {
					return
				}
			}
		}
	}

	data.Id = gfmProduceReceiveOrder.Id
	return
}

func (r *GfmProduceReceiveOrderRepo) UpdateBusinessClose(ctx context.Context, req *structure.UpdateGfmProduceReceiveOrderBusinessCloseParam) (data structure.UpdateGfmProduceReceiveOrderData, err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
	)
	toUint64 := req.Id.ToUint64()
	for _, v := range toUint64 {
		gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, v)
		if err != nil {
			return
		}
		err = gfmProduceReceiveOrder.UpdateBusinessClose(ctx, req.BusinessClose)
		if err != nil {
			return
		}
		gfmProduceReceiveOrder, err = mysql.MustUpdateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *GfmProduceReceiveOrderRepo) UpdateStatusPass(
	ctx context.Context, id uint64) (
	data structure.UpdateGfmProduceReceiveOrderStatusData,
	addDetailParams structure.AddGfmWarehouseParamList,
	getItemAndFcIds structure.GetItemAndFcIds,
	updateItems produceStructure.UpdateProductionChangeOrderAuditParamList,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
	)

	gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, id)
	if err != nil {
		return
	}

	addDetailParams, getItemAndFcIds, updateItems, salePlanOrderItemIds, err = r.judgeAuditPass(id, gfmProduceReceiveOrder, r.tx.Context)
	if err != nil {
		return
	}

	// 审核
	err = gfmProduceReceiveOrder.Audit(ctx)
	if err != nil {
		return
	}
	gfmProduceReceiveOrder, err = mysql.MustUpdateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
	if err != nil {
		return
	}
	data.OrderNo = gfmProduceReceiveOrder.OrderNo
	return
}

func (r *GfmProduceReceiveOrderRepo) UpdateStatusWait(ctx context.Context, id uint64) (
	data structure.UpdateGfmProduceReceiveOrderStatusData,
	updateDetailParams structure.UpdateGfmWarehouseParamList,
	updateItems produceStructure.UpdateProductionChangeOrderAuditParamList,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
	)

	gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, id)
	if err != nil {
		return
	}

	updateDetailParams, updateItems, salePlanOrderItemIds, err = r.judgeAuditWait(id, gfmProduceReceiveOrder)
	if err != nil {
		return
	}

	// 消审
	err = gfmProduceReceiveOrder.Wait(ctx)
	if err != nil {
		return
	}
	gfmProduceReceiveOrder, err = mysql.MustUpdateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
	if err != nil {
		return
	}

	return
}

func (r *GfmProduceReceiveOrderRepo) UpdateStatusReject(ctx context.Context, req *structure.UpdateGfmProduceReceiveOrderStatusParam) (data structure.UpdateGfmProduceReceiveOrderStatusData, err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
	)

	ids := req.Id.ToUint64()
	for _, id := range ids {
		gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, id)
		if err != nil {
			return
		}

		// 驳回
		err = gfmProduceReceiveOrder.Reject(ctx)
		if err != nil {
			return
		}
		gfmProduceReceiveOrder, err = mysql.MustUpdateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *GfmProduceReceiveOrderRepo) UpdateStatusCancel(ctx context.Context, req *structure.UpdateGfmProduceReceiveOrderStatusParam) (data structure.UpdateGfmProduceReceiveOrderStatusData, err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
	)

	ids := req.Id.ToUint64()
	for _, id := range ids {
		gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, id)
		if err != nil {
			return
		}

		// 作废
		err = gfmProduceReceiveOrder.Cancel(ctx)
		if err != nil {
			return
		}
		gfmProduceReceiveOrder, err = mysql.MustUpdateGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
		if err != nil {
			return
		}
	}
	return
}

func (r *GfmProduceReceiveOrderRepo) Delete(ctx context.Context, req *structure.DeleteGfmProduceReceiveOrderParam) (data structure.DeleteGfmProduceReceiveOrderData, err error) {
	var (
		list mysql.GfmProduceReceiveOrderList
	)

	list, err = mysql.FindGfmProduceReceiveOrderByIDs(r.tx, req.Id.ToUint64())
	if err != nil {
		return
	}
	for _, v := range list {
		gfmProduceReceiveOrder := mysql.GfmProduceReceiveOrder{}
		gfmProduceReceiveOrder.Id = v.Id
		// 删除
		err = mysql.MustDeleteGfmProduceReceiveOrder(r.tx, gfmProduceReceiveOrder)
		if err != nil {
			return
		}
		if err != nil {
			return
		}
		data.Id = append(data.Id, gfmProduceReceiveOrder.Id)
	}
	return
}

func (r *GfmProduceReceiveOrderRepo) Get(ctx context.Context, req *structure.GetGfmProduceReceiveOrderQuery) (data structure.GetGfmProduceReceiveOrderData, err error) {
	var (
		gfmProduceReceiveOrder mysql.GfmProduceReceiveOrder
		itemDatas              mysql.GfmProduceReceiveOrderItemList
		yarnItemData           mysql.GfmProduceReceiveOrderUseYarnItemList
		yarnDatas              = make(mysql.GfmProduceReceiveOrderUseYarnList, 0)
		fineCodeList           mysql.GfmProduceReceiveOrderItemFineCodeList
		itemGetDataList        = make(structure.GetGfmProduceReceiveOrderItemDataList, 0)
		stockPBSvc             = stock_pb.NewClientStockRawMaterial()
	)
	gfmProduceReceiveOrder, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, req.Id)
	if err != nil {
		return
	}

	o := structure.GetGfmProduceReceiveOrderData{}
	r.swapListModel2Data(gfmProduceReceiveOrder, &o, ctx)

	itemDatas, err = mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParentIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	bizService := biz_pb.NewClientBizUnitService()
	baseInfoLevelService := base_info_pb.NewInfoBaseGreyFabricLevelClient()
	baseInfoColorService := base_info_pb.NewInfoProductGrayFabricColorClient()
	gfInfoSvcPB := gfInfoPB.NewGreyFabricInfoClient()
	dicSvc := dictionary.NewDictionaryClient()

	customerIds := mysql_base.GetUInt64List(itemDatas, "biz_unit_id")
	levelIds := mysql_base.GetUInt64List(itemDatas, "grey_fabric_level_id")
	colorIds := mysql_base.GetUInt64List(itemDatas, "gray_fabric_color_id")
	greyFabricIds := mysql_base.GetUInt64List(itemDatas, "grey_fabric_id")
	dicIds := mysql_base.GetUInt64ListV2("dictionary_detail_id", itemDatas, fineCodeList)
	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, customerIds)
	levelMap, _ := baseInfoLevelService.GetInfoBaseGreyFabricLevelNameByIds(ctx, levelIds)
	colorMap, _ := baseInfoColorService.GetInfoProductGrayFabricColorNameByIds(ctx, colorIds)
	gfList, _ := gfInfoSvcPB.GetGreyFabricInfoMapList(ctx, greyFabricIds)
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	for _, src := range itemDatas {
		dst := structure.GetGfmProduceReceiveOrderItemData{}
		dst.Id = src.Id
		dst.CreateTime = tools.MyTime(src.CreateTime)
		dst.UpdateTime = tools.MyTime(src.UpdateTime)
		dst.CreatorId = src.CreatorId
		dst.CreatorName = src.CreatorName
		dst.UpdaterId = src.UpdaterId
		dst.UpdateUserName = src.UpdaterName
		dst.OrderNo = src.OrderNo
		dst.GreyFabricDeliveryId = src.GreyFabricDeliveryId
		dst.ProduceNoticeOrderNo = src.ProduceNoticeOrderNo
		dst.ProduceNoticeOrderItemId = src.ProduceNoticeOrderItemId
		if greyFabric, ok := gfList[src.GreyFabricId]; ok {
			dst.GreyFabricCode = greyFabric.Code
			dst.GreyFabricName = greyFabric.Name
			dst.UnitId = greyFabric.UnitId
			dst.UnitName = greyFabric.UnitName
			dst.WeightOfFabric = greyFabric.WeightOfFabric
		}
		dst.CustomerId = src.CustomerId
		dst.NeedleSize = src.NeedleSize
		dst.RawMaterialYarnName = src.RawMaterialYarnName
		dst.RawMaterialBatchNum = src.RawMaterialBatchNum
		dst.RawMaterialBatchBrand = src.RawMaterialBatchBrand
		dst.WeavingProcess = src.WeavingProcess
		dst.YarnBatch = src.YarnBatch
		dst.GrayFabricColorId = src.GrayFabricColorId
		dst.GreyFabricLevelId = src.GreyFabricLevelId
		dst.MachineNumber = src.MachineNumber
		dst.Roll = src.Roll
		dst.ReturnRoll = src.ReturnRoll
		dst.ProcessSinglePrice = src.ProcessSinglePrice
		dst.OtherPrice = src.OtherPrice
		dst.GreyFabricRemark = src.GreyFabricRemark
		dst.TotalPrice = src.TotalPrice
		dst.TotalWeight = src.TotalWeight
		dst.AvgWeight = src.AvgWeight
		dst.Remark = src.Remark
		dst.GreyFabricId = src.GreyFabricId
		dst.WarehouseSumId = src.WarehouseSumId

		dst.CustomerName = customerMap[dst.CustomerId]
		dst.GrayFabricColorName = colorMap[dst.GrayFabricColorId]
		dst.GreyFabricLevelName = levelMap[dst.GreyFabricLevelId]
		dst.SalePlanOrderItemId = src.SalePlanOrderItemId
		dst.SalePlanOrderItemNo = src.SalePlanOrderItemNo
		dst.ReferenceWeight = src.ReferenceWeight

		dst.BuildGFResp(src.GreyFabricWidth, src.GreyFabricGramWeight, dicNameMap[src.GreyFabricWidthUnitId][1],
			dicNameMap[src.GreyFabricGramWeightUnitId][1], src.GreyFabricWidthUnitId, src.GreyFabricGramWeightUnitId)

		// 添加细码信息
		tmpFineCodeList := fineCodeList.PickList(src.Id)
		for _, fineCode := range tmpFineCodeList {
			fineCodeGetData := structure.GetGfmProduceReceiveOrderItemFineCodeData{}
			r.swapFineCodeModel2Data(fineCode, &fineCodeGetData, ctx)
			fineCodeGetData.Position = dicNameMap[fineCode.WarehouseBinId][1]
			dst.ItemFCData = append(dst.ItemFCData, fineCodeGetData)
		}

		// 用纱信息赋值
		yarnDatas, err = mysql.FindGfmProduceReceiveOrderUseYarnByParenTID(r.tx, dst.Id)
		if err != nil {
			return
		}

		for _, yarnData := range yarnDatas {
			yarn := structure.GetGfmProduceReceiveOrderUseYarnData{}
			r.swapYarnData2GetData(yarnData, &yarn, ctx)
			yarnItemData, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParenTID(r.tx, yarn.Id)
			if err != nil {
				return
			}
			rmlIDS := yarnItemData.GetRMLIDs()
			rmlMap, _ := stockPBSvc.QueryStockRawMaterialByIds(ctx, r.tx, rmlIDS)
			for _, item := range yarnItemData {
				getItemData := structure.GetGfmProduceReceiveOrderUseYarnItemData{}
				r.swapYarnItemModel2Data(item, &getItemData, rmlMap)
				yarn.UseYarnItemData = append(yarn.UseYarnItemData, getItemData)
				yarn.RmTotalCostPrice += item.BuoyantWeightPrice * item.UseYarnQuantity / vars.WeightUnitPriceMult
			}
			if yarn.UseYarnItemData == nil {
				yarn.UseYarnItemData = make(structure.GetGfmProduceReceiveOrderUseYarnItemDataList, 0)
			}
			dst.UseYarnData = append(dst.UseYarnData, yarn)
		}

		o.ItemData = append(o.ItemData, dst)
	}

	if o.ItemData == nil {
		o.ItemData = itemGetDataList
	}

	data = o
	return
}

func (r *GfmProduceReceiveOrderRepo) GetList(ctx context.Context, req *structure.GetGfmProduceReceiveOrderListQuery) (list structure.GetGfmProduceReceiveOrderDataList, total int, err error) {
	var (
		gfmProduceReceiveOrders mysql.GfmProduceReceiveOrderList
	)
	gfmProduceReceiveOrders, total, err = mysql.SearchGfmProduceReceiveOrder(r.tx, req)
	if err != nil {
		return
	}

	for _, gfmProduceReceiveOrder := range gfmProduceReceiveOrders.List() {
		o := structure.GetGfmProduceReceiveOrderData{}
		r.swapListModel2Data(gfmProduceReceiveOrder, &o, ctx)
		list = append(list, o)
	}
	return
}

func (r *GfmProduceReceiveOrderRepo) swapListModel2Data(src mysql.GfmProduceReceiveOrder, dst *structure.GetGfmProduceReceiveOrderData, ctx context.Context) {
	var (
		bizService = biz_pb.NewClientBizUnitService()
		saleSvc    = saleSys.NewSaleSystemClient()
	)

	bizMap, err1 := bizService.GetBizUnitNameByIds(ctx, []uint64{src.ReceiveUnitId, src.SupplierId})
	if err1 != nil {
		fmt.Println(err1)
	}
	saleSystemMap, err2 := saleSvc.GetSaleSystemById(ctx, src.SaleSystemId)
	if err2 != nil {
		fmt.Println(err2)
	}

	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.BusinessClose = src.BusinessClose
	dst.BusinessCloseName = dst.BusinessClose.String()
	dst.BusinessCloseUserId = src.BusinessCloseUserId
	dst.BusinessCloseUserName = src.BusinessCloseUserName
	dst.BusinessCloseTime = tools.MyTime(src.BusinessCloseTime)
	dst.DepartmentId = src.DepartmentId
	dst.SaleSystemId = src.SaleSystemId
	dst.ReceiveUnitId = src.ReceiveUnitId
	dst.SupplierId = src.SupplierId
	dst.VoucherNumber = src.VoucherNumber
	dst.ReceiveTime = tools.MyTime(src.ReceiveTime)
	dst.OrderNo = src.OrderNo
	dst.Number = src.Number
	dst.AuditStatus = src.AuditStatus
	dst.AuditStatusName = dst.AuditStatus.String()
	dst.AuditerId = src.AuditorId
	dst.AuditerName = src.AuditorName
	dst.AuditTime = tools.MyTime(src.AuditDate)
	dst.Remark = src.Remark
	dst.TotalPrice = tools.Cent(src.TotalPrice)
	dst.TotalWeight = tools.Milligram(src.TotalWeight)
	dst.TotalRoll = tools.Hundred(src.TotalRoll)
	dst.DyeUnitUseOrderNo = src.DyeUnitUseOrderNo
	if val, ok := saleSystemMap[src.SaleSystemId]; ok {
		dst.SaleSystemName = val
	}

	if val, ok := bizMap[src.ReceiveUnitId]; ok {
		dst.ReceiveUnitName = val
	}
	if val, ok := bizMap[src.SupplierId]; ok {
		dst.SupplierName = val
	}

}

// func (r *GfmProduceReceiveOrderRepo) swapItemModel2Data(src mysql.GfmProduceReceiveOrderItem, dst *structure.GetGfmProduceReceiveOrderItemData, ctx context.Context) {
//	var (
//		// PB 相当于rpc获取数据
//		bizService           = biz_pb.NewClientBizUnitService()
//		baseInfoLevelService = base_info_pb.NewInfoBaseGreyFabricLevelClient()
//		baseInfoColorService = base_info_pb.NewInfoProductGrayFabricColorClient()
//	)
//
//	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, []uint64{src.CustomerId})
//	levelMap, _ := baseInfoLevelService.GetInfoBaseGreyFabricLevelNameByIds(ctx, []uint64{src.GreyFabricLevelId})
//	colorMap, _ := baseInfoColorService.GetInfoProductGrayFabricColorNameByIds(ctx, []uint64{src.GrayFabricColorId})
//
//	dst.Id = src.Id
//	dst.CreateTime = tools.MyTime(src.CreateTime)
//	dst.UpdateTime = tools.MyTime(src.UpdateTime)
//	dst.CreatorId = src.CreatorId
//	dst.CreatorName = src.CreatorName
//	dst.UpdaterId = src.UpdaterId
//	dst.UpdateUserName = src.UpdaterName
//	dst.OrderNo = src.OrderNo
//	dst.GreyFabricDeliveryId = src.GreyFabricDeliveryId
//	dst.ProduceNoticeOrderNo = src.ProduceNoticeOrderNo
//	dst.ProduceNoticeOrderItemId = src.ProduceNoticeOrderItemId
//	dst.GreyFabricCode = src.GreyFabricCode
//	dst.GreyFabricName = src.GreyFabricName
//	dst.CustomerId = src.CustomerId
//	dst.GreyFabricWidth = src.GreyFabricWidth
//	dst.GreyFabricGramWeight = src.GreyFabricGramWeight
//	dst.NeedleSize = src.NeedleSize
//	dst.RawMaterialYarnName = src.RawMaterialYarnName
//	dst.RawMaterialBatchNum = src.RawMaterialBatchNum
//	dst.RawMaterialBatchBrand = src.RawMaterialBatchBrand
//	dst.WeavingProcess = src.WeavingProcess
//	dst.YarnBatch = src.YarnBatch
//	dst.GrayFabricColorId = src.GrayFabricColorId
//	dst.GreyFabricLevelId = src.GreyFabricLevelId
//	dst.MachineNumber = src.MachineNumber
//	dst.Roll = src.Roll
//	dst.ReturnRoll = src.ReturnRoll
//	dst.ProcessSinglePrice = src.ProcessSinglePrice
//	dst.OtherPrice = src.OtherPrice
//	dst.GreyFabricRemark = src.GreyFabricRemark
//	dst.TotalPrice = src.TotalPrice
//	dst.TotalWeight = src.TotalWeight
//	dst.AvgWeight = src.AvgWeight
//	dst.Remark = src.Remark
//	dst.GreyFabricId = src.GreyFabricId
//	dst.WarehouseSumId = src.WarehouseSumId
//
//	// 客户名称
//	if val, ok := customerMap[dst.CustomerId]; ok {
//		dst.CustomerName = val
//	}
//
//	if val, ok := colorMap[dst.GrayFabricColorId]; ok {
//		dst.GrayFabricColorName = val
//	}
//
//	if val, ok := levelMap[dst.GreyFabricLevelId]; ok {
//		dst.GreyFabricLevelName = val
//	}
//	return
// }

func (r *GfmProduceReceiveOrderRepo) swapFineCodeModel2Data(src mysql.GfmProduceReceiveOrderItemFineCode, dst *structure.GetGfmProduceReceiveOrderItemFineCodeData, ctx context.Context) {
	dst.Id = src.Id
	dst.CreateTime = tools.MyTime(src.CreateTime)
	dst.UpdateTime = tools.MyTime(src.UpdateTime)
	dst.CreatorId = src.CreatorId
	dst.CreatorName = src.CreatorName
	dst.UpdaterId = src.UpdaterId
	dst.UpdateUserName = src.UpdaterName
	dst.GfmProduceReceiveItemId = src.GfmProduceReceiveItemId
	dst.Roll = src.Roll
	dst.VolumeNumber = src.VolumeNumber
	dst.Position = src.Position
	dst.BarCode = src.BarCode
	dst.Weight = src.Weight
	dst.WarehouseBinId = src.WarehouseBinId
	dst.FabricPieceCode = src.FabricPieceCode
}

func (r *GfmProduceReceiveOrderRepo) judgeAuditPass(
	id uint64, order mysql.GfmProduceReceiveOrder, ctx context.Context) (
	addDetailParamsV2 structure.AddGfmWarehouseParamList,
	getItemAndFcIdsV2 structure.GetItemAndFcIds,
	updateItemsV2 produceStructure.UpdateProductionChangeOrderAuditParamList,
	salePlanOrderItemIds []uint64,
	err error) {

	var (
		fineCodeList          mysql.GfmProduceReceiveOrderItemFineCodeList
		itemData              mysql.GfmProduceReceiveOrderItem
		yarnData              mysql.GfmProduceReceiveOrderUseYarnList
		yarnItemData          mysql.GfmProduceReceiveOrderUseYarnItemList
		categoryItem          = make(map[string][]uint64)
		fcIdsMap              = make(map[string][]uint64)
		bizPBSvc              = biz_pb.NewClientBizUnitService()
		rmlPBSVC              = stock_pb.NewClientStockRawMaterial()
		updateItems           = make(produceStructure.UpdateProductionChangeOrderAuditParamList, 0)
		itemList              = make(mysql.GfmProduceReceiveOrderItemList, 0)
		useYarnList           = make(mysql.GfmProduceReceiveOrderUseYarnList, 0)
		yarnItemList          = make(mysql.GfmProduceReceiveOrderUseYarnItemList, 0)
		_salePlanOrderItemIds = make([]uint64, 0)
	)
	addDetailParams := structure.AddGfmWarehouseParamList{}
	getItemAndFcIds := structure.GetItemAndFcIds{}

	// 判断坯布数量是否符合
	itemList, err = mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	// 用纱比例
	useYarnList, err = mysql.FindGfmProduceReceiveOrderUseYarnByOrderIDs(r.tx, id)
	if err != nil {
		return
	}
	// 用纱信息
	yarnItemList, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParentIDs(r.tx, useYarnList.GetIds())
	if err != nil {
		return
	}

	for _, yarn := range useYarnList {
		var (
			tempList     = yarnItemList.PickList(yarn.Id)
			totalUseYarn = 0
		)
		for _, item := range tempList {
			totalUseYarn += item.UseYarnQuantity
		}
		if len(tempList) == 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeLackMustInfo, "请输入消耗用纱信息"))
			return
		}
		if yarn.ActuallyUseYarnQuantity != totalUseYarn {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeNoEqualData, "消耗用纱信息用纱量与实际用纱量不相等"))
			return
		}
	}

	bizTypeMap, _ := bizPBSvc.GetBizUnitTypeById(r.tx.Context, []uint64{order.ReceiveUnitId})

	for _, item := range itemList {
		var TotalRoll int = 0
		if item.Roll == 0 && item.TotalWeight == 0 {
			err = middleware.ErrorLog(cus_error.NewError(cus_error.ErrCodeRollAndWeightCanNotAllZero))
			return
		}
		if item.ProcessSinglePrice <= 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 加工单价需大于0，错误行："+item.GreyFabricName))
			return
		}

		fineCodeList, _ = mysql.FindGfmProduceReceiveOrderItemFineCodeByParenTID(r.tx, item.Id)
		if len(fineCodeList) == 0 {
			err = middleware.ErrorLog(cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 必须填入细码，错误行："+item.GreyFabricName))
			return
		}

		for _, fineCode := range fineCodeList {
			TotalRoll = TotalRoll + fineCode.Roll
		}
		if TotalRoll != item.Roll {
			err = cus_error.NewCustomError(cus_error.ErrCodeMysqlUpdate, "; 分录行的匹数，必须等于对应的细码总匹数，错误行："+item.GreyFabricName)
			return
		}

		// 反写生产通知单信息
		updateItem := item.ToAuditProducedItem()
		updateItems = append(updateItems, updateItem)

		// 判断分类是否为同一汇总信息（遍历分录行）
		judgeEqual := item.SumQurey2StrForJedgeEqual(order)
		categoryItem[judgeEqual] = append(categoryItem[judgeEqual], item.Id)

		if item.SalePlanOrderItemId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
	}

	getItemAndFcIds.ItemIdsMap = categoryItem
	// 如果收货单位是染厂，则需要进行汇总细码（当成一条细码来存）
	// if tools.ExistInList(bizTypeMap[order.ReceiveUnitId], uint64(common_system.BizUnitTypeNotOur)) {
	if tools.ExistInList(bizTypeMap[order.ReceiveUnitId], 666) {
		for k, v := range categoryItem {
			itemData, err = mysql.MustFirstGfmProduceReceiveOrderItemByID(r.tx, v[0])
			if err != nil {
				return
			}
			param := structure.AddGfmWarehouseParam{}
			paramFc := structure.AddWarehouseFcParam{}
			// 因为是汇总的，只去一个id赋值就够了
			param = itemData.ToAddStockGreyFabricParam(order)
			fineCodeList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParentIDs(r.tx, v)
			if err != nil {
				return
			}
			fcIds := fineCodeList.GetIds()
			fcIdsMap[k] = fcIds
			for _, fc := range fineCodeList {
				if fc.GreyFabricStockId > 0 {
					paramFc.Id = fc.GreyFabricStockId
				}
				paramFc.Num += fc.Roll
				paramFc.Weight += fc.Weight
				paramFc.AddWeightItemFcId = fc.Id
				paramFc.BarCode = fc.BarCode
				paramFc.WarehouseBinId = fc.WarehouseBinId
				paramFc.WarehouseBinName = fc.Position
				paramFc.VolumeNumber = fc.VolumeNumber
				paramFc.SrcFCId = fc.Id
				paramFc.FabricPieceCode = fc.FabricPieceCode
				param.StockRoll += fc.Roll
				param.StockWeight += fc.Weight
				param.InRoll += fc.Roll
				param.InWeight += fc.Weight
			}
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			param.FcParam = append(param.FcParam, paramFc)
			param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
			addDetailParams = append(addDetailParams, param)
		}
	} else {
		// 一条细码一条数据
		for k, v := range categoryItem {
			itemData, err = mysql.MustFirstGfmProduceReceiveOrderItemByID(r.tx, v[0])
			if err != nil {
				return
			}
			param := structure.AddGfmWarehouseParam{}
			paramFc := structure.AddWarehouseFcParam{}

			param = itemData.ToAddStockGreyFabricParam(order)
			fineCodeList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParentIDs(r.tx, v)
			if err != nil {
				return
			}
			fcIds := fineCodeList.GetIds()
			fcIdsMap[k] = fcIds
			for _, fc := range fineCodeList {
				if fc.GreyFabricStockId > 0 {
					paramFc.Id = fc.GreyFabricStockId
				}
				paramFc.Num = fc.Roll
				paramFc.Weight = fc.Weight
				paramFc.BarCode = fc.BarCode
				paramFc.WarehouseBinId = fc.WarehouseBinId
				paramFc.WarehouseBinName = fc.Position
				paramFc.VolumeNumber = fc.VolumeNumber
				paramFc.AddWeightItemFcId = fc.Id
				paramFc.SrcFCId = fc.Id
				paramFc.FabricPieceCode = fc.FabricPieceCode
				param.StockRoll += fc.Roll
				param.StockWeight += fc.Weight
				param.InRoll += fc.Roll
				param.InWeight += fc.Weight
				param.FcParam = append(param.FcParam, paramFc)
			}
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			param.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
			addDetailParams = append(addDetailParams, param)
		}
	}
	getItemAndFcIds.FcIdsMap = fcIdsMap

	// 审核扣纱
	yarnData, err = mysql.FindGfmProduceReceiveOrderUseYarnByParentIDs(r.tx, itemList.GetIds())
	if err != nil {
		return
	}
	yarnIds := yarnData.GetIds()
	yarnItemData, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParentIDs(r.tx, yarnIds)
	req := &sys_structure.SaveRawMaterialStockParams{}
	for _, item := range yarnItemData {
		req.Items = append(req.Items, &sys_structure.SaveRawMaterialStockItem{
			SrcId:       id,
			SrcOrderNo:  order.OrderNo,
			StockItemId: item.RmlStockId,
			TotalWeight: item.UseYarnQuantity,
			TransType:   stock_const.TransTypeOut,
			UseType:     stock_const.UseTypeProduceUse,
			SrcType:     uint8(stock_const.SrcTypeRawMatlGFProduceReceive),
		})
	}
	_, err = rmlPBSVC.ReduceRawMaterialStock(ctx, r.tx, req)
	if err != nil {
		return
	}
	addDetailParamsV2 = addDetailParams
	getItemAndFcIdsV2 = getItemAndFcIds
	updateItemsV2 = updateItems
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *GfmProduceReceiveOrderRepo) judgeAuditWait(
	id uint64, order mysql.GfmProduceReceiveOrder) (
	updateDetailParamsV2 structure.UpdateGfmWarehouseParamList,
	updateItemsV2 produceStructure.UpdateProductionChangeOrderAuditParamList,
	salePlanOrderItemIds []uint64,
	err error) {
	var (
		sumId                 uint64
		_salePlanOrderItemIds = make([]uint64, 0)
		fineCodeList          mysql.GfmProduceReceiveOrderItemFineCodeList
		itemDataList          mysql.GfmProduceReceiveOrderItemList
		categoryItem          = make(map[uint64][]uint64)
		// rmlPBSVC     = stock_pb.NewClientStockRawMaterial()
		updateItems = make(produceStructure.UpdateProductionChangeOrderAuditParamList, 0)
	)

	updateDetailParams := structure.UpdateGfmWarehouseParamList{}

	// 判断坯布数量是否符合
	itemList, _ := mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, id)

	for _, item := range itemList {
		// 反写生产通知单
		if item.ProduceNoticeOrderItemId > 0 && len(item.ProduceNoticeOrderNo) > 0 {
			updateItem := item.ToWaitProducedItem()
			updateItems = append(updateItems, updateItem)
		}

		// 判断分类是否为同一汇总信息（遍历分录行）
		sumId = item.WarehouseSumId
		categoryItem[sumId] = append(categoryItem[sumId], item.Id)

		if item.SalePlanOrderItemId > 0 {
			_salePlanOrderItemIds = append(_salePlanOrderItemIds, item.SalePlanOrderItemId)
		}
	}

	for _, v := range categoryItem {
		itemDataList, err = mysql.FindGfmProduceReceiveOrderItemByIDs(r.tx, v)
		if err != nil {
			return
		}
		fineCodeList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParentIDs(r.tx, v)
		if err != nil {
			return
		}
		param := structure.UpdateGfmWarehouseParam{}

		// 因为是汇总的，只去一个id赋值就够了
		if len(itemDataList) > 0 {
			param.WarehouseSumId = itemDataList[0].WarehouseSumId
			// param.WarehouseOutOrderId = order.Id
			// param.WarehouseOutOrderNo = order.DocumentCode
			// param.WarehouseOutType = cus_const.SourceWarehouseTypeWait
			param.ChangeTime = tools.QueryTime(order.ReceiveTime.Format("2006-01-02"))
			for _, fc := range fineCodeList {
				paramFc := structure.UpdateWarehouseFcParam{}
				paramFc.Id = fc.GreyFabricStockId
				paramFc.Num -= fc.Roll
				paramFc.Weight -= fc.Weight
				paramFc.AddWeightItemFcId = fc.Id
				paramFc.IsToWait = true
				param.StockRoll -= fc.Roll
				param.StockWeight -= fc.Weight
				param.InRoll -= fc.Roll
				param.InWeight -= fc.Weight
				param.FcParam = append(param.FcParam, paramFc)
			}
			param.TurnStatusWait = true
			param.Types = []cus_const.StockType{cus_const.StockTypeWaitDye, cus_const.StockTypeTotal}
			updateDetailParams = append(updateDetailParams, param)
		}
	}

	updateDetailParamsV2 = updateDetailParams
	updateItemsV2 = updateItems
	salePlanOrderItemIds = _salePlanOrderItemIds
	return
}

func (r *GfmProduceReceiveOrderRepo) UpdateStockId(
	ctx context.Context, tx *mysql_base.Tx, id uint64, idsMap map[uint64]uint64, sumIdsMap map[uint64]uint64, getItemAndFcIds structure.GetItemAndFcIds) (
	addRecordItemListV2 structure.AddGfmStockRecordParamList, err error) {
	var (
		order    mysql.GfmProduceReceiveOrder
		itemList mysql.GfmProduceReceiveOrderItemList
		fcList   mysql.GfmProduceReceiveOrderItemFineCodeList
		bizPBSvc = biz_pb.NewClientBizUnitService()
	)
	order, err = mysql.MustFirstGfmProduceReceiveOrderByID(r.tx, id)
	if err != nil {
		return
	}
	bizTypeMap, _ := bizPBSvc.GetBizUnitTypeById(r.tx.Context, []uint64{order.ReceiveUnitId})

	// 更新分录行的汇总库存id
	for k, v := range sumIdsMap {
		for _, v2 := range getItemAndFcIds.ItemIdsMap {
			exist := tools.UInt64Contains(k, v2...)
			if exist {
				err = mysql.MustUpdateGfmProduceReceiveOrderItemStockIDByIDs(r.tx, v, v2)
				if err != nil {
					return
				}
				break
			}
		}
	}

	// 如果收货单位是染厂，则需要进行汇总细码（当成一条细码来存）
	// if tools.ExistInList(bizTypeMap[order.ReceiveUnitId], uint64(common_system.BizUnitTypeNotOur)) {
	if tools.ExistInList(bizTypeMap[order.ReceiveUnitId], 666) {
		// 更新细码里的库存id
		for k, v := range idsMap {
			for _, v2 := range getItemAndFcIds.FcIdsMap {
				exist := tools.UInt64Contains(k, v2...)
				if exist {
					err = mysql.MustUpdateGfmProduceReceiveOrderItemFineCodeStockIDByIDs(r.tx, v, v2)
					if err != nil {
						return
					}
					break
				}
			}
		}
	} else {
		itemList, err = mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, id)
		if err != nil {
			return
		}
		for _, item := range itemList {
			fcList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParenTID(r.tx, item.Id)
			// 更新细码对应的库存id
			for _, fc := range fcList {
				if val, ok := idsMap[fc.Id]; ok {
					fc.GreyFabricStockId = val
					fc, err = mysql.MustUpdateGfmProduceReceiveOrderItemFineCode(r.tx, fc)
					if err != nil {
						return
					}
				}
			}
		}
	}

	// 记录台账（需要前面更新完）
	addRecordItemList := structure.AddGfmStockRecordParamList{}
	itemList, err = mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, id)
	if err != nil {
		return
	}
	for _, item := range itemList {
		fcList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParenTID(r.tx, item.Id)
		if err != nil {
			return
		}
		for _, fineCode := range fcList {
			addRecordItem := structure.AddGfmStockRecordParam{}
			addRecordItem = r.AddPDRCParam(order, item, fineCode)
			addRecordItemList = append(addRecordItemList, addRecordItem)
		}
	}
	addRecordItemListV2 = addRecordItemList
	return
}

func (r *GfmProduceReceiveOrderRepo) swapYarnData2GetData(src mysql.GfmProduceReceiveOrderUseYarn, dst *structure.GetGfmProduceReceiveOrderUseYarnData, ctx context.Context) {
	dst.Id = src.Id
	dst.OrderId = src.OrderId
	dst.ProduceOrderUseYarnId = src.ProduceOrderUseYarnId
	dst.ActuallyUseYarnQuantity = src.ActuallyUseYarnQuantity
	// 根据生产通知单-用纱信息id获取
	produceClient := produce_pb.NewProduceClient()
	yarn, _ := produceClient.GetProductionNotifyMaterialRatioById(ctx, src.ProduceOrderUseYarnId)
	// for _, yarn := range list {
	dst.ProductionNotifyOrderId = yarn.ProductionNotifyOrderId
	dst.RawMaterialId = yarn.RawMaterialId
	dst.RawMaterialCode = yarn.RawMaterialCode
	dst.RawMaterialName = yarn.RawMaterialName
	dst.WeavingCategory = yarn.WeavingCategory
	dst.RawMaterialBrand = yarn.RawMaterialBrand
	dst.RawMaterialBatchNumber = yarn.RawMaterialBatchNumber
	dst.GreyFabricColorId = yarn.GreyFabricColorId
	dst.GreyFabricColorName = yarn.GreyFabricColorName
	dst.SupplierId = yarn.SupplierId
	dst.SupplierName = yarn.SupplierName
	dst.MillPrivateYarn = yarn.MillPrivateYarn
	dst.YarnRatio = yarn.YarnRatio
	dst.YarnLoss = yarn.YarnLoss
	dst.UseYarnQuantity = yarn.UseYarnQuantity
	dst.SendYarnQuantity = yarn.SendYarnQuantity
	dst.Remark = yarn.Remark
	dst.ColorScheme = yarn.ColorScheme
	dst.UnitId = yarn.UnitId
	dst.UnitName = yarn.UnitName
	// }
}

func (r *GfmProduceReceiveOrderRepo) swapYarnItemModel2Data(src mysql.GfmProduceReceiveOrderUseYarnItem, dst *structure.GetGfmProduceReceiveOrderUseYarnItemData, rmlMap map[uint64]*sys_structure.StockRawMatlDetail) {
	var (
		bizService      = biz_pb.NewClientBizUnitService()
		base_info_pbSvc = base_info_pb.NewInfoBaseRawMaterialLevelClient()
	)
	dst.Id = src.Id
	dst.UseYarnId = src.UseYarnId
	dst.RmlStockId = src.RmlStockId
	dst.UseYarnQuantity = src.UseYarnQuantity
	dst.Remark = src.Remark
	dst.BuoyantWeightPrice = src.BuoyantWeightPrice
	dst.RmCostPrice = src.BuoyantWeightPrice * src.UseYarnQuantity / vars.WeightUnitPriceMult
	if val, ok := rmlMap[src.RmlStockId]; ok {
		bizMap, err1 := bizService.GetBizUnitNameByIds(r.tx.Context, []uint64{val.UnitId, val.CustomerId, val.SupplierId})
		if err1 != nil {
			fmt.Println(err1)
		}
		dst.UnitId = val.UnitId
		dst.SupplierId = val.SupplierId
		dst.RawMaterialId = val.RawMaterialId
		dst.RawMaterialCode = val.RawMaterialCode
		dst.RawMaterialName = val.RawMaterialName
		dst.CustomerId = val.CustomerId
		dst.Brand = val.Brand
		dst.Craft = val.Craft
		dst.BatchNum = val.BatchNum
		dst.ColorScheme = val.ColorScheme
		dst.LevelID = val.LevelID
		levelName, _ := base_info_pbSvc.GetInfoBaseRawMaterialLevelNameById(r.tx.Context, val.LevelID)
		dst.ProductionDate = val.ProductionDate
		dst.SpinningType = val.SpinningType
		dst.CottonOrigin = val.CottonOrigin
		dst.YarnOrigin = val.YarnOrigin
		dst.CartonNum = val.CartonNum
		dst.FapiaoNum = val.FapiaoNum
		dst.RawMaterialRemark = val.Remark
		dst.UnitName = bizMap[val.UnitId]
		dst.CustomerName = bizMap[val.CustomerId]
		dst.SupplierName = bizMap[val.SupplierId]
		dst.LevelName = levelName
		dst.ColorName = val.ColorName
		dst.ColorCode = val.ColorCode
		dst.ColorId = val.ColorId
		dst.MeasurementUnitId = val.MeasurementUnitId
		dst.MeasurementUnitName = val.MeasurementUnitName
		dst.DyelotNumber = val.DyelotNumber
	}
}

func (r *GfmProduceReceiveOrderRepo) AddPDRCParam(order mysql.GfmProduceReceiveOrder, item mysql.GfmProduceReceiveOrderItem, fc mysql.GfmProduceReceiveOrderItemFineCode) structure.AddGfmStockRecordParam {
	p := structure.AddGfmStockRecordParam{}
	p.OrderTime = tools.QueryTime(order.ReceiveTime.Format("2006-01-02"))
	p.OrderType = cus_const.SourceWarehouseTypeProductReceive
	p.OrderNo = order.OrderNo
	p.OutOrderTime = tools.QueryTime(order.ReceiveTime.Format("2006-01-02"))
	p.OutOrderNo = order.OrderNo
	p.OutOrderId = order.Id
	p.VoucherNumber = order.VoucherNumber
	p.OrderId = order.Id
	p.DyeFactoryId = order.ReceiveUnitId
	p.GreyFabricId = item.GreyFabricId
	p.SupplierId = order.SupplierId
	p.YarnBatch = item.YarnBatch
	p.RawMaterialYarnName = item.RawMaterialYarnName
	p.RawMaterialBatchNum = item.RawMaterialBatchNum
	p.GreyFabricColorId = item.GrayFabricColorId
	p.GreyFabricLevelId = item.GreyFabricLevelId
	p.MachineNumber = item.MachineNumber
	p.GreyFabricRemark = item.GreyFabricRemark
	p.GreyFabricCost = item.ProcessSinglePrice
	p.DyeFactoryRemark = order.Remark
	p.InRoll = fc.Roll
	p.InWeight = fc.Weight
	p.ProduceNotifyOrderId = item.ProduceNoticeOrderItemId
	p.ProduceNotifyOrderNo = item.ProduceNoticeOrderNo
	p.WarehouseSumId = item.WarehouseSumId
	p.GreyFabricStockId = fc.GreyFabricStockId
	p.WarehouseBinId = fc.WarehouseBinId
	p.DyeUnitUseOrderNo = order.DyeUnitUseOrderNo
	p.ReceiveUnitId = order.ReceiveUnitId
	p.ResourceUnitId = order.SupplierId
	p.VoucherNumber = fc.VolumeNumber
	p.FabricPieceCode = fc.FabricPieceCode
	return p
}

// 用纱比例-用料信息列表
func (r *GfmProduceReceiveOrderRepo) GetUseYarnItemListByOrderID(ctx context.Context, req *structure.GetGfmProduceReceiveOrderUseYarnItemListQuery) (list structure.GetGfmProduceReceiveOrderUseYarnItemDataList, total int, err error) {
	var (
		yarnItemData mysql.GfmProduceReceiveOrderUseYarnItemList
		yarnList     mysql.GfmProduceReceiveOrderUseYarnList
		itemList     mysql.GfmProduceReceiveOrderItemList
		stockPBSvc   = stock_pb.NewClientStockRawMaterial()
		order        mysql.GfmProduceReceiveOrder
		itemIds      = make([]uint64, 0)
		yarnIds      = make([]uint64, 0)
		exist        bool
	)
	// 查单,不存在则返回空数组
	order, exist, err = mysql.FirstGfmProduceReceiveOrderByID(r.tx, req.SourceId)
	if !exist {
		return
	}
	if err != nil {
		return
	}
	if order.AuditStatus != common_system.OrderStatusAudited {
		fmt.Println(cus_error.ErrCodeCanNotGetDataForNotPass.String())
		return list, 0, nil
	}
	// 查坯布信息
	itemList, err = mysql.FindGfmProduceReceiveOrderItemByParenTID(r.tx, req.SourceId)
	if err != nil {
		return
	}
	itemIds = itemList.GetIds()

	// 查用料比例
	if len(itemIds) > 0 {
		yarnList, err = mysql.FindGfmProduceReceiveOrderUseYarnByParentIDs(r.tx, itemIds)
		if err != nil {
			return
		}
		yarnIds = yarnList.GetIds()
	}

	// 差用料信息
	if len(yarnIds) > 0 {
		yarnItemData, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParentIDs(r.tx, yarnIds)
		if err != nil {
			return
		}
	}

	if len(yarnItemData) > 0 {
		rmlIDS := yarnItemData.GetRMLIDs()
		rmlMap, _ := stockPBSvc.QueryStockRawMaterialByIds(ctx, r.tx, rmlIDS)

		for _, gfmProduceReceiveOrderUseYarnItem := range yarnItemData.List() {
			o := structure.GetGfmProduceReceiveOrderUseYarnItemData{}
			r.swapYarnItemModel2Data(gfmProduceReceiveOrderUseYarnItem, &o, rmlMap)
			list = append(list, o)
		}
	}

	return
}

// 根据分录行id获取用纱比例和用纱信息
func (r *GfmProduceReceiveOrderRepo) GetPRCUseYarnDataUseShouldPayListByItemID(
	ctx context.Context, req *structure.GetGfmProduceReceiveOrderItemQuery) (
	data structure.GetPRCUseYarnDataUseShouldPay, err error) {

	var (
		yarnDatas    = make(mysql.GfmProduceReceiveOrderUseYarnList, 0)
		yarnItemData = mysql.GfmProduceReceiveOrderUseYarnItemList{}
		stockPBSvc   = stock_pb.NewClientStockRawMaterial()
	)
	// 用纱信息赋值
	yarnDatas, err = mysql.FindGfmProduceReceiveOrderUseYarnByParenTID(r.tx, req.Id)
	if err != nil {
		return
	}

	for _, yarnData := range yarnDatas {
		yarn := structure.GetGfmProduceReceiveOrderUseYarnData{}
		r.swapYarnData2GetData(yarnData, &yarn, ctx)
		yarnItemData, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParenTID(r.tx, yarn.Id)
		if err != nil {
			return
		}
		rmlIDS := yarnItemData.GetRMLIDs()
		rmlMap, _ := stockPBSvc.QueryStockRawMaterialByIds(ctx, r.tx, rmlIDS)
		for _, item := range yarnItemData {
			getItemData := structure.GetGfmProduceReceiveOrderUseYarnItemData{}
			r.swapYarnItemModel2Data(item, &getItemData, rmlMap)
			yarn.RmTotalCostPrice += getItemData.RmCostPrice
			data.UseDataItems = append(data.UseDataItems, getItemData)
		}
		if yarn.UseYarnItemData == nil {
			yarn.UseYarnItemData = make(structure.GetGfmProduceReceiveOrderUseYarnItemDataList, 0)
		}
		data.UseDatas = append(data.UseDatas, yarn)
	}
	return
}

func (r *GfmProduceReceiveOrderRepo) GetListWithItemById(ctx context.Context, ids []uint64) (data structure.GetGfmProduceReceiveOrderDataList, err error) {
	var (
		gfmProduceReceiveOrders mysql.GfmProduceReceiveOrderList
		itemDatas               mysql.GfmProduceReceiveOrderItemList
		yarnItemData            mysql.GfmProduceReceiveOrderUseYarnItemList
		yarnDatas               = make(mysql.GfmProduceReceiveOrderUseYarnList, 0)
		fineCodeList            mysql.GfmProduceReceiveOrderItemFineCodeList
		stockPBSvc              = stock_pb.NewClientStockRawMaterial()
	)
	gfmProduceReceiveOrders, err = mysql.FindGfmProduceReceiveOrderByIDs(r.tx, ids)
	if err != nil {
		return
	}

	itemDatas, err = mysql.FindGfmProduceReceiveOrderItemByParentIds(r.tx, gfmProduceReceiveOrders.GetIds())
	if err != nil {
		return
	}

	fineCodeList, err = mysql.FindGfmProduceReceiveOrderItemFineCodeByParentIDs(r.tx, itemDatas.GetIds())
	if err != nil {
		return
	}

	bizService := biz_pb.NewClientBizUnitService()
	baseInfoLevelService := base_info_pb.NewInfoBaseGreyFabricLevelClient()
	baseInfoColorService := base_info_pb.NewInfoProductGrayFabricColorClient()
	gfInfoSvcPB := gfInfoPB.NewGreyFabricInfoClient()
	dicSvc := dictionary.NewDictionaryClient()

	customerIds := mysql_base.GetUInt64List(itemDatas, "biz_unit_id")
	levelIds := mysql_base.GetUInt64List(itemDatas, "grey_fabric_level_id")
	colorIds := mysql_base.GetUInt64List(itemDatas, "gray_fabric_color_id")
	greyFabricIds := mysql_base.GetUInt64List(itemDatas, "grey_fabric_id")
	dicIds := mysql_base.GetUInt64ListV2("dictionary_detail_id", itemDatas, fineCodeList)
	customerMap, _ := bizService.GetBizUnitNameByIds(ctx, customerIds)
	levelMap, _ := baseInfoLevelService.GetInfoBaseGreyFabricLevelNameByIds(ctx, levelIds)
	colorMap, _ := baseInfoColorService.GetInfoProductGrayFabricColorNameByIds(ctx, colorIds)
	gfList, _ := gfInfoSvcPB.GetGreyFabricInfoMapList(ctx, greyFabricIds)
	dicNameMap, _ := dicSvc.GetDictionaryNameByIds(ctx, dicIds)

	for _, gfmProduceReceiveOrder := range gfmProduceReceiveOrders.List() {
		o := structure.GetGfmProduceReceiveOrderData{}
		r.swapListModel2Data(gfmProduceReceiveOrder, &o, ctx)

		for _, src := range itemDatas {
			dst := structure.GetGfmProduceReceiveOrderItemData{}
			dst.Id = src.Id
			dst.CreateTime = tools.MyTime(src.CreateTime)
			dst.UpdateTime = tools.MyTime(src.UpdateTime)
			dst.CreatorId = src.CreatorId
			dst.CreatorName = src.CreatorName
			dst.UpdaterId = src.UpdaterId
			dst.UpdateUserName = src.UpdaterName
			dst.OrderNo = src.OrderNo
			dst.GreyFabricDeliveryId = src.GreyFabricDeliveryId
			dst.ProduceNoticeOrderNo = src.ProduceNoticeOrderNo
			dst.ProduceNoticeOrderItemId = src.ProduceNoticeOrderItemId
			if greyFabric, ok := gfList[src.GreyFabricId]; ok {
				dst.GreyFabricCode = greyFabric.Code
				dst.GreyFabricName = greyFabric.Name
				dst.UnitId = greyFabric.UnitId
				dst.UnitName = greyFabric.UnitName
				dst.WeightOfFabric = greyFabric.WeightOfFabric
			}
			dst.CustomerId = src.CustomerId
			dst.NeedleSize = src.NeedleSize
			dst.RawMaterialYarnName = src.RawMaterialYarnName
			dst.RawMaterialBatchNum = src.RawMaterialBatchNum
			dst.RawMaterialBatchBrand = src.RawMaterialBatchBrand
			dst.WeavingProcess = src.WeavingProcess
			dst.YarnBatch = src.YarnBatch
			dst.GrayFabricColorId = src.GrayFabricColorId
			dst.GreyFabricLevelId = src.GreyFabricLevelId
			dst.MachineNumber = src.MachineNumber
			dst.Roll = src.Roll
			dst.ReturnRoll = src.ReturnRoll
			dst.ProcessSinglePrice = src.ProcessSinglePrice
			dst.OtherPrice = src.OtherPrice
			dst.GreyFabricRemark = src.GreyFabricRemark
			dst.TotalPrice = src.TotalPrice
			dst.TotalWeight = src.TotalWeight
			dst.AvgWeight = src.AvgWeight
			dst.Remark = src.Remark
			dst.GreyFabricId = src.GreyFabricId
			dst.WarehouseSumId = src.WarehouseSumId

			dst.CustomerName = customerMap[dst.CustomerId]
			dst.GrayFabricColorName = colorMap[dst.GrayFabricColorId]
			dst.GreyFabricLevelName = levelMap[dst.GreyFabricLevelId]
			dst.SalePlanOrderItemId = src.SalePlanOrderItemId
			dst.SalePlanOrderItemNo = src.SalePlanOrderItemNo
			dst.ReferenceWeight = src.ReferenceWeight

			dst.BuildGFResp(src.GreyFabricWidth, src.GreyFabricGramWeight, dicNameMap[src.GreyFabricWidthUnitId][1],
				dicNameMap[src.GreyFabricGramWeightUnitId][1], src.GreyFabricWidthUnitId, src.GreyFabricGramWeightUnitId)

			// 添加细码信息
			tmpFineCodeList := fineCodeList.PickList(src.Id)
			for _, fineCode := range tmpFineCodeList {
				fineCodeGetData := structure.GetGfmProduceReceiveOrderItemFineCodeData{}
				r.swapFineCodeModel2Data(fineCode, &fineCodeGetData, ctx)
				fineCodeGetData.Position = dicNameMap[fineCode.WarehouseBinId][1]
				dst.ItemFCData = append(dst.ItemFCData, fineCodeGetData)
			}

			// 用纱信息赋值
			yarnDatas, err = mysql.FindGfmProduceReceiveOrderUseYarnByParenTID(r.tx, dst.Id)
			if err != nil {
				return
			}

			for _, yarnData := range yarnDatas {
				yarn := structure.GetGfmProduceReceiveOrderUseYarnData{}
				r.swapYarnData2GetData(yarnData, &yarn, ctx)
				yarnItemData, err = mysql.FindGfmProduceReceiveOrderUseYarnItemByParenTID(r.tx, yarn.Id)
				if err != nil {
					return
				}
				rmlIDS := yarnItemData.GetRMLIDs()
				rmlMap, _ := stockPBSvc.QueryStockRawMaterialByIds(ctx, r.tx, rmlIDS)
				for _, item := range yarnItemData {
					getItemData := structure.GetGfmProduceReceiveOrderUseYarnItemData{}
					r.swapYarnItemModel2Data(item, &getItemData, rmlMap)
					yarn.UseYarnItemData = append(yarn.UseYarnItemData, getItemData)
				}
				if yarn.UseYarnItemData == nil {
					yarn.UseYarnItemData = make(structure.GetGfmProduceReceiveOrderUseYarnItemDataList, 0)
				}
				dst.UseYarnData = append(dst.UseYarnData, yarn)
			}

			o.ItemData = append(o.ItemData, dst)
		}
		data = append(data, o)
	}

	return
}

// UpdateBuoyantWeightPrice 手动更新原料毛重成本
func (r *GfmProduceReceiveOrderRepo) UpdateBuoyantWeightPrice(ctx context.Context, param structure.UpdateBuoyantWeightPriceParam) error {
	useYarnItem, err := mysql.MustFirstGfmProduceReceiveOrderUseYarnItemByID(r.tx, param.UseYarnItemId)
	if err != nil {
		return err
	}
	useYarnItem.BuoyantWeightPrice = param.BuoyantWeightPrice
	_, err = mysql.MustUpdateGfmProduceReceiveOrderUseYarnItem(r.tx, useYarnItem)
	return err
}

// AutoUpdateBuoyantWeightPrice 自动更新原料库存的毛重成本
func (r *GfmProduceReceiveOrderRepo) AutoUpdateBuoyantWeightPrice(ctx context.Context, param structure.UpdateBuoyantWeightPriceParam) error {
	useYarnItemList, err := mysql.FindGfmProduceReceiveOrderUseYarnItemByParenTID(r.tx, param.UseYarnId)
	if err != nil {
		return err
	}
	rmStockIds := make([]uint64, 0, len(useYarnItemList))
	for _, item := range useYarnItemList {
		rmStockIds = append(rmStockIds, item.RmlStockId)
	}
	rmCostPriceRepo := aggsRm.NewRmCostPriceRepo(ctx, r.tx, false)
	rmCostPriceList, _, err := rmCostPriceRepo.GetList(ctx, &structureRm.GetRmCostPriceListQuery{RmStockIds: rmStockIds})
	if err != nil {
		return err
	}
	for _, item := range useYarnItemList {
		rmCostPrice := rmCostPriceList.PickByRmStockId(item.RmlStockId)
		if rmCostPrice.Id == 0 {
			continue
		}
		item.BuoyantWeightPrice = rmCostPrice.BuoyantWeightPrice
		_, err = mysql.MustUpdateGfmProduceReceiveOrderUseYarnItem(r.tx, item)
		if err != nil {
			return err
		}
	}
	return err
}
