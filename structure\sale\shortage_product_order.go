package sale

import (
	common "hcscm/common/sale"
	common_system "hcscm/common/system_consts"
	"hcscm/structure/product"
	structure_base "hcscm/structure/system"
	"hcscm/tools"
)

type AddShortageProductOrderParam struct {
	structure_base.Param
	SaleSystemId          uint64                                 `json:"sale_system_id"`            // 营销体系ID
	VoucherNumber         string                                 `json:"voucher_number"`            // 凭证单号
	OrderTime             tools.QueryTime                        `json:"order_time"`                // 订单日期
	SendProductType       common.SendProductType                 `json:"send_product_type"`         // 出货类型 1出货 2销调
	WarehouseId           uint64                                 `json:"warehouse_id"`              // 调入仓库id
	CustomerId            uint64                                 `json:"customer_id"`               // 所属客户id
	SaleUserId            uint64                                 `json:"sale_user_id"`              // 销售员id
	SaleFollowerId        uint64                                 `json:"sale_follower_id"`          // 销售跟单员id
	SettleType            common.SettleType                      `json:"settle_type"`               // 结算类型
	ProcessFactoryId      uint64                                 `json:"process_factory_id"`        // 加工厂id
	Contacts              string                                 `json:"contacts"`                  // 联系人
	ContactPhone          string                                 `json:"contact_phone"`             // 联系电话
	PrintTag              string                                 `json:"print_tag"`                 // 打印标签(出货标签)
	LogisticsCompanyId    uint64                                 `json:"logistics_company_id"`      // 物流公司id
	LogisticsArea         string                                 `json:"logistics_area"`            // 物流区域
	ReceiptAddress        string                                 `json:"receipt_address"`           // 收货地址
	InfoSaleTaxableItemId uint64                                 `json:"info_sale_taxable_item_id"` // 含税项目id
	SaleTaxRate           int                                    `json:"sale_tax_rate"`             // 销售税率
	PostageItems          common.PostageItems                    `json:"postage_items"`             // 邮费项目 1包邮 2不包邮
	SaleProductOrderId    uint64                                 `json:"sale_product_order_id"`     // 成品销售单id
	SaleProductOrderNo    string                                 `json:"sale_product_order_no"`     // 成品销售单号
	InternalRemark        string                                 `json:"internal_remark"`           // 内部备注
	SendProductRemark     string                                 `json:"send_product_remark"`       // 出货备注
	SaleGroupId           uint64                                 `json:"sale_group_id"`             // 销售群体id
	IsWithTaxRate         bool                                   `json:"is_with_tax_rate"`          // 单价是否含税
	ItemData              AddShortageProductOrderDetailParamList `json:"item_data"`                 // 成品欠货单成品详情
}

type AddShortageProductOrderParamList []AddShortageProductOrderParam

func (r AddShortageProductOrderParamList) Adjust() {

}

type AddShortageProductOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type UpdateShortageProductOrderParam struct {
	structure_base.Param
	Id                    uint64                                 `json:"id"`
	SaleSystemId          uint64                                 `json:"sale_system_id"`            // 营销体系ID
	VoucherNumber         string                                 `json:"voucher_number"`            // 凭证单号
	OrderTime             tools.QueryTime                        `json:"order_time"`                // 订单日期
	SendProductType       common.SendProductType                 `json:"send_product_type"`         // 出货类型 1出货 2销调
	WarehouseId           uint64                                 `json:"warehouse_id"`              // 调入仓库id
	CustomerId            uint64                                 `json:"customer_id"`               // 所属客户id
	SaleUserId            uint64                                 `json:"sale_user_id"`              // 销售员id
	SaleFollowerId        uint64                                 `json:"sale_follower_id"`          // 销售跟单员id
	SettleType            common.SettleType                      `json:"settle_type"`               // 结算类型
	ProcessFactoryId      uint64                                 `json:"process_factory_id"`        // 加工厂id
	Contacts              string                                 `json:"contacts"`                  // 联系人
	ContactPhone          string                                 `json:"contact_phone"`             // 联系电话
	PrintTag              string                                 `json:"print_tag"`                 // 打印标签(出货标签)
	LogisticsCompanyId    uint64                                 `json:"logistics_company_id"`      // 物流公司id
	LogisticsArea         string                                 `json:"logistics_area"`            // 物流区域
	ReceiptAddress        string                                 `json:"receipt_address"`           // 收货地址
	InfoSaleTaxableItemId uint64                                 `json:"info_sale_taxable_item_id"` // 含税项目id
	SaleTaxRate           int                                    `json:"sale_tax_rate"`             // 销售税率
	PostageItems          common.PostageItems                    `json:"postage_items"`             // 邮费项目 1包邮 2不包邮
	SaleProductOrderId    uint64                                 `json:"sale_product_order_id"`     // 成品销售单id
	SaleProductOrderNo    string                                 `json:"sale_product_order_no"`     // 成品销售单号
	OrderNo               string                                 `json:"order_no"`                  // 成品欠货单号
	Number                int                                    `json:"number"`                    // 编号
	AuditStatus           common_system.OrderStatus              `json:"audit_status"`              // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId          uint64                                 `json:"department_id"`             // 下单用户所属部门
	CompanyId             uint64                                 `json:"company_id"`                // 公司ID
	AuditorId             uint64                                 `json:"auditor_id"`                // 审核人ID
	AuditDate             tools.QueryTime                        `json:"audit_date"`                // 审核时间
	BusinessClose         common_system.BusinessClose            `json:"business_close"`            // 业务关闭
	BusinessCloseUserId   uint64                                 `json:"business_close_user_id"`    // 业务关闭操作人
	BusinessCloseTime     tools.QueryTime                        `json:"business_close_time"`       // 业务关闭时间
	InternalRemark        string                                 `json:"internal_remark"`           // 内部备注
	SendProductRemark     string                                 `json:"send_product_remark"`       // 出货备注
	SaleGroupId           uint64                                 `json:"sale_group_id"`             // 销售群体id
	IsWithTaxRate         bool                                   `json:"is_with_tax_rate"`          // 单价是否含税
	ItemData              AddShortageProductOrderDetailParamList `json:"item_data"`                 // 成品欠货单成品详情
}

type UpdateShortageProductOrderData struct {
	structure_base.ResponseData
	Id uint64 `json:"id"`
}

type DeleteShortageProductOrderParam struct {
	structure_base.Param
	DeleteRemark string             `json:"delete_remark,optional"`
	Id           tools.QueryIntList `json:"id" form:"id"`
}

type DeleteShortageProductOrderData struct {
	structure_base.ResponseData
	Id []uint64 `json:"id"`
}

type UpdateShortageProductOrderAuditStatusParam struct {
	structure_base.Param
	Id tools.QueryIntList `json:"id"`
}

type UpdateShortageProductOrderAuditStatusData struct {
	structure_base.ResponseData
}

type UpdateShortageProductOrderBusinessCloseParam struct {
	structure_base.Param
	Id            tools.QueryIntList          `json:"id"`             // id，逗号拼接
	BusinessClose common_system.BusinessClose `json:"business_close"` // 业务关闭 1.正常 2。关闭
}

type UpdateShortageProductOrderBusinessCloseData struct {
	structure_base.ResponseData
}

type ExistOrderQuery struct {
	structure_base.ListQuery
	SaleProductOrderId uint64 `form:"sale_product_order_id"` // 成品销售单id
}

type GetShortageProductOrderQuery struct {
	structure_base.Query
	Id uint64 `form:"id"` // id
}

type GetShortageProductOrderListQuery struct {
	structure_base.ListQuery
	SaleSystemId          uint64                      `form:"sale_system_id"`                        // 营销体系ID
	VoucherNumber         string                      `form:"voucher_number"`                        // 凭证单号
	StartOrderTime        tools.QueryTime             `form:"start_order_time"`                      // 订单日期
	EndOrderTime          tools.QueryTime             `form:"end_order_time"`                        // 订单日期
	SendProductType       common.SendProductType      `form:"send_product_type"`                     // 出货类型 1出货 2销调
	WarehouseId           uint64                      `form:"warehouse_id"`                          // 调入仓库id
	CustomerId            uint64                      `form:"customer_id"`                           // 所属客户id
	SaleCustomerId        uint64                      `form:"sale_customer_id" relate:"customer_id"` // 购买成品客户id
	SaleUserId            uint64                      `form:"sale_user_id"`                          // 销售员id
	SaleFollowerId        uint64                      `form:"sale_follower_id"`                      // 销售跟单员id
	SettleType            common.SettleType           `form:"settle_type"`                           // 结算类型
	ProcessFactoryId      uint64                      `form:"process_factory_id"`                    // 加工厂id
	Contacts              string                      `form:"contacts"`                              // 联系人
	ContactPhone          string                      `form:"contact_phone"`                         // 联系电话
	PrintTag              string                      `form:"print_tag"`                             // 打印标签(出货标签)
	LogisticsCompanyId    uint64                      `form:"logistics_company_id"`                  // 物流公司id
	LogisticsArea         string                      `form:"logistics_area"`                        // 物流区域
	ReceiptAddress        string                      `form:"receipt_address"`                       // 收货地址
	InfoSaleTaxableItemId uint64                      `form:"info_sale_taxable_item_id"`             // 含税项目id
	SaleTaxRate           int                         `form:"sale_tax_rate"`                         // 销售税率
	PostageItems          common.PostageItems         `form:"postage_items"`                         // 邮费项目 1包邮 2不包邮
	SaleProductOrderId    uint64                      `form:"sale_product_order_id"`                 // 成品销售单id
	SaleProductOrderNo    string                      `form:"sale_product_order_no"`                 // 成品销售单号
	OrderNo               string                      `form:"order_no"`                              // 成品欠货单号
	AuditStatus           tools.QueryIntList          `form:"audit_status"`                          // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId          uint64                      `form:"department_id"`                         // 下单用户所属部门
	CompanyId             uint64                      `form:"company_id"`                            // 公司ID
	AuditorId             uint64                      `form:"auditor_id"`                            // 审核人ID
	BusinessClose         common_system.BusinessClose `form:"business_close"`                        // 业务关闭
	InternalRemark        string                      `form:"internal_remark"`                       // 内部备注
	SendProductRemark     string                      `form:"send_product_remark"`                   // 出货备注
	ProductId             uint64                      `form:"product_id"`                            // 成品id
	SaleGroupId           uint64                      `form:"sale_group_id"`                         // 销售群体id
	UpdateUserId          uint64                      `form:"updater_id"`                            // 最后修改人
	CreateTimeStart       tools.QueryTime             `form:"start_create_time"`                     // 创建时间开始
	CreateTimeEnd         tools.QueryTime             `form:"end_create_time"`                       // 创建时间结束
	AuditTimeStart        tools.QueryTime             `form:"start_audit_date"`                      // 审核时间开始
	AuditTimeEnd          tools.QueryTime             `form:"end_audit_date"`                        // 审核时间结束
	UpdateTimeStart       tools.QueryTime             `form:"start_update_time"`                     // 更新时间开始
	UpdateTimeEnd         tools.QueryTime             `form:"end_update_time"`                       // 更新时间结束
	OrderTimeStart        tools.QueryTime             `form:"start_order_time"`                      // 订单时间开始
	OrderTimeEnd          tools.QueryTime             `form:"end_order_time"`                        // 订单时间结束
}

func (r GetShortageProductOrderListQuery) Adjust() {

}

type GetShortageProductOrderData struct {
	structure_base.RecordData
	SaleSystemId            uint64                                `json:"sale_system_id"`                           // 营销体系ID
	SaleSystemName          string                                `json:"sale_system_name" excel:"营销体系"`            // 营销体系ID名称
	VoucherNumber           string                                `json:"voucher_number" excel:"凭证单号"`              // 凭证单号
	OrderTime               tools.MyTime                          `json:"order_time" excel:"订单日期"`                  // 订单日期
	SendProductType         common.SendProductType                `json:"send_product_type"`                        // 出货类型 1出货 2销调
	SendProductTypeName     string                                `json:"send_product_type_name" excel:"出货类型"`      // 出货类型 1出货 2销调
	WarehouseId             uint64                                `json:"warehouse_id"`                             // 调入仓库id
	WarehouseName           string                                `json:"warehouse_name" excel:"调入仓库"`              // 调入仓库名称
	CustomerId              uint64                                `json:"customer_id"`                              // 所属客户id
	CustomerCode            string                                `json:"customer_code"`                            // 所属客户编号
	CustomerName            string                                `json:"customer_name" excel:"所属客户"`               // 所属客户名称
	SaleUserId              uint64                                `json:"sale_user_id"`                             // 销售员id
	SaleUserName            string                                `json:"sale_user_name" excel:"销售员"`               // 销售员名称
	SaleFollowerId          uint64                                `json:"sale_follower_id"`                         // 销售跟单员id
	SaleFollowerName        string                                `json:"sale_follower_name" excel:"销售跟单员"`         // 销售跟单员名称
	SettleType              common.SettleType                     `json:"settle_type"`                              // 结算类型
	SettleTypeName          string                                `json:"settle_type_name" excel:"结算类型"`            // 结算类型
	ProcessFactoryId        uint64                                `json:"process_factory_id"`                       // 加工厂id
	ProcessFactoryName      string                                `json:"process_factory_name" excel:"加工厂"`         // 加工厂名称
	Contacts                string                                `json:"contacts" excel:"联系人"`                     // 联系人
	ContactPhone            string                                `json:"contact_phone" excel:"联系电话"`               // 联系电话
	PrintTag                string                                `json:"print_tag" excel:"打印标签"`                   // 打印标签(出货标签)
	LogisticsCompanyId      uint64                                `json:"logistics_company_id"`                     // 物流公司id
	LogisticsCompanyName    string                                `json:"logistics_company_name" excel:"物流公司"`      // 物流公司名称
	LogisticsArea           string                                `json:"logistics_area" excel:"物流区域"`              // 物流区域
	ReceiptAddress          string                                `json:"receipt_address" excel:"收货地址"`             // 收货地址
	InfoSaleTaxableItemId   uint64                                `json:"info_sale_taxable_item_id"`                // 含税项目id
	InfoSaleTaxableItemName string                                `json:"info_sale_taxable_item_name" excel:"含税项目"` // 含税项目名称
	SaleTaxRate             tools.Hundred                         `json:"sale_tax_rate" excel:"销售税率"`               // 销售税率
	PostageItems            common.PostageItems                   `json:"postage_items"`                            // 邮费项目 1包邮 2不包邮
	PostageItemsName        string                                `json:"postage_items_name" excel:"邮费项目"`          // 邮费项目 1包邮 2不包邮
	SaleProductOrderId      uint64                                `json:"sale_product_order_id"`                    // 成品销售单id
	SaleProductOrderNo      string                                `json:"sale_product_order_no" excel:"成品销售单号"`     // 成品销售单号
	OrderNo                 string                                `json:"order_no" excel:"成品欠货单号"`                  // 成品欠货单号
	AuditStatus             common_system.OrderStatus             `json:"audit_status"`                             // 订单状态 1待审核 2已审核 3已驳回 4已作废
	AuditStatusName         string                                `json:"audit_status_name" excel:"订单状态"`           // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId            uint64                                `json:"department_id"`                            // 下单用户所属部门
	DepartmentName          string                                `json:"department_name"`                          // 下单用户所属部门名称
	CompanyId               uint64                                `json:"company_id"`                               // 公司ID
	CompanyName             string                                `json:"company_name"`                             // 公司ID名称
	AuditorId               uint64                                `json:"auditor_id"`                               // 审核人ID
	AuditorName             string                                `json:"auditor_name"`                             // 审核人ID名称
	AuditDate               tools.MyTime                          `json:"audit_date"`                               // 审核时间
	BusinessClose           common_system.BusinessClose           `json:"business_close"`                           // 业务关闭
	BusinessCloseName       string                                `json:"business_close_name"`                      // 业务关闭
	BusinessCloseUserId     uint64                                `json:"business_close_user_id"`                   // 业务关闭操作人
	BusinessCloseUserName   string                                `json:"business_close_user_name"`                 // 业务关闭操作人名称
	BusinessCloseTime       tools.MyTime                          `json:"business_close_time"`                      // 业务关闭时间
	InternalRemark          string                                `json:"internal_remark" excel:"内部备注"`             // 内部备注
	SendProductRemark       string                                `json:"send_product_remark" excel:"出货备注"`         // 出货备注
	SaleGroupId             uint64                                `json:"sale_group_id"`                            // 销售群体id
	SaleGroupName           string                                `json:"sale_group_name" excel:"销售群体"`             // 销售群体名称
	IsWithTaxRate           bool                                  `json:"is_with_tax_rate" excel:"单价是否含税"`          // 单价是否含税
	ItemData                GetShortageProductOrderDetailDataList `json:"item_data"`                                // 成品欠货单成品详情
}

type GetShortageProductOrderDataList []GetShortageProductOrderData

func (g GetShortageProductOrderDataList) Adjust() {

}

type GetShortageProductOrderDropdownData struct {
	structure_base.RecordData
	SaleSystemId            uint64                      `json:"sale_system_id"`              // 营销体系ID
	SaleSystemName          string                      `json:"sale_system_name"`            // 营销体系ID名称
	VoucherNumber           string                      `json:"voucher_number"`              // 凭证单号
	OrderTime               tools.MyTime                `json:"order_time"`                  // 订单日期
	SendProductType         common.SendProductType      `json:"send_product_type"`           // 出货类型 1出货 2销调
	SendProductTypeName     string                      `json:"send_product_type_name"`      // 出货类型 1出货 2销调
	WarehouseId             uint64                      `json:"warehouse_id"`                // 调入仓库id
	WarehouseName           string                      `json:"warehouse_name"`              // 调入仓库名称
	CustomerId              uint64                      `json:"customer_id"`                 // 所属客户id
	CustomerCode            string                      `json:"customer_code"`               // 所属客户编号
	CustomerName            string                      `json:"customer_name"`               // 所属客户名称
	SaleUserId              uint64                      `json:"sale_user_id"`                // 销售员id
	SaleUserName            string                      `json:"sale_user_name"`              // 销售员名称
	SaleFollowerId          uint64                      `json:"sale_follower_id"`            // 销售跟单员id
	SaleFollowerName        string                      `json:"sale_follower_name"`          // 销售跟单员名称
	SettleType              common.SettleType           `json:"settle_type"`                 // 结算类型
	SettleTypeName          string                      `json:"settle_type_name"`            // 结算类型
	ProcessFactoryId        uint64                      `json:"process_factory_id"`          // 加工厂id
	ProcessFactoryName      string                      `json:"process_factory_name"`        // 加工厂名称
	Contacts                string                      `json:"contacts"`                    // 联系人
	ContactPhone            string                      `json:"contact_phone"`               // 联系电话
	PrintTag                string                      `json:"print_tag"`                   // 打印标签(出货标签)
	LogisticsCompanyId      uint64                      `json:"logistics_company_id"`        // 物流公司id
	LogisticsCompanyName    string                      `json:"logistics_company_name"`      // 物流公司名称
	LogisticsArea           string                      `json:"logistics_area"`              // 物流区域
	ReceiptAddress          string                      `json:"receipt_address"`             // 收货地址
	InfoSaleTaxableItemId   uint64                      `json:"info_sale_taxable_item_id"`   // 含税项目id
	InfoSaleTaxableItemName string                      `json:"info_sale_taxable_item_name"` // 含税项目名称
	SaleTaxRate             int                         `json:"sale_tax_rate"`               // 销售税率
	PostageItems            common.PostageItems         `json:"postage_items"`               // 邮费项目 1包邮 2不包邮
	PostageItemsName        string                      `json:"postage_items_name"`          // 邮费项目 1包邮 2不包邮
	SaleProductOrderId      uint64                      `json:"sale_product_order_id"`       // 成品销售单id
	SaleProductOrderNo      string                      `json:"sale_product_order_no"`       // 成品销售单号
	OrderNo                 string                      `json:"order_no"`                    // 成品欠货单号
	AuditStatus             common_system.OrderStatus   `json:"audit_status"`                // 订单状态 1待审核 2已审核 3已驳回 4已作废
	AuditStatusName         string                      `json:"audit_status_name"`           // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId            uint64                      `json:"department_id"`               // 下单用户所属部门
	DepartmentName          string                      `json:"department_name"`             // 下单用户所属部门名称
	CompanyId               uint64                      `json:"company_id"`                  // 公司ID
	CompanyName             string                      `json:"company_name"`                // 公司ID名称
	AuditorId               uint64                      `json:"auditor_id"`                  // 审核人ID
	AuditorName             string                      `json:"auditor_name"`                // 审核人ID名称
	AuditDate               tools.MyTime                `json:"audit_date"`                  // 审核时间
	BusinessClose           common_system.BusinessClose `json:"business_close"`              // 业务关闭
	BusinessCloseName       string                      `json:"business_close_name"`         // 业务关闭
	BusinessCloseUserId     uint64                      `json:"business_close_user_id"`      // 业务关闭操作人
	BusinessCloseUserName   string                      `json:"business_close_user_name"`    // 业务关闭操作人名称
	BusinessCloseTime       tools.MyTime                `json:"business_close_time"`         // 业务关闭时间
	InternalRemark          string                      `json:"internal_remark"`             // 内部备注
	SendProductRemark       string                      `json:"send_product_remark"`         // 出货备注
	SaleGroupId             uint64                      `json:"sale_group_id"`               // 销售群体id
	SaleGroupName           string                      `json:"sale_group_name"`             // 销售群体名称
}

type GetShortageProductOrderDropdownDataList []GetShortageProductOrderDropdownData

func (g GetShortageProductOrderDropdownDataList) Adjust() {

}

type GetShortageProductOrderDetailDropdownData struct {
	structure_base.RecordData
	structure_base.FinishProductWidthAndWightUnit
	SaleProductOrderId      uint64                      `json:"sale_product_order_id"`       // 成品销售单id
	SaleProductOrderNo      string                      `json:"sale_product_order_no"`       // 成品销售单号
	SaleSystemId            uint64                      `json:"sale_system_id"`              // 营销体系ID
	SaleSystemName          string                      `json:"sale_system_name"`            // 营销体系ID名称
	OrderNo                 string                      `json:"order_no"`                    // 成品欠货单号
	VoucherNumber           string                      `json:"voucher_number"`              // 凭证单号
	OrderTime               tools.MyTime                `json:"order_time"`                  // 订单日期
	CustomerId              uint64                      `json:"customer_id"`                 // 所属客户id
	CustomerCode            string                      `json:"customer_code"`               // 所属客户编号
	CustomerName            string                      `json:"customer_name"`               // 所属客户名称
	SaleUserId              uint64                      `json:"sale_user_id"`                // 销售员id
	SaleUserName            string                      `json:"sale_user_name"`              // 销售员名称
	WarehouseId             uint64                      `json:"warehouse_id"`                // 调入仓库id
	WarehouseName           string                      `json:"warehouse_name"`              // 调入仓库名称
	SendProductType         common.SendProductType      `json:"send_product_type"`           // 出货类型 1出货 2销调
	SendProductTypeName     string                      `json:"send_product_type_name"`      // 出货类型 1出货 2销调
	SaleFollowerId          uint64                      `json:"sale_follower_id"`            // 销售跟单员id
	SaleFollowerName        string                      `json:"sale_follower_name"`          // 销售跟单员名称
	SettleType              common.SettleType           `json:"settle_type"`                 // 结算类型
	SettleTypeName          string                      `json:"settle_type_name"`            // 结算类型
	ProcessFactoryId        uint64                      `json:"process_factory_id"`          // 加工厂id
	ProcessFactoryName      string                      `json:"process_factory_name"`        // 加工厂名称
	Contacts                string                      `json:"contacts"`                    // 联系人
	ContactPhone            string                      `json:"contact_phone"`               // 联系电话
	PrintTag                string                      `json:"print_tag"`                   // 打印标签(出货标签)
	LogisticsCompanyId      uint64                      `json:"logistics_company_id"`        // 物流公司id
	LogisticsCompanyName    string                      `json:"logistics_company_name"`      // 物流公司名称
	LogisticsArea           string                      `json:"logistics_area"`              // 物流区域
	ReceiptAddress          string                      `json:"receipt_address"`             // 收货地址
	InfoSaleTaxableItemId   uint64                      `json:"info_sale_taxable_item_id"`   // 含税项目id
	InfoSaleTaxableItemName string                      `json:"info_sale_taxable_item_name"` // 含税项目名称
	SaleTaxRate             int                         `json:"sale_tax_rate"`               // 销售税率
	PostageItems            common.PostageItems         `json:"postage_items"`               // 邮费项目 1包邮 2不包邮
	PostageItemsName        string                      `json:"postage_items_name"`          // 邮费项目 1包邮 2不包邮
	AuditStatus             common_system.OrderStatus   `json:"audit_status"`                // 订单状态 1待审核 2已审核 3已驳回 4已作废
	AuditStatusName         string                      `json:"audit_status_name"`           // 订单状态 1待审核 2已审核 3已驳回 4已作废
	DepartmentId            uint64                      `json:"department_id"`               // 下单用户所属部门
	DepartmentName          string                      `json:"department_name"`             // 下单用户所属部门名称
	CompanyId               uint64                      `json:"company_id"`                  // 公司ID
	CompanyName             string                      `json:"company_name"`                // 公司ID名称
	AuditorId               uint64                      `json:"auditor_id"`                  // 审核人ID
	AuditorName             string                      `json:"auditor_name"`                // 审核人ID名称
	AuditDate               tools.MyTime                `json:"audit_date"`                  // 审核时间
	BusinessClose           common_system.BusinessClose `json:"business_close"`              // 业务关闭
	BusinessCloseName       string                      `json:"business_close_name"`         // 业务关闭
	BusinessCloseUserId     uint64                      `json:"business_close_user_id"`      // 业务关闭操作人
	BusinessCloseUserName   string                      `json:"business_close_user_name"`    // 业务关闭操作人名称
	BusinessCloseTime       tools.MyTime                `json:"business_close_time"`         // 业务关闭时间
	InternalRemark          string                      `json:"internal_remark"`             // 内部备注
	SendProductRemark       string                      `json:"send_product_remark"`         // 出货备注
	StockProductId          uint64                      `json:"stock_product_id"`            // 库存汇总id
	ShortageProductOrderId  uint64                      `json:"shortage_product_order_id"`   // 成品欠货单id
	ProductId               uint64                      `json:"product_id"`                  // 成品id
	ProductCode             string                      `json:"product_code"`                // 面料编号
	ProductName             string                      `json:"product_name"`                // 成品名称
	ProductCustomerId       uint64                      `json:"product_customer_id"`         // 成品所属客户id
	ProductCustomerCode     string                      `json:"product_customer_code"`       // 成品所属客户编号
	ProductCustomerName     string                      `json:"product_customer_name"`       // 成品所属客户名称
	CustomerAccountNum      string                      `json:"customer_account_num"`        // 客户款号
	ProductColorId          uint64                      `json:"product_color_id"`            // 颜色id
	ProductColorCode        string                      `json:"product_color_code"`          // 颜色编号(色号)
	ProductColorName        string                      `json:"product_color_name"`          // 颜色名称
	ProductLevelId          uint64                      `json:"product_level_id"`            // 成品等级id
	ProductLevelName        string                      `json:"product_level_name"`          // 成品等级名称
	DyelotNumber            string                      `json:"dyelot_number"`               // 缸号
	ProductRemark           string                      `json:"product_remark"`              // 成品备注
	MeasurementUnitId       uint64                      `json:"measurement_unit_id"`         // 计量单位id
	MeasurementUnitName     string                      `json:"measurement_unit_name"`       // 计量单位名称
	Roll                    int                         `json:"roll"`                        // 匹数
	Weight                  int                         `json:"weight"`                      // 数量
	Length                  int                         `json:"length"`                      // 长度
	StandardWeight          int                         `json:"standard_weight"`             // 标准数量
	ProductKindId           uint64                      `json:"product_kind_id"`             // 布种类型id(关联type_grey_fabric_id)
	ProductKindName         string                      `json:"product_kind_name"`           // 布种类型id(关联type_grey_fabric_id)名称
	ProductColorKindId      uint64                      `json:"product_color_kind_id"`       // 颜色类别id(关联type_finished_product_kind_id)
	ProductColorKindName    string                      `json:"product_color_kind_name"`     // 颜色类别id(关联type_finished_product_kind_id)名称
	ShortageRoll            int                         `json:"shortage_roll"`               // 欠货匹数
	ShortageWeight          int                         `json:"shortage_weight"`             // 欠货数量
	ShortageLength          int                         `json:"shortage_length"`             // 欠货长度
	Remark                  string                      `json:"remark"`                      // 备注
	GreyFabricId            uint64                      `json:"grey_fabric_id"`              // 坯布信息ID
	GreyFabricCode          string                      `json:"grey_fabric_code"`            // 坯布信息编号
	GreyFabricName          string                      `json:"grey_fabric_name"`            // 坯布信息名称
	SaleGroupId             uint64                      `json:"sale_group_id"`               // 销售群体id
	SaleGroupName           string                      `json:"sale_group_name"`             // 销售群体名称
	product.SalePrice
}

type GetShortageProductOrderDetailDropdownDataList []GetShortageProductOrderDetailDropdownData

func (g GetShortageProductOrderDetailDropdownDataList) Adjust() {

}
