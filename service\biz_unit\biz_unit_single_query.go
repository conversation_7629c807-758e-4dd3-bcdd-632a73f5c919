package biz_unit

import (
	"context"
	"encoding/json"
	"github.com/gin-gonic/gin"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/system"
	"hcscm/tools"
)

// 剔除重复请求
type BizUnitQuerySingleService struct {
	saleGroupQueryService *SaleGroupQueryService
	saleAreaQueryService  *SaleAreaQueryService
	ginCtx                *gin.Context
	single                tools.SingleFlight
}

func NewBizUnitQuerySingleService(ginCtx *gin.Context) *BizUnitQuerySingleService {
	return &BizUnitQuerySingleService{
		saleGroupQueryService: NewSaleGroupQueryService(),
		saleAreaQueryService:  NewSaleAreaQueryService(),
		single:                tools.Single,
		ginCtx:                ginCtx,
	}
}

// 列表
func (s *BizUnitQuerySingleService) QueryBizUnitList(ctx context.Context, tx *mysql_base.Tx, req *structure.GetBizUnitListParams) (list []*structure.GetBizUnitListItem, total int, err error) {
	var (
		queryByte []byte
		val       interface{}
	)
	queryByte, err = json.Marshal(req)
	if err != nil {
		return
	}

	val, err = s.single.Do(tools.GetRequestKey(s.ginCtx, queryByte), func() (res interface{}, err error) {
		var (
			data []*structure.GetBizUnitListItem
		)
		svc := NewBizUnitQueryService()
		data, total, err = svc.QueryBizUnitList(ctx, tx, req)
		if err != nil {
			return
		}
		return data, nil
	})
	if err != nil {
		return
	}
	return val.([]*structure.GetBizUnitListItem), total, nil
}

// 详情
func (s *BizUnitQuerySingleService) QueryBizUnitDetail(ctx context.Context, tx *mysql_base.Tx, id uint64) (data *structure.GetBizUnitDetailResponse, err error) {
	var (
		queryByte []byte
		val       interface{}
	)
	queryByte, err = json.Marshal(id)
	if err != nil {
		return
	}

	val, err = s.single.Do(tools.GetRequestKey(s.ginCtx, queryByte), func() (res interface{}, err error) {
		var (
			data *structure.GetBizUnitDetailResponse
		)
		svc := NewBizUnitQueryService()
		data, err = svc.QueryBizUnitDetail(ctx, tx, &QueryBizUnitDetailReq{Id: id})
		if err != nil {
			return
		}
		return data, nil
	})
	if err != nil {
		return
	}
	return val.(*structure.GetBizUnitDetailResponse), nil
}
