package product

import (
	"context"
	cus_const "hcscm/common/product"
	common_system "hcscm/common/system_consts"
	"hcscm/model/mysql/mysql_base"
	structure "hcscm/structure/product"
	"hcscm/tools"
	"hcscm/vars"
	"time"

	"gorm.io/gorm"

	"hcscm/common/errors"
)

func GetFpmOutOrderItemFcIdList(objects interface{}) (ids []uint64) {
	return mysql_base.GetUInt64List(objects, "fpm_out_order_item_fc_id")
}

type FpmOutOrderItemFcList []FpmOutOrderItemFc

func (r FpmOutOrderItemFcList) List() []FpmOutOrderItemFc {
	return r
}

func (r FpmOutOrderItemFcList) GetIds() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.Id)
	}
	return o
}

func (r FpmOutOrderItemFcList) GetStockIDs() []uint64 {
	o := make([]uint64, 0)
	for _, t := range r {
		o = append(o, t.StockId)
	}
	return o
}

func (r FpmOutOrderItemFcList) One() FpmOutOrderItemFc {
	return r[0]
}

func (r FpmOutOrderItemFcList) Pick(id uint64) (o FpmOutOrderItemFc) {
	for _, t := range r {
		if t.Id == id {
			return t
		}
	}
	return
}

func (r FpmOutOrderItemFcList) PickFcListByParentId(pid uint64) (o FpmOutOrderItemFcList) {
	list := make(FpmOutOrderItemFcList, 0)
	for _, t := range r {
		if t.ParentId == pid {
			list = append(list, t)
		}
	}
	return list
}

func (r FpmOutOrderItemFcList) PickByStockDetailId(pid uint64) (o FpmOutOrderItemFcList) {
	list := make(FpmOutOrderItemFcList, 0)
	for _, t := range r {
		if t.StockId == pid {
			list = append(list, t)
		}
	}
	return list
}

func (r FpmOutOrderItemFcList) GetTotalRWL() []int {
	var (
		tr int
		tw int
		tl int
		o  = make([]int, 3)
	)
	for _, t := range r {
		tr += t.Roll
		tw += t.BaseUnitWeight
		tl += t.Length
	}
	o[0] = tr
	o[1] = tw
	o[2] = tl
	return o
}

// FpmOutOrderItemFc 出仓细码
type FpmOutOrderItemFc struct {
	mysql_base.ModelHard
	Id                            uint64                         `gorm:"column:id;primaryKey"`
	ParentId                      uint64                         `gorm:"column:parent_id"`                                                        // 父id（成品信息行id）
	Roll                          int                            `gorm:"column:roll"`                                                             // 条数(条)，乘100存
	WarehouseId                   uint64                         `gorm:"column:warehouse_id"`                                                     // 仓库id
	WarehouseBinId                uint64                         `gorm:"column:warehouse_bin_id" relate:"warehouse_bin_id"`                       // 仓号
	VolumeNumber                  int                            `gorm:"column:volume_number"`                                                    // 卷号
	WarehouseOutType              cus_const.WarehouseGoodOutType `gorm:"column:warehouse_out_type"`                                               // 出仓类型
	WarehouseOutOrderId           uint64                         `gorm:"column:warehouse_out_order_id"`                                           // 出仓单id
	WarehouseOutOrderNo           string                         `gorm:"column:warehouse_out_order_no"`                                           // 出仓单号
	WarehouseInType               cus_const.WarehouseGoodInType  `gorm:"column:warehouse_in_type"`                                                // 来源类型
	WarehouseInOrderId            uint64                         `gorm:"column:warehouse_in_order_id"`                                            // 进仓单id
	WarehouseInOrderNo            string                         `gorm:"column:warehouse_in_order_no"`                                            // 进仓单号
	ArrangeOrderNo                string                         `gorm:"column:arrange_order_no"`                                                 // 配布单号
	StockId                       uint64                         `gorm:"column:stock_id" relate:"stock_id"`                                       // 库存成品id
	SumStockId                    uint64                         `gorm:"column:sum_stock_id"`                                                     // 汇总库存成品id
	BaseUnitWeight                int                            `gorm:"column:base_unit_weight"`                                                 // 基本单位数量(公斤)，乘10000存
	PaperTubeWeight               int                            `gorm:"column:paper_tube_weight"`                                                // 纸筒数量(公斤)，乘10000存
	WeightError                   int                            `gorm:"column:weight_error"`                                                     // 空差数量(公斤)，乘10000存
	ActuallyWeight                int                            `gorm:"column:actually_weight"`                                                  // 码单数量
	SettleErrorWeight             int                            `gorm:"column:settle_error_weight"`                                              // 结算空差数量
	UnitId                        uint64                         `gorm:"column:unit_id" relate:"measurement_unit_id"`                             // 单位id（kg）
	AuxiliaryUnitId               uint64                         `gorm:"column:auxiliary_unit_id" relate:"auxiliary_unit_id"`                     // 辅助单位id（米/用于判断计算金额时使用哪个数量）
	Length                        int                            `gorm:"column:length"`                                                           // 长度，乘100存
	SettleWeight                  int                            `gorm:"column:settle_weight"`                                                    // 结算数量(公斤)，乘10000存
	SettleLength                  int                            `gorm:"column:settle_length"`                                                    // 结算长度(米),乘100存
	DigitalCode                   string                         `gorm:"column:digital_code"`                                                     // 数字码
	ShelfNo                       string                         `gorm:"column:shelf_no"`                                                         // 货架号
	ContractNumber                string                         `gorm:"column:contract_number"`                                                  // 合同号
	CustomerPoNum                 string                         `gorm:"column:customer_po_num"`                                                  // 客户po号
	AccountNum                    string                         `gorm:"column:account_num"`                                                      // 客户款号
	DyeFactoryColorCode           string                         `gorm:"column:dye_factory_color_code"`                                           // 染厂色号
	DyeFactoryDyelotNumber        string                         `gorm:"column:dye_factory_dyelot_number"`                                        // 染厂缸号
	ProductWidth                  string                         `gorm:"column:product_width"`                                                    // 成品幅宽
	ProductGramWeight             string                         `gorm:"column:product_gram_weight"`                                              // 成品克重
	FinishProductWidthUnitId      uint64                         `gorm:"column:finish_product_width_unit_id" relate:"dictionary_detail_id"`       // 成品幅宽单位id(字典)
	FinishProductGramWeightUnitId uint64                         `gorm:"column:finish_product_gram_weight_unit_id" relate:"dictionary_detail_id"` // 成品克重单位id(字典)
	StockRemark                   string                         `gorm:"column:stock_remark"`                                                     // 库存备注
	Remark                        string                         `gorm:"column:remark"`                                                           // 备注
	InternalRemark                string                         `gorm:"column:internal_remark"`                                                  // 内部备注(lotA lotB)
	ScanUserId                    uint64                         `gorm:"column:scan_user_id"`                                                     // 扫描人id
	ScanUserName                  string                         `gorm:"column:scan_user_name"`                                                   // 扫描人名称
	ScanTime                      time.Time                      `gorm:"column:scan_time"`                                                        // 扫描时间
	IsOutWarehouse                bool                           `gorm:"column:is_out_warehouse"`                                                 // 是否已经出仓；1是
	OrderTime                     time.Time                      `gorm:"column:order_time"`                                                       // 单据选框选择的那个时间
	ArrangeItemFcId               uint64                         `gorm:"column:arrange_item_fc_id"`                                               // 配布细码id
	IsBooked                      bool                           `gorm:"column:is_booked"`                                                        // 是否有上游单据占用库存
}

// 查询后的钩子
func (r *FpmOutOrderItemFc) AfterFind(tx *gorm.DB) (err error) {
	return
}

func (r FpmOutOrderItemFc) GetId() uint64 {
	return r.Id
}

// TableName FpmOutOrderItemFc 表名
func (FpmOutOrderItemFc) TableName() string {
	return "fpm_out_order_item_fc"
}

func (r FpmOutOrderItemFc) IsMain() bool {
	return false
}

func (r FpmOutOrderItemFc) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (FpmOutOrderItemFc) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

// ErrCodeFpmOutOrderItemFcAlreadyExist     ErrCode = 51XX1 // 出仓细码已存在
// ErrCodeFpmOutOrderItemFcNotExist         ErrCode = 51XX2 // 出仓细码不存在
func (FpmOutOrderItemFc) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeFpmOutOrderItemFcNotExist
}

func (FpmOutOrderItemFc) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeFpmOutOrderItemFcAlreadyExist
}

func (r FpmOutOrderItemFc) BuildReadCond(ctx context.Context, cond mysql_base.ICondition) {
	// CommonDataSeparate(ctx,r, cond)
}

func (r FpmOutOrderItemFc) BuildResp() structure.GetFpmOutOrderItemFcData {
	o := structure.GetFpmOutOrderItemFcData{}
	o.Id = r.Id
	o.ParentId = r.ParentId
	o.Roll = r.Roll
	o.WarehouseId = r.WarehouseId
	o.VolumeNumber = r.VolumeNumber
	o.WarehouseOutType = r.WarehouseOutType
	o.WarehouseOutOrderId = r.WarehouseOutOrderId
	o.WarehouseOutOrderNo = r.WarehouseOutOrderNo
	o.WarehouseInType = r.WarehouseInType
	o.WarehouseInOrderId = r.WarehouseInOrderId
	o.WarehouseInOrderNo = r.WarehouseInOrderNo
	o.ArrangeOrderNo = r.ArrangeOrderNo
	o.StockId = r.StockId
	o.SumStockId = r.SumStockId
	o.BaseUnitWeight = r.BaseUnitWeight
	o.PaperTubeWeight = r.PaperTubeWeight
	o.WeightError = r.WeightError
	o.ActuallyWeight = r.ActuallyWeight
	o.SettleErrorWeight = r.SettleErrorWeight
	o.UnitId = r.UnitId
	o.Length = r.Length
	o.SettleWeight = r.SettleWeight
	o.Remark = r.Remark
	o.Remark = r.StockRemark
	o.WarehouseBinId = r.WarehouseBinId
	o.DigitalCode = r.DigitalCode
	o.ShelfNo = r.ShelfNo
	o.ContractNumber = r.ContractNumber
	o.CustomerPoNum = r.CustomerPoNum
	o.AccountNum = r.AccountNum
	o.DyeFactoryColorCode = r.DyeFactoryColorCode
	o.DyeFactoryDyelotNumber = r.DyeFactoryDyelotNumber
	o.InternalRemark = r.InternalRemark
	o.ScanUserId = r.ScanUserId
	o.ScanUserName = r.ScanUserName
	o.ScanTime = tools.MyTime(r.ScanTime)
	o.IsOutWarehouse = r.IsOutWarehouse
	o.OrderTime = tools.MyTime(r.OrderTime)
	o.ArrangeItemFcId = r.ArrangeItemFcId
	o.IsBooked = r.IsBooked
	o.AvailableRoll = r.Roll
	o.AvailableWeight = r.BaseUnitWeight
	return o
}

func (w *FpmOutOrderItemFc) ToUpdateStockProductDetailParam(ctx context.Context, swap structure.Swap2StockFieldParam) *structure.UpdateStockProductDetailParam {
	o := &structure.UpdateStockProductDetailParam{}
	o.Id = w.StockId
	o.StockProductId = w.SumStockId
	o.Weight = w.BaseUnitWeight
	o.Length = w.Length
	o.Roll = w.Roll
	o.WeightError = w.WeightError
	o.PaperTubeWeight = w.PaperTubeWeight
	o.WarehouseOutOrderId = w.WarehouseOutOrderId
	o.WarehouseOutOrderNo = w.WarehouseOutOrderNo
	// 该扣减不适用多配布的情况，会多扣占用匹数和数量，改到上一层处理
	// if w.IsBooked {
	// 	o.BookRoll = w.Roll
	// 	o.BookWeight = w.BaseUnitWeight
	// }
	o.ProductId = swap.ProductId
	o.ProductColorId = swap.ProductColorId
	o.DyelotNumber = swap.DyeFactoryDyelotNumber
	o.WarehouseId = swap.WarehouseId
	o.CustomerId = swap.CustomerId
	o.ProductLevelId = swap.ProductLevelId
	o.ProductColorKindId = swap.ProductColorKindId
	o.Remark = w.StockRemark
	o.WarehouseBinId = w.WarehouseBinId
	o.DigitalCode = w.DigitalCode
	o.ShelfNo = w.ShelfNo
	o.ContractNumber = w.ContractNumber
	o.CustomerPoNum = w.CustomerPoNum
	o.CustomerAccountNum = w.AccountNum
	o.DyeFactoryColorCode = w.DyeFactoryColorCode
	o.DyelotNumber = w.DyeFactoryDyelotNumber
	o.FinishProductWidth = w.ProductWidth
	o.FinishProductGramWeight = w.ProductGramWeight
	o.FinishProductWidthUnitId = w.FinishProductWidthUnitId
	o.FinishProductGramWeightUnitId = w.FinishProductGramWeightUnitId
	o.ProductRemark = swap.ItemProductRemark
	o.InternalRemark = w.InternalRemark
	if w.ArrangeItemFcId != 0 {
		o.Status = common_system.StockStatusArrange
	}
	o.IsNoNeedCheckStock = true
	return o
}

func (w *FpmOutOrderItemFc) ToUpdateStockProductDetailParamBack(ctx context.Context, swap structure.Swap2StockFieldParam) *structure.UpdateStockProductDetailParam {
	o := &structure.UpdateStockProductDetailParam{}
	o.Id = w.StockId
	o.StockProductId = w.SumStockId
	o.WarehouseId = w.WarehouseId
	o.ProductId = swap.ProductId
	o.ProductColorId = swap.ProductColorId
	o.DyelotNumber = swap.DyeFactoryDyelotNumber
	o.WarehouseId = swap.WarehouseId
	o.CustomerId = swap.CustomerId
	o.ProductLevelId = swap.ProductLevelId
	o.ProductColorKindId = swap.ProductColorKindId
	o.Weight = -w.BaseUnitWeight
	o.Length = -w.Length
	o.Roll = -w.Roll
	o.WeightError = -w.WeightError
	o.PaperTubeWeight = -w.PaperTubeWeight
	o.WarehouseOutOrderId = w.WarehouseOutOrderId
	o.WarehouseOutOrderNo = w.WarehouseOutOrderNo
	o.Remark = w.StockRemark
	o.WarehouseBinId = w.WarehouseBinId
	o.DigitalCode = w.DigitalCode
	o.ShelfNo = w.ShelfNo
	o.ContractNumber = w.ContractNumber
	o.CustomerPoNum = w.CustomerPoNum
	o.CustomerAccountNum = w.AccountNum
	o.DyeFactoryColorCode = w.DyeFactoryColorCode
	o.DyelotNumber = w.DyeFactoryDyelotNumber
	o.FinishProductWidth = w.ProductWidth
	o.FinishProductGramWeight = w.ProductGramWeight
	o.FinishProductWidthUnitId = w.FinishProductWidthUnitId
	o.FinishProductGramWeightUnitId = w.FinishProductGramWeightUnitId
	o.InternalRemark = w.InternalRemark
	o.VolumeNumber = w.VolumeNumber
	// 判断是否有占用库存,有则需要释放
	// 该扣减不适用多配布的情况，会多扣占用匹数和数量，改到上一层处理
	// if w.IsBooked {
	// 	o.BookRoll = -w.Roll
	// 	o.BookWeight = -w.BaseUnitWeight
	// }
	return o
}

func NewFpmOutOrderItemFc(ctx context.Context, p *structure.AddFpmOutOrderItemFcParam) (r FpmOutOrderItemFc) {
	r.Id = vars.Snowflake.GenerateId().UInt64()
	r.Roll = p.Roll
	r.WarehouseBinId = p.WarehouseBinId
	r.VolumeNumber = p.VolumeNumber
	r.WarehouseOutType = p.WarehouseOutType
	r.WarehouseInType = p.WarehouseInType
	r.WarehouseInOrderId = p.WarehouseInOrderId
	r.WarehouseInOrderNo = p.WarehouseInOrderNo
	r.WarehouseOutOrderId = p.WarehouseOutOrderId
	r.WarehouseOutOrderNo = p.WarehouseOutOrderNo
	r.ArrangeOrderNo = p.ArrangeOrderNo
	r.StockId = p.StockId
	r.SumStockId = p.SumStockId
	r.BaseUnitWeight = p.BaseUnitWeight
	r.PaperTubeWeight = p.PaperTubeWeight
	r.WeightError = p.WeightError
	r.SettleErrorWeight = p.SettleErrorWeight
	r.UnitId = p.UnitId
	r.Length = p.Length
	r.SettleWeight = p.SettleWeight
	r.SettleLength = p.SettleLength
	r.DigitalCode = p.DigitalCode
	r.ShelfNo = p.ShelfNo
	r.ContractNumber = p.ContractNumber
	r.CustomerPoNum = p.CustomerPoNum
	r.AccountNum = p.AccountNum
	r.DyeFactoryColorCode = p.DyeFactoryColorCode
	r.DyeFactoryDyelotNumber = p.DyeFactoryDyelotNumber
	r.ProductWidth = p.ProductWidth
	r.ProductGramWeight = p.ProductGramWeight
	r.FinishProductWidthUnitId = p.FinishProductWidthUnitId
	r.FinishProductGramWeightUnitId = p.FinishProductGramWeightUnitId
	r.StockRemark = p.StockRemark
	r.Remark = p.Remark
	r.InternalRemark = p.InternalRemark
	r.ScanUserId = p.ScanUserId
	r.ScanUserName = p.ScanUserName
	r.ScanTime = p.ScanTime.ToTimeYMD()
	r.IsOutWarehouse = p.IsOutWarehouse
	r.OrderTime = p.OrderTime.ToTimeYMD()
	r.ArrangeItemFcId = p.ArrangeItemFcId
	r.IsBooked = p.IsBooked
	return
}

type FpmOutOrderItemFcForReport struct {
	FpmOutOrderItemFc
	SumRoll              int                `gorm:"column:sum_roll" sqlf:"sum(roll)"`
	SumWeight            int                `gorm:"column:sum_weight" sqlf:"sum(base_unit_weight)"`
	SumPaperTubeWeight   int                `gorm:"column:sum_paper_tube_weight" sqlf:"sum(paper_tube_weight)"`
	MergeStockIds        tools.QueryIntList `gorm:"column:merge_stock_ids" sqlf:"GROUP_CONCAT(distinct stock_id SEPARATOR ',')"`
	MergeWarehouseBinIds tools.QueryIntList `gorm:"column:merge_warehouse_bin_ids" sqlf:"GROUP_CONCAT(distinct warehouse_bin_id SEPARATOR ',')"`
}

type FpmOutOrderItemFcForReportList []FpmOutOrderItemFcForReport

type FpmOutOrderItemFcForFinishProductReport struct {
	FpmOutOrderItemFc
	SumRoll   int `gorm:"column:sum_roll" sqlf:"sum(roll)"`
	SumWeight int `gorm:"column:sum_weight" sqlf:"sum(base_unit_weight)"`
	// SumPaperTubeWeight   int                `gorm:"column:sum_paper_tube_weight" sqlf:"sum(paper_tube_weight)"`
	// MergeStockIds        tools.QueryIntList `gorm:"column:merge_stock_ids" sqlf:"GROUP_CONCAT(distinct stock_id SEPARATOR ',')"`
	// MergeWarehouseBinIds tools.QueryIntList `gorm:"column:merge_warehouse_bin_ids" sqlf:"GROUP_CONCAT(distinct warehouse_bin_id SEPARATOR ',')"`
}

type FpmOutOrderItemFcForFinishProductReportList []FpmOutOrderItemFcForFinishProductReport
