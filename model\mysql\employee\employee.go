package employee

import (
	"hcscm/common/errors"
	"hcscm/domain/employee/entity"
	"hcscm/model/mysql/mysql_base"
	"hcscm/vars"
)

type EmployeeList []Employee

func (r EmployeeList) PickByQYWXUserID(id string) Employee {
	for _, t := range r {
		if t.QYWXUserID == id {
			return t
		}
	}
	return Employee{}
}

// Employee 员工信息
type Employee struct {
	mysql_base.Model
	Id                uint64                `gorm:"primary_key;AUTO_INCREMENT;column:id" relate:"employee_id"`
	Code              string                `gorm:"column:code"`                                         // 编号
	Name              string                `gorm:"column:name"`                                         // 名称
	DepartmentID      uint64                `gorm:"column:department_id"`                                // 部门id
	Duty              mysql_base.UInt64List `gorm:"column:duty;type:json" relate:"dictionary_detail_id"` // 职责
	Phone             string                `gorm:"column:phone"`                                        // 手机号码
	Email             string                `gorm:"column:email"`                                        // 邮箱
	IsBlacklist       bool                  `gorm:"column:is_blacklist"`                                 // 是否为黑名单 0否 1是
	Remark            string                `gorm:"column:remark"`                                       // 备注
	Address           string                `gorm:"column:address"`                                      // 地址
	Birthday          string                `gorm:"column:birthday"`                                     // 出生日期
	IdentityNumber    string                `gorm:"column:identity_number"`                              // 身份证号码
	Nation            string                `gorm:"column:nation"`                                       // 民族
	MaritalStatus     int                   `gorm:"column:marital_status"`                               // 婚姻状况 1未婚 2已婚 3离异
	EducationLevel    int                   `gorm:"column:education_level"`                              // 教育状况 1初高中 2大专 3本科 4研究生 5硕士
	GraduateSchool    string                `gorm:"column:graduate_school"`                              // 毕业院校
	GraduateDate      string                `gorm:"column:graduate_date"`                                // 毕业日期
	ContractStartDate string                `gorm:"column:contract_start_date"`                          // 合同开始时间
	ContractEndDate   string                `gorm:"column:contract_end_date"`                            // 合同结束时间
	ResignDate        string                `gorm:"column:resign_date"`                                  // 离职日期
	TimecardNumber    string                `gorm:"column:timecard_number"`                              // 考勤卡号
	Status            int                   `gorm:"column:status"`                                       // 状态 1启用 2禁用
	Number            int                   `gorm:"column:number"`                                       // 编号（自增）
	QYWXUserName      string                `gorm:"column:qywx_user_name"`                               // 企业微信用户名
	QYWXUserID        string                `gorm:"column:qywx_user_id"`                                 // 企业微信用户ID
}

func (e *Employee) GetId() uint64 {
	return e.Id
}

func (e *Employee) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeEmployeeNotExist
}

func (e *Employee) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeEmployeeAlreadyExist
}

func (*Employee) TableName() string {
	return "employee"
}

func (e *Employee) IsHasRel() bool {
	return true
}

func (e *Employee) GetRelTableAndRelField() (mysql_base.ITable, string) {
	return &EmployeeSaleSystemRel{}, "employee_id"
}

func GetEmployeeIds(l []*Employee) []uint64 {
	ids := make([]uint64, 0, len(l))
	for _, item := range l {
		ids = append(ids, item.Id)
	}
	return ids
}

func GetDepartmentIdSet(l []*Employee) []uint64 {
	m := make(map[uint64]struct{})
	for _, item := range l {
		m[item.DepartmentID] = struct{}{}
	}
	ids := make([]uint64, 0, len(m))
	for id, _ := range m {
		ids = append(ids, id)
	}
	return ids
}

func GetDutyIdSet(l []*Employee) []uint64 {
	m := make(map[uint64]struct{})
	for _, item := range l {
		for _, dutyID := range item.Duty.ToInt() {
			m[dutyID] = struct{}{}
		}
	}
	ids := make([]uint64, 0, len(m))
	for id, _ := range m {
		ids = append(ids, id)
	}
	return ids
}

func ToEmployeePO(do *entity.Employee) *Employee {
	if do.Id <= 0 {
		do.Id = vars.Snowflake.GenerateId().UInt64()
	}
	po := &Employee{
		Id:           do.Id,
		Code:         do.Code,
		Name:         do.Name,
		DepartmentID: do.DepartmentID,
		// Duty:              do.GetIntDuty(),
		Duty:              do.Duty,
		Phone:             do.Phone,
		Email:             do.Email,
		IsBlacklist:       do.IsBlacklist,
		Remark:            do.Remark,
		Address:           do.Address,
		Birthday:          do.Birthday,
		IdentityNumber:    do.IdentityNumber,
		Nation:            do.Nation,
		MaritalStatus:     do.MaritalStatus,
		EducationLevel:    do.EducationLevel,
		GraduateSchool:    do.GraduateSchool,
		GraduateDate:      do.GraduateDate,
		ContractStartDate: do.ContractStartDate,
		ContractEndDate:   do.ContractEndDate,
		ResignDate:        do.ResignDate,
		TimecardNumber:    do.TimecardNumber,
		Status:            do.Status,
		Number:            do.Number,
		QYWXUserName:      do.QYWXUserName,
		QYWXUserID:        do.QYWXUserID,
	}
	return po
}

func ToEmployeeDO(po *Employee) (*entity.Employee, error) {
	if po == nil {
		return nil, nil
	}
	e, err := entity.NewEmployee(
		po.Id,
		po.Code,
		po.Name,
		po.DepartmentID,
		po.Duty,
		po.Phone,
		po.Email,
		po.IsBlacklist,
		po.Remark,
		po.Address,
		po.Birthday,
		po.IdentityNumber,
		po.Nation,
		po.MaritalStatus,
		po.EducationLevel,
		po.GraduateSchool,
		po.GraduateDate,
		po.ContractStartDate,
		po.ContractEndDate,
		po.ResignDate,
		po.TimecardNumber,
		po.QYWXUserName,
		po.QYWXUserID,
		po.Status,
	)
	if err != nil {
		return nil, err
	}
	return e, nil
}

func ToEmployeeDOs(po []*Employee) ([]*entity.Employee, error) {
	if len(po) <= 0 {
		return nil, nil
	}
	do := make([]*entity.Employee, 0, len(po))
	for _, item := range po {
		tmpDO, err := ToEmployeeDO(item)
		if err != nil {
			return nil, err
		}
		do = append(do, tmpDO)
	}
	return do, nil
}

func GetCheckDuty() int {
	do := entity.Employee{}
	return do.GetCheckDuty()
}

func NewTenantEmployee(param map[string]interface{}) Employee {
	var id uint64
	if _id, ok := param["id"].(uint64); ok {
		id = _id
	} else {
		id = vars.Snowflake.GenerateId().UInt64()
	}
	return Employee{
		Id:           id,
		Code:         param["name"].(string),
		Name:         param["name"].(string),
		DepartmentID: param["department_id"].(uint64),
		Duty:         param["duty"].([]uint64),
		Phone:        param["phone"].(string),
		Status:       1,
	}
}

func (r *Employee) UpdateTenantEmployee(param map[string]interface{}) {
	r.Code = param["name"].(string)
	r.Name = param["name"].(string)
	r.DepartmentID = param["department_id"].(uint64)
	r.Duty = param["duty"].([]uint64)
	r.Phone = param["phone"].(string)
	r.Status = param["status"].(int)
}

func MustCreateEmployee(tx *mysql_base.Tx, r Employee) (o Employee, err error) {
	err = mysql_base.MustCreateModel(tx, &r)
	o = r
	return
}

func MustUpdateEmployee(tx *mysql_base.Tx, r Employee) (o Employee, err error) {
	err = mysql_base.MustUpdateModel(tx, &r)
	o = r
	return
}

func FirstEmployeeByPhone(tx *mysql_base.Tx, phone string) (o Employee, exist bool, err error) {
	var (
		cond = mysql_base.NewCondition()
		r    Employee
	)
	cond.AddEqual("phone", phone)
	exist, err = mysql_base.FirstByCond(tx, &r, cond)
	o = r
	return
}
