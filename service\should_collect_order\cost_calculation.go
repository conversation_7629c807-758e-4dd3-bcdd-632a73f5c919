package should_collect_order

import (
	"fmt"
	"golang.org/x/net/context"
	aggsShouldCollectOrder "hcscm/aggs/should_collect_order"
	"hcscm/common/errors"
	common "hcscm/common/should_collect_order"
	product_pb "hcscm/extern/pb/basic_data/product"
	warehouse_pb "hcscm/extern/pb/basic_data/warehouse"
	biz_pb "hcscm/extern/pb/biz_unit"
	"hcscm/extern/pb/dictionary"
	employee_pb "hcscm/extern/pb/employee"
	color_pb "hcscm/extern/pb/finish_product"
	sale_system_pb "hcscm/extern/pb/sale_system"
	"hcscm/middleware"
	"hcscm/model/mysql/mysql_base"
	modelProduct "hcscm/model/mysql/product"
	mysqlProduct "hcscm/model/mysql/product/dao"
	model "hcscm/model/mysql/should_collect_order"
	mysql "hcscm/model/mysql/should_collect_order/dao"
	structure "hcscm/structure/should_collect_order"
	"hcscm/tools"
	"hcscm/tools/errgroup"
	"hcscm/tools/set"
	"hcscm/vars"
	"time"
)

type CostCalculationService struct {
}

func NewCostCalculationService() *CostCalculationService {
	return &CostCalculationService{}
}
func (s *CostCalculationService) GetList(ctx context.Context, req structure.CostCalculationListQuery) (data structure.CostCalculationListData, total int, err error) {
	var (
		orderList                                       model.ShouldCollectOrderList
		detailList                                      model.ShouldCollectOrderDetailList
		fpmArrangeOrders                                modelProduct.FpmArrangeOrderList
		fpmArrangeOrderItems                            modelProduct.FpmArrangeOrderItemList
		fpmArrangeOrderItemFcs                          modelProduct.FpmArrangeOrderItemFcList
		fpmSaleOutOrders                                modelProduct.FpmOutOrderList
		fpmSaleOutOrderItems, downPushSaleOutOrderItems modelProduct.FpmOutOrderItemList
		fpmSaleOutOrderItemFcs                          modelProduct.FpmOutOrderItemFcList
		stockDetailList                                 aggsShouldCollectOrder.StockDetailList
		funcList                                        []func(ctx context.Context) error
		bizService                                      = biz_pb.NewClientBizUnitService()
		saleSystemSvc                                   = sale_system_pb.NewSaleSystemClient()
		warehouseSvc                                    = warehouse_pb.NewPhysicalWarehouseClient()
		employeeSvc                                     = employee_pb.NewClientEmployeeService()
		dicSvc                                          = dictionary.NewDictionaryClient()
		productSvc                                      = product_pb.NewProductClient()
		colorSvc                                        = color_pb.NewClientFinishProductColorService()
		bizIds                                          = set.NewUint64Set()
		saleSystemIds                                   = set.NewUint64Set()
		employeeIds                                     = set.NewUint64Set()
		customerMap                                     = make(map[uint64]string, 0)
		warehouseMap                                    = make(map[uint64]string, 0)
		saleSystemMap                                   = make(map[uint64]string, 0)
		employeeMap                                     = make(map[uint64]string, 0)
		warehouseBinMap                                 = make(map[uint64]string, 0)
		dicNameMap                                      = make(map[uint64][2]string, 0)
		colorMap                                        = make(map[uint64][2]string, 0)
		productMap                                      = make(map[uint64]*product_pb.ProductRes, 0)
	)
	tx := mysql_base.TransactionSlaveEx(nil, ctx, true)
	repo := aggsShouldCollectOrder.NewCostCalculationRepo(tx)
	detailList, total, err = repo.QueryShouldCollectItemList(ctx, req)
	if err != nil {
		return
	}
	orderList, err = mysql.FindShouldCollectOrderByIDs(tx, detailList.GetShouldCollectOrderIds())
	if err != nil {
		return
	}
	fpmArrangeOrderItemIds := make([]uint64, 0, len(detailList))
	fpmSaleOutOrderItemIds := make([]uint64, 0, len(detailList))
	for _, detail := range detailList {
		order := orderList.Pick(detail.ShouldCollectOrderId)
		if order.SrcOrderType == common.SrcOrderTypeArrange {
			fpmArrangeOrderItemIds = append(fpmArrangeOrderItemIds, detail.SrcDetailId)
		} else if order.SrcOrderType == common.SrcOrderTypeProductSaleOut {
			fpmSaleOutOrderItemIds = append(fpmSaleOutOrderItemIds, detail.SrcDetailId)
		}
		bizIds.Add(order.CustomerId)
		saleSystemIds.Add(order.SaleSystemId)
		employeeIds.Add(order.SaleUserId)
		employeeIds.Add(order.SaleFollowerId)
	}
	fpmArrangeOrderItems, err = mysqlProduct.FindFpmArrangeOrderItemByIDs(tx, fpmArrangeOrderItemIds)
	if err != nil {
		return
	}
	fpmArrangeOrders, err = mysqlProduct.FindFpmArrangeOrderByIDs(tx, fpmArrangeOrderItems.GetParentIds())
	if err != nil {
		return
	}
	fpmArrangeOrderItemFcs, err = mysqlProduct.FindFpmArrangeOrderItemFcByParenTIDs(tx, fpmArrangeOrderItems.GetIds())
	if err != nil {
		return
	}
	fpmSaleOutOrderItems, err = mysqlProduct.FindFpmOutOrderItemByIDs(tx, fpmSaleOutOrderItemIds)
	if err != nil {
		return
	}
	downPushSaleOutOrderItems, err = mysqlProduct.FindFpmSaleOutOrderItemByArrangeItemIDs(tx, fpmArrangeOrderItemIds)
	if err != nil {
		return
	}
	fpmSaleOutOrders, err = mysqlProduct.FindFpmOutOrderByIDs(tx, append(fpmSaleOutOrderItems.GetParentIds(), downPushSaleOutOrderItems.GetParentIds()...))
	if err != nil {
		return
	}
	fpmSaleOutOrderItemFcs, err = mysqlProduct.FindFpmOutOrderItemFcByParenTIDs(tx, append(fpmSaleOutOrderItems.GetIds(), downPushSaleOutOrderItems.GetIds()...))
	if err != nil {
		return
	}
	stockDetailList, err = repo.QueryStockDetailList(ctx, mysql_base.GetUInt64ListV2("stock_id", fpmArrangeOrderItemFcs, fpmSaleOutOrderItemFcs))
	if err != nil {
		return
	}
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		productMap, err1 = productSvc.GetProductMapByIds(ctx, mysql_base.GetUInt64ListV2("product_id", detailList))
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetProductMapByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		colorMap, err1 = colorSvc.GetProductColorCodeNameByIds(ctx, mysql_base.GetUInt64ListV2("product_color_id", detailList))
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		customerMap, err1 = bizService.GetBizUnitNameByIds(ctx, append(bizIds.List(), mysql_base.GetUInt64ListV2("biz_unit_id", fpmArrangeOrderItems, fpmSaleOutOrderItems)...))
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		saleSystemMap, err1 = saleSystemSvc.GetSaleSystemByIds(ctx, saleSystemIds.List())
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetSaleSystemByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		warehouseMap, err1 = warehouseSvc.GetPhysicalWarehouseByIds(ctx, mysql_base.GetUInt64ListV2("warehouse_id", fpmArrangeOrders, fpmSaleOutOrders))
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		employeeMap, err1 = employeeSvc.GetEmployeeNameByIds(ctx, employeeIds.List())
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetBizUnitNameByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		dicNameMap, err1 = dicSvc.GetDictionaryNameByIds(ctx, mysql_base.GetUInt64List(stockDetailList, "dictionary_detail_id"))
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetDictionaryNameByIds err "+err1.Error()))
		}
		return nil
	})
	funcList = append(funcList, func(ctx context.Context) error {
		var err1 error
		warehouseBinMap, err1 = warehouseSvc.GetPhysicalWarehouseBinNameByIds(ctx, mysql_base.GetUInt64ListV2("warehouse_bin_id", fpmArrangeOrderItemFcs, fpmSaleOutOrderItemFcs))
		if err1 != nil {
			middleware.ErrorLog(errors.NewCustomError(errors.ErrCodeMysqlRetrieve, "GetPhysicalWarehouseBinNameByIds err "+err1.Error()))
		}
		return nil
	})
	errgroup.Finish(ctx, 0, funcList...)

	for _, detail := range detailList {
		var (
			fcDataList                                                                                     []structure.CostCalculationListItemFcData
			fpmArrangeOrder                                                                                modelProduct.FpmArrangeOrder
			fpmSaleOutOrder                                                                                modelProduct.FpmOutOrder
			address, product, color, arrangeWarehouseName, warehouseName, productCustomerName, grossMargin string
			buoyantWeightPrice, totalCostPrice, totalWeight                                                int
			fcList                                                                                         modelProduct.FpmOutOrderItemFcList
		)
		order := orderList.Pick(detail.ShouldCollectOrderId)
		if order.SrcOrderType == common.SrcOrderTypeArrange {
			fpmArrangeOrderItem := fpmArrangeOrderItems.Pick(detail.SrcDetailId)
			fpmArrangeOrder = fpmArrangeOrders.Pick(fpmArrangeOrderItem.ParentId)
			address = fpmArrangeOrder.ReceiveAddr
			arrangeWarehouseName, _ = warehouseMap[fpmArrangeOrder.WarehouseId]
			productCustomerName, _ = customerMap[fpmArrangeOrderItem.CustomerId]
			fpmSaleOutOrderItemList := downPushSaleOutOrderItems.PickByArrangeItemId(fpmArrangeOrderItem.Id)
			for i, fpmSaleOutOrderItem := range fpmSaleOutOrderItemList {
				if i == 0 {
					fpmSaleOutOrder = fpmSaleOutOrders.Pick(fpmSaleOutOrderItem.ParentId)
				}
				fcList = append(fcList, fpmSaleOutOrderItemFcs.PickFcListByParentId(fpmSaleOutOrderItem.Id)...)
			}
			warehouseName, _ = warehouseMap[fpmSaleOutOrder.WarehouseId]
		} else if order.SrcOrderType == common.SrcOrderTypeProductSaleOut {
			fpmSaleOutOrderItem := fpmSaleOutOrderItems.Pick(detail.SrcDetailId)
			fpmSaleOutOrder = fpmSaleOutOrders.Pick(fpmSaleOutOrderItem.ParentId)
			address = fpmSaleOutOrder.ReceiveAddr
			warehouseName, _ = warehouseMap[fpmSaleOutOrder.WarehouseId]
			productCustomerName, _ = customerMap[fpmSaleOutOrderItem.CustomerId]
			fcList = fpmSaleOutOrderItemFcs.PickFcListByParentId(detail.SrcDetailId)
		}
		finishProduct := productMap[detail.MaterialId]
		finishProductColor := colorMap[detail.ProductColorId]
		if finishProduct != nil {
			product = tools.ProductOrColorJoin(finishProduct.FinishProductCode, finishProduct.FinishProductName)
		}
		color = tools.ProductOrColorJoin(finishProductColor[0], finishProductColor[1])
		for _, fc := range fcList {
			if fc.DyeFactoryDyelotNumber != detail.DyelotNumber {
				continue
			}
			stockDetail := stockDetailList.PickById(fc.StockId)
			fcDataList = append(fcDataList, structure.CostCalculationListItemFcData{
				Roll:                    fc.Roll,
				WarehouseBin:            warehouseBinMap[fc.WarehouseBinId],
				VolumeNumber:            fc.VolumeNumber,
				BaseUnitWeight:          fc.BaseUnitWeight,
				ActuallyWeight:          fc.ActuallyWeight,
				PaperTubeWeight:         fc.PaperTubeWeight,
				WeightError:             fc.WeightError,
				SettleErrorWeight:       fc.SettleErrorWeight,
				SettleWeight:            fc.SettleWeight,
				Length:                  fc.Length,
				DyelotNumber:            fc.DyeFactoryDyelotNumber,
				FinishProductWidth:      fmt.Sprintf("%s%s", stockDetail.FinishProductWidth, dicNameMap[stockDetail.FinishProductWidthUnitId][1]),
				FinishProductGramWeight: fmt.Sprintf("%s%s", stockDetail.FinishProductGramWeight, dicNameMap[stockDetail.FinishProductGramWeightUnitId][1]),
				BuoyantWeightPrice:      stockDetail.BuoyantWeightPrice,
			})
			if stockDetail.BuoyantWeightPrice > 0 {
				totalCostPrice += stockDetail.BuoyantWeightPrice * fc.BaseUnitWeight / vars.WeightUnitPriceMult
				totalWeight += fc.BaseUnitWeight
			}
		}
		if totalWeight > 0 {
			buoyantWeightPrice = totalCostPrice * vars.WeightUnitPriceMult / totalWeight
		}
		if detail.SettlePrice > 0 {
			grossMargin = fmt.Sprintf("%.2f", float64(detail.SettlePrice-totalCostPrice)/float64(detail.SettlePrice)*100)
		}
		calculation := structure.CostCalculationListItemData{
			ProductCustomerName:  productCustomerName,
			ProductId:            detail.MaterialId,
			Product:              product,
			ProductColorId:       detail.ProductColorId,
			Color:                color,
			DyelotNumber:         detail.DyelotNumber,
			CustomerName:         customerMap[order.CustomerId],
			SalePrice:            tools.OneTenCent(detail.SalePrice),
			OffsetSalePrice:      tools.OneTenCent(detail.StandardSalePrice - detail.SalePrice),
			Roll:                 tools.Roll(detail.Roll),
			OutWeight:            tools.Milligram(detail.Weight),
			ActuallyWeight:       tools.Milligram(detail.ActuallyWeight),
			SettleErrorWeight:    tools.Milligram(detail.SettleErrorWeight),
			SettleWeight:         tools.Milligram(detail.SettleWeight),
			OtherPrice:           tools.Cent(detail.OtherPrice),
			SaleTaxRate:          detail.SaleTaxRate,
			SettlePrice:          tools.Cent(detail.SettlePrice),
			SettlePriceRemoveTax: tools.Cent(detail.SettlePrice * vars.SaleTaxRateMult / (vars.SaleTaxRateMult + detail.SaleTaxRate)),
			BuoyantWeightPrice:   tools.OneTenCent(buoyantWeightPrice),
			TotalCostPrice:       tools.Cent(totalCostPrice),
			GrossProfit:          tools.Cent(detail.SettlePrice - totalCostPrice),
			GrossMargin:          grossMargin,
			CreateTime:           tools.QueryTime(order.CreateTime.Format(time.DateOnly)),
			SaleOrderID:          fpmArrangeOrder.SrcId,
			SaleOrderNo:          fpmArrangeOrder.SrcOrderNo,
			ArrangeOrderID:       fpmArrangeOrder.Id,
			ArrangeOrderNo:       fpmArrangeOrder.OrderNo,
			ShouldCollectOrderId: detail.ShouldCollectOrderId,
			ShouldCollectOrderNO: order.OrderNo,
			FpmSaleOutOrderID:    fpmSaleOutOrder.Id,
			FpmSaleOutOrderNo:    fpmSaleOutOrder.OrderNo,
			SaleSystemId:         order.SaleSystemId,
			SaleSystemName:       saleSystemMap[order.SaleSystemId],
			SaleUserId:           order.SaleUserId,
			SaleUserName:         employeeMap[order.SaleUserId],
			SaleFollowerId:       order.SaleFollowerId,
			SaleFollowerName:     employeeMap[order.SaleFollowerId],
			ArrangeWarehouseName: arrangeWarehouseName,
			WarehouseName:        warehouseName,
			Address:              address,
			AuditStatus:          order.AuditStatus,
			AuditStatusName:      order.AuditStatus.String(),
			CollectStatus:        order.CollectStatus,
			CollectStatusName:    order.CollectStatus.String(),
			FcList:               fcDataList,
		}
		if detail.AuxiliaryUnitId != detail.MeasurementUnitId {
			calculation.SettleWeight = tools.Milligram(detail.Length)
			calculation.SalePrice = tools.OneTenCent(detail.LengthCutSalePrice)
			calculation.OffsetSalePrice = tools.OneTenCent(detail.StandardLengthCutSalePrice - detail.LengthCutSalePrice)
		}
		data.List = append(data.List, calculation)
		data.TotalRoll += detail.Roll
		data.TotalSettlePrice += detail.SettlePrice
		data.TotalCostPrice += totalCostPrice
		data.TotalGrossProfit += detail.SettlePrice - totalCostPrice
	}
	if data.TotalSettlePrice > 0 {
		data.TotalGrossMargin = fmt.Sprintf("%.2f", float64(data.TotalSettlePrice-data.TotalCostPrice)/float64(data.TotalSettlePrice)*100)
	}
	return
}
