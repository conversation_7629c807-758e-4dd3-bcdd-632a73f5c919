package tenant_management

import (
	"archive/tar"
	"context"
	"database/sql"
	"encoding/json"
	"fmt"
	"github.com/go-basic/uuid"
	"gopkg.in/yaml.v3"
	"gorm.io/gorm"
	EmployeeRepo "hcscm/aggs/employee"
	payRepo "hcscm/aggs/pay_record"
	"hcscm/common/errors"
	common "hcscm/common/system_consts"
	"hcscm/config"
	"hcscm/docs"
	"hcscm/extern/pb/dictionary"
	"hcscm/log"
	"hcscm/middleware"
	mysqlEmployee "hcscm/model/mysql/employee"
	"hcscm/model/mysql/mysql_base"
	payRecordMysql "hcscm/model/mysql/pay_record"
	payRecordDao "hcscm/model/mysql/pay_record/dao"
	mysqlSystem "hcscm/model/mysql/system"
	userMysql "hcscm/model/mysql/system"
	userDao "hcscm/model/mysql/system/dao"
	model "hcscm/model/mysql/tenant_management"
	mysql "hcscm/model/mysql/tenant_management"
	tenantManagementDao "hcscm/model/mysql/tenant_management/dao"
	"hcscm/model/redis"
	"hcscm/msg/msg_publish"
	structure "hcscm/structure/tenant_management"
	"hcscm/tools"
	"hcscm/tools/metadata"
	"hcscm/vars"
	"io/ioutil"
	"math/rand"
	"net/url"
	"os"
	"strings"
	"time"
)

type ITenantManagementRepo interface {
	Create(ctx context.Context, tx *mysql_base.Tx, param structure.AddTenantManagementParam) (data structure.ResPayUrlData, err error)
	PayNotify(ctx context.Context, tx *mysql_base.Tx, payRecord payRecordMysql.PayRecord) (err error)
	SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error)
	GetDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementQuery) (data structure.GetTenantManagementData, err error)
	RegisterCreate(ctx context.Context) (id uint64, err error)
	UpdateCompanyName(ctx context.Context, tx *mysql_base.Tx, param structure.UpdateTenantManagementNameParam) (data structure.ResTenantManagementIDData, err error)
	GetExpiringSoonTenantManagementPushQueue(ctx context.Context, tx *mysql_base.Tx) (err error)
	initDatabase(ctx context.Context, databaseName string) (err error)
	generateConfigYaml(ctx context.Context, tenantManagement mysql.TenantManagement) (yamlOfConfig map[string]interface{}, err error)
	// createDocker(ctx context.Context, tenantManagement mysql.TenantManagement) (err error)
	DisableTenantManagement(ctx context.Context, tx *mysql_base.Tx, param structure.DisableTenantManagementParam) (err error)
	EnableTenantManagement(ctx context.Context, param structure.EnableTenantManagementParam) (err error)
	GetMPDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementQuery) (data structure.GetMPTenantManagementData, err error)
	CreateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error)
	UpdateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error)
	ModifyTenantLoginPassword(ctx context.Context, tx *mysql_base.Tx, param structure.ModifyTenantManagementPasswordParam) (err error)

	GetUserTenantManagementList(ctx context.Context, query structure.GetTenantManagementQuery) (list structure.GetUserTenantManagementDataList, total int, err error)
	TenantManagementFeedback(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error)
	WashTenantManagementRel(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error)

	SetNewUserOrcDeadline(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (mysql.TenantManagement, error)
	CleanTenantManagements(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) error
	GetTenantManagementList(ctx context.Context, tx *mysql_base.Tx) (mysql.TenantManagementList, error)

	FillDomain(ctx context.Context, tx *mysql_base.Tx, q *structure.FillDomainParam) error
	MustFirst(ctx context.Context, tx *mysql_base.Tx, id uint64) (tenantManagement mysql.TenantManagement, err error)
	FindByIDs(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (list mysql.TenantManagementList, err error)
	GetUnbindTenantManagementList(ctx context.Context, tx *mysql_base.Tx, query structure.QYWXGetUnBindTenantManagementListQuery) (list mysql.TenantManagementList, total int, err error)
}

type tenantManagementRepo struct {
	tenantManagementDao           tenantManagementDao.ITenantManagementDao
	tenantManagementPackageRelDao tenantManagementDao.ITenantManagementPackageRelDao
	tenantManagementUserRelDao    tenantManagementDao.ITenantManagementUserRelDao
	tenantPackageDao              tenantManagementDao.ITenantPackageDao
	payRecordRepo                 payRecordDao.IPayRecordDao
	userDao                       userDao.IUserDao
	aliPayRepo                    payRepo.IPayRepo
	rechargeHistoryDao            tenantManagementDao.IRechargeHistoryDao
}

func NewTenantManagementRepo() ITenantManagementRepo {
	return &tenantManagementRepo{
		tenantManagementDao:           tenantManagementDao.NewTenantManagementDao(),
		tenantManagementPackageRelDao: tenantManagementDao.NewTenantManagementPackageRelDao(),
		tenantManagementUserRelDao:    tenantManagementDao.NewTenantManagementUserRelDao(),
		tenantPackageDao:              tenantManagementDao.NewTenantPackageDao(),
		payRecordRepo:                 payRecordDao.NewPayRecordDao(),
		userDao:                       userDao.NewUserDao(),
		aliPayRepo:                    payRepo.NewPayRepo(),
	}
}

// 废弃

func (repo *tenantManagementRepo) Create(ctx context.Context, tx *mysql_base.Tx, param structure.AddTenantManagementParam) (data structure.ResPayUrlData, err error) {
	var (
		tenantPackage    mysql.TenantPackage
		tenantManagement mysql.TenantManagement
		randomNumber     int
		_uuid            = uuid.New()
	)

	tenantPackage, err = repo.tenantPackageDao.MustFirst(ctx, tx, param.TenantPackageID)
	if err != nil {
		return
	}

	for {
		var isExist bool
		rand.Seed(time.Now().UnixNano())
		// 生成一个随机的五位整数，范围在 [52000, 65535] 之间
		randomNumber = rand.Intn(13536) + 52000
		tenantManagement, isExist, err = repo.tenantManagementDao.FirstByAssignPort(ctx, tx, randomNumber)
		if err != nil {
			return
		}
		if !isExist {
			break
		}
	}

	tenantManagement = mysql.NewTenantManagement(tenantPackage, param, randomNumber)
	tenantManagement, err = repo.tenantManagementDao.MustCreate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	switch param.PayWay {
	case common.PayWayWechatPay:
		return structure.ResPayUrlData{PayUrl: "10001", Price: tenantPackage.Price}, err
	case common.PayWayAliPay:
		var (
			payUrl    *url.URL
			payRecord payRecordMysql.PayRecord
		)
		payUrl, err = repo.aliPayRepo.GenTenantManagementAliPayUrl(_uuid, tenantPackage)
		if err != nil {
			return
		}
		payRecord = payRecordMysql.NewPayRecordForTenantManagement(_uuid, param.PayWay, tenantPackage, tenantManagement)
		payRecord, err = repo.payRecordRepo.MustCreate(ctx, tx, payRecord)
		if err != nil {
			return
		}
		err = msg_publish.PushAliPay(ctx, msg_publish.PayRecordMsg{
			PayRecordOrderNo: _uuid,
		})
		if err != nil {
			return
		}
		return structure.ResPayUrlData{PayUrl: payUrl.String(), Price: tenantPackage.Price}, err
	default:
		return structure.ResPayUrlData{PayUrl: "你他喵的倒是选支付方式啊", Price: tenantPackage.Price}, err
	}
}

func (repo *tenantManagementRepo) RegisterCreate(ctx context.Context) (id uint64, err error) {
	var (
		user                       userMysql.User
		tenantPackage              mysql.TenantPackage
		tenantManagement           mysql.TenantManagement
		tenantManagementPackageRel mysql.TenantManagementPackageRel
		tenantManagementUserRel    mysql.TenantManagementUserRel
		payRecord                  payRecordMysql.PayRecord
		role, _role                mysqlSystem.Role
		_db                        *gorm.DB
		menuIDs, resourceIDs       []uint64
		menus                      mysqlSystem.MenuList
		resourceTrees              mysqlSystem.ResourceTreeList
		routers                    mysqlSystem.RouterList
	)

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)

	user, err = repo.userDao.MustFirst(ctx, tx, metadata.GetUserId(ctx))
	if err != nil {
		return
	}

	role, err = mysqlSystem.MustFirstRoleByName(tx, "管理者")
	if err != nil {
		return
	}

	_role, err = mysqlSystem.MustFirstRoleByName(tx, "销售员")
	if err != nil {
		return
	}

	tenantPackage, err = repo.tenantPackageDao.MustFirstTrialVersion(ctx, tx)
	if err != nil {
		return
	}

	tenantManagement = mysql.NewFirstRegistrationTenantManagement(tenantPackage, user.Phone, 0, user.EmployeeName)
	tenantManagement, err = repo.tenantManagementDao.MustCreate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	payRecord = payRecordMysql.NewFirstRegistrationPayRecordForTenantManagement(tenantPackage, user.EmployeeName, user.Phone, tenantManagement)
	payRecord, err = repo.payRecordRepo.MustCreate(ctx, tx, payRecord)
	if err != nil {
		return
	}

	tenantManagementPackageRel = mysql.NewTenantManagementPackageRel(tenantManagement, tenantPackage, payRecord.Id)
	tenantManagementPackageRel, err = repo.tenantManagementPackageRelDao.MustCreate(ctx, tx, tenantManagementPackageRel)
	if err != nil {
		return
	}

	tenantManagementUserRel = mysql.NewTenantManagementUserRel(ctx, tenantManagement.Id, user.Id)
	tenantManagementUserRel, err = repo.tenantManagementUserRelDao.MustCreate(ctx, tx, tenantManagementUserRel)
	if err != nil {
		return
	}

	// 小程序注册绑定公司
	user.TenantManagementID = tenantManagement.Id
	user.EmployeeID = vars.DefaultEmployeeID
	user.DepartmentID = vars.DefaultDepartmentID
	user.TenantManagementDeadline = tenantManagement.Deadline
	user.IsTenantAdmin = true
	user, err = repo.userDao.MustUpdateTenantUser(ctx, tx, user)
	if err != nil {
		return
	}

	menuIDs, err = tools.StringArr2UInt64Arr(strings.Split(tenantPackage.MenuIDs, ","))
	if err != nil {
		return
	}

	menus, err = mysqlSystem.FindMenuByMenuID(tx, menuIDs)
	if err != nil {
		return
	}

	resourceIDs, err = tools.StringArr2UInt64Arr(strings.Split(tenantPackage.ResourceIDs, ","))
	if err != nil {
		return
	}

	resourceTrees, err = mysqlSystem.FindResourceTreeByResourceTreeID(tx, resourceIDs)
	if err != nil {
		return
	}

	routers, err = mysqlSystem.FindRouterByIDs(tx, tenantPackage.RouterIDs)
	if err != nil {
		return
	}

	err = repo.initDatabase(ctx, tenantManagement.DatabaseName)
	if err != nil {
		return
	}

	err = commit(err, recover())
	if err != nil {
		return
	}

	_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
	if err != nil {
		return
	}

	docs.GenTable(_db)

	// docs.GenBasicData(_db)

	user.Id = vars.AdminUserID
	user.EmployeeID = vars.DefaultEmployeeID
	user.AllowUpdateOrder = true
	user.AllowCancelOther = true
	user.AllowAuditSelf = true
	user.WarehouseRoleAccess = []uint64{
		vars.DefaultProductWarehouseID,
		vars.DefaultGreyFabricWarehouseID,
		vars.DefaultRawMaterialWarehouseID,
	}
	err = _db.Create(&user).Error
	if err != nil {
		return
	}

	_db.Create(&tenantManagement)
	_db.Create(&tenantManagementPackageRel)
	_db.Create(&tenantManagementUserRel)
	_db.Create(&tenantPackage)
	role.TenantDefaultRole = true
	_db.Create(&role)
	_db.Create(&_role)
	_db.Create(&payRecord)
	userRoleRel := mysqlSystem.UserRoleRel{
		Id:     vars.Snowflake.GenerateId().UInt64(),
		UserID: user.Id,
		RoleID: role.Id,
	}
	_db.Create(&userRoleRel)
	var (
		roleAccessList mysqlSystem.RoleAccessList
	)
	for _, roleID := range []uint64{role.Id, _role.Id} {
		roleAccessList = append(roleAccessList, mysqlSystem.NewTenantRoleAccess(roleID, tenantPackage))
	}
	_db.Create(&roleAccessList)
	_db.CreateInBatches(&menus, 1)
	_db.CreateInBatches(&resourceTrees, 1)
	_db.CreateInBatches(&routers, 1)
	employee := mysqlEmployee.Employee{
		Id:           vars.DefaultEmployeeID,
		DepartmentID: vars.DefaultDepartmentID,
		Code:         "001",
		Name:         "超级管理员",
		Phone:        user.Phone,
		Status:       1,
		Number:       1,
		Duty: []uint64{
			vars.DutySeller,
			vars.DutyOrderPrinter,
			vars.DutyRemover,
			vars.DutyClothChecker,
			vars.DutyWarehouseManager,
			vars.DutyBusinessManager,
			vars.DutyOrderFollower,
			vars.DutyOrderQC,
			vars.DutyDriver,
			vars.FabricMatcher,
			vars.DutyChecker,
			vars.DutyProducer,
			vars.DutyRepairer,
			vars.DutyQcSupervisor,
		},
	}
	_db.Create(&employee)

	// 初始化客户
	InitCustomer(ctx, _db, tenantManagement.TenantCompanyName, tenantManagement.TenantPhoneNumber)

	// 基本信息
	InitSystem(ctx, _db)

	// 初始化字典
	InitDictionary(ctx, _db)

	// 初始化基础数据类型
	InitTypeBasicData(ctx, _db)

	// 初始化基础数据
	InitBasicData(ctx, _db)

	// 初始化打印模板
	InitPrintTemplate(ctx, _db)

	// 新建账套通知
	if vars.Env != "local" && vars.Env != "test" && vars.Env != "pre" {
		msg_publish.PushNewTenantManagement(ctx, tenantManagement, common.MqMessageTypeNewTenantNotify)
	}
	return tenantManagement.Id, nil
}

func (repo *tenantManagementRepo) CreateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		tenantManagement mysql.TenantManagement
		createUserParam  = make(map[string]interface{})
		user             userMysql.User
		department       userMysql.Department
		roles            userMysql.RoleList
		employeeExist    bool
		employees        mysqlEmployee.Employee
		// employeeRepo                        = EmployeeRepo.NewRepo(mysql_base.GetConn(tx))
		// dutyService                         = dictionary.NewDictionaryClient()
		// dutyMap                             = make(map[uint64][2]string)
		// _db *gorm.DB
		saasUser                userMysql.User
		tenantManagementUserRel mysql.TenantManagementUserRel
	)

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	// 先判断是否是租户,租户ID不为0则为租户，为0则为平台用户
	tenantManagementID := metadata.GetTenantManagementId(ctx)
	if tenantManagementID != 0 {

		// 判断登录态的租户ID和入参的租户ID是否一致，不一致则报错
		if tenantManagementID != param.TenantManagementID {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，租户ID与当前租户ID不一致"))
			return
		}
		tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, param.TenantManagementID)
		if err != nil {
			return
		}
	}

	department, err = mysqlSystem.MustFirstDepartment(tx, param.DepartmentID)
	if err != nil {
		return
	}

	roles, err = mysqlSystem.FindRoleByRoleID(tx, param.RoleIDs)
	if err != nil {
		return
	}

	// 在租户管理平台新增用户，该用户不绑定员工，只绑定租户，在我们系统的权限为只能查询他自己绑定的租户
	var (
		saasExist bool
	)
	// 查找该用户的手机号码在租户管理平台中是否存在，如果存在，则只增加绑定关系
	saasUser, saasExist, err = repo.userDao.FirstByTenantManagementPhone(context.Background(), nil, param.Phone)
	if err != nil {
		return
	}

	// 查找账套内是否存在该手机号员工
	employees, employeeExist, err = mysqlEmployee.FirstEmployeeByPhone(tx, param.Phone)
	if err != nil {
		return
	}
	// 租户员工创建
	if !employeeExist {
		employees = mysqlEmployee.NewTenantEmployee(map[string]interface{}{
			"id":            0,
			"name":          param.UserName,
			"phone":         param.Phone,
			"duty":          param.DutyIDs,
			"department_id": department.Id,
		})
		employees, err = mysqlEmployee.MustCreateEmployee(tx, employees)
		if err != nil {
			return
		}
	}

	// 租户用户创建
	createUserParam = map[string]interface{}{
		"id":                         saasUser.Id,
		"tenant_management_id":       tenantManagement.Id,
		"phone":                      param.Phone,
		"tenant_management_deadline": tenantManagement.Deadline,
		"password":                   param.Password,
		"department_id":              department.Id,
		"department_name":            department.Name,
		"employee_id":                employees.Id,
		"employee_code":              param.UserName,
		"employee_name":              param.UserName,
		"status":                     param.Status,
		"access_scope":               param.AccessScope,
	}
	user = mysqlSystem.NewTenantUser(createUserParam)
	user, err = repo.userDao.MustCreateTenantUser(ctx, tx, user)
	if err != nil {
		return
	}

	// 平台用户不存在则创建
	if !saasExist {
		saasUser, err = repo.userDao.MustCreateTenantUser(context.Background(), nil, user)
		if err != nil {
			return
		}
	}

	// 创建租户和用户的关联关系
	if tenantManagementID != 0 {
		// 租户内
		tenantManagementUserRel = mysql.NewTenantManagementUserRel(ctx, tenantManagement.Id, user.Id)
		tenantManagementUserRel, err = repo.tenantManagementUserRelDao.MustCreate(ctx, tx, tenantManagementUserRel)
		if err != nil {
			return
		}
		// 平台
		tenantManagementUserRel = mysql.NewTenantManagementUserRel(ctx, tenantManagement.Id, saasUser.Id)
		tenantManagementUserRel, err = repo.tenantManagementUserRelDao.MustCreate(context.Background(), nil, tenantManagementUserRel)
		if err != nil {
			return
		}
	}

	// 用户角色绑定
	for _, role := range roles {
		var userRoleRel userMysql.UserRoleRel
		userRoleRel = userMysql.NewUserRoleRel(ctx, user.Id, role.Id)
		_, err = userMysql.MustCreateUserRoleRel(tx, userRoleRel)
		if err != nil {
			return
		}
	}

	data.Id = tenantManagement.Id

	return
}

/*
更新多种情况逻辑如下:
1.更新用户的手机号码
更新租户内用户信息,删除管理平台原本的用户和租户的关联关系
判断该手机号码在管理平台是否存在，
不存在则创建一个新用户并创建租户和用户的关联关系，
存在则创建管理平台租户和用户的关联关系
2.更新用户的状态
如果修改为禁用状态，则更新管理平台租户和用户的关联关系的状态为禁用
如果修改为启用状态，则更新管理平台租户和用户的关联关系的状态为启用
*/
func (repo *tenantManagementRepo) UpdateSubTenant(ctx context.Context, param structure.AddOrUpdateSubTenantManagementParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		// tenantManagement mysql.TenantManagement
		updateUserParam = make(map[string]interface{})
		oldUser         userMysql.User
		user            userMysql.User
		department      userMysql.Department
		roles           userMysql.RoleList
		employees       *mysqlEmployee.Employee
		userRoleRels    userMysql.UserRoleRelList
	)

	tenantManagementID := metadata.GetTenantManagementId(ctx)
	if tenantManagementID != 0 {
		// 判断登录态的租户ID和入参的租户ID是否一致，不一致则报错
		if tenantManagementID != param.TenantManagementID {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，租户ID与当前租户ID不一致"))
			return
		}
	}

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()
	var employeeRepo = EmployeeRepo.NewRepo(tx)

	user, err = repo.userDao.MustFirstByTenantManagementIDAndUserID(ctx, tx, tenantManagementID, param.UserID)
	if err != nil {
		return
	}
	oldUser = user

	department, err = mysqlSystem.MustFirstDepartment(tx, param.DepartmentID)
	if err != nil {
		return
	}

	// 禁止禁用自己的用户
	if param.Status == common.StatusDisable {
		if metadata.GetUserId(ctx) == user.Id {
			err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，无法禁用自己"))
			if err != nil {
				return
			}
		}
	}

	updateUserParam = map[string]interface{}{
		"phone":           param.Phone,
		"department_id":   department.Id,
		"department_name": department.Name,
		"employee_name":   param.UserName,
		"status":          param.Status,
		"access_scope":    param.AccessScope,
	}
	user.UpdateTenantUser(updateUserParam)
	user, err = repo.userDao.MustUpdateTenantUser(ctx, tx, user)
	if err != nil {
		return
	}

	employees, err = employeeRepo.QueryEmployee(ctx, user.EmployeeID)
	if err != nil {
		return
	}

	if employees != nil {
		employees.UpdateTenantEmployee(map[string]interface{}{
			"name":          param.UserName,
			"phone":         param.Phone,
			"duty":          param.DutyIDs,
			"department_id": department.Id,
			"status":        int(param.Status),
		})

		_, err = mysqlEmployee.MustUpdateEmployee(tx, *employees)
		if err != nil {
			return
		}
	}

	roles, err = mysqlSystem.FindRoleByRoleID(tx, param.RoleIDs)
	if err != nil {
		return
	}

	userRoleRels, err = mysqlSystem.FindUserRoleRelByUIDs(tx, user)
	if err != nil {
		return
	}

	for _, userRoleRel := range userRoleRels {
		err = mysqlSystem.MustDeleteUserRoleRel(tx, userRoleRel)
		if err != nil {
			return
		}
	}

	for _, role := range roles {
		var userRoleRel userMysql.UserRoleRel
		userRoleRel = userMysql.NewUserRoleRel(ctx, user.Id, role.Id)
		_, err = userMysql.MustCreateUserRoleRel(tx, userRoleRel)
		if err != nil {
			return
		}
	}

	var (
		newUser                       userMysql.User
		newUserExist, exist, relExist bool
		oldTenantManagementUserRel    mysql.TenantManagementUserRel
		userDao                       = userDao.NewUserDao()
		tenantManagementUserRelDao    = tenantManagementDao.NewTenantManagementUserRelDao()
	)
	newUser, newUserExist, err = userDao.FirstByTenantManagementPhone(context.Background(), nil, user.Phone)
	if err != nil {
		return
	}
	if !newUserExist {
		newUser = user
		newUser.Id = vars.Snowflake.GenerateId().UInt64()
		newUser, err = userDao.MustCreateTenantUser(context.Background(), nil, newUser)
		if err != nil {
			return
		}
	}
	// 手机号码变更，删除掉平台旧用户的用户租户关系，并创建平台新用户的用户租户关系
	if oldUser.Phone != newUser.Phone {
		// 旧用户的关系存在删除
		oldUser, exist, err = userDao.FirstByTenantManagementPhone(context.Background(), nil, oldUser.Phone)
		if err != nil {
			return
		}
		if exist {
			oldTenantManagementUserRel, relExist, err = tenantManagementUserRelDao.FirstByUserIDAndTenantManagementID(context.Background(), nil, oldUser.Id, tenantManagementID)
			if err != nil {
				return
			}
			if relExist {
				err = tenantManagementUserRelDao.MustDelete(context.Background(), nil, oldTenantManagementUserRel)
				if err != nil {
					return
				}
			}
		}

		// 新用户关系不存在则创建
		var (
			tenantManagementUserRel mysql.TenantManagementUserRel
		)
		tenantManagementUserRel, relExist, err = tenantManagementUserRelDao.FirstByUserIDAndTenantManagementID(context.Background(), nil, newUser.Id, tenantManagementID)
		if err != nil {
			return
		}
		if !relExist {
			tenantManagementUserRel = mysql.NewTenantManagementUserRel(ctx, tenantManagementID, newUser.Id)
			tenantManagementUserRel, err = tenantManagementUserRelDao.MustCreate(context.Background(), nil, tenantManagementUserRel)
			if err != nil {
				return
			}
		}
	}
	// 状态变更
	if oldUser.Status != newUser.Status {
		var (
			newTenantManagementUserRel mysql.TenantManagementUserRel
		)
		// 租户内用户关系不存在则创建
		oldTenantManagementUserRel, relExist, err = tenantManagementUserRelDao.FirstByUserIDAndTenantManagementID(ctx, tx, oldUser.Id, tenantManagementID)
		if err != nil {
			return
		}
		if !relExist {
			var (
				tenantManagementUserRel mysql.TenantManagementUserRel
			)
			tenantManagementUserRel = mysql.NewTenantManagementUserRel(ctx, tenantManagementID, oldUser.Id)
			oldTenantManagementUserRel, err = tenantManagementUserRelDao.MustCreate(ctx, tx, tenantManagementUserRel)
			if err != nil {
				return
			}
		}

		// 平台用户关系不存在则创建
		newTenantManagementUserRel, relExist, err = tenantManagementUserRelDao.FirstByUserIDAndTenantManagementID(context.Background(), nil, newUser.Id, tenantManagementID)
		if err != nil {
			return
		}
		if !relExist {
			var (
				tenantManagementUserRel mysql.TenantManagementUserRel
			)
			tenantManagementUserRel = mysql.NewTenantManagementUserRel(ctx, tenantManagementID, newUser.Id)
			newTenantManagementUserRel, err = tenantManagementUserRelDao.MustCreate(ctx, tx, tenantManagementUserRel)
			if err != nil {
				return
			}
		}

		// 用户启用状态同步
		if newUser.Status == common.StatusEnable {
			// 租户内用户启用状态同步
			oldTenantManagementUserRel.Status = common.StatusEnable
			oldTenantManagementUserRel, err = tenantManagementUserRelDao.MustUpdate(ctx, tx, oldTenantManagementUserRel)
			if err != nil {
				return
			}
			// 平台用户启用状态同步
			newTenantManagementUserRel.Status = common.StatusEnable
			newTenantManagementUserRel, err = tenantManagementUserRelDao.MustUpdate(context.Background(), nil, newTenantManagementUserRel)
			if err != nil {
				return
			}
		}
		// 用户禁用状态同步
		if newUser.Status == common.StatusDisable {
			// 禁止禁用自己的用户
			if metadata.GetUserId(ctx) == user.Id {
				err = middleware.WarnLog(errors.NewCustomError(errors.ErrCodeBusinessParameter, "，无法禁用自己"))
				if err != nil {
					return
				}
			}
			// 租户内用户禁用状态同步
			oldTenantManagementUserRel.Status = common.StatusDisable
			oldTenantManagementUserRel, err = tenantManagementUserRelDao.MustUpdate(ctx, tx, oldTenantManagementUserRel)
			if err != nil {
				return
			}
			// 平台用户禁用状态同步
			newTenantManagementUserRel.Status = common.StatusDisable
			newTenantManagementUserRel, err = tenantManagementUserRelDao.MustUpdate(context.Background(), nil, newTenantManagementUserRel)
			if err != nil {
				return
			}
		}
	}

	data.Id = user.Id
	return
}

func (repo *tenantManagementRepo) UpdateCompanyName(ctx context.Context, tx *mysql_base.Tx, param structure.UpdateTenantManagementNameParam) (data structure.ResTenantManagementIDData, err error) {
	var (
		tenantManagement mysql.TenantManagement
		// user             userMysql.User
	)
	tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		return
	}

	tenantManagement.TenantCompanyName = param.CompanyName
	tenantManagement, err = repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	if info := metadata.GetLoginInfo(ctx); info != nil {
		if info.GetTenantManagementId() != 0 {
			tenantManagement, err = repo.tenantManagementDao.MustUpdate(context.Background(), nil, tenantManagement)
			if err != nil {
				return
			}
		}
	}

	data.Id = tenantManagement.Id
	return
}

func (repo *tenantManagementRepo) SearchList(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementListQuery) (list structure.GetTenantManagementListDataList, total int, err error) {
	var (
		tenantManagements mysql.TenantManagementList
	)

	tenantManagements, total, err = repo.tenantManagementDao.SearchList(ctx, tx, query)
	if err != nil {
		return
	}

	list = make(structure.GetTenantManagementListDataList, 0)
	for _, tenantManagement := range tenantManagements {
		list = append(list, structure.GetTenantManagementListData{
			Id:                         tenantManagement.Id,
			CompanyName:                tenantManagement.TenantCompanyName,
			Phone:                      tenantManagement.TenantPhoneNumber,
			CreateTime:                 tools.MyTime(tenantManagement.CreateTime),
			CreatorName:                tenantManagement.CreatorName,
			UpdateTime:                 tools.MyTime(tenantManagement.UpdateTime),
			UpdateUserName:             tenantManagement.UpdaterName,
			ActivationTime:             tools.MyTime(tenantManagement.ActivationTime),
			Deadline:                   tools.MyTime(tenantManagement.Deadline),
			TenantManagementStatus:     tenantManagement.TenantManagementStatus,
			TenantManagementStatusName: tenantManagement.TenantManagementStatus.String(),
			Contacts:                   tenantManagement.TenantContacts,

			CodeListOrcDeadLine: tools.MyTime(tenantManagement.CodeListOrcDeadLine),
			CodeListOrcStatus:   tenantManagement.CodeListOrcStatus,
		})
	}
	return
}

func (repo *tenantManagementRepo) GetExpiringSoonTenantManagementPushQueue(ctx context.Context, tx *mysql_base.Tx) (err error) {
	var (
		tenantManagements           mysql.TenantManagementList
		tenantManagementPackageRels mysql.TenantManagementPackageRelList
	)

	tenantManagements, err = repo.tenantManagementDao.GetExpiringSoonList(ctx, tx)
	if err != nil {
		return
	}

	tenantManagementPackageRels, err = repo.tenantManagementPackageRelDao.GetExpiringSoonList(ctx, tx)
	if err != nil {
		return
	}

	for _, tenantManagement := range tenantManagements {
		err = msg_publish.PushExpireTenantManagement(ctx, tenantManagement)
		if err != nil {
			continue
		}
	}

	for _, tenantManagementPackageRel := range tenantManagementPackageRels {
		err = msg_publish.PushExpireTenantPackage(ctx, tenantManagementPackageRel)
		if err != nil {
			continue
		}
	}
	return
}

func (repo *tenantManagementRepo) GetDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementQuery) (data structure.GetTenantManagementData, err error) {
	var (
		tenantManagement            mysql.TenantManagement
		tenantPackages              mysql.TenantPackageList
		payRecords                  payRecordMysql.PayRecordList
		tenantManagementPackageRels mysql.TenantManagementPackageRelList
	)

	tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, query.Id)
	if err != nil {
		return
	}

	payRecords, err = repo.payRecordRepo.FindByTenantManagementIDs(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	tenantPackages, err = repo.tenantPackageDao.FindByIDs(ctx, tx, payRecords)
	if err != nil {
		return
	}

	tenantManagementPackageRels, err = repo.tenantManagementPackageRelDao.FindByTenantManagementIDs(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	data = structure.GetTenantManagementData{
		Id:                         tenantManagement.Id,
		CompanyName:                tenantManagement.TenantCompanyName,
		Phone:                      tenantManagement.TenantPhoneNumber,
		CreateTime:                 tools.MyTime(tenantManagement.CreateTime),
		CreatorName:                tenantManagement.CreatorName,
		UpdateTime:                 tools.MyTime(tenantManagement.UpdateTime),
		UpdateUserName:             tenantManagement.UpdaterName,
		ActivationTime:             tools.MyTime(tenantManagement.ActivationTime),
		Deadline:                   tools.MyTime(tenantManagement.Deadline),
		CodeListOrcDeadLine:        tools.MyTime(tenantManagement.CodeListOrcDeadLine),
		TenantManagementStatus:     tenantManagement.TenantManagementStatus,
		TenantManagementStatusName: tenantManagement.TenantManagementStatus.String(),
		Contacts:                   tenantManagement.TenantContacts,
		PayRecordDataList: func() (list []structure.PayRecordData) {
			for _, payRecord := range payRecords {
				tenantPackage := tenantPackages.Pick(payRecord.PackageID)
				tenantManagementPackageRel := tenantManagementPackageRels.PickByPayRecord(payRecord.Id)
				list = append(list, structure.PayRecordData{
					Id:                  payRecord.Id,
					PayWay:              payRecord.PayWay,
					PayWayName:          payRecord.PayWay.String(),
					PayOrderNo:          payRecord.PayOrderNo,
					PayStatus:           payRecord.PayStatus,
					PayStatusName:       payRecord.PayStatus.String(),
					PayFinishTime:       tools.MyTime(payRecord.PayFinishTime),
					TradeNo:             payRecord.TradeNo,
					TenantPackageID:     payRecord.PackageID,
					TenantPackageName:   tenantPackage.Name,
					ActivationTime:      tools.MyTime(tenantManagementPackageRel.ActivationTime),
					Deadline:            tools.MyTime(tenantManagementPackageRel.Deadline),
					UpdateUserName:      payRecord.UpdaterName,
					UpdateTime:          tools.MyTime(payRecord.UpdateTime),
					PayStatusChangeTime: tools.MyTime(payRecord.PayStatusChangeTime),
					PayerName:           payRecord.PayerName,
					PayPrice:            payRecord.Price,
				})
			}
			return
		}(),
	}
	return
}

func (repo *tenantManagementRepo) GetMPDetailInfo(ctx context.Context, tx *mysql_base.Tx, query structure.GetTenantManagementQuery) (data structure.GetMPTenantManagementData, err error) {
	var (
		tenantManagement         mysql.TenantManagement
		tenantManagementUserRels mysql.TenantManagementUserRelList
		users                    mysqlSystem.UserList
		departments              mysqlSystem.DepartmentList
		userRoleRels             mysqlSystem.UserRoleRelList
		roles                    mysqlSystem.RoleList
		dutyService              = dictionary.NewDictionaryClient()
		employeeRepo             = EmployeeRepo.NewRepo(tx)
		employees                []*mysqlEmployee.Employee
		employeeMap              = make(map[uint64]mysqlEmployee.Employee)
		dutyMap                  = make(map[uint64][2]string)
		dictionaryDetailIDs      []uint64
	)
	tenantManagementID := metadata.GetTenantManagementId(ctx)
	// if tenantManagementID != 0 {
	var (
		userIDs []uint64
	)
	tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, tenantManagementID)
	if err != nil {
		return
	}

	tenantManagementUserRels, err = repo.tenantManagementUserRelDao.FindByTenantManagementIDs(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	for _, tenantManagementUserRel := range tenantManagementUserRels {
		userIDs = append(userIDs, tenantManagementUserRel.UserID)
	}
	users, err = repo.userDao.FindByID(ctx, tx, userIDs)
	if err != nil {
		return
	}
	// } else {
	// 	users, err = repo.userDao.FindByTenantManagementID(ctx, tx, tenantManagementID)
	// 	if err != nil {
	// 		return
	// 	}
	// }

	departments, err = mysqlSystem.FindDepartment(tx, users)
	if err != nil {
		return
	}

	userRoleRels, err = mysqlSystem.FindUserRoleRelByUIDs(tx, users)
	if err != nil {
		return
	}

	roles, err = mysqlSystem.FindRole(tx, userRoleRels)
	if err != nil {
		return
	}

	employees, err = employeeRepo.QueryEmployeeByIds(ctx, mysql_base.GetUInt64ListV2("employee_id", users))
	if err != nil {
		return
	}

	for _, employee := range employees {
		employeeMap[employee.Id] = *employee
		dictionaryDetailIDs = append(dictionaryDetailIDs, employee.Duty.ToInt()...)
	}
	dutyMap, err = dutyService.GetDictionaryNameByIds(ctx, dictionaryDetailIDs)
	if err != nil {
		return
	}
	var (
		list = make([]structure.SubTenantManagementData, 0)
	)
	for _, user := range users {
		_employee := employeeMap[user.EmployeeID]
		department := departments.Pick(_employee.DepartmentID)
		list = append(list, structure.SubTenantManagementData{
			Id:              user.Id,
			UserName:        user.EmployeeName,
			Phone:           user.Phone,
			IsAdminTenant:   user.IsTenantAdmin,
			DepartmentID:    department.Id,
			DepartmentName:  department.Name,
			AccessScope:     user.AccessScope,
			AccessScopeName: user.AccessScope.String(),
			Status:          user.Status,
			StatusName:      user.Status.String(),
			DutyIDs: func() (ids []uint64) {
				ids = make([]uint64, 0)
				for _, employee := range employees {
					if employee.Id == user.EmployeeID {
						return employee.Duty.ToInt()
					}
				}
				return
			}(),
			DutyName: func() (str string) {
				return strings.Join(func() (dutyNames []string) {
					for _, employee := range employees {
						if employee.Id == user.EmployeeID {
							for _, dutyID := range employee.Duty.ToInt() {
								dutyNames = append(dutyNames, dutyMap[dutyID][1])
							}
							return
						}
					}
					return
				}(), ",")
			}(),
			RoleDataList: func() (list []structure.RoleData) {
				list = make([]structure.RoleData, 0)
				for _, rel := range userRoleRels.PickByUserID(user.Id) {
					role := roles.Pick(rel.RoleID)
					list = append(list, structure.RoleData{
						Id:   role.Id,
						Name: role.Name,
					})
				}
				return
			}(),
		})
	}
	data = structure.GetMPTenantManagementData{
		Id:                         tenantManagement.Id,
		CompanyName:                tenantManagement.TenantCompanyName,
		Phone:                      tenantManagement.TenantPhoneNumber,
		CreateTime:                 tools.MyTime(tenantManagement.CreateTime),
		CreatorName:                tenantManagement.CreatorName,
		UpdateTime:                 tools.MyTime(tenantManagement.UpdateTime),
		UpdateUserName:             tenantManagement.UpdaterName,
		Deadline:                   tools.MyTime(tenantManagement.Deadline),
		TenantManagementStatus:     tenantManagement.TenantManagementStatus,
		TenantManagementStatusName: tenantManagement.TenantManagementStatus.String(),
		Contacts:                   tenantManagement.TenantContacts,
		AdminName:                  users.PickAdmin().EmployeeName,
	}
	data.SubTenantManagementDataList = list
	return data, nil
}

func (repo *tenantManagementRepo) PayNotify(ctx context.Context, tx *mysql_base.Tx, payRecord payRecordMysql.PayRecord) (err error) {
	var (
		tenantPackage    mysql.TenantPackage
		tenantManagement mysql.TenantManagement
		// createUserParam  = make(map[string]interface{})
		yamlOfConfig   = make(map[string]interface{})
		configJsonByte []byte
		// db               *gorm.DB
	)

	tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, payRecord.PaySourceID)
	if err != nil {
		return
	}

	tenantPackage, err = repo.tenantPackageDao.MustFirst(ctx, tx, tenantManagement.TenantPackageID)
	if err != nil {
		return
	}

	err = repo.initDatabase(ctx, tenantManagement.DatabaseName)
	if err != nil {
		return
	}

	yamlOfConfig, err = repo.generateConfigYaml(ctx, tenantManagement)
	if err != nil {
		return
	}

	configJsonByte, err = json.Marshal(yamlOfConfig)
	if err != nil {
		return
	}

	// err = repo.createDocker(ctx, tenantManagement)
	// if err != nil {
	// 	return
	// }

	payRecord.PaySuccessUpdate()
	payRecord, err = repo.payRecordRepo.MustUpdate(ctx, tx, payRecord)
	if err != nil {
		return
	}

	tenantManagement.PaySuccessUpdate(tenantPackage, string(configJsonByte))
	tenantManagement, err = repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return
	}

	// createUserParam = map[string]interface{}{
	// 	"tenant_management_id":       tenantManagement.Id,
	// 	"phone":                      tenantManagement.TenantPhoneNumber,
	// 	"tenant_management_deadline": tenantManagement.Deadline,
	// }
	// _, err = repo.userDao.MustCreateTenantUser(ctx, tx, userMysql.NewTenantUser(createUserParam))
	// if err != nil {
	// 	return
	// }
	return
}

func (repo *tenantManagementRepo) initDatabase(ctx context.Context, databaseName string) (err error) {
	var db *sql.DB
	db, err = sql.Open("mysql", fmt.Sprintf("%s:%s@tcp(%s:%d)/",
		config.Conf.Mysql.UserName,
		config.Conf.Mysql.PassWord,
		config.Conf.Mysql.Host,
		config.Conf.Mysql.Port))
	if err != nil {
		return
	}
	defer db.Close()

	// 创建数据库
	_, err = db.Exec(fmt.Sprintf("CREATE DATABASE `%s` CHARACTER SET 'utf8mb4' COLLATE 'utf8mb4_bin';", databaseName))
	if err != nil {
		return
	}

	return
}

//
// func (repo *tenantManagementRepo) createDocker(ctx context.Context, tenantManagement mysql.TenantManagement) (err error) {
// 	var (
// 		cli        *client.Client
// 		configFile *os.File
// 		_container container.CreateResponse
// 		// 指定容器配置
// 		containerConfig = &container.Config{
// 			Image:      "hcscm:latest",
// 			WorkingDir: "/data",
// 			Cmd:        []string{"/data/hcscm", fmt.Sprintf("config.%s.yaml", tenantManagement.DatabaseName)},
// 			ExposedPorts: nat.PortSet{
// 				"51001/tcp": struct{}{},
// 			},
// 		}
// 		containerHostConfig = &container.HostConfig{
// 			PortBindings: nat.PortMap{
// 				nat.Port("51001/tcp"): []nat.PortBinding{
// 					{
// 						HostIP:   "0.0.0.0",
// 						HostPort: strconv.Itoa(tenantManagement.AssignPort),
// 					},
// 				},
// 			},
// 		}
// 	)
// 	cli, err = client.NewClientWithOpts(func(c *client.Client) (opts []client.Opt) {
// 		opts = []client.Opt{
// 			client.WithTLSClientConfigFromEnv(),
// 			client.WithHost("tcp://************:2375"),
// 			client.WithVersionFromEnv(),
// 		}
// 		for _, op := range opts {
// 			if err = op(c); err != nil {
// 				return nil
// 			}
// 		}
// 		return opts
// 	}(cli)...)
// 	if err != nil {
// 		return
// 	}
// 	defer cli.Close()
//
// 	// 创建容器
// 	_container, err = cli.ContainerCreate(context.Background(), containerConfig, containerHostConfig, nil, nil, tenantManagement.DatabaseName)
// 	if err != nil {
// 		return
// 	}
//
// 	configFile, err = os.Open(fmt.Sprintf("./config.%s.yaml.tar", tenantManagement.DatabaseName))
// 	if err != nil {
// 		return
// 	}
// 	defer configFile.Close()
//
// 	err = cli.CopyToContainer(context.Background(), _container.Id, "/data", configFile, types.CopyToContainerOptions{
// 		AllowOverwriteDirWithFile: true,
// 	})
// 	if err != nil {
// 		return
// 	}
//
// 	// 启动容器
// 	if err = cli.ContainerStart(context.Background(), _container.Id, types.ContainerStartOptions{}); err != nil {
// 		return
// 	}
// 	// 在这里可以添加更多操作，如等待容器退出等
//
// 	// 关闭Docker客户端连接
// 	return
// }

func (repo *tenantManagementRepo) generateConfigYaml(ctx context.Context, tenantManagement mysql.TenantManagement) (yamlOfConfig map[string]interface{}, err error) {
	var (
		file, tarFile *os.File
		tarWriter     *tar.Writer
		yamlContent   []byte
	)
	yamlOfConfig = make(map[string]interface{}, 0)
	yamlOfConfig = map[string]interface{}{
		"redis": map[string]interface{}{
			"host":     "************",
			"port":     6379,
			"password": nil,
			"db":       0,
			"poolsize": 0,
		},
		"mysql": map[string]interface{}{
			"username":        "root",
			"host":            "************",
			"port":            3306,
			"password":        "root",
			"dbname":          tenantManagement.DatabaseName,
			"debug":           true,
			"migrate":         true,
			"washerswitch":    true,
			"singular":        true,
			"maxidleconns":    71,
			"maxopenconns":    100,
			"logmode":         "info",
			"passwordsecret1": "luying",
			"passwordsecret2": "haochuan",
		},
		"mysqlslave": map[string]interface{}{
			"username": "root",
			"password": "root",
			"host":     "************",
			"port":     3306,
			"dbname":   tenantManagement.DatabaseName,
		},
		"server": map[string]interface{}{
			"servicename": "hcscm",
			"debug":       true,
			"environment": "local",
			"authon":      true,
			"version":     nil,
		},
		"web": map[string]interface{}{
			"port": 51001,
		},
		"cdn": map[string]interface{}{
			"bucket":   nil,
			"op":       nil,
			"password": nil,
		},
		"rabbitmq": map[string]interface{}{
			"user":       "guest",
			"password":   "guest",
			"host":       "************",
			"port":       5673,
			"dead":       "hcscm_delay",
			"routingkey": "hcscm",
		},
		"websocket": map[string]interface{}{
			"pingwait": 60,
		},
		"tenantmanagement": map[string]interface{}{
			"tenantphone": tenantManagement.TenantPhoneNumber,
		},
	}

	// 创建文件
	file, err = os.Create(fmt.Sprintf("./config.%s.yaml", tenantManagement.DatabaseName))
	if err != nil {
		return
	}
	defer file.Close()

	// 创建编码器
	encoder := yaml.NewEncoder(file)

	// 将配置编码为 YAML 数据
	err = encoder.Encode(&yamlOfConfig)
	if err != nil {
		return
	}

	tarFile, err = os.Create(fmt.Sprintf("./config.%s.yaml.tar", tenantManagement.DatabaseName))
	if err != nil {
		return
	}
	defer tarFile.Close()

	// 创建一个 tar.Writer，将 tar 归档写入 tarFile 文件
	tarWriter = tar.NewWriter(tarFile)
	defer tarWriter.Close()

	// 读取 YAML 文件的内容
	yamlContent, err = ioutil.ReadFile(fmt.Sprintf("./config.%s.yaml", tenantManagement.DatabaseName))
	if err != nil {
		return
	}

	// 创建一个 tar.Header 来描述文件
	hdr := &tar.Header{
		Name: fmt.Sprintf("./config.%s.yaml", tenantManagement.DatabaseName),
		Mode: 0600, // 文件权限
		Size: int64(len(yamlContent)),
	}
	if err = tarWriter.WriteHeader(hdr); err != nil {
		return
	}

	// 写入 YAML 内容到 tar 归档
	if _, err = tarWriter.Write(yamlContent); err != nil {
		return
	}

	return
}

func (repo *tenantManagementRepo) DisableTenantManagement(ctx context.Context, tx *mysql_base.Tx, param structure.DisableTenantManagementParam) (err error) {
	var (
		tenantManagements mysql.TenantManagementList
		users             mysqlSystem.UserList
		_db               *gorm.DB
	)

	tenantManagements, err = repo.tenantManagementDao.FindByIDs(ctx, tx, param.IDs)
	if err != nil {
		return
	}

	users, err = repo.userDao.FindByTenantManagementIDs(ctx, tx, tenantManagements)
	if err != nil {
		return
	}

	for _, tenantManagement := range tenantManagements {
		tenantManagement.TenantManagementStatus = common.TenantManagementStatusDisable
		tenantManagement, err = repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
		if err != nil {
			return
		}

		_db = mysql_base.GetDBMap(tenantManagement.Id)
		if _db == nil {
			_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
			if err != nil {
				return
			}
		}

		err = _db.Updates(&tenantManagement).Error
		if err != nil {
			return
		}

		for _, user := range users.PickByTenantManagementID(tenantManagement.Id) {
			for ind, fn := range []func(context.Context, uint64) ([]string, error){redis.GetTokenByMPUser, redis.GetTokenByAdminUser, redis.GetTokenByPDAUser} {
				var tokens []string
				tokens, err = fn(ctx, user.Id)
				if err != nil {
					return
				}
				switch ind {
				case 0:
					err = redis.FlushMPToken(ctx, user.Id, tokens)
					if err != nil {
						return
					}
				case 1:
					err = redis.FlushAdminToken(ctx, user.Id, tokens)
					if err != nil {
						return
					}
				case 2:
					err = redis.FlushPDAToken(ctx, user.Id, tokens)
					if err != nil {
						return
					}
				}
			}
		}
	}
	return
}

func (repo *tenantManagementRepo) EnableTenantManagement(ctx context.Context, param structure.EnableTenantManagementParam) (err error) {
	var (
		tenantManagement            mysql.TenantManagement
		tenantManagementPackageRels mysql.TenantManagementPackageRelList
		_db                         *gorm.DB
	)

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)

	tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, param.Id)
	if err != nil {
		err = commit(err, recover())
		return
	}

	if tenantManagement.TenantManagementStatus != common.TenantManagementStatusDisable {
		err = log.ErrorLog(errors.NewError(errors.ErrCodeTenantManagementStatusNotOperate))
		err = commit(err, recover())
		return
	}

	tenantManagementPackageRels, err = repo.tenantManagementPackageRelDao.FindByTenantManagementIDs(ctx, tx, tenantManagement)
	if err != nil {
		err = commit(err, recover())
		return
	}

	tenantManagement.TenantManagementStatus = common.TenantManagementStatusExpire
	for _, tenantManagementPackageRel := range tenantManagementPackageRels {
		if tenantManagementPackageRel.TenantManagementPackageStatus == common.TenantManagementPackageStatusNormal {
			tenantManagement.TenantManagementStatus = common.TenantManagementStatusNormal
		}
	}

	tenantManagement, err = repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		err = commit(err, recover())
		return
	}

	err = commit(err, recover())

	_db = mysql_base.GetDBMap(tenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
		if err != nil {
			return
		}
	}

	_db.Updates(&tenantManagement)

	return
}

func (repo *tenantManagementRepo) ModifyTenantLoginPassword(ctx context.Context, tx *mysql_base.Tx, param structure.ModifyTenantManagementPasswordParam) (err error) {
	var (
		user, loginUser userMysql.User
	)

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)

	user, err = repo.userDao.MustFirstByTenantManagementIDAndUserID(ctx, tx, param.TenantManagementID, param.UserID)
	if err != nil {
		// todo:2024-02-19 这里怎么这样子写？
		err = commit(err, recover())
		return
	}

	loginUser, err = repo.userDao.MustFirst(ctx, tx, metadata.GetUserId(ctx))
	if err != nil {
		err = commit(err, recover())
		return
	}

	if !loginUser.IsTenantAdmin {
		err = log.ErrorLog(errors.NewError(errors.ErrCodeCanOnlyBeModifyByAdmin))
		err = commit(err, recover())
		return
	}

	user.SetPassword(param.Password)
	_, err = repo.userDao.MustUpdateTenantUser(ctx, tx, user)
	if err != nil {
		return
	}

	err = commit(err, recover())

	_, err = repo.userDao.MustUpdateTenantUser(ctx, nil, user)
	if err != nil {
		return
	}
	return
}

func (repo *tenantManagementRepo) GetUserTenantManagementList(ctx context.Context, query structure.GetTenantManagementQuery) (list structure.GetUserTenantManagementDataList, total int, err error) {
	var (
		tenantManagementUserRels mysql.TenantManagementUserRelList
		tenantManagements        mysql.TenantManagementList
	)
	tenantManagementUserRels, total, err = repo.tenantManagementUserRelDao.SearchList(ctx, nil, query)
	if err != nil {
		return
	}

	tenantManagementIds := mysql_base.GetUInt64ListV2("tenant_management_id", tenantManagementUserRels)
	tenantManagements, err = repo.tenantManagementDao.FindByIDs(ctx, nil, tenantManagementIds)
	if err != nil {
		return
	}

	for _, tenantManagementUserRel := range tenantManagementUserRels {
		tenantManagement := tenantManagements.Pick(tenantManagementUserRel.TenantManagementID)
		data := structure.GetUserTenantManagementData{}
		data.TenantManagementID = tenantManagementUserRel.TenantManagementID
		data.TenantPhone = tenantManagement.TenantPhoneNumber
		data.TenantContacts = tenantManagement.TenantContacts
		data.CompanyName = tenantManagement.TenantCompanyName
		data.DatabaseName = tenantManagement.DatabaseName
		data.Secret = tenantManagement.Secret
		list = append(list, data)
	}

	total = len(tenantManagementUserRels)
	return
}

func (repo *tenantManagementRepo) TenantManagementFeedback(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error) {

	var (
		tenantManagement mysql.TenantManagement
		feedback         msg_publish.Feedback
	)

	tx := mysql_base.TransactionSlaveEx(nil, ctx, false)

	if metadata.GetTenantManagementId(ctx) != 0 {
		tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, metadata.GetTenantManagementId(ctx))
		if err != nil {
			return
		}
		feedback.TenantManagementId = tenantManagement.Id
		feedback.TextureURL = q.Urls.ToString()
		feedback.Suggestion = q.Suggestion
		msg_publish.PushTenantManagementFeedback(ctx, feedback, common.MqMessageTypeCustomerFeedback)
	}
	return
}

func (repo *tenantManagementRepo) WashTenantManagementRel(ctx context.Context, q *structure.TenantManagementFeedbackParam) (err error) {
	var (
		tenantManagementList mysql.TenantManagementList
	)

	tx, commit := mysql_base.TransactionMainEx(nil, ctx, false)
	defer func() {
		err = commit(err, recover())
	}()

	// 查找平台所有账套租户
	tenantManagementList, err = repo.tenantManagementDao.FindAll(ctx, tx)
	if err != nil {
		return
	}

	for _, tenantManagement := range tenantManagementList {
		var (
			tenantManagementUserRelList mysql.TenantManagementUserRelList
		)
		// 查找账套租户对应的用户关系
		tenantManagementUserRelList, err = repo.tenantManagementUserRelDao.FindByTenantManagementIDs(ctx, tx, tenantManagement)
		if err != nil {
			return
		}

		// 查找出账套租户内所有的用户
		var (
			_db                          *gorm.DB
			_users                       userMysql.UserList
			_tenantManagementUserRelList mysql.TenantManagementUserRelList
			dbErr                        error
		)
		_db = mysql_base.GetDBMap(tenantManagement.Id)
		if _db == nil {
			_db, dbErr = mysql_base.NewDBConn(ctx, tenantManagement.DatabaseName, tenantManagement.Id)
			if dbErr != nil {
				continue
			}
		}
		dbErr = _db.Model(&userMysql.User{}).Scan(&_users).Error
		if dbErr != nil {
			continue
		}
		dbErr = _db.Model(&mysql.TenantManagementUserRelList{}).Scan(&_tenantManagementUserRelList).Error
		if dbErr != nil {
			continue
		}

		// 如果用户的手机号码在平台有对应的用户，则查询平台是否有对应的账套租户用户关系，没有则建立账套租户用户关系
		for _, _user := range _users {
			// 查询账套租户是否有对应的用户关系
			_tenantManagementUserRel := _tenantManagementUserRelList.PickByTenantIDAndUserID(tenantManagement.Id, _user.Id)
			if _tenantManagementUserRel.Id == 0 {
				// 没有则新建
				_tenantManagementUserRel = model.NewTenantManagementUserRel(ctx, tenantManagement.Id, _user.Id)
				_db.Create(&_tenantManagementUserRel)
			}

			var (
				tenantManagementUserRel mysql.TenantManagementUserRel
				user                    userMysql.User
				exist                   bool
			)
			// 根据手机号码查询平台用户是否存在
			user, exist, err = repo.userDao.FirstByTenantManagementPhone(ctx, tx, _user.Phone)
			if err != nil {
				return
			}

			// 平台用户存在
			if exist {
				// 查询平台账套租户是否有对应的用户关系
				tenantManagementUserRel = tenantManagementUserRelList.PickByTenantIDAndUserID(tenantManagement.Id, user.Id)
				if tenantManagementUserRel.Id == 0 {
					// 没有则新建
					tenantManagementUserRel = model.NewTenantManagementUserRel(ctx, tenantManagement.Id, user.Id)
					tenantManagementUserRel, err = repo.tenantManagementUserRelDao.MustCreate(ctx, tx, tenantManagementUserRel)
					if err != nil {
						return
					}
				}
			}
		}
	}
	return
}

// 设置新用户的码单有效期为3天
func (repo *tenantManagementRepo) SetNewUserOrcDeadline(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) (mysql.TenantManagement, error) {
	var (
		tenantManagement mysql.TenantManagement
		rechargeHistory  mysql.RechargeHistory
		_db              *gorm.DB
	)

	// 获取账套信息
	tenantManagement, _, err := repo.tenantManagementDao.FirstByID(ctx, tx, tenantManagementId)
	if err != nil {
		return tenantManagement, err
	}

	if !tenantManagement.CodeListOrcIsRecognize {
		return tenantManagement, nil
	}

	// 设置码单有效期为激活时间之后的3天
	tenantManagement.CodeListOrcDeadLine = tenantManagement.ActivationTime.AddDate(0, 0, 3)
	tenantManagement.CodeListOrcStatus = common.CodeListStatusEnable

	// 创建充值记录
	rechargeHistory = mysql.NewRechargeHistory(tenantManagementId, tenantManagement.CodeListOrcDeadLine, "", "")
	rechargeHistory.Remark = "三天使用"
	rechargeHistory.Voucher = ""
	rechargeHistory.DeadLine = tenantManagement.CodeListOrcDeadLine

	// 保存充值记录
	_, err = repo.rechargeHistoryDao.MustCreate(ctx, tx, rechargeHistory)
	if err != nil {
		return tenantManagement, err
	}

	// 更新账套信息
	updatedTenantManagement, err := repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return updatedTenantManagement, err
	}

	_db = mysql_base.GetDBMap(updatedTenantManagement.Id)
	if _db == nil {
		_db, err = mysql_base.NewDBConn(ctx, updatedTenantManagement.DatabaseName, updatedTenantManagement.Id)
		if err != nil {
			return updatedTenantManagement, err
		}
	}

	err = _db.Updates(&updatedTenantManagement).Error

	return updatedTenantManagement, nil
}

// 清洗账套，提供3天的试用期
func (repo *tenantManagementRepo) CleanTenantManagements(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64) error {
	var (
		now = time.Now()
		_db *gorm.DB
	)

	// 如果指定了账套ID，则只清洗指定的账套
	tenantManagement, err := repo.tenantManagementDao.MustFirst(ctx, tx, tenantManagementId)
	if err != nil {
		return err
	}

	// 如果该账套没被识别过，则设置码单有效期为激活时间之后的3天
	if !tenantManagement.CodeListOrcIsRecognize { // 识别过
		// 判断是否过期
		if tenantManagement.CodeListOrcDeadLine.Before(time.Now()) {
			// 如果过期，则更改状态为禁用
			if tenantManagement.CodeListOrcStatus == common.CodeListStatusEnable {
				tenantManagement.CodeListOrcStatus = common.CodeListStatusDisable
				_, err = repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
				if err != nil {
					return err
				}
			}
		}
	}

	// 设置码单有效期为当前时间之后的3天
	tenantManagement.CodeListOrcDeadLine = now.AddDate(0, 0, 3)
	tenantManagement.CodeListOrcStatus = common.CodeListStatusEnable

	// 更新账套信息
	_, err = repo.tenantManagementDao.MustUpdate(ctx, tx, tenantManagement)
	if err != nil {
		return err
	}

	_db = mysql_base.GetDBMap(tenantManagement.Id)

	err = _db.Updates(&tenantManagement).Error
	if err != nil {
		return err
	}
	return nil
}

// 根据手机号码或者id删除账套 todo
func (repo *tenantManagementRepo) DeleteTenantManagementByPhone(ctx context.Context, tx *mysql_base.Tx, tenantManagementId uint64, phone string) (err error) {
	var (
		tenantManagement mysql.TenantManagement
		exist            bool
	)

	// 查找出对应的账套
	if tenantManagementId != 0 {
		tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, tenantManagementId)
		if err != nil {
			return
		}
	} else {
		tenantManagement, exist, err = repo.tenantManagementDao.FirstByPhone(ctx, tx, phone)
		if err != nil {
			return
		}
		if !exist {
			return errors.NewCustomError(errors.ErrCodeBusinessParameter, "未找到该用户")
		}
	}

	// 根据账套信息查找出关联的所有信息，用户，员工，用户账套关系，账套套餐关系，账套支付信息，账套用户收货地址
	var (
		tenantManagementUserRelList    mysql.TenantManagementUserRelList
		tenantManagementPackageRelList mysql.TenantManagementPackageRelList
		// rechargeHistoryList            mysql.RechargeHistoryList
	)
	tenantManagementUserRelList, err = repo.tenantManagementUserRelDao.FindByTenantManagementID(ctx, tx, tenantManagement.Id)
	if err != nil {
		return
	}
	tenantManagementPackageRelList, err = repo.tenantManagementPackageRelDao.FindByTenantManagementID(ctx, tx, tenantManagement.Id)
	if err != nil {
		return
	}

	for _, tenantManagementUserRel := range tenantManagementUserRelList {
		err = repo.tenantManagementUserRelDao.MustDelete(ctx, tx, tenantManagementUserRel)
		if err != nil {
			return
		}
	}
	for _, tenantManagementPackageRel := range tenantManagementPackageRelList {
		err = repo.tenantManagementPackageRelDao.MustDelete(ctx, tx, tenantManagementPackageRel)
		if err != nil {
			return
		}
	}

	return
}

// 获取租户管理列表
func (repo *tenantManagementRepo) GetTenantManagementList(ctx context.Context, tx *mysql_base.Tx) (mysql.TenantManagementList, error) {
	var (
		tenantManagementList mysql.TenantManagementList
	)

	tenantManagementList, err := repo.tenantManagementDao.FindAll(ctx, tx)
	if err != nil {
		return tenantManagementList, err
	}

	return tenantManagementList, nil
}

// 填充域名
func (repo *tenantManagementRepo) FillDomain(ctx context.Context, tx *mysql_base.Tx, q *structure.FillDomainParam) error {
	// 1. 验证domain格式
	if q.RequestDomain == "" || q.RequestDomainPrefix == "" {
		return errors.NewCustomError(errors.ErrCodeBusinessParameter, "域名/前缀不能为空")
	}
	// 2. 更新所有租户的domain字段
	err := repo.tenantManagementDao.UpdateDomain(ctx, tx, q)
	if err != nil {
		return err
	}
	return nil
}

func (repo *tenantManagementRepo) MustFirst(ctx context.Context, tx *mysql_base.Tx, id uint64) (tenantManagement mysql.TenantManagement, err error) {
	tenantManagement, err = repo.tenantManagementDao.MustFirst(ctx, tx, id)
	return
}

func (repo *tenantManagementRepo) FindByIDs(ctx context.Context, tx *mysql_base.Tx, objects ...interface{}) (list mysql.TenantManagementList, err error) {
	list, err = repo.tenantManagementDao.FindByIDs(ctx, tx, mysql_base.GetUInt64List(objects, "tenant_management_id"))
	return
}

func (repo *tenantManagementRepo) GetUnbindTenantManagementList(ctx context.Context, tx *mysql_base.Tx, query structure.QYWXGetUnBindTenantManagementListQuery) (list mysql.TenantManagementList, total int, err error) {
	list, total, err = repo.tenantManagementDao.GetUnbindTenantManagementList(ctx, tx, query)
	return
}
