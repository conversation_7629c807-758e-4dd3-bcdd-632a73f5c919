package third_party_extra

import (
	"hcscm/common/errors"
	"hcscm/model/mysql/mysql_base"
	"time"
)

// UserBehaviorTracking 用户行为跟踪
type UserBehaviorTracking struct {
	mysql_base.Model
	Id           uint64    `gorm:"column:id"`
	UserId       uint64    `gorm:"column:user_id"`       // 用户ID
	ActionType   string    `gorm:"column:action_type"`   // 行为类型 login/logout/view/click/search等
	ActionTarget string    `gorm:"column:action_target"` // 行为目标 页面/按钮/功能模块等
	ActionData   string    `gorm:"column:action_data"`   // 行为数据 JSON格式存储详细信息
	IpAddress    string    `gorm:"column:ip_address"`    // IP地址
	UserAgent    string    `gorm:"column:user_agent"`    // 用户代理
	SessionId    string    `gorm:"column:session_id"`    // 会话ID
	Duration     int64     `gorm:"column:duration"`      // 持续时间(毫秒)
	ActionTime   time.Time `gorm:"column:action_time"`   // 行为时间
}

func (u *UserBehaviorTracking) GetId() uint64 {
	return u.Id
}

// TableName UserBehaviorTracking 表名
func (UserBehaviorTracking) TableName() string {
	return "user_behavior_tracking"
}

func (u UserBehaviorTracking) CheckBeforeModify(tx *mysql_base.Tx) (exist bool, err error) {
	return
}

func (UserBehaviorTracking) OnceComplexKey() [][]string {
	r := [][]string{}
	return r
}

func (UserBehaviorTracking) GetNotExistError() errors.ErrCode {
	return errors.ErrCodeUserBehaviorTrackingNotExist
}

func (UserBehaviorTracking) GetAlreadyExistError() errors.ErrCode {
	return errors.ErrCodeUserBehaviorTrackingAlreadyExist
}

type UserBehaviorTrackingList []UserBehaviorTracking
