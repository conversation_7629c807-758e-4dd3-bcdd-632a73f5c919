package data_analysis

import (
	"hcscm/common/sale"
	"hcscm/common/should_collect_order"
	common "hcscm/common/system_consts"
	"hcscm/structure/system"
	"hcscm/tools"
	"time"
)

// GetWorkbenchDataReq 获取工作台数据请求
type GetWorkbenchDataReq struct {
	system.Param
	StartTime     tools.QueryTime `form:"start_time" json:"start_time"`           // 开始时间
	EndTime       tools.QueryTime `form:"end_time" json:"end_time"`               // 结束时间
	LastStartTime tools.QueryTime `form:"last_start_time" json:"last_start_time"` // 上期开始时间
	LastEndTime   tools.QueryTime `form:"last_end_time" json:"last_end_time"`     // 上期结束时间
}

func (r *GetWorkbenchDataReq) Adjust() {
}

// GetWorkbenchDataRes 获取工作台数据响应
type GetWorkbenchDataRes struct {
	system.ResponseData
	SaleAmount            float64 `json:"sale_amount"`              // 销售金额
	SaleAmountLastRate    string  `json:"sale_amount_last_rate"`    // 销售金额较上期
	SaleAmountDailyAvg    string  `json:"sale_amount_daily_avg"`    // 销售金额日均
	Roll                  int64   `json:"roll"`                     // 匹数
	RollLastRate          string  `json:"roll_last_rate"`           // 匹数较上期
	RollDailyAvg          string  `json:"roll_daily_avg"`           // 匹数日均
	BigCustomerCount      int     `json:"big_customer_count"`       // 大货客户数
	BigCustomerLastRate   string  `json:"big_customer_last_rate"`   // 大货客户数较上期
	BigCustomerDailyAvg   string  `json:"big_customer_daily_avg"`   // 大货客户数日均
	BigOrderWeight        string  `json:"big_order_weight"`         // 大货数量(带单位)
	BigOrderLastRate      string  `json:"big_order_last_rate"`      // 大货数量较上期
	BigOrderDailyAvg      string  `json:"big_order_daily_avg"`      // 大货数量日均
	PlateOrderWeight      string  `json:"plate_order_weight"`       // 剪板数量(带单位)
	PlateOrderLastRate    string  `json:"plate_order_last_rate"`    // 剪板数量较上期
	PlateOrderDailyAvg    string  `json:"plate_order_daily_avg"`    // 剪板数量日均
	SmallCustomerCount    int     `json:"small_customer_count"`     // 小样客户数
	SmallCustomerLastRate string  `json:"small_customer_last_rate"` // 小样客户数较上期
	SmallCustomerDailyAvg string  `json:"small_customer_daily_avg"` // 小样客户数日均
	TotalCount            int     `json:"total_count"`              // 总客户数
	TotalCountLastRate    string  `json:"total_count_last_rate"`    // 总客户数较上期
	TotalCountDailyAvg    string  `json:"total_count_daily_avg"`    // 总客户数日均
	TotalDebt             float64 `json:"total_debt"`               // 总欠款
	AdvanceCollectAmount  float64 `json:"advance_collect_amount"`   // 预收金额
	ActuallyCollectAmount float64 `json:"actually_collect_amount"`  // 实收金额
}

func (r GetWorkbenchDataRes) Adjust() {
}

type WorkbenchData struct {
	CustomerId        uint64  `gorm:"column:customer_id" sqlf:"should_collect_order.customer_id"`                        // 客户ID
	SettlePrice       float64 `gorm:"column:settle_price" sqlf:"should_collect_order_detail.settle_price"`               // 销售金额
	Roll              int64   `gorm:"column:roll" sqlf:"should_collect_order_detail.roll"`                               // 匹数
	SettleWeight      float64 `gorm:"column:settle_weight" sqlf:"should_collect_order_detail.settle_weight"`             // 结算数量
	MeasurementUnitId uint64  `gorm:"column:measurement_unit_id" sqlf:"should_collect_order_detail.measurement_unit_id"` // 计量单位ID
	AuxiliaryUnitId   int64   `gorm:"column:auxiliary_unit_id" sqlf:"should_collect_order_detail.auxiliary_unit_id"`     // 辅助单位ID
	CollectType       int64   `gorm:"column:collect_type" sqlf:"should_collect_order.collect_type"`                      // 单据类型
	SaleMode          int64   `gorm:"column:sale_mode" sqlf:"should_collect_order.sale_mode"`                            // 销售模式 1大货 2剪板 3客订大货 4客订剪板
}

type GetAnalysisHomeReq struct {
	system.ListQuery
	StartTime             tools.QueryTime       `form:"start_time" json:"start_time"`             // 开始时间
	EndTime               tools.QueryTime       `form:"end_time" json:"end_time"`                 // 结束时间
	TrendStartTime        tools.QueryTime       `form:"trend_start_time" json:"trend_start_time"` // 趋势分析开始时间
	TrendEndTime          tools.QueryTime       `form:"trend_end_time" json:"trend_end_time"`     // 趋势分析结束时间
	CustomerRatioDate     tools.QueryTime       `form:"-" json:"-"`                               // 新老客户占比统计日期
	SaleSystemId          uint64                `form:"sale_system_id" json:"sale_system_id"`     // 营销体系id
	SaleUserId            uint64                `form:"sale_user_id" json:"sale_user_id"`         // 销售员id
	Type                  int64                 `form:"type" json:"type"`                         // 分析类型 1:产品分析 2:客户分析
	IsTrendForDay         bool                  `form:"-" json:"-"`
	SaleModeIden          int64                 `form:"sale_mode_iden" json:"sale_mode_iden"`     // 销售模式id
	CustomerId            tools.QueryIntList    `form:"customer_id" json:"customer_id"`           // 客户id
	ProductId             uint64                `form:"product_id" json:"product_id"`             // 产品id
	ProductColorId        tools.QueryIntList    `form:"product_color_id" json:"product_color_id"` // 产品颜色id
	Location              tools.QueryStringList `form:"location" json:"location"`                 // 城市
	TypeFabricId          tools.QueryIntList    `form:"type_fabric_id" json:"type_fabric_id"`     // 布种类型id
	SaleGroupId           tools.QueryIntList    `form:"sale_group_id" json:"sale_group_id"`
	IfShowOldCustomerData bool                  `form:"is_show_old_customer" json:"is_show_old_customer"`
	IfShowNewCustomerData bool                  `form:"is_show_new_customer" json:"is_show_new_customer"`
}

type GetTopProductReq struct {
	system.ListQuery
	StartTime             tools.QueryTime       `form:"start_time" json:"start_time"`             // 开始时间
	EndTime               tools.QueryTime       `form:"end_time" json:"end_time"`                 // 结束时间
	SaleSystemId          uint64                `form:"sale_system_id" json:"sale_system_id"`     // 营销体系id
	SaleUserId            uint64                `form:"sale_user_id" json:"sale_user_id"`         // 销售员id
	CustomerId            uint64                `form:"customer_id" json:"customer_id"`           // 客户id
	ProductId             uint64                `form:"product_id" json:"product_id"`             // 产品id
	ProductColorId        tools.QueryIntList    `form:"product_color_id" json:"product_color_id"` // 产品颜色id
	SaleModeIden          int64                 `form:"sale_mode_iden" json:"sale_mode_iden"`     // 销售模式id
	Location              tools.QueryStringList `form:"location" json:"location"`
	TypeFabricId          tools.QueryIntList    `form:"type_fabric_id" json:"type_fabric_id"`
	SaleGroupId           tools.QueryIntList    `form:"sale_group_id" json:"sale_group_id"`
	IfShowOldCustomerData bool                  `form:"is_show_old_customer" json:"is_show_old_customer"`
	IfShowNewCustomerData bool                  `form:"is_show_new_customer" json:"is_show_new_customer"`
}

func (r *GetAnalysisHomeReq) Adjust() {
}

// SELECT
//
//	should_collect_order_detail.material_id AS product_id,
//	COUNT(DISTINCT should_collect_order_detail.product_color_id) AS color_count,
//	COUNT(DISTINCT should_collect_order.customer_id) AS customer_count,
//	SUM(CASE WHEN should_collect_order.collect_type = 1 THEN should_collect_order_detail.roll ELSE 0 END) AS sale_roll,
//	SUM(CASE WHEN should_collect_order.collect_type = 2 THEN should_collect_order_detail.roll ELSE 0 END) AS return_roll,
//	SUM(should_collect_order_detail.roll) AS total_roll,
//	SUM(should_collect_order_detail.settle_weight) AS settle_weight, -- 使用MAX或MIN来选取代表值
//	MAX(should_collect_order_detail.measurement_unit_id) AS measurement_unit_id, -- 使用MAX或MIN来选取代表值
//	SUM(should_collect_order_detail.settle_price) AS total_sale_amount,
//	should_collect_order.sale_mode,
//	-- 添加过去15天每天的匹数总计
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 14 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_14_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 13 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_13_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 12 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_12_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 11 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_11_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 10 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_10_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 9 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_9_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 8 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_8_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 7 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_7_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 6 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_6_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 5 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_5_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 4 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_4_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 3 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_3_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 2 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_2_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 1 DAY THEN should_collect_order_detail.roll ELSE 0 END) AS day_1_roll,
//	SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() THEN should_collect_order_detail.roll ELSE 0 END) AS day_0_roll
//
// FROM
//
//	`should_collect_order_detail`
//	LEFT JOIN should_collect_order ON should_collect_order.id = should_collect_order_detail.should_collect_order_id
//
// WHERE
//
//	(should_collect_order.order_time BETWEEN '2025-02-13' AND '2025-02-13 23:59:59'
//	AND should_collect_order.collect_type IN (1, 2)
//	AND should_collect_order.audit_status = 2)
//	AND should_collect_order_detail.delete_time = '0000-00-00 00:00:00'
//	AND should_collect_order.sale_mode in (1,3)
//
// GROUP BY
//
//	should_collect_order_detail.material_id
//
// ORDER BY
//
//	total_sale_amount DESC;
//
// Top10ProductDetail 前十产品详细信息
type TopProductDetail struct {
	ProductId         uint64  `json:"product_id" gorm:"column:product_id" sqlf:"should_collect_order_detail.material_id" relate:"product_id"`
	ProductColorIds   string  `json:"product_color_ids" gorm:"column:product_color_ids" sqlf:"GROUP_CONCAT(DISTINCT should_collect_order_detail.product_color_id ORDER BY should_collect_order_detail.product_color_id SEPARATOR ',')"`     // 产品颜色ID
	ColorCount        int     `json:"color_count" gorm:"column:color_count" sqlf:"COUNT(DISTINCT should_collect_order_detail.product_color_id)" relate:"product_color_id"`                                                                  // 产品颜色ID
	CustomerIds       string  `json:"customer_ids" gorm:"column:customer_ids" sqlf:"GROUP_CONCAT(DISTINCT should_collect_order.customer_id ORDER BY should_collect_order.customer_id SEPARATOR ',')"`                                       // 客户ID
	CustomerCount     int     `json:"customer_count" gorm:"column:customer_count" sqlf:"COUNT(DISTINCT should_collect_order.customer_id)" relate:"customer_id"`                                                                             // 客户ID
	SaleRoll          int64   `json:"sale_roll" gorm:"column:sale_roll" sqlf:"SUM(CASE WHEN should_collect_order.collect_type = 1 THEN should_collect_order_detail.roll ELSE 0 END)"`                                                       // 销售匹数
	ReturnRoll        int64   `json:"return_roll" gorm:"column:return_roll" sqlf:"SUM(CASE WHEN should_collect_order.collect_type = 2 THEN should_collect_order_detail.roll ELSE 0 END)"`                                                   // 退货匹数
	TotalRoll         int64   `json:"total_roll" gorm:"column:total_roll" sqlf:"SUM(should_collect_order_detail.roll)"`                                                                                                                     // 总匹数
	TotalWeight       float64 `json:"total_weight" gorm:"column:total_weight" sqlf:"SUM(should_collect_order_detail.settle_weight)"`                                                                                                        // 总数量(带单位)
	MeasurementUnitId uint64  `json:"measurement_unit_id" gorm:"column:measurement_unit_id" sqlf:"MAX(should_collect_order_detail.measurement_unit_id)"`                                                                                    // 计量单位ID
	SaleAmount        float64 `json:"sale_amount" gorm:"column:total_sale_amount" sqlf:"SUM(should_collect_order_detail.settle_price)"`                                                                                                     // SaleMode                                              // 销售金额
	SaleMode          int64   `json:"sale_mode" gorm:"column:sale_mode" sqlf:"should_collect_order.sale_mode"`                                                                                                                              // 销售模式 1大货 2剪板 3客订大货 4客订剪板                                                                                                      // 销售模式 1大货 2剪板 3客订大货 4客订剪板
	Day14SaleMount    int64   `json:"day_14_sale_mount" gorm:"column:day_14_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 14 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近15客户下单该产品天每天的匹数
	Day13SaleMount    int64   `json:"day_13_sale_mount" gorm:"column:day_13_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 13 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近14客户下单该产品天每天的匹数
	Day12SaleMount    int64   `json:"day_12_sale_mount" gorm:"column:day_12_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 12 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近13客户下单该产品天每天的匹数
	Day11SaleMount    int64   `json:"day_11_sale_mount" gorm:"column:day_11_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 11 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近12客户下单该产品天每天的匹数
	Day10SaleMount    int64   `json:"day_10_sale_mount" gorm:"column:day_10_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 10 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近11客户下单该产品天每天的匹数
	Day9SaleMount     int64   `json:"day_9_sale_mount" gorm:"column:day_9_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 9 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近10客户下单该产品天每天的匹数
	Day8SaleMount     int64   `json:"day_8_sale_mount" gorm:"column:day_8_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 8 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近9客户下单该产品天每天的匹数
	Day7SaleMount     int64   `json:"day_7_sale_mount" gorm:"column:day_7_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 7 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近8客户下单该产品天每天的匹数
	Day6SaleMount     int64   `json:"day_6_sale_mount" gorm:"column:day_6_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 6 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近7客户下单该产品天每天的匹数
	Day5SaleMount     int64   `json:"day_5_sale_mount" gorm:"column:day_5_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 5 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近6客户下单该产品天每天的匹数
	Day4SaleMount     int64   `json:"day_4_sale_mount" gorm:"column:day_4_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 4 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近5客户下单该产品天每天的匹数
	Day3SaleMount     int64   `json:"day_3_sale_mount" gorm:"column:day_3_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 3 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近4客户下单该产品天每天的匹数
	Day2SaleMount     int64   `json:"day_2_sale_mount" gorm:"column:day_2_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 2 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近3客户下单该产品天每天的匹数
	Day1SaleMount     int64   `json:"day_1_sale_mount" gorm:"column:day_1_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 1 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近2客户下单该产品天每天的匹数
	Day0SaleMount     int64   `json:"day_0_sale_mount" gorm:"column:day_0_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() THEN should_collect_order_detail.settle_price ELSE 0 END)"`                     // 最近1客户下单该产品天每天的匹数
}

// Top10ProductDetail 前十产品详细信息
type TopColorDetail struct {
	ProductId         uint64  `json:"product_id" gorm:"column:product_id" sqlf:"should_collect_order_detail.material_id" relate:"product_id"`
	ProductColorId    uint64  `json:"product_color_id" gorm:"column:product_color_id" sqlf:"should_collect_order_detail.product_color_id" relate:"product_color_id"` // 产品颜色ID
	CustomerCount     int     `json:"customer_count" gorm:"column:customer_count" sqlf:"COUNT(DISTINCT should_collect_order.customer_id)"`
	CustomerIds       string  `json:"customer_ids" gorm:"column:customer_ids" sqlf:"GROUP_CONCAT(DISTINCT should_collect_order.customer_id ORDER BY should_collect_order.customer_id SEPARATOR ',' )"`                                      // 客户数量
	SaleRoll          int64   `json:"sale_roll" gorm:"column:sale_roll" sqlf:"SUM(CASE WHEN should_collect_order.collect_type = 1 THEN should_collect_order_detail.roll ELSE 0 END)"`                                                       // 销售匹数
	ReturnRoll        int64   `json:"return_roll" gorm:"column:return_roll" sqlf:"SUM(CASE WHEN should_collect_order.collect_type = 2 THEN should_collect_order_detail.roll ELSE 0 END)"`                                                   // 退货匹数
	TotalRoll         int64   `json:"total_roll" gorm:"column:total_roll" sqlf:"SUM(should_collect_order_detail.roll)"`                                                                                                                     // 总匹数
	SettleWeight      float64 `json:"settle_weight" gorm:"column:settle_weight" sqlf:"SUM(should_collect_order_detail.settle_weight)"`                                                                                                      // 结算重量
	MeasurementUnitId uint64  `json:"measurement_unit_id" gorm:"column:measurement_unit_id" sqlf:"MAX(should_collect_order_detail.measurement_unit_id)"`                                                                                    // 计量单位ID
	TotalSaleAmount   float64 `json:"total_sale_amount" gorm:"column:total_sale_amount" sqlf:"SUM(should_collect_order_detail.settle_price)"`                                                                                               // 总销售金额
	SaleMode          int     `json:"sale_mode" gorm:"column:sale_mode"`                                                                                                                                                                    // 销售模式
	Day14SaleMount    int64   `json:"day_14_sale_mount" gorm:"column:day_14_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 14 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近15客户下单该产品天每天的匹数
	Day13SaleMount    int64   `json:"day_13_sale_mount" gorm:"column:day_13_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 13 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近14客户下单该产品天每天的匹数
	Day12SaleMount    int64   `json:"day_12_sale_mount" gorm:"column:day_12_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 12 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近13客户下单该产品天每天的匹数
	Day11SaleMount    int64   `json:"day_11_sale_mount" gorm:"column:day_11_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 11 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近12客户下单该产品天每天的匹数
	Day10SaleMount    int64   `json:"day_10_sale_mount" gorm:"column:day_10_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 10 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近11客户下单该产品天每天的匹数
	Day9SaleMount     int64   `json:"day_9_sale_mount" gorm:"column:day_9_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 9 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近10客户下单该产品天每天的匹数
	Day8SaleMount     int64   `json:"day_8_sale_mount" gorm:"column:day_8_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 8 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近9客户下单该产品天每天的匹数
	Day7SaleMount     int64   `json:"day_7_sale_mount" gorm:"column:day_7_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 7 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近8客户下单该产品天每天的匹数
	Day6SaleMount     int64   `json:"day_6_sale_mount" gorm:"column:day_6_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 6 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近7客户下单该产品天每天的匹数
	Day5SaleMount     int64   `json:"day_5_sale_mount" gorm:"column:day_5_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 5 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近6客户下单该产品天每天的匹数
	Day4SaleMount     int64   `json:"day_4_sale_mount" gorm:"column:day_4_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 4 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近5客户下单该产品天每天的匹数
	Day3SaleMount     int64   `json:"day_3_sale_mount" gorm:"column:day_3_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 3 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近4客户下单该产品天每天的匹数
	Day2SaleMount     int64   `json:"day_2_sale_mount" gorm:"column:day_2_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 2 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近3客户下单该产品天每天的匹数
	Day1SaleMount     int64   `json:"day_1_sale_mount" gorm:"column:day_1_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 1 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近2客户下单该产品天每天的匹数
	Day0SaleMount     int64   `json:"day_0_sale_mount" gorm:"column:day_0_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() THEN should_collect_order_detail.settle_price ELSE 0 END)"`                     // 最近1客户下单该产品天每天的匹数
}

type TopCustomerDeatil struct {
	CustomerId        uint64  `json:"customer_id" gorm:"column:customer_id" sqlf:"customer_id" relate:"customer_id"`                                                                                                                        // 客户ID
	ColorCount        int64   `json:"color_count" gorm:"column:color_count" sqlf:"COUNT(DISTINCT should_collect_order_detail.product_color_id)"`                                                                                            // 颜色数量
	ProductCount      int64   `json:"product_count" gorm:"column:product_count" sqlf:"COUNT(DISTINCT should_collect_order_detail.material_id)"`                                                                                             // 产品数量
	SaleRoll          int64   `json:"sale_roll" gorm:"column:sale_roll" sqlf:"SUM(CASE WHEN should_collect_order.collect_type = 1 THEN should_collect_order_detail.roll ELSE 0 END)"`                                                       // 销售匹数
	ReturnRoll        int64   `json:"return_roll" gorm:"column:return_roll" sqlf:"SUM(CASE WHEN should_collect_order.collect_type = 2 THEN should_collect_order_detail.roll ELSE 0 END)"`                                                   // 退货匹数
	TotalRoll         int64   `json:"total_roll" gorm:"column:total_roll" sqlf:"SUM(should_collect_order_detail.roll)"`                                                                                                                     // 总匹数
	SettleWeight      float64 `json:"settle_weight" gorm:"column:settle_weight" sqlf:"SUM(should_collect_order_detail.settle_weight)"`                                                                                                      // 结算重量
	MeasurementUnitId uint64  `json:"measurement_unit_id" gorm:"column:measurement_unit_id" sqlf:"MAX(should_collect_order_detail.measurement_unit_id)"`                                                                                    // 计量单位ID
	TotalSaleAmount   float64 `json:"total_sale_amount" gorm:"column:total_sale_amount" sqlf:"SUM(should_collect_order_detail.settle_price)"`                                                                                               // 总销售金额
	SaleMode          int64   `json:"sale_mode" gorm:"column:sale_mode" sqlf:"should_collect_order.sale_mode"`                                                                                                                              // 销售模式
	Day14SaleMount    int64   `json:"day_14_sale_mount" gorm:"column:day_14_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 14 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近15客户下单该产品天每天的匹数
	Day13SaleMount    int64   `json:"day_13_sale_mount" gorm:"column:day_13_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 13 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近14客户下单该产品天每天的匹数
	Day12SaleMount    int64   `json:"day_12_sale_mount" gorm:"column:day_12_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 12 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近13客户下单该产品天每天的匹数
	Day11SaleMount    int64   `json:"day_11_sale_mount" gorm:"column:day_11_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 11 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近12客户下单该产品天每天的匹数
	Day10SaleMount    int64   `json:"day_10_sale_mount" gorm:"column:day_10_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 10 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"` // 最近11客户下单该产品天每天的匹数
	Day9SaleMount     int64   `json:"day_9_sale_mount" gorm:"column:day_9_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 9 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近10客户下单该产品天每天的匹数
	Day8SaleMount     int64   `json:"day_8_sale_mount" gorm:"column:day_8_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 8 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近9客户下单该产品天每天的匹数
	Day7SaleMount     int64   `json:"day_7_sale_mount" gorm:"column:day_7_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 7 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近8客户下单该产品天每天的匹数
	Day6SaleMount     int64   `json:"day_6_sale_mount" gorm:"column:day_6_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 6 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近7客户下单该产品天每天的匹数
	Day5SaleMount     int64   `json:"day_5_sale_mount" gorm:"column:day_5_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 5 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近6客户下单该产品天每天的匹数
	Day4SaleMount     int64   `json:"day_4_sale_mount" gorm:"column:day_4_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 4 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近5客户下单该产品天每天的匹数
	Day3SaleMount     int64   `json:"day_3_sale_mount" gorm:"column:day_3_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 3 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近4客户下单该产品天每天的匹数
	Day2SaleMount     int64   `json:"day_2_sale_mount" gorm:"column:day_2_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 2 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近3客户下单该产品天每天的匹数
	Day1SaleMount     int64   `json:"day_1_sale_mount" gorm:"column:day_1_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() - INTERVAL 1 DAY THEN should_collect_order_detail.settle_price ELSE 0 END)"`    // 最近2客户下单该产品天每天的匹数
	Day0SaleMount     int64   `json:"day_0_sale_mount" gorm:"column:day_0_sale_mount" sqlf:"SUM(CASE WHEN DATE(should_collect_order.order_time) = CURDATE() THEN should_collect_order_detail.settle_price ELSE 0 END)"`                     // 最近1客户下单该产品天每天的匹数
}

type TopProductDetailList []TopProductDetail
type TopColorDetailList []TopColorDetail
type TopCustomerDeatilList []TopCustomerDeatil

// Top10CustomerDetail 前十客户详细信息
type Top10CustomerDetail struct {
	CustomerId         uint64    `json:"customer_id"`         // 客户ID
	CustomerName       string    `json:"customer_name"`       // 客户名称
	SaleRoll           int64     `json:"sale_roll"`           // 总匹数
	SaleAmount         float64   `json:"sale_amount"`         // 销售金额
	ProductCount       int       `json:"product_count"`       // 面料种类数量
	ColorCount         int       `json:"color_count"`         // 颜色数量
	RollRatio          string    `json:"roll_ratio"`          // 匹数占比
	Last15DaySaleMount []float64 `json:"last_15_day_roll"`    // 最近15天每天的匹数
	TotalSettleWeight  string    `json:"total_settle_weight"` // 销售数量
	TotalReturnRoll    int64     `json:"total_return_roll"`   // 退货匹数
	TotalRoll          int64     `json:"total_roll"`          // 合计匹数
}

// 将城市销售额转换为切片并排序
type CityData struct {
	Name  string
	Value float64
}

type ColorSaleInfo struct {
	ColorId uint64
	Amount  float64
}

// GetAnalysisHomeRes 获取产品分析首页数据响应
type GetAnalysisHomeRes struct {
	system.ResponseData
	TotalSaleAmount   float64  `json:"total_sale_amount"`            // 产品总销售额
	TotalRoll         int64    `json:"total_roll"`                   // 产品总匹数
	TotalSettleWeight string   `json:"total_settle_weight"`          // 产品总数量（按单位分组）
	Top10ProductIds   []uint64 `json:"top_10_product_ids,omitempty"` // 前十产品id
	//Top10BigProducts             []Top10ProductDetail     `json:"top_10_products"`               // 前十产品详细信息
	TotalCustomerCount        int                      `json:"total_customer_count"`          // 客户数
	Top10ProductCategoryRatio []ProductCategoryRatio   `json:"top_10_product_category_ratio"` // 前十产品类别占比
	Top10SaleGroupRatio       []SaleGroupRatio         `json:"top_10_sale_group_ratio"`       // 前十客户类别占比
	Top10CustomerSaleRatio    []Top10CustomerSaleRatio `json:"customer_sale_ratio"`           // 客户销售占比
	NewCustomerRatio          string                   `json:"new_customer_ratio"`            // 新客户占比
	OldCustomerRatio          string                   `json:"old_customer_ratio"`            // 老客户占比
	SalesAreaStat             []SalesAreaStat          `json:"sales_area_stat"`               // 销售地区统计
}
type SalesAreaStat struct {
	Name  string  `json:"name"`  // 地区名称
	Value float64 `json:"value"` // 销售金额
}

// ProductCategoryRatio 产品类别占比
type ProductCategoryRatio struct {
	TypeId    uint64 `json:"type_id"`    // 布种类型ID
	TypeName  string `json:"type_name"`  // 布种类型名称
	SaleRatio string `json:"sale_ratio"` // 销售占比
}

// SaleGroupRatio 销售群体占比
type SaleGroupRatio struct {
	SaleGroupId   uint64 `json:"sale_group_id"`   // 销售群体ID
	SaleGroupName string `json:"sale_group_name"` // 销售群体名称
	SaleRatio     string `json:"sale_ratio"`      // 销售占比
}

func (r GetAnalysisHomeRes) Adjust() {
}

// ProductAnalysisData 产品分析数据
type ProductAnalysisData struct {
	ProductId         uint64    `gorm:"column:product_id" sqlf:"should_collect_order_detail.material_id" relate:"product_id"`                           // 产品ID
	ProductColorId    uint64    `gorm:"column:product_color_id" sqlf:"should_collect_order_detail.product_color_id" relate:"product_color_id"`          // 产品颜色ID
	CustomerId        uint64    `gorm:"column:customer_id" sqlf:"should_collect_order.customer_id" relate:"customer_id"`                                // 客户ID
	CollectType       int64     `gorm:"column:collect_type" sqlf:"should_collect_order.collect_type" `                                                  // 收款类型
	SaleAmount        float64   `gorm:"column:sale_amount" sqlf:"should_collect_order_detail.settle_price"`                                             // 销售金额
	TotalRoll         int64     `gorm:"column:total_roll" sqlf:"should_collect_order_detail.roll" `                                                     // 总匹数
	TotalSettleWeight float64   `gorm:"column:total_settle_weight" sqlf:"should_collect_order_detail.settle_weight"`                                    // 总结算数量
	MeasurementUnitId uint64    `gorm:"column:measurement_unit_id" sqlf:"should_collect_order_detail.measurement_unit_id" relate:"measurement_unit_id"` // 计量单位ID
	OrderTime         time.Time `gorm:"column:order_time" sqlf:"should_collect_order.order_time" `                                                      // 订单时间
	SaleMode          int64     `gorm:"column:sale_mode" sqlf:"should_collect_order.sale_mode"`                                                         // 销售模式 1大货 2剪板 3客订大货 4客订剪板
}

// CustomerFirstOrder 客户首次下单信息
type CustomerFirstOrder struct {
	CustomerId     uint64    `gorm:"column:customer_id" sqlf:"should_collect_order.customer_id"`          // 客户ID
	FirstOrderTime time.Time `gorm:"column:first_order_time" sqlf:"MIN(should_collect_order.order_time)"` // 首次下单时间
}

// GetProAnalysisTrendReq 获取产品分析趋势数据请求
//type GetProAnalysisTrendReq struct {
//	system.Param
//	// 分析维度类型
//	Type int64 `form:"type" json:"type"` // 1：日分析，2：月分析
//	// 开始时间
//	StartTime tools.QueryTime `form:"start_time" json:"start_time"`
//	// 结束时间
//	EndTime tools.QueryTime `form:"end_time" json:"end_time"`
//	// 产品ID
//	ProductId uint64 `form:"product_id" json:"product_id"`
//	// 产品颜色ID
//	ProductColorId uint64 `form:"product_color_id" json:"product_color_id"`
//	CustomerId     uint64 `form:"customer_id" json:"customer_id"`
//}

// GetProAnalysisTrendRes 获取产品分析趋势数据响应
type GetProAnalysisTrendRes struct {
	system.ResponseData
	// 三年的匹数统计(今年、去年、前年)
	ThreeYearRoll struct {
		ThisYear              []int64 `json:"this_year"`                // 今年每月/日匹数
		LastYear              []int64 `json:"last_year"`                // 去年每月/日匹数
		BeforeYear            []int64 `json:"before_year"`              // 前年每月/日匹数
		ThisMonthEstimateRoll int64   `json:"this_month_estimate_roll"` // 当月预估Roll
		ThisDayEstimateRoll   float64 `json:"this_day_estimate_roll"`   // 当天预估Roll
	} `json:"three_year_roll"`

	// 三年的销售金额统计(今年、去年、前年)
	ThreeYearSaleAmount struct {
		ThisYear                    interface{} `json:"this_year"`                       // 今年每月/日销售金额
		LastYear                    interface{} `json:"last_year"`                       // 去年每月/日销售金额
		BeforeYear                  interface{} `json:"before_year"`                     // 前年每月/日销售金额
		ThisMonthEstimateSaleAmount float64     `json:"this_month_estimate_sale_amount"` // 当月预估销售金额
		ThisDayEstimateSaleAmount   float64     `json:"this_day_estimate_sale_amount"`   // 当天预估销售金额
	} `json:"three_year_sale_amount"`

	// 三年的数量统计(今年、去年、前年)
	ThreeYearSettleWeight struct {
		ThisYear                      interface{} `json:"this_year"`                         // 今年每月/日数量
		LastYear                      interface{} `json:"last_year"`                         // 去年每月/日数量
		BeforeYear                    interface{} `json:"before_year"`                       // 前年每月/日数量
		ThisMonthEstimateSettleWeight float64     `json:"this_month_estimate_settle_weight"` // 当月预估数量
		ThisDayEstimateSettleWeight   float64     `json:"this_day_estimate_settle_weight"`   // 当天预估数量
	} `json:"three_year_settle_weight"`

	// 五月的预测数据区间，比如（110-5） ~（110+5），存110和5
	MayPredictData struct {
		PredictData float64 `json:"predict_data"` // 预测数据
		Range       float64 `json:"range"`        // 区间
	} `json:"may_predict_data"`
}

// ProductColorSaleRank 产品颜色销售排行
type ProductColorSaleRank struct {
	ColorId       uint64  `json:"color_id"`         // 颜色ID
	ColorCode     string  `json:"color_code"`       // 颜色编号
	ColorName     string  `json:"color_name"`       // 颜色名称
	Roll          int64   `json:"roll"`             // 匹数
	SettleWeight  string  `json:"settle_weight"`    // 结算重量(带单位)
	SaleAmount    float64 `json:"sale_amount"`      // 销售金额
	CustomerCount int     `json:"customer_count"`   // 客户数
	RollRatio     string  `json:"roll_ratio"`       // 匹数占比
	Last15DayRoll []int64 `json:"last_15_day_roll"` // 最近15天每天的匹数
	ReturnRoll    int64   `json:"return_roll"`      // 退货匹数
	TotalRoll     int64   `json:"total_roll"`       // 合计匹数
}

// GetProAnalysisProductRes 获取产品维度分析数据响应
type GetProAnalysisProductRes struct {
	system.ResponseData
	ProductId         uint64  `json:"product_id"`          // 产品ID
	ProductCode       string  `json:"product_code"`        // 产品编号
	ProductName       string  `json:"product_name"`        // 产品名称
	TotalSaleAmount   float64 `json:"total_sale_amount"`   // 销售总额
	TotalRoll         int64   `json:"total_roll"`          // 总匹数
	TotalSettleWeight string  `json:"total_settle_weight"` // 总数量（按单位分组）
	// 前十销售群体占比
	Top10SaleGroupRatio []SaleGroupRatio `json:"top_10_sale_group_ratio"`
	TotalCustomerCount  int              `json:"total_customer_count"` // 客户数
	NewCustomerRatio    string           `json:"new_customer_ratio"`   // 新客户占比
	OldCustomerRatio    string           `json:"old_customer_ratio"`   // 老客户占比
	// 色号占比
	ColorRatio []ColorRatio `json:"color_ratio"`
	// 销售地区
	SalesAreaStat []SalesAreaStat `json:"sales_area_stat"`
}

type GetCusAnalysisCustomerRes struct {
	system.ResponseData
	CustomerId                uint64                 `json:"customer_id"`         // 客户ID
	CustomerName              string                 `json:"customer_name"`       // 客户名称
	TotalSaleAmount           float64                `json:"total_sale_amount"`   // 该客户在筛选时间内的销售总额
	TotalRoll                 int64                  `json:"total_roll"`          // 该客户在筛选时间内的总匹数
	TotalSettleWeight         string                 `json:"total_settle_weight"` // 该客户在筛选时间内的总数量（按单位分组）
	Top10ProductCategoryRatio []ProductCategoryRatio `json:"top_10_product_category_ratio"`
}

func (r GetProAnalysisProductRes) Adjust() {
}

type GetProAnalysisCustomerRes struct {
	system.ResponseData
	ProductId          uint64          `json:"product_id"`           // 产品ID
	ProductCode        string          `json:"product_code"`         // 产品编号
	ProductName        string          `json:"product_name"`         // 产品名称
	ProductColorId     uint64          `json:"product_color_id"`     // 产品颜色ID
	ProductColorCode   string          `json:"product_color_code"`   // 产品颜色编号
	ProductColorName   string          `json:"product_color_name"`   // 产品颜色名称
	TotalSaleAmount    float64         `json:"total_sale_amount"`    // 销售总额
	TotalRoll          int64           `json:"total_roll"`           // 总匹数
	TotalSettleWeight  string          `json:"total_settle_weight"`  // 总数量（按单位分组）
	TotalCustomerCount int             `json:"total_customer_count"` // 客户数
	SalesAreaStat      []SalesAreaStat `json:"sales_area_stat"`      // 销售地区
	//Top10SaleGroupRatio []SaleGroupRatio   `json:"top_10_sale_group_ratio"` // 前十销售群体占比
}

// GetCusAnalysisProductRes 获取客户产品维度分析数据响应
type GetCusAnalysisProductRes struct {
	system.ResponseData
	CustomerId        uint64  `json:"customer_id"`         // 客户ID
	CustomerName      string  `json:"customer_name"`       // 客户名称
	ProductId         uint64  `json:"product_id"`          // 点击进来对应的产品id
	ProductCode       string  `json:"product_code"`        // 产品编号
	ProductName       string  `json:"product_name"`        // 产品名称
	TotalSaleAmount   float64 `json:"total_sale_amount"`   // 销售总额
	TotalRoll         int64   `json:"total_roll"`          // 总匹数
	TotalSettleWeight string  `json:"total_settle_weight"` // 总数量（按单位分组）
}

func (r GetProAnalysisCustomerRes) Adjust() {
}

// PeriodStats 统计数据结构
type PeriodStats struct {
	SaleAmount         float64             // 销售单金额
	Roll               int64               // 匹数
	BigCustomerIds     map[uint64]struct{} // 大货客户ID集合
	PlateCustomerIds   map[uint64]struct{} // 剪板客户ID集
	BigSettleWeights   map[uint64]float64  // 按计量单位分组的结算数量
	PlateSettleWeights map[uint64]float64  // 按计量单位分组的结算数量
	BigOrderWeights    float64             // 大货数量
	PlateWeights       float64             // 剪板数量
	SaleMode           int64               // 销售模式 1大货 2剪板 3客订大货 4客订剪板
}

// ColorRatio 色号占比
type ColorRatio struct {
	ColorId   uint64 `json:"color_id"`   // 颜色ID
	ColorCode string `json:"color_code"` // 颜色编号
	ColorName string `json:"color_name"` // 颜色名称
	SaleRatio string `json:"sale_ratio"` // 销售占比
}

// CustomerSaleRatio 客户销售占比
type Top10CustomerSaleRatio struct {
	CustomerId   uint64 `json:"customer_id"`   // 客户ID
	CustomerName string `json:"customer_name"` // 客户名称
	SaleRatio    string `json:"sale_ratio"`    // 销售占比
}

// GetTop10ProductRes 获取前十产品响应
type GetTopDataRes struct {
	TopDataList interface{} `json:"top_data_list"` // 前十产品列表，可以是GetTopProductResForTop或GetTopColorResForTop的切片
	Total       int         `json:"total"`
}

type GetTopProductResForTop struct {
	ProductId           uint64    `json:"product_id"`          // 产品ID
	ProductName         string    `json:"product_name"`        // 产品名称
	ProductCode         string    `json:"product_code"`        // 产品编号
	ColorCount          int       `json:"color_count"`         // 颜色数量
	ProductColorIds     string    `json:"product_color_ids"`   // 产品颜色ID
	CustomerCount       int       `json:"customer_count"`      // 客户数量
	CustomerIds         string    `json:"customer_ids"`        // 客户ID
	TotalSaleAmount     float64   `json:"total_sale_amount"`   // 销售总额
	SaleRoll            int64     `json:"sale_roll"`           // 销售匹数
	ReturnRoll          int64     `json:"return_roll"`         // 退货匹数
	TotalRoll           int64     `json:"total_roll"`          // 总匹数
	TotalSettleWeight   string    `json:"total_settle_weight"` // 总数量（按单位分组）
	RollRatio           string    `json:"roll_ratio"`          // 匹数占比
	Last15DaySaleAmount []float64 `json:"last_15_day_sale_amount"`
}

func (r *GetTopDataRes) Adjust() {}

// GetTopColorResForTop 获取颜色TOP数据响应
type GetTopColorResForTop struct {
	ProductId           uint64    `json:"product_id"`
	ProductName         string    `json:"product_name"`
	ProductCode         string    `json:"product_code"`
	ProductColorId      uint64    `json:"product_color_id"`    // 产品颜色ID
	ProductColorName    string    `json:"product_color_name"`  // 产品颜色名称
	ProductColorCode    string    `json:"product_color_code"`  // 产品颜色编号
	CustomerCount       int       `json:"customer_count"`      // 客户数量
	CustomerIds         string    `json:"customer_ids"`        // 客户ID
	TotalSaleAmount     float64   `json:"total_sale_amount"`   // 销售总额
	SaleRoll            int64     `json:"sale_roll"`           // 销售匹数
	ReturnRoll          int64     `json:"return_roll"`         // 退货匹数
	TotalRoll           int64     `json:"total_roll"`          // 总匹数
	TotalSettleWeight   string    `json:"total_settle_weight"` // 总数量（按单位分组）
	SaleAmountRatio     string    `json:"sale_amount_ratio"`   // 销售金额占比
	RollRatio           string    `json:"roll_ratio"`          // 匹数占比
	Last15DaySaleAmount []float64 `json:"last_15_day_sale_amount"`
}

type GetTopCustomerResForTop struct {
	CustomerId          uint64    `json:"customer_id"`         // 客户ID
	CustomerName        string    `json:"customer_name"`       // 客户名称
	ProductCount        int       `json:"product_count"`       // 产品数量
	ProductColorCount   int       `json:"product_color_count"` // 产品颜色数量
	TotalSaleAmount     float64   `json:"total_sale_amount"`   // 销售总额
	SaleRoll            int64     `json:"sale_roll"`           // 销售匹数
	ReturnRoll          int64     `json:"return_roll"`         // 退货匹数
	TotalRoll           int64     `json:"total_roll"`          // 总匹数
	TotalSettleWeight   string    `json:"total_settle_weight"` // 总数量（按单位分组）
	RollRatio           string    `json:"roll_ratio"`          // 匹数占比
	Last15DaySaleAmount []float64 `json:"last_15_day_sale_amount"`
}

// UnifiedAnalysisReq 统一分析请求
type UnifiedAnalysisReq struct {
	system.Param
	// 分析维度类型
	AnalysisType int `json:"analysis_type" form:"analysis_type" binding:"required"` // 分析维度类型 1-产品 2-客户
	// 分析时间范围
	StartDate string `json:"start_date" form:"start_date" binding:"required"` // 开始日期
	EndDate   string `json:"end_date" form:"end_date" binding:"required"`     // 结束日期
	// 产品ID
	ProductId uint64 `json:"product_id" form:"product_id"` // 产品ID
	// 客户ID
	CustomerId uint64 `json:"customer_id" form:"customer_id"` // 客户ID
	// 颜色ID
	ColorId uint64 `json:"color_id" form:"color_id"` // 颜色ID
	// 销售模式
	SaleMode []int `json:"sale_mode" form:"sale_mode"` // 销售模式 1大货 2剪板 3客订大货 4客订剪板
	// 分析粒度
	AnalysisGranularity int `json:"analysis_granularity" form:"analysis_granularity"` // 分析粒度 1-日 2-月
}

// UnifiedAnalysisRes 统一分析响应
type GetUnifiedAnalysisRes struct {
	system.ResponseData
	HomeData                  GetAnalysisHomeRes     `json:"home_data"`                     // 首页数据
	ProAnalysisTrendDataDay   GetProAnalysisTrendRes `json:"pro_analysis_trend_data_day"`   // 产品分析趋势数据
	ProAnalysisTrendDataMonth GetProAnalysisTrendRes `json:"pro_analysis_trend_data_month"` // 产品分析趋势数据
	TopProductData            GetTopDataRes          `json:"top_product_data"`              // 前十产品数据
	TopCustomerData           GetTopDataRes          `json:"top_customer_data"`             // 前十客户数据
}

type GetUnifiedProductDetailAnalysisRes struct {
	system.ResponseData
	ProductAnalysisData       GetProAnalysisProductRes `json:"product_analysis_data"`         // 首页数据
	ProAnalysisTrendDataDay   GetProAnalysisTrendRes   `json:"pro_analysis_trend_data_day"`   // 产品分析趋势数据
	ProAnalysisTrendDataMonth GetProAnalysisTrendRes   `json:"pro_analysis_trend_data_month"` // 产品分析趋势数据
	TopColorData              GetTopDataRes            `json:"top_color_data"`                // 前十颜色数据
	TopCustomerData           GetTopDataRes            `json:"top_customer_data"`             // 前十颜色数据
}

type GetUnifiedCustomerDetailAnalysisRes struct {
	system.ResponseData
	CustomerAnalysisData      GetProAnalysisCustomerRes `json:"customer_analysis_data"`        // 首页数据
	ProAnalysisTrendDataDay   GetProAnalysisTrendRes    `json:"pro_analysis_trend_data_day"`   // 产品分析趋势数据
	ProAnalysisTrendDataMonth GetProAnalysisTrendRes    `json:"pro_analysis_trend_data_month"` // 产品分析趋势数据
	TopCustomerData           GetTopDataRes             `json:"top_customer_data"`             // 前十客户数据
}

type GetUnifiedCustomerDetailCustomerAnalysisRes struct {
	system.ResponseData
	CustomerAnalysisData      GetCusAnalysisCustomerRes `json:"customer_customerAnalysis_data"` // 首页数据
	ProAnalysisTrendDataDay   GetProAnalysisTrendRes    `json:"pro_analysis_trend_data_day"`    // 产品分析趋势数据
	ProAnalysisTrendDataMonth GetProAnalysisTrendRes    `json:"pro_analysis_trend_data_month"`  // 产品分析趋势数据
	TopProductData            GetTopDataRes             `json:"top_product_data"`               // 前十客户数据
}

type GetUnifiedProductDetailCustomerAnalysisRes struct {
	system.ResponseData
	CustomerAnalysisData      GetCusAnalysisProductRes `json:"customer_productAnalysis_data"` // 首页数据
	ProAnalysisTrendDataDay   GetProAnalysisTrendRes   `json:"pro_analysis_trend_data_day"`   // 产品分析趋势数据
	ProAnalysisTrendDataMonth GetProAnalysisTrendRes   `json:"pro_analysis_trend_data_month"` // 产品分析趋势数据
	TopColorData              GetTopDataRes            `json:"top_color_data"`                // 前十颜色数据
}

// GetReceivableOrderListReq 获取应收单列表请求
type GetReceivableOrderListReq struct {
	system.ListQuery
	CustomerId uint64          `form:"customer_id" json:"customer_id" binding:"required"` // 客户ID
	StartTime  tools.QueryTime `form:"start_time" json:"start_time"`                      // 开始时间
	EndTime    tools.QueryTime `form:"end_time" json:"end_time"`                          // 结束时间
}

// GetReceivableOrderListRes 获取应收单列表响应
type GetReceivableOrderListRes struct {
	system.ResponseData
	Total int64                 `json:"total"` // 总数量
	List  []ReceivableOrderItem `json:"list"`  // 应收单列表
}

// ReceivableOrderItem 应收单项
type ReceivableOrderItem struct {
	// 基础信息
	OrderId   uint64    `json:"order_id"`   // 应收单ID
	OrderNo   string    `json:"order_no"`   // 应收单号
	OrderTime time.Time `json:"order_time"` // 下单时间

	// 商品信息
	ProductName      string `json:"product_name"` // 成品名称
	ProductCode      string `json:"product_code"`
	ProductColorCode string `json:"product_color_code"` // 色号
	ProductColorName string `json:"product_color_name"` // 颜色

	// 数量信息
	Roll         int `json:"roll"`          // 匹数
	SettleWeight int `json:"settle_weight"` // 结算数量
	// 缸号
	DyelotNumber string `json:"dyelot_number"` // 缸号
	// 结算空差
	SettleErrorWeight int `json:"settle_error_weight"` // 结算空差

	// 价格信息
	SalePrice       int `json:"sale_price"`        // 销售金额
	OffsetSalePrice int `json:"offset_sale_price"` // 优惠幅度
	// 其他金额
	OtherAmount int `json:"other_amount"` // 其他金额

	// 汇总金额
	SettleAmount      int `json:"settle_amount"`      // 结算金额
	CollectedAmount   int `json:"collected_amount"`   // 已收金额
	UncollectedAmount int `json:"uncollected_amount"` // 未收金额

	// 状态信息
	CollectType       should_collect_order.CollectType   `json:"collect_type"` // 单据类型 1:销售单 2:退货单
	CollectTypeName   string                             `json:"collect_type_name"`
	SaleMode          sale.SaleOrderType                 `json:"sale_mode"` // 销售模式 1:大货 2:剪板 3:客订大货 4:客订剪板
	SaleModeName      string                             `json:"sale_mode_name"`
	AuditStatus       common.OrderStatus                 `json:"audit_status"` // 审核状态 1:未审核 2:已审核
	AuditStatusName   string                             `json:"audit_status_name"`
	CollectStatus     should_collect_order.CollectStatus `json:"collect_status"` // 收款状态 1:未收款 2:已收部分 3:已收全款
	CollectStatusName string                             `json:"collect_status_name"`

	CreateTime  time.Time `json:"create_time"`
	SaleOrderNo string    `json:"sale_order_no"`
	SaleOrderID uint64    `json:"sale_order_id"`
}
